package com.aliyun.xdragon.flink.state;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;
import java.util.Random;

import com.aliyun.xdragon.algorithm.common.DetectorConstants;
import com.aliyun.xdragon.common.enumeration.MissingFillType;
import com.aliyun.xdragon.common.enumeration.algorithm.AnomalyType;
import com.aliyun.xdragon.config.MetricDetectConfig;
import com.aliyun.xdragon.flink.state.processors.MultiProcessor;
import com.aliyun.xdragon.util.CircularQueue;
import lombok.SneakyThrows;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.math3.util.FastMath;
import org.apache.commons.math3.util.Pair;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeutils.ComparatorTestBase;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.java.tuple.Tuple5;
import org.apache.flink.api.java.typeutils.GenericTypeInfo;
import org.apache.flink.api.java.typeutils.PojoTypeInfo;
import org.apache.flink.api.java.typeutils.TypeExtractor;
import org.apache.flink.core.memory.DataInputView;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2023/07/31
 */
public class MultiProcessorTest {

    @Test
    public void testValueCache() {
        MetricDetectConfig config = new MetricDetectConfig();
        config.setTsInterval(1);
        config.setExpireTs(15);
        config.setMissFillType(MissingFillType.ZERO);
        HashMap<AnomalyType, Properties> map = new HashMap<>();
        Properties properties = new Properties() {
            {
                put(DetectorConstants.THRESHOLD, 0.05);
            }
        };
        map.put(AnomalyType.PC_ANOMALY, properties);
        map.put(AnomalyType.OUTLIER, properties);
        config.setDetectParams(map);
        int dim = 2;
        int[] id = {0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1};
        int[] ts = {0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 11, 11, 12};
        double[] v = {100, 200, 100, 200, 100, 200, 100, 200, 100, 200, 100, 200, 100, 200, 100, 200, 100, 200, 100,
            200, 100, 100, 200, 202};
        MultiProcessor processor = new MultiProcessor(dim);
        ArrayList<Tuple5<AnomalyType, Integer, Double, Integer, Integer>> list = new ArrayList<>();
        for (int i = 0; i < id.length; i++) {
            List<Tuple5<AnomalyType, Integer, Double, Integer, Integer>> tupleList = processor.detect(id[i], ts[i],
                v[i], config);
            list.addAll(tupleList);
        }
        Assert.assertEquals(2, list.size());
        Assert.assertEquals(AnomalyType.OUTLIER, list.get(0).f0);
        Assert.assertEquals(10, (int)list.get(0).f1);
        Assert.assertEquals(AnomalyType.PC_ANOMALY, list.get(1).f0);
        Assert.assertEquals(10, (int)list.get(1).f1);
        //内部状态检验
        Assert.assertEquals(0, processor.getData().get(10)[1], 1e-10);
        Assert.assertEquals(1, processor.getTsInterval());
        Assert.assertEquals(2, processor.getMetricNum());
        Assert.assertEquals(MissingFillType.ZERO, processor.getMissingFillType());
        Assert.assertEquals(0.05,
            MapUtils.getDoubleValue(processor.getDetectParams().get(AnomalyType.PC_ANOMALY),
                DetectorConstants.THRESHOLD), 1e-10);
        Assert.assertEquals(15, processor.getExpireTs());
        Assert.assertEquals(1, processor.getCnt());
        Assert.assertNull(processor.getCurValues()[0]);
        Assert.assertEquals(202, processor.getCurValues()[1], 1e-10);
        Assert.assertEquals(12, (int)processor.getCurTs());
    }

    @Test
    public void testImpute() {
        MultiProcessor processor = new MultiProcessor(2);
        processor.setMissingFillType(MissingFillType.LINEAR);
        processor.setMetricNum(2);
        processor.setTsInterval(1);
        processor.setExpireTs(50);
        processor.setDetectParams(new HashMap<>());
        processor.setData(new CircularQueue<>(50));
        int[] id = {1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0};
        int[] ts = {0, 1, 1, 2, 2, 3, 3, 4, 5, 5, 6, 7};
        double[] v = {100, 50, 110, 51, 120, 52, 110, 55, 55, 111, 60, 59};
        for (int i = 0; i < 9; i++) {
            processor.detect(id[i], ts[i], v[i], null);
        }
        Assert.assertEquals(0, processor.getData().get(0)[0], 1e-10);
        Assert.assertEquals(100, processor.getData().get(4)[1], 1e-10);
        processor.setMissingFillType(MissingFillType.PREVIOUS);
        for (int i = 9; i < id.length; i++) {
            processor.detect(id[i], ts[i], v[i], null);
        }
        Assert.assertEquals(111, processor.getData().get(6)[1], 1e-10);
    }

    @Test
    public void testLinear() {
        int dim = 10;
        MultiProcessor processor = new MultiProcessor(dim);
        double[] x = new double[dim];
        for (int i = 0; i < 1000; i++) {
            for (int j = 0; j < dim; j++) {
                x[j] = i * j;
            }
            processor.getAccumulator().update(x);
            Assert.assertNull(processor.pccDetect(x, null));
        }
    }

    @Test
    public void testCosine() {
        MultiProcessor processor = new MultiProcessor(3);
        double[] x = new double[3];
        for (int i = 0; i < 1000; i++) {
            x[0] = FastMath.sin(i);
            x[1] = FastMath.cos(i);
            x[2] = FastMath.sin(0.7 * i) * 10;
            processor.getAccumulator().update(x);
            Assert.assertNull(processor.pccDetect(x, null));
        }
    }

    @Test
    public void testCorrelationAnomaly() {
        MultiProcessor processor = new MultiProcessor(2);
        double[] x = new double[2];
        AnomalyType[] result = new AnomalyType[1000];
        Random random = new Random(0xc0ffee);
        for (int t = 0; t < 1000; t++) {
            //在t=400之后，两个序列的相关性发生背离
            x[0] = 5 * FastMath.sin(t * 2 * FastMath.PI / 40) + random.nextDouble() * 1e-1;
            if (t < 400) {
                x[1] = -FastMath.sin(t * 2 * FastMath.PI / 40) + random.nextDouble() * 1e-1;
            } else {
                x[1] = FastMath.sin(t * 2 * FastMath.PI / 40) + random.nextDouble() * 1e-1;
            }
            processor.getAccumulator().update(x);
            Pair<AnomalyType, Double> pair = processor.pccDetect(x, null);
            result[t] = pair == null ? null : pair.getFirst();
        }
        //在t=400及其一定的延迟之内应当检测出相关性异常
        boolean any = false;
        for (int t = 400; t < 410; t++) {
            any |= (result[t] == AnomalyType.PC_ANOMALY);
        }
        Assert.assertTrue(any);
        //之前的时刻不应该检测出异常
        for (int t = 0; t < 400; t++) {
            Assert.assertNull(result[t]);
        }
    }

    @Test
    public void testSpike() {
        int dim = 2;
        MultiProcessor processor = new MultiProcessor(dim);
        double[] x = new double[dim];
        for (int t = 0; t < 1000; t++) {
            for (int j = 0; j < dim; j++) {
                x[j] = t * j;
            }
            if (t == 104) {
                x[0] += 1;
            }
            processor.getAccumulator().update(x);
            Pair<AnomalyType, Double> pair = processor.pccDetect(x, null);
            if (t == 104) {
                Assert.assertEquals(pair.getFirst(), AnomalyType.PC_ANOMALY);
            } else {
                Assert.assertNull(pair);
            }
        }
    }

    @Test
    @SneakyThrows
    public void testSerialization() {
        //类型信息检验（退化到Kryo将受到严重的序列化性能损失）
        TypeInformation<?> ti = TypeExtractor.createTypeInfo(MultiProcessor.class);
        Assert.assertTrue(ti instanceof PojoTypeInfo);
        PojoTypeInfo<?> pojo = ((PojoTypeInfo<?>)ti);
        for (int i = 0; i < pojo.getFieldNames().length; i++) {
            Assert.assertFalse(pojo.getTypeAt(i) instanceof GenericTypeInfo);
        }
        //构造对象
        MetricDetectConfig config = new MetricDetectConfig();
        config.setTsInterval(1);
        config.setExpireTs(5);
        HashMap<AnomalyType, Properties> map = new HashMap<>();
        map.put(AnomalyType.PC_ANOMALY, new Properties());
        config.setDetectParams(map);
        int dim = 2;
        int[] id = {0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1};
        int[] ts = {0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 11, 11};
        double[] v = {100, 200, 100, 200, 100, 200, 100, 200, 100, 200, 100, 200, 100, 200, 100, 200, 100, 200, 100,
            200, 100, 100, 200};
        MultiProcessor processor1 = new MultiProcessor(dim);
        for (int i = 0; i < id.length; i++) {
            processor1.detect(id[i], ts[i], v[i], config);
        }
        //序列化和反序列化
        TypeSerializer<MultiProcessor> serializer = TypeInformation.of(MultiProcessor.class)
            .createSerializer(new ExecutionConfig());
        ComparatorTestBase.TestOutputView out = new ComparatorTestBase.TestOutputView();
        serializer.serialize(processor1, out);
        DataInputView in = out.getInputView();
        MultiProcessor processor2 = serializer.deserialize(in);
        //正确性测试
        Assert.assertEquals(processor1, processor2);
        Assert.assertEquals(processor1.hashCode(), processor2.hashCode());
    }

}