package com.aliyun.xdragon.flink.task;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;

import com.aliyun.xdragon.algorithm.common.DetectorConstants;
import com.aliyun.xdragon.common.enumeration.DetectType;
import com.aliyun.xdragon.common.enumeration.MissingFillType;
import com.aliyun.xdragon.common.enumeration.algorithm.AnomalyType;
import com.aliyun.xdragon.config.MetricDetectConfig;
import com.aliyun.xdragon.util.Constants;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple6;
import org.apache.flink.api.java.tuple.Tuple8;
import org.apache.flink.streaming.api.operators.co.CoBroadcastWithKeyedOperator;
import org.apache.flink.streaming.runtime.streamrecord.StreamRecord;
import org.apache.flink.streaming.util.KeyedTwoInputStreamOperatorTestHarness;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2023/08/10
 */
public class MultiDetectTest {
    private KeyedTwoInputStreamOperatorTestHarness<
        String,
        Tuple6<String, String, Integer, Double, Long, String>,
        MetricDetectConfig,
        Tuple8<String, String, Integer, String, Double, String, Integer, Integer>
        > testHarness;

    @Before
    public void setupTestHarness() throws Exception {
        MultiDetect anomalyDetect = new MultiDetect(10);
        // KeyedOneInputStreamOperatorTestHarness 需要三个参数：算子对象、键 Selector、键类型
        testHarness = new KeyedTwoInputStreamOperatorTestHarness<>(
            new CoBroadcastWithKeyedOperator<>(anomalyDetect, Collections.singletonList(
                new MapStateDescriptor<>("broadcastState", String.class, MetricDetectConfig.class))),
            (KeySelector<Tuple6<String, String, Integer, Double, Long, String>, String>)v -> v.f1.split("/")[0],
            (KeySelector<MetricDetectConfig, String>)MetricDetectConfig::getConfigKey,
            TypeInformation.of(String.class)
        );
        testHarness.setup();
        testHarness.open();
    }

    @After
    public void cleanup() throws Exception {
        testHarness.close();
    }

    @Test
    public void test() throws Exception {
        //支持PC_Anomaly的Config1
        MetricDetectConfig detectConfig1 = new MetricDetectConfig();
        detectConfig1.setTsInterval(1);
        detectConfig1.setExpireTs(4800);
        detectConfig1.setSmoothWinSize(DetectorConstants.DEFAULT_SMOOTH_WIN_SIZE);
        detectConfig1.setMissFillType(MissingFillType.LINEAR);
        detectConfig1.setDetectType(DetectType.MULTIPLE);
        detectConfig1.setConfigKey(Constants.MULTI_PREFIX + "1");
        detectConfig1.setConfigName("metricTest");
        ArrayList<String> sourceIds = new ArrayList<>();
        sourceIds.add("1");
        sourceIds.add("2");
        sourceIds.add("3");
        detectConfig1.setSourceIds(sourceIds);
        Properties pcExceptionDetectParams = new Properties() {
            {
                put(DetectorConstants.THRESHOLD, 0.01);
                put(DetectorConstants.FORGET_FACTOR, 0.99);
            }
        };
        HashMap<AnomalyType, Properties> map = new HashMap<AnomalyType, Properties>() {
            {
                put(AnomalyType.PC_ANOMALY, pcExceptionDetectParams);
            }
        };
        detectConfig1.setDetectParams(map);
        MetricDetectConfig detectConfig2 = new MetricDetectConfig();
        detectConfig2.setDetectType(DetectType.SINGLE);
        detectConfig2.setConfigKey("single1");

        //broadcast配置信息
        testHarness.processElement2(new StreamRecord<>(detectConfig1, System.currentTimeMillis()));
        testHarness.processElement2(new StreamRecord<>(detectConfig2, System.currentTimeMillis()));

        //检测异常
        for (int t = 0; t < 1000; t++) {
            for (int j = 0; j < 2; j++) {
                //注入异常
                double v = (t == 204 && j == 0) ? 1 : t * j;
                testHarness.processElement1(
                    new Tuple6<>("1", "metricTest/" + j + "/i-test", t, v, System.currentTimeMillis(), "{}"),
                    System.currentTimeMillis()
                );
            }
        }
        //此时可以检出异常
        List<StreamRecord<? extends Tuple8<String, String, Integer, String, Double, String, Integer, Integer>>>
            records = testHarness.extractOutputStreamRecords();
        Assert.assertEquals(1, records.size());
        Assert.assertEquals("1", records.get(0).getValue().f0);
        Assert.assertEquals("metricTest/i-test", records.get(0).getValue().f1);
        Assert.assertEquals(204, (int)records.get(0).getValue().f2);
        Assert.assertEquals(AnomalyType.PC_ANOMALY, AnomalyType.of(records.get(0).getValue().f3));
        Assert.assertEquals("{\"metric_names\":\"metricTest/0,metricTest/1\"}", records.get(0).getValue().f5);
        Assert.assertEquals(0, (int)records.get(0).getValue().f6);
        Assert.assertEquals(204, (int)records.get(0).getValue().f7);

        //过期Config1并broadcast
        detectConfig1.setCreateTs(System.currentTimeMillis() - Constants.BD_STATE_TTL - 10);
        testHarness.processElement2(new StreamRecord<>(detectConfig1, System.currentTimeMillis()));

        //再次检测异常
        for (int t = 0; t < 1000; t++) {
            for (int j = 0; j < 2; j++) {
                //注入异常
                double v = (t == 204 && j == 0) ? 1 : t * j;
                testHarness.processElement1(
                    new Tuple6<>("1", "metricTest/" + j + "/i-test", t + 1000, v, System.currentTimeMillis(), "{}"),
                    System.currentTimeMillis()
                );
            }
        }
        //由于Config1已经过期，因此无法检出任何新的异常。records长度仍然为1
        records = testHarness.extractOutputStreamRecords();
        Assert.assertEquals(1, records.size());
    }
}