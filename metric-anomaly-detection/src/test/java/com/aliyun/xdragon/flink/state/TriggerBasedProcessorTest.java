package com.aliyun.xdragon.flink.state;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Properties;
import java.util.Random;

import com.aliyun.xdragon.algorithm.common.DetectorConstants;
import com.aliyun.xdragon.common.enumeration.MissingFillType;
import com.aliyun.xdragon.common.enumeration.algorithm.AnomalyType;
import com.aliyun.xdragon.config.MetricDetectConfig;
import com.aliyun.xdragon.flink.state.processors.TriggerBasedProcessor;
import com.aliyun.xdragon.util.Constants;
import junit.framework.TestCase;
import lombok.SneakyThrows;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeutils.ComparatorTestBase;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.java.tuple.Tuple6;
import org.apache.flink.api.java.typeutils.GenericTypeInfo;
import org.apache.flink.api.java.typeutils.PojoTypeInfo;
import org.apache.flink.api.java.typeutils.TypeExtractor;
import org.apache.flink.core.memory.DataInputView;
import org.junit.Assert;
import org.junit.Test;

public class TriggerBasedProcessorTest {
    @Test
    public void processorTest() {
        MetricDetectConfig detectConfig = new MetricDetectConfig();
        detectConfig.setTsInterval(3);
        HashMap<AnomalyType, Properties> anomalyTypeDetectAlarmConfigHashMap = new HashMap<>();

        Properties params = new Properties() {
            {
                put(Constants.CONTINUANCE, 3);
                put(Constants.BREAK_POINT_NUM, 1);
                put(DetectorConstants.VOLATILITY, 0.2);
                put(Constants.SILENCE, Constants.SECOND_IN_HOUR);
            }
        };
        anomalyTypeDetectAlarmConfigHashMap.put(AnomalyType.SPIKE, params);
        detectConfig.setDetectParams(anomalyTypeDetectAlarmConfigHashMap);
        detectConfig.setMissFillType(MissingFillType.MEAN);
        detectConfig.setExpireTs(Constants.SECOND_IN_HOUR);
        detectConfig.setSmoothWinSize(DetectorConstants.DEFAULT_SMOOTH_WIN_SIZE);
        TriggerBasedProcessor processor = new TriggerBasedProcessor();
        // 注意这里测试的时候吗，每次修改config之后，加点操作前都要loadConfig
        processor.loadConfig(detectConfig);
        processor.addValue(1, 1.0);
        processor.addValue(4, 2.0);
        TestCase.assertTrue(processor.addValue(10, 3.0) > 0);

        detectConfig.setTsInterval(6);
        TestCase.assertEquals(6, detectConfig.getTsInterval());
        TestCase.assertEquals(1.5, ((Math.round(processor.getData().get(2).getValue() * 10)) / 10.0));
        TestCase.assertEquals(1.75, ((Math.round(processor.getData().get(3).getTrend() * 100)) / 100.0));
        detectConfig.setTsInterval(10);
        detectConfig.setExpireTs(Constants.SECOND_IN_DAY * 2);
        TestCase.assertEquals(Constants.SECOND_IN_DAY * 2, detectConfig.getExpireTs());
        detectConfig.setMissFillType(MissingFillType.MEDIAN);
        TestCase.assertEquals(MissingFillType.MEDIAN, detectConfig.getMissFillType());
        processor.loadConfig(detectConfig);

        TestCase.assertTrue(processor.addValue(20, 1.0) > 0);
        TestCase.assertTrue(detectConfig.getDetectParams().containsKey(AnomalyType.SPIKE));
        TestCase.assertFalse(processor.addValue(10, 800.0) > 0);

        detectConfig.setMissFillType(MissingFillType.LINEAR);
        processor.loadConfig(detectConfig);
        processor.addValue(40, 4.0);
        TestCase.assertEquals(2.5, processor.getData().get(5).getValue(), 1e-10);
        TestCase.assertEquals(4, processor.getData().get(6).getValue(), 1e-10);
        TestCase.assertEquals(0, processor.getLastJump());

    }

    @Test
    public void testDynamicAnomaly() {
        MetricDetectConfig detectConfig = new MetricDetectConfig();
        detectConfig.setTsInterval(300);
        detectConfig.setExpireTs(7200);
        detectConfig.setSmoothWinSize(1);//不平滑
        HashMap<AnomalyType, Properties> anomalyTypeDetectAlarmConfigHashMap = new HashMap<>();
        detectConfig.setDetectParams(anomalyTypeDetectAlarmConfigHashMap);
        double[] valueList = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 0, 0, 0, 0, 0, 0, 0, 0};
        TriggerBasedProcessor processor = new TriggerBasedProcessor();
        for (int i = 0; i < 15; i++) {
            processor.detect(i * 300, valueList[i], detectConfig);
        }
        Properties params = new Properties() {
            {
                put(Constants.CONTINUANCE, 3);
                put(Constants.BREAK_POINT_NUM, 0);
                put(DetectorConstants.THRESHOLD, 1);
            }
        };
        anomalyTypeDetectAlarmConfigHashMap.put(AnomalyType.DROP, params);
        for (int i = 15; i < valueList.length; i++) {
            List<Tuple6<AnomalyType, Integer, Double, Integer, Double, String>> anomalyList = processor.detect(i * 300, valueList[i], detectConfig);
            List<AnomalyType> list = new ArrayList<>();
            anomalyList.forEach(t -> list.add(t.f0));
            TestCase.assertEquals(i >= 17, list.contains(AnomalyType.DROP));
        }
    }


    @Test
    public void testJump() {
        MetricDetectConfig detectConfig = new MetricDetectConfig();
        detectConfig.setTsInterval(300);
        detectConfig.setExpireTs(7200);
        detectConfig.setSmoothWinSize(5);
        HashMap<AnomalyType, Properties> map = new HashMap<>();
        map.put(AnomalyType.DIP, new Properties());
        map.put(AnomalyType.SPIKE, new Properties());
        detectConfig.setDetectParams(map);

        TriggerBasedProcessor processor = new TriggerBasedProcessor();
        LinkedList<Tuple6<AnomalyType, Integer, Double, Integer, Double, String>> anomalyList = new LinkedList<>();
        //向分解器加入数据
        Random random = new Random(0x1234);
        for (int i = 0; i < 60; i++) {
            double value = random.nextGaussian() + (i > 50 ? 15 : 0);
            anomalyList.addAll(processor.detect(i * 300, value, detectConfig));
        }
        //分解后可以发现跳变上升
        Assert.assertTrue(processor.getLastJump() > 0);
        Assert.assertSame(anomalyList.get(0).f0, AnomalyType.SPIKE);
    }


    @Test
    @SneakyThrows
    public void testSerialization() {
        //类型信息检验（退化到Kryo将受到严重的序列化性能损失）
        TypeInformation<?> ti = TypeExtractor.createTypeInfo(TriggerBasedProcessor.class);
        Assert.assertTrue(ti instanceof PojoTypeInfo);
        PojoTypeInfo<?> pojo = ((PojoTypeInfo<?>) ti);
        for (int i = 0; i < pojo.getFieldNames().length; i++) {
            Assert.assertFalse(pojo.getTypeAt(i) instanceof GenericTypeInfo);
        }

        //构造对象
        MetricDetectConfig detectConfig = new MetricDetectConfig();
        detectConfig.setTsInterval(300);
        detectConfig.setExpireTs(7200);
        detectConfig.setSmoothWinSize(12);
        HashMap<AnomalyType, Properties> map = new HashMap<>();
        Properties properties = new Properties();

        map.put(AnomalyType.DIP, properties);
        map.put(AnomalyType.SPIKE, properties);
        detectConfig.setDetectParams(map);
        TriggerBasedProcessor processor1 = new TriggerBasedProcessor();
        for (int i = 0; i < 100; i++) {
            processor1.detect(i * 300,i,detectConfig);
        }

        //序列化和反序列化
        TypeSerializer<TriggerBasedProcessor> serializer = TypeInformation.of(TriggerBasedProcessor.class)
            .createSerializer(new ExecutionConfig());
        ComparatorTestBase.TestOutputView out = new ComparatorTestBase.TestOutputView();
        serializer.serialize(processor1, out);
        DataInputView in = out.getInputView();
        TriggerBasedProcessor processor2 = serializer.deserialize(in);

        //正确性测试
        Assert.assertEquals(processor1, processor2);
    }

}
