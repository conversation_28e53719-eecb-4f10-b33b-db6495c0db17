package com.aliyun.xdragon.flink.state;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;

import com.aliyun.xdragon.algorithm.common.DetectorConstants;
import com.aliyun.xdragon.common.enumeration.MissingFillType;
import com.aliyun.xdragon.common.enumeration.algorithm.AnomalyType;
import com.aliyun.xdragon.config.MetricDetectConfig;
import com.aliyun.xdragon.flink.state.MultiMetricState;
import lombok.SneakyThrows;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeutils.ComparatorTestBase;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple5;
import org.apache.flink.api.java.typeutils.GenericTypeInfo;
import org.apache.flink.api.java.typeutils.PojoTypeInfo;
import org.apache.flink.api.java.typeutils.TypeExtractor;
import org.apache.flink.core.memory.DataInputView;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2023/08/01
 */
public class MultiMetricStateTest {

    @Test
    public void test() {
        MetricDetectConfig config = new MetricDetectConfig();
        config.setTsInterval(1);
        config.setExpireTs(5);
        config.setMissFillType(MissingFillType.ZERO);
        HashMap<AnomalyType, Properties> map = new HashMap<>();
        Properties properties = new Properties() {
            {
                put(DetectorConstants.THRESHOLD, 0.05);
            }
        };
        map.put(AnomalyType.PC_ANOMALY, properties);
        config.setDetectParams(map);

        String[] metricNames = {"metric1", "metric2"};
        MultiMetricState state = new MultiMetricState(metricNames);
        double[] x = new double[2];
        ArrayList<Tuple5<AnomalyType, Integer, Double, Integer, Integer>> list = new ArrayList<>();
        for (int t = 0; t < 1000; t++) {
            for (int j = 0; j < 2; j++) {
                x[j] = t * j;
                if (t == 104 && j == 0) {
                    x[0] += 1;
                }
                List<Tuple5<AnomalyType, Integer, Double, Integer, Integer>> tupleList
                    = state.detect(null, metricNames[j], t, x[j], config);
                list.addAll(tupleList);
            }
        }
        Assert.assertEquals(1, list.size());
        Assert.assertEquals(AnomalyType.PC_ANOMALY, list.get(0).f0);
        Assert.assertEquals(104, (int)list.get(0).f1);
    }

    @Test
    public void testInit() {
        MetricDetectConfig config = new MetricDetectConfig();
        config.setTsInterval(1);
        config.setExpireTs(5);
        config.setMissFillType(MissingFillType.ZERO);
        HashMap<AnomalyType, Properties> map = new HashMap<>();
        Properties properties = new Properties() {
            {
                put(DetectorConstants.THRESHOLD, 0.05);
            }
        };
        map.put(AnomalyType.PC_ANOMALY, properties);
        config.setDetectParams(map);

        MultiMetricState state = new MultiMetricState(50);
        String[] metrics = {"metric1/i-test", "metric2/i-test", "metric1/i-test", "metric2/i-test", "metric3/i-test"};
        for (int i = 0; i < metrics.length - 1; i++) {
            Assert.assertEquals(0, state.detect(null, metrics[i], i * 20, 0d, config).size());
        }
        Assert.assertEquals(2, state.getMetricNameMap().size());
        Assert.assertEquals(0, (int)state.getMetricNameMap().get(Tuple2.of("metric1/i-test", null)));
        Assert.assertEquals(1, (int)state.getMetricNameMap().get(Tuple2.of("metric2/i-test", null)));
        state.detect(null, metrics[4], 80, 0d, config);
        Assert.assertEquals(3, state.getMetricNameMap().size());
        Assert.assertEquals(0, (int)state.getMetricNameMap().get(Tuple2.of("metric1/i-test", null)));
        Assert.assertEquals(1, (int)state.getMetricNameMap().get(Tuple2.of("metric2/i-test", null)));
        Assert.assertEquals(2, (int)state.getMetricNameMap().get(Tuple2.of("metric3/i-test", null)));
        Assert.assertEquals("", state.getSourceIdList());
        Assert.assertEquals("metric1,metric2,metric3", state.getMetricNameList());
    }

    @Test
    @SneakyThrows
    public void testSerialization() {
        //类型信息检验（退化到Kryo将受到严重的序列化性能损失）
        TypeInformation<?> ti = TypeExtractor.createTypeInfo(MultiMetricState.class);
        Assert.assertTrue(ti instanceof PojoTypeInfo);
        PojoTypeInfo<?> pojo = ((PojoTypeInfo<?>)ti);
        for (int i = 0; i < pojo.getFieldNames().length; i++) {
            Assert.assertFalse(pojo.getTypeAt(i) instanceof GenericTypeInfo);
        }

        //构造对象
        MetricDetectConfig config = new MetricDetectConfig();
        config.setTsInterval(1);
        config.setExpireTs(5);
        HashMap<AnomalyType, Properties> map = new HashMap<>();
        map.put(AnomalyType.PC_ANOMALY, new Properties());
        config.setDetectParams(map);

        String[] metricNames = {"metric1", "metric2"};
        MultiMetricState state1 = new MultiMetricState(metricNames);
        for (int t = 0; t < 1000; t++) {
            for (int j = 0; j < 2; j++) {
                state1.detect(null, metricNames[j], t, (double)(t * j), config);
            }
        }

        //序列化和反序列化
        TypeSerializer<MultiMetricState> serializer = TypeInformation.of(MultiMetricState.class)
            .createSerializer(new ExecutionConfig());
        ComparatorTestBase.TestOutputView out = new ComparatorTestBase.TestOutputView();
        serializer.serialize(state1, out);
        DataInputView in = out.getInputView();
        MultiMetricState state2 = serializer.deserialize(in);

        //正确性测试
        Assert.assertEquals(state1, state2);
        Assert.assertEquals(state1.hashCode(), state2.hashCode());
    }

}