package com.aliyun.xdragon.flink.task;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;

import com.aliyun.xdragon.algorithm.common.DetectorConstants;
import com.aliyun.xdragon.common.enumeration.MissingFillType;
import com.aliyun.xdragon.common.enumeration.algorithm.AnomalyType;
import com.aliyun.xdragon.config.MetricDetectConfig;
import com.aliyun.xdragon.util.Constants;
import junit.framework.TestCase;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple6;
import org.apache.flink.api.java.tuple.Tuple11;
import org.apache.flink.streaming.api.TimeCharacteristic;
import org.apache.flink.streaming.api.operators.co.CoBroadcastWithKeyedOperator;
import org.apache.flink.streaming.api.watermark.Watermark;
import org.apache.flink.streaming.runtime.streamrecord.StreamRecord;
import org.apache.flink.streaming.util.KeyedTwoInputStreamOperatorTestHarness;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

public class AnomalyDetectTest {
    private KeyedTwoInputStreamOperatorTestHarness<
        String,
        Tuple6<String, String, Integer, Double, Long, String>,
        MetricDetectConfig,
        Tuple11<String, String, Integer, Double, String, String, Integer, Double, Integer, Double, String>
        > testHarness;


    @Before
    public void setupTestHarness() throws Exception {
        AnomalyDetect anomalyDetect = spy(new AnomalyDetect());
        JedisPool mockedPool = mock(JedisPool.class);
        Jedis mockedJedis = mock(Jedis.class);
        when(mockedPool.getResource()).thenReturn(mockedJedis);
        when(mockedJedis.get("flink_metric_period:metricTestPeriodic")).thenReturn("48");
        when(anomalyDetect.createJedisPool(any())).thenReturn(mockedPool);

        // KeyedOneInputStreamOperatorTestHarness 需要三个参数：算子对象、键 Selector、键类型
        testHarness = new KeyedTwoInputStreamOperatorTestHarness<>(
                new CoBroadcastWithKeyedOperator<>(anomalyDetect, Collections.singletonList(
                        new MapStateDescriptor<>("broadcastState", String.class, MetricDetectConfig.class))),
                (KeySelector<Tuple6<String, String, Integer, Double, Long, String>, String>) v -> v.f0 + "/" + v.f1,
                (KeySelector<MetricDetectConfig, String>)MetricDetectConfig::getConfigKey,
                TypeInformation.of(String.class)
        );
        testHarness.setup();
        testHarness.open();
    }


    @After
    public void cleanup() throws Exception {
        testHarness.close();
    }


    @Test
    public void testAperiodicInput() throws Exception {

        // 2、异常数据内置配置
        MetricDetectConfig detectConfig1 = new MetricDetectConfig();
        MetricDetectConfig detectConfig2 = new MetricDetectConfig();
        MetricDetectConfig detectConfig3 = new MetricDetectConfig();

        detectConfig1.setTsInterval(1800);
        detectConfig1.setExpireTs(Constants.SECOND_IN_DAY * 3);
        detectConfig1.setSmoothWinSize(DetectorConstants.DEFAULT_SMOOTH_WIN_SIZE);
        detectConfig1.setMissFillType(MissingFillType.MEDIAN);
        detectConfig1.setConfigName("metricTest");
        detectConfig1.setConfigKey("1metricTest");

        detectConfig2.setTsInterval(1800);
        detectConfig2.setExpireTs(Constants.SECOND_IN_DAY * 3);
        detectConfig2.setSmoothWinSize(DetectorConstants.DEFAULT_SMOOTH_WIN_SIZE);
        detectConfig2.setMissFillType(MissingFillType.MEDIAN);
        detectConfig2.setConfigName("metricTestSourceId");
        detectConfig2.setConfigKey("1");

        detectConfig3.setAnomalyCheck(false);
        detectConfig3.setConfigKey("1metricTestNotCheck");
        detectConfig3.setConfigName("metricTestNotCheck");

        Properties exceptionDetectParams = new Properties() {
            {
                put(Constants.CONTINUANCE, 3);
                put(Constants.BREAK_POINT_NUM, 2);
                put(DetectorConstants.VOLATILITY, 0.2);
                put(Constants.SILENCE, Constants.SECOND_IN_HOUR * 3);
                put(Constants.MUTATION_BASELINE, 14);
            }
        };
        Properties newlyExceptionDetectParams = new Properties() {
            {
                put(DetectorConstants.THRESHOLD, 200);
                put(DetectorConstants.VOLATILITY_VALUE, 100);
                put(DetectorConstants.NEWLY_MAX_WINDOWS, 3);
            }
        };
        // 支持突增、突降、趋势上升两种异常检测
        HashMap<AnomalyType, Properties> map = new HashMap<AnomalyType, Properties>() {
            {
                put(AnomalyType.SPIKE, exceptionDetectParams);
                put(AnomalyType.DIP, exceptionDetectParams);
                put(AnomalyType.RAMP_DOWN, exceptionDetectParams);
                put(AnomalyType.NEWLY, newlyExceptionDetectParams);
            }
        };
        detectConfig1.setDetectParams(map);
        detectConfig2.setDetectParams(map);
        detectConfig3.setDetectParams(map);

        // broadcast配置信息
        testHarness.processElement2(new StreamRecord<>(detectConfig1, System.currentTimeMillis()));
        testHarness.processElement2(new StreamRecord<>(detectConfig2, System.currentTimeMillis()));
        testHarness.processElement2(new StreamRecord<>(detectConfig3, System.currentTimeMillis()));

        // 该条记录直接匹配完全规则 detectConfig1
        testHarness.processElement1(
                new Tuple6<>("1", "metricTest", 1800, 1.0, System.currentTimeMillis(), "{\"additonalKey\":\"additionalValue\"}"),
                System.currentTimeMillis()
        );

        // 完全规则 metricTest 过期
        detectConfig1.setCreateTs(System.currentTimeMillis() - Constants.BD_STATE_TTL - 10);

        testHarness.processElement2(new StreamRecord<>(detectConfig1, System.currentTimeMillis()));

        // 数据源级别匹配 metricTestSourceId
        testHarness.processElement1(
                new Tuple6<>("1", "metricTest", 1800, 2.0, System.currentTimeMillis(), "{\"additonalKey\":\"additionalValue\"}"),
                System.currentTimeMillis()
        );

        // 下面的均匹配 metricTestSourceId 规则，这个测试中其中一个dip 和 一个spike被衰减机制抹掉了，最后一个dip被基础阈值14抹掉了
        double[] testList = new double[]{
                79, 77, 76, 77, 79, 80, 76, 79, 75, 72, 75, 78, 72, 79, 70, 79, 79, 74, 78,
                70, 71, 75, 77, 71, 76, 73, 70, 77, 74, 72, 75, 80, 72, 12, 71, 11, 12, 11,
                70, 70, 79, 75, 72, 31, 32, 32, 70, 70, 70, 70, 90, 89, 90, 72, 75, 78, 72,
                15, 15, 15};
        for (int i = 0; i < testList.length; i++) {
            int ts = 1800 * i;
            testHarness.processElement1(
                    new Tuple6<>("1", "metricTest", ts, testList[i], System.currentTimeMillis(), "{\"ts\":\"" + ts + "\"}"),
                    System.currentTimeMillis()
            );
        }
        // anomalyCheck = false
        testHarness.processElement1(
                new Tuple6<>("1", "metricTestNotCheck", 1800, 2.0, System.currentTimeMillis(), "{\"ts\":\"" + 1800 + "\"}"),
                System.currentTimeMillis()
        );

        //Access records collected during processElement
        List<StreamRecord<? extends Tuple11<String, String, Integer, Double, String, String, Integer, Double, Integer, Double, String>>> records =
                testHarness.extractOutputStreamRecords();
        //System.out.println(records);
        TestCase.assertEquals(AnomalyType.NEWLY, AnomalyType.of(records.get(0).getValue().f5));
        TestCase.assertEquals(AnomalyType.DIP, AnomalyType.of(records.get(1).getValue().f5));
    }

    @Test
    public void testMutation() throws Exception {
        MetricDetectConfig detectConfig = new MetricDetectConfig();
        detectConfig.setTsInterval(1800);
        detectConfig.setExpireTs(Constants.SECOND_IN_DAY * 3);
        detectConfig.setSmoothWinSize(5);
        detectConfig.setMissFillType(MissingFillType.MEDIAN);
        detectConfig.setConfigKey("1");

        Properties exceptionDetectParams = new Properties() {
            {
                put(Constants.CONTINUANCE, 3);
                put(Constants.BREAK_POINT_NUM, 2);
                put(DetectorConstants.VOLATILITY, 0.2);
                put(Constants.SILENCE, Constants.SECOND_IN_HOUR * 3);
            }
        };

        // 只支持突降异常检测
        HashMap<AnomalyType, Properties> map = new HashMap<AnomalyType, Properties>() {
            {
                put(AnomalyType.DIP, exceptionDetectParams);
            }
        };
        detectConfig.setDetectParams(map);

        // broadcast配置信息
        testHarness.processElement2(new StreamRecord<>(detectConfig, System.currentTimeMillis()));

        // 指标先增再降，但是我们不检测增，增后回到正常水平，所以降也不报出
        double[] testList = new double[]{
            79, 77, 76, 77, 79, 80, 76, 79, 75, 72, 75, 78, 72, 79, 70, 79, 79, 74, 78,
            70, 71, 75, 77, 71, 76, 73, 70, 77, 74, 72, 75, 110, 100, 102, 102, 103, 71, 70, 70,
            79, 75, 72, 70, 70, 70, 70,72, 75, 78, 72};
        for (int i = 0; i < testList.length; i++) {
            int ts = 1800 * i;
            testHarness.processElement1(
                new Tuple6<>("1", "metricTest", ts, testList[i], System.currentTimeMillis(), "{\"ts\":\"" + ts + "\"}"),
                System.currentTimeMillis()
            );
        }

        //Access records collected during processElement
        List<StreamRecord<? extends Tuple11<String, String, Integer, Double, String, String, Integer, Double, Integer, Double, String>>> records =
            testHarness.extractOutputStreamRecords();
        // 去除BaseProcessor中305行的逻辑，这里会会出现DIP异常
        TestCase.assertTrue(records.isEmpty());
    }

    @Test
    public void testPeriodicInput() throws Exception {
        double[] valueArray = {29011.0, 28551.0, 30152.0, 30691.0, 31032.0, 31879.0, 31046.0, 32218.0, 32665.0, 33034.0,
                33840.0, 33683.0, 34216.0, 32812.0, 33669.0, 33774.0, 33706.0, 32903.0, 33303.0, 33622.0, 32957.0,
                34227.0, 33020.0, 34784.0, 32776.0, 33691.0, 34315.0, 32392.0, 33282.0, 31952.0, 32432.0, 32708.0,
                32180.0, 33132.0, 33196.0, 32494.0, 31580.0, 31256.0, 30966.0, 32242.0, 29727.0, 31648.0, 29565.0,
                28806.0, 29295.0, 28703.0, 28204.0, 29101.0, 27803.0, 29462.0, 28164.0, 29562.0, 30443.0, 30171.0,
                30677.0, 30508.0, 31717.0, 32568.0, 30735.0, 33657.0, 32886.0, 32405.0, 32807.0, 32072.0, 31113.0,
                32029.0, 31789.0, 30739.0, 30793.0, 31779.0, 30939.0, 30932.0, 30421.0, 30154.0, 30372.0, 29916.0,
                28197.0, 28998.0, 27469.0, 29757.0, 28563.0, 28879.0, 28531.0, 28186.0, 28272.0, 28651.0, 27113.0,
                28000.0, 26690.0, 27138.0, 26787.0, 25413.0, 26858.0, 26154.0, 26048.0, 26452.0, 25000.0, 26423.0,
                26894.0, 27177.0, 28942.0, 27457.0, 28203.0, 29548.0, 27779.0, 31061.0, 29697.0, 29278.0, 30475.0,
                29338.0, 29361.0, 27856.0, 27193.0, 27830.0, 27466.0, 26884.0, 27800.0, 26933.0, 25561.0, 26468.0,
                25922.0, 29623.0, 29639.0, 29957.0, 28906.0, 27874.0, 28458.0, 27236.0, 29159.0, 29703.0, 27649.0,
                28489.0, 27333.0, 26162.0, 26703.0, 24811.0, 24610.0, 33933.0, 32133.0, 32683.0, 32183.0, 31804.0,
                32028.0, 31396.0, 31388.0, 32864.0, 32564.0, 33873.0, 34013.0, 34561.0, 34106.0, 34597.0, 35669.0,
                36599.0, 36481.0, 37683.0, 36185.0, 37617.0, 37133.0, 35571.0, 36228.0, 35302.0, 36266.0, 37105.0,
                35369.0, 36944.0, 36706.0, 36937.0, 37389.0, 36194.0, 37323.0, 37319.0, 35086.0, 36585.0, 35156.0,
                36558.0, 35909.0, 35293.0, 36286.0, 35802.0, 35497.0, 35724.0, 33712.0, 35231.0, 34056.0, 34415.0};

        //设置MetricDetectConfig
        MetricDetectConfig detectConfig = new MetricDetectConfig();
        detectConfig.setTsInterval(100);
        detectConfig.setExpireTs(48 * 3 * 100);
        detectConfig.setSmoothWinSize(12);
        detectConfig.setConfigKey("1");
        detectConfig.setConfigName("metric");
        HashMap<AnomalyType, Properties> map = new HashMap<>();
        Properties properties = new Properties() {
            {
                put(DetectorConstants.VOLATILITY, 0);
                put(DetectorConstants.PERIOD_NUM, 2);
                put(DetectorConstants.RISK, -1);
            }
        };
        map.put(AnomalyType.SPIKE, properties);
        detectConfig.setDetectParams(map);

        //先使用非周期性的配置
        testHarness.processElement2(new StreamRecord<>(detectConfig, System.currentTimeMillis()));
        for (int i = 0; i < 100; i++) {
            testHarness.processElement1(
                    new Tuple6<>("1", "metricTestPeriodic", i * 100, valueArray[i], System.currentTimeMillis(), "{}"),
                    System.currentTimeMillis());
        }

        //成功改为周期性的配置，并顺利检测到跳变异常
        detectConfig.setPeriod(true);
        testHarness.processElement2(new StreamRecord<>(detectConfig, System.currentTimeMillis()));
        for (int i = 100; i < valueArray.length; i++) {
            testHarness.processElement1(
                    new Tuple6<>("1", "metricTestPeriodic", i * 100, valueArray[i], System.currentTimeMillis(), "{}"),
                    System.currentTimeMillis());
        }

        List<StreamRecord<? extends Tuple11<String, String, Integer, Double, String, String, Integer, Double, Integer, Double, String>>> records =
                testHarness.extractOutputStreamRecords();
        //System.out.println(records);
        TestCase.assertEquals(4, records.stream().
            filter(a -> AnomalyType.SPIKE.equals(AnomalyType.of(a.getValue().f5))).count());
    }

    @Test
    public void testTrend() throws Exception {
        double[] testList = new double[] {
            1.065, 1.055, 1.055, 1.055, 1.065, 1.065, 1.055, 1.055, 1.06, 1.085, 1.115, 1.115, 1.135, 1.16, 1.16,
            1.16, 1.135, 1.135, 1.165, 1.165, 1.165, 1.165, 1.135, 1.185, 1.2, 1.2, 1.17, 1.145, 1.145, 1.125,
            1.12, 1.12, 1.105, 1.11, 1.135, 1.135, 1.105, 1.08, 1.08, 1.08, 1.08, 1.08, 1.095, 1.125, 1.14,
            1.15, 1.125, 1.125, 1.15, 1.17, 1.15, 1.14, 1.14, 1.16, 1.16, 1.16, 1.14, 1.125, 1.125, 1.095,
            1.09, 1.08, 1.06, 1.055, 1.055, 1.055, 1.055, 1.075, 1.09, 1.12, 1.155, 1.165, 1.165, 1.165, 1.165,
            1.165, 1.17, 1.17, 1.165, 1.155, 1.155, 1.155, 1.155, 1.15, 1.16, 1.17, 1.17, 1.215, 1.27, 1.275,
            1.285, 1.285, 1.285, 1.275, 1.275, 1.285, 1.275, 1.275, 1.27, 1.26, 1.26, 1.245, 1.235, 1.245, 1.245,
            1.25, 1.245, 1.235, 1.245, 1.245, 1.235, 1.215, 1.18, 1.18, 1.22, 1.2, 1.185, 1.185, 1.185, 1.195, 1.175};

        MetricDetectConfig detectConfig1 = new MetricDetectConfig();
        detectConfig1.setTsInterval(300);
        detectConfig1.setExpireTs(Constants.SECOND_IN_DAY * 3);
        detectConfig1.setSmoothWinSize(DetectorConstants.DEFAULT_SMOOTH_WIN_SIZE);
        detectConfig1.setMissFillType(MissingFillType.MEDIAN);

        Properties exceptionDetectParams = new Properties() {
            {
                put(Constants.CONTINUANCE, 3);
                put(DetectorConstants.VOLATILITY, 0.02);
                put(Constants.SILENCE, Constants.SECOND_IN_HOUR * 3);
                put(DetectorConstants.PRECISION, 0.01);
            }
        };

        // 趋势上升两种异常检测
        HashMap<AnomalyType, Properties> map = new HashMap<AnomalyType, Properties>() {
            {
                put(AnomalyType.RAMP_UP, exceptionDetectParams);
            }
        };
        detectConfig1.setDetectParams(map);
        detectConfig1.setConfigKey("1");
        detectConfig1.setConfigName("metricTest");
        testHarness.processElement2(new StreamRecord<>(detectConfig1, System.currentTimeMillis()));

        for (int i = 0; i < testList.length; i++) {
            int ts = 300 * i;
            testHarness.processElement1(
                new Tuple6<>("1", "metricTest", ts, testList[i], System.currentTimeMillis(), "{\"ts\":\"" + ts + "\"}"),
                System.currentTimeMillis()
            );
        }

        //Access records collected during processElement
        List<StreamRecord<? extends Tuple11<String, String, Integer, Double, String, String, Integer, Double, Integer, Double, String>>> records =
            testHarness.extractOutputStreamRecords();
        TestCase.assertEquals(AnomalyType.RAMP_UP, AnomalyType.of(records.get(0).getValue().f5));
    }

    @Test
    public void testNewly() throws Exception {
        double[] testList = new double[] {22,23,22,20,20,19};

        MetricDetectConfig detectConfig1 = new MetricDetectConfig();
        detectConfig1.setTsInterval(1800);
        detectConfig1.setExpireTs(Constants.SECOND_IN_DAY * 7);
        detectConfig1.setSmoothWinSize(DetectorConstants.DEFAULT_SMOOTH_WIN_SIZE);
        detectConfig1.setMissFillType(MissingFillType.ZERO);

        Properties exceptionDetectParams = new Properties() {
            {
                put(DetectorConstants.THRESHOLD, 1686067200);
                put(DetectorConstants.VOLATILITY_VALUE, 50);
                put(DetectorConstants.NEWLY_MAX_WINDOWS, 6);
            }
        };

        // 新增异常检测
        HashMap<AnomalyType, Properties> map = new HashMap<AnomalyType, Properties>() {
            {
                put(AnomalyType.NEWLY, exceptionDetectParams);
            }
        };
        detectConfig1.setDetectParams(map);
        detectConfig1.setConfigKey("1");
        detectConfig1.setConfigName("metricTest");
        testHarness.processElement2(new StreamRecord<>(detectConfig1, System.currentTimeMillis()));

        for (int i = 0; i < testList.length; i++) {
            int ts = 1708398000 + 1800 * i;
            testHarness.processElement1(
                new Tuple6<>("1", "metricTest", ts, testList[i], System.currentTimeMillis(), "{\"ts\":\"" + ts + "\"}"),
                System.currentTimeMillis()
            );
        }

        //Access records collected during processElement
        List<StreamRecord<? extends Tuple11<String, String, Integer, Double, String, String, Integer, Double, Integer, Double, String>>> records =
            testHarness.extractOutputStreamRecords();
        //System.out.println(records);
        TestCase.assertEquals(AnomalyType.NEWLY, AnomalyType.of(records.get(0).getValue().f5));
    }

    @Test
    public void testFill() throws Exception {
        MetricDetectConfig detectConfig1 = new MetricDetectConfig();

        detectConfig1.setTsInterval(300);
        detectConfig1.setExpireTs(4800);
        detectConfig1.setSmoothWinSize(DetectorConstants.DEFAULT_SMOOTH_WIN_SIZE);
        detectConfig1.setMissFillType(MissingFillType.LINEAR);
        detectConfig1.setConfigKey("1metricTest");
        detectConfig1.setConfigName("metricTest");

        Properties exceptionDetectParams = new Properties() {
            {
                put(Constants.CONTINUANCE, 3);
                put(Constants.BREAK_POINT_NUM, 1);
                put(DetectorConstants.VOLATILITY, 0.5);
                put(Constants.SILENCE, 900);
                put(Constants.NEED_UPDATE, "true");
            }
        };
        // 支持突增、突降、趋势上升两种异常检测
        HashMap<AnomalyType, Properties> map = new HashMap<AnomalyType, Properties>() {
            {
                put(AnomalyType.SPIKE, exceptionDetectParams);
                put(AnomalyType.DIP, exceptionDetectParams);
                put(AnomalyType.RAMP_DOWN, exceptionDetectParams);
            }
        };
        detectConfig1.setDetectParams(map);


        testHarness.processElement2(new StreamRecord<>(detectConfig1, System.currentTimeMillis()));

        int[] tsList = {0, 300, 600, 900, 1200, 2700, 3000, 3300, 3600, 3900, 4200, 4500, 4800, 5100};
        double[] valueList = {10, 10, 11, 11, 12, 13, 12, 14, 100, 110, 100, 120, 100, 200};

        for (int i = 0; i < tsList.length; i++) {
            testHarness.processElement1(
                    new Tuple6<>("1", "metricTest", tsList[i], valueList[i], System.currentTimeMillis(), "{}"),
                    System.currentTimeMillis()
            );
        }

        List<StreamRecord<? extends Tuple11<String, String, Integer, Double, String, String, Integer, Double, Integer, Double, String>>> records =
                testHarness.extractOutputStreamRecords();
//        System.out.println(records);
        TestCase.assertEquals(AnomalyType.SPIKE, AnomalyType.of(records.get(0).getValue().f5));
        TestCase.assertEquals("update", records.get(1).getValue().f10);
    }

    @Test
    public void testMutationDetect() throws Exception {
        MetricDetectConfig detectConfig1 = new MetricDetectConfig();

        detectConfig1.setTsInterval(300);
        detectConfig1.setExpireTs(48000);
        detectConfig1.setSmoothWinSize(DetectorConstants.DEFAULT_SMOOTH_WIN_SIZE);
        detectConfig1.setMissFillType(MissingFillType.LINEAR);
        detectConfig1.setConfigKey("1metricTest");
        detectConfig1.setConfigName("metricTest");

        Properties spikeParams = new Properties() {
            {
                put(Constants.CONTINUANCE, 3);
                put(Constants.BREAK_POINT_NUM, 1);
                put(DetectorConstants.VOLATILITY, 0.5);
                put(DetectorConstants.THRESHOLD, 0.95);
            }
        };

        Properties dipParams = new Properties() {
            {
                put(Constants.CONTINUANCE, 3);
                put(Constants.BREAK_POINT_NUM, 1);
                put(DetectorConstants.VOLATILITY, 0.2);
                put(DetectorConstants.THRESHOLD, 0.95);
            }
        };


        // 支持突增、突降、趋势上升两种异常检测
        HashMap<AnomalyType, Properties> map = new HashMap<AnomalyType, Properties>() {
            {
                put(AnomalyType.SPIKE, spikeParams);
                put(AnomalyType.DIP, dipParams);
            }
        };
        detectConfig1.setDetectParams(map);
        testHarness.processElement2(new StreamRecord<>(detectConfig1, System.currentTimeMillis()));

        int[] tsList = {0, 300, 600, 900, 1200, 2700, 3000, 3300, 3600, 3900, 4200, 4500, 4800, 5100};
        double[] valueList = {50, 50, 51, 51, 49, 48, 52, 54, 110, 1, 2, 20, 110, 120};

        for (int i = 0; i < tsList.length; i++) {
            if (i == 10) {
                detectConfig1.getDetectParams().get(AnomalyType.DIP).put(DetectorConstants.THRESHOLD, 0.951);
                testHarness.processElement2(new StreamRecord<>(detectConfig1, System.currentTimeMillis()));
            }
            if (i == 12) {
                detectConfig1.getDetectParams().get(AnomalyType.SPIKE).put(DetectorConstants.THRESHOLD, 0.999);
                detectConfig1.getDetectParams().get(AnomalyType.DIP).put(DetectorConstants.THRESHOLD, 0.9999);
                testHarness.processElement2(new StreamRecord<>(detectConfig1, System.currentTimeMillis()));
            }
            if (i == 13) {
                detectConfig1.getDetectParams().get(AnomalyType.DIP).put(DetectorConstants.THRESHOLD, 0.95);
            }
            testHarness.processElement1(
                    new Tuple6<>("1", "metricTest", tsList[i], valueList[i], System.currentTimeMillis(), "{}"),
                    System.currentTimeMillis()
            );

        }

        List<StreamRecord<? extends Tuple11<String, String, Integer, Double, String, String, Integer, Double, Integer, Double, String>>> records =
                testHarness.extractOutputStreamRecords();
//        System.out.println(records);
        TestCase.assertEquals(AnomalyType.DIP, AnomalyType.of(records.get(0).getValue().f5));

    }
}
