package com.aliyun.xdragon.flink.state;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;

import com.aliyun.xdragon.common.enumeration.algorithm.AnomalyType;
import com.aliyun.xdragon.config.MetricDetectConfig;
import com.aliyun.xdragon.flink.state.InstanceState;
import com.aliyun.xdragon.flink.state.MultiMetricState;
import com.aliyun.xdragon.util.Constants;
import lombok.SneakyThrows;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.state.ReadOnlyBroadcastState;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeutils.ComparatorTestBase;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.java.typeutils.GenericTypeInfo;
import org.apache.flink.api.java.typeutils.PojoTypeInfo;
import org.apache.flink.api.java.typeutils.TypeExtractor;
import org.apache.flink.core.memory.DataInputView;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2023/09/18
 */
public class InstanceStateTest {

    @Test
    @SneakyThrows
    public void testUpdate() {
        HashMap<String, ArrayList<String>> sourceMap = new HashMap<>();
        ArrayList<String> list1 = new ArrayList<>();
        list1.add("1");
        list1.add("2");
        sourceMap.put("multi1", list1);
        ArrayList<String> list2 = new ArrayList<>();
        list2.add("3");
        list2.add("4");
        list2.add("5");
        sourceMap.put("multi2", list2);
        ArrayList<String> list3 = new ArrayList<>();
        list1.add("6");
        list1.add("7");
        sourceMap.put("multi3", list3);
        HashMap<String, MultiMetricState> stateMap = new HashMap<>();
        MultiMetricState state1 = new MultiMetricState(10);
        state1.setConfigKey("state1");
        MultiMetricState state2 = new MultiMetricState(10);
        state2.setConfigKey("state2");
        MultiMetricState state3 = new MultiMetricState(10);
        state3.setConfigKey("state3");
        stateMap.put("multi1", state1);
        stateMap.put("multi2", state2);
        stateMap.put("multi3", state3);

        Map<String, MetricDetectConfig> map = new HashMap<>();
        MetricDetectConfig config1 = new MetricDetectConfig();
        config1.setConfigKey("multi1");
        ArrayList<String> sourceIds1 = new ArrayList<>();
        sourceIds1.add("1");
        sourceIds1.add("2");
        config1.setSourceIds(sourceIds1);
        map.put(config1.getConfigKey(), config1);
        MetricDetectConfig config2 = new MetricDetectConfig();
        config2.setConfigKey("multi2");
        ArrayList<String> sourceIds2 = new ArrayList<>();
        sourceIds2.add("3");
        sourceIds2.add("4");
        sourceIds2.add("5");
        sourceIds2.add("6");
        config2.setSourceIds(sourceIds2);
        map.put(config2.getConfigKey(), config2);
        TestReadOnlyBroadcastState<String, MetricDetectConfig> testReadOnlyBroadcastState
            = new TestReadOnlyBroadcastState<>(map);

        InstanceState state = new InstanceState();
        state.setStateMap(stateMap);
        state.setSourceMap(sourceMap);
        state.update(testReadOnlyBroadcastState);

        Assert.assertEquals(2, state.getStateMap().size());
        Assert.assertEquals(2, state.getSourceMap().size());
        Assert.assertEquals(state1, state.getStateMap().get("multi1"));
        ArrayList<String> ansList = new ArrayList<>(Arrays.asList("1,2".split(",")));
        Assert.assertEquals(ansList, state.getSourceMap().get("multi1"));
        Assert.assertEquals(null, state.getStateMap().get("multi2").getConfigKey());
        ansList = new ArrayList<>(Arrays.asList("3,4,5,6".split(",")));
        Assert.assertEquals(ansList, state.getSourceMap().get("multi2"));
    }

    @Test
    public void testDetect() {
        MetricDetectConfig config1 = new MetricDetectConfig();
        config1.setConfigKey(Constants.MULTI_PREFIX + "1");
        ArrayList<String> sourceIds1 = new ArrayList<>();
        sourceIds1.add("1");
        sourceIds1.add("2");
        config1.setSourceIds(sourceIds1);
        MetricDetectConfig config2 = new MetricDetectConfig();
        config2.setConfigKey(Constants.MULTI_PREFIX + "2");
        ArrayList<String> sourceIds2 = new ArrayList<>();
        sourceIds2.add("3");
        config2.setSourceIds(sourceIds2);

        InstanceState state = new InstanceState();
        state.setInitSeconds(100);
        state.detect("1", "VmExitMetric/hlT/i-test", 0, 0d, config1);
        state.detect("1", "VmExitMetric/nrvmexit/i-test", 0, 0d, config1);
        state.detect("2", "VmExitMetric/apiC_IPI/i-test", 0, 0d, config1);
        state.detect("3", "VmArpPingMetric/arp_timeout_ratio/i-test", 0, 0d, config2);
        state.detect("3", "VmArpPingMetric/arp_time_sum/i-test", 0, 0d, config2);
        state.detect("3", "VmArpPingMetric/arp_time_avg/i-test", 0, 0d, config2);

        Assert.assertEquals(100, state.getInitSeconds());
        Assert.assertEquals(2, state.getStateMap().size());
        Assert.assertEquals("VmExitMetric/apiC_IPI,VmExitMetric/hlT,VmExitMetric/nrvmexit",
            state.getMetricNameList(Constants.MULTI_PREFIX + "1"));
        Assert.assertEquals(
            "VmArpPingMetric/arp_time_avg,VmArpPingMetric/arp_time_sum,VmArpPingMetric/arp_timeout_ratio",
            state.getMetricNameList(Constants.MULTI_PREFIX + "2"));
    }

    @Test
    @SneakyThrows
    public void testSerialization() {
        //类型信息检验（退化到Kryo将受到严重的序列化性能损失）
        TypeInformation<?> ti = TypeExtractor.createTypeInfo(InstanceState.class);
        Assert.assertTrue(ti instanceof PojoTypeInfo);
        PojoTypeInfo<?> pojo = ((PojoTypeInfo<?>)ti);
        for (int i = 0; i < pojo.getFieldNames().length; i++) {
            Assert.assertFalse(pojo.getTypeAt(i) instanceof GenericTypeInfo);
        }

        //构造对象
        MetricDetectConfig config = new MetricDetectConfig();
        config.setTsInterval(1);
        config.setExpireTs(5);
        HashMap<AnomalyType, Properties> map = new HashMap<>();
        map.put(AnomalyType.PC_ANOMALY, new Properties());
        config.setDetectParams(map);

        String[] metricNames = {"metric1", "metric2"};
        MultiMetricState multiMetricState = new MultiMetricState(metricNames);
        for (int t = 0; t < 1000; t++) {
            for (int j = 0; j < 2; j++) {
                multiMetricState.detect(metricNames[j], metricNames[j], t, (double)(t * j), config);
            }
        }

        HashMap<String, MultiMetricState> stateMap = new HashMap<>();
        stateMap.put("01", multiMetricState);
        HashMap<String, ArrayList<String>> sourceMap = new HashMap<>();
        InstanceState state1 = new InstanceState();
        state1.setStateMap(stateMap);
        ArrayList<String> list = new ArrayList<>();
        list.add("101");
        list.add("102");
        sourceMap.put("01", list);
        state1.setSourceMap(sourceMap);

        //序列化和反序列化
        TypeSerializer<InstanceState> serializer = TypeInformation.of(InstanceState.class)
            .createSerializer(new ExecutionConfig());
        ComparatorTestBase.TestOutputView out = new ComparatorTestBase.TestOutputView();
        serializer.serialize(state1, out);
        DataInputView in = out.getInputView();
        InstanceState state2 = serializer.deserialize(in);

        //正确性测试
        Assert.assertEquals(state1, state2);
        Assert.assertEquals(state1.hashCode(), state2.hashCode());
    }

    private class TestReadOnlyBroadcastState<K, V> implements ReadOnlyBroadcastState<K, V> {

        private Map<K, V> map;

        public TestReadOnlyBroadcastState(Map<K, V> map) {
            this.map = map;
        }

        @Override
        public V get(K key) throws Exception {
            return map.get(key);
        }

        @Override
        public boolean contains(K key) throws Exception {
            return map.containsKey(key);
        }

        @Override
        public Iterable<Entry<K, V>> immutableEntries() throws Exception {
            return map.entrySet();
        }

        @Override
        public void clear() {
            map.clear();
        }
    }

}