package com.aliyun.xdragon.detector;

import com.aliyun.xdragon.flink.state.dataPoints.DataPoint;
import com.aliyun.xdragon.util.CircularQueue;
import com.aliyun.xdragon.util.StatisticsAccumulator;
import junit.framework.TestCase;
import org.junit.Test;

import java.util.Random;

public class DataPointMKTrendDetectorTest {
    @Test
    public void accumulatorTest() {
        CircularQueue<DataPoint> queue = new CircularQueue<>(100);
        Random random = new Random(0x1234);
        for (int i = 0; i < 100; i++) {
            DataPoint point = new DataPoint(i);
            point.setTrend(random.nextGaussian());
            queue.add(point);
        }

        double precision = 1e-3;
        StatisticsAccumulator accumulator = new StatisticsAccumulator(0);
        TestCase.assertEquals(0, accumulator.getPrecision(), 1e-100);
        accumulator.setPrecision(precision);
        TestCase.assertEquals(precision, accumulator.getPrecision(), 1e-100);

        int w = 20;
        accumulator.init(queue, 0, w);
        TestCase.assertEquals(DataPointMkTrendDetector.calculateS(queue, 0, w, precision), accumulator.getS());


        for (int i = w; i < queue.size(); i++) {
            accumulator.removeFirst(queue, i - w, i);
            accumulator.add(queue, i - w + 1, i);
            TestCase.assertEquals(DataPointMkTrendDetector.calculateS(queue, i - w + 1, i + 1, precision), accumulator.getS());
        }

        accumulator.clear();
        TestCase.assertEquals(0, accumulator.getS());
    }

}
