package com.aliyun.xdragon.util;

import com.aliyun.xdragon.flink.state.dataPoints.DecomposedDataPoint;
import lombok.SneakyThrows;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeutils.ComparatorTestBase;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.java.typeutils.GenericTypeInfo;
import org.apache.flink.api.java.typeutils.PojoTypeInfo;
import org.apache.flink.core.memory.DataInputView;
import org.junit.Assert;
import org.junit.Test;

import java.util.NoSuchElementException;


public class CircularQueueTest {


    @Test
    public void testQueue() {
        CircularQueue<Integer> queue = new CircularQueue<>();
        Assert.assertEquals(0, queue.size());
        Assert.assertEquals(0, queue.getCapacity());
        queue.resetCapacity(10);
        Assert.assertEquals(10, queue.getCapacity());
        Assert.assertThrows(NoSuchElementException.class, queue::getFirst);
        Assert.assertThrows(NoSuchElementException.class, queue::getLast);
        Assert.assertThrows(NoSuchElementException.class, queue::removeFirst);
        Assert.assertThrows(NoSuchElementException.class, queue::removeLast);
        for (int i = 0; i < 10; i++) {
            queue.add(i);
        }
        for (int i = 0; i < 10; i++) {
            Assert.assertEquals(i, (int) queue.get(i));
        }
        Assert.assertTrue(queue.isAtFullCapacity());
        Assert.assertEquals(0, (int) queue.removeFirst());
        Assert.assertEquals(9, (int) queue.removeLast());
        Assert.assertEquals(1, (int) queue.getFirst());
        Assert.assertEquals(8, (int) queue.getLast());
        Assert.assertThrows(NoSuchElementException.class, () -> queue.get(8));
        queue.resetCapacity(4);
        Assert.assertEquals(4, queue.size());
        queue.add(0);
        Assert.assertEquals(6, (int) queue.getFirst());
        queue.clear();
        Assert.assertEquals(0, queue.size());
    }


    @Test
    public void testGetterAndSetter() {
        CircularQueue<Integer> queue = new CircularQueue<>();
        int start = 1, size = 10, capacity = 20;
        Integer[] data = new Integer[20];

        queue.setData(data);
        queue.setCapacity(capacity);
        queue.setSize(size);
        queue.setStart(start);

        Assert.assertArrayEquals(data, queue.getData());
        Assert.assertEquals(start, queue.getStart());
        Assert.assertEquals(size, queue.getSize());
        Assert.assertEquals(capacity, queue.getCapacity());
    }


    @Test
    @SneakyThrows
    public void testSerialization() {
        //类型信息检验（退化到Kryo将受到严重的序列化性能损失）
        TypeInformation<CircularQueue<DecomposedDataPoint>> ti =
                TypeInformation.of(new TypeHint<CircularQueue<DecomposedDataPoint>>() {
                });
        Assert.assertTrue(ti instanceof PojoTypeInfo);
        PojoTypeInfo<?> pojo = ((PojoTypeInfo<?>) ti);
        for (int i = 0; i < pojo.getFieldNames().length; i++) {
            Assert.assertFalse(pojo.getTypeAt(i) instanceof GenericTypeInfo);
        }

        //构造对象
        CircularQueue<DecomposedDataPoint> queue1 = new CircularQueue<>(40);
        for (int i = 0; i <= 50; i++) {
            queue1.add(new DecomposedDataPoint(i));
        }
        queue1.resetCapacity(100);

        //序列化和反序列化
        TypeSerializer<CircularQueue<DecomposedDataPoint>> serializer = ti.createSerializer(new ExecutionConfig());
        ComparatorTestBase.TestOutputView out = new ComparatorTestBase.TestOutputView();
        serializer.serialize(queue1, out);
        DataInputView in = out.getInputView();
        CircularQueue<DecomposedDataPoint> queue2 = serializer.deserialize(in);

        //正确性测试
        Assert.assertEquals(queue1, queue2);
    }

}