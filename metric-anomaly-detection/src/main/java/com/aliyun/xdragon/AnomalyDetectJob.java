package com.aliyun.xdragon;

import java.time.Duration;

import com.alibaba.ververica.connectors.sls.source.SourceRecord;

import com.aliyun.xdragon.config.MetricDetectConfig;
import com.aliyun.xdragon.config.MetricGroups;
import com.aliyun.xdragon.flink.sink.SlsSink;
import com.aliyun.xdragon.flink.source.MysqlDataSource;
import com.aliyun.xdragon.flink.source.SlsSource;
import com.aliyun.xdragon.flink.task.AnomalyDetect;
import com.aliyun.xdragon.flink.task.MultiDetect;
import com.aliyun.xdragon.flink.task.SlsRawLogParse;
import com.aliyun.xdragon.flink.FlinkJobOptions;
import com.aliyun.xdragon.util.Constants;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.java.tuple.Tuple6;
import org.apache.flink.api.java.tuple.Tuple8;
import org.apache.flink.api.java.tuple.Tuple11;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.BroadcastConnectedStream;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import static java.lang.Long.MAX_VALUE;

/**
 * <AUTHOR>
 * @date 2022/05/18
 */

public class AnomalyDetectJob {
    public static void main(String[] args) throws Exception {
        final ParameterTool params = ParameterTool.fromArgs(args);
        // 注意下面的一行代码中的 configFile 不能修改，configFile文件是存放平台中设置的参数文件
        // 此处必须写 configFile，这样程序会读取上面说到的property文件参数
        String configFilePath = params.get("configFile");

        // 平台中设置的相关参数值
        ParameterTool parameterTool = ParameterTool.fromPropertiesFile(configFilePath);
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.getConfig().setGlobalJobParameters(parameterTool);

        // 添加日志数据源
        DataStream<SourceRecord> slsLogStream = env.addSource(SlsSource.create(parameterTool));

        String metricKey = parameterTool.get(Constants.METRIC_NAME_KEY, Constants.METRIC_NAME_KEY);
        String tsKey = parameterTool.get(Constants.METRIC_TS_KEY, Constants.METRIC_TS_KEY);
        String tsDateFormat = parameterTool.get(Constants.METRIC_DATE_FORMAT_KEY, null);
        String valueKey = parameterTool.get(Constants.METRIC_VALUE_KEY, Constants.METRIC_VALUE_KEY);
        String additionalKey = parameterTool.get(Constants.METRIC_ADDITIONAL_KEY, null);
        String metricPrefix = parameterTool.get(Constants.METRIC_PREFIX, null);
        String metricFilter = parameterTool.get(Constants.METRIC_FILTER, null);
        String sourceMap = parameterTool.get(Constants.METRIC_SOURCE_MAP, null);
        // 对日志数据进行解析，并逐条发送下游
        // 日志指标添加watermark
        int waterMarkDelay = parameterTool.getInt(FlinkJobOptions.BLINK_WATERMARK_DELAY, Constants.DEFAULT_WATERMARK);

        DataStream<Tuple6<String, String, Integer, Double, Long, String>> slsLogWithWatermarks = slsLogStream
                .flatMap(new SlsRawLogParse(metricKey, tsKey, valueKey, additionalKey,
                    tsDateFormat, metricPrefix, metricFilter, sourceMap))
                .assignTimestampsAndWatermarks(
                        WatermarkStrategy.<Tuple6<String, String, Integer, Double, Long, String>>forBoundedOutOfOrderness(Duration.ofMillis(waterMarkDelay))
                        .withTimestampAssigner(
                                (event, timestamp) -> event.f4
                        )
                        .withIdleness(Duration.ofMillis(waterMarkDelay * 3L)))
                .name("slsLogParse");

        // 数据源，用于广播配置信息
        DataStreamSource<MetricDetectConfig> mysqlConfigDataSource = env.addSource(new MysqlDataSource());

        // 这里双流数据源，需要把非主业务数据源（mysql）的水位线设置成最大，这样主业务流的水位线会成为标准
        BroadcastStream<MetricDetectConfig> configBroadcastStream = mysqlConfigDataSource
                .assignTimestampsAndWatermarks(WatermarkStrategy.<MetricDetectConfig>forMonotonousTimestamps()
                        .withTimestampAssigner((event, timestamp) -> MAX_VALUE)
                        .withIdleness(Duration.ofDays(365)))
                .broadcast(new MapStateDescriptor<>("broadcastState", String.class, MetricDetectConfig.class));

        if (params.has(Constants.MULTI_PREFIX)) {
            // 日志和异常检测配置join
            BroadcastConnectedStream<Tuple6<String, String, Integer, Double, Long, String>, MetricDetectConfig> connectedStream = slsLogWithWatermarks
                .keyBy(v -> MetricGroups.getInstanceId(v.f1))
                .connect(configBroadcastStream);

            int initSeconds = parameterTool.getInt(Constants.METRIC_INIT_SECONDS, Constants.INIT_SECONDS);
            // 异常检测流
            DataStream<Tuple8<String, String, Integer, String, Double, String, Integer, Integer>> anomalyDetect
                = connectedStream.process(new MultiDetect(initSeconds))
                .uid("anomalyDetect-id")
                .name("anomalyDetect");

            anomalyDetect.map(SlsSink::getSinkRecord)
                .addSink(SlsSink.create(parameterTool))
                .name(SlsSink.class.getSimpleName());
            env.execute(parameterTool.get(FlinkJobOptions.BLINK_JOB_NAME, "metric-anomaly-detect"));
            return;
        }


        // 日志和异常检测配置join
        BroadcastConnectedStream<Tuple6<String, String, Integer, Double, Long, String>,
                MetricDetectConfig> connectedStream = slsLogWithWatermarks
                .keyBy(v -> v.f0 + "/" + v.f1)
                .connect(configBroadcastStream);

        // 异常检测流
        DataStream<Tuple11<String, String, Integer, Double, String, String, Integer, Double, Integer, Double, String>> anomalyDetect = connectedStream
                .process(new AnomalyDetect())
                .uid("anomalyDetect-id")
                .name("anomalyDetect");

        anomalyDetect.map(SlsSink::getSinkRecord)
                .addSink(SlsSink.create(parameterTool))
                .name(SlsSink.class.getSimpleName());
//        anomalyDetect.print();

        // Streaming 程序必须加这个才能启动程序，否则不会有结果
        env.execute(parameterTool.get(FlinkJobOptions.BLINK_JOB_NAME, "metric-anomaly-detect"));
    }
}
