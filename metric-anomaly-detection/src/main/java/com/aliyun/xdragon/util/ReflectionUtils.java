package com.aliyun.xdragon.util;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.apache.flink.api.common.typeinfo.BasicArrayTypeInfo;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.PrimitiveArrayTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.EnumTypeInfo;
import org.apache.flink.api.java.typeutils.PojoField;

/**
 * <AUTHOR>
 * @date 2022/10/17
 * @description
 */
public class ReflectionUtils {

    /**
     * 对给定的POJO类，初始化它的POJO属性列表，列表将用于构造该类的PojoTypeInfo <br>
     * 列表中将包含该类的所有的非继承的非transient的简单类型的属性（其它属性的处理较为复杂，请手动加入列表）<br>
     * 简单类型包括：8种基本数据类型、包装类和由它们组成的数组, String, Date, Void, BigInteger, BigDecimal, Instant, Enum
     *
     * @param clazz 给定的POJO类
     * @return POJO属性列表（仅包括非继承的简单类型的属性）
     */
    public static List<PojoField> initPojoFieldListWithDeclaredBasicTypes(Class clazz) {
        List<PojoField> fieldList = new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (Modifier.isTransient(field.getModifiers()) || Modifier.isStatic(field.getModifiers())) {
                //transient属性和static属性不会被序列化
                continue;
            }
            TypeInformation info = getInfoFor(field.getType());
            if (info != null) {
                fieldList.add(new PojoField(field, info));
            }
        }
        return fieldList;
    }

    private static TypeInformation<?> getInfoFor(Class clazz) {
        //基本类型
        TypeInformation info = BasicTypeInfo.getInfoFor(clazz);
        if (info != null) {
            return info;
        }
        if (clazz.isArray()) {
            //基本类型数组
            info = PrimitiveArrayTypeInfo.getInfoFor(clazz);
            if (info != null) {
                return info;
            }
            //基本类型的包装类数组
            info = BasicArrayTypeInfo.getInfoFor(clazz);
            if (info != null) {
                return info;
            }
        }
        //枚举
        return clazz.isEnum() ? new EnumTypeInfo<>(clazz) : null;
    }

    /**
     * 基于反射的对象深度比较（不比较transient属性），默认输入的两个对象都是指定的类型
     *
     * @param clazz 输入的两个对象的类型
     * @param obj1  对象1
     * @param obj2  对象2
     * @return true表示两个对象在所有的非继承非transient属性上完全一致，false反之
     */
    public static boolean reflectEqual(Class<?> clazz, Object obj1, Object obj2) {
        Field[] fields = clazz.getDeclaredFields();
        try {
            for (Field field : fields) {
                if (Modifier.isTransient(field.getModifiers())) {
                    //跳过transient属性
                    continue;
                }
                field.setAccessible(true);
                if (!Objects.deepEquals(field.get(obj1), field.get(obj2))) {
                    return false;
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }
}
