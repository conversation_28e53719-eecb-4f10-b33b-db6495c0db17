package com.aliyun.xdragon.util;

/**
 * <AUTHOR>
 * @date 2022/04/27
 */
public final class Constants {
    /**
     * 指标时序数据点数限制
     */
    public static final int METRIC_BASE_LEN_LIMIT = 12;

    /**
     * 各类分位数标记
     */
    public static final int PERCENTILE_95TH = 95;
    public static final int PERCENTILE_90TH = 90;
    public static final int PERCENTILE_75TH = 75;
    public static final int PERCENTILE_50TH = 50;
    public static final int PERCENTILE_25TH = 25;
    public static final int PERCENTILE_10TH = 10;
    public static final int PERCENTILE_05TH = 5;

    /**
     * 浮点数为0判定的误差范围
     */
    public static final double DOUBLE_DIFF = 1e-6;

    public static final int SECOND_IN_DAY = 3600 * 24;
    public static final int SECOND_IN_HOUR = 3600;
    public static final int SECOND_IN_MINUTE = 60;

    /**
     * metric 关键字配置
     */
    public static final String METRIC_NAME_KEY = "metric";
    public static final String METRIC_TS_KEY = "timestamp";
    public static final String METRIC_START_TS_KEY = "startTimestamp";
    public static final String METRIC_DATE_FORMAT_KEY = "dateformat";
    public static final String METRIC_VALUE_KEY = "value";

    public static final String METRIC_SOURCE_ID_KEY = "sourceId";

    public static final String METRIC_SOURCE_MAP = "sourceMap";

    public static final String METRIC_ACTION = "action";
    public static final String METRIC_EXTREMUM_VALUE = "extremumValue";
    public static final String METRIC_BREAK_POINT_NUM_KEY = "breakPointNum";
    public static final String METRIC_ADDITIONAL_KEY = "additional";
    public static final String METRIC_ANOMALY_KEY = "anomaly";
    public static final String METRIC_ANOMALY_VALUE_KEY = "anomalyValue";
    public static final String METRIC_ANOMALY_SCORE_KEY = "score";
    public static final String METRIC_PREFIX = "metricPrefix";
    public static final String METRIC_PREFIX_VALUE_REPLACEMENT = "valueReplacement";
    public static final String METRIC_FILTER = "filter";
    public static final String METRIC_INIT_SECONDS = "initSeconds";

    public static final String SLS_SOURCE_KEY = "__source__";
    /**
     * metric当前支持10位时间戳，即秒级
     */
    public static final int METRIC_TS_LENGTH = 10;

    /**
     * metric当前支持13位时间戳，即毫秒级
     */
    public static final int METRIC_TS_LENGTH_13 = 13;

    /**
     * 默认用于周期项估计的周期个数
     */
    public static final int DEFAULT_PERIOD_NUM = 2;
    /**
     * 默认的延迟数，即连续出现的同方向突变异常数超过该值将被视作跳变异常
     */
    public static final int DEFAULT_LAG = 12;
    /**
     * 默认的多指标异常检测初始化时间，按秒计算
     */
    public static final int INIT_SECONDS = 300;

    /**
     * 业务相关的异常检测信息常量
     */
    public static final String CONTINUANCE = "continuance";
    public static final String BREAK_POINT_NUM = "continuanceBreakpointNum";
    public static final String SILENCE = "silence";
    public static final String PUNISH = "punish";
    public static final String REL_DIFF = "relativeDifference";
    public static final String ABS_DIFF = "absoluteDifference";
    public static final String MUTATION_BASELINE = "baseline";
    public static final String NEED_UPDATE = "needUpdate";
    public static final String DELAY = "delay";

    public static final Integer DEFAULT_BREAK_POINT_NUM = 0;
    public static final int DEFAULT_CONTINUANCE = 1;
    public static final int DEFAULT_SILENCE = 0;
    public static final double DEFAULT_PUNISH = 0;

    /**
     * broadcast异常检测配置过期时间 毫秒
     */
    public static final long BD_STATE_TTL = 2 * 3600 * 1000;

    /**
     * instance内部状态定时同步broadcast异常检测的更新间隔 毫秒
     */
    public static final long INSTANCE_STATE_UPDATE_TIME = 600 * 1000;

    /**
     * mysql source min flush interval 毫秒
     */
    public static final int MYSQL_MIN_FLUSH_INTERVAL = 1800 * 1000;

    /**
     * 数据缺失率阈值，如果窗口内缺失数据的比例超过该值就清空窗口
     */
    public static final double MISSING_CLEAR_PARAM = 0.5;

    /**
     * flink的watermark的默认值单位ms
     */
    public static final int DEFAULT_WATERMARK = 20000;

    /**
     * 多指标异常检测统一前缀
     */
    public static final String MULTI_PREFIX = "multi";

    public static final String SLASH = "/";

    public static final String COMMA = ",";

    public static final String PERIOD_KEY_PREFIX = "flink_metric_period:";

}
