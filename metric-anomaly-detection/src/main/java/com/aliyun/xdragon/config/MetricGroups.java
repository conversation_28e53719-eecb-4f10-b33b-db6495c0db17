package com.aliyun.xdragon.config;

import java.util.ArrayList;
import java.util.HashMap;

import com.aliyun.xdragon.util.Constants;

/**
 * <AUTHOR>
 * @date 2022/12/13
 * @description
 */
public class MetricGroups {

    private static HashMap<String, String> groupMap;
    private static HashMap<String, ArrayList<String>> metricsMap;

    static {
        //TODO: Replace the hard-coded group relationships with config file
        groupMap = new HashMap<>(2);
        groupMap.put("xdcrule", "xdc");
        groupMap.put("xdcplan", "xdc");

        metricsMap = new HashMap<>(1);
        ArrayList<String> list = new ArrayList<>(2);
        list.add("xdcrule");
        list.add("xdcplan");
        metricsMap.put("xdc", list);
    }

    /**
     * 对给定的指标名，返回它所在的指标组的名称
     *
     * @param name 指标名
     * @return 指标组的名称
     */
    public static String getGroupName(String name) {
        String[] sub = name.split(Constants.SLASH, 2);
        return groupMap.get(sub[0]) + Constants.SLASH + sub[1];
    }

    /**
     * 对给定的指标名，返回它所在的MetricSet的名称（用于混部场景）
     *
     * @param name 指标名
     * @return MetricSet的名称
     */
    public static String getMetricSetName(String name) {
        String[] sub = name.split(Constants.SLASH);
        return sub[0] + Constants.SLASH + sub[2];
    }

    /**
     * 对给定的指标名，返回它的InstanceId（用于混部场景）
     *
     * @param name 指标名
     * @return InstanceId
     */
    public static String getInstanceId(String name) {
        String[] sub = name.split(Constants.SLASH);
        return sub.length < 3 ? "null" : sub[2];
    }

    /**
     * 对给定的指标组，返回组内的所有指标名
     *
     * @param name 指标组
     * @return 组内的所有指标名
     */
    public static String[] getMetricGroup(String name) {
        String[] sub = name.split(Constants.SLASH, 2);
        ArrayList<String> list = metricsMap.get(groupMap.get(sub[0]));
        String[] ret = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            ret[i] = list.get(i) + Constants.SLASH + sub[1];
        }
        return ret;
    }

}
