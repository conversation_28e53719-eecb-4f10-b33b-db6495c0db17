package com.aliyun.xdragon.flink.task;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.ververica.connector.sls.shaded.com.aliyun.openservices.log.common.FastLog;
import com.alibaba.ververica.connector.sls.shaded.com.aliyun.openservices.log.common.FastLogContent;
import com.alibaba.ververica.connector.sls.shaded.com.aliyun.openservices.log.common.FastLogGroup;
import com.alibaba.ververica.connectors.sls.source.SourceRecord;
import com.aliyun.xdragon.common.enumeration.MetricFilterType;
import com.aliyun.xdragon.util.Constants;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.tuple.Tuple6;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2022/05/19
 * @description sls日志分析task，分析sSourceRecord，检查指标关键信息，产出tuple到下游
 */

public class SlsRawLogParse implements FlatMapFunction<SourceRecord, Tuple6<String, String, Integer, Double, Long, String>> {
    private static final Logger logger = LoggerFactory.getLogger(SlsRawLogParse.class);

    private final String[] metricKeys;
    private final String tsKey;
    private final String[] valueKeys;
    private final String additionalKey;
    private final SimpleDateFormat SDFormat;
    private final String prefix;

    private final Map<String, String> sourceMap;
    private final Map<MetricFilterType, Map<String, Set<String>>> filters;

    public SlsRawLogParse(String metricKey, String tsKey, String valueKey, String additionalKey, String tsDateFormat, String prefix,
                          String filters, String sourceMap) {
        this.metricKeys = metricKey.split(",");
        this.tsKey = tsKey;
        this.additionalKey = additionalKey;
        this.valueKeys = valueKey.split(",");
        SDFormat = tsDateFormat != null ? new SimpleDateFormat(tsDateFormat) : null;
        this.prefix = prefix == null ? "" : prefix;
        this.sourceMap = sourceMap == null ? new HashMap<>() : JSONObject.parseObject(sourceMap, new TypeReference<Map<String, String>>(){});
        this.filters = filters == null ? new HashMap<>() : toMap(JSONObject.parseObject(filters));
    }

    /**
     * json转为map，主要用于处理异常检测配置信息
     *
     * @return Map
     */
    public static Map<MetricFilterType, Map<String, Set<String>>> toMap(JSONObject jsonObj) {
        Map<MetricFilterType, Map<String, Set<String>>> map = new HashMap<>();
        for (String type : jsonObj.keySet()) {
            JSONObject value = (JSONObject) jsonObj.get(type);
            Map<String, Set<String>> innerMap = new HashMap<>();
            MetricFilterType filterType = MetricFilterType.of(type);
            for (String key : value.keySet()) {
                innerMap.put(key, new HashSet<>(value.getJSONArray(key).toJavaList(String.class)));
            }
            if (map.containsKey(filterType)) {
                map.get(filterType).putAll(innerMap);
            }else {
                map.put(filterType, innerMap);
            }
        }
        return map;
    }

    /**
     * 返回的tuple6(数据源ID，指标名，当前时间点，当前值，eventTime, 当前的additional)
     */
    @Override
    public void flatMap(SourceRecord sourceRecord, Collector<Tuple6<String, String, Integer, Double, Long, String>> collector) throws Exception {
        for (FastLogGroup lg : sourceRecord.getLogGroups()) {
            String source = lg.getSource();
            for (FastLog log : lg.getLogs()) {
                Map<String, String> contents = new HashMap<>();
                for (int idx = 0; idx < log.getContentsCount(); idx++) {
                    FastLogContent f = log.getContents(idx);
                    contents.put(f.getKey(), f.getValue());
                }

                String metricName = metricFilter(contents, source);
                if (metricName == null) {
                    continue;
                }

                String metricTs = contents.get(tsKey);
                int metricTsInt;
                if (metricTs == null || metricTs.isEmpty()){
                    logger.debug("metric timestamp:{} missed in log {}", tsKey, log);
                    continue;
                } else if (SDFormat != null) {
                    try {
                        Date d = SDFormat.parse(metricTs);
                        long seconds = d.getTime() / 1000;
                        metricTsInt = (int) seconds;
                    } catch (Exception exception) {
                        logger.debug("metric timestamp format error, need timestamp in {}", SDFormat.toPattern());
                        continue;
                    }
                } else if (!NumberUtils.isDigits(metricTs) ||
                    (metricTs.length() != Constants.METRIC_TS_LENGTH && metricTs.length() != Constants.METRIC_TS_LENGTH_13)) {
                    logger.warn("metric timestamp format error, need timestamp in second (10 digit integer)");
                    continue;
                } else {
                    if (metricTs.length() == Constants.METRIC_TS_LENGTH_13) {
                        metricTs = metricTs.substring(0, Constants.METRIC_TS_LENGTH);
                    }
                    metricTsInt = NumberUtils.toInt(metricTs);
                }

                long eventTime = (long) log.getTime() * 1000;

                for (String valueKey : valueKeys) {
                    String value = contents.get(valueKey);
                    double metricValue;
                    if (value == null || value.isEmpty()){
                        logger.debug("metric value:{} missed in log {}", valueKey, log);
                        continue;
                    } else if (!NumberUtils.isDigits(value) && !NumberUtils.isDigits(value.replaceFirst("\\.", ""))) {
                        logger.debug("metric value must be number");
                        continue;
                    } else {
                        metricValue = NumberUtils.toDouble(value);
                    }

                    String metricFullName = prefix.replace(Constants.METRIC_PREFIX_VALUE_REPLACEMENT, valueKey) + metricName;
                    String additional = this.additionalKey == null ? "{}" : contents.getOrDefault(this.additionalKey, "{}");
                    String metricSourceId = contents.getOrDefault(Constants.METRIC_SOURCE_ID_KEY,
                        sourceMap.getOrDefault(valueKey,"null"));

                    Tuple6<String, String, Integer, Double, Long, String> collectedTuple
                        = new Tuple6<>(metricSourceId, metricFullName, metricTsInt, metricValue, eventTime, additional);
                    try {
                        collector.collect(collectedTuple);
                    } catch (Exception e) {
                        logger.error("metric collect error, metric:{}, exception: {}", collectedTuple,
                            ExceptionUtils.getStackTrace(e));
                    }
                }
            }
        }
    }

    /**
     * 对指标进行过滤，去掉无关信息
     *
     * @param logContents       字典格式的sls数据
     * @param source            sls的__source__信息
     * @return string or null   最终的指标名
     */
    private String metricFilter(Map<String, String> logContents, String source) {
        ArrayList<String> metricNames = new ArrayList<>(metricKeys.length);

        for (String metricKey : metricKeys) {
            String keyName = Constants.SLS_SOURCE_KEY.equals(metricKey) ? source : logContents.get(metricKey);
            if (keyName == null || keyName.isEmpty()) {
                logger.debug("metric name:{} missed in log {}", keyName, logContents);
                return null;
            }
            metricNames.add(keyName);
        }

        if (filters.isEmpty()) {
            return String.join("/", metricNames);
        }

        Map<String, Set<String>> noTermsFilter = filters.get(MetricFilterType.NO_TERMS);
        if (noTermsFilter != null) {
            for (String filterKey : noTermsFilter.keySet()) {
                String keyValue = logContents.get(filterKey);
                if (keyValue == null || keyValue.isEmpty()) {
                    logger.debug("filter metric name:{} missed in log {}", filterKey, logContents);
                    continue;
                }
                if (noTermsFilter.get(filterKey).contains(keyValue)) {
                    return null;
                }
            }
        }

        Map<String, Set<String>> excludesFilter = filters.get(MetricFilterType.EXCLUDES);
        if (excludesFilter != null) {
            for (String filterKey : excludesFilter.keySet()) {
                String keyValue = logContents.get(filterKey);
                if (keyValue == null || keyValue.isEmpty()) {
                    logger.debug("filter metric name:{} missed in log {}", keyValue, logContents);
                    continue;
                }
                for (String excludeStr: excludesFilter.get(filterKey)) {
                    if (keyValue.contains(excludeStr)) {
                        return null;
                    }
                }
            }
        }

        Map<String, Set<String>> termsFilter = filters.get(MetricFilterType.TERMS);
        if (termsFilter != null) {
            for (String filterKey : termsFilter.keySet()) {
                String keyValue = logContents.get(filterKey);
                if (keyValue == null || keyValue.isEmpty()) {
                    logger.debug("filter metric name:{} missed in log {}", keyValue, logContents);
                    return null;
                }
                if (!termsFilter.get(filterKey).contains(keyValue)) {
                    return null;
                }
            }
        }

        Map<String, Set<String>> includesFilter = filters.get(MetricFilterType.INCLUDES);
        if (includesFilter != null) {
            for (String filterKey : includesFilter.keySet()) {
                String keyValue = logContents.get(filterKey);
                if (keyValue == null || keyValue.isEmpty()) {
                    logger.debug("filter metric name:{} missed in log {}", keyValue, logContents);
                    return null;
                }
                for (String includeStr : includesFilter.keySet()) {
                    if (!keyValue.contains(includeStr)) {
                        return null;
                    }
                }
            }
        }

        return String.join("/", metricNames);
    }
}
