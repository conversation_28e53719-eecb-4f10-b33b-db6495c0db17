package com.aliyun.xdragon.flink.state.processors;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.aliyun.xdragon.common.enumeration.algorithm.AnomalyType;
import com.aliyun.xdragon.config.MetricDetectConfig;
import com.aliyun.xdragon.flink.state.dataPoints.DataPoint;
import com.aliyun.xdragon.flink.state.dataPoints.DecomposedDataPoint;
import com.aliyun.xdragon.util.CircularQueue;
import com.aliyun.xdragon.util.Constants;
import com.aliyun.xdragon.util.ReflectionUtils;
import org.apache.commons.math3.stat.StatUtils;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInfoFactory;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple6;
import org.apache.flink.api.java.typeutils.PojoField;
import org.apache.flink.api.java.typeutils.PojoTypeInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@TypeInfo(TriggerBasedProcessor.TriggerBasedProcessorTypeInfoFactory.class)
public class TriggerBasedProcessor extends BaseProcessor<DataPoint>{
    static final Logger logger = LoggerFactory.getLogger(TriggerBasedProcessor.class);

    public TriggerBasedProcessor() {
        this.data = new CircularQueue<>();
    }

    /**
     * 检测当前点是否异常并告警
     *
     * @param curTs  当前点的时间戳
     * @param value  当前点的值
     * @param config 异常检测配置
     * @return List<Tuple4<AnomalyType, Integer, Double, Integer, Double, String>>
     *     异常列表 tuple(异常类型, 异常开始时间点, 异常值, 中断次数, 极值, 创建or更新)
     */
    public List<Tuple6<AnomalyType, Integer, Double, Integer, Double, String>> detect(int curTs, double value,
        int deployTs, MetricDetectConfig config) {
        // 加入新的数据点（由于数据冗余、缺失等原因，可能实际加入0个或多个数据点）
        if (data.size() == 0) {
            data.add(new DataPoint(value));
            return new ArrayList<>();
        }

        if (latestTs >= curTs) {
            return new ArrayList<>();
        }

        fillPoints(curTs, value);
        innerAddValue(curTs, value, curTs >= deployTs - config.getSmoothWinSize() * config.getTsInterval());

        //根据异常生成告警
        Map<AnomalyType, Boolean> anomalies = data.getLast().getAnomalyMap();
        List<Tuple6<AnomalyType, Integer, Double, Integer, Double, String>> anomalyRet = new ArrayList<>();
        for (AnomalyType anomalyType : anomalies.keySet()) {
            Tuple6<AnomalyType, Integer, Double, Integer, Double, String> alarm = getAlarm(anomalyType, config);
            if (alarm != null) {
                anomalyRet.add(alarm);
            }
        }
        return anomalyRet;
    }

    @Override
    public void loadConfig(MetricDetectConfig config) {
        super.loadConfig(config);
        int newSize = expireTs / tsInterval;
        this.data.resetCapacity(newSize);
    }

    @Override
    protected int addDataPoint(double value) {
        data.add(new DataPoint(value));
        return data.size() - 1;
    }


    @Override
    protected void decompose(int id) {
        update(id, false);
    }


    @Override
    protected void backtrack(int jumpId) {
        for (int i = jumpId; i < data.size(); i++) {
            update(i, true);
        }
    }

    /**
     * 更新指定数据点的分解。如果没有在回溯修改，需要去除突变点的残差影响，以避免其影响趋势
     *
     * @param id        指定数据点的索引
     * @param backtrack 是否在回溯修改
     */
    private void update(int id, boolean backtrack) {
        DataPoint point = data.get(id);
        int smoothPos = Math.max(id + 1 - this.smoothSize, lastJump);
        double[] valuesToSmooth = new double[id + 1 - smoothPos];
        for (int i = smoothPos; i <= id; i++) {
            //准备窗口内用于平滑的数据点
            if (!backtrack && data.get(i).getMutationAnomaly() != null) {
                //去除突变点的残差的影响
                valuesToSmooth[i - smoothPos] = data.get(i).getTrend();
            } else {
                valuesToSmooth[i - smoothPos] = data.get(i).getValue();
            }
        }
        //中值平滑
        double smooth = StatUtils.percentile(valuesToSmooth, Constants.PERCENTILE_50TH);
        point.setTrend(smooth);
    }

    public static class TriggerBasedProcessorTypeInfoFactory extends TypeInfoFactory<TriggerBasedProcessor> {

        @Override
        public TypeInformation<TriggerBasedProcessor> createTypeInfo(Type t,
            Map<String, TypeInformation<?>> genericParameters) {

            List<PojoField> fieldList = getPojoFieldList();
            try {
                TypeInformation<CircularQueue<DataPoint>> ti =
                    TypeInformation.of(new TypeHint<CircularQueue<DataPoint>>() {
                    });
                fieldList.add(new PojoField(BaseProcessor.class.getDeclaredField("data"), ti));
            } catch (NoSuchFieldException e) {
                logger.error("Field is not found.", e);
            }
            return new PojoTypeInfo<>(TriggerBasedProcessor.class, fieldList);
        }
    }
}
