package com.aliyun.xdragon.flink.state.processors;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

import com.aliyun.xdragon.algorithm.common.DetectorConstants;
import com.aliyun.xdragon.algorithm.detector.custom.BaselineDetector;
import com.aliyun.xdragon.algorithm.detector.mutation.CauchyMutationDetector;
import com.aliyun.xdragon.algorithm.detector.mutation.ExtremeValueTheoryDetector;
import com.aliyun.xdragon.algorithm.imputation.Imputation;
import com.aliyun.xdragon.common.enumeration.MissingFillType;
import com.aliyun.xdragon.common.enumeration.algorithm.AnomalyType;
import com.aliyun.xdragon.config.MetricDetectConfig;
import com.aliyun.xdragon.detector.DataPointMkTrendDetector;
import com.aliyun.xdragon.flink.state.dataPoints.DataPoint;
import com.aliyun.xdragon.util.CircularQueue;
import com.aliyun.xdragon.util.Constants;
import com.aliyun.xdragon.util.ReflectionUtils;
import com.aliyun.xdragon.util.StatisticsAccumulator;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.math3.stat.StatUtils;
import org.apache.commons.math3.util.FastMath;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.tuple.Tuple6;
import org.apache.flink.api.java.typeutils.MapTypeInfo;
import org.apache.flink.api.java.typeutils.PojoField;
import org.apache.flink.api.java.typeutils.TupleTypeInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * <AUTHOR>
 * @date 2022/8/4
 * @description 周期和非周期数据处理的基类，存放公共逻辑
 */
@Data
public abstract class BaseProcessor<T extends DataPoint> {

    private static final Logger logger = LoggerFactory.getLogger(BaseProcessor.class);

    protected CircularQueue<T> data;

    protected HashMap<String, Tuple3<Integer, Double, Double>> lastAlarmMap = new HashMap<>();

    protected int tsInterval;

    protected transient Map<AnomalyType, Properties> detectParams;

    protected transient int smoothSize;

    protected transient int expireTs;

    protected transient MissingFillType missingFillType;

    protected int periodLength;

    /**
     * 上一次趋势跳变发生时的索引，内部状态
     */
    protected int lastJump = 0;

    /**
     * 最近一个数据点的时间戳
     */
    protected int latestTs = 0;
    /**
     * MK-Test使用的S统计量（增量计算以提高检测速度）
     */
    protected StatisticsAccumulator statisticsAccumulator;

    /**
     * 创建BaseProcessor类的对象，根据其是否具有周期性返回不同的子类对象
     *
     * @param period 周期长度
     * @return BaseProcessor类对象
     */
    public static BaseProcessor<?> create(int period) {
        BaseProcessor<?> processor;
        if (period > 0) {
            processor = new PeriodicProcessor();
        } else {
            processor =  new AperiodicProcessor();
        }
        processor.setPeriodLength(period);
        return processor;
    }

    public static List<PojoField> getPojoFieldList() {
        List<PojoField> fieldList = ReflectionUtils.initPojoFieldListWithDeclaredBasicTypes(BaseProcessor.class);
        try {
            fieldList.add(new PojoField(BaseProcessor.class.getDeclaredField("lastAlarmMap"),
                new MapTypeInfo<>(BasicTypeInfo.STRING_TYPE_INFO,
                    new TupleTypeInfo<>(BasicTypeInfo.INT_TYPE_INFO, BasicTypeInfo.DOUBLE_TYPE_INFO, BasicTypeInfo.DOUBLE_TYPE_INFO))));
            fieldList.add(new PojoField(BaseProcessor.class.getDeclaredField("statisticsAccumulator"),
                TypeInformation.of(StatisticsAccumulator.class)));
        } catch (NoSuchFieldException e) {
            logger.error("Field is not found.", e);
        }
        return fieldList;
    }

    /**
     * 将另一个Processor中的数据批量加入该Processor
     *
     * @param another 另一个Processor
     * @param config  异常检测配置
     */
    public void copyValues(BaseProcessor another, MetricDetectConfig config, int startTs) {
        loadConfig(config);
        for (int i = 0; i < another.data.size(); i++) {
            DataPoint point = (DataPoint)another.data.get(i);
            int curTs = startTs + i * tsInterval;
            innerAddValue(curTs, point.getValue(), false);
        }
    }

    /**
     * 根据params设置算法的所有参数
     *
     * @param config 参数
     */
    public void loadConfig(MetricDetectConfig config) {
        this.tsInterval = config.getTsInterval();
        this.detectParams = config.getDetectParams();
        this.smoothSize = config.getSmoothWinSize();
        this.expireTs = config.getExpireTs();
        this.missingFillType = config.getMissFillType();

        double precision = StatisticsAccumulator.loadPrecision(detectParams);
        if (Double.isNaN(precision)) {
            //无需趋势检测
            this.statisticsAccumulator = null;
        } else if (this.statisticsAccumulator == null ||
            Math.abs(this.statisticsAccumulator.getPrecision() - precision) > Constants.DOUBLE_DIFF) {
            //创建累加器并加入数据
            this.statisticsAccumulator = new StatisticsAccumulator(precision);
            this.statisticsAccumulator.init(this.data, this.lastJump, this.data.size());
        }
    }

    /**
     * 检测当前点是否异常并告警
     *
     * @param curTs  当前点的时间戳
     * @param value  当前点的值
     * @param config 异常检测配置
     * @return List<Tuple4<AnomalyType, Integer, Double, Integer, Double>>
     *     异常列表 tuple(异常类型, 异常开始时间点, 异常值, 中断次数, 极值, 创建or更新)
     */
    public List<Tuple6<AnomalyType, Integer, Double, Integer, Double, String>> detect(int curTs, double value, MetricDetectConfig config) {
        loadConfig(config);
        //加入新的数据点（由于数据冗余、缺失等原因，可能实际加入0个或多个数据点）
        int cnt = addValue(curTs, value);
        //如果实际没有加入任何数据点，返回空的异常列表
        if (cnt == 0) {
            return new ArrayList<>();
        }
        //根据异常生成告警
        Map<AnomalyType, Boolean> anomalies = data.getLast().getAnomalyMap();
        List<Tuple6<AnomalyType, Integer, Double, Integer, Double, String>> anomalyRet = new ArrayList<>();
        for (Map.Entry<AnomalyType, Boolean> anomalyEntry: anomalies.entrySet()) {
            // 如果无需告警则直接跳过，无需告警的场景有：
            // 1、配置中没有检测突变的需求，但是为了跳变和回溯而额外检测的
            // 2、被EVT过滤掉的突变
            if (!anomalyEntry.getValue()) {
                continue;
            }
            Tuple6<AnomalyType, Integer, Double, Integer, Double, String> alarm = getAlarm(anomalyEntry.getKey(), config);
            if (alarm != null) {
                anomalyRet.add(alarm);
            }
        }
        jumpDetect(data.size()-1);
        return anomalyRet;
    }

    /**
     * 加入新的数据点（不一定是标准的，可能存在缺失、重复等问题）
     *
     * @param curTs 数据点的时间戳
     * @param value 数据点的值
     * @return 实际加入的数据点数目
     */
    public int addValue(int curTs, double value) {
        if (data.size() == 0) {
            // 这里有新增检测所以为true
            innerAddValue(curTs, value, true);
            return 1;
        }

        // 当前点不是最新的点，直接忽略
        if (latestTs >= curTs) {
            return 0;
        }

        int cnt = fillPoints(curTs, value);
        // 把当前点加入进去，只有最新的点才会进行异常检测
        innerAddValue(curTs, value, true);
        return cnt;
    }

    public int fillPoints(int curTs, double value) {
        //相当于(curTs - latestTs) / tsInterval的结果取上整
        int cnt = (curTs - latestTs + tsInterval - 1) / tsInterval;
        if (cnt > Constants.MISSING_CLEAR_PARAM * (1.0 * expireTs / tsInterval)) {
            //缺失过于严重，清空已有数据
            clear();
            cnt = 1;
        } else {
            //此时需要补点
            int fillTs = latestTs + tsInterval;
            while (fillTs < curTs) {
                double fill = calcFillValue(missingFillType, curTs, value, tsInterval);
                innerAddValue(fillTs, fill, false);
                fillTs += tsInterval;
            }
        }
        return cnt;
    }

    /**
     * 加入无缺失、无重复的数据点
     *
     * @param curTs 数据点的时间戳
     * @param value 数据点的数值
     * @param needDetect 是否检查异常
     */
    public void innerAddValue(int curTs, double value, boolean needDetect) {
        //在S统计量中去除将从循环队列中滑出的数据点的影响
        if (statisticsAccumulator != null && data.isAtFullCapacity() && lastJump == 0) {
            statisticsAccumulator.removeFirst(data, lastJump, data.size());
        }
        //更新跳变发生的索引lastJump。由于在循环队列满时插入数据会自动移除第一个数据点，已有数据点的索引都会减一
        if (data.isAtFullCapacity() && lastJump > 0) {
            lastJump--;
        }
        latestTs = curTs;
        //加入数据点
        int id = addDataPoint(value);
        //分解数据点
        decompose(id);
        if (needDetect) {
            //异常检测
            commonDetect(id);
            innerDetect(id);
        }
    }

    /**
     * 对指定数据点进行异常检测
     *
     * @param id 指定数据点的索引
     */
    public void innerDetect(int id) {
        if (id - lastJump + 1 < Constants.METRIC_BASE_LEN_LIMIT) {
            return;
        }
        // 准备数据，这里突变检测必须要做，因为突变检测是跳变的基础，跳变的发生会决定时候对历史数据进行回溯修正
        double[] residualList = new double[id];
        // 这里开始下边使用跳变点和上次告警点的最大值
        double[] mutationTrendList = new double[id - lastJump + 1];
        for (int i = 0; i < id; i++) {
            if (i + lastJump < id) {
                mutationTrendList[i] = data.get(i + lastJump).getTrend();
            }
            residualList[i] = data.get(i).getResidual();
        }
        mutationTrendList[id - lastJump] = data.get(id).getTrend();
        if (!detectParams.containsKey(AnomalyType.SPIKE) && !detectParams.containsKey(AnomalyType.DIP)) {
            AnomalyType newMutationAnomaly = CauchyMutationDetector.detectAnomaly(mutationTrendList, residualList,
                data.get(id).getResidual(), data.get(id).getValue(), new Properties());
            if (newMutationAnomaly != null) {
                data.get(id).addAnomaly(newMutationAnomaly, false);
            }
        } else {
            mutationDetect(id, mutationTrendList, residualList);
        }

        Properties rampParams = detectParams.getOrDefault(AnomalyType.RAMP_UP, detectParams.get(AnomalyType.RAMP_DOWN));
        if (rampParams != null) {
            int trendStart = FastMath.max(lastJump, getTrendStart());
            double[] trendList = new double[id - trendStart + 1];
            for (int i = trendStart; i <= id; i++) {
                trendList[i-trendStart] = data.get(i).getTrend();
            }
            AnomalyType newRampAnomaly = null;
            //更新S统计量，加入新的数据点
            if (statisticsAccumulator != null) {
                int preS = statisticsAccumulator.getS();
                statisticsAccumulator.add(data, lastJump, data.size() - 1);
                int curS = statisticsAccumulator.getS();
                double accumulation = MapUtils.getDoubleValue(rampParams, DetectorConstants.ACCUMULATION,
                    DetectorConstants.DEFAULT_ACCUMULATION);
                // 如果新加入的点让趋势累计值变大，即新加入的点大于原来的80%的点才认为有检测趋势的必要
                if (preS >= 0 && (curS - preS >= trendList.length * accumulation)
                    || preS <= 0 && (preS - curS) >= trendList.length * accumulation) {
                    newRampAnomaly = DataPointMkTrendDetector.detectAnomaly(trendList, curS, rampParams);
                }
            } else {
                newRampAnomaly = DataPointMkTrendDetector.detectAnomaly(trendList, rampParams);
            }
            if (newRampAnomaly != null) {
                data.get(id).addAnomaly(newRampAnomaly);
            }
        }
    }

    /**
     * spike/dip detect
     *
     * @param id 要检测点的索引
     * @param mutationTrendList 趋势列表
     * @param residualList 残差列表
     */
    public void mutationDetect(int id, double[] mutationTrendList, double[] residualList) {
        // 如果配置了突变检测，突增和突降检测需要分开看，因为他们的检测参数是不一样的
        AnomalyType newMutationAnomaly = null;
        // 中位数缓存
        double median = StatUtils.percentile(mutationTrendList, 50);

        Properties params = detectParams.get(AnomalyType.SPIKE);
        double spikeThreshold = DetectorConstants.DEFAULT_CAUCHY_THRESHOLD;
        boolean spikeDetected = false;
        if (params != null) {
            spikeThreshold = MapUtils.getDoubleValue(params, DetectorConstants.THRESHOLD, spikeThreshold);
            if (CauchyMutationDetector.paramsCheck(params, median, data.get(id).getValue())) {
                newMutationAnomaly = CauchyMutationDetector.detectAnomaly(residualList,
                        data.get(id).getResidual(), spikeThreshold, false);
                spikeDetected = true;
            }
            // 1.如果检出了突增异常类型，则记录并退出
            if (newMutationAnomaly == AnomalyType.SPIKE) {
                //如果EVT的入参是有效的，且EVT检测未发现异常，则不允许告警
                data.get(id).addAnomaly(newMutationAnomaly,
                        !isEVTDisable(residualList, data.get(id).getResidual(), newMutationAnomaly, params));
                return;
            }
            // 2.如果检出了突降异常，也需记录。有两个作用
            //   a.这样在如果后续不检测突降，则用于后续异常回溯逻辑，可以把突降点去噪，从而防止类似先降后增导致的误报
            //   b.方便后续判断，如果检测突降的参数严格程度是小于突增的，则省去一步检测
            // 注意这里先把allowAlert记录为false
            if (newMutationAnomaly == AnomalyType.DIP) {
                data.get(id).addAnomaly(newMutationAnomaly, false);
            }
        }

        params = detectParams.get(AnomalyType.DIP);
        // 是否配置了突降检测且通过了波动判定
        if (params != null && CauchyMutationDetector.paramsCheck(params, median, data.get(id).getValue())) {
            double dipThreshold = MapUtils.getDoubleValue(params, DetectorConstants.THRESHOLD,
                    DetectorConstants.DEFAULT_CAUCHY_THRESHOLD);

            // 3.如果突增方向的检测检出了突降异常类型，且严格程度大于突降，则直接沿用突增检测的结果即可
            if (newMutationAnomaly == AnomalyType.DIP && dipThreshold <= spikeThreshold) {
                // 如果EVT的入参是有效的，且EVT检测未发现异常，则不允许告警
                data.get(id).addAnomaly(newMutationAnomaly,
                        !isEVTDisable(residualList, data.get(id).getResidual(), newMutationAnomaly, params));
                return;
            }

            // 4.此时 newMutationAnomaly == null, 突增方向没有检测出异常 or 压根没有突增检测
            //   如果检测过突增异常，且突增方向的检测阈值小于等于突降（即突增更容易检出），则无需再次进行检测
            if (newMutationAnomaly == null && spikeDetected && dipThreshold >= spikeThreshold) {
                return;
            }

            newMutationAnomaly = CauchyMutationDetector.detectAnomaly(residualList, data.get(id).getResidual(),
                    dipThreshold, false);
            if (newMutationAnomaly == AnomalyType.DIP) {
                // 如果EVT的入参是有效的，且EVT检测未发现异常，则不允许告警
                data.get(id).addAnomaly(newMutationAnomaly,
                        !isEVTDisable(residualList, data.get(id).getResidual(), newMutationAnomaly, params));
            }
            if (newMutationAnomaly == AnomalyType.SPIKE) {
                // 这里如果检测出突增，说明配置中无需检测突增或者突增检测没有检出，则把突增异常记录下来，只用于后续异常回溯逻辑
                data.get(id).addAnomaly(newMutationAnomaly, false);
            }
        }
    }

    /**
     *  在柯西异常检测的基础上，进行复杂度更高的EVT检测以去噪
     */
    public boolean isEVTDisable(double[] residualList, double curRes, AnomalyType anomalyType, Properties params) {
        int anomalyNum = 0;
        for (int i = residualList.length - 1; i > 0 ; i--) {
            if (!data.get(i).hasAnomaly(anomalyType)) {
               break;
            }
            anomalyNum++;
        }
        if (anomalyNum > 0) {
            residualList = Arrays.copyOfRange(residualList, 0, residualList.length - anomalyNum);
        }

        return ExtremeValueTheoryDetector.detect(residualList, curRes, anomalyType, params) == null;
    }

    /**
     * 清除保存的数据
     */
    public void clear() {
        this.data.clear();
        this.lastAlarmMap.clear();
        this.lastJump = 0;
    }

    /**
     * 检测跳变异常
     *
     * @param id 要检测点的索引
     */
    protected void jumpDetect(int id) {
        // 如果检测出突变异常，需要判断下是否可能出现了跳变
        AnomalyType mutation = data.get(id).getMutationAnomaly();
        // 突变次数达跳变阈值则判断为跳变，需要对历史进行回溯，这一步需要放在最后面，原因是现在会使用历史的残差和趋势计算异常度，回溯修改不能提前做
        if (mutation != null) {
            int jumpLag = FastMath.max(DetectorConstants.DEFAULT_JUMP_LAG, this.smoothSize);
            int mutationCnt = 1;
            for (int i = 1; i < jumpLag; i++) {
                if (data.get(id - i).hasAnomaly(mutation)) {
                    mutationCnt++;
                } else {
                    break;
                }
            }
            if (mutationCnt == jumpLag) {
                // 发现跳变则回溯和重构
                lastJump = data.size() - mutationCnt;
                backtrack(lastJump);
                //重构跳变后的S统计量
                if (statisticsAccumulator != null) {
                    statisticsAccumulator.init(data, lastJump, data.size());
                }
            }
        }
    }

    /**
     * 向数据序列中加入数据点，不进行其它任何额外操作
     *
     * @param value 数据点的数值
     * @return 加入数据点的索引
     */
    protected abstract int addDataPoint(double value);

    /**
     * 分解指定数据点
     *
     * @param id 分解数据点的索引
     */
    protected abstract void decompose(int id);

    /**
     * 回溯修改跳变后的数据点的分解
     *
     * @param jumpId 跳变点的索引
     */
    protected abstract void backtrack(int jumpId);

    /**
     * 基础、公共的异常检测
     *
     * @param id 要检测点的索引
     */
    private void commonDetect(int id) {
        //检测新增异常
        DataPoint point = data.get(id);
        Properties newlyParam = detectParams.get(AnomalyType.NEWLY);
        // 总点数小于窗口才有新增这一个概念，大于则表示这个异常已经持续一段时间了
        if (newlyParam != null && data.size() <= MapUtils.getIntValue(newlyParam, DetectorConstants.NEWLY_MAX_WINDOWS,
            DetectorConstants.DEFAULT_NEWLY_MAX_WINDOWS)) {
            if (latestTs >= MapUtils.getIntValue(newlyParam, DetectorConstants.THRESHOLD, 0)){
                point.addAnomaly(AnomalyType.NEWLY);
            }
        }

        //检测静态机基线异常
        for (AnomalyType anomaly : new AnomalyType[] {AnomalyType.DROP, AnomalyType.RAISE}) {
            Properties param = detectParams.get(anomaly);
            if (param != null && BaselineDetector.detectAnomaly(point.getValue(), param) == anomaly) {
                point.addAnomaly(anomaly);
            }
        }
    }

    /**
     * 判断当前指标的异常是否达到异常告警要求
     *
     * @param anomalyType 异常类型
     * @param config      异常检测配置
     * @return tuple4     异常点
     */
    public Tuple6<AnomalyType, Integer, Double, Integer, Double, String> getAlarm(AnomalyType anomalyType, MetricDetectConfig config) {
        Properties params = config.getDetectParamOf(anomalyType);
        if (params == null) {
            return null;
        }
        int continuance = MapUtils.getIntValue(params, Constants.CONTINUANCE, Constants.DEFAULT_CONTINUANCE);
        int silence = MapUtils.getIntValue(params, Constants.SILENCE, Constants.DEFAULT_SILENCE);
        int breakPointNum = MapUtils.getIntValue(params, Constants.BREAK_POINT_NUM, Constants.DEFAULT_BREAK_POINT_NUM);
        double baseline = MapUtils.getDoubleValue(params, Constants.MUTATION_BASELINE,
            anomalyType == AnomalyType.SPIKE ? Double.MIN_VALUE : Double.MAX_VALUE);

        // 获取上次告警信息
        Tuple3<Integer, Double, Double> lastAlarmTuple = getLastAlarmTuple(anomalyType);

        // 新增告警处理
        if (anomalyType == AnomalyType.NEWLY) {
            // 且没有告警过
            if (lastAlarmTuple == null) {
                Properties newlyParam = detectParams.get(AnomalyType.NEWLY);
                double newlySum = 0;
                // 直接遍历统计所有，newlyMaxWindow内的数据才有可能有新增告警
                double maxValue = 0;
                for (int i = 0; i < data.size() ; i++) {
                    if (data.get(i).hasAnomaly(AnomalyType.NEWLY)) {
                        newlySum += data.get(i).getValue();
                        if (data.get(i).getValue() > maxValue) {
                            maxValue = data.get(i).getValue();
                        }
                    }
                }
                if (newlySum >= MapUtils.getDoubleValue(newlyParam, DetectorConstants.VOLATILITY_VALUE)) {
                    lastAlarmMap.put(anomalyType.getType(), new Tuple3<>(latestTs, newlySum, maxValue));
                    return new Tuple6<>(anomalyType, latestTs, newlySum, 0, maxValue, "create");
                }
            }
            return null;
        }

        boolean inSilence = isInSilence(lastAlarmTuple, silence);
        if (lastAlarmTuple != null && anomalyType.getType().equals(AnomalyType.SPIKE.getType())
                && MapUtils.getBooleanValue(params, Constants.NEED_UPDATE, false)) {
            boolean isWorsen = false;
            // 检测类型1：告警后紧接着继续恶化
            if (lastAlarmTuple.f0 + tsInterval == latestTs) {
                if (anomalyType.isUp() && data.getLast().getValue() > lastAlarmTuple.f2
                        || !anomalyType.isUp() && data.getLast().getValue() < lastAlarmTuple.f2) {
                    isWorsen = true;
                }
            } else if (inSilence) {
                // 检测类型2：静默期内表现翻倍
                double volatility = MapUtils.getDoubleValue(params, DetectorConstants.VOLATILITY_VALUE, DetectorConstants.DEFAULT_VOLATILITY);
                double volatilityValue = FastMath.max(MapUtils.getDoubleValue(params, DetectorConstants.VOLATILITY_VALUE,
                        DetectorConstants.DOUBLE_DIFF), DetectorConstants.DOUBLE_DIFF);
                double diff = FastMath.max(lastAlarmTuple.f2 * volatility, volatilityValue);
                // 突增时直接判断波动量
                // 突降时如果原最小值已经是0则没有可恶化的空间了，如果恶化的幅度小于波动同样不告警
                if (anomalyType.isUp() && data.getLast().getValue() >= diff + lastAlarmTuple.f2) {
                    isWorsen = true;
                } else if (!anomalyType.isUp() && lastAlarmTuple.f2 > Constants.DOUBLE_DIFF
                        && data.getLast().getValue() <= FastMath.max(lastAlarmTuple.f2 - diff, 0)) {
                    isWorsen = true;
                }
            }
            if (isWorsen) {
                return getWorsenAlarm(anomalyType, lastAlarmTuple);
            }
        }

        // 在静默期则跳过
        if (inSilence) {
            return null;
        }

        // 趋势异常需要做二次趋势检测，判断新的告警相对上次是否继续上升了
        if (anomalyType.getType().equals(AnomalyType.RAMP_UP.getType())) {
            // 如果指标有告警过，且上次告警的时间未过期，则需判定
            int trendStart = getTrendStart();
            if (trendStart > 0) {
                List<Double> diff = new ArrayList<>();
                for (int i = trendStart - 1, j = data.size() - 1; i >= 0 && j >= trendStart; i--, j--) {
                    diff.add(data.get(j).getTrend() - data.get(i).getTrend());
                }
                double mean = StatUtils.mean(diff.stream().mapToDouble(Double::doubleValue).toArray());
                // 如果新的指标段判定为趋势上升，但是平均变化小于精度则无需告警
                if (anomalyType.equals(AnomalyType.RAMP_UP) && mean < statisticsAccumulator.getPrecision()) {
                    return null;
                }
                // 如果新的指标段判定为趋势下降，但是平均变化大于负精度则无需告警
                if (anomalyType.equals(AnomalyType.RAMP_DOWN) && mean > -statisticsAccumulator.getPrecision()) {
                    return null;
                }
            }
        }

        // 突变类型需要考虑衰变和基线
        if (anomalyType.getType().equals(AnomalyType.SPIKE.getType())) {
            if (isDecaying(lastAlarmTuple) ||
                !overBaseline(anomalyType, data.getLast().getValue(), baseline)) {
                return null;
            }
        }
        // 异常满足连续性即返回相关异常
        return continuanceAchieved(anomalyType, continuance, breakPointNum);
    }

    /**
     * 这里趋势的数据开始下标，从上次告警点后一个点开始提取，这段数据趋势才会进行检测
     *
     * @return int
     */
    private Integer getTrendStart() {
        int trendStart = 0;
        if (lastAlarmMap.containsKey(AnomalyType.RAMP_UP.getType())) {
            int lastTrendAlarmTs = lastAlarmMap.get(AnomalyType.RAMP_UP.getType()).f0;
            // 如果上次告警点还未过期则计算下标
            if (latestTs - lastTrendAlarmTs < expireTs) {
                trendStart = data.size() - (latestTs - lastTrendAlarmTs) / tsInterval;
            }
        }
        return trendStart;
    }

    /**
     * 判断当前异常是否在静默期内
     *
     * @param lastAlarmTuple 上次告警信息
     * @param silence        静默时长
     * @return boolean
     */
    private boolean isInSilence(Tuple3<Integer, Double, Double> lastAlarmTuple, int silence) {
        // 如果没有告警过则认为不在静默时间 或 上一次告警已经超出指标本身的过期时间
        if (lastAlarmTuple == null) {
            return false;
        }
        return lastAlarmTuple.f0 + silence >= latestTs;
    }

    /**
     * 获取告警异常值
     *
     * @param from      int
     * @param to        int
     * @param curValue  double
     * @return          double
     */
    private double getAlarmValue(int from, int to, double curValue, AnomalyType anomalyType) {
        Double median = getMedian(from, to);
        int sign = anomalyType.isUp() ? 1 : -1;
        double alarmValue = sign * Double.MAX_VALUE;
        if (median != null && FastMath.abs(median - 0) >= Constants.DOUBLE_DIFF) {
            alarmValue = (curValue - median) / median;
        }
        return alarmValue;
    }

    /**
     * 对于的异常是否有告警记录，有则返回上次的告警时间
     *
     * @param anomalyType   AnomalyType
     * @return              Tuple2<Integer, Double, Double>
     */
    private Tuple3<Integer, Double, Double> getLastAlarmTuple(AnomalyType anomalyType) {
        Tuple3<Integer, Double, Double> lastAlarmTuple = lastAlarmMap.get(anomalyType.getType());
        if (lastAlarmTuple == null || lastAlarmTuple.f0 + expireTs < latestTs) {
            return null;
        }
        return lastAlarmTuple;
    }

    /**
     * 获取恶化告警
     *
     * @param anomalyType 异常类型
     * @param lastAlarmTuple 上次告警信息
     */
    private Tuple6<AnomalyType, Integer, Double, Integer, Double, String> getWorsenAlarm(
            AnomalyType anomalyType, Tuple3<Integer, Double, Double> lastAlarmTuple) {
        Double alarmValue = lastAlarmTuple.f1;
        if (alarmValue < Double.MAX_VALUE) {
            alarmValue = (alarmValue + 1) * (data.getLast().getValue() / lastAlarmTuple.f2) - 1;
        }
        lastAlarmMap.put(anomalyType.getType(),
                new Tuple3<>(latestTs, alarmValue, data.getLast().getValue()));
        // 这里更新时告警时间还是用之前的,作为合并的锚点
        return new Tuple6<>(anomalyType, lastAlarmTuple.f0, alarmValue, 0, data.getLast().getValue(), "update");
    }

    /**
     * 判断突变、跳变值是否满足基础基线
     *
     * @param anomalyType 异常类型
     * @param curValue    当前值
     * @param baseline    基线值
     * @return boolean  是否过线
     */
    private boolean overBaseline(AnomalyType anomalyType, double curValue, double baseline) {
        if (anomalyType == AnomalyType.SPIKE) {
            return curValue >= baseline;
        }
        if (anomalyType == AnomalyType.DIP) {
            return curValue <= baseline;
        }
        return false;
    }

    /**
     * 针对突变类异常加入衰减判定，超出衰减阈值才会告警
     *
     * @param lastAlarmTuple 上次异常信息
     * @return boolean
     */
    private boolean isDecaying(Tuple3<Integer, Double, Double> lastAlarmTuple) {
        // 如果没有告警过或者上一次告警已经超出指标本身的过期时间则认为不在静默中

        if (lastAlarmTuple == null || lastAlarmTuple.f2 <= data.getLast().getValue()) {
            return false;
        }

        // 这里做了一个抛物线衰减，从而提高后续异常的阈值，公式x = ay^2 + b
        int x1 = 0;
        double y1 = FastMath.abs(lastAlarmTuple.f1 - data.getLast().getTrend());
        int x2 = (latestTs - lastAlarmTuple.f0) / tsInterval;
        double y2 = FastMath.abs(data.getFirst().getResidual());
        if (y1 < y2) {
            return false;
        }
        int b = expireTs / tsInterval;
        double a = (x1 - b) / FastMath.pow(y1, 2);

        double expectY2 = FastMath.sqrt((x2 - b) / a);
        return y2 <= expectY2;
    }

    /**
     * 计算指定范围中位数，左闭右开
     *
     * @param from  int
     * @param to    int
     * @return      double
     */
    private Double getMedian(int from, int to) {
        double[] trendList = new double[to - from];
        for (int i = from; i < to; i++) {
            trendList[i-from] = data.get(i).getTrend();
        }
        if (trendList.length == 0) {
            return null;
        }
        return StatUtils.percentile(trendList, Constants.PERCENTILE_50TH);
    }


    /**
     * 判断连续性是否满足要求，目前需要判定连续性的异常有：
     *      趋势异常 trend
     *      突变异常 mutation
     *      基线异常 baseline
     *
     * @param anomalyType   异常类型
     * @param continuance   连续性阈值
     * @param breakPointNum 非异常点容忍阈值
     * @return tuple4   异常点
     */
    private Tuple6<AnomalyType, Integer, Double, Integer, Double, String> continuanceAchieved(AnomalyType anomalyType, int continuance,
        int breakPointNum) {
        int tmpContinuance = 0;
        int tmpBreakPointNum = 0;
        // 异常点告警标志，统计需要告警的点数
        int needAlertNum = 0;
        // 获取连续的极值
        double extremumValue = data.getLast().getValue();
        for (int i = data.size() - 1; i >= 0; i--) {
            if (data.get(i).hasAnomaly(anomalyType)) {
                tmpContinuance += 1;
                if (anomalyType.isUp()) {
                    if (data.get(i).getValue() > extremumValue) {
                        extremumValue = data.get(i).getValue();
                    }
                }else {
                    if (data.get(i).getValue() < extremumValue) {
                        extremumValue = data.get(i).getValue();
                    }
                }
                if (data.get(i).getAnomalyMap().get(anomalyType)) {
                    needAlertNum++;
                }
            } else {
                tmpBreakPointNum += 1;
            }

            if (tmpContinuance >= continuance && needAlertNum > continuance / 2) {
                // 计算异常最开始检测到的时间
                int anomalyStartTs = latestTs - (data.size() - i - 1) * tsInterval;
                double alarmValue;
                if (anomalyType.getType().equals(AnomalyType.RAMP_UP.getType())) {
                    // 趋势异常的的告警值为趋势斜率，趋势斜率用整体斜率的中位数评估
                    int trendStart = getTrendStart();
                    List<Double> slopes = new ArrayList<>();
                    for (int l = trendStart + 1; l < data.size(); l++) {
                        for (int m = trendStart; m < l; m++) {
                            slopes.add((data.get(l).getTrend() - data.get(m).getTrend()) / (l - m));
                        }
                    }
                    alarmValue = StatUtils.percentile(
                        slopes.stream().mapToDouble(Double::doubleValue).toArray(), Constants.PERCENTILE_50TH);
                    // 趋势异常发现后S统计量更新为0
                    if (statisticsAccumulator != null) {
                        statisticsAccumulator.setS(0);
                    }
                } else if (anomalyType.getType().equals(AnomalyType.SPIKE.getType())){
                    // 突增异常告警连续段中的最大偏差相对历史中位数的变化作为异常告警值
                    alarmValue = getAlarmValue(lastJump, i, extremumValue, anomalyType);
                } else {
                    // 基线异常告警连续段中的最大偏差作为异常告警值
                    alarmValue = extremumValue;
                }
                lastAlarmMap.put(anomalyType.getType(), new Tuple3<>(latestTs, alarmValue, extremumValue));
                return new Tuple6<>(anomalyType, anomalyStartTs, alarmValue, tmpBreakPointNum, extremumValue, "create");
            }
            if (tmpBreakPointNum > breakPointNum) {
                return null;
            }
        }

        return null;
    }

    /**
     * 计算缺失点的填补值
     *
     * @param missingFillType 补点类型
     * @param curTs           新到来的数据点的时间戳
     * @param value           新到来的数据点的值
     * @param tsInterval      标准时间间隔
     * @return double         填补值
     */
    public double calcFillValue(MissingFillType missingFillType, int curTs, double value, int tsInterval) {
        int winSize = data.size();
        missingFillType = ObjectUtils.defaultIfNull(missingFillType, MissingFillType.PREVIOUS);

        //如果数据点不足，一律转为前值填补。如果仍然不足，则转为零值填补（按目前的逻辑，前值填补的数据点一定是充足的）
        if (winSize + 1 < Imputation.getMinPoints(missingFillType)) {
            missingFillType = (winSize + 1 < Imputation.getMinPoints(MissingFillType.PREVIOUS)) ?
                MissingFillType.ZERO : MissingFillType.PREVIOUS;
        }
        //如果填补策略需要的数据点数目是固定的，则仅传入需要的数据点，以减少包装等时间开销
        if (Imputation.isPointNumFixed(missingFillType)) {
            winSize = Imputation.getMinPoints(missingFillType) - 1;
        } else {
            // 非固定点数则取理想和winSize的最小值，以减少包装等时间开销
            winSize = FastMath.min(winSize, Imputation.getFinePoints(missingFillType));
        }

        //准备数据
        double[] timeArray = new double[winSize + 1];
        double[] valueArray = new double[winSize + 1];
        int st = data.size() - winSize;
        for (int i = 0; i < winSize; i++) {
            timeArray[i] = latestTs - (data.size() - 1 - (i + st)) * tsInterval;
            valueArray[i] = data.get(i + st).getValue();
        }
        timeArray[winSize] = curTs;
        valueArray[winSize] = value;
        int imputeTs = latestTs + tsInterval;
        return Imputation.impute(timeArray, valueArray, imputeTs, missingFillType);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BaseProcessor<?> that = (BaseProcessor<?>)o;
        return tsInterval == that.tsInterval && periodLength == that.periodLength && lastJump == that.lastJump
            && Objects.equals(data, that.data) && Objects.equals(lastAlarmMap, that.lastAlarmMap) && Objects.equals(
            statisticsAccumulator, that.statisticsAccumulator);
    }

    @Override
    public int hashCode() {
        return Objects.hash(data, lastAlarmMap, tsInterval, periodLength, lastJump, statisticsAccumulator);
    }
}
