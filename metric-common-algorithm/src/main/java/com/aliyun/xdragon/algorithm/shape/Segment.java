package com.aliyun.xdragon.algorithm.shape;

/**
 * <AUTHOR>
 * @date 2024/07/17
 */
public class Segment {
    /**
     * 起始索引（包含）
     */
    private final int from;
    /**
     * 结束索引（不包含）
     */
    private final int to;

    public Segment(int from, int to) {
        this.from = from;
        this.to = to;
    }

    public int getFrom() {
        return from;
    }

    public int getTo() {
        return to;
    }

    @Override
    public String toString() {
        return "Segment(" + from + "-" + to + ')';
    }
}
