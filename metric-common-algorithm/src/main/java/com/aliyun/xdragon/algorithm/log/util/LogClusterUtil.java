package com.aliyun.xdragon.algorithm.log.util;

import com.aliyun.xdragon.algorithm.log.model.utils.RegexWithLimitLogSplitter;
import com.aliyun.xdragon.common.model.log.LogPattern;
import com.aliyun.xdragon.common.model.log.LogPatternSimple;
import com.google.re2j.Matcher;
import com.google.re2j.Pattern;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/02/24
 */
public class LogClusterUtil {
    private static final Logger logger = LoggerFactory.getLogger(LogClusterUtil.class);

    private static final String GENERAL_REGEX = "***";
    private static final String NUMBER_REGEX = "NUMBER";
    private static final int TOKEN_CHECK_NUM = 3;

    public static boolean isPatternMatch(String raw, String pattern) {
        String[] tokens = pattern.split(" ");
        int fromIndex = 0;
        int tokenIndex = 0;
        int toCheckNum = Math.min(tokens.length, TOKEN_CHECK_NUM);
        while (toCheckNum-- > 0 && tokenIndex < tokens.length) {
            if (GENERAL_REGEX.equals(tokens[tokenIndex]) || tokens[tokenIndex].contains(NUMBER_REGEX)) {
                ++tokenIndex;
                ++toCheckNum;
                continue;
            }
            fromIndex = raw.indexOf(tokens[tokenIndex], fromIndex);
            if (fromIndex == -1) {
                return false;
            }
            fromIndex += tokens[tokenIndex].length();
            ++tokenIndex;
        }
        String patternRegex = getPatternRegex(pattern);
        String flatRaw = raw.replace("\n", " ").replaceAll(" {2,}", " ");

        Pattern p = Pattern.compile(patternRegex);
        return p.matcher(flatRaw).matches();
    }

    public static boolean isPatternMatch(String raw, LogPattern pattern) {
        return pattern.match(raw);
    }

    public static Pattern getCaptureRegex(String pattern) {
        return Pattern.compile("(.*?)" + pattern.replace("\\", "\\\\")
            .replace(NUMBER_REGEX, "")
            .replace(".", "\\.")
            .replace("|", "\\|")
            .replace("$", "\\$")
            .replace("&", "\\&")
            .replace("?", "\\?")
            .replace("^", "\\^")
            .replace("$", "\\$")
            .replace(" *** ", " ")
            .replace("*** ", "")
            .replace(" ***", "")
            .replace("*", "\\*")
            .replace(" ", "(.+?)") + "(.*)");
    }

    public static ArrayList<String> captureVariable(Pattern p, String raw) {
        Matcher matcher = p.matcher(raw);
        int cnt = matcher.groupCount();
        ArrayList<String> varList = new ArrayList<>(cnt);
        if (matcher.find()) {
            for (int i = 1; i < cnt + 1; ++i) {
                varList.add(matcher.group(i));
            }
            return varList;
        }
        return new ArrayList<>();
    }

    private static String getPatternRegex(String pattern) {
        return pattern
            .replace("\\", "\\\\")
            .replace(".", "\\.")
            .replace("|", "\\|")
            .replace("$", "\\$")
            .replace("&", "\\&")
            .replace("?", "\\?")
            .replace("^", "\\^")
            .replace("$", "\\$")
            .replace(" " + GENERAL_REGEX + " ", ".+")
            .replace(" " + GENERAL_REGEX, ".+")
            .replace(GENERAL_REGEX + " ", ".+")
            .replace("*", "\\*")
            .replace(NUMBER_REGEX, "(?:[\\-\\+]?\\d+\\.?\\d*)")
            .replace(" ", "(?:" + RegexWithLimitLogSplitter.DELIMITERS + "|/){1,5}")
            + ".*";
    }

    public static List<LogPattern> topoSort(List<LogPattern> patterns) {
        if (patterns == null || patterns.size() <= 1) {
            return patterns;
        }

        int[] degree = new int[patterns.size()];
        List<List<Integer>> edges = new ArrayList<>(degree.length);
        for (int i = 0; i < degree.length; ++i) {
            edges.add(new ArrayList<>(degree.length));
        }
        for (int i = 0; i < degree.length - 1; ++i) {
            for (int j = 0; j < degree.length; ++j) {
                if (i == j) {
                    continue;
                }
                if (patterns.get(i).match(patterns.get(j))) {
                    ++degree[i];
                    edges.get(j).add(i);
                }
            }
        }

        Queue<Integer> queue = new ArrayDeque<>();
        for (int i = 0; i < degree.length; ++i) {
            if (degree[i] == 0) {
                queue.add(i);
            }
        }

        List<LogPattern> sorted = new ArrayList<>(patterns.size());
        while (!queue.isEmpty()) {
            int cur = queue.poll();
            sorted.add(patterns.get(cur));
            for (Integer i : edges.get(cur)) {
                if (--degree[i] == 0) {
                    queue.add(i);
                }
            }
        }
        return sorted;
    }

    public static List<String> sortPattern(List<String> patternStr) {
        if (patternStr == null) {
            return null;
        }
        if (patternStr.size() <= 1) {
            return patternStr;
        }
        List<LogPattern> patterns = new ArrayList<>(patternStr.size());
        patternStr.forEach(str -> patterns.add(new LogPattern(str)));
        List<LogPattern> sortedPatterns = topoSort(patterns);
        List<String> sortStr = new ArrayList<>(patternStr.size());
        sortedPatterns.forEach(p -> sortStr.add(p.getPatternStr()));
        return sortStr;
    }

    public static List<String> sortPatternSimple(List<String> patternStr) {
        if (patternStr == null) {
            return null;
        }
        if (patternStr.size() <= 1) {
            return patternStr;
        }
        List<LogPatternSimple> patterns = new ArrayList<>(patternStr.size());
        patternStr.forEach(str -> patterns.add(new LogPatternSimple(str)));
        patterns.sort((ps1, ps2) -> ps2.getWordCnt() - ps1.getWordCnt());
        List<String> sortStr = new ArrayList<>(patternStr.size());
        patterns.forEach(p -> sortStr.add(p.getPatternStr()));
        return sortStr;
    }

    public static String assembleIdMd5(Long id, String md5) {
        return String.format("%d|%s", id, md5);
    }

    /**
     * 和assembleIdMd5在内部逻辑配套使用，省略参数检查逻辑
     */
    public static Pair<Long, String> disassembleIdMd5NotSafe(String idMd5) {
        String[] tokens = idMd5.split("\\|");
        return Pair.of(Long.valueOf(tokens[0]), tokens[1]);
    }

    public static Set<Integer> splitIndicesStr(String indicesStr) {
        if (indicesStr == null || indicesStr.isEmpty()) {
            return Collections.emptySet();
        }
        String[] indicesArray = indicesStr.split("[;,]");
        Set<Integer> indices = new LinkedHashSet<>(indicesArray.length);

        Integer index;
        for (String indexStr : indicesArray) {
            try {
                index = Integer.valueOf(indexStr);
            } catch (NumberFormatException e) {
                continue;
            }
            indices.add(index);
        }

        return indices;
    }

    public static Map<Long, Set<Integer>> splitIndicesMap(String indicesMapStr) {
        if (indicesMapStr == null || indicesMapStr.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<Long, Set<Integer>> indicesMap = new HashMap<>();
        String[] indicesMapArray = indicesMapStr.split("\\|");
        for (String indicesStr : indicesMapArray) {
            try {
                int index = indicesStr.indexOf(":");
                Long id = Long.valueOf(indicesStr.substring(0, index));
                if (indicesStr.length() > index + 1) {
                    String indices = indicesStr.substring(index + 1);
                    Set<Integer> indicesSet = splitIndicesStr(indices);
                    indicesMap.put(id, indicesSet);
                }
            } catch (NumberFormatException e) {
                logger.error("splitIndicesMap error, indicesStr: {}", indicesStr);
                continue;
            }
        }
        return indicesMap;
    }
}
