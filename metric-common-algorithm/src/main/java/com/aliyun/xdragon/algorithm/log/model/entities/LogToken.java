package com.aliyun.xdragon.algorithm.log.model.entities;

import com.aliyun.xdragon.common.util.BKDRHash;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/22
 */
@Data
public class LogToken {
    private String token;
    private String type;

    public LogToken(String word) {
        this(word, "raw");
    }

    public LogToken(String token, String type) {
        this.token = token;
        this.type = type;
    }

    public Integer getHash() {
        return BKDRHash.hash(token);
    }
}
