package com.aliyun.xdragon.algorithm.log.model.entities;

import com.aliyun.xdragon.algorithm.log.model.utils.RegexWithLimitLogSplitter;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/07/10
 */
@Data
public class LogInfo {
    private List<LogToken> tokens;
    private Map<String, Set<Long>> idInfos;
    private Map<String, Map<String, Integer>> extensions;
    private Integer count;
    private static final String DEFAULT_NC = "unknown";
    private static Function<String, Map<String, Map<String, Integer>>> extParser = (ext) -> {
        String[] exts = ext.split("\\|");
        Map<String, Map<String, Integer>> extMap = new HashMap<>(exts.length);
        for (int i = 0; i < exts.length; i++) {
            Map<String, Integer> mp = new HashMap<>();
            mp.put(exts[i], 1);
            extMap.put(String.format("ext%d", i), mp);
        }
        return extMap;
    };
    private static Function<String, Long> idGenerator = (raw) -> raw == null ? 0L : (long)raw.hashCode();
    private static Function<String, List<LogToken>> logSplitter = (raw) -> {
        RegexWithLimitLogSplitter splitter = RegexWithLimitLogSplitter.defaultSplitter();
        String[] words = splitter.split(raw);
        List<LogToken> tokens = new ArrayList<>(words.length);
        for (String word : words) {
            tokens.add(new LogToken(word));
        }
        return tokens;
    };

    public LogInfo(String raw) {
        this(raw, idGenerator.apply(raw), 1);
    }

    public LogInfo(String raw, Long id, Integer count) {
        this.tokens = logSplitter.apply(raw);
        this.extensions = null;
        this.idInfos = new HashMap<>();
        Set<Long> idSet = new HashSet<>();
        idSet.add(id);
        this.idInfos.put(DEFAULT_NC, idSet);
        this.count = count;
    }

    public LogInfo(String raw, Long id, String exts) {
        this(raw, id, exts, 1);
    }

    public LogInfo(String raw, Long id, String exts, Integer count) {
        this(raw, id, DEFAULT_NC, exts, count);
    }

    public LogInfo(String raw, Long id, String nc, String exts, Integer count) {
        this.tokens = logSplitter.apply(raw);
        this.extensions = exts == null ? null : extParser.apply(exts);
        if (this.extensions != null) {
            for (Map<String, Integer> mp : this.extensions.values()) {
                for (Map.Entry<String, Integer> entry : mp.entrySet()) {
                    int value = entry.getValue() * count;
                    entry.setValue(value);
                }
            }
        }
        this.idInfos = new HashMap<>();
        Set<Long> idSet = new HashSet<>();
        idSet.add(id);
        this.idInfos.put(nc, idSet);
        this.count = count;
    }

    public LogInfo(String raw, Long id, String nc, Map<String, Map<String, Integer>> exts, Integer count) {
        this.tokens = logSplitter.apply(raw);
        this.extensions = exts;
        this.idInfos = new HashMap<>();
        Set<Long> idSet = new HashSet<>();
        idSet.add(id);
        this.idInfos.put(nc, idSet);
        this.count = count;
    }

    public LogInfo(String raw, Long id, String nc, String exts, Function<String, String[]> splitter) {
        String[] tokens = splitter.apply(raw);
        this.tokens = new ArrayList<>(tokens.length);
        for (String token : tokens) {
            this.tokens.add(new LogToken(token));
        }
        this.extensions = exts == null ? null : extParser.apply(exts);
        Set<Long> rawIds = new HashSet<>();
        rawIds.add(id);
        this.idInfos = new HashMap<>();
        this.idInfos.put(nc, rawIds);
        this.count = 1;
    }

    public String getReadableTokens() {
        List<String> tokenStrList = this.tokens.stream().map(LogToken::getToken).collect(Collectors.toList());
        return String.join(" ", tokenStrList);
    }

    public void merge(LogInfo info) {
        if (info == null || !this.getReadableTokens().equals(info.getReadableTokens())) {
            return;
        }
        this.count += info.getCount();
        if (this.idInfos == null) {
            this.idInfos = info.idInfos;
        } else if (info.idInfos != null) {
            for (Map.Entry<String, Set<Long>> entry : info.idInfos.entrySet()) {
                if (!this.idInfos.containsKey(entry.getKey())) {
                    this.idInfos.put(entry.getKey(), entry.getValue());
                } else {
                    this.idInfos.get(entry.getKey()).addAll(entry.getValue());
                }
            }
        }
        if (info.extensions == null) {
            return;
        }
        if (this.extensions == null) {
            this.extensions = new HashMap<>();
            this.extensions.putAll(info.extensions);
        } else {
            for (Map.Entry<String, Map<String, Integer>> entry : info.extensions.entrySet()) {
                if (!this.extensions.containsKey(entry.getKey())) {
                    this.extensions.put(entry.getKey(), entry.getValue());
                } else {
                    for (Map.Entry<String, Integer> valueEntry : entry.getValue().entrySet()) {
                        String key = valueEntry.getKey();
                        Integer value = valueEntry.getValue();
                        Map<String, Integer> mp = this.extensions.get(entry.getKey());
                        if (mp.containsKey(key)) {
                            mp.put(key, mp.get(key) + value);
                        } else {
                            mp.put(key, value);
                        }
                    }
                }
            }
        }
    }
}
