package com.aliyun.xdragon.algorithm.log.model.entities;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.gson.Gson;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/05/05
 */
@Data
public class CandidateEntity implements Serializable {
    private final String[] tokens;
    private Integer support;
    private Integer[] minVars;
    private Integer[] maxVars;
    private List<Map<String, Integer>> extensionInfo;
    private Set<String> md5Set;

    /**
     * @description 构造函数
     * @param tokens 日志中的关键字列表
     * @param minVars 关键字之间被略掉的最小字数
     * @param maxVars 关键字之间被略掉的最大字数
     */
    public CandidateEntity(String[] tokens, Integer[] minVars, Integer[] maxVars) {
        this.tokens = tokens;
        this.minVars = minVars;
        this.maxVars = maxVars;
        support = 0;
        extensionInfo = new ArrayList<>(10);
        md5Set = new HashSet<>();
    }

    public CandidateEntity(List<String> tokens) {
        this.tokens = tokens.toArray(new String[tokens.size()]);
        support = 0;
        extensionInfo = new ArrayList<>(10);
        md5Set = new HashSet<>();
    }

    public void setMinVarByIndex(int i, int value) {
        minVars[i] = value;
    }

    public int getMinVarByIndex(int i){
        return minVars[i];
    }

    public void setMaxVarByIndex(int i, int value) {
        maxVars[i] = value;
    }

    public int getMaxVarByIndex(int i){
        return maxVars[i];
    }

    public void addSupportByDelta(Integer delta){
        support += delta;
    }

    public String getExtensionInfoStr() {
        return new Gson().toJson(extensionInfo);
    }

    public void updateMd5Set(Collection<String> md5s) {
        if (md5s != null) {
            md5Set.addAll(md5s);
        }
    }

    public void updateExtensionInfo(List<String> extensionList) {
        if (extensionList == null || extensionList.isEmpty()) {
            return;
        }

        for (int i = 0; i < extensionList.size(); ++i) {
            if (extensionInfo.size() < i + 1) {
                extensionInfo.add(new HashMap<>(1024));
            }
            Map<String, Integer> extension = extensionInfo.get(i);
            String ext = extensionList.get(i);
            extension.put(ext, (extension.containsKey(ext) ? extension.get(ext) : 0) + 1);
        }
    }

    public void mergeExtensionInfo(List<Map<String, Integer>> extensionMapList) {
        if (extensionMapList == null || extensionMapList.isEmpty()) {
            return;
        }

        for (int i = 0; i < extensionMapList.size(); ++i) {
            if (extensionInfo.size() < i + 1) {
                extensionInfo.add(extensionMapList.get(i));
            } else {
                for (Map.Entry<String, Integer> entry : extensionMapList.get(i).entrySet()) {
                    Map<String, Integer> extension = extensionInfo.get(i);
                    String ext = entry.getKey();
                    extension.put(ext, (extension.getOrDefault(ext, 0)) + entry.getValue());
                }
            }
        }
    }

    public String getPattern() {
        return String.join(" ", tokens);
    }

    /**
    * 生成token和通配符组成的模式，不合并通配符
    */
    public List<String> generatePattern() {
        List<String> patternTokens = new LinkedList<>();
        int tmpMaxVar = 0;
        for (int i = 0; i < tokens.length; i++){
            tmpMaxVar += maxVars[i];
            String curToken = tokens[i];
            if (curToken != null && curToken.length() != 0) {
                if (tmpMaxVar > 0) {
                    patternTokens.add("***");
                }
                patternTokens.add(curToken);
                tmpMaxVar = 0;
            }
        }
        tmpMaxVar += maxVars[tokens.length];
        if (tmpMaxVar > 0) {
            patternTokens.add("***");
        }
        return patternTokens;
    }
}
