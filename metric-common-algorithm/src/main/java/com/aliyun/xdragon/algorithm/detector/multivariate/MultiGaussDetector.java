package com.aliyun.xdragon.algorithm.detector.multivariate;

import java.util.Properties;

import com.aliyun.xdragon.algorithm.common.DetectorConstants;
import com.aliyun.xdragon.algorithm.common.LinearUtils;
import com.aliyun.xdragon.common.enumeration.algorithm.AnomalyType;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.math3.linear.MatrixUtils;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.linear.RealVector;
import org.apache.commons.math3.util.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2023/08/11
 */
public class MultiGaussDetector {

    private static final Logger logger = LoggerFactory.getLogger(MultiGaussDetector.class);

    public static Pair<AnomalyType, Double> detect(RealVector x, RealVector mu, RealMatrix sigma, Properties params) {
        //读取参数
        double threshold = MapUtils.getDoubleValue(params, DetectorConstants.THRESHOLD,
            DetectorConstants.DEFAULT_FAR_THRESHOLD);

        //这里不直接使用Apache Commons Math3中提供的类MultivariateNormalDistribution，主要原因有两点：
        //一是MultivariateNormalDistribution类内部在计算逆矩阵时不太稳定，容易报矩阵奇异的错误，因此使用伪逆矩阵来代替逆矩阵；
        //二是如果直接使用原始概率密度，密度值会受到数据范围的巨大影响。因此，数据需要标准化，使其协方差为单位阵。
        //标准化和概率密度的计算可以结合起来，以避免不必要的运算。
        try {
            //计算标准化后的概率密度
            int d = x.getDimension();
            RealMatrix nx = MatrixUtils.createRowRealMatrix(x.subtract(mu).toArray());
            double e = nx.multiply(LinearUtils.pseudoInverse(sigma)).multiply(nx.transpose()).getEntry(0, 0);
            double pdf = Math.pow(2 * Math.PI, -0.5 * d) * Math.exp(-0.5 * e);
            //输出结果
            if (pdf < threshold) {
                return Pair.create(AnomalyType.OUTLIER, e / Math.sqrt(d));
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.error("Exception happens with covariance matrix " + sigma + e);
        }
        return null;
    }
}
