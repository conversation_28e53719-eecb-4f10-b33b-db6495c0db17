package com.aliyun.xdragon.algorithm.log.model.entities;

import com.google.gson.Gson;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/05/05
 */
@Data
public class CandidateEntityV2 implements Serializable {
    private final List<LogToken> tokens;
    private Integer support;
    private Integer[] minVars;
    private Integer[] maxVars;
    private Map<String, Map<String, Integer>> extensionInfo;
    private Map<String, Set<Long>> idInfos;

    /**
     * @description 构造函数
     * @param tokens 日志中的关键字列表
     * @param minVars 关键字之间被略掉的最小字数
     * @param maxVars 关键字之间被略掉的最大字数
     */
    public CandidateEntityV2(List<LogToken> tokens, Integer[] minVars, Integer[] maxVars) {
        this.tokens = tokens;
        this.minVars = minVars;
        this.maxVars = maxVars;
        support = 0;
        extensionInfo = new HashMap<>(10);
        idInfos = new HashMap<>();
    }

    public CandidateEntityV2(List<LogToken> tokens) {
        this.tokens = tokens;
        support = 0;
        extensionInfo = new HashMap<>(10);
        idInfos = new HashMap<>();
    }

    public void setMinVarByIndex(int i, int value) {
        minVars[i] = value;
    }

    public int getMinVarByIndex(int i){
        return minVars[i];
    }

    public void setMaxVarByIndex(int i, int value) {
        maxVars[i] = value;
    }

    public int getMaxVarByIndex(int i){
        return maxVars[i];
    }

    public void addSupportByDelta(Integer delta){
        support += delta;
    }

    public String getExtensionInfoStr() {
        return new Gson().toJson(extensionInfo);
    }

    public void updateIdSet(Map<String, Set<Long>> idMap) {
        if (idMap != null) {
            for (Map.Entry<String, Set<Long>> entry : idMap.entrySet()) {
                if (!idInfos.containsKey(entry.getKey())) {
                    idInfos.put(entry.getKey(), entry.getValue());
                } else {
                    idInfos.get(entry.getKey()).addAll(entry.getValue());
                }
            }
        }
    }

    public void updateExtensionInfo(Map<String, String> extensionMap) {
        if (extensionMap == null || extensionMap.isEmpty()) {
            return;
        }

        for (Map.Entry<String, String> entry : extensionMap.entrySet()) {
            String key = entry.getKey();
            if (!extensionInfo.containsKey(key)) {
                extensionInfo.put(key, new HashMap<>(1024));
            }
            Map<String, Integer> extension = extensionInfo.get(key);
            extension.put(entry.getValue(), extension.getOrDefault(key, 0) + 1);
        }
    }

    public void mergeExtensionInfo(Map<String, Map<String, Integer>> extensionMap) {
        if (extensionMap == null || extensionMap.isEmpty()) {
            return;
        }

        for (Map.Entry<String, Map<String, Integer>> entry : extensionMap.entrySet()) {
            String key = entry.getKey();
            if (!extensionInfo.containsKey(key)) {
                extensionInfo.put(key, new HashMap<>(1024));
            }
            Map<String, Integer> extension = extensionInfo.get(key);
            for (Map.Entry<String, Integer> valueEntry : entry.getValue().entrySet()) {
                extension.put(valueEntry.getKey(), extension.getOrDefault(valueEntry.getKey(), 0) + valueEntry.getValue());
            }
        }
    }

    public String getPattern() {
        return tokens.stream().map(LogToken::getToken).collect(Collectors.joining(" "));
    }

    /**
    * 生成token和通配符组成的模式，不合并通配符
    */
    public List<String> generatePattern() {
        List<String> patternTokens = new LinkedList<>();
        int tmpMaxVar = 0;
        for (int i = 0; i < tokens.size(); i++){
            tmpMaxVar += maxVars[i];
            String curToken = tokens.get(i).getToken();
            if (curToken != null && !curToken.isEmpty()) {
                if (tmpMaxVar > 0) {
                    patternTokens.add("***");
                }
                patternTokens.add(curToken);
                tmpMaxVar = 0;
            }
        }
        tmpMaxVar += maxVars[tokens.size()];
        if (tmpMaxVar > 0) {
            patternTokens.add("***");
        }
        return patternTokens;
    }

    public List<LogToken> getClusterTokens() {
        List<LogToken> clusterTokens = new LinkedList<>();
        int tmpMaxVar = 0;
        for (int i = 0; i < tokens.size(); i++){
            tmpMaxVar += maxVars[i];
            LogToken curToken = tokens.get(i);
            if (curToken != null) {
                if (tmpMaxVar > 0) {
                    clusterTokens.add(new LogToken("***", "wildcard"));
                }
                clusterTokens.add(curToken);
                tmpMaxVar = 0;
            }
        }
        tmpMaxVar += maxVars[tokens.size()];
        if (tmpMaxVar > 0) {
            clusterTokens.add(new LogToken("***", "wildcard"));
        }
        return clusterTokens;
    }
}
