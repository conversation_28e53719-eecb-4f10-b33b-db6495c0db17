package com.aliyun.xdragon.algorithm.detector.mutation;

import com.aliyun.xdragon.algorithm.common.DetectorConstants;
import com.aliyun.xdragon.common.enumeration.algorithm.AnomalyType;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.math3.stat.StatUtils;
import org.apache.commons.math3.distribution.NormalDistribution;
import org.apache.commons.math3.util.FastMath;

import java.util.Arrays;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2022/04/06
 * @description 柯西分布检测突变异常，主要过程：1、判断尖峰低谷过滤 2、数据一阶差分 3、柯西分布检验
 */

public class CauchyMutationDetector extends BaseMutationDetector {
    public static AnomalyType detectAnomaly(double[] numList, double curNum) {
        return detectAnomaly(numList, curNum, DetectorConstants.DEFAULT_CAUCHY_THRESHOLD, false);
    }

    /**
     * 基于柯西分布进行异常检测
     *
     * @param trendList 趋势序列/平滑数据
     * @param resList   残差序列
     * @param curRes    需要检测的数据点的残差
     * @param params    参数
     * @return 检测到的异常，null表示未检测到异常
     */
    public static AnomalyType detectAnomaly(double[] trendList, double[] resList, double curRes, double curValue,
        Properties params) {
        if (!paramsCheck(params, trendList, curValue)) {
            return null;
        }
        //读取参数
        double threshold = MapUtils.getDoubleValue(params, DetectorConstants.THRESHOLD,
            DetectorConstants.DEFAULT_CAUCHY_THRESHOLD);

        return detectAnomaly(resList, curRes, threshold, false);
    }

    public static AnomalyType detectAnomaly(double[] numList, double curNum, double threshold, boolean half) {
        if (!isLengthValid(numList.length)) {
            return null;
        }
        double median = StatUtils.percentile(numList, DetectorConstants.PERCENTILE_50TH);
        double mad = StatUtils.percentile(Arrays.stream(numList).map(x -> FastMath.abs(x - median)).toArray(),
            DetectorConstants.PERCENTILE_50TH);
        double curCdf;
        if (FastMath.abs(mad - 0.0) < DetectorConstants.DOUBLE_DIFF) {
            // 如果mad为0则退化为使用正太分布的来确认异常
            double mean = StatUtils.mean(numList);
            double std = FastMath.sqrt(StatUtils.variance(numList, mean));
            // 如果标准差为0说明历史数据均相等
            if (FastMath.abs(std - 0.0) < DetectorConstants.DOUBLE_DIFF) {
                curCdf = curNum > mean ? 0.99 : (curNum < mean ? 0.01 : 0.5);
            } else {
                NormalDistribution normalDistribution = new NormalDistribution(mean, std);
                curCdf = normalDistribution.cumulativeProbability(curNum);
            }
        } else {
            // 如果是半分布即 x >= 0, 这里需要转换一下
            if (half && curNum < median) {
                // 这里需要把[0, +∞)的x映射成(−∞,+∞)分布的x'
                // 因为x >= 0，当median为0时，走不到这一步
                curNum = 2 * median * Math.tan(Math.PI * (curNum - median) / 2 / median) / FastMath.PI + median;
            }
            // 柯西分布的CDF公式
            curCdf = FastMath.atan((curNum - median) / mad) / FastMath.PI + 0.5;
        }

        // 这里把cdf结果装换为正负两极，用于公共函数判断
        double middle = 0.5;
        if (curCdf < middle) {
            curCdf = curCdf - 1;
        }
        return anomalyJudge(threshold, curCdf);
    }
}
