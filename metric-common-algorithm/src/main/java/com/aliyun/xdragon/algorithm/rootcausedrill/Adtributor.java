package com.aliyun.xdragon.algorithm.rootcausedrill;

import java.util.Arrays;

import com.aliyun.xdragon.algorithm.common.DetectorConstants;
import com.aliyun.xdragon.algorithm.common.LinearUtils;
import org.apache.commons.math3.stat.StatUtils;

/**
 * 基于《Adtributor: Revenue Debugging in Advertising Systems》的指标下钻算法
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
public class Adtributor {

    /**
     * 在指定维度下，计算基本指标的解释力。
     * 输入的两个数组长度完全相同，相同的索引对应同一个维度元素。
     *
     * @param f 当维度取值为各个元素时，基本指标的预测值
     * @param a 当维度取值为各个元素时，基本指标的真实值
     * @return 各维度元素对基本指标变化的解释力。数组长度与输入相等。
     */
    public static double[] fundamentalExplanatory(double[] f, double[] a) {
        //计算各维度元素的影响力
        int n = f.length;
        double[] ep = new double[n];
        for (int i = 0; i < n; i++) {
            ep[i] = a[i] - f[i];
        }
        //归一化
        if (StatUtils.sum(LinearUtils.arrayAbs(ep)) < DetectorConstants.DOUBLE_DIFF) {
            //特殊处理，各元素均没有变化，所有元素均分解释力
            Arrays.fill(ep, 1.0 / n);
        } else if (Math.abs(StatUtils.sum(ep)) < DetectorConstants.DOUBLE_DIFF) {
            //特殊处理，此时指标没有变化，按影响力的绝对值做归一化
            ep = LinearUtils.arrayAbs(ep);
        }
        return LinearUtils.normalizeSum(ep);
    }

    /**
     * 在指定维度下，计算形如m1/m2的派生指标的解释力。
     * 输入的四个数组长度完全相同，相同的索引对应同一个维度元素。
     *
     * @param f1 当维度取值为各个元素时，m1指标的预测值
     * @param a1 当维度取值为各个元素时，m1指标的真实值
     * @param f2 当维度取值为各个元素时，m2指标的预测值
     * @param a2 当维度取值为各个元素时，m2指标的真实值
     * @return 各维度元素对派生指标变化的解释力。数组长度与输入相等。
     */
    public static double[] derivedExplanatory(double[] f1, double[] a1, double[] f2, double[] a2) {
        //计算总体的m1和m2
        double f1Total = StatUtils.sum(f1);
        double f2Total = StatUtils.sum(f2);
        double a1Total = StatUtils.sum(a1);
        double a2Total = StatUtils.sum(a2);
        //特殊处理
        int n = f1.length;
        double[] ep = new double[n];
        if (Math.abs(a2Total) < DetectorConstants.DOUBLE_DIFF) {
            //特殊处理，此时实际的派生指标无意义，解释力等价于f2（权重）
            System.arraycopy(f2, 0, ep, 0, n);
            return LinearUtils.normalizeSum(ep);
        }
        if (Math.abs(f2Total) < DetectorConstants.DOUBLE_DIFF) {
            //特殊处理，此时预测的派生指标无意义，解释力等价于a2（权重）
            System.arraycopy(a2, 0, ep, 0, n);
            return LinearUtils.normalizeSum(ep);
        }
        //计算各维度元素的解释力
        for (int i = 0; i < n; i++) {
            if (Math.abs(f2Total + a2[i] - f2[i]) < DetectorConstants.DOUBLE_DIFF) {
                //特殊处理，此时应用该元素变化后的派生指标为NAN（0/0），该元素解释力为0，不为任何变化负责
                ep[i] = 0;
                continue;
            }
            ep[i] = (f1Total + a1[i] - f1[i]) / (f2Total + a2[i] - f2[i]) - f1Total / f2Total;
        }
        //归一化
        double overallEp = a1Total / a2Total - f1Total / f2Total;
        if (Math.abs(overallEp) < DetectorConstants.DOUBLE_DIFF) {
            //特殊处理，此时总体的派生指标没有变化，按影响力的绝对值做归一化
            ep = LinearUtils.normalizeSum(LinearUtils.arrayAbs(ep));
        } else {
            //统计同向和反向的解释力之和
            double support = 0, oppose = 0;
            for (int i = 0; i < n; i++) {
                if (ep[i] * overallEp > 0) {
                    support += ep[i];
                } else {
                    oppose += ep[i];
                }
            }
            //计算同向和反向解释力的归一化系数
            double supportNorm, opposeNorm;
            if (Math.abs(oppose) < DetectorConstants.DOUBLE_DIFF) {
                supportNorm = overallEp;
                opposeNorm = 1;
            } else {
                supportNorm = Math.abs(support - overallEp) < DetectorConstants.DOUBLE_DIFF ?
                    overallEp * 2 : overallEp;
                opposeNorm = oppose / (1 - support / supportNorm);
            }
            //根据系数执行归一化
            for (int i = 0; i < n; i++) {
                if (ep[i] * overallEp > 0) {
                    ep[i] = ep[i] / supportNorm;
                } else {
                    ep[i] = ep[i] / opposeNorm;
                }
            }
        }
        return ep;
    }

}
