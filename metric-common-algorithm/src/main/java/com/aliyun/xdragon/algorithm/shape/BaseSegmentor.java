package com.aliyun.xdragon.algorithm.shape;

import java.util.List;
import java.util.Properties;

import org.apache.commons.collections.MapUtils;

/**
 * <AUTHOR>
 * @date 2024/03/14
 */
public abstract class BaseSegmentor {

    /**
     * 根据参数构建分割器
     *
     * @param params 参数
     * @return 分割器
     */
    public static BaseSegmentor build(Properties params) {
        String segmentorName = MapUtils.getString(params, "name", "kadane");
        switch (segmentorName) {
            case "linear":
                return new LinearSegmentor(params);
            case "simple":
                return new SimpleSegmentor(params);
            case "kadane":
                return new KadaneSegmentor(params);
            default:
                throw new IllegalArgumentException("Unsupported segmentor name: " + segmentorName);
        }
    }

    /**
     * 将给定的序列分割为若干个近线性的子序列
     *
     * @param x 序列
     * @return 分割结果
     */
    public abstract List<Segment> calculate(double[] x);

}
