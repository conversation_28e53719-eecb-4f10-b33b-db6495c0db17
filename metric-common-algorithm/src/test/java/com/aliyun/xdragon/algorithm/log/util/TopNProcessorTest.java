package com.aliyun.xdragon.algorithm.log.util;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Test;

public class TopNProcessorTest {
    @Test
    public void testTopNProcessorBasic() {
        TopNProcessor processor = new TopNProcessor(5, true);
        Assert.assertEquals(5, processor.getThreshold());
        Assert.assertTrue(processor.isNeedSort());

        processor.setThreshold(10);
        Assert.assertEquals(10, processor.getThreshold());

        processor.setNeedSort(false);
        Assert.assertFalse(processor.isNeedSort());
    }

    @Test
    public void testTopNProcessorGetDistribution() {
        int threshold = 3;
        StringDistributionProcessor processor = new TopNProcessor(threshold, false);
        Assert.assertEquals("TopNprocessor, limit to 3 k-v pairs, must sorted: false.", processor.getDiscription());

        Map<String, Integer> counter = new HashMap<>(5);

        // Test case when input map is empty
        List<Pair<String, Integer>> showList = processor.getDistribution(counter);
        Assert.assertNotNull(showList);
        Assert.assertTrue(showList.isEmpty());

        // Test case when input map size <= threshold
        counter.put("test1", 10);
        counter.put("test2", 7);
        counter.put("test3", 5);
        showList = processor.getDistribution(counter);
        Assert.assertNotNull(showList);
        Assert.assertEquals(counter.size(), showList.size());
        int index = 0;
        for (Map.Entry<String, Integer> entry : counter.entrySet()) {
            Assert.assertEquals(entry.getKey(), showList.get(index).getLeft());
            Assert.assertEquals(entry.getValue(), showList.get(index).getRight());
            ++index;
        }

        // Test case when input map size > threshold
        counter.put("test4", 7);
        counter.put("test5", 3);
        showList = processor.getDistribution(counter);
        Assert.assertNotNull(showList);
        Assert.assertEquals(threshold, showList.size());
        Assert.assertEquals("test2", showList.get(0).getLeft());
        Assert.assertEquals(Integer.valueOf(7), showList.get(0).getRight());
        Assert.assertEquals("test4", showList.get(1).getLeft());
        Assert.assertEquals(Integer.valueOf(7), showList.get(1).getRight());
        Assert.assertEquals("test1", showList.get(2).getLeft());
        Assert.assertEquals(Integer.valueOf(10), showList.get(2).getRight());
    }
}
