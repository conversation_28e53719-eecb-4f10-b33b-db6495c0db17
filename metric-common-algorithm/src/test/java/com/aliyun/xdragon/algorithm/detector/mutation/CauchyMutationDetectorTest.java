package com.aliyun.xdragon.algorithm.detector.mutation;

import com.aliyun.xdragon.algorithm.common.DetectorConstants;
import com.aliyun.xdragon.common.enumeration.algorithm.AnomalyType;
import junit.framework.TestCase;
import org.apache.commons.math3.distribution.NormalDistribution;
import org.apache.commons.math3.stat.StatUtils;
import org.apache.commons.math3.util.FastMath;
import org.junit.Test;

import java.util.Arrays;
import java.util.Properties;

public class CauchyMutationDetectorTest {
    @Test
    public void detectAnomalyTest(){
//        double[] testList = new double[]{13,12,15,11,12,12,15,11,12,12,15,11,12,12,15,11,12,15,11,12,12,15,11,12,44,34,4};
        double[] testList = new double[]{79,77,76,77,79,80,76,79,75,72,75,78,72,79,70,79,79,74,78,70,71,75,77,71,76,73,70,77,74,72,75,80,72,10,12};
        TestCase.assertEquals(CauchyMutationDetector.detectAnomaly(testList, 9), AnomalyType.DIP);
        Properties params = new Properties(){
            {
                put(DetectorConstants.THRESHOLD, 0.95);
                put(DetectorConstants.VOLATILITY, 0.2);
            }
        };
        double[] smoothList = new double[testList.length];
        for (int i=0;i<testList.length;i++){
            int s = Math.max(0, i-DetectorConstants.DEFAULT_SMOOTH_WIN_SIZE);
            double[] tmpList = Arrays.copyOfRange(testList, s, i+1);
            smoothList[i] = StatUtils.percentile(tmpList, 50);
        }
        double[] diffList = new double[testList.length-1];
        for (int i=0; i<testList.length-1; i++) {
            diffList[i] = testList[i] - smoothList[i];
        }
        double curDiff = testList[testList.length-1] - smoothList[testList.length-1];
        TestCase.assertEquals(CauchyMutationDetector.detectAnomaly(testList, diffList, curDiff, testList[testList.length-1], params), AnomalyType.DIP);
        params.put(DetectorConstants.VOLATILITY_VALUE, 100);
        params.put(DetectorConstants.CHECK_DIP_ZERO, "true");
        TestCase.assertNull(CauchyMutationDetector.detectAnomaly(testList, diffList, curDiff, testList[testList.length-1], params));
        TestCase.assertEquals(CauchyMutationDetector.detectAnomaly(testList, diffList, curDiff, 0, params), AnomalyType.DIP);

        double[] testList2 = new double[]{1,1,1,10,1,1,1,1,12,1,10,1,1,11,1};
        TestCase.assertNull(CauchyMutationDetector.detectAnomaly(testList2, 2));
        double[] testList3 = new double[]{1,1,1,1,1,1,1,1,1,1,1,1,1,1,1};
        TestCase.assertEquals(AnomalyType.DIP, CauchyMutationDetector.detectAnomaly(testList3, 0));
    }
}
