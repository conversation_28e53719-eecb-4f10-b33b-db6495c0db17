package com.aliyun.xdragon.algorithm.log.model.utils.reader;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Test;

public class SlsCsvReaderTest {
    @Test
    public void testSlsCsvReader() throws IOException {
        String filename = getClass().getClassLoader().getResource("dump.csv").getFile();
        SlsCsvReader reader = new SlsCsvReader(filename);
        List<Map<String, String>> records = reader.read();
        Assert.assertNotNull(records);
        Assert.assertEquals(43L, records.size());
    }
}
