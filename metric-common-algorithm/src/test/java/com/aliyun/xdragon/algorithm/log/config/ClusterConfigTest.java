package com.aliyun.xdragon.algorithm.log.config;

import com.aliyun.xdragon.algorithm.log.config.ClusterConfig;
import com.aliyun.xdragon.algorithm.log.model.utils.MaskingInstruction;
import org.junit.Assert;
import org.junit.Test;

public class ClusterConfigTest {
    @Test
    public void testClusterConfig() {
        MaskingInstruction[] maskingInstructions  = new MaskingInstruction[]{
            new MaskingInstruction("((\\d\\d){1,2})[/-](?:0?[1-9]|1[0-2])[/-](?:(?:0[1-9])|(?:[12][0-9])|(?:3[01])|[1-9])[T ](?:2[0123]|[01]?[0-9]):?(?:[0-5][0-9])(?::?(?:(?:[0-5]?[0-9]|60)(?:[:.,][0-9]+)?))?(?:Z|[+-](?:2[0123]|[01]?[0-9])(?::?(?:[0-5][0-9])))?", "REGEX")};
        ClusterConfig clusterConfig = new ClusterConfig(
            3, 3, 5, 30, 0.04, "", "",
            maskingInstructions, new String[]{"[", "]"});

        Assert.assertNotNull(clusterConfig);
        Assert.assertEquals(3, clusterConfig.getSupport());
        Assert.assertEquals(3, clusterConfig.getRsupport());
        Assert.assertEquals(5, clusterConfig.getCutLines());
        Assert.assertEquals(30, clusterConfig.getCutLength());
        Assert.assertEquals(0.04, clusterConfig.getWeightThreadhold(), 1e-8);
        Assert.assertEquals("", clusterConfig.getMaskPrefix());
        Assert.assertEquals("", clusterConfig.getMaskSuffix());
        Assert.assertArrayEquals(maskingInstructions, clusterConfig.getMaskingInstructions());
        Assert.assertArrayEquals(new String[]{"[","]"}, clusterConfig.getDelimiters());
    }
}
