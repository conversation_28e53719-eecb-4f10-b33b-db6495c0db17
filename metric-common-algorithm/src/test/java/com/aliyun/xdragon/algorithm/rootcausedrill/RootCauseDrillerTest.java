package com.aliyun.xdragon.algorithm.rootcausedrill;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.math3.util.Pair;
import org.junit.Assert;
import org.junit.Test;

public class RootCauseDrillerTest {
    @Test
    public void test1() {
        double[][] data1 = {
                {10, 10}, {10, 10}, {10, 11}, {10, 12}, {10, 13}, {12, 13},
                {10, 12}, {10, 13}, {10, 13}, {10, 12}, {10, 13}, {10, 14},
                {10, 14}, {10, 15}, {10, 15}, {10, 15}, {10, 16}, {12, 18},
                {10, 11}, {10, 11}, {10, 12}, {10, 12}, {10, 13}, {12, 13},
                {10, 14}, {10, 15}, {10, 15}, {10, 13}, {10, 13}, {12, 13},
                {12, 15}, {12, 17}, {12, 20}, {12, 20}, {10, 23}, {11, 24},
                {12, 13}, {15, 15}, {15, 16}, {15, 20}, {15, 18}, {11, 19},
                {18, 40}, {12, 31}, {16, 30}, {11, 23}, {19, 24}, {10, 25},
                {10, 35}, {10, 36}, {10, 37}
        };
        List<List<Double>> dataList1 = new ArrayList<>();
        for (double[] subArray : data1) {
            List<Double> subList = new ArrayList<>();
            for (double i : subArray) {
                subList.add(i);
            }
            dataList1.add(subList);
        }

        String[][] dims1 = {
                {"A4", "B1", "C4"}, {"A4", "B2", "C4"}, {"A4", "B3", "C4"}, {"A4", "B1", "C4"}, {"A4", "B2", "C4"}, {"A4", "B3", "C4"},
                {"A4", "B1", "C1"}, {"A4", "B2", "C1"}, {"A4", "B3", "C1"}, {"A4", "B1", "C2"}, {"A4", "B2", "C2"}, {"A4", "B3", "C2"},
                {"A1", "B4", "C1"}, {"A1", "B4", "C1"}, {"A1", "B4", "C1"}, {"A1", "B4", "C2"}, {"A1", "B4", "C2"}, {"A1", "B4", "C2"},
                {"A1", "B1", "C4"}, {"A1", "B2", "C4"}, {"A1", "B3", "C4"}, {"A1", "B1", "C4"}, {"A1", "B2", "C4"}, {"A1", "B3", "C4"},
                {"A1", "B1", "C1"}, {"A1", "B2", "C1"}, {"A1", "B3", "C1"}, {"A1", "B1", "C2"}, {"A1", "B2", "C2"}, {"A1", "B3", "C2"},
                {"A1", "B1", "C3"}, {"A1", "B2", "C3"}, {"A1", "B3", "C3"}, {"A2", "B1", "C1"}, {"A2", "B2", "C1"}, {"A2", "B3", "C1"},
                {"A2", "B1", "C2"}, {"A2", "B2", "C2"}, {"A2", "B3", "C2"}, {"A2", "B1", "C3"}, {"A2", "B2", "C3"}, {"A2", "B3", "C3"},
                {"A3", "B1", "C1"}, {"A3", "B2", "C1"}, {"A3", "B3", "C1"}, {"A3", "B1", "C2"}, {"A3", "B2", "C2"}, {"A3", "B3", "C2"},
                {"A3", "B1", "C3"}, {"A3", "B2", "C3"}, {"A3", "B3", "C3"},
        };

        List<List<String>> dimList1 = new ArrayList<>();
        for (String[] subArray : dims1) {
            List<String> subList = new ArrayList<>(Arrays.asList(subArray));
            dimList1.add(subList);
        }

        RootCauseDriller driller = new RootCauseDriller(0.7);
        List<Pair<List<List<String>>, Double>> res = driller.getRootCause(dimList1, dataList1, "Up");
        System.out.println(res);
    }

    @Test
    public void test2() {
        double[][] data2 = { // 预测值，真实值
                {10, 5},
                {20, 10},
                {31, 30},
                {9.8, 10},
                {2, 2},
                {210, 200},
                {22, 20},
                {203, 200},
                {43, 41}
        };
        List<List<Double>> dataList2 = new ArrayList<>();
        for (double[] subArray : data2) {
            List<Double> subList = new ArrayList<>();
            for (double i : subArray) {
                subList.add(i);
            }
            dataList2.add(subList);
        }

        String[][] dims2 = {
                {"Beijing", "China Mobile"},
                {"Beijing", "China Unicom"},
                {"Shanghai", "China Unicom"},
                {"Guangdong", "China Mobile"},
                {"Zhejiang", "China Unicom"},
                {"Guangdong", "China Unicom"},
                {"Shanxi", "China Unicom"},
                {"Jiangsu", "China Unicom"},
                {"Tianjin", "China Mobile"}
        };

        List<List<String>> dimList2 = new ArrayList<>();
        for (String[] subArray : dims2) {
            List<String> subList = new ArrayList<>(Arrays.asList(subArray));
            dimList2.add(subList);
        }

        // [[[Beijing, null]]]
        RootCauseDriller driller = new RootCauseDriller(0.7);
        List<Pair<List<List<String>>, Double>> res = driller.getRootCause(dimList2, dataList2, "Down");
        System.out.println(res);
        Assert.assertArrayEquals(new String[] {"Beijing", null}, res.get(0).getFirst().get(0).toArray());
        Assert.assertArrayEquals(new String[] {"Guangdong", null}, res.get(1).getFirst().get(0).toArray());
    }

    @Test
    public void test3() {
        double[][] data3 = {
                {20, 14},
                {15, 9},
                {10, 10},
                {10, 7},
                {25, 15},
                {20, 20}
        };
        List<List<Double>> dataList3 = new ArrayList<>();
        for (double[] subArray : data3) {
            List<Double> subList = new ArrayList<>();
            for (double i : subArray) {
                subList.add(i);
            }
            dataList3.add(subList);
        }
        String[][] dims3 = {
                {"Beijing", "Mobile"},
                {"Shanghai", "Mobile"},
                {"Guangdong", "Mobile"},
                {"Beijing", "Unicom"},
                {"Shanghai", "Unicom"},
                {"Guangdong", "Unicom"}
        };
        List<List<String>> dimList3 = new ArrayList<>();
        for (String[] subArray : dims3) {
            List<String> subList = new ArrayList<>(Arrays.asList(subArray));
            dimList3.add(subList);
        }

        // [[[Beijing, null]], [[Shanghai, null]]]
        RootCauseDriller driller = new RootCauseDriller(0.7);
        List<Pair<List<List<String>>, Double>> res = driller.getRootCause(dimList3, dataList3, "Down");
        System.out.println(res);
        Assert.assertArrayEquals(new String[] {"Shanghai", null}, res.get(0).getFirst().get(0).toArray());
    }

    @Test
    public void test4() {
        double[][] data4 = {
                {20, 14},
                {15, 15},
                {10, 10},
                {10, 7},
                {25, 25},
                {20, 20}
        };
        List<List<Double>> dataList4 = new ArrayList<>();
        for (double[] subArray : data4) {
            List<Double> subList = new ArrayList<>();
            for (double i : subArray) {
                subList.add(i);
            }
            dataList4.add(subList);
        }
        String[][] dims4 = {
                {"Beijing", "Mobile"},
                {"Shanghai", "Mobile"},
                {"Guangdong", "Mobile"},
                {"Beijing", "Unicom"},
                {"Shanghai", "Unicom"},
                {"Guangdong", "Unicom"}
        };
        List<List<String>> dimList4 = new ArrayList<>();
        for (String[] subArray : dims4) {
            List<String> subList = new ArrayList<>(Arrays.asList(subArray));
            dimList4.add(subList);
        }

        // [[[Beijing, null]]]
        RootCauseDriller driller = new RootCauseDriller(0.7);
        List<Pair<List<List<String>>, Double>> res = driller.getRootCause(dimList4, dataList4, "Down");
        System.out.println(res);
        Assert.assertArrayEquals(new String[] {"Beijing", null}, res.get(0).getFirst().get(0).toArray());
    }

    @Test
    public void test5() {
        double[][] data5 = {
                {0, 0},
                {0, 0},
                {20, 14},
                {15, 15},
                {10, 10},
                {10, 7},
                {25, 25},
                {20, 20},
        };
        List<List<Double>> dataList5 = new ArrayList<>();
        for (double[] subArray : data5) {
            List<Double> subList = new ArrayList<>();
            for (double i : subArray) {
                subList.add(i);
            }
            dataList5.add(subList);
        }
        String[][] dims4 = {
                {"Beijing", "Test"},
                {"Shanghai", "Test"},
                {"Beijing", "Mobile"},
                {"Shanghai", "Mobile"},
                {"Guangdong", "Mobile"},
                {"Beijing", "Unicom"},
                {"Shanghai", "Unicom"},
                {"Guangdong", "Unicom"}
        };
        List<List<String>> dimList5 = new ArrayList<>();
        for (String[] subArray : dims4) {
            List<String> subList = new ArrayList<>(Arrays.asList(subArray));
            dimList5.add(subList);
        }

        // [[[Beijing, null]]]
        RootCauseDriller driller = new RootCauseDriller(0.7);
        List<Pair<List<List<String>>, Double>> res = driller.getRootCause(dimList5, dataList5, "Down");
        System.out.println(res);
        Assert.assertArrayEquals(new String[] {"Beijing", null}, res.get(0).getFirst().get(0).toArray());
    }

    @Test
    public void test6() {
        double[][] data6 = {
                {20, 14},
                {15, 10},
                {10, 5},
        };
        List<List<Double>> dataList6 = new ArrayList<>();
        for (double[] subArray : data6) {
            List<Double> subList = new ArrayList<>();
            for (double i : subArray) {
                subList.add(i);
            }
            dataList6.add(subList);
        }
        String[][] dims6 = {
                {"A", "X"},
                {"A", "Y"},
                {"A", "Z"},
        };
        List<List<String>> dimList6 = new ArrayList<>();
        for (String[] subArray : dims6) {
            List<String> subList = new ArrayList<>(Arrays.asList(subArray));
            dimList6.add(subList);
        }

        RootCauseDriller driller = new RootCauseDriller(0.7);
        List<Pair<List<List<String>>, Double>> res = driller.getRootCause(dimList6, dataList6, "Down");
        System.out.println(res);
        Assert.assertArrayEquals(new String[] {"A", null}, res.get(0).getFirst().get(0).toArray());
    }

    @Test
    public void test7() {
        double[][] data6 = {
                {20, 14},
                {15, 10},
                {10, 5},
                {15, 15},
                {10, 10},
        };
        List<List<Double>> dataList6 = new ArrayList<>();
        for (double[] subArray : data6) {
            List<Double> subList = new ArrayList<>();
            for (double i : subArray) {
                subList.add(i);
            }
            dataList6.add(subList);
        }
        String[][] dims6 = {
                {"A", "X"},
                {"A", "Y"},
                {"A", "Z"},
                {"B", "W"},
                {"B", "U"},
        };
        List<List<String>> dimList6 = new ArrayList<>();
        for (String[] subArray : dims6) {
            List<String> subList = new ArrayList<>(Arrays.asList(subArray));
            dimList6.add(subList);
        }

        RootCauseDriller driller = new RootCauseDriller(0.7);
        List<Pair<List<List<String>>, Double>> res = driller.getRootCause(dimList6, dataList6, "Down");
        System.out.println(res);
        Assert.assertArrayEquals(new String[] {"A", null}, res.get(0).getFirst().get(0).toArray());
    }

    @Test
    public void testNonNullDimensionCnt() {
        List<String> dimList = new ArrayList<>();
        dimList.add("A");
        dimList.add("B");
        dimList.add(null);
        dimList.add(null);
        int cnt = RootCauseDriller.nonNullDimensionCnt(dimList);
        Assert.assertEquals(2, cnt);
        dimList.add("B");
        cnt = RootCauseDriller.nonNullDimensionCnt(dimList);
        Assert.assertEquals(3, cnt);
    }

    @Test
    public void testGetNonNullDimensionIndex() {
        List<String> dimList = new ArrayList<>();
        dimList.add("A");
        dimList.add("B");
        dimList.add(null);
        dimList.add(null);
        int index = RootCauseDriller.getNonNullDimensionIndex(dimList);
        Assert.assertEquals(0, index);
        dimList.add("B");
        index = RootCauseDriller.getNonNullDimensionIndex(dimList);
        Assert.assertEquals(0, index);
    }
}
