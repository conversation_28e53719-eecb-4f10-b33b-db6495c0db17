package com.aliyun.xdragon.algorithm.imputation;

import java.util.Random;

import com.aliyun.xdragon.common.enumeration.MissingFillType;
import org.apache.commons.math3.analysis.polynomials.PolynomialFunctionLagrangeForm;
import org.apache.commons.math3.util.FastMath;
import org.junit.Assert;
import org.junit.Test;

public class ImputationTest {

    private double delta = 1e-10;
    private int times = 100;


    @Test
    public void imputeSimple() {
        double[] time = {0, 1, 2, 3, 4, 5, 6, 7, 8, 10};
        double[] value = {93.0, 9.0, 8.0, 7.0, 6.0, 5.0, 4.0, 2.0, 1.0, 2.0};

        Assert.assertEquals(1, Imputation.impute(time, value, 9, MissingFillType.PREVIOUS), delta);
        Assert.assertEquals(0, Imputation.impute(time, value, 9, MissingFillType.ZERO), delta);
        Assert.assertEquals(1.5, Imputation.impute(time, value, 9, MissingFillType.LINEAR), delta);
        Assert.assertEquals(6, Imputation.impute(time, value, 9, MissingFillType.MEDIAN), delta);
        Assert.assertEquals(15, Imputation.impute(time, value, 9, MissingFillType.MEAN), delta);
        Assert.assertEquals(2, Imputation.imputeNoIncoming(time, value, 11, MissingFillType.PREVIOUS), delta);
        Assert.assertEquals(0, Imputation.imputeNoIncoming(time, value, 11, MissingFillType.ZERO), delta);
        Assert.assertEquals(2.5, Imputation.imputeNoIncoming(time, value, 11, MissingFillType.LINEAR), delta);
        Assert.assertEquals(5.5, Imputation.imputeNoIncoming(time, value, 11, MissingFillType.MEDIAN), delta);
        Assert.assertEquals(13.7, Imputation.imputeNoIncoming(time, value, 11, MissingFillType.MEAN), delta);

    }


    @Test
    public void imputeQuad() {
        Random random = new Random(0xc0ffee);
        double[] time = {0, 1, 2, 3, 4, 5, 6, 7, 8, 10};
        double[] value = new double[10];

        for (int n = 0; n < times; n++) {
            double a = random.nextDouble() * 10;
            double b = random.nextDouble() * 10;
            double c = random.nextDouble() * 10;
            for (int i = 0; i < time.length; i++) {
                value[i] = a * time[i] * time[i] + b * time[i] + c;
            }
            Assert.assertEquals(a * 9 * 9 + b * 9 + c, Imputation.impute(time, value, 9, MissingFillType.QUAD), delta);
        }
    }


    @Test
    public void imputeCubic() {
        Random random = new Random(0xc0ffee);
        double[] time = {0, 1, 2, 3, 4, 5, 6, 7, 8, 10};
        double[] value = new double[10];

        for (int n = 0; n < times; n++) {
            double a = random.nextDouble() * 10;
            double b = random.nextDouble() * 10;
            double c = random.nextDouble() * 10;
            double d = random.nextDouble() * 10;
            for (int i = 0; i < time.length; i++) {
                value[i] = a * FastMath.pow(time[i], 3) + b * time[i] * time[i] + c * time[i] + d;
            }
            Assert.assertEquals(a * FastMath.pow(9, 3) + b * FastMath.pow(9, 2) + c * 9 + d,
                    Imputation.impute(time, value, 9, MissingFillType.CUBIC), delta);
        }
    }


    @Test
    public void imputeAR() {
        double[] time = {0, 1, 2, 3, 4, 5, 6, 7, 8, 10};
        double[] value = new double[10];
        value[0] = 1;
        value[1] = 1.3;
        Random random = new Random(0xc0ffee);

        for (int n = 0; n < times; n++) {
            double a = random.nextDouble();
            double b = random.nextDouble();
            double c = random.nextDouble();

            for (int i = 2; i < value.length; i++) {
                value[i] = a * value[i - 1] + b * value[i - 2] + c;
            }
            Assert.assertEquals(a * value[8] + b * value[7] + c,
                    Imputation.impute(time, value, 9, MissingFillType.AR), delta);
        }
    }


    @Test
    public void imputeSpline() {
        double[] time = {0, 1, 2, 3, 4, 5, 6, 7, 8, 10};
        double[] value = new double[10];
        Random random = new Random(0xc0ffee);
        for (int n = 0; n < times; n++) {

            double[] two = new double[10];
            double lastOne = 0, lastZero = 0;
            for (int i = 1; i < 9; i++) {
                two[i] = random.nextDouble() * 10;
            }
            lastOne = random.nextDouble();
            lastZero = random.nextDouble();


            value[0] = lastZero;
            double a = 0, b = 0, c = 0, d = 0;
            for (int i = 0; i < time.length - 1; i++) {
                double x[] = {time[i], time[i + 1]};
                double y[] = {two[i], two[i + 1]};
                PolynomialFunctionLagrangeForm func = new PolynomialFunctionLagrangeForm(x, y);
                double coe[] = func.getCoefficients();
                a = coe[1] / 6.0;
                b = coe[0] / 2.0;
                c = lastOne - 3 * a * FastMath.pow(time[i], 2) - 2 * b * time[i];
                d = lastZero - a * FastMath.pow(time[i], 3) - b * FastMath.pow(time[i], 2) - c * time[i];
                lastOne = 3 * a * FastMath.pow(time[i + 1], 2) + 2 * b * time[i + 1] + c;
                lastZero = a * FastMath.pow(time[i + 1], 3) + b * FastMath.pow(time[i + 1], 2) + c * time[i + 1] + d;
                value[i + 1] = lastZero;
            }


            Assert.assertEquals(a * FastMath.pow(9, 3) + b * 9 * 9 + c * 9 + d,
                    Imputation.impute(time, value, 9, MissingFillType.SPLINE), delta);
        }
    }


    @Test
    public void testException() {

        Exception ex = Assert.assertThrows(IllegalArgumentException.class,
                () -> Imputation.impute(new double[4], new double[5], 0, MissingFillType.LINEAR));
        Assert.assertEquals("time and value array have different lengths.", ex.getMessage());

        ex = Assert.assertThrows(IllegalArgumentException.class,
                () -> Imputation.impute(new double[1], new double[1], 0, MissingFillType.LINEAR));
        Assert.assertEquals("length of value is 1, but should >= 2.", ex.getMessage());

        Assert.assertThrows(NullPointerException.class,
                () -> Imputation.impute(new double[4], new double[4], 0, null));
    }

}