package com.aliyun.xdragon.algorithm.detector.custom;

import com.aliyun.xdragon.algorithm.common.DetectorConstants;
import com.aliyun.xdragon.common.enumeration.algorithm.AnomalyType;
import junit.framework.TestCase;
import org.junit.Test;

import java.util.Properties;

public class BaselineDetectorTest {
    @Test
    public void detectAnomalyTest(){
        Properties params = new Properties(){
            {
                put(DetectorConstants.THRESHOLD, 9.0);
            }
        };
        TestCase.assertEquals(BaselineDetector.detectAnomaly(0, params), AnomalyType.DROP);
        TestCase.assertEquals(BaselineDetector.detectAnomaly(10, params), AnomalyType.RAISE);
        TestCase.assertNull(BaselineDetector.detectAnomaly(9, params));
    }
}
