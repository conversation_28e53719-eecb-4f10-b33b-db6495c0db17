package com.aliyun.xdragon.algorithm.common;

import junit.framework.TestCase;
import org.apache.commons.math3.stat.StatUtils;
import org.junit.Test;

/**
 * 插件测试
 *
 * <AUTHOR>
 * @date 2022/0402
 */

public class MedianFilterTest {

    @Test
    public void filterTest(){
        double[] testList = new double[]{1,2,3,4,5,6,7,2,5,5,4,4,3,};
        // 过滤后的数组为 3,4,5,5,5,5,5,4,4
        TestCase.assertEquals(40.0, StatUtils.sum(FilterUtils.medianFilter(testList, 5)));
        TestCase.assertEquals(1.0, StatUtils.sum(FilterUtils.medianFilter(new double[]{1}, 1)));
        TestCase.assertEquals(2.0, StatUtils.sum(FilterUtils.medianFilter(new double[]{1,2,3}, 5)));
    }
}
