import os
import csv

def search_files(directory, suffix):
    file_list = []
    for root, dirs, files in os.walk(directory):
        for filename in files:
            if filename.endswith(suffix):
                filepath = os.path.join(root, filename)
                file_list.append(filepath)
    return file_list

def process_file(filepath):
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')

        input_id = search_with_prefix(lines, "## Input")
        output_id = search_with_prefix(lines, "## Output")
        mock_id = search_with_prefix(lines, "## Mock")

        query = extract(lines, input_id + 1, output_id)
        expect_answer = extract(lines, output_id + 1, mock_id)
        test_id = filepath[filepath.rfind('/')+1:filepath.rfind('.')]
        return query, expect_answer, test_id
    raise RuntimeError(f"{filepath} is not found.")

def extract(lines, start, end):
    result = []
    for line in lines[start:end]:
        stripped_line = line.strip()
        if stripped_line:
            result.append(stripped_line)
    return '\n'.join(result)

def search_with_prefix(lines, prefix):
    for idx, line in enumerate(lines):
        if line.startswith(prefix):
            return idx
    raise RuntimeError(f"{prefix} is not found.")

def main(directory):
    # 获取所有.md文件
    file_list = search_files(directory, '.md')
    # 定义CSV表头
    data = [
        ["query", "expectAnswer", "testId"]
    ]
    # 遍历文件列表，处理每个文件
    for filepath in file_list:
        data.append(process_file(filepath))
    # 写入CSV
    with open('baiLianAgentChat.csv', 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file, quoting=csv.QUOTE_ALL)
        writer.writerows(data)

if __name__ == '__main__':
    main("metric-biz/src/test/resources/AiAgentTest/")