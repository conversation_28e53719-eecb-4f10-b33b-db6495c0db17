package com.aliyun.xdragon.common.enumeration;

/**
 * <AUTHOR>
 * @date 2022/04/27
 */
public enum TimeShiftUnit {
    DAY("d", 86400),
    HOUR("h", 3600),
    MINUTE("m", 60);
    private String unit;
    private Integer second;
    TimeShiftUnit(String unit, Integer second) {
        this.unit = unit;
        this.second = second;
    }

    public String getUnit() {
        return unit;
    }

    public Integer getSecond() {
        return second;
    }

    public static TimeShiftUnit of(String unit) {
        for(TimeShiftUnit t: TimeShiftUnit.values()) {
            if (t.getUnit().equals(unit)) {
                return t;
            }
        }
        return null;
    }
    public static boolean check(String unit) {
        TimeShiftUnit t = of(unit);
        return t != null;
    }
}
