package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.FollowUsers;
import com.aliyun.xdragon.common.generate.model.FollowUsersExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface FollowUsersMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    long countByExample(FollowUsersExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    int deleteByExample(FollowUsersExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    int insert(FollowUsers record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    int insertSelective(FollowUsers record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    List<FollowUsers> selectByExampleWithRowbounds(FollowUsersExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    List<FollowUsers> selectByExampleSelective(@Param("example") FollowUsersExample example, @Param("selective") FollowUsers.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    FollowUsers selectOneByExample(FollowUsersExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    FollowUsers selectOneByExampleSelective(@Param("example") FollowUsersExample example, @Param("selective") FollowUsers.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    List<FollowUsers> selectByExample(FollowUsersExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    FollowUsers selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") FollowUsers.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    FollowUsers selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") FollowUsers record, @Param("example") FollowUsersExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") FollowUsers record, @Param("example") FollowUsersExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(FollowUsers record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(FollowUsers record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<FollowUsers> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<FollowUsers> list, @Param("selective") FollowUsers.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    int upsert(FollowUsers record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table follow_users
     *
     * @mbg.generated
     */
    int upsertSelective(FollowUsers record);
}