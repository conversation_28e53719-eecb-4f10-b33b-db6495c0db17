package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.LogClusterMetric;
import com.aliyun.xdragon.common.generate.model.LogClusterMetricExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface LogClusterMetricMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    long countByExample(LogClusterMetricExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    int deleteByExample(LogClusterMetricExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    int insert(LogClusterMetric record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    int insertSelective(LogClusterMetric record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    List<LogClusterMetric> selectByExampleWithRowbounds(LogClusterMetricExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    List<LogClusterMetric> selectByExampleSelective(@Param("example") LogClusterMetricExample example, @Param("selective") LogClusterMetric.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    LogClusterMetric selectOneByExample(LogClusterMetricExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    LogClusterMetric selectOneByExampleSelective(@Param("example") LogClusterMetricExample example, @Param("selective") LogClusterMetric.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    List<LogClusterMetric> selectByExample(LogClusterMetricExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    LogClusterMetric selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") LogClusterMetric.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    LogClusterMetric selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") LogClusterMetric record, @Param("example") LogClusterMetricExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") LogClusterMetric record, @Param("example") LogClusterMetricExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(LogClusterMetric record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(LogClusterMetric record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<LogClusterMetric> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<LogClusterMetric> list, @Param("selective") LogClusterMetric.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    int upsert(LogClusterMetric record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_metric
     *
     * @mbg.generated
     */
    int upsertSelective(LogClusterMetric record);
}