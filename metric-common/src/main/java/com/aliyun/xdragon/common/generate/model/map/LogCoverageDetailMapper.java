package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.LogCoverageDetail;
import com.aliyun.xdragon.common.generate.model.LogCoverageDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface LogCoverageDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    long countByExample(LogCoverageDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    int deleteByExample(LogCoverageDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    int insert(LogCoverageDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    int insertSelective(LogCoverageDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    List<LogCoverageDetail> selectByExampleWithRowbounds(LogCoverageDetailExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    List<LogCoverageDetail> selectByExampleSelective(@Param("example") LogCoverageDetailExample example, @Param("selective") LogCoverageDetail.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    LogCoverageDetail selectOneByExample(LogCoverageDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    LogCoverageDetail selectOneByExampleSelective(@Param("example") LogCoverageDetailExample example, @Param("selective") LogCoverageDetail.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    List<LogCoverageDetail> selectByExample(LogCoverageDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    LogCoverageDetail selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") LogCoverageDetail.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    LogCoverageDetail selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") LogCoverageDetail record, @Param("example") LogCoverageDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") LogCoverageDetail record, @Param("example") LogCoverageDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(LogCoverageDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(LogCoverageDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<LogCoverageDetail> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<LogCoverageDetail> list, @Param("selective") LogCoverageDetail.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    int upsert(LogCoverageDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    int upsertSelective(LogCoverageDetail record);
}