package com.aliyun.xdragon.common.model.workItem.request;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/15
 */

@Data
public class UpdateWorkItemRequest implements Serializable {

    private static final long serialVersionUID = 3798112497243420712L;
    /**
     * id
     */
    @NotNull
    Long id;

    /**
     * calibrationFirstLabel
     */
    Long calibrationFirstLabel;

    /**
     * action
     */
    String action;

    /**
     * comment
     */
    String exceptionTime;

    /**
     * secondaryLabel
     */
    String secondaryLabel;

    /**
     * comment
     */
    String comment;

    public UpdateWorkItemRequest() {}

    public UpdateWorkItemRequest(Long id, Long calibrationFirstLabel, String action, String secondaryLabel,
        String comment, String exceptionTime) {
        this.id = id;
        this.calibrationFirstLabel = calibrationFirstLabel;
        this.action = action;
        this.secondaryLabel = secondaryLabel;
        this.comment = comment;
        this.exceptionTime = exceptionTime;
    }
}
