package com.aliyun.xdragon.common.model.keyMetric.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * <AUTHOR> 2023/11/23
 */
@Data
public class BaseClientOpsRequest implements Serializable {

    private static final long serialVersionUID = -2700279517233092186L;

    /**
     * aliUid
     */
    @NotNull
    @NotEmpty
    private String aliUid;

    /**
     * startMs
     */
    @NotNull
    @NotEmpty
    String startMs;

    /**
     * endMs
     */
    @NotNull
    @NotEmpty
    String endMs;
}
