package com.aliyun.xdragon.common.model.param;

/**
 * <AUTHOR>
 * @date 2022/04/12
 */

import java.io.Serializable;
import java.util.List;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class EvaluateRequest implements Serializable {

    @SerializedName("metric")
    @Expose
    private String metric;
    @SerializedName("data")
    @Expose
    private List<Datum> data = null;
    @SerializedName("level")
    @Expose
    private Double level = 0.05;

    private final static long serialVersionUID = -8831320820094182401L;

    public String getMetric() {
        return metric;
    }

    public void setMetric(String metric) {
        this.metric = metric;
    }

    public List<Datum> getData() {
        return data;
    }

    public void setData(List<Datum> data) {
        this.data = data;
    }

    public Double getLevel() {
        return level;
    }

    public void setLevel(Double level) {
        this.level = level;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(EvaluateRequest.class.getName()).append('@').append(
            Integer.toHexString(System.identityHashCode(this))).append('[');
        sb.append("metric");
        sb.append('=');
        sb.append(((this.metric == null) ? "<null>" : this.metric));
        sb.append(',');
        sb.append("data");
        sb.append('=');
        sb.append(((this.data == null) ? "<null>" : this.data));
        sb.append(',');
        if (sb.charAt((sb.length() - 1)) == ',') {
            sb.setCharAt((sb.length() - 1), ']');
        } else {
            sb.append(']');
        }
        return sb.toString();
    }

    public class Datum implements Serializable {

        @SerializedName("name")
        @Expose
        private String name;
        @SerializedName("value")
        @Expose
        private List<Double> value = null;
        private final static long serialVersionUID = 2917410755871692650L;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<Double> getValue() {
            return value;
        }

        public void setValue(List<Double> value) {
            this.value = value;
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append(Datum.class.getName()).append('@').append(Integer.toHexString(System.identityHashCode(this)))
                .append('[');
            sb.append("name");
            sb.append('=');
            sb.append(((this.name == null) ? "<null>" : this.name));
            sb.append(',');
            sb.append("value");
            sb.append('=');
            sb.append(((this.value == null) ? "<null>" : this.value));
            sb.append(',');
            if (sb.charAt((sb.length() - 1)) == ',') {
                sb.setCharAt((sb.length() - 1), ']');
            } else {
                sb.append(']');
            }
            return sb.toString();
        }

    }

}
