package com.aliyun.xdragon.common.model.clientops.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.aliyun.xdragon.common.enumeration.ClientResourceType;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/04/03
 */
@Data
public class CheckClientResourceRequest implements Serializable {
    private static final long serialVersionUID = 2819294759860041716L;

    @NotBlank
    private String empId;

    @NotNull
    private ClientResourceType type;

    @Size(min = 1, max = 100)
    private List<String> resources;
}
