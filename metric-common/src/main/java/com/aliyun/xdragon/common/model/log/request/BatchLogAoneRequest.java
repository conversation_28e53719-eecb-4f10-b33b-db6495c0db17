package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/12
 */
@Data
public class BatchLogAoneRequest implements Serializable {
    private static final long serialVersionUID = -1684929231785453935L;

    @Valid
    @Size(min = 1, max = 25)
    List<LogAoneRequest> requests;
}
