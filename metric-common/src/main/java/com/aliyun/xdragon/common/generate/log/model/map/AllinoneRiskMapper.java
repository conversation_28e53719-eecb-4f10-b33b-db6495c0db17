package com.aliyun.xdragon.common.generate.log.model.map;

import com.aliyun.xdragon.common.generate.log.model.AllinoneRisk;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRiskExample;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRiskKey;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface AllinoneRiskMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    long countByExample(AllinoneRiskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    int deleteByExample(AllinoneRiskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(AllinoneRisk<PERSON><PERSON> key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    int insert(AllinoneRisk record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    int insertSelective(AllinoneRisk record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    List<AllinoneRisk> selectByExampleWithRowbounds(AllinoneRiskExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    List<AllinoneRisk> selectByExampleSelective(@Param("example") AllinoneRiskExample example, @Param("selective") AllinoneRisk.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    AllinoneRisk selectOneByExample(AllinoneRiskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    AllinoneRisk selectOneByExampleSelective(@Param("example") AllinoneRiskExample example, @Param("selective") AllinoneRisk.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    List<AllinoneRisk> selectByExample(AllinoneRiskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    AllinoneRisk selectByPrimaryKeySelective(@Param("record") AllinoneRiskKey key, @Param("selective") AllinoneRisk.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    AllinoneRisk selectByPrimaryKey(AllinoneRiskKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") AllinoneRisk record, @Param("example") AllinoneRiskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") AllinoneRisk record, @Param("example") AllinoneRiskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AllinoneRisk record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AllinoneRisk record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<AllinoneRisk> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<AllinoneRisk> list, @Param("selective") AllinoneRisk.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    int upsert(AllinoneRisk record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk
     *
     * @mbg.generated
     */
    int upsertSelective(AllinoneRisk record);
}