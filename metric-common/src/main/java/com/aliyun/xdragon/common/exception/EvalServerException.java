package com.aliyun.xdragon.common.exception;

/**
 * <AUTHOR>
 * @date 2022/04/21
 */
public class EvalServerException extends XdragonMetricSystemException {
    private static final String CODE = "EVAL_SERVER_ERROR";
    public EvalServerException(String message) {
        super(message);
    }

    public EvalServerException(String message, Throwable e) {
        super(message, e);
    }

    public EvalServerException(Throwable e) {
        super(e);
    }

    @Override
    public String getCode() {
        return CODE;
    }
}
