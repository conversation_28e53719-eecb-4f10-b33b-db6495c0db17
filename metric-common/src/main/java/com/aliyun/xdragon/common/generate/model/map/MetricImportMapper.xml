<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.model.map.MetricImportMapper">
  <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.model.MetricImport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="BIGINT" property="gmtModified" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="instance" jdbcType="VARCHAR" property="instance" />
    <result column="instance_ids" jdbcType="VARCHAR" property="instanceIds" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="value_aggr" jdbcType="VARCHAR" property="valueAggr" />
    <result column="aggr_window" jdbcType="INTEGER" property="aggrWindow" />
    <result column="last_aggr_time" jdbcType="INTEGER" property="lastAggrTime" />
    <result column="backtrack" jdbcType="BIT" property="backtrack" />
    <result column="max_back_window" jdbcType="INTEGER" property="maxBackWindow" />
    <result column="sls_query_condition" jdbcType="VARCHAR" property="slsQueryCondition" />
    <result column="sls_analyse_condition" jdbcType="VARCHAR" property="slsAnalyseCondition" />
    <result column="advance_sql" jdbcType="VARCHAR" property="advanceSql" />
    <result column="is_enabled" jdbcType="BIT" property="isEnabled" />
    <result column="aggr_names" jdbcType="VARCHAR" property="aggrNames" />
    <result column="project" jdbcType="VARCHAR" property="project" />
    <result column="is_group" jdbcType="BIT" property="isGroup" />
    <result column="logstore" jdbcType="VARCHAR" property="logstore" />
    <result column="user" jdbcType="VARCHAR" property="user" />
    <result column="region" jdbcType="VARCHAR" property="region" />
    <result column="tsdb" jdbcType="VARCHAR" property="tsdb" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, gmt_create, gmt_modified, `name`, `instance`, instance_ids, `value`, value_aggr, 
    aggr_window, last_aggr_time, backtrack, max_back_window, sls_query_condition, sls_analyse_condition, 
    advance_sql, is_enabled, aggr_names, project, is_group, logstore, `user`, region, 
    tsdb
  </sql>
  <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.model.MetricImportExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from metric_import_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="example != null and example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from metric_import_config
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from metric_import_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from metric_import_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from metric_import_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.model.MetricImportExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from metric_import_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.model.MetricImport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into metric_import_config (gmt_create, gmt_modified, `name`, 
      `instance`, instance_ids, `value`, 
      value_aggr, aggr_window, last_aggr_time, 
      backtrack, max_back_window, sls_query_condition, 
      sls_analyse_condition, advance_sql, is_enabled, 
      aggr_names, project, is_group, 
      logstore, `user`, region, 
      tsdb)
    values (#{gmtCreate,jdbcType=BIGINT}, #{gmtModified,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{instance,jdbcType=VARCHAR}, #{instanceIds,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR}, 
      #{valueAggr,jdbcType=VARCHAR}, #{aggrWindow,jdbcType=INTEGER}, #{lastAggrTime,jdbcType=INTEGER}, 
      #{backtrack,jdbcType=BIT}, #{maxBackWindow,jdbcType=INTEGER}, #{slsQueryCondition,jdbcType=VARCHAR}, 
      #{slsAnalyseCondition,jdbcType=VARCHAR}, #{advanceSql,jdbcType=VARCHAR}, #{isEnabled,jdbcType=BIT}, 
      #{aggrNames,jdbcType=VARCHAR}, #{project,jdbcType=VARCHAR}, #{isGroup,jdbcType=BIT}, 
      #{logstore,jdbcType=VARCHAR}, #{user,jdbcType=VARCHAR}, #{region,jdbcType=VARCHAR}, 
      #{tsdb,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.model.MetricImport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into metric_import_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="instance != null">
        `instance`,
      </if>
      <if test="instanceIds != null">
        instance_ids,
      </if>
      <if test="value != null">
        `value`,
      </if>
      <if test="valueAggr != null">
        value_aggr,
      </if>
      <if test="aggrWindow != null">
        aggr_window,
      </if>
      <if test="lastAggrTime != null">
        last_aggr_time,
      </if>
      <if test="backtrack != null">
        backtrack,
      </if>
      <if test="maxBackWindow != null">
        max_back_window,
      </if>
      <if test="slsQueryCondition != null">
        sls_query_condition,
      </if>
      <if test="slsAnalyseCondition != null">
        sls_analyse_condition,
      </if>
      <if test="advanceSql != null">
        advance_sql,
      </if>
      <if test="isEnabled != null">
        is_enabled,
      </if>
      <if test="aggrNames != null">
        aggr_names,
      </if>
      <if test="project != null">
        project,
      </if>
      <if test="isGroup != null">
        is_group,
      </if>
      <if test="logstore != null">
        logstore,
      </if>
      <if test="user != null">
        `user`,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="tsdb != null">
        tsdb,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="instance != null">
        #{instance,jdbcType=VARCHAR},
      </if>
      <if test="instanceIds != null">
        #{instanceIds,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="valueAggr != null">
        #{valueAggr,jdbcType=VARCHAR},
      </if>
      <if test="aggrWindow != null">
        #{aggrWindow,jdbcType=INTEGER},
      </if>
      <if test="lastAggrTime != null">
        #{lastAggrTime,jdbcType=INTEGER},
      </if>
      <if test="backtrack != null">
        #{backtrack,jdbcType=BIT},
      </if>
      <if test="maxBackWindow != null">
        #{maxBackWindow,jdbcType=INTEGER},
      </if>
      <if test="slsQueryCondition != null">
        #{slsQueryCondition,jdbcType=VARCHAR},
      </if>
      <if test="slsAnalyseCondition != null">
        #{slsAnalyseCondition,jdbcType=VARCHAR},
      </if>
      <if test="advanceSql != null">
        #{advanceSql,jdbcType=VARCHAR},
      </if>
      <if test="isEnabled != null">
        #{isEnabled,jdbcType=BIT},
      </if>
      <if test="aggrNames != null">
        #{aggrNames,jdbcType=VARCHAR},
      </if>
      <if test="project != null">
        #{project,jdbcType=VARCHAR},
      </if>
      <if test="isGroup != null">
        #{isGroup,jdbcType=BIT},
      </if>
      <if test="logstore != null">
        #{logstore,jdbcType=VARCHAR},
      </if>
      <if test="user != null">
        #{user,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="tsdb != null">
        #{tsdb,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.model.MetricImportExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from metric_import_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update metric_import_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.instance != null">
        `instance` = #{record.instance,jdbcType=VARCHAR},
      </if>
      <if test="record.instanceIds != null">
        instance_ids = #{record.instanceIds,jdbcType=VARCHAR},
      </if>
      <if test="record.value != null">
        `value` = #{record.value,jdbcType=VARCHAR},
      </if>
      <if test="record.valueAggr != null">
        value_aggr = #{record.valueAggr,jdbcType=VARCHAR},
      </if>
      <if test="record.aggrWindow != null">
        aggr_window = #{record.aggrWindow,jdbcType=INTEGER},
      </if>
      <if test="record.lastAggrTime != null">
        last_aggr_time = #{record.lastAggrTime,jdbcType=INTEGER},
      </if>
      <if test="record.backtrack != null">
        backtrack = #{record.backtrack,jdbcType=BIT},
      </if>
      <if test="record.maxBackWindow != null">
        max_back_window = #{record.maxBackWindow,jdbcType=INTEGER},
      </if>
      <if test="record.slsQueryCondition != null">
        sls_query_condition = #{record.slsQueryCondition,jdbcType=VARCHAR},
      </if>
      <if test="record.slsAnalyseCondition != null">
        sls_analyse_condition = #{record.slsAnalyseCondition,jdbcType=VARCHAR},
      </if>
      <if test="record.advanceSql != null">
        advance_sql = #{record.advanceSql,jdbcType=VARCHAR},
      </if>
      <if test="record.isEnabled != null">
        is_enabled = #{record.isEnabled,jdbcType=BIT},
      </if>
      <if test="record.aggrNames != null">
        aggr_names = #{record.aggrNames,jdbcType=VARCHAR},
      </if>
      <if test="record.project != null">
        project = #{record.project,jdbcType=VARCHAR},
      </if>
      <if test="record.isGroup != null">
        is_group = #{record.isGroup,jdbcType=BIT},
      </if>
      <if test="record.logstore != null">
        logstore = #{record.logstore,jdbcType=VARCHAR},
      </if>
      <if test="record.user != null">
        `user` = #{record.user,jdbcType=VARCHAR},
      </if>
      <if test="record.region != null">
        region = #{record.region,jdbcType=VARCHAR},
      </if>
      <if test="record.tsdb != null">
        tsdb = #{record.tsdb,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update metric_import_config
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=BIGINT},
      gmt_modified = #{record.gmtModified,jdbcType=BIGINT},
      `name` = #{record.name,jdbcType=VARCHAR},
      `instance` = #{record.instance,jdbcType=VARCHAR},
      instance_ids = #{record.instanceIds,jdbcType=VARCHAR},
      `value` = #{record.value,jdbcType=VARCHAR},
      value_aggr = #{record.valueAggr,jdbcType=VARCHAR},
      aggr_window = #{record.aggrWindow,jdbcType=INTEGER},
      last_aggr_time = #{record.lastAggrTime,jdbcType=INTEGER},
      backtrack = #{record.backtrack,jdbcType=BIT},
      max_back_window = #{record.maxBackWindow,jdbcType=INTEGER},
      sls_query_condition = #{record.slsQueryCondition,jdbcType=VARCHAR},
      sls_analyse_condition = #{record.slsAnalyseCondition,jdbcType=VARCHAR},
      advance_sql = #{record.advanceSql,jdbcType=VARCHAR},
      is_enabled = #{record.isEnabled,jdbcType=BIT},
      aggr_names = #{record.aggrNames,jdbcType=VARCHAR},
      project = #{record.project,jdbcType=VARCHAR},
      is_group = #{record.isGroup,jdbcType=BIT},
      logstore = #{record.logstore,jdbcType=VARCHAR},
      `user` = #{record.user,jdbcType=VARCHAR},
      region = #{record.region,jdbcType=VARCHAR},
      tsdb = #{record.tsdb,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.xdragon.common.generate.model.MetricImport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update metric_import_config
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="instance != null">
        `instance` = #{instance,jdbcType=VARCHAR},
      </if>
      <if test="instanceIds != null">
        instance_ids = #{instanceIds,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        `value` = #{value,jdbcType=VARCHAR},
      </if>
      <if test="valueAggr != null">
        value_aggr = #{valueAggr,jdbcType=VARCHAR},
      </if>
      <if test="aggrWindow != null">
        aggr_window = #{aggrWindow,jdbcType=INTEGER},
      </if>
      <if test="lastAggrTime != null">
        last_aggr_time = #{lastAggrTime,jdbcType=INTEGER},
      </if>
      <if test="backtrack != null">
        backtrack = #{backtrack,jdbcType=BIT},
      </if>
      <if test="maxBackWindow != null">
        max_back_window = #{maxBackWindow,jdbcType=INTEGER},
      </if>
      <if test="slsQueryCondition != null">
        sls_query_condition = #{slsQueryCondition,jdbcType=VARCHAR},
      </if>
      <if test="slsAnalyseCondition != null">
        sls_analyse_condition = #{slsAnalyseCondition,jdbcType=VARCHAR},
      </if>
      <if test="advanceSql != null">
        advance_sql = #{advanceSql,jdbcType=VARCHAR},
      </if>
      <if test="isEnabled != null">
        is_enabled = #{isEnabled,jdbcType=BIT},
      </if>
      <if test="aggrNames != null">
        aggr_names = #{aggrNames,jdbcType=VARCHAR},
      </if>
      <if test="project != null">
        project = #{project,jdbcType=VARCHAR},
      </if>
      <if test="isGroup != null">
        is_group = #{isGroup,jdbcType=BIT},
      </if>
      <if test="logstore != null">
        logstore = #{logstore,jdbcType=VARCHAR},
      </if>
      <if test="user != null">
        `user` = #{user,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        region = #{region,jdbcType=VARCHAR},
      </if>
      <if test="tsdb != null">
        tsdb = #{tsdb,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.model.MetricImport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update metric_import_config
    set gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_modified = #{gmtModified,jdbcType=BIGINT},
      `name` = #{name,jdbcType=VARCHAR},
      `instance` = #{instance,jdbcType=VARCHAR},
      instance_ids = #{instanceIds,jdbcType=VARCHAR},
      `value` = #{value,jdbcType=VARCHAR},
      value_aggr = #{valueAggr,jdbcType=VARCHAR},
      aggr_window = #{aggrWindow,jdbcType=INTEGER},
      last_aggr_time = #{lastAggrTime,jdbcType=INTEGER},
      backtrack = #{backtrack,jdbcType=BIT},
      max_back_window = #{maxBackWindow,jdbcType=INTEGER},
      sls_query_condition = #{slsQueryCondition,jdbcType=VARCHAR},
      sls_analyse_condition = #{slsAnalyseCondition,jdbcType=VARCHAR},
      advance_sql = #{advanceSql,jdbcType=VARCHAR},
      is_enabled = #{isEnabled,jdbcType=BIT},
      aggr_names = #{aggrNames,jdbcType=VARCHAR},
      project = #{project,jdbcType=VARCHAR},
      is_group = #{isGroup,jdbcType=BIT},
      logstore = #{logstore,jdbcType=VARCHAR},
      `user` = #{user,jdbcType=VARCHAR},
      region = #{region,jdbcType=VARCHAR},
      tsdb = #{tsdb,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.model.MetricImportExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from metric_import_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into metric_import_config
    (gmt_create, gmt_modified, `name`, `instance`, instance_ids, `value`, value_aggr, 
      aggr_window, last_aggr_time, backtrack, max_back_window, sls_query_condition, sls_analyse_condition, 
      advance_sql, is_enabled, aggr_names, project, is_group, logstore, `user`, region, 
      tsdb)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.gmtCreate,jdbcType=BIGINT}, #{item.gmtModified,jdbcType=BIGINT}, #{item.name,jdbcType=VARCHAR}, 
        #{item.instance,jdbcType=VARCHAR}, #{item.instanceIds,jdbcType=VARCHAR}, #{item.value,jdbcType=VARCHAR}, 
        #{item.valueAggr,jdbcType=VARCHAR}, #{item.aggrWindow,jdbcType=INTEGER}, #{item.lastAggrTime,jdbcType=INTEGER}, 
        #{item.backtrack,jdbcType=BIT}, #{item.maxBackWindow,jdbcType=INTEGER}, #{item.slsQueryCondition,jdbcType=VARCHAR}, 
        #{item.slsAnalyseCondition,jdbcType=VARCHAR}, #{item.advanceSql,jdbcType=VARCHAR}, 
        #{item.isEnabled,jdbcType=BIT}, #{item.aggrNames,jdbcType=VARCHAR}, #{item.project,jdbcType=VARCHAR}, 
        #{item.isGroup,jdbcType=BIT}, #{item.logstore,jdbcType=VARCHAR}, #{item.user,jdbcType=VARCHAR}, 
        #{item.region,jdbcType=VARCHAR}, #{item.tsdb,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into metric_import_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'gmt_create'.toString() == column.value">
          #{item.gmtCreate,jdbcType=BIGINT}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=BIGINT}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'instance'.toString() == column.value">
          #{item.instance,jdbcType=VARCHAR}
        </if>
        <if test="'instance_ids'.toString() == column.value">
          #{item.instanceIds,jdbcType=VARCHAR}
        </if>
        <if test="'value'.toString() == column.value">
          #{item.value,jdbcType=VARCHAR}
        </if>
        <if test="'value_aggr'.toString() == column.value">
          #{item.valueAggr,jdbcType=VARCHAR}
        </if>
        <if test="'aggr_window'.toString() == column.value">
          #{item.aggrWindow,jdbcType=INTEGER}
        </if>
        <if test="'last_aggr_time'.toString() == column.value">
          #{item.lastAggrTime,jdbcType=INTEGER}
        </if>
        <if test="'backtrack'.toString() == column.value">
          #{item.backtrack,jdbcType=BIT}
        </if>
        <if test="'max_back_window'.toString() == column.value">
          #{item.maxBackWindow,jdbcType=INTEGER}
        </if>
        <if test="'sls_query_condition'.toString() == column.value">
          #{item.slsQueryCondition,jdbcType=VARCHAR}
        </if>
        <if test="'sls_analyse_condition'.toString() == column.value">
          #{item.slsAnalyseCondition,jdbcType=VARCHAR}
        </if>
        <if test="'advance_sql'.toString() == column.value">
          #{item.advanceSql,jdbcType=VARCHAR}
        </if>
        <if test="'is_enabled'.toString() == column.value">
          #{item.isEnabled,jdbcType=BIT}
        </if>
        <if test="'aggr_names'.toString() == column.value">
          #{item.aggrNames,jdbcType=VARCHAR}
        </if>
        <if test="'project'.toString() == column.value">
          #{item.project,jdbcType=VARCHAR}
        </if>
        <if test="'is_group'.toString() == column.value">
          #{item.isGroup,jdbcType=BIT}
        </if>
        <if test="'logstore'.toString() == column.value">
          #{item.logstore,jdbcType=VARCHAR}
        </if>
        <if test="'user'.toString() == column.value">
          #{item.user,jdbcType=VARCHAR}
        </if>
        <if test="'region'.toString() == column.value">
          #{item.region,jdbcType=VARCHAR}
        </if>
        <if test="'tsdb'.toString() == column.value">
          #{item.tsdb,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
  <insert id="upsertSelective" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.MetricImport" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into metric_import_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="instance != null">
        `instance`,
      </if>
      <if test="instanceIds != null">
        instance_ids,
      </if>
      <if test="value != null">
        `value`,
      </if>
      <if test="valueAggr != null">
        value_aggr,
      </if>
      <if test="aggrWindow != null">
        aggr_window,
      </if>
      <if test="lastAggrTime != null">
        last_aggr_time,
      </if>
      <if test="backtrack != null">
        backtrack,
      </if>
      <if test="maxBackWindow != null">
        max_back_window,
      </if>
      <if test="slsQueryCondition != null">
        sls_query_condition,
      </if>
      <if test="slsAnalyseCondition != null">
        sls_analyse_condition,
      </if>
      <if test="advanceSql != null">
        advance_sql,
      </if>
      <if test="isEnabled != null">
        is_enabled,
      </if>
      <if test="aggrNames != null">
        aggr_names,
      </if>
      <if test="project != null">
        project,
      </if>
      <if test="isGroup != null">
        is_group,
      </if>
      <if test="logstore != null">
        logstore,
      </if>
      <if test="user != null">
        `user`,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="tsdb != null">
        tsdb,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="instance != null">
        #{instance,jdbcType=VARCHAR},
      </if>
      <if test="instanceIds != null">
        #{instanceIds,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="valueAggr != null">
        #{valueAggr,jdbcType=VARCHAR},
      </if>
      <if test="aggrWindow != null">
        #{aggrWindow,jdbcType=INTEGER},
      </if>
      <if test="lastAggrTime != null">
        #{lastAggrTime,jdbcType=INTEGER},
      </if>
      <if test="backtrack != null">
        #{backtrack,jdbcType=BIT},
      </if>
      <if test="maxBackWindow != null">
        #{maxBackWindow,jdbcType=INTEGER},
      </if>
      <if test="slsQueryCondition != null">
        #{slsQueryCondition,jdbcType=VARCHAR},
      </if>
      <if test="slsAnalyseCondition != null">
        #{slsAnalyseCondition,jdbcType=VARCHAR},
      </if>
      <if test="advanceSql != null">
        #{advanceSql,jdbcType=VARCHAR},
      </if>
      <if test="isEnabled != null">
        #{isEnabled,jdbcType=BIT},
      </if>
      <if test="aggrNames != null">
        #{aggrNames,jdbcType=VARCHAR},
      </if>
      <if test="project != null">
        #{project,jdbcType=VARCHAR},
      </if>
      <if test="isGroup != null">
        #{isGroup,jdbcType=BIT},
      </if>
      <if test="logstore != null">
        #{logstore,jdbcType=VARCHAR},
      </if>
      <if test="user != null">
        #{user,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="tsdb != null">
        #{tsdb,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="instance != null">
        `instance` = #{instance,jdbcType=VARCHAR},
      </if>
      <if test="instanceIds != null">
        instance_ids = #{instanceIds,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        `value` = #{value,jdbcType=VARCHAR},
      </if>
      <if test="valueAggr != null">
        value_aggr = #{valueAggr,jdbcType=VARCHAR},
      </if>
      <if test="aggrWindow != null">
        aggr_window = #{aggrWindow,jdbcType=INTEGER},
      </if>
      <if test="lastAggrTime != null">
        last_aggr_time = #{lastAggrTime,jdbcType=INTEGER},
      </if>
      <if test="backtrack != null">
        backtrack = #{backtrack,jdbcType=BIT},
      </if>
      <if test="maxBackWindow != null">
        max_back_window = #{maxBackWindow,jdbcType=INTEGER},
      </if>
      <if test="slsQueryCondition != null">
        sls_query_condition = #{slsQueryCondition,jdbcType=VARCHAR},
      </if>
      <if test="slsAnalyseCondition != null">
        sls_analyse_condition = #{slsAnalyseCondition,jdbcType=VARCHAR},
      </if>
      <if test="advanceSql != null">
        advance_sql = #{advanceSql,jdbcType=VARCHAR},
      </if>
      <if test="isEnabled != null">
        is_enabled = #{isEnabled,jdbcType=BIT},
      </if>
      <if test="aggrNames != null">
        aggr_names = #{aggrNames,jdbcType=VARCHAR},
      </if>
      <if test="project != null">
        project = #{project,jdbcType=VARCHAR},
      </if>
      <if test="isGroup != null">
        is_group = #{isGroup,jdbcType=BIT},
      </if>
      <if test="logstore != null">
        logstore = #{logstore,jdbcType=VARCHAR},
      </if>
      <if test="user != null">
        `user` = #{user,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        region = #{region,jdbcType=VARCHAR},
      </if>
      <if test="tsdb != null">
        tsdb = #{tsdb,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="upsert" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.MetricImport" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into metric_import_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      gmt_create,
      gmt_modified,
      `name`,
      `instance`,
      instance_ids,
      `value`,
      value_aggr,
      aggr_window,
      last_aggr_time,
      backtrack,
      max_back_window,
      sls_query_condition,
      sls_analyse_condition,
      advance_sql,
      is_enabled,
      aggr_names,
      project,
      is_group,
      logstore,
      `user`,
      region,
      tsdb,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{gmtCreate,jdbcType=BIGINT},
      #{gmtModified,jdbcType=BIGINT},
      #{name,jdbcType=VARCHAR},
      #{instance,jdbcType=VARCHAR},
      #{instanceIds,jdbcType=VARCHAR},
      #{value,jdbcType=VARCHAR},
      #{valueAggr,jdbcType=VARCHAR},
      #{aggrWindow,jdbcType=INTEGER},
      #{lastAggrTime,jdbcType=INTEGER},
      #{backtrack,jdbcType=BIT},
      #{maxBackWindow,jdbcType=INTEGER},
      #{slsQueryCondition,jdbcType=VARCHAR},
      #{slsAnalyseCondition,jdbcType=VARCHAR},
      #{advanceSql,jdbcType=VARCHAR},
      #{isEnabled,jdbcType=BIT},
      #{aggrNames,jdbcType=VARCHAR},
      #{project,jdbcType=VARCHAR},
      #{isGroup,jdbcType=BIT},
      #{logstore,jdbcType=VARCHAR},
      #{user,jdbcType=VARCHAR},
      #{region,jdbcType=VARCHAR},
      #{tsdb,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_modified = #{gmtModified,jdbcType=BIGINT},
      `name` = #{name,jdbcType=VARCHAR},
      `instance` = #{instance,jdbcType=VARCHAR},
      instance_ids = #{instanceIds,jdbcType=VARCHAR},
      `value` = #{value,jdbcType=VARCHAR},
      value_aggr = #{valueAggr,jdbcType=VARCHAR},
      aggr_window = #{aggrWindow,jdbcType=INTEGER},
      last_aggr_time = #{lastAggrTime,jdbcType=INTEGER},
      backtrack = #{backtrack,jdbcType=BIT},
      max_back_window = #{maxBackWindow,jdbcType=INTEGER},
      sls_query_condition = #{slsQueryCondition,jdbcType=VARCHAR},
      sls_analyse_condition = #{slsAnalyseCondition,jdbcType=VARCHAR},
      advance_sql = #{advanceSql,jdbcType=VARCHAR},
      is_enabled = #{isEnabled,jdbcType=BIT},
      aggr_names = #{aggrNames,jdbcType=VARCHAR},
      project = #{project,jdbcType=VARCHAR},
      is_group = #{isGroup,jdbcType=BIT},
      logstore = #{logstore,jdbcType=VARCHAR},
      `user` = #{user,jdbcType=VARCHAR},
      region = #{region,jdbcType=VARCHAR},
      tsdb = #{tsdb,jdbcType=VARCHAR},
    </trim>
  </insert>
  <select id="selectOneByExample" parameterType="com.aliyun.xdragon.common.generate.model.MetricImportExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from metric_import_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from metric_import_config
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
</mapper>