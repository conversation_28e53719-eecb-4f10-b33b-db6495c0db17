<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.model.map.LogPatternAccuracyCalcMapper">
  <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalc">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="md5" jdbcType="VARCHAR" property="md5" />
    <result column="pattern" jdbcType="VARCHAR" property="pattern" />
    <result column="appear_time" jdbcType="TIMESTAMP" property="appearTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="pattern_source" jdbcType="INTEGER" property="patternSource" />
    <result column="label_time" jdbcType="TIMESTAMP" property="labelTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, task_id, md5, pattern, appear_time, `status`, ext, pattern_source, label_time
  </sql>
  <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalcExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from log_pattern_accuracy_calc
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="example != null and example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from log_pattern_accuracy_calc
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from log_pattern_accuracy_calc
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from log_pattern_accuracy_calc
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from log_pattern_accuracy_calc
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalcExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from log_pattern_accuracy_calc
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalc">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into log_pattern_accuracy_calc (task_id, md5, pattern, 
      appear_time, `status`, ext, 
      pattern_source, label_time)
    values (#{taskId,jdbcType=BIGINT}, #{md5,jdbcType=VARCHAR}, #{pattern,jdbcType=VARCHAR}, 
      #{appearTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{ext,jdbcType=VARCHAR}, 
      #{patternSource,jdbcType=INTEGER}, #{labelTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalc">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into log_pattern_accuracy_calc
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="md5 != null">
        md5,
      </if>
      <if test="pattern != null">
        pattern,
      </if>
      <if test="appearTime != null">
        appear_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="patternSource != null">
        pattern_source,
      </if>
      <if test="labelTime != null">
        label_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="md5 != null">
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="pattern != null">
        #{pattern,jdbcType=VARCHAR},
      </if>
      <if test="appearTime != null">
        #{appearTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="patternSource != null">
        #{patternSource,jdbcType=INTEGER},
      </if>
      <if test="labelTime != null">
        #{labelTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalcExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from log_pattern_accuracy_calc
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_pattern_accuracy_calc
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.md5 != null">
        md5 = #{record.md5,jdbcType=VARCHAR},
      </if>
      <if test="record.pattern != null">
        pattern = #{record.pattern,jdbcType=VARCHAR},
      </if>
      <if test="record.appearTime != null">
        appear_time = #{record.appearTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.ext != null">
        ext = #{record.ext,jdbcType=VARCHAR},
      </if>
      <if test="record.patternSource != null">
        pattern_source = #{record.patternSource,jdbcType=INTEGER},
      </if>
      <if test="record.labelTime != null">
        label_time = #{record.labelTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_pattern_accuracy_calc
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      md5 = #{record.md5,jdbcType=VARCHAR},
      pattern = #{record.pattern,jdbcType=VARCHAR},
      appear_time = #{record.appearTime,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=INTEGER},
      ext = #{record.ext,jdbcType=VARCHAR},
      pattern_source = #{record.patternSource,jdbcType=INTEGER},
      label_time = #{record.labelTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalc">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_pattern_accuracy_calc
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="md5 != null">
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="pattern != null">
        pattern = #{pattern,jdbcType=VARCHAR},
      </if>
      <if test="appearTime != null">
        appear_time = #{appearTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="patternSource != null">
        pattern_source = #{patternSource,jdbcType=INTEGER},
      </if>
      <if test="labelTime != null">
        label_time = #{labelTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalc">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_pattern_accuracy_calc
    set task_id = #{taskId,jdbcType=BIGINT},
      md5 = #{md5,jdbcType=VARCHAR},
      pattern = #{pattern,jdbcType=VARCHAR},
      appear_time = #{appearTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      ext = #{ext,jdbcType=VARCHAR},
      pattern_source = #{patternSource,jdbcType=INTEGER},
      label_time = #{labelTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalcExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from log_pattern_accuracy_calc
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_pattern_accuracy_calc
    (task_id, md5, pattern, appear_time, `status`, ext, pattern_source, label_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.taskId,jdbcType=BIGINT}, #{item.md5,jdbcType=VARCHAR}, #{item.pattern,jdbcType=VARCHAR}, 
        #{item.appearTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=INTEGER}, #{item.ext,jdbcType=VARCHAR}, 
        #{item.patternSource,jdbcType=INTEGER}, #{item.labelTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_pattern_accuracy_calc (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'task_id'.toString() == column.value">
          #{item.taskId,jdbcType=BIGINT}
        </if>
        <if test="'md5'.toString() == column.value">
          #{item.md5,jdbcType=VARCHAR}
        </if>
        <if test="'pattern'.toString() == column.value">
          #{item.pattern,jdbcType=VARCHAR}
        </if>
        <if test="'appear_time'.toString() == column.value">
          #{item.appearTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'ext'.toString() == column.value">
          #{item.ext,jdbcType=VARCHAR}
        </if>
        <if test="'pattern_source'.toString() == column.value">
          #{item.patternSource,jdbcType=INTEGER}
        </if>
        <if test="'label_time'.toString() == column.value">
          #{item.labelTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
  <insert id="upsertSelective" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalc" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_pattern_accuracy_calc
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="md5 != null">
        md5,
      </if>
      <if test="pattern != null">
        pattern,
      </if>
      <if test="appearTime != null">
        appear_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="patternSource != null">
        pattern_source,
      </if>
      <if test="labelTime != null">
        label_time,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="md5 != null">
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="pattern != null">
        #{pattern,jdbcType=VARCHAR},
      </if>
      <if test="appearTime != null">
        #{appearTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="patternSource != null">
        #{patternSource,jdbcType=INTEGER},
      </if>
      <if test="labelTime != null">
        #{labelTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="md5 != null">
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="pattern != null">
        pattern = #{pattern,jdbcType=VARCHAR},
      </if>
      <if test="appearTime != null">
        appear_time = #{appearTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="patternSource != null">
        pattern_source = #{patternSource,jdbcType=INTEGER},
      </if>
      <if test="labelTime != null">
        label_time = #{labelTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="upsert" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalc" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_pattern_accuracy_calc
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      task_id,
      md5,
      pattern,
      appear_time,
      `status`,
      ext,
      pattern_source,
      label_time,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{taskId,jdbcType=BIGINT},
      #{md5,jdbcType=VARCHAR},
      #{pattern,jdbcType=VARCHAR},
      #{appearTime,jdbcType=TIMESTAMP},
      #{status,jdbcType=INTEGER},
      #{ext,jdbcType=VARCHAR},
      #{patternSource,jdbcType=INTEGER},
      #{labelTime,jdbcType=TIMESTAMP},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      task_id = #{taskId,jdbcType=BIGINT},
      md5 = #{md5,jdbcType=VARCHAR},
      pattern = #{pattern,jdbcType=VARCHAR},
      appear_time = #{appearTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      ext = #{ext,jdbcType=VARCHAR},
      pattern_source = #{patternSource,jdbcType=INTEGER},
      label_time = #{labelTime,jdbcType=TIMESTAMP},
    </trim>
  </insert>
  <select id="selectOneByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalcExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from log_pattern_accuracy_calc
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from log_pattern_accuracy_calc
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
</mapper>