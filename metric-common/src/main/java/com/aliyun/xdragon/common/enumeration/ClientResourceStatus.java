package com.aliyun.xdragon.common.enumeration;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/04/03
 */

@Getter
public enum ClientResourceStatus {
    /**
     * 未审批,数据入库
     */
    NOT_APPROVE(0),
    /**
     * 审批中
     */
    APPROVING(1),
    /**
     * 审批拒绝
     */
    APPROVE_REJECT(2),
    /**
     * 审批通过
     */
    APPROVE_ACCEPT(3),
    /**
     * 审批错误
     */
    APPROVE_ERROR(4),
    /**
     * 审批取消
     */
    APPROVE_CANCEL(5),
    /**
     * 超时删除
     */
    EXPIRED_DELETED(6),
    /**
     * 删除
     */
    DELETED(7),
    /**
     * TAM自动授权
     */
    CLOUDDOC_NOTIFY_ACCEPT(8);

    private final int status;
    ClientResourceStatus(int status) {
        this.status = status;
    }
}
