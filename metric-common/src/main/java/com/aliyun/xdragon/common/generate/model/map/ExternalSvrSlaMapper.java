package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.ExternalSvrSla;
import com.aliyun.xdragon.common.generate.model.ExternalSvrSlaExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface ExternalSvrSlaMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    long countByExample(ExternalSvrSlaExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    int deleteByExample(ExternalSvrSlaExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long sid);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    int insert(ExternalSvrSla record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    int insertSelective(ExternalSvrSla record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    List<ExternalSvrSla> selectByExampleWithRowbounds(ExternalSvrSlaExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    List<ExternalSvrSla> selectByExampleSelective(@Param("example") ExternalSvrSlaExample example, @Param("selective") ExternalSvrSla.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    ExternalSvrSla selectOneByExample(ExternalSvrSlaExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    ExternalSvrSla selectOneByExampleSelective(@Param("example") ExternalSvrSlaExample example, @Param("selective") ExternalSvrSla.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    List<ExternalSvrSla> selectByExample(ExternalSvrSlaExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    ExternalSvrSla selectByPrimaryKeySelective(@Param("sid") Long sid, @Param("selective") ExternalSvrSla.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    ExternalSvrSla selectByPrimaryKey(Long sid);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") ExternalSvrSla record, @Param("example") ExternalSvrSlaExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") ExternalSvrSla record, @Param("example") ExternalSvrSlaExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(ExternalSvrSla record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(ExternalSvrSla record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<ExternalSvrSla> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<ExternalSvrSla> list, @Param("selective") ExternalSvrSla.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    int upsert(ExternalSvrSla record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table external_svr_sla
     *
     * @mbg.generated
     */
    int upsertSelective(ExternalSvrSla record);
}