package com.aliyun.xdragon.common.generate.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MetricAnomalyConfigExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    public MetricAnomalyConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andConfigIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andConfigIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andConfigIdEqualTo(Long value) {
            addCriterion("id =", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdGreaterThan(Long value) {
            addCriterion("id >", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdLessThan(Long value) {
            addCriterion("id <", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdIn(List<Long> values) {
            addCriterion("id in", values, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "configId");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andConfigNameIsNull() {
            addCriterion("metric is null");
            return (Criteria) this;
        }

        public Criteria andConfigNameIsNotNull() {
            addCriterion("metric is not null");
            return (Criteria) this;
        }

        public Criteria andConfigNameEqualTo(String value) {
            addCriterion("metric =", value, "configName");
            return (Criteria) this;
        }

        public Criteria andConfigNameNotEqualTo(String value) {
            addCriterion("metric <>", value, "configName");
            return (Criteria) this;
        }

        public Criteria andConfigNameGreaterThan(String value) {
            addCriterion("metric >", value, "configName");
            return (Criteria) this;
        }

        public Criteria andConfigNameGreaterThanOrEqualTo(String value) {
            addCriterion("metric >=", value, "configName");
            return (Criteria) this;
        }

        public Criteria andConfigNameLessThan(String value) {
            addCriterion("metric <", value, "configName");
            return (Criteria) this;
        }

        public Criteria andConfigNameLessThanOrEqualTo(String value) {
            addCriterion("metric <=", value, "configName");
            return (Criteria) this;
        }

        public Criteria andConfigNameLike(String value) {
            addCriterion("metric like", value, "configName");
            return (Criteria) this;
        }

        public Criteria andConfigNameNotLike(String value) {
            addCriterion("metric not like", value, "configName");
            return (Criteria) this;
        }

        public Criteria andConfigNameIn(List<String> values) {
            addCriterion("metric in", values, "configName");
            return (Criteria) this;
        }

        public Criteria andConfigNameNotIn(List<String> values) {
            addCriterion("metric not in", values, "configName");
            return (Criteria) this;
        }

        public Criteria andConfigNameBetween(String value1, String value2) {
            addCriterion("metric between", value1, value2, "configName");
            return (Criteria) this;
        }

        public Criteria andConfigNameNotBetween(String value1, String value2) {
            addCriterion("metric not between", value1, value2, "configName");
            return (Criteria) this;
        }

        public Criteria andTsIntervalIsNull() {
            addCriterion("ts_interval is null");
            return (Criteria) this;
        }

        public Criteria andTsIntervalIsNotNull() {
            addCriterion("ts_interval is not null");
            return (Criteria) this;
        }

        public Criteria andTsIntervalEqualTo(Integer value) {
            addCriterion("ts_interval =", value, "tsInterval");
            return (Criteria) this;
        }

        public Criteria andTsIntervalNotEqualTo(Integer value) {
            addCriterion("ts_interval <>", value, "tsInterval");
            return (Criteria) this;
        }

        public Criteria andTsIntervalGreaterThan(Integer value) {
            addCriterion("ts_interval >", value, "tsInterval");
            return (Criteria) this;
        }

        public Criteria andTsIntervalGreaterThanOrEqualTo(Integer value) {
            addCriterion("ts_interval >=", value, "tsInterval");
            return (Criteria) this;
        }

        public Criteria andTsIntervalLessThan(Integer value) {
            addCriterion("ts_interval <", value, "tsInterval");
            return (Criteria) this;
        }

        public Criteria andTsIntervalLessThanOrEqualTo(Integer value) {
            addCriterion("ts_interval <=", value, "tsInterval");
            return (Criteria) this;
        }

        public Criteria andTsIntervalIn(List<Integer> values) {
            addCriterion("ts_interval in", values, "tsInterval");
            return (Criteria) this;
        }

        public Criteria andTsIntervalNotIn(List<Integer> values) {
            addCriterion("ts_interval not in", values, "tsInterval");
            return (Criteria) this;
        }

        public Criteria andTsIntervalBetween(Integer value1, Integer value2) {
            addCriterion("ts_interval between", value1, value2, "tsInterval");
            return (Criteria) this;
        }

        public Criteria andTsIntervalNotBetween(Integer value1, Integer value2) {
            addCriterion("ts_interval not between", value1, value2, "tsInterval");
            return (Criteria) this;
        }

        public Criteria andExpireIsNull() {
            addCriterion("expire is null");
            return (Criteria) this;
        }

        public Criteria andExpireIsNotNull() {
            addCriterion("expire is not null");
            return (Criteria) this;
        }

        public Criteria andExpireEqualTo(Integer value) {
            addCriterion("expire =", value, "expire");
            return (Criteria) this;
        }

        public Criteria andExpireNotEqualTo(Integer value) {
            addCriterion("expire <>", value, "expire");
            return (Criteria) this;
        }

        public Criteria andExpireGreaterThan(Integer value) {
            addCriterion("expire >", value, "expire");
            return (Criteria) this;
        }

        public Criteria andExpireGreaterThanOrEqualTo(Integer value) {
            addCriterion("expire >=", value, "expire");
            return (Criteria) this;
        }

        public Criteria andExpireLessThan(Integer value) {
            addCriterion("expire <", value, "expire");
            return (Criteria) this;
        }

        public Criteria andExpireLessThanOrEqualTo(Integer value) {
            addCriterion("expire <=", value, "expire");
            return (Criteria) this;
        }

        public Criteria andExpireIn(List<Integer> values) {
            addCriterion("expire in", values, "expire");
            return (Criteria) this;
        }

        public Criteria andExpireNotIn(List<Integer> values) {
            addCriterion("expire not in", values, "expire");
            return (Criteria) this;
        }

        public Criteria andExpireBetween(Integer value1, Integer value2) {
            addCriterion("expire between", value1, value2, "expire");
            return (Criteria) this;
        }

        public Criteria andExpireNotBetween(Integer value1, Integer value2) {
            addCriterion("expire not between", value1, value2, "expire");
            return (Criteria) this;
        }

        public Criteria andMissFillTypeIsNull() {
            addCriterion("miss_fill_type is null");
            return (Criteria) this;
        }

        public Criteria andMissFillTypeIsNotNull() {
            addCriterion("miss_fill_type is not null");
            return (Criteria) this;
        }

        public Criteria andMissFillTypeEqualTo(String value) {
            addCriterion("miss_fill_type =", value, "missFillType");
            return (Criteria) this;
        }

        public Criteria andMissFillTypeNotEqualTo(String value) {
            addCriterion("miss_fill_type <>", value, "missFillType");
            return (Criteria) this;
        }

        public Criteria andMissFillTypeGreaterThan(String value) {
            addCriterion("miss_fill_type >", value, "missFillType");
            return (Criteria) this;
        }

        public Criteria andMissFillTypeGreaterThanOrEqualTo(String value) {
            addCriterion("miss_fill_type >=", value, "missFillType");
            return (Criteria) this;
        }

        public Criteria andMissFillTypeLessThan(String value) {
            addCriterion("miss_fill_type <", value, "missFillType");
            return (Criteria) this;
        }

        public Criteria andMissFillTypeLessThanOrEqualTo(String value) {
            addCriterion("miss_fill_type <=", value, "missFillType");
            return (Criteria) this;
        }

        public Criteria andMissFillTypeLike(String value) {
            addCriterion("miss_fill_type like", value, "missFillType");
            return (Criteria) this;
        }

        public Criteria andMissFillTypeNotLike(String value) {
            addCriterion("miss_fill_type not like", value, "missFillType");
            return (Criteria) this;
        }

        public Criteria andMissFillTypeIn(List<String> values) {
            addCriterion("miss_fill_type in", values, "missFillType");
            return (Criteria) this;
        }

        public Criteria andMissFillTypeNotIn(List<String> values) {
            addCriterion("miss_fill_type not in", values, "missFillType");
            return (Criteria) this;
        }

        public Criteria andMissFillTypeBetween(String value1, String value2) {
            addCriterion("miss_fill_type between", value1, value2, "missFillType");
            return (Criteria) this;
        }

        public Criteria andMissFillTypeNotBetween(String value1, String value2) {
            addCriterion("miss_fill_type not between", value1, value2, "missFillType");
            return (Criteria) this;
        }

        public Criteria andAnomalyConfigIsNull() {
            addCriterion("anomaly_config is null");
            return (Criteria) this;
        }

        public Criteria andAnomalyConfigIsNotNull() {
            addCriterion("anomaly_config is not null");
            return (Criteria) this;
        }

        public Criteria andAnomalyConfigEqualTo(String value) {
            addCriterion("anomaly_config =", value, "anomalyConfig");
            return (Criteria) this;
        }

        public Criteria andAnomalyConfigNotEqualTo(String value) {
            addCriterion("anomaly_config <>", value, "anomalyConfig");
            return (Criteria) this;
        }

        public Criteria andAnomalyConfigGreaterThan(String value) {
            addCriterion("anomaly_config >", value, "anomalyConfig");
            return (Criteria) this;
        }

        public Criteria andAnomalyConfigGreaterThanOrEqualTo(String value) {
            addCriterion("anomaly_config >=", value, "anomalyConfig");
            return (Criteria) this;
        }

        public Criteria andAnomalyConfigLessThan(String value) {
            addCriterion("anomaly_config <", value, "anomalyConfig");
            return (Criteria) this;
        }

        public Criteria andAnomalyConfigLessThanOrEqualTo(String value) {
            addCriterion("anomaly_config <=", value, "anomalyConfig");
            return (Criteria) this;
        }

        public Criteria andAnomalyConfigLike(String value) {
            addCriterion("anomaly_config like", value, "anomalyConfig");
            return (Criteria) this;
        }

        public Criteria andAnomalyConfigNotLike(String value) {
            addCriterion("anomaly_config not like", value, "anomalyConfig");
            return (Criteria) this;
        }

        public Criteria andAnomalyConfigIn(List<String> values) {
            addCriterion("anomaly_config in", values, "anomalyConfig");
            return (Criteria) this;
        }

        public Criteria andAnomalyConfigNotIn(List<String> values) {
            addCriterion("anomaly_config not in", values, "anomalyConfig");
            return (Criteria) this;
        }

        public Criteria andAnomalyConfigBetween(String value1, String value2) {
            addCriterion("anomaly_config between", value1, value2, "anomalyConfig");
            return (Criteria) this;
        }

        public Criteria andAnomalyConfigNotBetween(String value1, String value2) {
            addCriterion("anomaly_config not between", value1, value2, "anomalyConfig");
            return (Criteria) this;
        }

        public Criteria andMatchingRuleIsNull() {
            addCriterion("matching_rule is null");
            return (Criteria) this;
        }

        public Criteria andMatchingRuleIsNotNull() {
            addCriterion("matching_rule is not null");
            return (Criteria) this;
        }

        public Criteria andMatchingRuleEqualTo(String value) {
            addCriterion("matching_rule =", value, "matchingRule");
            return (Criteria) this;
        }

        public Criteria andMatchingRuleNotEqualTo(String value) {
            addCriterion("matching_rule <>", value, "matchingRule");
            return (Criteria) this;
        }

        public Criteria andMatchingRuleGreaterThan(String value) {
            addCriterion("matching_rule >", value, "matchingRule");
            return (Criteria) this;
        }

        public Criteria andMatchingRuleGreaterThanOrEqualTo(String value) {
            addCriterion("matching_rule >=", value, "matchingRule");
            return (Criteria) this;
        }

        public Criteria andMatchingRuleLessThan(String value) {
            addCriterion("matching_rule <", value, "matchingRule");
            return (Criteria) this;
        }

        public Criteria andMatchingRuleLessThanOrEqualTo(String value) {
            addCriterion("matching_rule <=", value, "matchingRule");
            return (Criteria) this;
        }

        public Criteria andMatchingRuleLike(String value) {
            addCriterion("matching_rule like", value, "matchingRule");
            return (Criteria) this;
        }

        public Criteria andMatchingRuleNotLike(String value) {
            addCriterion("matching_rule not like", value, "matchingRule");
            return (Criteria) this;
        }

        public Criteria andMatchingRuleIn(List<String> values) {
            addCriterion("matching_rule in", values, "matchingRule");
            return (Criteria) this;
        }

        public Criteria andMatchingRuleNotIn(List<String> values) {
            addCriterion("matching_rule not in", values, "matchingRule");
            return (Criteria) this;
        }

        public Criteria andMatchingRuleBetween(String value1, String value2) {
            addCriterion("matching_rule between", value1, value2, "matchingRule");
            return (Criteria) this;
        }

        public Criteria andMatchingRuleNotBetween(String value1, String value2) {
            addCriterion("matching_rule not between", value1, value2, "matchingRule");
            return (Criteria) this;
        }

        public Criteria andSmoothWinSizeIsNull() {
            addCriterion("smooth_win_size is null");
            return (Criteria) this;
        }

        public Criteria andSmoothWinSizeIsNotNull() {
            addCriterion("smooth_win_size is not null");
            return (Criteria) this;
        }

        public Criteria andSmoothWinSizeEqualTo(Integer value) {
            addCriterion("smooth_win_size =", value, "smoothWinSize");
            return (Criteria) this;
        }

        public Criteria andSmoothWinSizeNotEqualTo(Integer value) {
            addCriterion("smooth_win_size <>", value, "smoothWinSize");
            return (Criteria) this;
        }

        public Criteria andSmoothWinSizeGreaterThan(Integer value) {
            addCriterion("smooth_win_size >", value, "smoothWinSize");
            return (Criteria) this;
        }

        public Criteria andSmoothWinSizeGreaterThanOrEqualTo(Integer value) {
            addCriterion("smooth_win_size >=", value, "smoothWinSize");
            return (Criteria) this;
        }

        public Criteria andSmoothWinSizeLessThan(Integer value) {
            addCriterion("smooth_win_size <", value, "smoothWinSize");
            return (Criteria) this;
        }

        public Criteria andSmoothWinSizeLessThanOrEqualTo(Integer value) {
            addCriterion("smooth_win_size <=", value, "smoothWinSize");
            return (Criteria) this;
        }

        public Criteria andSmoothWinSizeIn(List<Integer> values) {
            addCriterion("smooth_win_size in", values, "smoothWinSize");
            return (Criteria) this;
        }

        public Criteria andSmoothWinSizeNotIn(List<Integer> values) {
            addCriterion("smooth_win_size not in", values, "smoothWinSize");
            return (Criteria) this;
        }

        public Criteria andSmoothWinSizeBetween(Integer value1, Integer value2) {
            addCriterion("smooth_win_size between", value1, value2, "smoothWinSize");
            return (Criteria) this;
        }

        public Criteria andSmoothWinSizeNotBetween(Integer value1, Integer value2) {
            addCriterion("smooth_win_size not between", value1, value2, "smoothWinSize");
            return (Criteria) this;
        }

        public Criteria andPeriodLengthIsNull() {
            addCriterion("period_length is null");
            return (Criteria) this;
        }

        public Criteria andPeriodLengthIsNotNull() {
            addCriterion("period_length is not null");
            return (Criteria) this;
        }

        public Criteria andPeriodLengthEqualTo(Integer value) {
            addCriterion("period_length =", value, "periodLength");
            return (Criteria) this;
        }

        public Criteria andPeriodLengthNotEqualTo(Integer value) {
            addCriterion("period_length <>", value, "periodLength");
            return (Criteria) this;
        }

        public Criteria andPeriodLengthGreaterThan(Integer value) {
            addCriterion("period_length >", value, "periodLength");
            return (Criteria) this;
        }

        public Criteria andPeriodLengthGreaterThanOrEqualTo(Integer value) {
            addCriterion("period_length >=", value, "periodLength");
            return (Criteria) this;
        }

        public Criteria andPeriodLengthLessThan(Integer value) {
            addCriterion("period_length <", value, "periodLength");
            return (Criteria) this;
        }

        public Criteria andPeriodLengthLessThanOrEqualTo(Integer value) {
            addCriterion("period_length <=", value, "periodLength");
            return (Criteria) this;
        }

        public Criteria andPeriodLengthIn(List<Integer> values) {
            addCriterion("period_length in", values, "periodLength");
            return (Criteria) this;
        }

        public Criteria andPeriodLengthNotIn(List<Integer> values) {
            addCriterion("period_length not in", values, "periodLength");
            return (Criteria) this;
        }

        public Criteria andPeriodLengthBetween(Integer value1, Integer value2) {
            addCriterion("period_length between", value1, value2, "periodLength");
            return (Criteria) this;
        }

        public Criteria andPeriodLengthNotBetween(Integer value1, Integer value2) {
            addCriterion("period_length not between", value1, value2, "periodLength");
            return (Criteria) this;
        }

        public Criteria andDetectTypeIsNull() {
            addCriterion("detect_type is null");
            return (Criteria) this;
        }

        public Criteria andDetectTypeIsNotNull() {
            addCriterion("detect_type is not null");
            return (Criteria) this;
        }

        public Criteria andDetectTypeEqualTo(String value) {
            addCriterion("detect_type =", value, "detectType");
            return (Criteria) this;
        }

        public Criteria andDetectTypeNotEqualTo(String value) {
            addCriterion("detect_type <>", value, "detectType");
            return (Criteria) this;
        }

        public Criteria andDetectTypeGreaterThan(String value) {
            addCriterion("detect_type >", value, "detectType");
            return (Criteria) this;
        }

        public Criteria andDetectTypeGreaterThanOrEqualTo(String value) {
            addCriterion("detect_type >=", value, "detectType");
            return (Criteria) this;
        }

        public Criteria andDetectTypeLessThan(String value) {
            addCriterion("detect_type <", value, "detectType");
            return (Criteria) this;
        }

        public Criteria andDetectTypeLessThanOrEqualTo(String value) {
            addCriterion("detect_type <=", value, "detectType");
            return (Criteria) this;
        }

        public Criteria andDetectTypeLike(String value) {
            addCriterion("detect_type like", value, "detectType");
            return (Criteria) this;
        }

        public Criteria andDetectTypeNotLike(String value) {
            addCriterion("detect_type not like", value, "detectType");
            return (Criteria) this;
        }

        public Criteria andDetectTypeIn(List<String> values) {
            addCriterion("detect_type in", values, "detectType");
            return (Criteria) this;
        }

        public Criteria andDetectTypeNotIn(List<String> values) {
            addCriterion("detect_type not in", values, "detectType");
            return (Criteria) this;
        }

        public Criteria andDetectTypeBetween(String value1, String value2) {
            addCriterion("detect_type between", value1, value2, "detectType");
            return (Criteria) this;
        }

        public Criteria andDetectTypeNotBetween(String value1, String value2) {
            addCriterion("detect_type not between", value1, value2, "detectType");
            return (Criteria) this;
        }

        public Criteria andSourceIdsIsNull() {
            addCriterion("source_ids is null");
            return (Criteria) this;
        }

        public Criteria andSourceIdsIsNotNull() {
            addCriterion("source_ids is not null");
            return (Criteria) this;
        }

        public Criteria andSourceIdsEqualTo(String value) {
            addCriterion("source_ids =", value, "sourceIds");
            return (Criteria) this;
        }

        public Criteria andSourceIdsNotEqualTo(String value) {
            addCriterion("source_ids <>", value, "sourceIds");
            return (Criteria) this;
        }

        public Criteria andSourceIdsGreaterThan(String value) {
            addCriterion("source_ids >", value, "sourceIds");
            return (Criteria) this;
        }

        public Criteria andSourceIdsGreaterThanOrEqualTo(String value) {
            addCriterion("source_ids >=", value, "sourceIds");
            return (Criteria) this;
        }

        public Criteria andSourceIdsLessThan(String value) {
            addCriterion("source_ids <", value, "sourceIds");
            return (Criteria) this;
        }

        public Criteria andSourceIdsLessThanOrEqualTo(String value) {
            addCriterion("source_ids <=", value, "sourceIds");
            return (Criteria) this;
        }

        public Criteria andSourceIdsLike(String value) {
            addCriterion("source_ids like", value, "sourceIds");
            return (Criteria) this;
        }

        public Criteria andSourceIdsNotLike(String value) {
            addCriterion("source_ids not like", value, "sourceIds");
            return (Criteria) this;
        }

        public Criteria andSourceIdsIn(List<String> values) {
            addCriterion("source_ids in", values, "sourceIds");
            return (Criteria) this;
        }

        public Criteria andSourceIdsNotIn(List<String> values) {
            addCriterion("source_ids not in", values, "sourceIds");
            return (Criteria) this;
        }

        public Criteria andSourceIdsBetween(String value1, String value2) {
            addCriterion("source_ids between", value1, value2, "sourceIds");
            return (Criteria) this;
        }

        public Criteria andSourceIdsNotBetween(String value1, String value2) {
            addCriterion("source_ids not between", value1, value2, "sourceIds");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table metric_anomaly
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}