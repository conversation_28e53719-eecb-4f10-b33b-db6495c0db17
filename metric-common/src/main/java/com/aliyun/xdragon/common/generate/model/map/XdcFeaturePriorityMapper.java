package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.XdcFeaturePriority;
import com.aliyun.xdragon.common.generate.model.XdcFeaturePriorityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface XdcFeaturePriorityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table xdc_feature_priority
     *
     * @mbg.generated
     */
    long countByExample(XdcFeaturePriorityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table xdc_feature_priority
     *
     * @mbg.generated
     */
    int deleteByExample(XdcFeaturePriorityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table xdc_feature_priority
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table xdc_feature_priority
     *
     * @mbg.generated
     */
    int insert(XdcFeaturePriority record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table xdc_feature_priority
     *
     * @mbg.generated
     */
    int insertSelective(XdcFeaturePriority record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table xdc_feature_priority
     *
     * @mbg.generated
     */
    List<XdcFeaturePriority> selectByExampleWithRowbounds(XdcFeaturePriorityExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table xdc_feature_priority
     *
     * @mbg.generated
     */
    List<XdcFeaturePriority> selectByExample(XdcFeaturePriorityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table xdc_feature_priority
     *
     * @mbg.generated
     */
    XdcFeaturePriority selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table xdc_feature_priority
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") XdcFeaturePriority record, @Param("example") XdcFeaturePriorityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table xdc_feature_priority
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") XdcFeaturePriority record, @Param("example") XdcFeaturePriorityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table xdc_feature_priority
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(XdcFeaturePriority record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table xdc_feature_priority
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(XdcFeaturePriority record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table xdc_feature_priority
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<XdcFeaturePriority> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table xdc_feature_priority
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<XdcFeaturePriority> list, @Param("selective") XdcFeaturePriority.Column ... selective);
}