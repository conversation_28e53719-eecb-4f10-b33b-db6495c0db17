package com.aliyun.xdragon.common.generate.model;

import java.util.ArrayList;
import java.util.List;

public class EvaluateMonitorPercentileExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    public EvaluateMonitorPercentileExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBatchIdIsNull() {
            addCriterion("batch_id is null");
            return (Criteria) this;
        }

        public Criteria andBatchIdIsNotNull() {
            addCriterion("batch_id is not null");
            return (Criteria) this;
        }

        public Criteria andBatchIdEqualTo(Long value) {
            addCriterion("batch_id =", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdNotEqualTo(Long value) {
            addCriterion("batch_id <>", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdGreaterThan(Long value) {
            addCriterion("batch_id >", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdGreaterThanOrEqualTo(Long value) {
            addCriterion("batch_id >=", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdLessThan(Long value) {
            addCriterion("batch_id <", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdLessThanOrEqualTo(Long value) {
            addCriterion("batch_id <=", value, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdIn(List<Long> values) {
            addCriterion("batch_id in", values, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdNotIn(List<Long> values) {
            addCriterion("batch_id not in", values, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdBetween(Long value1, Long value2) {
            addCriterion("batch_id between", value1, value2, "batchId");
            return (Criteria) this;
        }

        public Criteria andBatchIdNotBetween(Long value1, Long value2) {
            addCriterion("batch_id not between", value1, value2, "batchId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andMetricNameIsNull() {
            addCriterion("metric_name is null");
            return (Criteria) this;
        }

        public Criteria andMetricNameIsNotNull() {
            addCriterion("metric_name is not null");
            return (Criteria) this;
        }

        public Criteria andMetricNameEqualTo(String value) {
            addCriterion("metric_name =", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameNotEqualTo(String value) {
            addCriterion("metric_name <>", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameGreaterThan(String value) {
            addCriterion("metric_name >", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameGreaterThanOrEqualTo(String value) {
            addCriterion("metric_name >=", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameLessThan(String value) {
            addCriterion("metric_name <", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameLessThanOrEqualTo(String value) {
            addCriterion("metric_name <=", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameLike(String value) {
            addCriterion("metric_name like", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameNotLike(String value) {
            addCriterion("metric_name not like", value, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameIn(List<String> values) {
            addCriterion("metric_name in", values, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameNotIn(List<String> values) {
            addCriterion("metric_name not in", values, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameBetween(String value1, String value2) {
            addCriterion("metric_name between", value1, value2, "metricName");
            return (Criteria) this;
        }

        public Criteria andMetricNameNotBetween(String value1, String value2) {
            addCriterion("metric_name not between", value1, value2, "metricName");
            return (Criteria) this;
        }

        public Criteria andActionNameIsNull() {
            addCriterion("action_name is null");
            return (Criteria) this;
        }

        public Criteria andActionNameIsNotNull() {
            addCriterion("action_name is not null");
            return (Criteria) this;
        }

        public Criteria andActionNameEqualTo(String value) {
            addCriterion("action_name =", value, "actionName");
            return (Criteria) this;
        }

        public Criteria andActionNameNotEqualTo(String value) {
            addCriterion("action_name <>", value, "actionName");
            return (Criteria) this;
        }

        public Criteria andActionNameGreaterThan(String value) {
            addCriterion("action_name >", value, "actionName");
            return (Criteria) this;
        }

        public Criteria andActionNameGreaterThanOrEqualTo(String value) {
            addCriterion("action_name >=", value, "actionName");
            return (Criteria) this;
        }

        public Criteria andActionNameLessThan(String value) {
            addCriterion("action_name <", value, "actionName");
            return (Criteria) this;
        }

        public Criteria andActionNameLessThanOrEqualTo(String value) {
            addCriterion("action_name <=", value, "actionName");
            return (Criteria) this;
        }

        public Criteria andActionNameLike(String value) {
            addCriterion("action_name like", value, "actionName");
            return (Criteria) this;
        }

        public Criteria andActionNameNotLike(String value) {
            addCriterion("action_name not like", value, "actionName");
            return (Criteria) this;
        }

        public Criteria andActionNameIn(List<String> values) {
            addCriterion("action_name in", values, "actionName");
            return (Criteria) this;
        }

        public Criteria andActionNameNotIn(List<String> values) {
            addCriterion("action_name not in", values, "actionName");
            return (Criteria) this;
        }

        public Criteria andActionNameBetween(String value1, String value2) {
            addCriterion("action_name between", value1, value2, "actionName");
            return (Criteria) this;
        }

        public Criteria andActionNameNotBetween(String value1, String value2) {
            addCriterion("action_name not between", value1, value2, "actionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameIsNull() {
            addCriterion("exception_name is null");
            return (Criteria) this;
        }

        public Criteria andExceptionNameIsNotNull() {
            addCriterion("exception_name is not null");
            return (Criteria) this;
        }

        public Criteria andExceptionNameEqualTo(String value) {
            addCriterion("exception_name =", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameNotEqualTo(String value) {
            addCriterion("exception_name <>", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameGreaterThan(String value) {
            addCriterion("exception_name >", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameGreaterThanOrEqualTo(String value) {
            addCriterion("exception_name >=", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameLessThan(String value) {
            addCriterion("exception_name <", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameLessThanOrEqualTo(String value) {
            addCriterion("exception_name <=", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameLike(String value) {
            addCriterion("exception_name like", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameNotLike(String value) {
            addCriterion("exception_name not like", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameIn(List<String> values) {
            addCriterion("exception_name in", values, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameNotIn(List<String> values) {
            addCriterion("exception_name not in", values, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameBetween(String value1, String value2) {
            addCriterion("exception_name between", value1, value2, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameNotBetween(String value1, String value2) {
            addCriterion("exception_name not between", value1, value2, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andPercentileIsNull() {
            addCriterion("percentile is null");
            return (Criteria) this;
        }

        public Criteria andPercentileIsNotNull() {
            addCriterion("percentile is not null");
            return (Criteria) this;
        }

        public Criteria andPercentileEqualTo(Double value) {
            addCriterion("percentile =", value, "percentile");
            return (Criteria) this;
        }

        public Criteria andPercentileNotEqualTo(Double value) {
            addCriterion("percentile <>", value, "percentile");
            return (Criteria) this;
        }

        public Criteria andPercentileGreaterThan(Double value) {
            addCriterion("percentile >", value, "percentile");
            return (Criteria) this;
        }

        public Criteria andPercentileGreaterThanOrEqualTo(Double value) {
            addCriterion("percentile >=", value, "percentile");
            return (Criteria) this;
        }

        public Criteria andPercentileLessThan(Double value) {
            addCriterion("percentile <", value, "percentile");
            return (Criteria) this;
        }

        public Criteria andPercentileLessThanOrEqualTo(Double value) {
            addCriterion("percentile <=", value, "percentile");
            return (Criteria) this;
        }

        public Criteria andPercentileIn(List<Double> values) {
            addCriterion("percentile in", values, "percentile");
            return (Criteria) this;
        }

        public Criteria andPercentileNotIn(List<Double> values) {
            addCriterion("percentile not in", values, "percentile");
            return (Criteria) this;
        }

        public Criteria andPercentileBetween(Double value1, Double value2) {
            addCriterion("percentile between", value1, value2, "percentile");
            return (Criteria) this;
        }

        public Criteria andPercentileNotBetween(Double value1, Double value2) {
            addCriterion("percentile not between", value1, value2, "percentile");
            return (Criteria) this;
        }

        public Criteria andDurationIsNull() {
            addCriterion("duration is null");
            return (Criteria) this;
        }

        public Criteria andDurationIsNotNull() {
            addCriterion("duration is not null");
            return (Criteria) this;
        }

        public Criteria andDurationEqualTo(Double value) {
            addCriterion("duration =", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotEqualTo(Double value) {
            addCriterion("duration <>", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThan(Double value) {
            addCriterion("duration >", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThanOrEqualTo(Double value) {
            addCriterion("duration >=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThan(Double value) {
            addCriterion("duration <", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThanOrEqualTo(Double value) {
            addCriterion("duration <=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationIn(List<Double> values) {
            addCriterion("duration in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotIn(List<Double> values) {
            addCriterion("duration not in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationBetween(Double value1, Double value2) {
            addCriterion("duration between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotBetween(Double value1, Double value2) {
            addCriterion("duration not between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andTotalDurationIsNull() {
            addCriterion("total_duration is null");
            return (Criteria) this;
        }

        public Criteria andTotalDurationIsNotNull() {
            addCriterion("total_duration is not null");
            return (Criteria) this;
        }

        public Criteria andTotalDurationEqualTo(Double value) {
            addCriterion("total_duration =", value, "totalDuration");
            return (Criteria) this;
        }

        public Criteria andTotalDurationNotEqualTo(Double value) {
            addCriterion("total_duration <>", value, "totalDuration");
            return (Criteria) this;
        }

        public Criteria andTotalDurationGreaterThan(Double value) {
            addCriterion("total_duration >", value, "totalDuration");
            return (Criteria) this;
        }

        public Criteria andTotalDurationGreaterThanOrEqualTo(Double value) {
            addCriterion("total_duration >=", value, "totalDuration");
            return (Criteria) this;
        }

        public Criteria andTotalDurationLessThan(Double value) {
            addCriterion("total_duration <", value, "totalDuration");
            return (Criteria) this;
        }

        public Criteria andTotalDurationLessThanOrEqualTo(Double value) {
            addCriterion("total_duration <=", value, "totalDuration");
            return (Criteria) this;
        }

        public Criteria andTotalDurationIn(List<Double> values) {
            addCriterion("total_duration in", values, "totalDuration");
            return (Criteria) this;
        }

        public Criteria andTotalDurationNotIn(List<Double> values) {
            addCriterion("total_duration not in", values, "totalDuration");
            return (Criteria) this;
        }

        public Criteria andTotalDurationBetween(Double value1, Double value2) {
            addCriterion("total_duration between", value1, value2, "totalDuration");
            return (Criteria) this;
        }

        public Criteria andTotalDurationNotBetween(Double value1, Double value2) {
            addCriterion("total_duration not between", value1, value2, "totalDuration");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table evaluate_monitor_percentile
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}