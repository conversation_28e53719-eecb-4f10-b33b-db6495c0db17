<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.model.map.MetricSourceMapper">
  <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.model.MetricSource">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="source_name" jdbcType="VARCHAR" property="sourceName" />
    <result column="is_period" jdbcType="BIT" property="isPeriod" />
    <result column="source_logstore" jdbcType="VARCHAR" property="sourceLogstore" />
    <result column="metric_anomaly_id" jdbcType="BIGINT" property="metricAnomalyId" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="comments" jdbcType="VARCHAR" property="comments" />
    <result column="metric_count" jdbcType="INTEGER" property="metricCount" />
    <result column="anomaly_check" jdbcType="BIT" property="anomalyCheck" />
    <result column="lost_check" jdbcType="BIT" property="lostCheck" />
    <result column="ts_interval" jdbcType="INTEGER" property="tsInterval" />
    <result column="gmt_latest_date" jdbcType="TIMESTAMP" property="gmtLatestDate" />
    <result column="source_type" jdbcType="VARCHAR" property="sourceType" />
    <result column="sls_config" jdbcType="CHAR" property="slsConfig" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    source_id, gmt_create, gmt_modified, source_name, is_period, source_logstore, metric_anomaly_id, 
    `owner`, is_deleted, comments, metric_count, anomaly_check, lost_check, ts_interval, 
    gmt_latest_date, source_type, sls_config
  </sql>
  <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.model.MetricSourceExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from metric_source
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="example != null and example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from metric_source
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from metric_source
    where source_id = #{sourceId,jdbcType=BIGINT}
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from metric_source
    where source_id = #{sourceId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from metric_source
    where source_id = #{sourceId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.model.MetricSourceExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from metric_source
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.model.MetricSource">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="sourceId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into metric_source (gmt_create, gmt_modified, source_name, 
      is_period, source_logstore, metric_anomaly_id, 
      `owner`, is_deleted, comments, 
      metric_count, anomaly_check, lost_check, 
      ts_interval, gmt_latest_date, source_type, 
      sls_config)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{sourceName,jdbcType=VARCHAR}, 
      #{isPeriod,jdbcType=BIT}, #{sourceLogstore,jdbcType=VARCHAR}, #{metricAnomalyId,jdbcType=BIGINT}, 
      #{owner,jdbcType=VARCHAR}, #{isDeleted,jdbcType=BIT}, #{comments,jdbcType=VARCHAR}, 
      #{metricCount,jdbcType=INTEGER}, #{anomalyCheck,jdbcType=BIT}, #{lostCheck,jdbcType=BIT}, 
      #{tsInterval,jdbcType=INTEGER}, #{gmtLatestDate,jdbcType=TIMESTAMP}, #{sourceType,jdbcType=VARCHAR}, 
      #{slsConfig,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.model.MetricSource">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="sourceId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into metric_source
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="sourceName != null">
        source_name,
      </if>
      <if test="isPeriod != null">
        is_period,
      </if>
      <if test="sourceLogstore != null">
        source_logstore,
      </if>
      <if test="metricAnomalyId != null">
        metric_anomaly_id,
      </if>
      <if test="owner != null">
        `owner`,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="comments != null">
        comments,
      </if>
      <if test="metricCount != null">
        metric_count,
      </if>
      <if test="anomalyCheck != null">
        anomaly_check,
      </if>
      <if test="lostCheck != null">
        lost_check,
      </if>
      <if test="tsInterval != null">
        ts_interval,
      </if>
      <if test="gmtLatestDate != null">
        gmt_latest_date,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="slsConfig != null">
        sls_config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceName != null">
        #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="isPeriod != null">
        #{isPeriod,jdbcType=BIT},
      </if>
      <if test="sourceLogstore != null">
        #{sourceLogstore,jdbcType=VARCHAR},
      </if>
      <if test="metricAnomalyId != null">
        #{metricAnomalyId,jdbcType=BIGINT},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="metricCount != null">
        #{metricCount,jdbcType=INTEGER},
      </if>
      <if test="anomalyCheck != null">
        #{anomalyCheck,jdbcType=BIT},
      </if>
      <if test="lostCheck != null">
        #{lostCheck,jdbcType=BIT},
      </if>
      <if test="tsInterval != null">
        #{tsInterval,jdbcType=INTEGER},
      </if>
      <if test="gmtLatestDate != null">
        #{gmtLatestDate,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="slsConfig != null">
        #{slsConfig,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.model.MetricSourceExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from metric_source
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update metric_source
    <set>
      <if test="record.sourceId != null">
        source_id = #{record.sourceId,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sourceName != null">
        source_name = #{record.sourceName,jdbcType=VARCHAR},
      </if>
      <if test="record.isPeriod != null">
        is_period = #{record.isPeriod,jdbcType=BIT},
      </if>
      <if test="record.sourceLogstore != null">
        source_logstore = #{record.sourceLogstore,jdbcType=VARCHAR},
      </if>
      <if test="record.metricAnomalyId != null">
        metric_anomaly_id = #{record.metricAnomalyId,jdbcType=BIGINT},
      </if>
      <if test="record.owner != null">
        `owner` = #{record.owner,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.comments != null">
        comments = #{record.comments,jdbcType=VARCHAR},
      </if>
      <if test="record.metricCount != null">
        metric_count = #{record.metricCount,jdbcType=INTEGER},
      </if>
      <if test="record.anomalyCheck != null">
        anomaly_check = #{record.anomalyCheck,jdbcType=BIT},
      </if>
      <if test="record.lostCheck != null">
        lost_check = #{record.lostCheck,jdbcType=BIT},
      </if>
      <if test="record.tsInterval != null">
        ts_interval = #{record.tsInterval,jdbcType=INTEGER},
      </if>
      <if test="record.gmtLatestDate != null">
        gmt_latest_date = #{record.gmtLatestDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sourceType != null">
        source_type = #{record.sourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.slsConfig != null">
        sls_config = #{record.slsConfig,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update metric_source
    set source_id = #{record.sourceId,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      source_name = #{record.sourceName,jdbcType=VARCHAR},
      is_period = #{record.isPeriod,jdbcType=BIT},
      source_logstore = #{record.sourceLogstore,jdbcType=VARCHAR},
      metric_anomaly_id = #{record.metricAnomalyId,jdbcType=BIGINT},
      `owner` = #{record.owner,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      comments = #{record.comments,jdbcType=VARCHAR},
      metric_count = #{record.metricCount,jdbcType=INTEGER},
      anomaly_check = #{record.anomalyCheck,jdbcType=BIT},
      lost_check = #{record.lostCheck,jdbcType=BIT},
      ts_interval = #{record.tsInterval,jdbcType=INTEGER},
      gmt_latest_date = #{record.gmtLatestDate,jdbcType=TIMESTAMP},
      source_type = #{record.sourceType,jdbcType=VARCHAR},
      sls_config = #{record.slsConfig,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.xdragon.common.generate.model.MetricSource">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update metric_source
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceName != null">
        source_name = #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="isPeriod != null">
        is_period = #{isPeriod,jdbcType=BIT},
      </if>
      <if test="sourceLogstore != null">
        source_logstore = #{sourceLogstore,jdbcType=VARCHAR},
      </if>
      <if test="metricAnomalyId != null">
        metric_anomaly_id = #{metricAnomalyId,jdbcType=BIGINT},
      </if>
      <if test="owner != null">
        `owner` = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="comments != null">
        comments = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="metricCount != null">
        metric_count = #{metricCount,jdbcType=INTEGER},
      </if>
      <if test="anomalyCheck != null">
        anomaly_check = #{anomalyCheck,jdbcType=BIT},
      </if>
      <if test="lostCheck != null">
        lost_check = #{lostCheck,jdbcType=BIT},
      </if>
      <if test="tsInterval != null">
        ts_interval = #{tsInterval,jdbcType=INTEGER},
      </if>
      <if test="gmtLatestDate != null">
        gmt_latest_date = #{gmtLatestDate,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="slsConfig != null">
        sls_config = #{slsConfig,jdbcType=CHAR},
      </if>
    </set>
    where source_id = #{sourceId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.model.MetricSource">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update metric_source
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      source_name = #{sourceName,jdbcType=VARCHAR},
      is_period = #{isPeriod,jdbcType=BIT},
      source_logstore = #{sourceLogstore,jdbcType=VARCHAR},
      metric_anomaly_id = #{metricAnomalyId,jdbcType=BIGINT},
      `owner` = #{owner,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIT},
      comments = #{comments,jdbcType=VARCHAR},
      metric_count = #{metricCount,jdbcType=INTEGER},
      anomaly_check = #{anomalyCheck,jdbcType=BIT},
      lost_check = #{lostCheck,jdbcType=BIT},
      ts_interval = #{tsInterval,jdbcType=INTEGER},
      gmt_latest_date = #{gmtLatestDate,jdbcType=TIMESTAMP},
      source_type = #{sourceType,jdbcType=VARCHAR},
      sls_config = #{slsConfig,jdbcType=CHAR}
    where source_id = #{sourceId,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.model.MetricSourceExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from metric_source
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert" keyColumn="source_id" keyProperty="sourceId" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into metric_source
    (gmt_create, gmt_modified, source_name, is_period, source_logstore, metric_anomaly_id, 
      `owner`, is_deleted, comments, metric_count, anomaly_check, lost_check, ts_interval, 
      gmt_latest_date, source_type, sls_config)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.sourceName,jdbcType=VARCHAR}, 
        #{item.isPeriod,jdbcType=BIT}, #{item.sourceLogstore,jdbcType=VARCHAR}, #{item.metricAnomalyId,jdbcType=BIGINT}, 
        #{item.owner,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=BIT}, #{item.comments,jdbcType=VARCHAR}, 
        #{item.metricCount,jdbcType=INTEGER}, #{item.anomalyCheck,jdbcType=BIT}, #{item.lostCheck,jdbcType=BIT}, 
        #{item.tsInterval,jdbcType=INTEGER}, #{item.gmtLatestDate,jdbcType=TIMESTAMP}, 
        #{item.sourceType,jdbcType=VARCHAR}, #{item.slsConfig,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="source_id" keyProperty="list.sourceId" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into metric_source (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'gmt_create'.toString() == column.value">
          #{item.gmtCreate,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'source_name'.toString() == column.value">
          #{item.sourceName,jdbcType=VARCHAR}
        </if>
        <if test="'is_period'.toString() == column.value">
          #{item.isPeriod,jdbcType=BIT}
        </if>
        <if test="'source_logstore'.toString() == column.value">
          #{item.sourceLogstore,jdbcType=VARCHAR}
        </if>
        <if test="'metric_anomaly_id'.toString() == column.value">
          #{item.metricAnomalyId,jdbcType=BIGINT}
        </if>
        <if test="'owner'.toString() == column.value">
          #{item.owner,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=BIT}
        </if>
        <if test="'comments'.toString() == column.value">
          #{item.comments,jdbcType=VARCHAR}
        </if>
        <if test="'metric_count'.toString() == column.value">
          #{item.metricCount,jdbcType=INTEGER}
        </if>
        <if test="'anomaly_check'.toString() == column.value">
          #{item.anomalyCheck,jdbcType=BIT}
        </if>
        <if test="'lost_check'.toString() == column.value">
          #{item.lostCheck,jdbcType=BIT}
        </if>
        <if test="'ts_interval'.toString() == column.value">
          #{item.tsInterval,jdbcType=INTEGER}
        </if>
        <if test="'gmt_latest_date'.toString() == column.value">
          #{item.gmtLatestDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'source_type'.toString() == column.value">
          #{item.sourceType,jdbcType=VARCHAR}
        </if>
        <if test="'sls_config'.toString() == column.value">
          #{item.slsConfig,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
  <insert id="upsertSelective" keyColumn="source_id" keyProperty="sourceId" parameterType="com.aliyun.xdragon.common.generate.model.MetricSource" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into metric_source
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="sourceName != null">
        source_name,
      </if>
      <if test="isPeriod != null">
        is_period,
      </if>
      <if test="sourceLogstore != null">
        source_logstore,
      </if>
      <if test="metricAnomalyId != null">
        metric_anomaly_id,
      </if>
      <if test="owner != null">
        `owner`,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="comments != null">
        comments,
      </if>
      <if test="metricCount != null">
        metric_count,
      </if>
      <if test="anomalyCheck != null">
        anomaly_check,
      </if>
      <if test="lostCheck != null">
        lost_check,
      </if>
      <if test="tsInterval != null">
        ts_interval,
      </if>
      <if test="gmtLatestDate != null">
        gmt_latest_date,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="slsConfig != null">
        sls_config,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sourceId != null">
        #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceName != null">
        #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="isPeriod != null">
        #{isPeriod,jdbcType=BIT},
      </if>
      <if test="sourceLogstore != null">
        #{sourceLogstore,jdbcType=VARCHAR},
      </if>
      <if test="metricAnomalyId != null">
        #{metricAnomalyId,jdbcType=BIGINT},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="metricCount != null">
        #{metricCount,jdbcType=INTEGER},
      </if>
      <if test="anomalyCheck != null">
        #{anomalyCheck,jdbcType=BIT},
      </if>
      <if test="lostCheck != null">
        #{lostCheck,jdbcType=BIT},
      </if>
      <if test="tsInterval != null">
        #{tsInterval,jdbcType=INTEGER},
      </if>
      <if test="gmtLatestDate != null">
        #{gmtLatestDate,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="slsConfig != null">
        #{slsConfig,jdbcType=CHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceName != null">
        source_name = #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="isPeriod != null">
        is_period = #{isPeriod,jdbcType=BIT},
      </if>
      <if test="sourceLogstore != null">
        source_logstore = #{sourceLogstore,jdbcType=VARCHAR},
      </if>
      <if test="metricAnomalyId != null">
        metric_anomaly_id = #{metricAnomalyId,jdbcType=BIGINT},
      </if>
      <if test="owner != null">
        `owner` = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="comments != null">
        comments = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="metricCount != null">
        metric_count = #{metricCount,jdbcType=INTEGER},
      </if>
      <if test="anomalyCheck != null">
        anomaly_check = #{anomalyCheck,jdbcType=BIT},
      </if>
      <if test="lostCheck != null">
        lost_check = #{lostCheck,jdbcType=BIT},
      </if>
      <if test="tsInterval != null">
        ts_interval = #{tsInterval,jdbcType=INTEGER},
      </if>
      <if test="gmtLatestDate != null">
        gmt_latest_date = #{gmtLatestDate,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="slsConfig != null">
        sls_config = #{slsConfig,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <insert id="upsert" keyColumn="source_id" keyProperty="sourceId" parameterType="com.aliyun.xdragon.common.generate.model.MetricSource" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into metric_source
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sourceId != null">
        source_id,
      </if>
      gmt_create,
      gmt_modified,
      source_name,
      is_period,
      source_logstore,
      metric_anomaly_id,
      `owner`,
      is_deleted,
      comments,
      metric_count,
      anomaly_check,
      lost_check,
      ts_interval,
      gmt_latest_date,
      source_type,
      sls_config,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sourceId != null">
        #{sourceId,jdbcType=BIGINT},
      </if>
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{sourceName,jdbcType=VARCHAR},
      #{isPeriod,jdbcType=BIT},
      #{sourceLogstore,jdbcType=VARCHAR},
      #{metricAnomalyId,jdbcType=BIGINT},
      #{owner,jdbcType=VARCHAR},
      #{isDeleted,jdbcType=BIT},
      #{comments,jdbcType=VARCHAR},
      #{metricCount,jdbcType=INTEGER},
      #{anomalyCheck,jdbcType=BIT},
      #{lostCheck,jdbcType=BIT},
      #{tsInterval,jdbcType=INTEGER},
      #{gmtLatestDate,jdbcType=TIMESTAMP},
      #{sourceType,jdbcType=VARCHAR},
      #{slsConfig,jdbcType=CHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=BIGINT},
      </if>
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      source_name = #{sourceName,jdbcType=VARCHAR},
      is_period = #{isPeriod,jdbcType=BIT},
      source_logstore = #{sourceLogstore,jdbcType=VARCHAR},
      metric_anomaly_id = #{metricAnomalyId,jdbcType=BIGINT},
      `owner` = #{owner,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIT},
      comments = #{comments,jdbcType=VARCHAR},
      metric_count = #{metricCount,jdbcType=INTEGER},
      anomaly_check = #{anomalyCheck,jdbcType=BIT},
      lost_check = #{lostCheck,jdbcType=BIT},
      ts_interval = #{tsInterval,jdbcType=INTEGER},
      gmt_latest_date = #{gmtLatestDate,jdbcType=TIMESTAMP},
      source_type = #{sourceType,jdbcType=VARCHAR},
      sls_config = #{slsConfig,jdbcType=CHAR},
    </trim>
  </insert>
  <select id="selectOneByExample" parameterType="com.aliyun.xdragon.common.generate.model.MetricSourceExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from metric_source
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from metric_source
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
</mapper>