package com.aliyun.xdragon.common.model.log.response;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import com.aliyun.xdragon.common.enumeration.log.PatternDetailType;
import com.aliyun.xdragon.common.model.log.MapPatternDetail;
import com.aliyun.xdragon.common.model.log.PatternDetail;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/08/01
 */
@Data
public class RawPatternDetailResponse implements Serializable {
    private static final long serialVersionUID = -8862915202301774375L;

    List<String> tokens;

    List<PatternDetail> details;

    public RawPatternDetailResponse() {}

    public RawPatternDetailResponse(String pattern, List<HashMap<String, Integer>> mapList) {
        tokens = Arrays.stream(pattern.split(" ")).filter(w -> !"***".equals(w)).collect(Collectors.toList());
        details = new ArrayList<>(mapList.size());
        for (HashMap<String, Integer> mp : mapList) {
            MapPatternDetail detail = new MapPatternDetail();
            detail.setType(PatternDetailType.MAP);
            detail.setMap(mp);
            details.add(detail);
        }
    }
}
