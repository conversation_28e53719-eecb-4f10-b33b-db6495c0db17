package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.model.ChatSessionInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ChatSessionInfoMapper {
    /**
     * 保存会话
     * */
    int saveSession(ChatSessionInfo chatSessionInfo);

    /**
     * 根据工号查询会话列表
     * */
    List<ChatSessionInfo> querySessionListByStaffId(@Param("staffId") String staffId, @Param("offset")Integer offset, @Param("pageSize") Integer pageSize);

    /**
     * 根据sessionId查询会话信息
     * */
    ChatSessionInfo querySessionInfo(@Param("sessionId") String sessionId);

}
