package com.aliyun.xdragon.common.generate.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

public class LogCoverageDetail implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_detail.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_detail.metric
     *
     * @mbg.generated
     */
    private String metric;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_detail.timestamp
     *
     * @mbg.generated
     */
    private Integer timestamp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_detail.value
     *
     * @mbg.generated
     */
    private String value;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_detail.region
     *
     * @mbg.generated
     */
    private String region;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_detail.total_nc
     *
     * @mbg.generated
     */
    private Integer totalNc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_detail.log_nc
     *
     * @mbg.generated
     */
    private Integer logNc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_detail.project
     *
     * @mbg.generated
     */
    private String project;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_detail.logstore
     *
     * @mbg.generated
     */
    private String logstore;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_detail.pick_interval
     *
     * @mbg.generated
     */
    private Integer pickInterval;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_detail.is_total
     *
     * @mbg.generated
     */
    private Boolean isTotal;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_detail.gmt_modified
     *
     * @mbg.generated
     */
    private Long gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_detail.gmt_create
     *
     * @mbg.generated
     */
    private Long gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_detail.is_fixed
     *
     * @mbg.generated
     */
    private Boolean isFixed;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_detail.id
     *
     * @return the value of log_coverage_detail.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_detail.id
     *
     * @param id the value for log_coverage_detail.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_detail.metric
     *
     * @return the value of log_coverage_detail.metric
     *
     * @mbg.generated
     */
    public String getMetric() {
        return metric;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_detail.metric
     *
     * @param metric the value for log_coverage_detail.metric
     *
     * @mbg.generated
     */
    public void setMetric(String metric) {
        this.metric = metric;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_detail.timestamp
     *
     * @return the value of log_coverage_detail.timestamp
     *
     * @mbg.generated
     */
    public Integer getTimestamp() {
        return timestamp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_detail.timestamp
     *
     * @param timestamp the value for log_coverage_detail.timestamp
     *
     * @mbg.generated
     */
    public void setTimestamp(Integer timestamp) {
        this.timestamp = timestamp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_detail.value
     *
     * @return the value of log_coverage_detail.value
     *
     * @mbg.generated
     */
    public String getValue() {
        return value;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_detail.value
     *
     * @param value the value for log_coverage_detail.value
     *
     * @mbg.generated
     */
    public void setValue(String value) {
        this.value = value;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_detail.region
     *
     * @return the value of log_coverage_detail.region
     *
     * @mbg.generated
     */
    public String getRegion() {
        return region;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_detail.region
     *
     * @param region the value for log_coverage_detail.region
     *
     * @mbg.generated
     */
    public void setRegion(String region) {
        this.region = region;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_detail.total_nc
     *
     * @return the value of log_coverage_detail.total_nc
     *
     * @mbg.generated
     */
    public Integer getTotalNc() {
        return totalNc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_detail.total_nc
     *
     * @param totalNc the value for log_coverage_detail.total_nc
     *
     * @mbg.generated
     */
    public void setTotalNc(Integer totalNc) {
        this.totalNc = totalNc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_detail.log_nc
     *
     * @return the value of log_coverage_detail.log_nc
     *
     * @mbg.generated
     */
    public Integer getLogNc() {
        return logNc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_detail.log_nc
     *
     * @param logNc the value for log_coverage_detail.log_nc
     *
     * @mbg.generated
     */
    public void setLogNc(Integer logNc) {
        this.logNc = logNc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_detail.project
     *
     * @return the value of log_coverage_detail.project
     *
     * @mbg.generated
     */
    public String getProject() {
        return project;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_detail.project
     *
     * @param project the value for log_coverage_detail.project
     *
     * @mbg.generated
     */
    public void setProject(String project) {
        this.project = project;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_detail.logstore
     *
     * @return the value of log_coverage_detail.logstore
     *
     * @mbg.generated
     */
    public String getLogstore() {
        return logstore;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_detail.logstore
     *
     * @param logstore the value for log_coverage_detail.logstore
     *
     * @mbg.generated
     */
    public void setLogstore(String logstore) {
        this.logstore = logstore;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_detail.pick_interval
     *
     * @return the value of log_coverage_detail.pick_interval
     *
     * @mbg.generated
     */
    public Integer getPickInterval() {
        return pickInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_detail.pick_interval
     *
     * @param pickInterval the value for log_coverage_detail.pick_interval
     *
     * @mbg.generated
     */
    public void setPickInterval(Integer pickInterval) {
        this.pickInterval = pickInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_detail.is_total
     *
     * @return the value of log_coverage_detail.is_total
     *
     * @mbg.generated
     */
    public Boolean getIsTotal() {
        return isTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_detail.is_total
     *
     * @param isTotal the value for log_coverage_detail.is_total
     *
     * @mbg.generated
     */
    public void setIsTotal(Boolean isTotal) {
        this.isTotal = isTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_detail.gmt_modified
     *
     * @return the value of log_coverage_detail.gmt_modified
     *
     * @mbg.generated
     */
    public Long getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_detail.gmt_modified
     *
     * @param gmtModified the value for log_coverage_detail.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Long gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_detail.gmt_create
     *
     * @return the value of log_coverage_detail.gmt_create
     *
     * @mbg.generated
     */
    public Long getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_detail.gmt_create
     *
     * @param gmtCreate the value for log_coverage_detail.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Long gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_detail.is_fixed
     *
     * @return the value of log_coverage_detail.is_fixed
     *
     * @mbg.generated
     */
    public Boolean getIsFixed() {
        return isFixed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_detail.is_fixed
     *
     * @param isFixed the value for log_coverage_detail.is_fixed
     *
     * @mbg.generated
     */
    public void setIsFixed(Boolean isFixed) {
        this.isFixed = isFixed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        LogCoverageDetail other = (LogCoverageDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getMetric() == null ? other.getMetric() == null : this.getMetric().equals(other.getMetric()))
            && (this.getTimestamp() == null ? other.getTimestamp() == null : this.getTimestamp().equals(other.getTimestamp()))
            && (this.getValue() == null ? other.getValue() == null : this.getValue().equals(other.getValue()))
            && (this.getRegion() == null ? other.getRegion() == null : this.getRegion().equals(other.getRegion()))
            && (this.getTotalNc() == null ? other.getTotalNc() == null : this.getTotalNc().equals(other.getTotalNc()))
            && (this.getLogNc() == null ? other.getLogNc() == null : this.getLogNc().equals(other.getLogNc()))
            && (this.getProject() == null ? other.getProject() == null : this.getProject().equals(other.getProject()))
            && (this.getLogstore() == null ? other.getLogstore() == null : this.getLogstore().equals(other.getLogstore()))
            && (this.getPickInterval() == null ? other.getPickInterval() == null : this.getPickInterval().equals(other.getPickInterval()))
            && (this.getIsTotal() == null ? other.getIsTotal() == null : this.getIsTotal().equals(other.getIsTotal()))
            && (this.getGmtModified() == null ? other.getGmtModified() == null : this.getGmtModified().equals(other.getGmtModified()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getIsFixed() == null ? other.getIsFixed() == null : this.getIsFixed().equals(other.getIsFixed()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getMetric() == null) ? 0 : getMetric().hashCode());
        result = prime * result + ((getTimestamp() == null) ? 0 : getTimestamp().hashCode());
        result = prime * result + ((getValue() == null) ? 0 : getValue().hashCode());
        result = prime * result + ((getRegion() == null) ? 0 : getRegion().hashCode());
        result = prime * result + ((getTotalNc() == null) ? 0 : getTotalNc().hashCode());
        result = prime * result + ((getLogNc() == null) ? 0 : getLogNc().hashCode());
        result = prime * result + ((getProject() == null) ? 0 : getProject().hashCode());
        result = prime * result + ((getLogstore() == null) ? 0 : getLogstore().hashCode());
        result = prime * result + ((getPickInterval() == null) ? 0 : getPickInterval().hashCode());
        result = prime * result + ((getIsTotal() == null) ? 0 : getIsTotal().hashCode());
        result = prime * result + ((getGmtModified() == null) ? 0 : getGmtModified().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getIsFixed() == null) ? 0 : getIsFixed().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", metric=").append(metric);
        sb.append(", timestamp=").append(timestamp);
        sb.append(", value=").append(value);
        sb.append(", region=").append(region);
        sb.append(", totalNc=").append(totalNc);
        sb.append(", logNc=").append(logNc);
        sb.append(", project=").append(project);
        sb.append(", logstore=").append(logstore);
        sb.append(", pickInterval=").append(pickInterval);
        sb.append(", isTotal=").append(isTotal);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", isFixed=").append(isFixed);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    public enum Column {
        id("id", "id", "BIGINT", false),
        metric("metric", "metric", "VARCHAR", false),
        timestamp("timestamp", "timestamp", "INTEGER", true),
        value("value", "value", "VARCHAR", true),
        region("region", "region", "VARCHAR", false),
        totalNc("total_nc", "totalNc", "INTEGER", false),
        logNc("log_nc", "logNc", "INTEGER", false),
        project("project", "project", "VARCHAR", false),
        logstore("logstore", "logstore", "VARCHAR", false),
        pickInterval("pick_interval", "pickInterval", "INTEGER", false),
        isTotal("is_total", "isTotal", "BIT", false),
        gmtModified("gmt_modified", "gmtModified", "BIGINT", false),
        gmtCreate("gmt_create", "gmtCreate", "BIGINT", false),
        isFixed("is_fixed", "isFixed", "BIT", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_detail
         *
         * @mbg.generated
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}