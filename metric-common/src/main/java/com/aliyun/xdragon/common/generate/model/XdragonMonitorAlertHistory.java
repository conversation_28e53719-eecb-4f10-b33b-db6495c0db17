package com.aliyun.xdragon.common.generate.model;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

@Data
public class XdragonMonitorAlertHistory implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column xdragon_monitor_alert_history.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column xdragon_monitor_alert_history.alert_info_id
     *
     * @mbg.generated
     */
    private Long alertInfoId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column xdragon_monitor_alert_history.alert_info_name
     *
     * @mbg.generated
     */
    private String alertInfoName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column xdragon_monitor_alert_history.level
     *
     * @mbg.generated
     */
    private String level;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column xdragon_monitor_alert_history.receiver
     *
     * @mbg.generated
     */
    private String receiver;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column xdragon_monitor_alert_history.resource_id
     *
     * @mbg.generated
     */
    private String resourceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column xdragon_monitor_alert_history.alert_channel
     *
     * @mbg.generated
     */
    private String alertChannel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column xdragon_monitor_alert_history.gmt_created
     *
     * @mbg.generated
     */
    private Date gmtCreated;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column xdragon_monitor_alert_history.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table xdragon_monitor_alert_history
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

}