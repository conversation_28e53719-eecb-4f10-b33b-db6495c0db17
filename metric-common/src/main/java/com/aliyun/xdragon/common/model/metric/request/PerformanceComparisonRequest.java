package com.aliyun.xdragon.common.model.metric.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * 性能对比功能请求，包括基本信息对比以及指标对比
 *
 * <AUTHOR>
 * @date 2024/04/12
 */
@Data
public class PerformanceComparisonRequest implements Serializable {
    private static final long serialVersionUID = -7892354859879809561L;
    /**
     * 用于对比的所有实例id
     */
    @NotNull
    @Size(min = 1, max = 10)
    private List<String> instanceIds;
    /**
     * 对比的指标。用于基本信息对比时为空字符串即可
     */
    @NotNull
    private String metricName;
    /**
     * 开始时间。必要参数
     */
    @NotNull
    @Positive
    private Integer startTs;
    /**
     * 结束时间。必要参数
     */
    @NotNull
    @Positive
    private Integer endTs;
}
