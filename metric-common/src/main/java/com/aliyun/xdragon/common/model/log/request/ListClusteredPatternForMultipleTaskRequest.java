package com.aliyun.xdragon.common.model.log.request;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import com.aliyun.xdragon.common.enumeration.algorithm.AgglomerativeClusterLinkage;
import com.aliyun.xdragon.common.enumeration.algorithm.SeriesSimilarityType;
import com.aliyun.xdragon.common.enumeration.log.PatternTagType;
import lombok.Data;

@Data
public class ListClusteredPatternForMultipleTaskRequest extends ListPatternForMultipleTaskRequest {
    private static final long serialVersionUID = -6814346068827546336L;

    /**
     * completeness threshold, series with completeness lower than it is invalid and not used for clustering
     */
    @Min(0)
    @Max(1)
    Double completeness;

    /**
     * similarity threshold of clustering, higher dissimilarity tolerance with decreasing threshold
     */
    @Min(0)
    @Max(1)
    Double similarity;

    /**
     * linkage strategy of agglomerative cluster
     */
    @NotNull
    AgglomerativeClusterLinkage linkage;

    /**
     * the function for distance measurements
     */
    @NotNull
    SeriesSimilarityType distanceFunction;


    public ListClusteredPatternForMultipleTaskRequest(List<Long> taskIds, Long startMs, Long endMs, Integer start,
        Integer pageSize, PatternTagType type, TagFlag tagFlag, Map<String, List<String>> conditionMap,
        String patternFilter, List<String> patternMd5s, Double completeness, Double similarity,
        AgglomerativeClusterLinkage linkage, SeriesSimilarityType distanceFunction) {
        super(taskIds, startMs, endMs, start, pageSize, type, tagFlag, conditionMap, patternFilter, patternMd5s);
        this.completeness = completeness;
        this.similarity = similarity;
        this.linkage = linkage;
        this.distanceFunction = distanceFunction;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        ListClusteredPatternRequest request = (ListClusteredPatternRequest) o;
        return completeness.equals(request.completeness) && similarity.equals(
            request.similarity) && linkage == request.linkage && distanceFunction == request.distanceFunction;
    }


    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), completeness, similarity, linkage, distanceFunction);
    }
}
