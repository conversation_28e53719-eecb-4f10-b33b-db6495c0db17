package com.aliyun.xdragon.common.model.param;

/**
 * <AUTHOR>
 * @date 2022/04/11
 */

import java.io.Serializable;
import java.util.List;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class OdpsTaskParam implements Serializable {

    @SerializedName("run_time")
    @Expose
    private String runTime;
    @SerializedName("data_set")
    @Expose
    private List<OdpsDataSet> dataSet = null;
    @SerializedName("level")
    @Expose
    private Double level=0.05;

    @SerializedName("detail_config")
    @Expose
    private DetailConfig detailConfig;

    private final static long serialVersionUID = -3459631073288334596L;

    public String getRunTime() {
        return runTime;
    }

    public void setRunTime(String runTime) {
        this.runTime = runTime;
    }

    public List<OdpsDataSet> getDataSet() {
        return dataSet;
    }

    public void setDataSet(List<OdpsDataSet> dataSet) {
        this.dataSet = dataSet;
    }


    public Double getLevel() {
        return level;
    }

    public void setLevel(Double level) {
        this.level = level;
    }

    public DetailConfig getDetailConfig() {
        return detailConfig;
    }

    public void setDetailConfig(DetailConfig detailConfig) {
        this.detailConfig = detailConfig;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(OdpsTaskParam.class.getName()).append('@').append(Integer.toHexString(System.identityHashCode(this)))
            .append('[');
        sb.append("runTime");
        sb.append('=');
        sb.append(((this.runTime == null) ? "<null>" : this.runTime));
        sb.append(',');
        sb.append("dataSet");
        sb.append('=');
        sb.append(((this.dataSet == null) ? "<null>" : this.dataSet));
        sb.append(',');
        sb.append("level");
        sb.append('=');
        sb.append(((this.level == null)?"<null>":this.level));
        sb.append(',');
        if (sb.charAt((sb.length() - 1)) == ',') {
            sb.setCharAt((sb.length() - 1), ']');
        } else {
            sb.append(']');
        }
        return sb.toString();
    }
}
