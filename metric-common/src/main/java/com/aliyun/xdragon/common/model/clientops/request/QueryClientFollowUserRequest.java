package com.aliyun.xdragon.common.model.clientops.request;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/06/18
 */
@Data
public class QueryClientFollowUserRequest implements Serializable {

    private static final long serialVersionUID = -3962364910126981281L;

    /**
     * 关注人工号
     */
    @NotBlank
    private String empId;

    /**
     * uid
     */
    private String aliUid;

    /**
     * 名称
     */
    private String userName;

    /**
     * cid
     */
    private Long cid;

    /**
     * 备注
     */
    private String comment;
}
