package com.aliyun.xdragon.common.model.metric.request;

import java.io.Serializable;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

import com.aliyun.xdragon.common.enumeration.DetectType;
import com.aliyun.xdragon.common.enumeration.MatchingRule;
import com.aliyun.xdragon.common.enumeration.MissingFillType;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @date 2023/03/30
 */
@Data
public class UpsertMetricAnomalyConfigRequest implements Serializable {

    private static final long serialVersionUID = -3986692639105834500L;

    private Long configId;

    @Length(min = 1, max = 128)
    private String configName;

    @Positive
    @NotNull
    private Integer tsInterval;

    @Positive
    @NotNull
    private Integer expire;

    @NotNull
    private MissingFillType missFillType;

    @NotNull
    private String anomalyConfig;

    @Positive
    @NotNull
    private Integer smoothWinSize;

    private MatchingRule matchingRule = MatchingRule.PREFIX;

    private Boolean isDeleted;

    @NotNull
    private DetectType detectType;

    private String sourceIds;
}
