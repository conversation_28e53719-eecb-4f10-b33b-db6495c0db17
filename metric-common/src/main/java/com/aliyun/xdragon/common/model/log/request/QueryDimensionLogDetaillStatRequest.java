package com.aliyun.xdragon.common.model.log.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/28
 */
@Data
public class QueryDimensionLogDetaillStatRequest extends BaseTaskRequest implements Serializable {
    private static final long serialVersionUID = 7024525474901576264L;
    /**
     * query start time, in ms
     */
    @NotNull
    Long startMs;

    /**
     * query end time, in ms
     */
    @NotNull
    Long endMs;

    public QueryDimensionLogDetaillStatRequest() {}

    public QueryDimensionLogDetaillStatRequest(Long taskId, Long startMs, Long endMs) {
        setTaskId(taskId);
        this.startMs = startMs;
        this.endMs = endMs;
    }
}
