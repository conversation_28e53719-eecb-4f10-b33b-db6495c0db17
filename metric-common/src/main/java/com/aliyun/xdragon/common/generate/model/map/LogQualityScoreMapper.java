package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.LogQualityScore;
import com.aliyun.xdragon.common.generate.model.LogQualityScoreExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface LogQualityScoreMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    long countByExample(LogQualityScoreExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    int deleteByExample(LogQualityScoreExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    int insert(LogQualityScore record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    int insertSelective(LogQualityScore record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    List<LogQualityScore> selectByExampleWithRowbounds(LogQualityScoreExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    List<LogQualityScore> selectByExampleSelective(@Param("example") LogQualityScoreExample example, @Param("selective") LogQualityScore.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    LogQualityScore selectOneByExample(LogQualityScoreExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    LogQualityScore selectOneByExampleSelective(@Param("example") LogQualityScoreExample example, @Param("selective") LogQualityScore.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    List<LogQualityScore> selectByExample(LogQualityScoreExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    LogQualityScore selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") LogQualityScore.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    LogQualityScore selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") LogQualityScore record, @Param("example") LogQualityScoreExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") LogQualityScore record, @Param("example") LogQualityScoreExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(LogQualityScore record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(LogQualityScore record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<LogQualityScore> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<LogQualityScore> list, @Param("selective") LogQualityScore.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    int upsert(LogQualityScore record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    int upsertSelective(LogQualityScore record);
}