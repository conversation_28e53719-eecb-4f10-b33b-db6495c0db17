
package com.aliyun.xdragon.common.model.bpms;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class BpmsProcessStatus extends BpmsBase{
    @SerializedName("content")
    @Expose
    private Content content;


    @Data
    public class Content {
        @SerializedName("processInstanceId")
        @Expose
        private String processInstanceId;
        @SerializedName("finishTime")
        @Expose
        private String finishTime;
        @SerializedName("instStatus")
        @Expose
        private String instStatus;
        @SerializedName("title")
        @Expose
        private String title;
        @SerializedName("processVersion")
        @Expose
        private String processVersion;
        @SerializedName("dynamicProcess")
        @Expose
        private Boolean dynamicProcess;
        @SerializedName("outResult")
        @Expose
        private String outResult;
        @SerializedName("titleEn")
        @Expose
        private String titleEn;
        @SerializedName("createTime")
        @Expose
        private String createTime;
        @SerializedName("originatorJob")
        @Expose
        private String originatorJob;
        @SerializedName("processCode")
        @Expose
        private String processCode;
        @SerializedName("originatorId")
        @Expose
        private String originatorId;
    }

}
