package com.aliyun.xdragon.common.generate.log.model.map;

import com.aliyun.xdragon.common.generate.log.model.LogPatternMapping;
import com.aliyun.xdragon.common.generate.log.model.LogPatternMappingExample;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface LogPatternMappingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    long countByExample(LogPatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int deleteByExample(LogPatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Date time);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int insert(LogPatternMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int insertSelective(LogPatternMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    List<LogPatternMapping> selectByExampleWithBLOBsWithRowbounds(LogPatternMappingExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    List<LogPatternMapping> selectByExampleSelective(@Param("example") LogPatternMappingExample example, @Param("selective") LogPatternMapping.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    LogPatternMapping selectOneByExample(LogPatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    LogPatternMapping selectOneByExampleSelective(@Param("example") LogPatternMappingExample example, @Param("selective") LogPatternMapping.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    LogPatternMapping selectOneByExampleWithBLOBs(LogPatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    List<LogPatternMapping> selectByExampleWithBLOBs(LogPatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    List<LogPatternMapping> selectByExampleWithRowbounds(LogPatternMappingExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    List<LogPatternMapping> selectByExample(LogPatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    LogPatternMapping selectByPrimaryKeySelective(@Param("time") Date time, @Param("selective") LogPatternMapping.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    LogPatternMapping selectByPrimaryKey(Date time);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") LogPatternMapping record, @Param("example") LogPatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") LogPatternMapping record, @Param("example") LogPatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") LogPatternMapping record, @Param("example") LogPatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(LogPatternMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(LogPatternMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(LogPatternMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<LogPatternMapping> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<LogPatternMapping> list, @Param("selective") LogPatternMapping.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int upsert(LogPatternMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int upsertSelective(LogPatternMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_mapping
     *
     * @mbg.generated
     */
    int upsertWithBLOBs(LogPatternMapping record);
}