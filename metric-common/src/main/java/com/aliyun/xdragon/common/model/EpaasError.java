
package com.aliyun.xdragon.common.model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class EpaasError {

    @SerializedName("_RequestId")
    @Expose
    private String requestId;
    @SerializedName("Message")
    @Expose
    private String message;
    @SerializedName("success")
    @Expose
    private Boolean success;
    @SerializedName("errorCode")
    @Expose
    private Integer errorCode;
    @SerializedName("HostId")
    @Expose
    private String hostId;
    @SerializedName("Code")
    @Expose
    private String code;
    @SerializedName("errorMsg")
    @Expose
    private String errorMsg;
    @SerializedName("errorLevel")
    @Expose
    private String errorLevel;
}
