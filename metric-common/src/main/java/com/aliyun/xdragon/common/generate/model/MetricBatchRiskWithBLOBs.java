package com.aliyun.xdragon.common.generate.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

public class MetricBatchRiskWithBLOBs extends MetricBatchRisk implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.mark_comments
     *
     * @mbg.generated
     */
    private String markComments;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.drill_info
     *
     * @mbg.generated
     */
    private String drillInfo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.vcpu_info
     *
     * @mbg.generated
     */
    private String vcpuInfo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.extra_info
     *
     * @mbg.generated
     */
    private String extraInfo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table metric_batch_risk
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.mark_comments
     *
     * @return the value of metric_batch_risk.mark_comments
     *
     * @mbg.generated
     */
    public String getMarkComments() {
        return markComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.mark_comments
     *
     * @param markComments the value for metric_batch_risk.mark_comments
     *
     * @mbg.generated
     */
    public void setMarkComments(String markComments) {
        this.markComments = markComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.drill_info
     *
     * @return the value of metric_batch_risk.drill_info
     *
     * @mbg.generated
     */
    public String getDrillInfo() {
        return drillInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.drill_info
     *
     * @param drillInfo the value for metric_batch_risk.drill_info
     *
     * @mbg.generated
     */
    public void setDrillInfo(String drillInfo) {
        this.drillInfo = drillInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.vcpu_info
     *
     * @return the value of metric_batch_risk.vcpu_info
     *
     * @mbg.generated
     */
    public String getVcpuInfo() {
        return vcpuInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.vcpu_info
     *
     * @param vcpuInfo the value for metric_batch_risk.vcpu_info
     *
     * @mbg.generated
     */
    public void setVcpuInfo(String vcpuInfo) {
        this.vcpuInfo = vcpuInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.extra_info
     *
     * @return the value of metric_batch_risk.extra_info
     *
     * @mbg.generated
     */
    public String getExtraInfo() {
        return extraInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.extra_info
     *
     * @param extraInfo the value for metric_batch_risk.extra_info
     *
     * @mbg.generated
     */
    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MetricBatchRiskWithBLOBs other = (MetricBatchRiskWithBLOBs) that;
        return (this.getRiskId() == null ? other.getRiskId() == null : this.getRiskId().equals(other.getRiskId()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtModified() == null ? other.getGmtModified() == null : this.getGmtModified().equals(other.getGmtModified()))
            && (this.getRiskName() == null ? other.getRiskName() == null : this.getRiskName().equals(other.getRiskName()))
            && (this.getRiskType() == null ? other.getRiskType() == null : this.getRiskType().equals(other.getRiskType()))
            && (this.getAnomaly() == null ? other.getAnomaly() == null : this.getAnomaly().equals(other.getAnomaly()))
            && (this.getIsMarked() == null ? other.getIsMarked() == null : this.getIsMarked().equals(other.getIsMarked()))
            && (this.getValue() == null ? other.getValue() == null : this.getValue().equals(other.getValue()))
            && (this.getRiskLevel() == null ? other.getRiskLevel() == null : this.getRiskLevel().equals(other.getRiskLevel()))
            && (this.getGmtAnomaly() == null ? other.getGmtAnomaly() == null : this.getGmtAnomaly().equals(other.getGmtAnomaly()))
            && (this.getSourceId() == null ? other.getSourceId() == null : this.getSourceId().equals(other.getSourceId()))
            && (this.getMergeType() == null ? other.getMergeType() == null : this.getMergeType().equals(other.getMergeType()))
            && (this.getParentId() == null ? other.getParentId() == null : this.getParentId().equals(other.getParentId()))
            && (this.getTargetName() == null ? other.getTargetName() == null : this.getTargetName().equals(other.getTargetName()))
            && (this.getMarkComments() == null ? other.getMarkComments() == null : this.getMarkComments().equals(other.getMarkComments()))
            && (this.getDrillInfo() == null ? other.getDrillInfo() == null : this.getDrillInfo().equals(other.getDrillInfo()))
            && (this.getVcpuInfo() == null ? other.getVcpuInfo() == null : this.getVcpuInfo().equals(other.getVcpuInfo()))
            && (this.getExtraInfo() == null ? other.getExtraInfo() == null : this.getExtraInfo().equals(other.getExtraInfo()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getRiskId() == null) ? 0 : getRiskId().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtModified() == null) ? 0 : getGmtModified().hashCode());
        result = prime * result + ((getRiskName() == null) ? 0 : getRiskName().hashCode());
        result = prime * result + ((getRiskType() == null) ? 0 : getRiskType().hashCode());
        result = prime * result + ((getAnomaly() == null) ? 0 : getAnomaly().hashCode());
        result = prime * result + ((getIsMarked() == null) ? 0 : getIsMarked().hashCode());
        result = prime * result + ((getValue() == null) ? 0 : getValue().hashCode());
        result = prime * result + ((getRiskLevel() == null) ? 0 : getRiskLevel().hashCode());
        result = prime * result + ((getGmtAnomaly() == null) ? 0 : getGmtAnomaly().hashCode());
        result = prime * result + ((getSourceId() == null) ? 0 : getSourceId().hashCode());
        result = prime * result + ((getMergeType() == null) ? 0 : getMergeType().hashCode());
        result = prime * result + ((getParentId() == null) ? 0 : getParentId().hashCode());
        result = prime * result + ((getTargetName() == null) ? 0 : getTargetName().hashCode());
        result = prime * result + ((getMarkComments() == null) ? 0 : getMarkComments().hashCode());
        result = prime * result + ((getDrillInfo() == null) ? 0 : getDrillInfo().hashCode());
        result = prime * result + ((getVcpuInfo() == null) ? 0 : getVcpuInfo().hashCode());
        result = prime * result + ((getExtraInfo() == null) ? 0 : getExtraInfo().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", markComments=").append(markComments);
        sb.append(", drillInfo=").append(drillInfo);
        sb.append(", vcpuInfo=").append(vcpuInfo);
        sb.append(", extraInfo=").append(extraInfo);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table metric_batch_risk
     *
     * @mbg.generated
     */
    public enum Column {
        riskId("id", "riskId", "BIGINT", false),
        gmtCreate("gmt_create", "gmtCreate", "TIMESTAMP", false),
        gmtModified("gmt_modified", "gmtModified", "TIMESTAMP", false),
        riskName("risk_name", "riskName", "VARCHAR", false),
        riskType("risk_type", "riskType", "VARCHAR", false),
        anomaly("anomaly", "anomaly", "VARCHAR", false),
        isMarked("is_marked", "isMarked", "BIT", false),
        value("value", "value", "INTEGER", true),
        riskLevel("risk_level", "riskLevel", "VARCHAR", false),
        gmtAnomaly("gmt_anomaly", "gmtAnomaly", "TIMESTAMP", false),
        sourceId("source_id", "sourceId", "BIGINT", false),
        mergeType("merge_type", "mergeType", "INTEGER", false),
        parentId("parent_id", "parentId", "BIGINT", false),
        targetName("target_name", "targetName", "VARCHAR", false),
        markComments("mark_comments", "markComments", "LONGVARCHAR", false),
        drillInfo("drill_info", "drillInfo", "LONGVARCHAR", false),
        vcpuInfo("vcpu_info", "vcpuInfo", "LONGVARCHAR", false),
        extraInfo("extra_info", "extraInfo", "LONGVARCHAR", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}