package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/02/13
 */
@Data
public class SetAssociationDescRequest extends BaseTaskRequest implements Serializable {
    private static final long serialVersionUID = -5758076784880795480L;

    @Size(min = 1)
    @NotNull
    List<String> associations;
}
