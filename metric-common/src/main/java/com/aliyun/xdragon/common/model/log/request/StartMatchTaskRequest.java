package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import com.aliyun.xdragon.common.model.log.response.LogTaskStartStrategy;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/03/04
 */
@Data
public class StartMatchTaskRequest extends SubTaskRequest implements Serializable {
    private static final long serialVersionUID = -175865898752880692L;
    @NotNull
    private LogTaskStartStrategy startStrategy;
}
