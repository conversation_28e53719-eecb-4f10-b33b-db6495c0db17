package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/12
 */
@Data
public class BatchUpdatePatternMarkerRequest implements Serializable {
    private static final long serialVersionUID = 7750225355963336960L;

    @Valid
    @Size(min = 1, max = 25)
    List<UpdatePatternMarkerRequest> requests;
}
