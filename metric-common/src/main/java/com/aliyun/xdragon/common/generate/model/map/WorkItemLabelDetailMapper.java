package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.WorkItemLabelDetail;
import com.aliyun.xdragon.common.generate.model.WorkItemLabelDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface WorkItemLabelDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    long countByExample(WorkItemLabelDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    int deleteByExample(WorkItemLabelDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    int insert(WorkItemLabelDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    int insertSelective(WorkItemLabelDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    List<WorkItemLabelDetail> selectByExampleWithRowbounds(WorkItemLabelDetailExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    List<WorkItemLabelDetail> selectByExampleSelective(@Param("example") WorkItemLabelDetailExample example, @Param("selective") WorkItemLabelDetail.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    WorkItemLabelDetail selectOneByExample(WorkItemLabelDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    WorkItemLabelDetail selectOneByExampleSelective(@Param("example") WorkItemLabelDetailExample example, @Param("selective") WorkItemLabelDetail.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    List<WorkItemLabelDetail> selectByExample(WorkItemLabelDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    WorkItemLabelDetail selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") WorkItemLabelDetail.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    WorkItemLabelDetail selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") WorkItemLabelDetail record, @Param("example") WorkItemLabelDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") WorkItemLabelDetail record, @Param("example") WorkItemLabelDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(WorkItemLabelDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(WorkItemLabelDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<WorkItemLabelDetail> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<WorkItemLabelDetail> list, @Param("selective") WorkItemLabelDetail.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    int upsert(WorkItemLabelDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    int upsertSelective(WorkItemLabelDetail record);
}