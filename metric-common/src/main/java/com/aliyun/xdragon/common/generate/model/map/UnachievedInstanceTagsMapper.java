package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.UnachievedInstanceTags;
import com.aliyun.xdragon.common.generate.model.UnachievedInstanceTagsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface UnachievedInstanceTagsMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    long countByExample(UnachievedInstanceTagsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    int deleteByExample(UnachievedInstanceTagsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    int insert(UnachievedInstanceTags record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    int insertSelective(UnachievedInstanceTags record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    List<UnachievedInstanceTags> selectByExampleWithRowbounds(UnachievedInstanceTagsExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    List<UnachievedInstanceTags> selectByExampleSelective(@Param("example") UnachievedInstanceTagsExample example, @Param("selective") UnachievedInstanceTags.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    UnachievedInstanceTags selectOneByExample(UnachievedInstanceTagsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    UnachievedInstanceTags selectOneByExampleSelective(@Param("example") UnachievedInstanceTagsExample example, @Param("selective") UnachievedInstanceTags.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    List<UnachievedInstanceTags> selectByExample(UnachievedInstanceTagsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    UnachievedInstanceTags selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") UnachievedInstanceTags.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    UnachievedInstanceTags selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") UnachievedInstanceTags record, @Param("example") UnachievedInstanceTagsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") UnachievedInstanceTags record, @Param("example") UnachievedInstanceTagsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(UnachievedInstanceTags record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(UnachievedInstanceTags record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<UnachievedInstanceTags> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<UnachievedInstanceTags> list, @Param("selective") UnachievedInstanceTags.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    int upsert(UnachievedInstanceTags record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table unachieved_instance_tags
     *
     * @mbg.generated
     */
    int upsertSelective(UnachievedInstanceTags record);
}