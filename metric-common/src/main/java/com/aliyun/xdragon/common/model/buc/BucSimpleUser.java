
package com.aliyun.xdragon.common.model.buc;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * https://aliyuque.antfin.com/buc-acl/buc-dev/gqrqfa?#bhmZi
 */
@Data
public class BucSimpleUser extends BucBase {
    @SerializedName("content")
    @Expose
    private Content content;

    @Data
    public class Content {

        @SerializedName("empId")
        @Expose
        private String empId;
        @SerializedName("lastName")
        @Expose
        private String lastName;
        @SerializedName("businessUnit")
        @Expose
        private String businessUnit;
        @SerializedName("nickNameEn")
        @Expose
        private String nickNameEn;
        @SerializedName("available")
        @Expose
        private String available;
        @SerializedName("nickNameCn")
        @Expose
        private String nickNameCn;
        @SerializedName("nickName")
        @Expose
        private String nickName;
        @SerializedName("emailPrefix")
        @Expose
        private String emailPrefix;
        @SerializedName("userId")
        @Expose
        private Integer userId;
        @SerializedName("hrStatus")
        @Expose
        private String hrStatus;
        @SerializedName("firstName")
        @Expose
        private String firstName;
        @SerializedName("emailAddr")
        @Expose
        private String emailAddr;
        @SerializedName("isTemp")
        @Expose
        private String isTemp;
        @SerializedName("ownerUserId")
        @Expose
        private Integer ownerUserId;
        @SerializedName("depId")
        @Expose
        private String depId;
        @SerializedName("primaryHavanaId")
        @Expose
        private String primaryHavanaId;
        @SerializedName("userType")
        @Expose
        private String userType;
        /**
         * 同步blacklistuser【status】字段
         * 0：正常 ;
         * 1：禁用;
         * 2：休假中;
         * 3:预入职;
         * 5:离职回收;
         * 100:系统冻结：外包三个月未登录/认证;
         * 101:系统冻结：弱密码通知7次以上;
         * 102:系统冻结:外包账号合同到期;
         * 103:预离职;
         * 104:HRG紧急发起的冻结账号;
         * 105:风冻结账号
         */
        @SerializedName("status")
        @Expose
        private Integer status;
    }

}
