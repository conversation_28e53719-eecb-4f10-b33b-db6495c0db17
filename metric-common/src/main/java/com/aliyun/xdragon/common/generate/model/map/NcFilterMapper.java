package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.NcFilter;
import com.aliyun.xdragon.common.generate.model.NcFilterExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface NcFilterMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    long countByExample(NcFilterExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    int deleteByExample(NcFilterExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    int insert(NcFilter record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    int insertSelective(NcFilter record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    List<NcFilter> selectByExampleWithRowbounds(NcFilterExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    List<NcFilter> selectByExampleSelective(@Param("example") NcFilterExample example, @Param("selective") NcFilter.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    NcFilter selectOneByExample(NcFilterExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    NcFilter selectOneByExampleSelective(@Param("example") NcFilterExample example, @Param("selective") NcFilter.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    List<NcFilter> selectByExample(NcFilterExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    NcFilter selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") NcFilter.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    NcFilter selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") NcFilter record, @Param("example") NcFilterExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") NcFilter record, @Param("example") NcFilterExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(NcFilter record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(NcFilter record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<NcFilter> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<NcFilter> list, @Param("selective") NcFilter.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    int upsert(NcFilter record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_filter
     *
     * @mbg.generated
     */
    int upsertSelective(NcFilter record);
}