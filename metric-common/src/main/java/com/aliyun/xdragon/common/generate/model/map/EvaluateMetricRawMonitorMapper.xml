<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.model.map.EvaluateMetricRawMonitorMapper">
  <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.model.EvaluateMetricRawMonitor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="batch_id" jdbcType="BIGINT" property="batchId" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="metric_name" jdbcType="VARCHAR" property="metricName" />
    <result column="action_name" jdbcType="VARCHAR" property="actionName" />
    <result column="instance_id" jdbcType="VARCHAR" property="instanceId" />
    <result column="exception_name" jdbcType="VARCHAR" property="exceptionName" />
    <result column="exception_time_list" jdbcType="VARCHAR" property="exceptionTimeList" />
    <result column="duration" jdbcType="DOUBLE" property="duration" />
    <result column="monitor_type" jdbcType="VARCHAR" property="monitorType" />
    <result column="monitor_level" jdbcType="VARCHAR" property="monitorLevel" />
    <result column="ali_uid" jdbcType="VARCHAR" property="aliUid" />
    <result column="nc_ip" jdbcType="VARCHAR" property="ncIp" />
    <result column="nc_id" jdbcType="VARCHAR" property="ncId" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, batch_id, task_id, metric_name, action_name, instance_id, exception_name, exception_time_list, 
    duration, monitor_type, monitor_level, ali_uid, nc_ip, nc_id, sn
  </sql>
  <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateMetricRawMonitorExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from evaluate_metric_raw_monitor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from evaluate_metric_raw_monitor
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from evaluate_metric_raw_monitor
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateMetricRawMonitorExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from evaluate_metric_raw_monitor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateMetricRawMonitor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into evaluate_metric_raw_monitor (batch_id, task_id, metric_name, 
      action_name, instance_id, exception_name, 
      exception_time_list, duration, monitor_type, 
      monitor_level, ali_uid, nc_ip, 
      nc_id, sn)
    values (#{batchId,jdbcType=BIGINT}, #{taskId,jdbcType=BIGINT}, #{metricName,jdbcType=VARCHAR}, 
      #{actionName,jdbcType=VARCHAR}, #{instanceId,jdbcType=VARCHAR}, #{exceptionName,jdbcType=VARCHAR}, 
      #{exceptionTimeList,jdbcType=VARCHAR}, #{duration,jdbcType=DOUBLE}, #{monitorType,jdbcType=VARCHAR}, 
      #{monitorLevel,jdbcType=VARCHAR}, #{aliUid,jdbcType=VARCHAR}, #{ncIp,jdbcType=VARCHAR}, 
      #{ncId,jdbcType=VARCHAR}, #{sn,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateMetricRawMonitor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into evaluate_metric_raw_monitor
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="metricName != null">
        metric_name,
      </if>
      <if test="actionName != null">
        action_name,
      </if>
      <if test="instanceId != null">
        instance_id,
      </if>
      <if test="exceptionName != null">
        exception_name,
      </if>
      <if test="exceptionTimeList != null">
        exception_time_list,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="monitorType != null">
        monitor_type,
      </if>
      <if test="monitorLevel != null">
        monitor_level,
      </if>
      <if test="aliUid != null">
        ali_uid,
      </if>
      <if test="ncIp != null">
        nc_ip,
      </if>
      <if test="ncId != null">
        nc_id,
      </if>
      <if test="sn != null">
        sn,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="batchId != null">
        #{batchId,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="metricName != null">
        #{metricName,jdbcType=VARCHAR},
      </if>
      <if test="actionName != null">
        #{actionName,jdbcType=VARCHAR},
      </if>
      <if test="instanceId != null">
        #{instanceId,jdbcType=VARCHAR},
      </if>
      <if test="exceptionName != null">
        #{exceptionName,jdbcType=VARCHAR},
      </if>
      <if test="exceptionTimeList != null">
        #{exceptionTimeList,jdbcType=VARCHAR},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=DOUBLE},
      </if>
      <if test="monitorType != null">
        #{monitorType,jdbcType=VARCHAR},
      </if>
      <if test="monitorLevel != null">
        #{monitorLevel,jdbcType=VARCHAR},
      </if>
      <if test="aliUid != null">
        #{aliUid,jdbcType=VARCHAR},
      </if>
      <if test="ncIp != null">
        #{ncIp,jdbcType=VARCHAR},
      </if>
      <if test="ncId != null">
        #{ncId,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateMetricRawMonitorExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from evaluate_metric_raw_monitor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluate_metric_raw_monitor
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.batchId != null">
        batch_id = #{record.batchId,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.metricName != null">
        metric_name = #{record.metricName,jdbcType=VARCHAR},
      </if>
      <if test="record.actionName != null">
        action_name = #{record.actionName,jdbcType=VARCHAR},
      </if>
      <if test="record.instanceId != null">
        instance_id = #{record.instanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.exceptionName != null">
        exception_name = #{record.exceptionName,jdbcType=VARCHAR},
      </if>
      <if test="record.exceptionTimeList != null">
        exception_time_list = #{record.exceptionTimeList,jdbcType=VARCHAR},
      </if>
      <if test="record.duration != null">
        duration = #{record.duration,jdbcType=DOUBLE},
      </if>
      <if test="record.monitorType != null">
        monitor_type = #{record.monitorType,jdbcType=VARCHAR},
      </if>
      <if test="record.monitorLevel != null">
        monitor_level = #{record.monitorLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.aliUid != null">
        ali_uid = #{record.aliUid,jdbcType=VARCHAR},
      </if>
      <if test="record.ncIp != null">
        nc_ip = #{record.ncIp,jdbcType=VARCHAR},
      </if>
      <if test="record.ncId != null">
        nc_id = #{record.ncId,jdbcType=VARCHAR},
      </if>
      <if test="record.sn != null">
        sn = #{record.sn,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluate_metric_raw_monitor
    set id = #{record.id,jdbcType=BIGINT},
      batch_id = #{record.batchId,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      metric_name = #{record.metricName,jdbcType=VARCHAR},
      action_name = #{record.actionName,jdbcType=VARCHAR},
      instance_id = #{record.instanceId,jdbcType=VARCHAR},
      exception_name = #{record.exceptionName,jdbcType=VARCHAR},
      exception_time_list = #{record.exceptionTimeList,jdbcType=VARCHAR},
      duration = #{record.duration,jdbcType=DOUBLE},
      monitor_type = #{record.monitorType,jdbcType=VARCHAR},
      monitor_level = #{record.monitorLevel,jdbcType=VARCHAR},
      ali_uid = #{record.aliUid,jdbcType=VARCHAR},
      nc_ip = #{record.ncIp,jdbcType=VARCHAR},
      nc_id = #{record.ncId,jdbcType=VARCHAR},
      sn = #{record.sn,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateMetricRawMonitor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluate_metric_raw_monitor
    <set>
      <if test="batchId != null">
        batch_id = #{batchId,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="metricName != null">
        metric_name = #{metricName,jdbcType=VARCHAR},
      </if>
      <if test="actionName != null">
        action_name = #{actionName,jdbcType=VARCHAR},
      </if>
      <if test="instanceId != null">
        instance_id = #{instanceId,jdbcType=VARCHAR},
      </if>
      <if test="exceptionName != null">
        exception_name = #{exceptionName,jdbcType=VARCHAR},
      </if>
      <if test="exceptionTimeList != null">
        exception_time_list = #{exceptionTimeList,jdbcType=VARCHAR},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=DOUBLE},
      </if>
      <if test="monitorType != null">
        monitor_type = #{monitorType,jdbcType=VARCHAR},
      </if>
      <if test="monitorLevel != null">
        monitor_level = #{monitorLevel,jdbcType=VARCHAR},
      </if>
      <if test="aliUid != null">
        ali_uid = #{aliUid,jdbcType=VARCHAR},
      </if>
      <if test="ncIp != null">
        nc_ip = #{ncIp,jdbcType=VARCHAR},
      </if>
      <if test="ncId != null">
        nc_id = #{ncId,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        sn = #{sn,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateMetricRawMonitor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluate_metric_raw_monitor
    set batch_id = #{batchId,jdbcType=BIGINT},
      task_id = #{taskId,jdbcType=BIGINT},
      metric_name = #{metricName,jdbcType=VARCHAR},
      action_name = #{actionName,jdbcType=VARCHAR},
      instance_id = #{instanceId,jdbcType=VARCHAR},
      exception_name = #{exceptionName,jdbcType=VARCHAR},
      exception_time_list = #{exceptionTimeList,jdbcType=VARCHAR},
      duration = #{duration,jdbcType=DOUBLE},
      monitor_type = #{monitorType,jdbcType=VARCHAR},
      monitor_level = #{monitorLevel,jdbcType=VARCHAR},
      ali_uid = #{aliUid,jdbcType=VARCHAR},
      nc_ip = #{ncIp,jdbcType=VARCHAR},
      nc_id = #{ncId,jdbcType=VARCHAR},
      sn = #{sn,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateMetricRawMonitorExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from evaluate_metric_raw_monitor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into evaluate_metric_raw_monitor
    (batch_id, task_id, metric_name, action_name, instance_id, exception_name, exception_time_list, 
      duration, monitor_type, monitor_level, ali_uid, nc_ip, nc_id, sn)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.batchId,jdbcType=BIGINT}, #{item.taskId,jdbcType=BIGINT}, #{item.metricName,jdbcType=VARCHAR}, 
        #{item.actionName,jdbcType=VARCHAR}, #{item.instanceId,jdbcType=VARCHAR}, #{item.exceptionName,jdbcType=VARCHAR}, 
        #{item.exceptionTimeList,jdbcType=VARCHAR}, #{item.duration,jdbcType=DOUBLE}, #{item.monitorType,jdbcType=VARCHAR}, 
        #{item.monitorLevel,jdbcType=VARCHAR}, #{item.aliUid,jdbcType=VARCHAR}, #{item.ncIp,jdbcType=VARCHAR}, 
        #{item.ncId,jdbcType=VARCHAR}, #{item.sn,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into evaluate_metric_raw_monitor (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'batch_id'.toString() == column.value">
          #{item.batchId,jdbcType=BIGINT}
        </if>
        <if test="'task_id'.toString() == column.value">
          #{item.taskId,jdbcType=BIGINT}
        </if>
        <if test="'metric_name'.toString() == column.value">
          #{item.metricName,jdbcType=VARCHAR}
        </if>
        <if test="'action_name'.toString() == column.value">
          #{item.actionName,jdbcType=VARCHAR}
        </if>
        <if test="'instance_id'.toString() == column.value">
          #{item.instanceId,jdbcType=VARCHAR}
        </if>
        <if test="'exception_name'.toString() == column.value">
          #{item.exceptionName,jdbcType=VARCHAR}
        </if>
        <if test="'exception_time_list'.toString() == column.value">
          #{item.exceptionTimeList,jdbcType=VARCHAR}
        </if>
        <if test="'duration'.toString() == column.value">
          #{item.duration,jdbcType=DOUBLE}
        </if>
        <if test="'monitor_type'.toString() == column.value">
          #{item.monitorType,jdbcType=VARCHAR}
        </if>
        <if test="'monitor_level'.toString() == column.value">
          #{item.monitorLevel,jdbcType=VARCHAR}
        </if>
        <if test="'ali_uid'.toString() == column.value">
          #{item.aliUid,jdbcType=VARCHAR}
        </if>
        <if test="'nc_ip'.toString() == column.value">
          #{item.ncIp,jdbcType=VARCHAR}
        </if>
        <if test="'nc_id'.toString() == column.value">
          #{item.ncId,jdbcType=VARCHAR}
        </if>
        <if test="'sn'.toString() == column.value">
          #{item.sn,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>