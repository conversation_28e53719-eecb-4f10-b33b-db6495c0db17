<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.model.map.PatternAlarmRecordsMapper">
  <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.model.PatternAlarmRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="pattern" jdbcType="VARCHAR" property="pattern" />
    <result column="md5" jdbcType="VARCHAR" property="md5" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="kind" jdbcType="VARCHAR" property="kind" />
    <result column="level" jdbcType="TINYINT" property="level" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="receiver" jdbcType="VARCHAR" property="receiver" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="create_ms" jdbcType="BIGINT" property="createMs" />
    <result column="modify_ms" jdbcType="BIGINT" property="modifyMs" />
    <result column="creater" jdbcType="VARCHAR" property="creater" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, task_id, pattern, md5, `type`, kind, `level`, channel, token, receiver, ext, 
    create_ms, modify_ms, creater, modifier
  </sql>
  <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.model.PatternAlarmRecordsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pattern_alarm_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="example != null and example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from pattern_alarm_records
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from pattern_alarm_records
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from pattern_alarm_records
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from pattern_alarm_records
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.model.PatternAlarmRecordsExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from pattern_alarm_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.model.PatternAlarmRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pattern_alarm_records (task_id, pattern, md5, 
      `type`, kind, `level`, 
      channel, token, receiver, 
      ext, create_ms, modify_ms, 
      creater, modifier)
    values (#{taskId,jdbcType=BIGINT}, #{pattern,jdbcType=VARCHAR}, #{md5,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{kind,jdbcType=VARCHAR}, #{level,jdbcType=TINYINT}, 
      #{channel,jdbcType=VARCHAR}, #{token,jdbcType=VARCHAR}, #{receiver,jdbcType=VARCHAR}, 
      #{ext,jdbcType=VARCHAR}, #{createMs,jdbcType=BIGINT}, #{modifyMs,jdbcType=BIGINT}, 
      #{creater,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.model.PatternAlarmRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pattern_alarm_records
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="pattern != null">
        pattern,
      </if>
      <if test="md5 != null">
        md5,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="kind != null">
        kind,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="receiver != null">
        receiver,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="createMs != null">
        create_ms,
      </if>
      <if test="modifyMs != null">
        modify_ms,
      </if>
      <if test="creater != null">
        creater,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="pattern != null">
        #{pattern,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null">
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="kind != null">
        #{kind,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=TINYINT},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null">
        #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="createMs != null">
        #{createMs,jdbcType=BIGINT},
      </if>
      <if test="modifyMs != null">
        #{modifyMs,jdbcType=BIGINT},
      </if>
      <if test="creater != null">
        #{creater,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.model.PatternAlarmRecordsExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from pattern_alarm_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update pattern_alarm_records
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.pattern != null">
        pattern = #{record.pattern,jdbcType=VARCHAR},
      </if>
      <if test="record.md5 != null">
        md5 = #{record.md5,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.kind != null">
        kind = #{record.kind,jdbcType=VARCHAR},
      </if>
      <if test="record.level != null">
        `level` = #{record.level,jdbcType=TINYINT},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=VARCHAR},
      </if>
      <if test="record.token != null">
        token = #{record.token,jdbcType=VARCHAR},
      </if>
      <if test="record.receiver != null">
        receiver = #{record.receiver,jdbcType=VARCHAR},
      </if>
      <if test="record.ext != null">
        ext = #{record.ext,jdbcType=VARCHAR},
      </if>
      <if test="record.createMs != null">
        create_ms = #{record.createMs,jdbcType=BIGINT},
      </if>
      <if test="record.modifyMs != null">
        modify_ms = #{record.modifyMs,jdbcType=BIGINT},
      </if>
      <if test="record.creater != null">
        creater = #{record.creater,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update pattern_alarm_records
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      pattern = #{record.pattern,jdbcType=VARCHAR},
      md5 = #{record.md5,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=VARCHAR},
      kind = #{record.kind,jdbcType=VARCHAR},
      `level` = #{record.level,jdbcType=TINYINT},
      channel = #{record.channel,jdbcType=VARCHAR},
      token = #{record.token,jdbcType=VARCHAR},
      receiver = #{record.receiver,jdbcType=VARCHAR},
      ext = #{record.ext,jdbcType=VARCHAR},
      create_ms = #{record.createMs,jdbcType=BIGINT},
      modify_ms = #{record.modifyMs,jdbcType=BIGINT},
      creater = #{record.creater,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.xdragon.common.generate.model.PatternAlarmRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update pattern_alarm_records
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="pattern != null">
        pattern = #{pattern,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null">
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="kind != null">
        kind = #{kind,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        `level` = #{level,jdbcType=TINYINT},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null">
        receiver = #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="createMs != null">
        create_ms = #{createMs,jdbcType=BIGINT},
      </if>
      <if test="modifyMs != null">
        modify_ms = #{modifyMs,jdbcType=BIGINT},
      </if>
      <if test="creater != null">
        creater = #{creater,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.model.PatternAlarmRecords">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update pattern_alarm_records
    set task_id = #{taskId,jdbcType=BIGINT},
      pattern = #{pattern,jdbcType=VARCHAR},
      md5 = #{md5,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      kind = #{kind,jdbcType=VARCHAR},
      `level` = #{level,jdbcType=TINYINT},
      channel = #{channel,jdbcType=VARCHAR},
      token = #{token,jdbcType=VARCHAR},
      receiver = #{receiver,jdbcType=VARCHAR},
      ext = #{ext,jdbcType=VARCHAR},
      create_ms = #{createMs,jdbcType=BIGINT},
      modify_ms = #{modifyMs,jdbcType=BIGINT},
      creater = #{creater,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.model.PatternAlarmRecordsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pattern_alarm_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into pattern_alarm_records
    (task_id, pattern, md5, `type`, kind, `level`, channel, token, receiver, ext, create_ms, 
      modify_ms, creater, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.taskId,jdbcType=BIGINT}, #{item.pattern,jdbcType=VARCHAR}, #{item.md5,jdbcType=VARCHAR}, 
        #{item.type,jdbcType=VARCHAR}, #{item.kind,jdbcType=VARCHAR}, #{item.level,jdbcType=TINYINT}, 
        #{item.channel,jdbcType=VARCHAR}, #{item.token,jdbcType=VARCHAR}, #{item.receiver,jdbcType=VARCHAR}, 
        #{item.ext,jdbcType=VARCHAR}, #{item.createMs,jdbcType=BIGINT}, #{item.modifyMs,jdbcType=BIGINT}, 
        #{item.creater,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into pattern_alarm_records (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'task_id'.toString() == column.value">
          #{item.taskId,jdbcType=BIGINT}
        </if>
        <if test="'pattern'.toString() == column.value">
          #{item.pattern,jdbcType=VARCHAR}
        </if>
        <if test="'md5'.toString() == column.value">
          #{item.md5,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'kind'.toString() == column.value">
          #{item.kind,jdbcType=VARCHAR}
        </if>
        <if test="'level'.toString() == column.value">
          #{item.level,jdbcType=TINYINT}
        </if>
        <if test="'channel'.toString() == column.value">
          #{item.channel,jdbcType=VARCHAR}
        </if>
        <if test="'token'.toString() == column.value">
          #{item.token,jdbcType=VARCHAR}
        </if>
        <if test="'receiver'.toString() == column.value">
          #{item.receiver,jdbcType=VARCHAR}
        </if>
        <if test="'ext'.toString() == column.value">
          #{item.ext,jdbcType=VARCHAR}
        </if>
        <if test="'create_ms'.toString() == column.value">
          #{item.createMs,jdbcType=BIGINT}
        </if>
        <if test="'modify_ms'.toString() == column.value">
          #{item.modifyMs,jdbcType=BIGINT}
        </if>
        <if test="'creater'.toString() == column.value">
          #{item.creater,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
  <insert id="upsertSelective" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.PatternAlarmRecords" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into pattern_alarm_records
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="pattern != null">
        pattern,
      </if>
      <if test="md5 != null">
        md5,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="kind != null">
        kind,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="receiver != null">
        receiver,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="createMs != null">
        create_ms,
      </if>
      <if test="modifyMs != null">
        modify_ms,
      </if>
      <if test="creater != null">
        creater,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="pattern != null">
        #{pattern,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null">
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="kind != null">
        #{kind,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=TINYINT},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null">
        #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="createMs != null">
        #{createMs,jdbcType=BIGINT},
      </if>
      <if test="modifyMs != null">
        #{modifyMs,jdbcType=BIGINT},
      </if>
      <if test="creater != null">
        #{creater,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="pattern != null">
        pattern = #{pattern,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null">
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="kind != null">
        kind = #{kind,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        `level` = #{level,jdbcType=TINYINT},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null">
        receiver = #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="createMs != null">
        create_ms = #{createMs,jdbcType=BIGINT},
      </if>
      <if test="modifyMs != null">
        modify_ms = #{modifyMs,jdbcType=BIGINT},
      </if>
      <if test="creater != null">
        creater = #{creater,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="upsert" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.PatternAlarmRecords" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into pattern_alarm_records
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      task_id,
      pattern,
      md5,
      `type`,
      kind,
      `level`,
      channel,
      token,
      receiver,
      ext,
      create_ms,
      modify_ms,
      creater,
      modifier,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{taskId,jdbcType=BIGINT},
      #{pattern,jdbcType=VARCHAR},
      #{md5,jdbcType=VARCHAR},
      #{type,jdbcType=VARCHAR},
      #{kind,jdbcType=VARCHAR},
      #{level,jdbcType=TINYINT},
      #{channel,jdbcType=VARCHAR},
      #{token,jdbcType=VARCHAR},
      #{receiver,jdbcType=VARCHAR},
      #{ext,jdbcType=VARCHAR},
      #{createMs,jdbcType=BIGINT},
      #{modifyMs,jdbcType=BIGINT},
      #{creater,jdbcType=VARCHAR},
      #{modifier,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      task_id = #{taskId,jdbcType=BIGINT},
      pattern = #{pattern,jdbcType=VARCHAR},
      md5 = #{md5,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      kind = #{kind,jdbcType=VARCHAR},
      `level` = #{level,jdbcType=TINYINT},
      channel = #{channel,jdbcType=VARCHAR},
      token = #{token,jdbcType=VARCHAR},
      receiver = #{receiver,jdbcType=VARCHAR},
      ext = #{ext,jdbcType=VARCHAR},
      create_ms = #{createMs,jdbcType=BIGINT},
      modify_ms = #{modifyMs,jdbcType=BIGINT},
      creater = #{creater,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    </trim>
  </insert>
  <select id="selectOneByExample" parameterType="com.aliyun.xdragon.common.generate.model.PatternAlarmRecordsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from pattern_alarm_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from pattern_alarm_records
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
</mapper>