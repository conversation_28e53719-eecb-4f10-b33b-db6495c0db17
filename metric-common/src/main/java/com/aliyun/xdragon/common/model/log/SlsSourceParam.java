package com.aliyun.xdragon.common.model.log;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2022/06/15
 *{
 * "accountName": "xunjian",
 * "endpoint":"http://cn-wulanchabu.sls.xxx.com",
 * "project": "xdragon_log",
 * "logStore": "log_cluster",
 * "consumerGroup": "group",
 * "logField": "message",
 * "filterField": "level",
 * "filterString": "error,ERROR",
 * "extFields": "__source__,cnt",
 * "filterGamma": true,
 * "filterStatus": true
 * }
 */
@Data
public class SlsSourceParam implements SourceParam {
    private final static long serialVersionUID = 4621884630666058139L;

    @SerializedName("accountName")
    @Expose
    private String accountName;
    @SerializedName("endpoint")
    @Expose
    private String endpoint;
    @SerializedName("project")
    @Expose
    private String project;
    @SerializedName("logStore")
    @Expose
    private String logStore;
    @SerializedName("consumerGroup")
    @Expose
    private String consumerGroup;
    @SerializedName("logField")
    @Expose
    private String logField;
    @SerializedName("filterField")
    @Expose
    private String filterField;
    @SerializedName("filterString")
    @Expose
    private String filterString;
    @SerializedName("extFields")
    @Expose
    private String extFields;
    @SerializedName("filterGamma")
    @Expose
    private Boolean filterGamma = false;
    @SerializedName("filterStatus")
    @Expose
    private Boolean filterStatus = false;
    @SerializedName("batchGetSize")
    @Expose
    private Integer batchGetSize = 200;
    @SerializedName("filterContent")
    @Expose
    private Boolean filterContent = true;
    // 0: not join
    // 1: join region table
    @SerializedName("joinType")
    @Expose
    private Integer joinType = 1;

    @Override
    public SourceParam copy(){
        SlsSourceParam sourceParam = new SlsSourceParam();
        BeanUtils.copyProperties(this, sourceParam);
        return sourceParam;
    }
}
