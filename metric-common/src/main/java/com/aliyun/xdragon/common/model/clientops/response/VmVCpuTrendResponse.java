package com.aliyun.xdragon.common.model.clientops.response;

import com.aliyun.xdragon.common.model.clientops.HouYiInstanceInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @date 2024/03
 */
@Data
public class VmVCpuTrendResponse implements Serializable {
    private static final long serialVersionUID = 4623910906192949650L;

    private List<Map<String,Object>> nodeDateList;
    private List<HouYiInstanceInfo> instanceHistoryList;
    private Map<String,Object> vmRunningInfos;

}
