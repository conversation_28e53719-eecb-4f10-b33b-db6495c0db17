package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.WorkItemVm;
import com.aliyun.xdragon.common.generate.model.WorkItemVmExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface WorkItemVmMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    long countByExample(WorkItemVmExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int deleteByExample(WorkItemVmExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int insert(WorkItemVm record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int insertSelective(WorkItemVm record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    List<WorkItemVm> selectByExampleWithBLOBsWithRowbounds(WorkItemVmExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    List<WorkItemVm> selectByExampleSelective(@Param("example") WorkItemVmExample example, @Param("selective") WorkItemVm.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    WorkItemVm selectOneByExample(WorkItemVmExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    WorkItemVm selectOneByExampleSelective(@Param("example") WorkItemVmExample example, @Param("selective") WorkItemVm.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    WorkItemVm selectOneByExampleWithBLOBs(WorkItemVmExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    List<WorkItemVm> selectByExampleWithBLOBs(WorkItemVmExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    List<WorkItemVm> selectByExampleWithRowbounds(WorkItemVmExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    List<WorkItemVm> selectByExample(WorkItemVmExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    WorkItemVm selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") WorkItemVm.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    WorkItemVm selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") WorkItemVm record, @Param("example") WorkItemVmExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") WorkItemVm record, @Param("example") WorkItemVmExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") WorkItemVm record, @Param("example") WorkItemVmExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(WorkItemVm record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(WorkItemVm record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(WorkItemVm record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<WorkItemVm> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<WorkItemVm> list, @Param("selective") WorkItemVm.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int upsert(WorkItemVm record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int upsertSelective(WorkItemVm record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_vm
     *
     * @mbg.generated
     */
    int upsertWithBLOBs(WorkItemVm record);
}