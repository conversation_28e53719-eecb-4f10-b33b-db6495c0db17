package com.aliyun.xdragon.common.generate.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class MetricBatchRiskNewResource implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk_new_resource.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk_new_resource.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk_new_resource.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk_new_resource.risk_id
     *
     * @mbg.generated
     */
    private Long riskId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk_new_resource.nc_ip
     *
     * @mbg.generated
     */
    private String ncIp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk_new_resource.instance_id
     *
     * @mbg.generated
     */
    private String instanceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk_new_resource.target_type
     *
     * @mbg.generated
     */
    private String targetType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk_new_resource.exception_name
     *
     * @mbg.generated
     */
    private String exceptionName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk_new_resource.exception_time
     *
     * @mbg.generated
     */
    private Date exceptionTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk_new_resource.change_type
     *
     * @mbg.generated
     */
    private Byte changeType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk_new_resource.aliuid
     *
     * @mbg.generated
     */
    private String aliuid;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk_new_resource.id
     *
     * @return the value of metric_batch_risk_new_resource.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk_new_resource.id
     *
     * @param id the value for metric_batch_risk_new_resource.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk_new_resource.gmt_create
     *
     * @return the value of metric_batch_risk_new_resource.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk_new_resource.gmt_create
     *
     * @param gmtCreate the value for metric_batch_risk_new_resource.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk_new_resource.gmt_modified
     *
     * @return the value of metric_batch_risk_new_resource.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk_new_resource.gmt_modified
     *
     * @param gmtModified the value for metric_batch_risk_new_resource.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk_new_resource.risk_id
     *
     * @return the value of metric_batch_risk_new_resource.risk_id
     *
     * @mbg.generated
     */
    public Long getRiskId() {
        return riskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk_new_resource.risk_id
     *
     * @param riskId the value for metric_batch_risk_new_resource.risk_id
     *
     * @mbg.generated
     */
    public void setRiskId(Long riskId) {
        this.riskId = riskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk_new_resource.nc_ip
     *
     * @return the value of metric_batch_risk_new_resource.nc_ip
     *
     * @mbg.generated
     */
    public String getNcIp() {
        return ncIp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk_new_resource.nc_ip
     *
     * @param ncIp the value for metric_batch_risk_new_resource.nc_ip
     *
     * @mbg.generated
     */
    public void setNcIp(String ncIp) {
        this.ncIp = ncIp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk_new_resource.instance_id
     *
     * @return the value of metric_batch_risk_new_resource.instance_id
     *
     * @mbg.generated
     */
    public String getInstanceId() {
        return instanceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk_new_resource.instance_id
     *
     * @param instanceId the value for metric_batch_risk_new_resource.instance_id
     *
     * @mbg.generated
     */
    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk_new_resource.target_type
     *
     * @return the value of metric_batch_risk_new_resource.target_type
     *
     * @mbg.generated
     */
    public String getTargetType() {
        return targetType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk_new_resource.target_type
     *
     * @param targetType the value for metric_batch_risk_new_resource.target_type
     *
     * @mbg.generated
     */
    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk_new_resource.exception_name
     *
     * @return the value of metric_batch_risk_new_resource.exception_name
     *
     * @mbg.generated
     */
    public String getExceptionName() {
        return exceptionName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk_new_resource.exception_name
     *
     * @param exceptionName the value for metric_batch_risk_new_resource.exception_name
     *
     * @mbg.generated
     */
    public void setExceptionName(String exceptionName) {
        this.exceptionName = exceptionName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk_new_resource.exception_time
     *
     * @return the value of metric_batch_risk_new_resource.exception_time
     *
     * @mbg.generated
     */
    public Date getExceptionTime() {
        return exceptionTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk_new_resource.exception_time
     *
     * @param exceptionTime the value for metric_batch_risk_new_resource.exception_time
     *
     * @mbg.generated
     */
    public void setExceptionTime(Date exceptionTime) {
        this.exceptionTime = exceptionTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk_new_resource.change_type
     *
     * @return the value of metric_batch_risk_new_resource.change_type
     *
     * @mbg.generated
     */
    public Byte getChangeType() {
        return changeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk_new_resource.change_type
     *
     * @param changeType the value for metric_batch_risk_new_resource.change_type
     *
     * @mbg.generated
     */
    public void setChangeType(Byte changeType) {
        this.changeType = changeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk_new_resource.aliuid
     *
     * @return the value of metric_batch_risk_new_resource.aliuid
     *
     * @mbg.generated
     */
    public String getAliuid() {
        return aliuid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk_new_resource.aliuid
     *
     * @param aliuid the value for metric_batch_risk_new_resource.aliuid
     *
     * @mbg.generated
     */
    public void setAliuid(String aliuid) {
        this.aliuid = aliuid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MetricBatchRiskNewResource other = (MetricBatchRiskNewResource) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtModified() == null ? other.getGmtModified() == null : this.getGmtModified().equals(other.getGmtModified()))
            && (this.getRiskId() == null ? other.getRiskId() == null : this.getRiskId().equals(other.getRiskId()))
            && (this.getNcIp() == null ? other.getNcIp() == null : this.getNcIp().equals(other.getNcIp()))
            && (this.getInstanceId() == null ? other.getInstanceId() == null : this.getInstanceId().equals(other.getInstanceId()))
            && (this.getTargetType() == null ? other.getTargetType() == null : this.getTargetType().equals(other.getTargetType()))
            && (this.getExceptionName() == null ? other.getExceptionName() == null : this.getExceptionName().equals(other.getExceptionName()))
            && (this.getExceptionTime() == null ? other.getExceptionTime() == null : this.getExceptionTime().equals(other.getExceptionTime()))
            && (this.getChangeType() == null ? other.getChangeType() == null : this.getChangeType().equals(other.getChangeType()))
            && (this.getAliuid() == null ? other.getAliuid() == null : this.getAliuid().equals(other.getAliuid()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtModified() == null) ? 0 : getGmtModified().hashCode());
        result = prime * result + ((getRiskId() == null) ? 0 : getRiskId().hashCode());
        result = prime * result + ((getNcIp() == null) ? 0 : getNcIp().hashCode());
        result = prime * result + ((getInstanceId() == null) ? 0 : getInstanceId().hashCode());
        result = prime * result + ((getTargetType() == null) ? 0 : getTargetType().hashCode());
        result = prime * result + ((getExceptionName() == null) ? 0 : getExceptionName().hashCode());
        result = prime * result + ((getExceptionTime() == null) ? 0 : getExceptionTime().hashCode());
        result = prime * result + ((getChangeType() == null) ? 0 : getChangeType().hashCode());
        result = prime * result + ((getAliuid() == null) ? 0 : getAliuid().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", riskId=").append(riskId);
        sb.append(", ncIp=").append(ncIp);
        sb.append(", instanceId=").append(instanceId);
        sb.append(", targetType=").append(targetType);
        sb.append(", exceptionName=").append(exceptionName);
        sb.append(", exceptionTime=").append(exceptionTime);
        sb.append(", changeType=").append(changeType);
        sb.append(", aliuid=").append(aliuid);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    public enum Column {
        id("id", "id", "BIGINT", false),
        gmtCreate("gmt_create", "gmtCreate", "TIMESTAMP", false),
        gmtModified("gmt_modified", "gmtModified", "TIMESTAMP", false),
        riskId("risk_id", "riskId", "BIGINT", false),
        ncIp("nc_ip", "ncIp", "VARCHAR", false),
        instanceId("instance_id", "instanceId", "VARCHAR", false),
        targetType("target_type", "targetType", "VARCHAR", false),
        exceptionName("exception_name", "exceptionName", "VARCHAR", false),
        exceptionTime("exception_time", "exceptionTime", "TIMESTAMP", false),
        changeType("change_type", "changeType", "TINYINT", false),
        aliuid("aliuid", "aliuid", "VARCHAR", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk_new_resource
         *
         * @mbg.generated
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}