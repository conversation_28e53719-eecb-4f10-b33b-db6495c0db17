package com.aliyun.xdragon.common.enumeration;

/**
 * <AUTHOR>
 * @date 2022/05/16
 */

public enum MissingFillType {
    /**
     * 中值补齐
     */
    MEDIAN("median"),

    /**
     * 均值补齐
     */
    MEAN("mean"),

    /**
     * 零值补齐
     */
    ZERO("zero"),

    /**
     * 前值补齐
     */
    PREVIOUS("previous"),

    /**
     * 线性插值补齐
     */
    LINEAR("linear"),

    /**
     * 二次插值补齐
     */
    QUAD("quad"),

    /**
     * 三次插值补齐
     */
    CUBIC("cubic"),

    /**
     * 三次样条插值补齐
     */
    SPLINE("spline"),

    /**
     * 自回归模型预测补齐
     */
    AR("ar");


    private final String name;


    public String getName() {
        return name;
    }


    MissingFillType(String name) {
        this.name = name;
    }


    public static MissingFillType of(String name) {
        for (MissingFillType missingFillType : MissingFillType.values()) {
            if (missingFillType.getName().equals(name)) {
                return missingFillType;
            }
        }
        return null;
    }

}
