package com.aliyun.xdragon.common.model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/06/24
 */
public class SlsUserInfo implements Serializable {

    @SerializedName("name")
    @Expose
    private String name;
    @SerializedName("ak")
    @Expose
    private String ak;
    @SerializedName("sk")
    @Expose
    private String sk;
    @SerializedName("description")
    @Expose
    private String description;
    private final static long serialVersionUID = 2409567858483562337L;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAk() {
        return ak;
    }

    public void setAk(String ak) {
        this.ak = ak;
    }

    public String getSk() {
        return sk;
    }

    public void setSk(String sk) {
        this.sk = sk;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(SlsUserInfo.class.getName()).append('@').append(Integer.toHexString(System.identityHashCode(this)))
            .append('[');
        sb.append("name");
        sb.append('=');
        sb.append(((this.name == null) ? "<null>" : this.name));
        sb.append(',');
        sb.append("ak");
        sb.append('=');
        sb.append(((this.ak == null) ? "<null>" : this.ak));
        sb.append(',');
        sb.append("sk");
        sb.append('=');
        sb.append(((this.sk == null) ? "<null>" : this.sk));
        sb.append(',');
        sb.append("description");
        sb.append('=');
        sb.append(((this.description == null) ? "<null>" : this.description));
        sb.append(',');
        if (sb.charAt((sb.length() - 1)) == ',') {
            sb.setCharAt((sb.length() - 1), ']');
        } else {
            sb.append(']');
        }
        return sb.toString();
    }
}
