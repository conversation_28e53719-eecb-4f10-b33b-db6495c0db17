package com.aliyun.xdragon.common.util;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2022/07/13
 */
public class IpUtil {
    private static final Logger logger = LoggerFactory.getLogger(IpUtil.class);

    private static final int IPV4_PART_NUM = 4;

    public static int ipv4ToInt(String ipv4) {
        if (!isValidIpv4(ipv4)) {
            throw new IllegalArgumentException("Invalid ipv4 string.");
        }
        return ipv4ToIntUnsafe(ipv4);
    }

    public static int ipv4ToIntUnsafe(String ipv4) {
        int ipInt = 0;
        String[] parts = ipv4.split("\\.");
        for (int i = 0; i < IPV4_PART_NUM; ++i) {
            ipInt <<= 8;
            ipInt |= Integer.parseInt(parts[i]);
        }
        return ipInt;
    }

    public static String intToIpv4String(int ipInt) {
        char[] buffer = new char[16];
        int len = 0;
        int index = buffer.length - 1;

        int cur;
        int loopCnt = 0;
        while (loopCnt++ != IPV4_PART_NUM) {
            cur = ipInt & 0xff;
            ipInt >>>= 8;
            if (cur == 0) {
                buffer[index--] = '0';
                ++len;
            }
            while (cur > 0) {
                buffer[index--] = (char)('0' + (cur % 10));
                cur /= 10;
                ++len;
            }
            buffer[index--] = '.';
            ++len;
        }
        --len;

        return String.copyValueOf(buffer, buffer.length - len, len);
    }

    public static boolean isValidIpv4(String ipv4) {
        // Check for null and empty
        if (ipv4 == null || ipv4.isEmpty()) {
            return false;
        }

        // Check for part number
        String[] parts = ipv4.split("\\.");
        if (parts.length != IPV4_PART_NUM) {
            return false;
        }

        // Check for each parts
        int partInt;
        for (int i = 0; i < IPV4_PART_NUM; ++i) {
            partInt = -1;
            try {
                partInt = Integer.parseInt(parts[i]);
            } catch (NumberFormatException ex) {
                return false;
            }
            if (partInt > 255 || partInt < 0) {
                return false;
            }
        }
        return true;
    }

    public static String getCurrentIp() {
        String localip = null;// 本地IP，如果没有配置外网IP则返回它
        String netip = null;// 外网IP
        try {
            Enumeration<NetworkInterface> netInterfaces = NetworkInterface
                    .getNetworkInterfaces();
            InetAddress ip = null;
            boolean finded = false;// 是否找到外网IP
            while (netInterfaces.hasMoreElements() && !finded) {
                NetworkInterface ni = netInterfaces.nextElement();
                Enumeration<InetAddress> address = ni.getInetAddresses();
                while (address.hasMoreElements()) {
                    ip = address.nextElement();
                    if (!ip.isSiteLocalAddress() && !ip.isLoopbackAddress()
                            && ip.getHostAddress().indexOf(":") == -1) {// 外网IP
                        netip = ip.getHostAddress();
                        finded = true;
                        break;
                    } else if (ip.isSiteLocalAddress()
                            && !ip.isLoopbackAddress()
                            && ip.getHostAddress().indexOf(":") == -1) {// 内网IP
                        localip = ip.getHostAddress();
                    }
                }
            }
        } catch (SocketException e) {
            logger.error("getCurrentIp error", e);
        }
        if (!StringUtils.isBlank(netip)) {
            return netip;
        } else {
            return localip;
        }
    }
}
