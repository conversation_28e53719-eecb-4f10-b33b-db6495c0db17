package com.aliyun.xdragon.common.generate.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LogQualityScoreExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    public LogQualityScoreExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andPatternRateIsNull() {
            addCriterion("pattern_rate is null");
            return (Criteria) this;
        }

        public Criteria andPatternRateIsNotNull() {
            addCriterion("pattern_rate is not null");
            return (Criteria) this;
        }

        public Criteria andPatternRateEqualTo(Double value) {
            addCriterion("pattern_rate =", value, "patternRate");
            return (Criteria) this;
        }

        public Criteria andPatternRateNotEqualTo(Double value) {
            addCriterion("pattern_rate <>", value, "patternRate");
            return (Criteria) this;
        }

        public Criteria andPatternRateGreaterThan(Double value) {
            addCriterion("pattern_rate >", value, "patternRate");
            return (Criteria) this;
        }

        public Criteria andPatternRateGreaterThanOrEqualTo(Double value) {
            addCriterion("pattern_rate >=", value, "patternRate");
            return (Criteria) this;
        }

        public Criteria andPatternRateLessThan(Double value) {
            addCriterion("pattern_rate <", value, "patternRate");
            return (Criteria) this;
        }

        public Criteria andPatternRateLessThanOrEqualTo(Double value) {
            addCriterion("pattern_rate <=", value, "patternRate");
            return (Criteria) this;
        }

        public Criteria andPatternRateIn(List<Double> values) {
            addCriterion("pattern_rate in", values, "patternRate");
            return (Criteria) this;
        }

        public Criteria andPatternRateNotIn(List<Double> values) {
            addCriterion("pattern_rate not in", values, "patternRate");
            return (Criteria) this;
        }

        public Criteria andPatternRateBetween(Double value1, Double value2) {
            addCriterion("pattern_rate between", value1, value2, "patternRate");
            return (Criteria) this;
        }

        public Criteria andPatternRateNotBetween(Double value1, Double value2) {
            addCriterion("pattern_rate not between", value1, value2, "patternRate");
            return (Criteria) this;
        }

        public Criteria andNewlyCountIsNull() {
            addCriterion("newly_count is null");
            return (Criteria) this;
        }

        public Criteria andNewlyCountIsNotNull() {
            addCriterion("newly_count is not null");
            return (Criteria) this;
        }

        public Criteria andNewlyCountEqualTo(Integer value) {
            addCriterion("newly_count =", value, "newlyCount");
            return (Criteria) this;
        }

        public Criteria andNewlyCountNotEqualTo(Integer value) {
            addCriterion("newly_count <>", value, "newlyCount");
            return (Criteria) this;
        }

        public Criteria andNewlyCountGreaterThan(Integer value) {
            addCriterion("newly_count >", value, "newlyCount");
            return (Criteria) this;
        }

        public Criteria andNewlyCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("newly_count >=", value, "newlyCount");
            return (Criteria) this;
        }

        public Criteria andNewlyCountLessThan(Integer value) {
            addCriterion("newly_count <", value, "newlyCount");
            return (Criteria) this;
        }

        public Criteria andNewlyCountLessThanOrEqualTo(Integer value) {
            addCriterion("newly_count <=", value, "newlyCount");
            return (Criteria) this;
        }

        public Criteria andNewlyCountIn(List<Integer> values) {
            addCriterion("newly_count in", values, "newlyCount");
            return (Criteria) this;
        }

        public Criteria andNewlyCountNotIn(List<Integer> values) {
            addCriterion("newly_count not in", values, "newlyCount");
            return (Criteria) this;
        }

        public Criteria andNewlyCountBetween(Integer value1, Integer value2) {
            addCriterion("newly_count between", value1, value2, "newlyCount");
            return (Criteria) this;
        }

        public Criteria andNewlyCountNotBetween(Integer value1, Integer value2) {
            addCriterion("newly_count not between", value1, value2, "newlyCount");
            return (Criteria) this;
        }

        public Criteria andAnomalyScoreIsNull() {
            addCriterion("anomaly_score is null");
            return (Criteria) this;
        }

        public Criteria andAnomalyScoreIsNotNull() {
            addCriterion("anomaly_score is not null");
            return (Criteria) this;
        }

        public Criteria andAnomalyScoreEqualTo(Double value) {
            addCriterion("anomaly_score =", value, "anomalyScore");
            return (Criteria) this;
        }

        public Criteria andAnomalyScoreNotEqualTo(Double value) {
            addCriterion("anomaly_score <>", value, "anomalyScore");
            return (Criteria) this;
        }

        public Criteria andAnomalyScoreGreaterThan(Double value) {
            addCriterion("anomaly_score >", value, "anomalyScore");
            return (Criteria) this;
        }

        public Criteria andAnomalyScoreGreaterThanOrEqualTo(Double value) {
            addCriterion("anomaly_score >=", value, "anomalyScore");
            return (Criteria) this;
        }

        public Criteria andAnomalyScoreLessThan(Double value) {
            addCriterion("anomaly_score <", value, "anomalyScore");
            return (Criteria) this;
        }

        public Criteria andAnomalyScoreLessThanOrEqualTo(Double value) {
            addCriterion("anomaly_score <=", value, "anomalyScore");
            return (Criteria) this;
        }

        public Criteria andAnomalyScoreIn(List<Double> values) {
            addCriterion("anomaly_score in", values, "anomalyScore");
            return (Criteria) this;
        }

        public Criteria andAnomalyScoreNotIn(List<Double> values) {
            addCriterion("anomaly_score not in", values, "anomalyScore");
            return (Criteria) this;
        }

        public Criteria andAnomalyScoreBetween(Double value1, Double value2) {
            addCriterion("anomaly_score between", value1, value2, "anomalyScore");
            return (Criteria) this;
        }

        public Criteria andAnomalyScoreNotBetween(Double value1, Double value2) {
            addCriterion("anomaly_score not between", value1, value2, "anomalyScore");
            return (Criteria) this;
        }

        public Criteria andAppHealthScoreIsNull() {
            addCriterion("app_health_score is null");
            return (Criteria) this;
        }

        public Criteria andAppHealthScoreIsNotNull() {
            addCriterion("app_health_score is not null");
            return (Criteria) this;
        }

        public Criteria andAppHealthScoreEqualTo(Double value) {
            addCriterion("app_health_score =", value, "appHealthScore");
            return (Criteria) this;
        }

        public Criteria andAppHealthScoreNotEqualTo(Double value) {
            addCriterion("app_health_score <>", value, "appHealthScore");
            return (Criteria) this;
        }

        public Criteria andAppHealthScoreGreaterThan(Double value) {
            addCriterion("app_health_score >", value, "appHealthScore");
            return (Criteria) this;
        }

        public Criteria andAppHealthScoreGreaterThanOrEqualTo(Double value) {
            addCriterion("app_health_score >=", value, "appHealthScore");
            return (Criteria) this;
        }

        public Criteria andAppHealthScoreLessThan(Double value) {
            addCriterion("app_health_score <", value, "appHealthScore");
            return (Criteria) this;
        }

        public Criteria andAppHealthScoreLessThanOrEqualTo(Double value) {
            addCriterion("app_health_score <=", value, "appHealthScore");
            return (Criteria) this;
        }

        public Criteria andAppHealthScoreIn(List<Double> values) {
            addCriterion("app_health_score in", values, "appHealthScore");
            return (Criteria) this;
        }

        public Criteria andAppHealthScoreNotIn(List<Double> values) {
            addCriterion("app_health_score not in", values, "appHealthScore");
            return (Criteria) this;
        }

        public Criteria andAppHealthScoreBetween(Double value1, Double value2) {
            addCriterion("app_health_score between", value1, value2, "appHealthScore");
            return (Criteria) this;
        }

        public Criteria andAppHealthScoreNotBetween(Double value1, Double value2) {
            addCriterion("app_health_score not between", value1, value2, "appHealthScore");
            return (Criteria) this;
        }

        public Criteria andDaySmoothScoreIsNull() {
            addCriterion("day_smooth_score is null");
            return (Criteria) this;
        }

        public Criteria andDaySmoothScoreIsNotNull() {
            addCriterion("day_smooth_score is not null");
            return (Criteria) this;
        }

        public Criteria andDaySmoothScoreEqualTo(Double value) {
            addCriterion("day_smooth_score =", value, "daySmoothScore");
            return (Criteria) this;
        }

        public Criteria andDaySmoothScoreNotEqualTo(Double value) {
            addCriterion("day_smooth_score <>", value, "daySmoothScore");
            return (Criteria) this;
        }

        public Criteria andDaySmoothScoreGreaterThan(Double value) {
            addCriterion("day_smooth_score >", value, "daySmoothScore");
            return (Criteria) this;
        }

        public Criteria andDaySmoothScoreGreaterThanOrEqualTo(Double value) {
            addCriterion("day_smooth_score >=", value, "daySmoothScore");
            return (Criteria) this;
        }

        public Criteria andDaySmoothScoreLessThan(Double value) {
            addCriterion("day_smooth_score <", value, "daySmoothScore");
            return (Criteria) this;
        }

        public Criteria andDaySmoothScoreLessThanOrEqualTo(Double value) {
            addCriterion("day_smooth_score <=", value, "daySmoothScore");
            return (Criteria) this;
        }

        public Criteria andDaySmoothScoreIn(List<Double> values) {
            addCriterion("day_smooth_score in", values, "daySmoothScore");
            return (Criteria) this;
        }

        public Criteria andDaySmoothScoreNotIn(List<Double> values) {
            addCriterion("day_smooth_score not in", values, "daySmoothScore");
            return (Criteria) this;
        }

        public Criteria andDaySmoothScoreBetween(Double value1, Double value2) {
            addCriterion("day_smooth_score between", value1, value2, "daySmoothScore");
            return (Criteria) this;
        }

        public Criteria andDaySmoothScoreNotBetween(Double value1, Double value2) {
            addCriterion("day_smooth_score not between", value1, value2, "daySmoothScore");
            return (Criteria) this;
        }

        public Criteria andPatternCountIsNull() {
            addCriterion("pattern_count is null");
            return (Criteria) this;
        }

        public Criteria andPatternCountIsNotNull() {
            addCriterion("pattern_count is not null");
            return (Criteria) this;
        }

        public Criteria andPatternCountEqualTo(Integer value) {
            addCriterion("pattern_count =", value, "patternCount");
            return (Criteria) this;
        }

        public Criteria andPatternCountNotEqualTo(Integer value) {
            addCriterion("pattern_count <>", value, "patternCount");
            return (Criteria) this;
        }

        public Criteria andPatternCountGreaterThan(Integer value) {
            addCriterion("pattern_count >", value, "patternCount");
            return (Criteria) this;
        }

        public Criteria andPatternCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("pattern_count >=", value, "patternCount");
            return (Criteria) this;
        }

        public Criteria andPatternCountLessThan(Integer value) {
            addCriterion("pattern_count <", value, "patternCount");
            return (Criteria) this;
        }

        public Criteria andPatternCountLessThanOrEqualTo(Integer value) {
            addCriterion("pattern_count <=", value, "patternCount");
            return (Criteria) this;
        }

        public Criteria andPatternCountIn(List<Integer> values) {
            addCriterion("pattern_count in", values, "patternCount");
            return (Criteria) this;
        }

        public Criteria andPatternCountNotIn(List<Integer> values) {
            addCriterion("pattern_count not in", values, "patternCount");
            return (Criteria) this;
        }

        public Criteria andPatternCountBetween(Integer value1, Integer value2) {
            addCriterion("pattern_count between", value1, value2, "patternCount");
            return (Criteria) this;
        }

        public Criteria andPatternCountNotBetween(Integer value1, Integer value2) {
            addCriterion("pattern_count not between", value1, value2, "patternCount");
            return (Criteria) this;
        }

        public Criteria andAnomalyCountIsNull() {
            addCriterion("anomaly_count is null");
            return (Criteria) this;
        }

        public Criteria andAnomalyCountIsNotNull() {
            addCriterion("anomaly_count is not null");
            return (Criteria) this;
        }

        public Criteria andAnomalyCountEqualTo(Double value) {
            addCriterion("anomaly_count =", value, "anomalyCount");
            return (Criteria) this;
        }

        public Criteria andAnomalyCountNotEqualTo(Double value) {
            addCriterion("anomaly_count <>", value, "anomalyCount");
            return (Criteria) this;
        }

        public Criteria andAnomalyCountGreaterThan(Double value) {
            addCriterion("anomaly_count >", value, "anomalyCount");
            return (Criteria) this;
        }

        public Criteria andAnomalyCountGreaterThanOrEqualTo(Double value) {
            addCriterion("anomaly_count >=", value, "anomalyCount");
            return (Criteria) this;
        }

        public Criteria andAnomalyCountLessThan(Double value) {
            addCriterion("anomaly_count <", value, "anomalyCount");
            return (Criteria) this;
        }

        public Criteria andAnomalyCountLessThanOrEqualTo(Double value) {
            addCriterion("anomaly_count <=", value, "anomalyCount");
            return (Criteria) this;
        }

        public Criteria andAnomalyCountIn(List<Double> values) {
            addCriterion("anomaly_count in", values, "anomalyCount");
            return (Criteria) this;
        }

        public Criteria andAnomalyCountNotIn(List<Double> values) {
            addCriterion("anomaly_count not in", values, "anomalyCount");
            return (Criteria) this;
        }

        public Criteria andAnomalyCountBetween(Double value1, Double value2) {
            addCriterion("anomaly_count between", value1, value2, "anomalyCount");
            return (Criteria) this;
        }

        public Criteria andAnomalyCountNotBetween(Double value1, Double value2) {
            addCriterion("anomaly_count not between", value1, value2, "anomalyCount");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table log_quality_score
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table log_quality_score
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}