package com.aliyun.xdragon.common.model.log.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

import java.io.Serializable;
import java.util.List;

@Data
public class SubmitOrUpdatePatternMarkRequest implements Serializable {

    private static final long serialVersionUID = 4006427043554476958L;
    /**
     * task id
     */
    @NotNull(message = "task is null")
    @Positive
    private Long taskId;

    private List<String> patternMd5;

    private List<String> pattern;
    /**
     * 模式选择：忽略，保持观察，关注，告警
     */
    private List<String> patternCheckList;
    /**
     * 模式打标的日志原因
     */
    private String logReason;
    /**
     * 告警配置
     */
    private String alarmConfig;


}
