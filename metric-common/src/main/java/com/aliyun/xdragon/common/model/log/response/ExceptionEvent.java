package com.aliyun.xdragon.common.model.log.response;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/21
 */
@Data
public class ExceptionEvent implements Serializable {
    private static final long serialVersionUID = -332569930821273458L;
    private String exceptionCount;
        private String exceptionType;
        private String warningLevel;
        private String lastExceptionTime;
        private String exceptionTime;
        private String ncIp;
        private String additionalInfo;
        private String exceptionName;
        private String exceptionCondition;
        private String reason;
        private String machineId;
}
