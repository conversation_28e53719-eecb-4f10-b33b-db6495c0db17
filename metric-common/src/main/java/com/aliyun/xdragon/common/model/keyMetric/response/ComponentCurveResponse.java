package com.aliyun.xdragon.common.model.keyMetric.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class ComponentCurveResponse implements Serializable {
    private static final long serialVersionUID = -6339637957280570058L;

    /**
     * <date, value>
     */
    Map<String, String> performanceCurve;
    Map<String, String> unavailabilityCurve;
    Map<String, String> controlCurve;

    public ComponentCurveResponse() {
    }
}
