package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.model.ChartFeedbackInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ChartFeedbackInfoMapper {

    List<ChartFeedbackInfo> queryInfoByMessageId(@Param("messageId") Long messageId);

    List<ChartFeedbackInfo> queryTestCaseInfo(@Param("startDate") Date start, @Param("endDate") Date end);

    int saveFeedbackMessage(ChartFeedbackInfo chartFeedbackInfo);

    int updateFeedbackContent(@Param("messageId") Long messageId, @Param("feedbackContent") String feedbackContent, @Param("approve") Byte approve);

    int expireFeedback(@Param("messageId") Long messageId);
}
