package com.aliyun.xdragon.common.generate.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

public class LogCoverageConfig implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.gmt_create
     *
     * @mbg.generated
     */
    private Long gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.gmt_modified
     *
     * @mbg.generated
     */
    private Long gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.metric_name
     *
     * @mbg.generated
     */
    private String metricName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.logstore
     *
     * @mbg.generated
     */
    private String logstore;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.project
     *
     * @mbg.generated
     */
    private String project;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.region
     *
     * @mbg.generated
     */
    private String region;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.is_cycle
     *
     * @mbg.generated
     */
    private Boolean isCycle;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.data_interval
     *
     * @mbg.generated
     */
    private Integer dataInterval;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.is_enabled
     *
     * @mbg.generated
     */
    private Boolean isEnabled;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.sls_query
     *
     * @mbg.generated
     */
    private String slsQuery;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.query_step
     *
     * @mbg.generated
     */
    private Integer queryStep;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.check_empty_nc
     *
     * @mbg.generated
     */
    private Boolean checkEmptyNc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.nc_key
     *
     * @mbg.generated
     */
    private String ncKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.need_fix
     *
     * @mbg.generated
     */
    private Boolean needFix;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.query_type
     *
     * @mbg.generated
     */
    private Integer queryType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_coverage_config.nc_filter_type
     *
     * @mbg.generated
     */
    private Integer ncFilterType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table log_coverage_config
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.id
     *
     * @return the value of log_coverage_config.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.id
     *
     * @param id the value for log_coverage_config.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.gmt_create
     *
     * @return the value of log_coverage_config.gmt_create
     *
     * @mbg.generated
     */
    public Long getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.gmt_create
     *
     * @param gmtCreate the value for log_coverage_config.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Long gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.gmt_modified
     *
     * @return the value of log_coverage_config.gmt_modified
     *
     * @mbg.generated
     */
    public Long getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.gmt_modified
     *
     * @param gmtModified the value for log_coverage_config.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Long gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.metric_name
     *
     * @return the value of log_coverage_config.metric_name
     *
     * @mbg.generated
     */
    public String getMetricName() {
        return metricName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.metric_name
     *
     * @param metricName the value for log_coverage_config.metric_name
     *
     * @mbg.generated
     */
    public void setMetricName(String metricName) {
        this.metricName = metricName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.logstore
     *
     * @return the value of log_coverage_config.logstore
     *
     * @mbg.generated
     */
    public String getLogstore() {
        return logstore;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.logstore
     *
     * @param logstore the value for log_coverage_config.logstore
     *
     * @mbg.generated
     */
    public void setLogstore(String logstore) {
        this.logstore = logstore;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.project
     *
     * @return the value of log_coverage_config.project
     *
     * @mbg.generated
     */
    public String getProject() {
        return project;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.project
     *
     * @param project the value for log_coverage_config.project
     *
     * @mbg.generated
     */
    public void setProject(String project) {
        this.project = project;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.region
     *
     * @return the value of log_coverage_config.region
     *
     * @mbg.generated
     */
    public String getRegion() {
        return region;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.region
     *
     * @param region the value for log_coverage_config.region
     *
     * @mbg.generated
     */
    public void setRegion(String region) {
        this.region = region;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.is_cycle
     *
     * @return the value of log_coverage_config.is_cycle
     *
     * @mbg.generated
     */
    public Boolean getIsCycle() {
        return isCycle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.is_cycle
     *
     * @param isCycle the value for log_coverage_config.is_cycle
     *
     * @mbg.generated
     */
    public void setIsCycle(Boolean isCycle) {
        this.isCycle = isCycle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.data_interval
     *
     * @return the value of log_coverage_config.data_interval
     *
     * @mbg.generated
     */
    public Integer getDataInterval() {
        return dataInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.data_interval
     *
     * @param dataInterval the value for log_coverage_config.data_interval
     *
     * @mbg.generated
     */
    public void setDataInterval(Integer dataInterval) {
        this.dataInterval = dataInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.is_enabled
     *
     * @return the value of log_coverage_config.is_enabled
     *
     * @mbg.generated
     */
    public Boolean getIsEnabled() {
        return isEnabled;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.is_enabled
     *
     * @param isEnabled the value for log_coverage_config.is_enabled
     *
     * @mbg.generated
     */
    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.sls_query
     *
     * @return the value of log_coverage_config.sls_query
     *
     * @mbg.generated
     */
    public String getSlsQuery() {
        return slsQuery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.sls_query
     *
     * @param slsQuery the value for log_coverage_config.sls_query
     *
     * @mbg.generated
     */
    public void setSlsQuery(String slsQuery) {
        this.slsQuery = slsQuery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.query_step
     *
     * @return the value of log_coverage_config.query_step
     *
     * @mbg.generated
     */
    public Integer getQueryStep() {
        return queryStep;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.query_step
     *
     * @param queryStep the value for log_coverage_config.query_step
     *
     * @mbg.generated
     */
    public void setQueryStep(Integer queryStep) {
        this.queryStep = queryStep;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.check_empty_nc
     *
     * @return the value of log_coverage_config.check_empty_nc
     *
     * @mbg.generated
     */
    public Boolean getCheckEmptyNc() {
        return checkEmptyNc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.check_empty_nc
     *
     * @param checkEmptyNc the value for log_coverage_config.check_empty_nc
     *
     * @mbg.generated
     */
    public void setCheckEmptyNc(Boolean checkEmptyNc) {
        this.checkEmptyNc = checkEmptyNc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.nc_key
     *
     * @return the value of log_coverage_config.nc_key
     *
     * @mbg.generated
     */
    public String getNcKey() {
        return ncKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.nc_key
     *
     * @param ncKey the value for log_coverage_config.nc_key
     *
     * @mbg.generated
     */
    public void setNcKey(String ncKey) {
        this.ncKey = ncKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.need_fix
     *
     * @return the value of log_coverage_config.need_fix
     *
     * @mbg.generated
     */
    public Boolean getNeedFix() {
        return needFix;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.need_fix
     *
     * @param needFix the value for log_coverage_config.need_fix
     *
     * @mbg.generated
     */
    public void setNeedFix(Boolean needFix) {
        this.needFix = needFix;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.query_type
     *
     * @return the value of log_coverage_config.query_type
     *
     * @mbg.generated
     */
    public Integer getQueryType() {
        return queryType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.query_type
     *
     * @param queryType the value for log_coverage_config.query_type
     *
     * @mbg.generated
     */
    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_coverage_config.nc_filter_type
     *
     * @return the value of log_coverage_config.nc_filter_type
     *
     * @mbg.generated
     */
    public Integer getNcFilterType() {
        return ncFilterType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_coverage_config.nc_filter_type
     *
     * @param ncFilterType the value for log_coverage_config.nc_filter_type
     *
     * @mbg.generated
     */
    public void setNcFilterType(Integer ncFilterType) {
        this.ncFilterType = ncFilterType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_config
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        LogCoverageConfig other = (LogCoverageConfig) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtModified() == null ? other.getGmtModified() == null : this.getGmtModified().equals(other.getGmtModified()))
            && (this.getMetricName() == null ? other.getMetricName() == null : this.getMetricName().equals(other.getMetricName()))
            && (this.getLogstore() == null ? other.getLogstore() == null : this.getLogstore().equals(other.getLogstore()))
            && (this.getProject() == null ? other.getProject() == null : this.getProject().equals(other.getProject()))
            && (this.getRegion() == null ? other.getRegion() == null : this.getRegion().equals(other.getRegion()))
            && (this.getIsCycle() == null ? other.getIsCycle() == null : this.getIsCycle().equals(other.getIsCycle()))
            && (this.getDataInterval() == null ? other.getDataInterval() == null : this.getDataInterval().equals(other.getDataInterval()))
            && (this.getIsEnabled() == null ? other.getIsEnabled() == null : this.getIsEnabled().equals(other.getIsEnabled()))
            && (this.getSlsQuery() == null ? other.getSlsQuery() == null : this.getSlsQuery().equals(other.getSlsQuery()))
            && (this.getQueryStep() == null ? other.getQueryStep() == null : this.getQueryStep().equals(other.getQueryStep()))
            && (this.getCheckEmptyNc() == null ? other.getCheckEmptyNc() == null : this.getCheckEmptyNc().equals(other.getCheckEmptyNc()))
            && (this.getNcKey() == null ? other.getNcKey() == null : this.getNcKey().equals(other.getNcKey()))
            && (this.getNeedFix() == null ? other.getNeedFix() == null : this.getNeedFix().equals(other.getNeedFix()))
            && (this.getQueryType() == null ? other.getQueryType() == null : this.getQueryType().equals(other.getQueryType()))
            && (this.getNcFilterType() == null ? other.getNcFilterType() == null : this.getNcFilterType().equals(other.getNcFilterType()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_config
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtModified() == null) ? 0 : getGmtModified().hashCode());
        result = prime * result + ((getMetricName() == null) ? 0 : getMetricName().hashCode());
        result = prime * result + ((getLogstore() == null) ? 0 : getLogstore().hashCode());
        result = prime * result + ((getProject() == null) ? 0 : getProject().hashCode());
        result = prime * result + ((getRegion() == null) ? 0 : getRegion().hashCode());
        result = prime * result + ((getIsCycle() == null) ? 0 : getIsCycle().hashCode());
        result = prime * result + ((getDataInterval() == null) ? 0 : getDataInterval().hashCode());
        result = prime * result + ((getIsEnabled() == null) ? 0 : getIsEnabled().hashCode());
        result = prime * result + ((getSlsQuery() == null) ? 0 : getSlsQuery().hashCode());
        result = prime * result + ((getQueryStep() == null) ? 0 : getQueryStep().hashCode());
        result = prime * result + ((getCheckEmptyNc() == null) ? 0 : getCheckEmptyNc().hashCode());
        result = prime * result + ((getNcKey() == null) ? 0 : getNcKey().hashCode());
        result = prime * result + ((getNeedFix() == null) ? 0 : getNeedFix().hashCode());
        result = prime * result + ((getQueryType() == null) ? 0 : getQueryType().hashCode());
        result = prime * result + ((getNcFilterType() == null) ? 0 : getNcFilterType().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_config
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", metricName=").append(metricName);
        sb.append(", logstore=").append(logstore);
        sb.append(", project=").append(project);
        sb.append(", region=").append(region);
        sb.append(", isCycle=").append(isCycle);
        sb.append(", dataInterval=").append(dataInterval);
        sb.append(", isEnabled=").append(isEnabled);
        sb.append(", slsQuery=").append(slsQuery);
        sb.append(", queryStep=").append(queryStep);
        sb.append(", checkEmptyNc=").append(checkEmptyNc);
        sb.append(", ncKey=").append(ncKey);
        sb.append(", needFix=").append(needFix);
        sb.append(", queryType=").append(queryType);
        sb.append(", ncFilterType=").append(ncFilterType);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table log_coverage_config
     *
     * @mbg.generated
     */
    public enum Column {
        id("id", "id", "BIGINT", false),
        gmtCreate("gmt_create", "gmtCreate", "BIGINT", false),
        gmtModified("gmt_modified", "gmtModified", "BIGINT", false),
        metricName("metric_name", "metricName", "VARCHAR", false),
        logstore("logstore", "logstore", "VARCHAR", false),
        project("project", "project", "VARCHAR", false),
        region("region", "region", "VARCHAR", false),
        isCycle("is_cycle", "isCycle", "BIT", false),
        dataInterval("data_interval", "dataInterval", "INTEGER", false),
        isEnabled("is_enabled", "isEnabled", "BIT", false),
        slsQuery("sls_query", "slsQuery", "VARCHAR", false),
        queryStep("query_step", "queryStep", "INTEGER", false),
        checkEmptyNc("check_empty_nc", "checkEmptyNc", "BIT", false),
        ncKey("nc_key", "ncKey", "VARCHAR", false),
        needFix("need_fix", "needFix", "BIT", false),
        queryType("query_type", "queryType", "INTEGER", false),
        ncFilterType("nc_filter_type", "ncFilterType", "INTEGER", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_coverage_config
         *
         * @mbg.generated
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}