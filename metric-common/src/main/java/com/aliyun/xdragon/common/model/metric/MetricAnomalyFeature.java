package com.aliyun.xdragon.common.model.metric;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/08/23
 */
@Data
public class MetricAnomalyFeature implements Serializable {

    private static final long serialVersionUID = 2972032835443340491L;

    private double score;
    private String instanceId;
    private String ncIp;
    private List<HashMap<String, String>> features;

    public MetricAnomalyFeature(String instanceId, String ncIp) {
        this.instanceId = instanceId;
        this.ncIp = ncIp;
        this.features = new ArrayList<>();
    }

    public void addFeature(HashMap<String, String> feature) {
        features.add(feature);
    }
}