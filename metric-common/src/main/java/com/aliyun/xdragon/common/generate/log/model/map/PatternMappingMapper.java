package com.aliyun.xdragon.common.generate.log.model.map;

import com.aliyun.xdragon.common.generate.log.model.PatternMapping;
import com.aliyun.xdragon.common.generate.log.model.PatternMappingExample;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface PatternMappingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    long countByExample(PatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int deleteByExample(PatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Date time);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int insert(PatternMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int insertSelective(PatternMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    List<PatternMapping> selectByExampleWithBLOBsWithRowbounds(PatternMappingExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    List<PatternMapping> selectByExampleSelective(@Param("example") PatternMappingExample example, @Param("selective") PatternMapping.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    PatternMapping selectOneByExample(PatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    PatternMapping selectOneByExampleSelective(@Param("example") PatternMappingExample example, @Param("selective") PatternMapping.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    PatternMapping selectOneByExampleWithBLOBs(PatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    List<PatternMapping> selectByExampleWithBLOBs(PatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    List<PatternMapping> selectByExampleWithRowbounds(PatternMappingExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    List<PatternMapping> selectByExample(PatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    PatternMapping selectByPrimaryKeySelective(@Param("time") Date time, @Param("selective") PatternMapping.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    PatternMapping selectByPrimaryKey(Date time);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") PatternMapping record, @Param("example") PatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") PatternMapping record, @Param("example") PatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") PatternMapping record, @Param("example") PatternMappingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(PatternMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(PatternMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(PatternMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<PatternMapping> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<PatternMapping> list, @Param("selective") PatternMapping.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int upsert(PatternMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int upsertSelective(PatternMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table pattern_mapping
     *
     * @mbg.generated
     */
    int upsertWithBLOBs(PatternMapping record);
}