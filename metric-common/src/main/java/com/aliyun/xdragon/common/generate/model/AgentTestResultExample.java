package com.aliyun.xdragon.common.generate.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AgentTestResultExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    public AgentTestResultExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTimeIsNull() {
            addCriterion("`time` is null");
            return (Criteria) this;
        }

        public Criteria andTimeIsNotNull() {
            addCriterion("`time` is not null");
            return (Criteria) this;
        }

        public Criteria andTimeEqualTo(Date value) {
            addCriterion("`time` =", value, "time");
            return (Criteria) this;
        }

        public Criteria andTimeNotEqualTo(Date value) {
            addCriterion("`time` <>", value, "time");
            return (Criteria) this;
        }

        public Criteria andTimeGreaterThan(Date value) {
            addCriterion("`time` >", value, "time");
            return (Criteria) this;
        }

        public Criteria andTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("`time` >=", value, "time");
            return (Criteria) this;
        }

        public Criteria andTimeLessThan(Date value) {
            addCriterion("`time` <", value, "time");
            return (Criteria) this;
        }

        public Criteria andTimeLessThanOrEqualTo(Date value) {
            addCriterion("`time` <=", value, "time");
            return (Criteria) this;
        }

        public Criteria andTimeIn(List<Date> values) {
            addCriterion("`time` in", values, "time");
            return (Criteria) this;
        }

        public Criteria andTimeNotIn(List<Date> values) {
            addCriterion("`time` not in", values, "time");
            return (Criteria) this;
        }

        public Criteria andTimeBetween(Date value1, Date value2) {
            addCriterion("`time` between", value1, value2, "time");
            return (Criteria) this;
        }

        public Criteria andTimeNotBetween(Date value1, Date value2) {
            addCriterion("`time` not between", value1, value2, "time");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("`type` is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("`type` is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("`type` =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("`type` <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("`type` >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("`type` >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("`type` <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("`type` <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("`type` like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("`type` not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("`type` in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("`type` not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("`type` between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("`type` not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTestCntIsNull() {
            addCriterion("test_cnt is null");
            return (Criteria) this;
        }

        public Criteria andTestCntIsNotNull() {
            addCriterion("test_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andTestCntEqualTo(Integer value) {
            addCriterion("test_cnt =", value, "testCnt");
            return (Criteria) this;
        }

        public Criteria andTestCntNotEqualTo(Integer value) {
            addCriterion("test_cnt <>", value, "testCnt");
            return (Criteria) this;
        }

        public Criteria andTestCntGreaterThan(Integer value) {
            addCriterion("test_cnt >", value, "testCnt");
            return (Criteria) this;
        }

        public Criteria andTestCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("test_cnt >=", value, "testCnt");
            return (Criteria) this;
        }

        public Criteria andTestCntLessThan(Integer value) {
            addCriterion("test_cnt <", value, "testCnt");
            return (Criteria) this;
        }

        public Criteria andTestCntLessThanOrEqualTo(Integer value) {
            addCriterion("test_cnt <=", value, "testCnt");
            return (Criteria) this;
        }

        public Criteria andTestCntIn(List<Integer> values) {
            addCriterion("test_cnt in", values, "testCnt");
            return (Criteria) this;
        }

        public Criteria andTestCntNotIn(List<Integer> values) {
            addCriterion("test_cnt not in", values, "testCnt");
            return (Criteria) this;
        }

        public Criteria andTestCntBetween(Integer value1, Integer value2) {
            addCriterion("test_cnt between", value1, value2, "testCnt");
            return (Criteria) this;
        }

        public Criteria andTestCntNotBetween(Integer value1, Integer value2) {
            addCriterion("test_cnt not between", value1, value2, "testCnt");
            return (Criteria) this;
        }

        public Criteria andPassCntIsNull() {
            addCriterion("pass_cnt is null");
            return (Criteria) this;
        }

        public Criteria andPassCntIsNotNull() {
            addCriterion("pass_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andPassCntEqualTo(Integer value) {
            addCriterion("pass_cnt =", value, "passCnt");
            return (Criteria) this;
        }

        public Criteria andPassCntNotEqualTo(Integer value) {
            addCriterion("pass_cnt <>", value, "passCnt");
            return (Criteria) this;
        }

        public Criteria andPassCntGreaterThan(Integer value) {
            addCriterion("pass_cnt >", value, "passCnt");
            return (Criteria) this;
        }

        public Criteria andPassCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("pass_cnt >=", value, "passCnt");
            return (Criteria) this;
        }

        public Criteria andPassCntLessThan(Integer value) {
            addCriterion("pass_cnt <", value, "passCnt");
            return (Criteria) this;
        }

        public Criteria andPassCntLessThanOrEqualTo(Integer value) {
            addCriterion("pass_cnt <=", value, "passCnt");
            return (Criteria) this;
        }

        public Criteria andPassCntIn(List<Integer> values) {
            addCriterion("pass_cnt in", values, "passCnt");
            return (Criteria) this;
        }

        public Criteria andPassCntNotIn(List<Integer> values) {
            addCriterion("pass_cnt not in", values, "passCnt");
            return (Criteria) this;
        }

        public Criteria andPassCntBetween(Integer value1, Integer value2) {
            addCriterion("pass_cnt between", value1, value2, "passCnt");
            return (Criteria) this;
        }

        public Criteria andPassCntNotBetween(Integer value1, Integer value2) {
            addCriterion("pass_cnt not between", value1, value2, "passCnt");
            return (Criteria) this;
        }

        public Criteria andFailCntIsNull() {
            addCriterion("fail_cnt is null");
            return (Criteria) this;
        }

        public Criteria andFailCntIsNotNull() {
            addCriterion("fail_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andFailCntEqualTo(Integer value) {
            addCriterion("fail_cnt =", value, "failCnt");
            return (Criteria) this;
        }

        public Criteria andFailCntNotEqualTo(Integer value) {
            addCriterion("fail_cnt <>", value, "failCnt");
            return (Criteria) this;
        }

        public Criteria andFailCntGreaterThan(Integer value) {
            addCriterion("fail_cnt >", value, "failCnt");
            return (Criteria) this;
        }

        public Criteria andFailCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("fail_cnt >=", value, "failCnt");
            return (Criteria) this;
        }

        public Criteria andFailCntLessThan(Integer value) {
            addCriterion("fail_cnt <", value, "failCnt");
            return (Criteria) this;
        }

        public Criteria andFailCntLessThanOrEqualTo(Integer value) {
            addCriterion("fail_cnt <=", value, "failCnt");
            return (Criteria) this;
        }

        public Criteria andFailCntIn(List<Integer> values) {
            addCriterion("fail_cnt in", values, "failCnt");
            return (Criteria) this;
        }

        public Criteria andFailCntNotIn(List<Integer> values) {
            addCriterion("fail_cnt not in", values, "failCnt");
            return (Criteria) this;
        }

        public Criteria andFailCntBetween(Integer value1, Integer value2) {
            addCriterion("fail_cnt between", value1, value2, "failCnt");
            return (Criteria) this;
        }

        public Criteria andFailCntNotBetween(Integer value1, Integer value2) {
            addCriterion("fail_cnt not between", value1, value2, "failCnt");
            return (Criteria) this;
        }

        public Criteria andExtensionIsNull() {
            addCriterion("extension is null");
            return (Criteria) this;
        }

        public Criteria andExtensionIsNotNull() {
            addCriterion("extension is not null");
            return (Criteria) this;
        }

        public Criteria andExtensionEqualTo(String value) {
            addCriterion("extension =", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionNotEqualTo(String value) {
            addCriterion("extension <>", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionGreaterThan(String value) {
            addCriterion("extension >", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionGreaterThanOrEqualTo(String value) {
            addCriterion("extension >=", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionLessThan(String value) {
            addCriterion("extension <", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionLessThanOrEqualTo(String value) {
            addCriterion("extension <=", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionLike(String value) {
            addCriterion("extension like", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionNotLike(String value) {
            addCriterion("extension not like", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionIn(List<String> values) {
            addCriterion("extension in", values, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionNotIn(List<String> values) {
            addCriterion("extension not in", values, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionBetween(String value1, String value2) {
            addCriterion("extension between", value1, value2, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionNotBetween(String value1, String value2) {
            addCriterion("extension not between", value1, value2, "extension");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table agent_test_result
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table agent_test_result
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}