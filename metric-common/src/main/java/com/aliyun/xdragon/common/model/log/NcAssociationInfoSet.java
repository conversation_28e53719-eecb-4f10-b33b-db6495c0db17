package com.aliyun.xdragon.common.model.log;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotNull;

import com.aliyun.xdragon.common.generate.log.model.GlobalVersionSource;
import com.aliyun.xdragon.common.generate.log.model.NcAssociationInfo;
import com.aliyun.xdragon.common.generate.model.HostStaticInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/04/23
 */
@Data
public class NcAssociationInfoSet implements Serializable {
    private static final long serialVersionUID = -4198317090144059385L;
    @NotNull
    String ncIp;
    @NotNull
    Date time;

    // Info from table nc_association_info
    NcAssociationInfo info;

    // Info from table global_version_source
    GlobalVersionSource source;

    // Info from table host_static_info_ecs_alarm
    HostStaticInfo hostInfo;
}
