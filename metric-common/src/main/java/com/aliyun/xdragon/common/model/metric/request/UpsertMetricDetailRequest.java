package com.aliyun.xdragon.common.model.metric.request;

import java.io.Serializable;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/03/28
 */
@Data
public class UpsertMetricDetailRequest implements Serializable {
    private static final long serialVersionUID = -5959616962451852235L;

    private Long detailId;

    private Long metricAnomalyId;

    @NotNull
    @PositiveOrZero
    private Integer expertPeriodLength;

    @NotNull
    private Boolean anomalyCheck;

    @NotNull
    private Boolean lostCheck;

    private Long expertLostThreshold;

    private String feedback;

    private String metricName;

    private Long sourceId;
}
