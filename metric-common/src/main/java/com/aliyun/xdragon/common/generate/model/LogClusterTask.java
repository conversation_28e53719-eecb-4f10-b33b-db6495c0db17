package com.aliyun.xdragon.common.generate.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class LogClusterTask implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_cluster_task.task_id
     *
     * @mbg.generated
     */
    private Long taskId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_cluster_task.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_cluster_task.task_name
     *
     * @mbg.generated
     */
    private String taskName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_cluster_task.task_description
     *
     * @mbg.generated
     */
    private String taskDescription;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_cluster_task.submitter
     *
     * @mbg.generated
     */
    private String submitter;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_cluster_task.source_type
     *
     * @mbg.generated
     */
    private String sourceType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_cluster_task.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_cluster_task.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_cluster_task.error_message
     *
     * @mbg.generated
     */
    private String errorMessage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_cluster_task.owner
     *
     * @mbg.generated
     */
    private String owner;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_cluster_task.aone_project
     *
     * @mbg.generated
     */
    private Integer aoneProject;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_cluster_task.server_role
     *
     * @mbg.generated
     */
    private String serverRole;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column log_cluster_task.enable_health_score
     *
     * @mbg.generated
     */
    private Integer enableHealthScore;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table log_cluster_task
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_cluster_task.task_id
     *
     * @return the value of log_cluster_task.task_id
     *
     * @mbg.generated
     */
    public Long getTaskId() {
        return taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_cluster_task.task_id
     *
     * @param taskId the value for log_cluster_task.task_id
     *
     * @mbg.generated
     */
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_cluster_task.status
     *
     * @return the value of log_cluster_task.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_cluster_task.status
     *
     * @param status the value for log_cluster_task.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_cluster_task.task_name
     *
     * @return the value of log_cluster_task.task_name
     *
     * @mbg.generated
     */
    public String getTaskName() {
        return taskName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_cluster_task.task_name
     *
     * @param taskName the value for log_cluster_task.task_name
     *
     * @mbg.generated
     */
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_cluster_task.task_description
     *
     * @return the value of log_cluster_task.task_description
     *
     * @mbg.generated
     */
    public String getTaskDescription() {
        return taskDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_cluster_task.task_description
     *
     * @param taskDescription the value for log_cluster_task.task_description
     *
     * @mbg.generated
     */
    public void setTaskDescription(String taskDescription) {
        this.taskDescription = taskDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_cluster_task.submitter
     *
     * @return the value of log_cluster_task.submitter
     *
     * @mbg.generated
     */
    public String getSubmitter() {
        return submitter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_cluster_task.submitter
     *
     * @param submitter the value for log_cluster_task.submitter
     *
     * @mbg.generated
     */
    public void setSubmitter(String submitter) {
        this.submitter = submitter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_cluster_task.source_type
     *
     * @return the value of log_cluster_task.source_type
     *
     * @mbg.generated
     */
    public String getSourceType() {
        return sourceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_cluster_task.source_type
     *
     * @param sourceType the value for log_cluster_task.source_type
     *
     * @mbg.generated
     */
    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_cluster_task.gmt_create
     *
     * @return the value of log_cluster_task.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_cluster_task.gmt_create
     *
     * @param gmtCreate the value for log_cluster_task.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_cluster_task.gmt_modified
     *
     * @return the value of log_cluster_task.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_cluster_task.gmt_modified
     *
     * @param gmtModified the value for log_cluster_task.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_cluster_task.error_message
     *
     * @return the value of log_cluster_task.error_message
     *
     * @mbg.generated
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_cluster_task.error_message
     *
     * @param errorMessage the value for log_cluster_task.error_message
     *
     * @mbg.generated
     */
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_cluster_task.owner
     *
     * @return the value of log_cluster_task.owner
     *
     * @mbg.generated
     */
    public String getOwner() {
        return owner;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_cluster_task.owner
     *
     * @param owner the value for log_cluster_task.owner
     *
     * @mbg.generated
     */
    public void setOwner(String owner) {
        this.owner = owner;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_cluster_task.aone_project
     *
     * @return the value of log_cluster_task.aone_project
     *
     * @mbg.generated
     */
    public Integer getAoneProject() {
        return aoneProject;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_cluster_task.aone_project
     *
     * @param aoneProject the value for log_cluster_task.aone_project
     *
     * @mbg.generated
     */
    public void setAoneProject(Integer aoneProject) {
        this.aoneProject = aoneProject;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_cluster_task.server_role
     *
     * @return the value of log_cluster_task.server_role
     *
     * @mbg.generated
     */
    public String getServerRole() {
        return serverRole;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_cluster_task.server_role
     *
     * @param serverRole the value for log_cluster_task.server_role
     *
     * @mbg.generated
     */
    public void setServerRole(String serverRole) {
        this.serverRole = serverRole;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column log_cluster_task.enable_health_score
     *
     * @return the value of log_cluster_task.enable_health_score
     *
     * @mbg.generated
     */
    public Integer getEnableHealthScore() {
        return enableHealthScore;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column log_cluster_task.enable_health_score
     *
     * @param enableHealthScore the value for log_cluster_task.enable_health_score
     *
     * @mbg.generated
     */
    public void setEnableHealthScore(Integer enableHealthScore) {
        this.enableHealthScore = enableHealthScore;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_task
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        LogClusterTask other = (LogClusterTask) that;
        return (this.getTaskId() == null ? other.getTaskId() == null : this.getTaskId().equals(other.getTaskId()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getTaskName() == null ? other.getTaskName() == null : this.getTaskName().equals(other.getTaskName()))
            && (this.getTaskDescription() == null ? other.getTaskDescription() == null : this.getTaskDescription().equals(other.getTaskDescription()))
            && (this.getSubmitter() == null ? other.getSubmitter() == null : this.getSubmitter().equals(other.getSubmitter()))
            && (this.getSourceType() == null ? other.getSourceType() == null : this.getSourceType().equals(other.getSourceType()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtModified() == null ? other.getGmtModified() == null : this.getGmtModified().equals(other.getGmtModified()))
            && (this.getErrorMessage() == null ? other.getErrorMessage() == null : this.getErrorMessage().equals(other.getErrorMessage()))
            && (this.getOwner() == null ? other.getOwner() == null : this.getOwner().equals(other.getOwner()))
            && (this.getAoneProject() == null ? other.getAoneProject() == null : this.getAoneProject().equals(other.getAoneProject()))
            && (this.getServerRole() == null ? other.getServerRole() == null : this.getServerRole().equals(other.getServerRole()))
            && (this.getEnableHealthScore() == null ? other.getEnableHealthScore() == null : this.getEnableHealthScore().equals(other.getEnableHealthScore()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_task
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getTaskId() == null) ? 0 : getTaskId().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getTaskName() == null) ? 0 : getTaskName().hashCode());
        result = prime * result + ((getTaskDescription() == null) ? 0 : getTaskDescription().hashCode());
        result = prime * result + ((getSubmitter() == null) ? 0 : getSubmitter().hashCode());
        result = prime * result + ((getSourceType() == null) ? 0 : getSourceType().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtModified() == null) ? 0 : getGmtModified().hashCode());
        result = prime * result + ((getErrorMessage() == null) ? 0 : getErrorMessage().hashCode());
        result = prime * result + ((getOwner() == null) ? 0 : getOwner().hashCode());
        result = prime * result + ((getAoneProject() == null) ? 0 : getAoneProject().hashCode());
        result = prime * result + ((getServerRole() == null) ? 0 : getServerRole().hashCode());
        result = prime * result + ((getEnableHealthScore() == null) ? 0 : getEnableHealthScore().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_cluster_task
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", taskId=").append(taskId);
        sb.append(", status=").append(status);
        sb.append(", taskName=").append(taskName);
        sb.append(", taskDescription=").append(taskDescription);
        sb.append(", submitter=").append(submitter);
        sb.append(", sourceType=").append(sourceType);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", errorMessage=").append(errorMessage);
        sb.append(", owner=").append(owner);
        sb.append(", aoneProject=").append(aoneProject);
        sb.append(", serverRole=").append(serverRole);
        sb.append(", enableHealthScore=").append(enableHealthScore);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table log_cluster_task
     *
     * @mbg.generated
     */
    public enum Column {
        taskId("task_id", "taskId", "BIGINT", false),
        status("status", "status", "TINYINT", true),
        taskName("task_name", "taskName", "VARCHAR", false),
        taskDescription("task_description", "taskDescription", "VARCHAR", false),
        submitter("submitter", "submitter", "VARCHAR", false),
        sourceType("source_type", "sourceType", "VARCHAR", false),
        gmtCreate("gmt_create", "gmtCreate", "TIMESTAMP", false),
        gmtModified("gmt_modified", "gmtModified", "TIMESTAMP", false),
        errorMessage("error_message", "errorMessage", "VARCHAR", false),
        owner("owner", "owner", "VARCHAR", true),
        aoneProject("aone_project", "aoneProject", "INTEGER", false),
        serverRole("server_role", "serverRole", "VARCHAR", false),
        enableHealthScore("enable_health_score", "enableHealthScore", "INTEGER", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table log_cluster_task
         *
         * @mbg.generated
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}