package com.aliyun.xdragon.common.generate.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class EvaluateInstance implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.task_id
     *
     * @mbg.generated
     */
    private Long taskId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.batch_id
     *
     * @mbg.generated
     */
    private Long batchId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.task_time
     *
     * @mbg.generated
     */
    private Long taskTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.run_status
     *
     * @mbg.generated
     */
    private Byte runStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.optimal_action
     *
     * @mbg.generated
     */
    private String optimalAction;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.power
     *
     * @mbg.generated
     */
    private BigDecimal power;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.recommend_percent
     *
     * @mbg.generated
     */
    private Byte recommendPercent;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.is_significant
     *
     * @mbg.generated
     */
    private Byte isSignificant;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.pair_compare_result
     *
     * @mbg.generated
     */
    private String pairCompareResult;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.sample_size
     *
     * @mbg.generated
     */
    private String sampleSize;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.sample_mean
     *
     * @mbg.generated
     */
    private String sampleMean;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.statistics
     *
     * @mbg.generated
     */
    private String statistics;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.message
     *
     * @mbg.generated
     */
    private String message;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column evaluate_instance.metric_name
     *
     * @mbg.generated
     */
    private String metricName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table evaluate_instance
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.id
     *
     * @return the value of evaluate_instance.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.id
     *
     * @param id the value for evaluate_instance.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.task_id
     *
     * @return the value of evaluate_instance.task_id
     *
     * @mbg.generated
     */
    public Long getTaskId() {
        return taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.task_id
     *
     * @param taskId the value for evaluate_instance.task_id
     *
     * @mbg.generated
     */
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.batch_id
     *
     * @return the value of evaluate_instance.batch_id
     *
     * @mbg.generated
     */
    public Long getBatchId() {
        return batchId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.batch_id
     *
     * @param batchId the value for evaluate_instance.batch_id
     *
     * @mbg.generated
     */
    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.task_time
     *
     * @return the value of evaluate_instance.task_time
     *
     * @mbg.generated
     */
    public Long getTaskTime() {
        return taskTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.task_time
     *
     * @param taskTime the value for evaluate_instance.task_time
     *
     * @mbg.generated
     */
    public void setTaskTime(Long taskTime) {
        this.taskTime = taskTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.run_status
     *
     * @return the value of evaluate_instance.run_status
     *
     * @mbg.generated
     */
    public Byte getRunStatus() {
        return runStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.run_status
     *
     * @param runStatus the value for evaluate_instance.run_status
     *
     * @mbg.generated
     */
    public void setRunStatus(Byte runStatus) {
        this.runStatus = runStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.optimal_action
     *
     * @return the value of evaluate_instance.optimal_action
     *
     * @mbg.generated
     */
    public String getOptimalAction() {
        return optimalAction;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.optimal_action
     *
     * @param optimalAction the value for evaluate_instance.optimal_action
     *
     * @mbg.generated
     */
    public void setOptimalAction(String optimalAction) {
        this.optimalAction = optimalAction;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.power
     *
     * @return the value of evaluate_instance.power
     *
     * @mbg.generated
     */
    public BigDecimal getPower() {
        return power;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.power
     *
     * @param power the value for evaluate_instance.power
     *
     * @mbg.generated
     */
    public void setPower(BigDecimal power) {
        this.power = power;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.recommend_percent
     *
     * @return the value of evaluate_instance.recommend_percent
     *
     * @mbg.generated
     */
    public Byte getRecommendPercent() {
        return recommendPercent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.recommend_percent
     *
     * @param recommendPercent the value for evaluate_instance.recommend_percent
     *
     * @mbg.generated
     */
    public void setRecommendPercent(Byte recommendPercent) {
        this.recommendPercent = recommendPercent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.is_significant
     *
     * @return the value of evaluate_instance.is_significant
     *
     * @mbg.generated
     */
    public Byte getIsSignificant() {
        return isSignificant;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.is_significant
     *
     * @param isSignificant the value for evaluate_instance.is_significant
     *
     * @mbg.generated
     */
    public void setIsSignificant(Byte isSignificant) {
        this.isSignificant = isSignificant;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.pair_compare_result
     *
     * @return the value of evaluate_instance.pair_compare_result
     *
     * @mbg.generated
     */
    public String getPairCompareResult() {
        return pairCompareResult;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.pair_compare_result
     *
     * @param pairCompareResult the value for evaluate_instance.pair_compare_result
     *
     * @mbg.generated
     */
    public void setPairCompareResult(String pairCompareResult) {
        this.pairCompareResult = pairCompareResult;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.sample_size
     *
     * @return the value of evaluate_instance.sample_size
     *
     * @mbg.generated
     */
    public String getSampleSize() {
        return sampleSize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.sample_size
     *
     * @param sampleSize the value for evaluate_instance.sample_size
     *
     * @mbg.generated
     */
    public void setSampleSize(String sampleSize) {
        this.sampleSize = sampleSize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.sample_mean
     *
     * @return the value of evaluate_instance.sample_mean
     *
     * @mbg.generated
     */
    public String getSampleMean() {
        return sampleMean;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.sample_mean
     *
     * @param sampleMean the value for evaluate_instance.sample_mean
     *
     * @mbg.generated
     */
    public void setSampleMean(String sampleMean) {
        this.sampleMean = sampleMean;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.statistics
     *
     * @return the value of evaluate_instance.statistics
     *
     * @mbg.generated
     */
    public String getStatistics() {
        return statistics;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.statistics
     *
     * @param statistics the value for evaluate_instance.statistics
     *
     * @mbg.generated
     */
    public void setStatistics(String statistics) {
        this.statistics = statistics;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.message
     *
     * @return the value of evaluate_instance.message
     *
     * @mbg.generated
     */
    public String getMessage() {
        return message;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.message
     *
     * @param message the value for evaluate_instance.message
     *
     * @mbg.generated
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.gmt_create
     *
     * @return the value of evaluate_instance.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.gmt_create
     *
     * @param gmtCreate the value for evaluate_instance.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.gmt_modified
     *
     * @return the value of evaluate_instance.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.gmt_modified
     *
     * @param gmtModified the value for evaluate_instance.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column evaluate_instance.metric_name
     *
     * @return the value of evaluate_instance.metric_name
     *
     * @mbg.generated
     */
    public String getMetricName() {
        return metricName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column evaluate_instance.metric_name
     *
     * @param metricName the value for evaluate_instance.metric_name
     *
     * @mbg.generated
     */
    public void setMetricName(String metricName) {
        this.metricName = metricName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluate_instance
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        EvaluateInstance other = (EvaluateInstance) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTaskId() == null ? other.getTaskId() == null : this.getTaskId().equals(other.getTaskId()))
            && (this.getBatchId() == null ? other.getBatchId() == null : this.getBatchId().equals(other.getBatchId()))
            && (this.getTaskTime() == null ? other.getTaskTime() == null : this.getTaskTime().equals(other.getTaskTime()))
            && (this.getRunStatus() == null ? other.getRunStatus() == null : this.getRunStatus().equals(other.getRunStatus()))
            && (this.getOptimalAction() == null ? other.getOptimalAction() == null : this.getOptimalAction().equals(other.getOptimalAction()))
            && (this.getPower() == null ? other.getPower() == null : this.getPower().equals(other.getPower()))
            && (this.getRecommendPercent() == null ? other.getRecommendPercent() == null : this.getRecommendPercent().equals(other.getRecommendPercent()))
            && (this.getIsSignificant() == null ? other.getIsSignificant() == null : this.getIsSignificant().equals(other.getIsSignificant()))
            && (this.getPairCompareResult() == null ? other.getPairCompareResult() == null : this.getPairCompareResult().equals(other.getPairCompareResult()))
            && (this.getSampleSize() == null ? other.getSampleSize() == null : this.getSampleSize().equals(other.getSampleSize()))
            && (this.getSampleMean() == null ? other.getSampleMean() == null : this.getSampleMean().equals(other.getSampleMean()))
            && (this.getStatistics() == null ? other.getStatistics() == null : this.getStatistics().equals(other.getStatistics()))
            && (this.getMessage() == null ? other.getMessage() == null : this.getMessage().equals(other.getMessage()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtModified() == null ? other.getGmtModified() == null : this.getGmtModified().equals(other.getGmtModified()))
            && (this.getMetricName() == null ? other.getMetricName() == null : this.getMetricName().equals(other.getMetricName()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluate_instance
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTaskId() == null) ? 0 : getTaskId().hashCode());
        result = prime * result + ((getBatchId() == null) ? 0 : getBatchId().hashCode());
        result = prime * result + ((getTaskTime() == null) ? 0 : getTaskTime().hashCode());
        result = prime * result + ((getRunStatus() == null) ? 0 : getRunStatus().hashCode());
        result = prime * result + ((getOptimalAction() == null) ? 0 : getOptimalAction().hashCode());
        result = prime * result + ((getPower() == null) ? 0 : getPower().hashCode());
        result = prime * result + ((getRecommendPercent() == null) ? 0 : getRecommendPercent().hashCode());
        result = prime * result + ((getIsSignificant() == null) ? 0 : getIsSignificant().hashCode());
        result = prime * result + ((getPairCompareResult() == null) ? 0 : getPairCompareResult().hashCode());
        result = prime * result + ((getSampleSize() == null) ? 0 : getSampleSize().hashCode());
        result = prime * result + ((getSampleMean() == null) ? 0 : getSampleMean().hashCode());
        result = prime * result + ((getStatistics() == null) ? 0 : getStatistics().hashCode());
        result = prime * result + ((getMessage() == null) ? 0 : getMessage().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtModified() == null) ? 0 : getGmtModified().hashCode());
        result = prime * result + ((getMetricName() == null) ? 0 : getMetricName().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table evaluate_instance
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskId=").append(taskId);
        sb.append(", batchId=").append(batchId);
        sb.append(", taskTime=").append(taskTime);
        sb.append(", runStatus=").append(runStatus);
        sb.append(", optimalAction=").append(optimalAction);
        sb.append(", power=").append(power);
        sb.append(", recommendPercent=").append(recommendPercent);
        sb.append(", isSignificant=").append(isSignificant);
        sb.append(", pairCompareResult=").append(pairCompareResult);
        sb.append(", sampleSize=").append(sampleSize);
        sb.append(", sampleMean=").append(sampleMean);
        sb.append(", statistics=").append(statistics);
        sb.append(", message=").append(message);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", metricName=").append(metricName);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table evaluate_instance
     *
     * @mbg.generated
     */
    public enum Column {
        id("id", "id", "BIGINT", false),
        taskId("task_id", "taskId", "BIGINT", false),
        batchId("batch_id", "batchId", "BIGINT", false),
        taskTime("task_time", "taskTime", "BIGINT", false),
        runStatus("run_status", "runStatus", "TINYINT", false),
        optimalAction("optimal_action", "optimalAction", "VARCHAR", false),
        power("power", "power", "DECIMAL", true),
        recommendPercent("recommend_percent", "recommendPercent", "TINYINT", false),
        isSignificant("is_significant", "isSignificant", "TINYINT", false),
        pairCompareResult("pair_compare_result", "pairCompareResult", "VARCHAR", false),
        sampleSize("sample_size", "sampleSize", "VARCHAR", false),
        sampleMean("sample_mean", "sampleMean", "VARCHAR", false),
        statistics("statistics", "statistics", "VARCHAR", true),
        message("message", "message", "VARCHAR", false),
        gmtCreate("gmt_create", "gmtCreate", "TIMESTAMP", false),
        gmtModified("gmt_modified", "gmtModified", "TIMESTAMP", false),
        metricName("metric_name", "metricName", "VARCHAR", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table evaluate_instance
         *
         * @mbg.generated
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}