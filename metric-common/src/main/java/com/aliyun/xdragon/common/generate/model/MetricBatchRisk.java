package com.aliyun.xdragon.common.generate.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class MetricBatchRisk implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.id
     *
     * @mbg.generated
     */
    private Long riskId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.risk_name
     *
     * @mbg.generated
     */
    private String riskName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.risk_type
     *
     * @mbg.generated
     */
    private String riskType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.anomaly
     *
     * @mbg.generated
     */
    private String anomaly;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.is_marked
     *
     * @mbg.generated
     */
    private Boolean isMarked;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.value
     *
     * @mbg.generated
     */
    private Integer value;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.risk_level
     *
     * @mbg.generated
     */
    private String riskLevel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.gmt_anomaly
     *
     * @mbg.generated
     */
    private Date gmtAnomaly;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.source_id
     *
     * @mbg.generated
     */
    private Long sourceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.merge_type
     *
     * @mbg.generated
     */
    private Integer mergeType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.parent_id
     *
     * @mbg.generated
     */
    private Long parentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column metric_batch_risk.target_name
     *
     * @mbg.generated
     */
    private String targetName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table metric_batch_risk
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.id
     *
     * @return the value of metric_batch_risk.id
     *
     * @mbg.generated
     */
    public Long getRiskId() {
        return riskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.id
     *
     * @param riskId the value for metric_batch_risk.id
     *
     * @mbg.generated
     */
    public void setRiskId(Long riskId) {
        this.riskId = riskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.gmt_create
     *
     * @return the value of metric_batch_risk.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.gmt_create
     *
     * @param gmtCreate the value for metric_batch_risk.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.gmt_modified
     *
     * @return the value of metric_batch_risk.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.gmt_modified
     *
     * @param gmtModified the value for metric_batch_risk.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.risk_name
     *
     * @return the value of metric_batch_risk.risk_name
     *
     * @mbg.generated
     */
    public String getRiskName() {
        return riskName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.risk_name
     *
     * @param riskName the value for metric_batch_risk.risk_name
     *
     * @mbg.generated
     */
    public void setRiskName(String riskName) {
        this.riskName = riskName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.risk_type
     *
     * @return the value of metric_batch_risk.risk_type
     *
     * @mbg.generated
     */
    public String getRiskType() {
        return riskType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.risk_type
     *
     * @param riskType the value for metric_batch_risk.risk_type
     *
     * @mbg.generated
     */
    public void setRiskType(String riskType) {
        this.riskType = riskType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.anomaly
     *
     * @return the value of metric_batch_risk.anomaly
     *
     * @mbg.generated
     */
    public String getAnomaly() {
        return anomaly;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.anomaly
     *
     * @param anomaly the value for metric_batch_risk.anomaly
     *
     * @mbg.generated
     */
    public void setAnomaly(String anomaly) {
        this.anomaly = anomaly;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.is_marked
     *
     * @return the value of metric_batch_risk.is_marked
     *
     * @mbg.generated
     */
    public Boolean getIsMarked() {
        return isMarked;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.is_marked
     *
     * @param isMarked the value for metric_batch_risk.is_marked
     *
     * @mbg.generated
     */
    public void setIsMarked(Boolean isMarked) {
        this.isMarked = isMarked;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.value
     *
     * @return the value of metric_batch_risk.value
     *
     * @mbg.generated
     */
    public Integer getValue() {
        return value;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.value
     *
     * @param value the value for metric_batch_risk.value
     *
     * @mbg.generated
     */
    public void setValue(Integer value) {
        this.value = value;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.risk_level
     *
     * @return the value of metric_batch_risk.risk_level
     *
     * @mbg.generated
     */
    public String getRiskLevel() {
        return riskLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.risk_level
     *
     * @param riskLevel the value for metric_batch_risk.risk_level
     *
     * @mbg.generated
     */
    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.gmt_anomaly
     *
     * @return the value of metric_batch_risk.gmt_anomaly
     *
     * @mbg.generated
     */
    public Date getGmtAnomaly() {
        return gmtAnomaly;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.gmt_anomaly
     *
     * @param gmtAnomaly the value for metric_batch_risk.gmt_anomaly
     *
     * @mbg.generated
     */
    public void setGmtAnomaly(Date gmtAnomaly) {
        this.gmtAnomaly = gmtAnomaly;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.source_id
     *
     * @return the value of metric_batch_risk.source_id
     *
     * @mbg.generated
     */
    public Long getSourceId() {
        return sourceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.source_id
     *
     * @param sourceId the value for metric_batch_risk.source_id
     *
     * @mbg.generated
     */
    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.merge_type
     *
     * @return the value of metric_batch_risk.merge_type
     *
     * @mbg.generated
     */
    public Integer getMergeType() {
        return mergeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.merge_type
     *
     * @param mergeType the value for metric_batch_risk.merge_type
     *
     * @mbg.generated
     */
    public void setMergeType(Integer mergeType) {
        this.mergeType = mergeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.parent_id
     *
     * @return the value of metric_batch_risk.parent_id
     *
     * @mbg.generated
     */
    public Long getParentId() {
        return parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.parent_id
     *
     * @param parentId the value for metric_batch_risk.parent_id
     *
     * @mbg.generated
     */
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column metric_batch_risk.target_name
     *
     * @return the value of metric_batch_risk.target_name
     *
     * @mbg.generated
     */
    public String getTargetName() {
        return targetName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column metric_batch_risk.target_name
     *
     * @param targetName the value for metric_batch_risk.target_name
     *
     * @mbg.generated
     */
    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MetricBatchRisk other = (MetricBatchRisk) that;
        return (this.getRiskId() == null ? other.getRiskId() == null : this.getRiskId().equals(other.getRiskId()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtModified() == null ? other.getGmtModified() == null : this.getGmtModified().equals(other.getGmtModified()))
            && (this.getRiskName() == null ? other.getRiskName() == null : this.getRiskName().equals(other.getRiskName()))
            && (this.getRiskType() == null ? other.getRiskType() == null : this.getRiskType().equals(other.getRiskType()))
            && (this.getAnomaly() == null ? other.getAnomaly() == null : this.getAnomaly().equals(other.getAnomaly()))
            && (this.getIsMarked() == null ? other.getIsMarked() == null : this.getIsMarked().equals(other.getIsMarked()))
            && (this.getValue() == null ? other.getValue() == null : this.getValue().equals(other.getValue()))
            && (this.getRiskLevel() == null ? other.getRiskLevel() == null : this.getRiskLevel().equals(other.getRiskLevel()))
            && (this.getGmtAnomaly() == null ? other.getGmtAnomaly() == null : this.getGmtAnomaly().equals(other.getGmtAnomaly()))
            && (this.getSourceId() == null ? other.getSourceId() == null : this.getSourceId().equals(other.getSourceId()))
            && (this.getMergeType() == null ? other.getMergeType() == null : this.getMergeType().equals(other.getMergeType()))
            && (this.getParentId() == null ? other.getParentId() == null : this.getParentId().equals(other.getParentId()))
            && (this.getTargetName() == null ? other.getTargetName() == null : this.getTargetName().equals(other.getTargetName()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getRiskId() == null) ? 0 : getRiskId().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtModified() == null) ? 0 : getGmtModified().hashCode());
        result = prime * result + ((getRiskName() == null) ? 0 : getRiskName().hashCode());
        result = prime * result + ((getRiskType() == null) ? 0 : getRiskType().hashCode());
        result = prime * result + ((getAnomaly() == null) ? 0 : getAnomaly().hashCode());
        result = prime * result + ((getIsMarked() == null) ? 0 : getIsMarked().hashCode());
        result = prime * result + ((getValue() == null) ? 0 : getValue().hashCode());
        result = prime * result + ((getRiskLevel() == null) ? 0 : getRiskLevel().hashCode());
        result = prime * result + ((getGmtAnomaly() == null) ? 0 : getGmtAnomaly().hashCode());
        result = prime * result + ((getSourceId() == null) ? 0 : getSourceId().hashCode());
        result = prime * result + ((getMergeType() == null) ? 0 : getMergeType().hashCode());
        result = prime * result + ((getParentId() == null) ? 0 : getParentId().hashCode());
        result = prime * result + ((getTargetName() == null) ? 0 : getTargetName().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", riskId=").append(riskId);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", riskName=").append(riskName);
        sb.append(", riskType=").append(riskType);
        sb.append(", anomaly=").append(anomaly);
        sb.append(", isMarked=").append(isMarked);
        sb.append(", value=").append(value);
        sb.append(", riskLevel=").append(riskLevel);
        sb.append(", gmtAnomaly=").append(gmtAnomaly);
        sb.append(", sourceId=").append(sourceId);
        sb.append(", mergeType=").append(mergeType);
        sb.append(", parentId=").append(parentId);
        sb.append(", targetName=").append(targetName);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table metric_batch_risk
     *
     * @mbg.generated
     */
    public enum Column {
        riskId("id", "riskId", "BIGINT", false),
        gmtCreate("gmt_create", "gmtCreate", "TIMESTAMP", false),
        gmtModified("gmt_modified", "gmtModified", "TIMESTAMP", false),
        riskName("risk_name", "riskName", "VARCHAR", false),
        riskType("risk_type", "riskType", "VARCHAR", false),
        anomaly("anomaly", "anomaly", "VARCHAR", false),
        isMarked("is_marked", "isMarked", "BIT", false),
        value("value", "value", "INTEGER", true),
        riskLevel("risk_level", "riskLevel", "VARCHAR", false),
        gmtAnomaly("gmt_anomaly", "gmtAnomaly", "TIMESTAMP", false),
        sourceId("source_id", "sourceId", "BIGINT", false),
        mergeType("merge_type", "mergeType", "INTEGER", false),
        parentId("parent_id", "parentId", "BIGINT", false),
        targetName("target_name", "targetName", "VARCHAR", false),
        markComments("mark_comments", "markComments", "LONGVARCHAR", false),
        drillInfo("drill_info", "drillInfo", "LONGVARCHAR", false),
        vcpuInfo("vcpu_info", "vcpuInfo", "LONGVARCHAR", false),
        extraInfo("extra_info", "extraInfo", "LONGVARCHAR", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table metric_batch_risk
         *
         * @mbg.generated
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}