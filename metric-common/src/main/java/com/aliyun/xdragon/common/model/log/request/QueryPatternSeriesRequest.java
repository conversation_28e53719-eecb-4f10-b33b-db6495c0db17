package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/24
 */
@Data
public class QueryPatternSeriesRequest extends BaseTaskRequest implements Serializable {
    private static final long serialVersionUID = 8173906753185324618L;

    /**
     * pattern md5 list
     */
    @NotNull
    @Size(min = 1, max = 25)
    List<String> patternMd5;
    /**
     * query start time, in ms
     */
    @NotNull
    @Positive
    Long startMs;
    /**
     * query end time, in ms
     */
    @NotNull
    @Positive
    Long endMs;
    /**
     * region list, null or empty for all regions
     */
    List<String> regions;
    /**
     * if add total
     */
    Boolean total = true;

    /**
     * query by dimension
     */
    String dimension;

    private boolean simpleResult = true;
}
