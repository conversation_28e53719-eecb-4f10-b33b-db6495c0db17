package com.aliyun.xdragon.common.exception;

/**
 * <AUTHOR>
 * @date 2023/12/17
 */
public class XdragonMetricUserException extends XdragonMetricBaseException {
    public XdragonMetricUserException(String message) {
        super(message);
    }

    public XdragonMetricUserException(String message, Throwable e) {
        super(message, e);
    }

    public XdragonMetricUserException(Throwable e) {
        super(e);
    }

    public String getCode() {
        return "400";
    }
}
