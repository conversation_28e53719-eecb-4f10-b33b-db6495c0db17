package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLog;
import com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface ClientopsAutoPermissionLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    long countByExample(ClientopsAutoPermissionLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    int deleteByExample(ClientopsAutoPermissionLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    int insert(ClientopsAutoPermissionLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    int insertSelective(ClientopsAutoPermissionLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    List<ClientopsAutoPermissionLog> selectByExampleWithRowbounds(ClientopsAutoPermissionLogExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    List<ClientopsAutoPermissionLog> selectByExampleSelective(@Param("example") ClientopsAutoPermissionLogExample example, @Param("selective") ClientopsAutoPermissionLog.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    ClientopsAutoPermissionLog selectOneByExample(ClientopsAutoPermissionLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    ClientopsAutoPermissionLog selectOneByExampleSelective(@Param("example") ClientopsAutoPermissionLogExample example, @Param("selective") ClientopsAutoPermissionLog.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    List<ClientopsAutoPermissionLog> selectByExample(ClientopsAutoPermissionLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    ClientopsAutoPermissionLog selectByPrimaryKeySelective(@Param("id") Integer id, @Param("selective") ClientopsAutoPermissionLog.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    ClientopsAutoPermissionLog selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") ClientopsAutoPermissionLog record, @Param("example") ClientopsAutoPermissionLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") ClientopsAutoPermissionLog record, @Param("example") ClientopsAutoPermissionLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(ClientopsAutoPermissionLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(ClientopsAutoPermissionLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<ClientopsAutoPermissionLog> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<ClientopsAutoPermissionLog> list, @Param("selective") ClientopsAutoPermissionLog.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    int upsert(ClientopsAutoPermissionLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table clientops_auto_permission_log
     *
     * @mbg.generated
     */
    int upsertSelective(ClientopsAutoPermissionLog record);
}