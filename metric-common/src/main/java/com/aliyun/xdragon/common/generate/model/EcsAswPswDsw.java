package com.aliyun.xdragon.common.generate.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

public class EcsAswPswDsw implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ecs_asw_psw_dsw.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ecs_asw_psw_dsw.asw
     *
     * @mbg.generated
     */
    private String asw;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ecs_asw_psw_dsw.psw
     *
     * @mbg.generated
     */
    private String psw;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ecs_asw_psw_dsw.dsw
     *
     * @mbg.generated
     */
    private String dsw;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ecs_asw_psw_dsw.net_pod_name
     *
     * @mbg.generated
     */
    private String netPodName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ecs_asw_psw_dsw.idc
     *
     * @mbg.generated
     */
    private String idc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ecs_asw_psw_dsw.dsw_cluster_name
     *
     * @mbg.generated
     */
    private String dswClusterName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column ecs_asw_psw_dsw.ds
     *
     * @mbg.generated
     */
    private String ds;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table ecs_asw_psw_dsw
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ecs_asw_psw_dsw.id
     *
     * @return the value of ecs_asw_psw_dsw.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ecs_asw_psw_dsw.id
     *
     * @param id the value for ecs_asw_psw_dsw.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ecs_asw_psw_dsw.asw
     *
     * @return the value of ecs_asw_psw_dsw.asw
     *
     * @mbg.generated
     */
    public String getAsw() {
        return asw;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ecs_asw_psw_dsw.asw
     *
     * @param asw the value for ecs_asw_psw_dsw.asw
     *
     * @mbg.generated
     */
    public void setAsw(String asw) {
        this.asw = asw;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ecs_asw_psw_dsw.psw
     *
     * @return the value of ecs_asw_psw_dsw.psw
     *
     * @mbg.generated
     */
    public String getPsw() {
        return psw;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ecs_asw_psw_dsw.psw
     *
     * @param psw the value for ecs_asw_psw_dsw.psw
     *
     * @mbg.generated
     */
    public void setPsw(String psw) {
        this.psw = psw;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ecs_asw_psw_dsw.dsw
     *
     * @return the value of ecs_asw_psw_dsw.dsw
     *
     * @mbg.generated
     */
    public String getDsw() {
        return dsw;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ecs_asw_psw_dsw.dsw
     *
     * @param dsw the value for ecs_asw_psw_dsw.dsw
     *
     * @mbg.generated
     */
    public void setDsw(String dsw) {
        this.dsw = dsw;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ecs_asw_psw_dsw.net_pod_name
     *
     * @return the value of ecs_asw_psw_dsw.net_pod_name
     *
     * @mbg.generated
     */
    public String getNetPodName() {
        return netPodName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ecs_asw_psw_dsw.net_pod_name
     *
     * @param netPodName the value for ecs_asw_psw_dsw.net_pod_name
     *
     * @mbg.generated
     */
    public void setNetPodName(String netPodName) {
        this.netPodName = netPodName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ecs_asw_psw_dsw.idc
     *
     * @return the value of ecs_asw_psw_dsw.idc
     *
     * @mbg.generated
     */
    public String getIdc() {
        return idc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ecs_asw_psw_dsw.idc
     *
     * @param idc the value for ecs_asw_psw_dsw.idc
     *
     * @mbg.generated
     */
    public void setIdc(String idc) {
        this.idc = idc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ecs_asw_psw_dsw.dsw_cluster_name
     *
     * @return the value of ecs_asw_psw_dsw.dsw_cluster_name
     *
     * @mbg.generated
     */
    public String getDswClusterName() {
        return dswClusterName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ecs_asw_psw_dsw.dsw_cluster_name
     *
     * @param dswClusterName the value for ecs_asw_psw_dsw.dsw_cluster_name
     *
     * @mbg.generated
     */
    public void setDswClusterName(String dswClusterName) {
        this.dswClusterName = dswClusterName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column ecs_asw_psw_dsw.ds
     *
     * @return the value of ecs_asw_psw_dsw.ds
     *
     * @mbg.generated
     */
    public String getDs() {
        return ds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column ecs_asw_psw_dsw.ds
     *
     * @param ds the value for ecs_asw_psw_dsw.ds
     *
     * @mbg.generated
     */
    public void setDs(String ds) {
        this.ds = ds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_asw_psw_dsw
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        EcsAswPswDsw other = (EcsAswPswDsw) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAsw() == null ? other.getAsw() == null : this.getAsw().equals(other.getAsw()))
            && (this.getPsw() == null ? other.getPsw() == null : this.getPsw().equals(other.getPsw()))
            && (this.getDsw() == null ? other.getDsw() == null : this.getDsw().equals(other.getDsw()))
            && (this.getNetPodName() == null ? other.getNetPodName() == null : this.getNetPodName().equals(other.getNetPodName()))
            && (this.getIdc() == null ? other.getIdc() == null : this.getIdc().equals(other.getIdc()))
            && (this.getDswClusterName() == null ? other.getDswClusterName() == null : this.getDswClusterName().equals(other.getDswClusterName()))
            && (this.getDs() == null ? other.getDs() == null : this.getDs().equals(other.getDs()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_asw_psw_dsw
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAsw() == null) ? 0 : getAsw().hashCode());
        result = prime * result + ((getPsw() == null) ? 0 : getPsw().hashCode());
        result = prime * result + ((getDsw() == null) ? 0 : getDsw().hashCode());
        result = prime * result + ((getNetPodName() == null) ? 0 : getNetPodName().hashCode());
        result = prime * result + ((getIdc() == null) ? 0 : getIdc().hashCode());
        result = prime * result + ((getDswClusterName() == null) ? 0 : getDswClusterName().hashCode());
        result = prime * result + ((getDs() == null) ? 0 : getDs().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_asw_psw_dsw
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", asw=").append(asw);
        sb.append(", psw=").append(psw);
        sb.append(", dsw=").append(dsw);
        sb.append(", netPodName=").append(netPodName);
        sb.append(", idc=").append(idc);
        sb.append(", dswClusterName=").append(dswClusterName);
        sb.append(", ds=").append(ds);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table ecs_asw_psw_dsw
     *
     * @mbg.generated
     */
    public enum Column {
        id("id", "id", "BIGINT", false),
        asw("asw", "asw", "VARCHAR", false),
        psw("psw", "psw", "VARCHAR", false),
        dsw("dsw", "dsw", "VARCHAR", false),
        netPodName("net_pod_name", "netPodName", "VARCHAR", false),
        idc("idc", "idc", "VARCHAR", false),
        dswClusterName("dsw_cluster_name", "dswClusterName", "VARCHAR", false),
        ds("ds", "ds", "CHAR", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table ecs_asw_psw_dsw
         *
         * @mbg.generated
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}