<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.log.model.map.AllinoneRiskNcDetailMapper">
  <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="nc_detail_id" jdbcType="BIGINT" property="ncDetailId" />
    <id column="exception_time" jdbcType="VARCHAR" property="exceptionTime" />
    <result column="risk_id" jdbcType="BIGINT" property="riskId" />
    <result column="nc_ip" jdbcType="VARCHAR" property="ncIp" />
    <result column="nc_id" jdbcType="VARCHAR" property="ncId" />
    <result column="nc_sn" jdbcType="VARCHAR" property="ncSn" />
    <result column="hostname" jdbcType="VARCHAR" property="hostname" />
    <result column="cluster" jdbcType="VARCHAR" property="cluster" />
    <result column="cluster_usage" jdbcType="VARCHAR" property="clusterUsage" />
    <result column="azone" jdbcType="VARCHAR" property="azone" />
    <result column="region" jdbcType="VARCHAR" property="region" />
    <result column="idc" jdbcType="VARCHAR" property="idc" />
    <result column="room" jdbcType="VARCHAR" property="room" />
    <result column="rack" jdbcType="VARCHAR" property="rack" />
    <result column="asw_id" jdbcType="VARCHAR" property="aswId" />
    <result column="vcpu_mod" jdbcType="VARCHAR" property="vcpuMod" />
    <result column="physical_model" jdbcType="VARCHAR" property="physicalModel" />
    <result column="cpu_model" jdbcType="VARCHAR" property="cpuModel" />
    <result column="nc_vcpu" jdbcType="VARCHAR" property="ncVcpu" />
    <result column="avs_version" jdbcType="VARCHAR" property="avsVersion" />
    <result column="nc_category" jdbcType="VARCHAR" property="ncCategory" />
    <result column="date_outwarranty" jdbcType="VARCHAR" property="dateOutwarranty" />
    <result column="date_purchase" jdbcType="VARCHAR" property="datePurchase" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="virt_type" jdbcType="VARCHAR" property="virtType" />
    <result column="river_type" jdbcType="VARCHAR" property="riverType" />
    <result column="network_type" jdbcType="VARCHAR" property="networkType" />
    <result column="storage_network_type" jdbcType="VARCHAR" property="storageNetworkType" />
    <result column="storage_type" jdbcType="VARCHAR" property="storageType" />
    <result column="cpu_generation" jdbcType="VARCHAR" property="cpuGeneration" />
    <result column="on_ecs" jdbcType="VARCHAR" property="onEcs" />
    <result column="exception_name" jdbcType="VARCHAR" property="exceptionName" />
    <result column="recover_time" jdbcType="VARCHAR" property="recoverTime" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    nc_detail_id, exception_time, risk_id, nc_ip, nc_id, nc_sn, hostname, `cluster`, 
    cluster_usage, azone, region, idc, room, rack, asw_id, vcpu_mod, physical_model, 
    cpu_model, nc_vcpu, avs_version, nc_category, date_outwarranty, date_purchase, manufacturer, 
    product_name, virt_type, river_type, network_type, storage_network_type, storage_type, 
    cpu_generation, on_ecs, exception_name, recover_time, gmt_created, gmt_modified
  </sql>
  <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from allinone_risk_nc_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="example != null and example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from allinone_risk_nc_detail
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetailKey" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from allinone_risk_nc_detail
    where nc_detail_id = #{ncDetailId,jdbcType=BIGINT}
      and exception_time = #{exceptionTime,jdbcType=VARCHAR}
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from allinone_risk_nc_detail
    where nc_detail_id = #{record.ncDetailId,jdbcType=BIGINT}
      and exception_time = #{record.exceptionTime,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetailKey">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from allinone_risk_nc_detail
    where nc_detail_id = #{ncDetailId,jdbcType=BIGINT}
      and exception_time = #{exceptionTime,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from allinone_risk_nc_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="ncDetailId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into allinone_risk_nc_detail (exception_time, risk_id, nc_ip, 
      nc_id, nc_sn, hostname, 
      `cluster`, cluster_usage, azone, 
      region, idc, room, 
      rack, asw_id, vcpu_mod, 
      physical_model, cpu_model, nc_vcpu, 
      avs_version, nc_category, date_outwarranty, 
      date_purchase, manufacturer, product_name, 
      virt_type, river_type, network_type, 
      storage_network_type, storage_type, cpu_generation, 
      on_ecs, exception_name, recover_time, 
      gmt_created, gmt_modified)
    values (#{exceptionTime,jdbcType=VARCHAR}, #{riskId,jdbcType=BIGINT}, #{ncIp,jdbcType=VARCHAR}, 
      #{ncId,jdbcType=VARCHAR}, #{ncSn,jdbcType=VARCHAR}, #{hostname,jdbcType=VARCHAR}, 
      #{cluster,jdbcType=VARCHAR}, #{clusterUsage,jdbcType=VARCHAR}, #{azone,jdbcType=VARCHAR}, 
      #{region,jdbcType=VARCHAR}, #{idc,jdbcType=VARCHAR}, #{room,jdbcType=VARCHAR}, 
      #{rack,jdbcType=VARCHAR}, #{aswId,jdbcType=VARCHAR}, #{vcpuMod,jdbcType=VARCHAR}, 
      #{physicalModel,jdbcType=VARCHAR}, #{cpuModel,jdbcType=VARCHAR}, #{ncVcpu,jdbcType=VARCHAR}, 
      #{avsVersion,jdbcType=VARCHAR}, #{ncCategory,jdbcType=VARCHAR}, #{dateOutwarranty,jdbcType=VARCHAR}, 
      #{datePurchase,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, 
      #{virtType,jdbcType=VARCHAR}, #{riverType,jdbcType=VARCHAR}, #{networkType,jdbcType=VARCHAR}, 
      #{storageNetworkType,jdbcType=VARCHAR}, #{storageType,jdbcType=VARCHAR}, #{cpuGeneration,jdbcType=VARCHAR}, 
      #{onEcs,jdbcType=VARCHAR}, #{exceptionName,jdbcType=VARCHAR}, #{recoverTime,jdbcType=VARCHAR}, 
      #{gmtCreated,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="ncDetailId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into allinone_risk_nc_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="exceptionTime != null">
        exception_time,
      </if>
      <if test="riskId != null">
        risk_id,
      </if>
      <if test="ncIp != null">
        nc_ip,
      </if>
      <if test="ncId != null">
        nc_id,
      </if>
      <if test="ncSn != null">
        nc_sn,
      </if>
      <if test="hostname != null">
        hostname,
      </if>
      <if test="cluster != null">
        `cluster`,
      </if>
      <if test="clusterUsage != null">
        cluster_usage,
      </if>
      <if test="azone != null">
        azone,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="idc != null">
        idc,
      </if>
      <if test="room != null">
        room,
      </if>
      <if test="rack != null">
        rack,
      </if>
      <if test="aswId != null">
        asw_id,
      </if>
      <if test="vcpuMod != null">
        vcpu_mod,
      </if>
      <if test="physicalModel != null">
        physical_model,
      </if>
      <if test="cpuModel != null">
        cpu_model,
      </if>
      <if test="ncVcpu != null">
        nc_vcpu,
      </if>
      <if test="avsVersion != null">
        avs_version,
      </if>
      <if test="ncCategory != null">
        nc_category,
      </if>
      <if test="dateOutwarranty != null">
        date_outwarranty,
      </if>
      <if test="datePurchase != null">
        date_purchase,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="virtType != null">
        virt_type,
      </if>
      <if test="riverType != null">
        river_type,
      </if>
      <if test="networkType != null">
        network_type,
      </if>
      <if test="storageNetworkType != null">
        storage_network_type,
      </if>
      <if test="storageType != null">
        storage_type,
      </if>
      <if test="cpuGeneration != null">
        cpu_generation,
      </if>
      <if test="onEcs != null">
        on_ecs,
      </if>
      <if test="exceptionName != null">
        exception_name,
      </if>
      <if test="recoverTime != null">
        recover_time,
      </if>
      <if test="gmtCreated != null">
        gmt_created,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="exceptionTime != null">
        #{exceptionTime,jdbcType=VARCHAR},
      </if>
      <if test="riskId != null">
        #{riskId,jdbcType=BIGINT},
      </if>
      <if test="ncIp != null">
        #{ncIp,jdbcType=VARCHAR},
      </if>
      <if test="ncId != null">
        #{ncId,jdbcType=VARCHAR},
      </if>
      <if test="ncSn != null">
        #{ncSn,jdbcType=VARCHAR},
      </if>
      <if test="hostname != null">
        #{hostname,jdbcType=VARCHAR},
      </if>
      <if test="cluster != null">
        #{cluster,jdbcType=VARCHAR},
      </if>
      <if test="clusterUsage != null">
        #{clusterUsage,jdbcType=VARCHAR},
      </if>
      <if test="azone != null">
        #{azone,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="idc != null">
        #{idc,jdbcType=VARCHAR},
      </if>
      <if test="room != null">
        #{room,jdbcType=VARCHAR},
      </if>
      <if test="rack != null">
        #{rack,jdbcType=VARCHAR},
      </if>
      <if test="aswId != null">
        #{aswId,jdbcType=VARCHAR},
      </if>
      <if test="vcpuMod != null">
        #{vcpuMod,jdbcType=VARCHAR},
      </if>
      <if test="physicalModel != null">
        #{physicalModel,jdbcType=VARCHAR},
      </if>
      <if test="cpuModel != null">
        #{cpuModel,jdbcType=VARCHAR},
      </if>
      <if test="ncVcpu != null">
        #{ncVcpu,jdbcType=VARCHAR},
      </if>
      <if test="avsVersion != null">
        #{avsVersion,jdbcType=VARCHAR},
      </if>
      <if test="ncCategory != null">
        #{ncCategory,jdbcType=VARCHAR},
      </if>
      <if test="dateOutwarranty != null">
        #{dateOutwarranty,jdbcType=VARCHAR},
      </if>
      <if test="datePurchase != null">
        #{datePurchase,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="virtType != null">
        #{virtType,jdbcType=VARCHAR},
      </if>
      <if test="riverType != null">
        #{riverType,jdbcType=VARCHAR},
      </if>
      <if test="networkType != null">
        #{networkType,jdbcType=VARCHAR},
      </if>
      <if test="storageNetworkType != null">
        #{storageNetworkType,jdbcType=VARCHAR},
      </if>
      <if test="storageType != null">
        #{storageType,jdbcType=VARCHAR},
      </if>
      <if test="cpuGeneration != null">
        #{cpuGeneration,jdbcType=VARCHAR},
      </if>
      <if test="onEcs != null">
        #{onEcs,jdbcType=VARCHAR},
      </if>
      <if test="exceptionName != null">
        #{exceptionName,jdbcType=VARCHAR},
      </if>
      <if test="recoverTime != null">
        #{recoverTime,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreated != null">
        #{gmtCreated,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from allinone_risk_nc_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update allinone_risk_nc_detail
    <set>
      <if test="record.ncDetailId != null">
        nc_detail_id = #{record.ncDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.exceptionTime != null">
        exception_time = #{record.exceptionTime,jdbcType=VARCHAR},
      </if>
      <if test="record.riskId != null">
        risk_id = #{record.riskId,jdbcType=BIGINT},
      </if>
      <if test="record.ncIp != null">
        nc_ip = #{record.ncIp,jdbcType=VARCHAR},
      </if>
      <if test="record.ncId != null">
        nc_id = #{record.ncId,jdbcType=VARCHAR},
      </if>
      <if test="record.ncSn != null">
        nc_sn = #{record.ncSn,jdbcType=VARCHAR},
      </if>
      <if test="record.hostname != null">
        hostname = #{record.hostname,jdbcType=VARCHAR},
      </if>
      <if test="record.cluster != null">
        `cluster` = #{record.cluster,jdbcType=VARCHAR},
      </if>
      <if test="record.clusterUsage != null">
        cluster_usage = #{record.clusterUsage,jdbcType=VARCHAR},
      </if>
      <if test="record.azone != null">
        azone = #{record.azone,jdbcType=VARCHAR},
      </if>
      <if test="record.region != null">
        region = #{record.region,jdbcType=VARCHAR},
      </if>
      <if test="record.idc != null">
        idc = #{record.idc,jdbcType=VARCHAR},
      </if>
      <if test="record.room != null">
        room = #{record.room,jdbcType=VARCHAR},
      </if>
      <if test="record.rack != null">
        rack = #{record.rack,jdbcType=VARCHAR},
      </if>
      <if test="record.aswId != null">
        asw_id = #{record.aswId,jdbcType=VARCHAR},
      </if>
      <if test="record.vcpuMod != null">
        vcpu_mod = #{record.vcpuMod,jdbcType=VARCHAR},
      </if>
      <if test="record.physicalModel != null">
        physical_model = #{record.physicalModel,jdbcType=VARCHAR},
      </if>
      <if test="record.cpuModel != null">
        cpu_model = #{record.cpuModel,jdbcType=VARCHAR},
      </if>
      <if test="record.ncVcpu != null">
        nc_vcpu = #{record.ncVcpu,jdbcType=VARCHAR},
      </if>
      <if test="record.avsVersion != null">
        avs_version = #{record.avsVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.ncCategory != null">
        nc_category = #{record.ncCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.dateOutwarranty != null">
        date_outwarranty = #{record.dateOutwarranty,jdbcType=VARCHAR},
      </if>
      <if test="record.datePurchase != null">
        date_purchase = #{record.datePurchase,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.virtType != null">
        virt_type = #{record.virtType,jdbcType=VARCHAR},
      </if>
      <if test="record.riverType != null">
        river_type = #{record.riverType,jdbcType=VARCHAR},
      </if>
      <if test="record.networkType != null">
        network_type = #{record.networkType,jdbcType=VARCHAR},
      </if>
      <if test="record.storageNetworkType != null">
        storage_network_type = #{record.storageNetworkType,jdbcType=VARCHAR},
      </if>
      <if test="record.storageType != null">
        storage_type = #{record.storageType,jdbcType=VARCHAR},
      </if>
      <if test="record.cpuGeneration != null">
        cpu_generation = #{record.cpuGeneration,jdbcType=VARCHAR},
      </if>
      <if test="record.onEcs != null">
        on_ecs = #{record.onEcs,jdbcType=VARCHAR},
      </if>
      <if test="record.exceptionName != null">
        exception_name = #{record.exceptionName,jdbcType=VARCHAR},
      </if>
      <if test="record.recoverTime != null">
        recover_time = #{record.recoverTime,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreated != null">
        gmt_created = #{record.gmtCreated,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update allinone_risk_nc_detail
    set nc_detail_id = #{record.ncDetailId,jdbcType=BIGINT},
      exception_time = #{record.exceptionTime,jdbcType=VARCHAR},
      risk_id = #{record.riskId,jdbcType=BIGINT},
      nc_ip = #{record.ncIp,jdbcType=VARCHAR},
      nc_id = #{record.ncId,jdbcType=VARCHAR},
      nc_sn = #{record.ncSn,jdbcType=VARCHAR},
      hostname = #{record.hostname,jdbcType=VARCHAR},
      `cluster` = #{record.cluster,jdbcType=VARCHAR},
      cluster_usage = #{record.clusterUsage,jdbcType=VARCHAR},
      azone = #{record.azone,jdbcType=VARCHAR},
      region = #{record.region,jdbcType=VARCHAR},
      idc = #{record.idc,jdbcType=VARCHAR},
      room = #{record.room,jdbcType=VARCHAR},
      rack = #{record.rack,jdbcType=VARCHAR},
      asw_id = #{record.aswId,jdbcType=VARCHAR},
      vcpu_mod = #{record.vcpuMod,jdbcType=VARCHAR},
      physical_model = #{record.physicalModel,jdbcType=VARCHAR},
      cpu_model = #{record.cpuModel,jdbcType=VARCHAR},
      nc_vcpu = #{record.ncVcpu,jdbcType=VARCHAR},
      avs_version = #{record.avsVersion,jdbcType=VARCHAR},
      nc_category = #{record.ncCategory,jdbcType=VARCHAR},
      date_outwarranty = #{record.dateOutwarranty,jdbcType=VARCHAR},
      date_purchase = #{record.datePurchase,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      virt_type = #{record.virtType,jdbcType=VARCHAR},
      river_type = #{record.riverType,jdbcType=VARCHAR},
      network_type = #{record.networkType,jdbcType=VARCHAR},
      storage_network_type = #{record.storageNetworkType,jdbcType=VARCHAR},
      storage_type = #{record.storageType,jdbcType=VARCHAR},
      cpu_generation = #{record.cpuGeneration,jdbcType=VARCHAR},
      on_ecs = #{record.onEcs,jdbcType=VARCHAR},
      exception_name = #{record.exceptionName,jdbcType=VARCHAR},
      recover_time = #{record.recoverTime,jdbcType=VARCHAR},
      gmt_created = #{record.gmtCreated,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update allinone_risk_nc_detail
    <set>
      <if test="riskId != null">
        risk_id = #{riskId,jdbcType=BIGINT},
      </if>
      <if test="ncIp != null">
        nc_ip = #{ncIp,jdbcType=VARCHAR},
      </if>
      <if test="ncId != null">
        nc_id = #{ncId,jdbcType=VARCHAR},
      </if>
      <if test="ncSn != null">
        nc_sn = #{ncSn,jdbcType=VARCHAR},
      </if>
      <if test="hostname != null">
        hostname = #{hostname,jdbcType=VARCHAR},
      </if>
      <if test="cluster != null">
        `cluster` = #{cluster,jdbcType=VARCHAR},
      </if>
      <if test="clusterUsage != null">
        cluster_usage = #{clusterUsage,jdbcType=VARCHAR},
      </if>
      <if test="azone != null">
        azone = #{azone,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        region = #{region,jdbcType=VARCHAR},
      </if>
      <if test="idc != null">
        idc = #{idc,jdbcType=VARCHAR},
      </if>
      <if test="room != null">
        room = #{room,jdbcType=VARCHAR},
      </if>
      <if test="rack != null">
        rack = #{rack,jdbcType=VARCHAR},
      </if>
      <if test="aswId != null">
        asw_id = #{aswId,jdbcType=VARCHAR},
      </if>
      <if test="vcpuMod != null">
        vcpu_mod = #{vcpuMod,jdbcType=VARCHAR},
      </if>
      <if test="physicalModel != null">
        physical_model = #{physicalModel,jdbcType=VARCHAR},
      </if>
      <if test="cpuModel != null">
        cpu_model = #{cpuModel,jdbcType=VARCHAR},
      </if>
      <if test="ncVcpu != null">
        nc_vcpu = #{ncVcpu,jdbcType=VARCHAR},
      </if>
      <if test="avsVersion != null">
        avs_version = #{avsVersion,jdbcType=VARCHAR},
      </if>
      <if test="ncCategory != null">
        nc_category = #{ncCategory,jdbcType=VARCHAR},
      </if>
      <if test="dateOutwarranty != null">
        date_outwarranty = #{dateOutwarranty,jdbcType=VARCHAR},
      </if>
      <if test="datePurchase != null">
        date_purchase = #{datePurchase,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="virtType != null">
        virt_type = #{virtType,jdbcType=VARCHAR},
      </if>
      <if test="riverType != null">
        river_type = #{riverType,jdbcType=VARCHAR},
      </if>
      <if test="networkType != null">
        network_type = #{networkType,jdbcType=VARCHAR},
      </if>
      <if test="storageNetworkType != null">
        storage_network_type = #{storageNetworkType,jdbcType=VARCHAR},
      </if>
      <if test="storageType != null">
        storage_type = #{storageType,jdbcType=VARCHAR},
      </if>
      <if test="cpuGeneration != null">
        cpu_generation = #{cpuGeneration,jdbcType=VARCHAR},
      </if>
      <if test="onEcs != null">
        on_ecs = #{onEcs,jdbcType=VARCHAR},
      </if>
      <if test="exceptionName != null">
        exception_name = #{exceptionName,jdbcType=VARCHAR},
      </if>
      <if test="recoverTime != null">
        recover_time = #{recoverTime,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreated != null">
        gmt_created = #{gmtCreated,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where nc_detail_id = #{ncDetailId,jdbcType=BIGINT}
      and exception_time = #{exceptionTime,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update allinone_risk_nc_detail
    set risk_id = #{riskId,jdbcType=BIGINT},
      nc_ip = #{ncIp,jdbcType=VARCHAR},
      nc_id = #{ncId,jdbcType=VARCHAR},
      nc_sn = #{ncSn,jdbcType=VARCHAR},
      hostname = #{hostname,jdbcType=VARCHAR},
      `cluster` = #{cluster,jdbcType=VARCHAR},
      cluster_usage = #{clusterUsage,jdbcType=VARCHAR},
      azone = #{azone,jdbcType=VARCHAR},
      region = #{region,jdbcType=VARCHAR},
      idc = #{idc,jdbcType=VARCHAR},
      room = #{room,jdbcType=VARCHAR},
      rack = #{rack,jdbcType=VARCHAR},
      asw_id = #{aswId,jdbcType=VARCHAR},
      vcpu_mod = #{vcpuMod,jdbcType=VARCHAR},
      physical_model = #{physicalModel,jdbcType=VARCHAR},
      cpu_model = #{cpuModel,jdbcType=VARCHAR},
      nc_vcpu = #{ncVcpu,jdbcType=VARCHAR},
      avs_version = #{avsVersion,jdbcType=VARCHAR},
      nc_category = #{ncCategory,jdbcType=VARCHAR},
      date_outwarranty = #{dateOutwarranty,jdbcType=VARCHAR},
      date_purchase = #{datePurchase,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      virt_type = #{virtType,jdbcType=VARCHAR},
      river_type = #{riverType,jdbcType=VARCHAR},
      network_type = #{networkType,jdbcType=VARCHAR},
      storage_network_type = #{storageNetworkType,jdbcType=VARCHAR},
      storage_type = #{storageType,jdbcType=VARCHAR},
      cpu_generation = #{cpuGeneration,jdbcType=VARCHAR},
      on_ecs = #{onEcs,jdbcType=VARCHAR},
      exception_name = #{exceptionName,jdbcType=VARCHAR},
      recover_time = #{recoverTime,jdbcType=VARCHAR},
      gmt_created = #{gmtCreated,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where nc_detail_id = #{ncDetailId,jdbcType=BIGINT}
      and exception_time = #{exceptionTime,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from allinone_risk_nc_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert" keyColumn="nc_detail_id" keyProperty="ncDetailId" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into allinone_risk_nc_detail
    (exception_time, risk_id, nc_ip, nc_id, nc_sn, hostname, `cluster`, cluster_usage, 
      azone, region, idc, room, rack, asw_id, vcpu_mod, physical_model, cpu_model, nc_vcpu, 
      avs_version, nc_category, date_outwarranty, date_purchase, manufacturer, product_name, 
      virt_type, river_type, network_type, storage_network_type, storage_type, cpu_generation, 
      on_ecs, exception_name, recover_time, gmt_created, gmt_modified)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.exceptionTime,jdbcType=VARCHAR}, #{item.riskId,jdbcType=BIGINT}, #{item.ncIp,jdbcType=VARCHAR}, 
        #{item.ncId,jdbcType=VARCHAR}, #{item.ncSn,jdbcType=VARCHAR}, #{item.hostname,jdbcType=VARCHAR}, 
        #{item.cluster,jdbcType=VARCHAR}, #{item.clusterUsage,jdbcType=VARCHAR}, #{item.azone,jdbcType=VARCHAR}, 
        #{item.region,jdbcType=VARCHAR}, #{item.idc,jdbcType=VARCHAR}, #{item.room,jdbcType=VARCHAR}, 
        #{item.rack,jdbcType=VARCHAR}, #{item.aswId,jdbcType=VARCHAR}, #{item.vcpuMod,jdbcType=VARCHAR}, 
        #{item.physicalModel,jdbcType=VARCHAR}, #{item.cpuModel,jdbcType=VARCHAR}, #{item.ncVcpu,jdbcType=VARCHAR}, 
        #{item.avsVersion,jdbcType=VARCHAR}, #{item.ncCategory,jdbcType=VARCHAR}, #{item.dateOutwarranty,jdbcType=VARCHAR}, 
        #{item.datePurchase,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR}, #{item.productName,jdbcType=VARCHAR}, 
        #{item.virtType,jdbcType=VARCHAR}, #{item.riverType,jdbcType=VARCHAR}, #{item.networkType,jdbcType=VARCHAR}, 
        #{item.storageNetworkType,jdbcType=VARCHAR}, #{item.storageType,jdbcType=VARCHAR}, 
        #{item.cpuGeneration,jdbcType=VARCHAR}, #{item.onEcs,jdbcType=VARCHAR}, #{item.exceptionName,jdbcType=VARCHAR}, 
        #{item.recoverTime,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="nc_detail_id" keyProperty="list.ncDetailId" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into allinone_risk_nc_detail (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'exception_time'.toString() == column.value">
          #{item.exceptionTime,jdbcType=VARCHAR}
        </if>
        <if test="'risk_id'.toString() == column.value">
          #{item.riskId,jdbcType=BIGINT}
        </if>
        <if test="'nc_ip'.toString() == column.value">
          #{item.ncIp,jdbcType=VARCHAR}
        </if>
        <if test="'nc_id'.toString() == column.value">
          #{item.ncId,jdbcType=VARCHAR}
        </if>
        <if test="'nc_sn'.toString() == column.value">
          #{item.ncSn,jdbcType=VARCHAR}
        </if>
        <if test="'hostname'.toString() == column.value">
          #{item.hostname,jdbcType=VARCHAR}
        </if>
        <if test="'cluster'.toString() == column.value">
          #{item.cluster,jdbcType=VARCHAR}
        </if>
        <if test="'cluster_usage'.toString() == column.value">
          #{item.clusterUsage,jdbcType=VARCHAR}
        </if>
        <if test="'azone'.toString() == column.value">
          #{item.azone,jdbcType=VARCHAR}
        </if>
        <if test="'region'.toString() == column.value">
          #{item.region,jdbcType=VARCHAR}
        </if>
        <if test="'idc'.toString() == column.value">
          #{item.idc,jdbcType=VARCHAR}
        </if>
        <if test="'room'.toString() == column.value">
          #{item.room,jdbcType=VARCHAR}
        </if>
        <if test="'rack'.toString() == column.value">
          #{item.rack,jdbcType=VARCHAR}
        </if>
        <if test="'asw_id'.toString() == column.value">
          #{item.aswId,jdbcType=VARCHAR}
        </if>
        <if test="'vcpu_mod'.toString() == column.value">
          #{item.vcpuMod,jdbcType=VARCHAR}
        </if>
        <if test="'physical_model'.toString() == column.value">
          #{item.physicalModel,jdbcType=VARCHAR}
        </if>
        <if test="'cpu_model'.toString() == column.value">
          #{item.cpuModel,jdbcType=VARCHAR}
        </if>
        <if test="'nc_vcpu'.toString() == column.value">
          #{item.ncVcpu,jdbcType=VARCHAR}
        </if>
        <if test="'avs_version'.toString() == column.value">
          #{item.avsVersion,jdbcType=VARCHAR}
        </if>
        <if test="'nc_category'.toString() == column.value">
          #{item.ncCategory,jdbcType=VARCHAR}
        </if>
        <if test="'date_outwarranty'.toString() == column.value">
          #{item.dateOutwarranty,jdbcType=VARCHAR}
        </if>
        <if test="'date_purchase'.toString() == column.value">
          #{item.datePurchase,jdbcType=VARCHAR}
        </if>
        <if test="'manufacturer'.toString() == column.value">
          #{item.manufacturer,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'virt_type'.toString() == column.value">
          #{item.virtType,jdbcType=VARCHAR}
        </if>
        <if test="'river_type'.toString() == column.value">
          #{item.riverType,jdbcType=VARCHAR}
        </if>
        <if test="'network_type'.toString() == column.value">
          #{item.networkType,jdbcType=VARCHAR}
        </if>
        <if test="'storage_network_type'.toString() == column.value">
          #{item.storageNetworkType,jdbcType=VARCHAR}
        </if>
        <if test="'storage_type'.toString() == column.value">
          #{item.storageType,jdbcType=VARCHAR}
        </if>
        <if test="'cpu_generation'.toString() == column.value">
          #{item.cpuGeneration,jdbcType=VARCHAR}
        </if>
        <if test="'on_ecs'.toString() == column.value">
          #{item.onEcs,jdbcType=VARCHAR}
        </if>
        <if test="'exception_name'.toString() == column.value">
          #{item.exceptionName,jdbcType=VARCHAR}
        </if>
        <if test="'recover_time'.toString() == column.value">
          #{item.recoverTime,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
  <insert id="upsertSelective" keyColumn="nc_detail_id" keyProperty="ncDetailId" parameterType="com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetail" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into allinone_risk_nc_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ncDetailId != null">
        nc_detail_id,
      </if>
      <if test="exceptionTime != null">
        exception_time,
      </if>
      <if test="riskId != null">
        risk_id,
      </if>
      <if test="ncIp != null">
        nc_ip,
      </if>
      <if test="ncId != null">
        nc_id,
      </if>
      <if test="ncSn != null">
        nc_sn,
      </if>
      <if test="hostname != null">
        hostname,
      </if>
      <if test="cluster != null">
        `cluster`,
      </if>
      <if test="clusterUsage != null">
        cluster_usage,
      </if>
      <if test="azone != null">
        azone,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="idc != null">
        idc,
      </if>
      <if test="room != null">
        room,
      </if>
      <if test="rack != null">
        rack,
      </if>
      <if test="aswId != null">
        asw_id,
      </if>
      <if test="vcpuMod != null">
        vcpu_mod,
      </if>
      <if test="physicalModel != null">
        physical_model,
      </if>
      <if test="cpuModel != null">
        cpu_model,
      </if>
      <if test="ncVcpu != null">
        nc_vcpu,
      </if>
      <if test="avsVersion != null">
        avs_version,
      </if>
      <if test="ncCategory != null">
        nc_category,
      </if>
      <if test="dateOutwarranty != null">
        date_outwarranty,
      </if>
      <if test="datePurchase != null">
        date_purchase,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="virtType != null">
        virt_type,
      </if>
      <if test="riverType != null">
        river_type,
      </if>
      <if test="networkType != null">
        network_type,
      </if>
      <if test="storageNetworkType != null">
        storage_network_type,
      </if>
      <if test="storageType != null">
        storage_type,
      </if>
      <if test="cpuGeneration != null">
        cpu_generation,
      </if>
      <if test="onEcs != null">
        on_ecs,
      </if>
      <if test="exceptionName != null">
        exception_name,
      </if>
      <if test="recoverTime != null">
        recover_time,
      </if>
      <if test="gmtCreated != null">
        gmt_created,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ncDetailId != null">
        #{ncDetailId,jdbcType=BIGINT},
      </if>
      <if test="exceptionTime != null">
        #{exceptionTime,jdbcType=VARCHAR},
      </if>
      <if test="riskId != null">
        #{riskId,jdbcType=BIGINT},
      </if>
      <if test="ncIp != null">
        #{ncIp,jdbcType=VARCHAR},
      </if>
      <if test="ncId != null">
        #{ncId,jdbcType=VARCHAR},
      </if>
      <if test="ncSn != null">
        #{ncSn,jdbcType=VARCHAR},
      </if>
      <if test="hostname != null">
        #{hostname,jdbcType=VARCHAR},
      </if>
      <if test="cluster != null">
        #{cluster,jdbcType=VARCHAR},
      </if>
      <if test="clusterUsage != null">
        #{clusterUsage,jdbcType=VARCHAR},
      </if>
      <if test="azone != null">
        #{azone,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="idc != null">
        #{idc,jdbcType=VARCHAR},
      </if>
      <if test="room != null">
        #{room,jdbcType=VARCHAR},
      </if>
      <if test="rack != null">
        #{rack,jdbcType=VARCHAR},
      </if>
      <if test="aswId != null">
        #{aswId,jdbcType=VARCHAR},
      </if>
      <if test="vcpuMod != null">
        #{vcpuMod,jdbcType=VARCHAR},
      </if>
      <if test="physicalModel != null">
        #{physicalModel,jdbcType=VARCHAR},
      </if>
      <if test="cpuModel != null">
        #{cpuModel,jdbcType=VARCHAR},
      </if>
      <if test="ncVcpu != null">
        #{ncVcpu,jdbcType=VARCHAR},
      </if>
      <if test="avsVersion != null">
        #{avsVersion,jdbcType=VARCHAR},
      </if>
      <if test="ncCategory != null">
        #{ncCategory,jdbcType=VARCHAR},
      </if>
      <if test="dateOutwarranty != null">
        #{dateOutwarranty,jdbcType=VARCHAR},
      </if>
      <if test="datePurchase != null">
        #{datePurchase,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="virtType != null">
        #{virtType,jdbcType=VARCHAR},
      </if>
      <if test="riverType != null">
        #{riverType,jdbcType=VARCHAR},
      </if>
      <if test="networkType != null">
        #{networkType,jdbcType=VARCHAR},
      </if>
      <if test="storageNetworkType != null">
        #{storageNetworkType,jdbcType=VARCHAR},
      </if>
      <if test="storageType != null">
        #{storageType,jdbcType=VARCHAR},
      </if>
      <if test="cpuGeneration != null">
        #{cpuGeneration,jdbcType=VARCHAR},
      </if>
      <if test="onEcs != null">
        #{onEcs,jdbcType=VARCHAR},
      </if>
      <if test="exceptionName != null">
        #{exceptionName,jdbcType=VARCHAR},
      </if>
      <if test="recoverTime != null">
        #{recoverTime,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreated != null">
        #{gmtCreated,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="ncDetailId != null">
        nc_detail_id = #{ncDetailId,jdbcType=BIGINT},
      </if>
      <if test="exceptionTime != null">
        exception_time = #{exceptionTime,jdbcType=VARCHAR},
      </if>
      <if test="riskId != null">
        risk_id = #{riskId,jdbcType=BIGINT},
      </if>
      <if test="ncIp != null">
        nc_ip = #{ncIp,jdbcType=VARCHAR},
      </if>
      <if test="ncId != null">
        nc_id = #{ncId,jdbcType=VARCHAR},
      </if>
      <if test="ncSn != null">
        nc_sn = #{ncSn,jdbcType=VARCHAR},
      </if>
      <if test="hostname != null">
        hostname = #{hostname,jdbcType=VARCHAR},
      </if>
      <if test="cluster != null">
        `cluster` = #{cluster,jdbcType=VARCHAR},
      </if>
      <if test="clusterUsage != null">
        cluster_usage = #{clusterUsage,jdbcType=VARCHAR},
      </if>
      <if test="azone != null">
        azone = #{azone,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        region = #{region,jdbcType=VARCHAR},
      </if>
      <if test="idc != null">
        idc = #{idc,jdbcType=VARCHAR},
      </if>
      <if test="room != null">
        room = #{room,jdbcType=VARCHAR},
      </if>
      <if test="rack != null">
        rack = #{rack,jdbcType=VARCHAR},
      </if>
      <if test="aswId != null">
        asw_id = #{aswId,jdbcType=VARCHAR},
      </if>
      <if test="vcpuMod != null">
        vcpu_mod = #{vcpuMod,jdbcType=VARCHAR},
      </if>
      <if test="physicalModel != null">
        physical_model = #{physicalModel,jdbcType=VARCHAR},
      </if>
      <if test="cpuModel != null">
        cpu_model = #{cpuModel,jdbcType=VARCHAR},
      </if>
      <if test="ncVcpu != null">
        nc_vcpu = #{ncVcpu,jdbcType=VARCHAR},
      </if>
      <if test="avsVersion != null">
        avs_version = #{avsVersion,jdbcType=VARCHAR},
      </if>
      <if test="ncCategory != null">
        nc_category = #{ncCategory,jdbcType=VARCHAR},
      </if>
      <if test="dateOutwarranty != null">
        date_outwarranty = #{dateOutwarranty,jdbcType=VARCHAR},
      </if>
      <if test="datePurchase != null">
        date_purchase = #{datePurchase,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="virtType != null">
        virt_type = #{virtType,jdbcType=VARCHAR},
      </if>
      <if test="riverType != null">
        river_type = #{riverType,jdbcType=VARCHAR},
      </if>
      <if test="networkType != null">
        network_type = #{networkType,jdbcType=VARCHAR},
      </if>
      <if test="storageNetworkType != null">
        storage_network_type = #{storageNetworkType,jdbcType=VARCHAR},
      </if>
      <if test="storageType != null">
        storage_type = #{storageType,jdbcType=VARCHAR},
      </if>
      <if test="cpuGeneration != null">
        cpu_generation = #{cpuGeneration,jdbcType=VARCHAR},
      </if>
      <if test="onEcs != null">
        on_ecs = #{onEcs,jdbcType=VARCHAR},
      </if>
      <if test="exceptionName != null">
        exception_name = #{exceptionName,jdbcType=VARCHAR},
      </if>
      <if test="recoverTime != null">
        recover_time = #{recoverTime,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreated != null">
        gmt_created = #{gmtCreated,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="upsert" keyColumn="nc_detail_id" keyProperty="ncDetailId" parameterType="com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetail" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into allinone_risk_nc_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ncDetailId != null">
        nc_detail_id,
      </if>
      exception_time,
      risk_id,
      nc_ip,
      nc_id,
      nc_sn,
      hostname,
      `cluster`,
      cluster_usage,
      azone,
      region,
      idc,
      room,
      rack,
      asw_id,
      vcpu_mod,
      physical_model,
      cpu_model,
      nc_vcpu,
      avs_version,
      nc_category,
      date_outwarranty,
      date_purchase,
      manufacturer,
      product_name,
      virt_type,
      river_type,
      network_type,
      storage_network_type,
      storage_type,
      cpu_generation,
      on_ecs,
      exception_name,
      recover_time,
      gmt_created,
      gmt_modified,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ncDetailId != null">
        #{ncDetailId,jdbcType=BIGINT},
      </if>
      #{exceptionTime,jdbcType=VARCHAR},
      #{riskId,jdbcType=BIGINT},
      #{ncIp,jdbcType=VARCHAR},
      #{ncId,jdbcType=VARCHAR},
      #{ncSn,jdbcType=VARCHAR},
      #{hostname,jdbcType=VARCHAR},
      #{cluster,jdbcType=VARCHAR},
      #{clusterUsage,jdbcType=VARCHAR},
      #{azone,jdbcType=VARCHAR},
      #{region,jdbcType=VARCHAR},
      #{idc,jdbcType=VARCHAR},
      #{room,jdbcType=VARCHAR},
      #{rack,jdbcType=VARCHAR},
      #{aswId,jdbcType=VARCHAR},
      #{vcpuMod,jdbcType=VARCHAR},
      #{physicalModel,jdbcType=VARCHAR},
      #{cpuModel,jdbcType=VARCHAR},
      #{ncVcpu,jdbcType=VARCHAR},
      #{avsVersion,jdbcType=VARCHAR},
      #{ncCategory,jdbcType=VARCHAR},
      #{dateOutwarranty,jdbcType=VARCHAR},
      #{datePurchase,jdbcType=VARCHAR},
      #{manufacturer,jdbcType=VARCHAR},
      #{productName,jdbcType=VARCHAR},
      #{virtType,jdbcType=VARCHAR},
      #{riverType,jdbcType=VARCHAR},
      #{networkType,jdbcType=VARCHAR},
      #{storageNetworkType,jdbcType=VARCHAR},
      #{storageType,jdbcType=VARCHAR},
      #{cpuGeneration,jdbcType=VARCHAR},
      #{onEcs,jdbcType=VARCHAR},
      #{exceptionName,jdbcType=VARCHAR},
      #{recoverTime,jdbcType=VARCHAR},
      #{gmtCreated,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="ncDetailId != null">
        nc_detail_id = #{ncDetailId,jdbcType=BIGINT},
      </if>
      exception_time = #{exceptionTime,jdbcType=VARCHAR},
      risk_id = #{riskId,jdbcType=BIGINT},
      nc_ip = #{ncIp,jdbcType=VARCHAR},
      nc_id = #{ncId,jdbcType=VARCHAR},
      nc_sn = #{ncSn,jdbcType=VARCHAR},
      hostname = #{hostname,jdbcType=VARCHAR},
      `cluster` = #{cluster,jdbcType=VARCHAR},
      cluster_usage = #{clusterUsage,jdbcType=VARCHAR},
      azone = #{azone,jdbcType=VARCHAR},
      region = #{region,jdbcType=VARCHAR},
      idc = #{idc,jdbcType=VARCHAR},
      room = #{room,jdbcType=VARCHAR},
      rack = #{rack,jdbcType=VARCHAR},
      asw_id = #{aswId,jdbcType=VARCHAR},
      vcpu_mod = #{vcpuMod,jdbcType=VARCHAR},
      physical_model = #{physicalModel,jdbcType=VARCHAR},
      cpu_model = #{cpuModel,jdbcType=VARCHAR},
      nc_vcpu = #{ncVcpu,jdbcType=VARCHAR},
      avs_version = #{avsVersion,jdbcType=VARCHAR},
      nc_category = #{ncCategory,jdbcType=VARCHAR},
      date_outwarranty = #{dateOutwarranty,jdbcType=VARCHAR},
      date_purchase = #{datePurchase,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      virt_type = #{virtType,jdbcType=VARCHAR},
      river_type = #{riverType,jdbcType=VARCHAR},
      network_type = #{networkType,jdbcType=VARCHAR},
      storage_network_type = #{storageNetworkType,jdbcType=VARCHAR},
      storage_type = #{storageType,jdbcType=VARCHAR},
      cpu_generation = #{cpuGeneration,jdbcType=VARCHAR},
      on_ecs = #{onEcs,jdbcType=VARCHAR},
      exception_name = #{exceptionName,jdbcType=VARCHAR},
      recover_time = #{recoverTime,jdbcType=VARCHAR},
      gmt_created = #{gmtCreated,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
    </trim>
  </insert>
  <select id="selectOneByExample" parameterType="com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from allinone_risk_nc_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from allinone_risk_nc_detail
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
</mapper>