package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import com.aliyun.xdragon.common.enumeration.log.PatternTagType;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/02/13
 */
@Data
public class GetSuggestReasonRequest extends BaseTaskRequest implements Serializable {
    private static final long serialVersionUID = 8775041906786339140L;

    @NotNull
    PatternTagType type;
}
