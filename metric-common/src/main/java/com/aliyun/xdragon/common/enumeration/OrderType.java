package com.aliyun.xdragon.common.enumeration;

/**
 * <AUTHOR>
 * @date 2022/04/27
 */
public enum OrderType {
    DESC("desc"),
    ASC("asc");
    private String order;
    OrderType(String order) {
        this.order = order;
    }

    public String getOrder() {
        return order;
    }

    public static OrderType of(String order) {
        for(OrderType o: OrderType.values()) {
            if (o.getOrder().equals(order)) {
                return o;
            }
        }
        return null;
    }

    public static boolean check(String order) {
        OrderType t = of(order);
        return t != null;
    }
}
