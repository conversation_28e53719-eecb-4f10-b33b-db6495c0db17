package com.aliyun.xdragon.common.model.clientops.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.Size;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/06/24
 */
@Data
public class UserInfoRequest implements Serializable {
    private static final long serialVersionUID = -9171212284071678949L;
    @Size(min = 1, max = 20, message = "uid size must be between 1 and 20")
    private List<String> uids;
}
