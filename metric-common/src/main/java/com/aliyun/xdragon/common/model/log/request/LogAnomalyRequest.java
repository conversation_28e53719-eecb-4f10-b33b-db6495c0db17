package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/02/01
 */
@Data
public class LogAnomalyRequest implements Serializable {
    private static final long serialVersionUID = -7777211386166448111L;

    /**
     * nc info list
     */
    @NotNull
    List<String> ip;

    /**
     * start time in ms for query range
     */
    @NotNull
    Long startMs;

    /**
     * end time in ms for query range
     */
    @NotNull
    Long endMs;
}
