package com.aliyun.xdragon.common.model.workItem.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/11/28
 */

@Data
public class ClientListWorkItemRequest implements Serializable {

    private static final long serialVersionUID = 3570914224933733570L;
    /**
     * query aliUid
     */
    @NotNull
    @NotEmpty
    String aliUid;

    /**
     * query cid
     */
    String cid;

    /**
     * query start time
     */
    @NotNull
    @Positive
    Long startMs;

    /**
     * query end time
     */
    @NotNull
    @Positive
    Long endMs;

    public ClientListWorkItemRequest() {}
}
