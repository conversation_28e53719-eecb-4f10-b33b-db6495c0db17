<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.model.map.LogClusterMetricMapper">
  <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.model.LogClusterMetric">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="time_period" jdbcType="BIGINT" property="timePeriod" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="task_description" jdbcType="VARCHAR" property="taskDescription" />
    <result column="type" jdbcType="BIGINT" property="type" />
    <result column="log_cnt" jdbcType="DOUBLE" property="logCnt" />
    <result column="number_metric" jdbcType="DOUBLE" property="numberMetric" />
    <result column="anomaly_type" jdbcType="BIGINT" property="anomalyType" />
    <result column="anomaly_cnt" jdbcType="BIGINT" property="anomalyCnt" />
    <result column="anomaly_related_cnt" jdbcType="BIGINT" property="anomalyRelatedCnt" />
    <result column="anomaly_metric" jdbcType="DOUBLE" property="anomalyMetric" />
    <result column="metric" jdbcType="DOUBLE" property="metric" />
    <result column="remain_cnt" jdbcType="BIGINT" property="remainCnt" />
    <result column="library_cnt" jdbcType="BIGINT" property="libraryCnt" />
    <result column="after_filtered_cnt" jdbcType="BIGINT" property="afterFilteredCnt" />
    <result column="clustered_cnt" jdbcType="BIGINT" property="clusteredCnt" />
    <result column="reclustered_cnt" jdbcType="BIGINT" property="reclusteredCnt" />
    <result column="first_level_cnt" jdbcType="BIGINT" property="firstLevelCnt" />
    <result column="second_level_cnt" jdbcType="BIGINT" property="secondLevelCnt" />
    <result column="third_level_cnt" jdbcType="BIGINT" property="thirdLevelCnt" />
    <result column="type_cnt" jdbcType="BIGINT" property="typeCnt" />
    <result column="deleted_cnt" jdbcType="BIGINT" property="deletedCnt" />
    <result column="expired_cnt" jdbcType="BIGINT" property="expiredCnt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, time_period, start_time, end_time, task_id, task_description, `type`, log_cnt, 
    number_metric, anomaly_type, anomaly_cnt, anomaly_related_cnt, anomaly_metric, metric, 
    remain_cnt, library_cnt, after_filtered_cnt, clustered_cnt, reclustered_cnt, first_level_cnt, 
    second_level_cnt, third_level_cnt, type_cnt, deleted_cnt, expired_cnt
  </sql>
  <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogClusterMetricExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from log_cluster_metric
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="example != null and example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from log_cluster_metric
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from log_cluster_metric
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from log_cluster_metric
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from log_cluster_metric
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogClusterMetricExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from log_cluster_metric
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.model.LogClusterMetric">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into log_cluster_metric (time_period, start_time, end_time, 
      task_id, task_description, `type`, 
      log_cnt, number_metric, anomaly_type, 
      anomaly_cnt, anomaly_related_cnt, anomaly_metric, 
      metric, remain_cnt, library_cnt, 
      after_filtered_cnt, clustered_cnt, reclustered_cnt, 
      first_level_cnt, second_level_cnt, third_level_cnt, 
      type_cnt, deleted_cnt, expired_cnt
      )
    values (#{timePeriod,jdbcType=BIGINT}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{taskId,jdbcType=BIGINT}, #{taskDescription,jdbcType=VARCHAR}, #{type,jdbcType=BIGINT}, 
      #{logCnt,jdbcType=DOUBLE}, #{numberMetric,jdbcType=DOUBLE}, #{anomalyType,jdbcType=BIGINT}, 
      #{anomalyCnt,jdbcType=BIGINT}, #{anomalyRelatedCnt,jdbcType=BIGINT}, #{anomalyMetric,jdbcType=DOUBLE}, 
      #{metric,jdbcType=DOUBLE}, #{remainCnt,jdbcType=BIGINT}, #{libraryCnt,jdbcType=BIGINT}, 
      #{afterFilteredCnt,jdbcType=BIGINT}, #{clusteredCnt,jdbcType=BIGINT}, #{reclusteredCnt,jdbcType=BIGINT}, 
      #{firstLevelCnt,jdbcType=BIGINT}, #{secondLevelCnt,jdbcType=BIGINT}, #{thirdLevelCnt,jdbcType=BIGINT}, 
      #{typeCnt,jdbcType=BIGINT}, #{deletedCnt,jdbcType=BIGINT}, #{expiredCnt,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.model.LogClusterMetric">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into log_cluster_metric
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="timePeriod != null">
        time_period,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="taskDescription != null">
        task_description,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="logCnt != null">
        log_cnt,
      </if>
      <if test="numberMetric != null">
        number_metric,
      </if>
      <if test="anomalyType != null">
        anomaly_type,
      </if>
      <if test="anomalyCnt != null">
        anomaly_cnt,
      </if>
      <if test="anomalyRelatedCnt != null">
        anomaly_related_cnt,
      </if>
      <if test="anomalyMetric != null">
        anomaly_metric,
      </if>
      <if test="metric != null">
        metric,
      </if>
      <if test="remainCnt != null">
        remain_cnt,
      </if>
      <if test="libraryCnt != null">
        library_cnt,
      </if>
      <if test="afterFilteredCnt != null">
        after_filtered_cnt,
      </if>
      <if test="clusteredCnt != null">
        clustered_cnt,
      </if>
      <if test="reclusteredCnt != null">
        reclustered_cnt,
      </if>
      <if test="firstLevelCnt != null">
        first_level_cnt,
      </if>
      <if test="secondLevelCnt != null">
        second_level_cnt,
      </if>
      <if test="thirdLevelCnt != null">
        third_level_cnt,
      </if>
      <if test="typeCnt != null">
        type_cnt,
      </if>
      <if test="deletedCnt != null">
        deleted_cnt,
      </if>
      <if test="expiredCnt != null">
        expired_cnt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="timePeriod != null">
        #{timePeriod,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskDescription != null">
        #{taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=BIGINT},
      </if>
      <if test="logCnt != null">
        #{logCnt,jdbcType=DOUBLE},
      </if>
      <if test="numberMetric != null">
        #{numberMetric,jdbcType=DOUBLE},
      </if>
      <if test="anomalyType != null">
        #{anomalyType,jdbcType=BIGINT},
      </if>
      <if test="anomalyCnt != null">
        #{anomalyCnt,jdbcType=BIGINT},
      </if>
      <if test="anomalyRelatedCnt != null">
        #{anomalyRelatedCnt,jdbcType=BIGINT},
      </if>
      <if test="anomalyMetric != null">
        #{anomalyMetric,jdbcType=DOUBLE},
      </if>
      <if test="metric != null">
        #{metric,jdbcType=DOUBLE},
      </if>
      <if test="remainCnt != null">
        #{remainCnt,jdbcType=BIGINT},
      </if>
      <if test="libraryCnt != null">
        #{libraryCnt,jdbcType=BIGINT},
      </if>
      <if test="afterFilteredCnt != null">
        #{afterFilteredCnt,jdbcType=BIGINT},
      </if>
      <if test="clusteredCnt != null">
        #{clusteredCnt,jdbcType=BIGINT},
      </if>
      <if test="reclusteredCnt != null">
        #{reclusteredCnt,jdbcType=BIGINT},
      </if>
      <if test="firstLevelCnt != null">
        #{firstLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="secondLevelCnt != null">
        #{secondLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="thirdLevelCnt != null">
        #{thirdLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="typeCnt != null">
        #{typeCnt,jdbcType=BIGINT},
      </if>
      <if test="deletedCnt != null">
        #{deletedCnt,jdbcType=BIGINT},
      </if>
      <if test="expiredCnt != null">
        #{expiredCnt,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogClusterMetricExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from log_cluster_metric
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_cluster_metric
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.timePeriod != null">
        time_period = #{record.timePeriod,jdbcType=BIGINT},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.taskDescription != null">
        task_description = #{record.taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=BIGINT},
      </if>
      <if test="record.logCnt != null">
        log_cnt = #{record.logCnt,jdbcType=DOUBLE},
      </if>
      <if test="record.numberMetric != null">
        number_metric = #{record.numberMetric,jdbcType=DOUBLE},
      </if>
      <if test="record.anomalyType != null">
        anomaly_type = #{record.anomalyType,jdbcType=BIGINT},
      </if>
      <if test="record.anomalyCnt != null">
        anomaly_cnt = #{record.anomalyCnt,jdbcType=BIGINT},
      </if>
      <if test="record.anomalyRelatedCnt != null">
        anomaly_related_cnt = #{record.anomalyRelatedCnt,jdbcType=BIGINT},
      </if>
      <if test="record.anomalyMetric != null">
        anomaly_metric = #{record.anomalyMetric,jdbcType=DOUBLE},
      </if>
      <if test="record.metric != null">
        metric = #{record.metric,jdbcType=DOUBLE},
      </if>
      <if test="record.remainCnt != null">
        remain_cnt = #{record.remainCnt,jdbcType=BIGINT},
      </if>
      <if test="record.libraryCnt != null">
        library_cnt = #{record.libraryCnt,jdbcType=BIGINT},
      </if>
      <if test="record.afterFilteredCnt != null">
        after_filtered_cnt = #{record.afterFilteredCnt,jdbcType=BIGINT},
      </if>
      <if test="record.clusteredCnt != null">
        clustered_cnt = #{record.clusteredCnt,jdbcType=BIGINT},
      </if>
      <if test="record.reclusteredCnt != null">
        reclustered_cnt = #{record.reclusteredCnt,jdbcType=BIGINT},
      </if>
      <if test="record.firstLevelCnt != null">
        first_level_cnt = #{record.firstLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="record.secondLevelCnt != null">
        second_level_cnt = #{record.secondLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="record.thirdLevelCnt != null">
        third_level_cnt = #{record.thirdLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="record.typeCnt != null">
        type_cnt = #{record.typeCnt,jdbcType=BIGINT},
      </if>
      <if test="record.deletedCnt != null">
        deleted_cnt = #{record.deletedCnt,jdbcType=BIGINT},
      </if>
      <if test="record.expiredCnt != null">
        expired_cnt = #{record.expiredCnt,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_cluster_metric
    set id = #{record.id,jdbcType=BIGINT},
      time_period = #{record.timePeriod,jdbcType=BIGINT},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      task_id = #{record.taskId,jdbcType=BIGINT},
      task_description = #{record.taskDescription,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=BIGINT},
      log_cnt = #{record.logCnt,jdbcType=DOUBLE},
      number_metric = #{record.numberMetric,jdbcType=DOUBLE},
      anomaly_type = #{record.anomalyType,jdbcType=BIGINT},
      anomaly_cnt = #{record.anomalyCnt,jdbcType=BIGINT},
      anomaly_related_cnt = #{record.anomalyRelatedCnt,jdbcType=BIGINT},
      anomaly_metric = #{record.anomalyMetric,jdbcType=DOUBLE},
      metric = #{record.metric,jdbcType=DOUBLE},
      remain_cnt = #{record.remainCnt,jdbcType=BIGINT},
      library_cnt = #{record.libraryCnt,jdbcType=BIGINT},
      after_filtered_cnt = #{record.afterFilteredCnt,jdbcType=BIGINT},
      clustered_cnt = #{record.clusteredCnt,jdbcType=BIGINT},
      reclustered_cnt = #{record.reclusteredCnt,jdbcType=BIGINT},
      first_level_cnt = #{record.firstLevelCnt,jdbcType=BIGINT},
      second_level_cnt = #{record.secondLevelCnt,jdbcType=BIGINT},
      third_level_cnt = #{record.thirdLevelCnt,jdbcType=BIGINT},
      type_cnt = #{record.typeCnt,jdbcType=BIGINT},
      deleted_cnt = #{record.deletedCnt,jdbcType=BIGINT},
      expired_cnt = #{record.expiredCnt,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.xdragon.common.generate.model.LogClusterMetric">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_cluster_metric
    <set>
      <if test="timePeriod != null">
        time_period = #{timePeriod,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskDescription != null">
        task_description = #{taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=BIGINT},
      </if>
      <if test="logCnt != null">
        log_cnt = #{logCnt,jdbcType=DOUBLE},
      </if>
      <if test="numberMetric != null">
        number_metric = #{numberMetric,jdbcType=DOUBLE},
      </if>
      <if test="anomalyType != null">
        anomaly_type = #{anomalyType,jdbcType=BIGINT},
      </if>
      <if test="anomalyCnt != null">
        anomaly_cnt = #{anomalyCnt,jdbcType=BIGINT},
      </if>
      <if test="anomalyRelatedCnt != null">
        anomaly_related_cnt = #{anomalyRelatedCnt,jdbcType=BIGINT},
      </if>
      <if test="anomalyMetric != null">
        anomaly_metric = #{anomalyMetric,jdbcType=DOUBLE},
      </if>
      <if test="metric != null">
        metric = #{metric,jdbcType=DOUBLE},
      </if>
      <if test="remainCnt != null">
        remain_cnt = #{remainCnt,jdbcType=BIGINT},
      </if>
      <if test="libraryCnt != null">
        library_cnt = #{libraryCnt,jdbcType=BIGINT},
      </if>
      <if test="afterFilteredCnt != null">
        after_filtered_cnt = #{afterFilteredCnt,jdbcType=BIGINT},
      </if>
      <if test="clusteredCnt != null">
        clustered_cnt = #{clusteredCnt,jdbcType=BIGINT},
      </if>
      <if test="reclusteredCnt != null">
        reclustered_cnt = #{reclusteredCnt,jdbcType=BIGINT},
      </if>
      <if test="firstLevelCnt != null">
        first_level_cnt = #{firstLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="secondLevelCnt != null">
        second_level_cnt = #{secondLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="thirdLevelCnt != null">
        third_level_cnt = #{thirdLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="typeCnt != null">
        type_cnt = #{typeCnt,jdbcType=BIGINT},
      </if>
      <if test="deletedCnt != null">
        deleted_cnt = #{deletedCnt,jdbcType=BIGINT},
      </if>
      <if test="expiredCnt != null">
        expired_cnt = #{expiredCnt,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.model.LogClusterMetric">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_cluster_metric
    set time_period = #{timePeriod,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      task_id = #{taskId,jdbcType=BIGINT},
      task_description = #{taskDescription,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=BIGINT},
      log_cnt = #{logCnt,jdbcType=DOUBLE},
      number_metric = #{numberMetric,jdbcType=DOUBLE},
      anomaly_type = #{anomalyType,jdbcType=BIGINT},
      anomaly_cnt = #{anomalyCnt,jdbcType=BIGINT},
      anomaly_related_cnt = #{anomalyRelatedCnt,jdbcType=BIGINT},
      anomaly_metric = #{anomalyMetric,jdbcType=DOUBLE},
      metric = #{metric,jdbcType=DOUBLE},
      remain_cnt = #{remainCnt,jdbcType=BIGINT},
      library_cnt = #{libraryCnt,jdbcType=BIGINT},
      after_filtered_cnt = #{afterFilteredCnt,jdbcType=BIGINT},
      clustered_cnt = #{clusteredCnt,jdbcType=BIGINT},
      reclustered_cnt = #{reclusteredCnt,jdbcType=BIGINT},
      first_level_cnt = #{firstLevelCnt,jdbcType=BIGINT},
      second_level_cnt = #{secondLevelCnt,jdbcType=BIGINT},
      third_level_cnt = #{thirdLevelCnt,jdbcType=BIGINT},
      type_cnt = #{typeCnt,jdbcType=BIGINT},
      deleted_cnt = #{deletedCnt,jdbcType=BIGINT},
      expired_cnt = #{expiredCnt,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.model.LogClusterMetricExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from log_cluster_metric
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_cluster_metric
    (time_period, start_time, end_time, task_id, task_description, `type`, log_cnt, number_metric, 
      anomaly_type, anomaly_cnt, anomaly_related_cnt, anomaly_metric, metric, remain_cnt, 
      library_cnt, after_filtered_cnt, clustered_cnt, reclustered_cnt, first_level_cnt, 
      second_level_cnt, third_level_cnt, type_cnt, deleted_cnt, expired_cnt)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.timePeriod,jdbcType=BIGINT}, #{item.startTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP}, 
        #{item.taskId,jdbcType=BIGINT}, #{item.taskDescription,jdbcType=VARCHAR}, #{item.type,jdbcType=BIGINT}, 
        #{item.logCnt,jdbcType=DOUBLE}, #{item.numberMetric,jdbcType=DOUBLE}, #{item.anomalyType,jdbcType=BIGINT}, 
        #{item.anomalyCnt,jdbcType=BIGINT}, #{item.anomalyRelatedCnt,jdbcType=BIGINT}, 
        #{item.anomalyMetric,jdbcType=DOUBLE}, #{item.metric,jdbcType=DOUBLE}, #{item.remainCnt,jdbcType=BIGINT}, 
        #{item.libraryCnt,jdbcType=BIGINT}, #{item.afterFilteredCnt,jdbcType=BIGINT}, #{item.clusteredCnt,jdbcType=BIGINT}, 
        #{item.reclusteredCnt,jdbcType=BIGINT}, #{item.firstLevelCnt,jdbcType=BIGINT}, 
        #{item.secondLevelCnt,jdbcType=BIGINT}, #{item.thirdLevelCnt,jdbcType=BIGINT}, 
        #{item.typeCnt,jdbcType=BIGINT}, #{item.deletedCnt,jdbcType=BIGINT}, #{item.expiredCnt,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_cluster_metric (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'time_period'.toString() == column.value">
          #{item.timePeriod,jdbcType=BIGINT}
        </if>
        <if test="'start_time'.toString() == column.value">
          #{item.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'task_id'.toString() == column.value">
          #{item.taskId,jdbcType=BIGINT}
        </if>
        <if test="'task_description'.toString() == column.value">
          #{item.taskDescription,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=BIGINT}
        </if>
        <if test="'log_cnt'.toString() == column.value">
          #{item.logCnt,jdbcType=DOUBLE}
        </if>
        <if test="'number_metric'.toString() == column.value">
          #{item.numberMetric,jdbcType=DOUBLE}
        </if>
        <if test="'anomaly_type'.toString() == column.value">
          #{item.anomalyType,jdbcType=BIGINT}
        </if>
        <if test="'anomaly_cnt'.toString() == column.value">
          #{item.anomalyCnt,jdbcType=BIGINT}
        </if>
        <if test="'anomaly_related_cnt'.toString() == column.value">
          #{item.anomalyRelatedCnt,jdbcType=BIGINT}
        </if>
        <if test="'anomaly_metric'.toString() == column.value">
          #{item.anomalyMetric,jdbcType=DOUBLE}
        </if>
        <if test="'metric'.toString() == column.value">
          #{item.metric,jdbcType=DOUBLE}
        </if>
        <if test="'remain_cnt'.toString() == column.value">
          #{item.remainCnt,jdbcType=BIGINT}
        </if>
        <if test="'library_cnt'.toString() == column.value">
          #{item.libraryCnt,jdbcType=BIGINT}
        </if>
        <if test="'after_filtered_cnt'.toString() == column.value">
          #{item.afterFilteredCnt,jdbcType=BIGINT}
        </if>
        <if test="'clustered_cnt'.toString() == column.value">
          #{item.clusteredCnt,jdbcType=BIGINT}
        </if>
        <if test="'reclustered_cnt'.toString() == column.value">
          #{item.reclusteredCnt,jdbcType=BIGINT}
        </if>
        <if test="'first_level_cnt'.toString() == column.value">
          #{item.firstLevelCnt,jdbcType=BIGINT}
        </if>
        <if test="'second_level_cnt'.toString() == column.value">
          #{item.secondLevelCnt,jdbcType=BIGINT}
        </if>
        <if test="'third_level_cnt'.toString() == column.value">
          #{item.thirdLevelCnt,jdbcType=BIGINT}
        </if>
        <if test="'type_cnt'.toString() == column.value">
          #{item.typeCnt,jdbcType=BIGINT}
        </if>
        <if test="'deleted_cnt'.toString() == column.value">
          #{item.deletedCnt,jdbcType=BIGINT}
        </if>
        <if test="'expired_cnt'.toString() == column.value">
          #{item.expiredCnt,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
  <insert id="upsertSelective" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.LogClusterMetric" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_cluster_metric
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="timePeriod != null">
        time_period,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="taskDescription != null">
        task_description,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="logCnt != null">
        log_cnt,
      </if>
      <if test="numberMetric != null">
        number_metric,
      </if>
      <if test="anomalyType != null">
        anomaly_type,
      </if>
      <if test="anomalyCnt != null">
        anomaly_cnt,
      </if>
      <if test="anomalyRelatedCnt != null">
        anomaly_related_cnt,
      </if>
      <if test="anomalyMetric != null">
        anomaly_metric,
      </if>
      <if test="metric != null">
        metric,
      </if>
      <if test="remainCnt != null">
        remain_cnt,
      </if>
      <if test="libraryCnt != null">
        library_cnt,
      </if>
      <if test="afterFilteredCnt != null">
        after_filtered_cnt,
      </if>
      <if test="clusteredCnt != null">
        clustered_cnt,
      </if>
      <if test="reclusteredCnt != null">
        reclustered_cnt,
      </if>
      <if test="firstLevelCnt != null">
        first_level_cnt,
      </if>
      <if test="secondLevelCnt != null">
        second_level_cnt,
      </if>
      <if test="thirdLevelCnt != null">
        third_level_cnt,
      </if>
      <if test="typeCnt != null">
        type_cnt,
      </if>
      <if test="deletedCnt != null">
        deleted_cnt,
      </if>
      <if test="expiredCnt != null">
        expired_cnt,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="timePeriod != null">
        #{timePeriod,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskDescription != null">
        #{taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=BIGINT},
      </if>
      <if test="logCnt != null">
        #{logCnt,jdbcType=DOUBLE},
      </if>
      <if test="numberMetric != null">
        #{numberMetric,jdbcType=DOUBLE},
      </if>
      <if test="anomalyType != null">
        #{anomalyType,jdbcType=BIGINT},
      </if>
      <if test="anomalyCnt != null">
        #{anomalyCnt,jdbcType=BIGINT},
      </if>
      <if test="anomalyRelatedCnt != null">
        #{anomalyRelatedCnt,jdbcType=BIGINT},
      </if>
      <if test="anomalyMetric != null">
        #{anomalyMetric,jdbcType=DOUBLE},
      </if>
      <if test="metric != null">
        #{metric,jdbcType=DOUBLE},
      </if>
      <if test="remainCnt != null">
        #{remainCnt,jdbcType=BIGINT},
      </if>
      <if test="libraryCnt != null">
        #{libraryCnt,jdbcType=BIGINT},
      </if>
      <if test="afterFilteredCnt != null">
        #{afterFilteredCnt,jdbcType=BIGINT},
      </if>
      <if test="clusteredCnt != null">
        #{clusteredCnt,jdbcType=BIGINT},
      </if>
      <if test="reclusteredCnt != null">
        #{reclusteredCnt,jdbcType=BIGINT},
      </if>
      <if test="firstLevelCnt != null">
        #{firstLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="secondLevelCnt != null">
        #{secondLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="thirdLevelCnt != null">
        #{thirdLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="typeCnt != null">
        #{typeCnt,jdbcType=BIGINT},
      </if>
      <if test="deletedCnt != null">
        #{deletedCnt,jdbcType=BIGINT},
      </if>
      <if test="expiredCnt != null">
        #{expiredCnt,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="timePeriod != null">
        time_period = #{timePeriod,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskDescription != null">
        task_description = #{taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=BIGINT},
      </if>
      <if test="logCnt != null">
        log_cnt = #{logCnt,jdbcType=DOUBLE},
      </if>
      <if test="numberMetric != null">
        number_metric = #{numberMetric,jdbcType=DOUBLE},
      </if>
      <if test="anomalyType != null">
        anomaly_type = #{anomalyType,jdbcType=BIGINT},
      </if>
      <if test="anomalyCnt != null">
        anomaly_cnt = #{anomalyCnt,jdbcType=BIGINT},
      </if>
      <if test="anomalyRelatedCnt != null">
        anomaly_related_cnt = #{anomalyRelatedCnt,jdbcType=BIGINT},
      </if>
      <if test="anomalyMetric != null">
        anomaly_metric = #{anomalyMetric,jdbcType=DOUBLE},
      </if>
      <if test="metric != null">
        metric = #{metric,jdbcType=DOUBLE},
      </if>
      <if test="remainCnt != null">
        remain_cnt = #{remainCnt,jdbcType=BIGINT},
      </if>
      <if test="libraryCnt != null">
        library_cnt = #{libraryCnt,jdbcType=BIGINT},
      </if>
      <if test="afterFilteredCnt != null">
        after_filtered_cnt = #{afterFilteredCnt,jdbcType=BIGINT},
      </if>
      <if test="clusteredCnt != null">
        clustered_cnt = #{clusteredCnt,jdbcType=BIGINT},
      </if>
      <if test="reclusteredCnt != null">
        reclustered_cnt = #{reclusteredCnt,jdbcType=BIGINT},
      </if>
      <if test="firstLevelCnt != null">
        first_level_cnt = #{firstLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="secondLevelCnt != null">
        second_level_cnt = #{secondLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="thirdLevelCnt != null">
        third_level_cnt = #{thirdLevelCnt,jdbcType=BIGINT},
      </if>
      <if test="typeCnt != null">
        type_cnt = #{typeCnt,jdbcType=BIGINT},
      </if>
      <if test="deletedCnt != null">
        deleted_cnt = #{deletedCnt,jdbcType=BIGINT},
      </if>
      <if test="expiredCnt != null">
        expired_cnt = #{expiredCnt,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <insert id="upsert" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.LogClusterMetric" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_cluster_metric
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      time_period,
      start_time,
      end_time,
      task_id,
      task_description,
      `type`,
      log_cnt,
      number_metric,
      anomaly_type,
      anomaly_cnt,
      anomaly_related_cnt,
      anomaly_metric,
      metric,
      remain_cnt,
      library_cnt,
      after_filtered_cnt,
      clustered_cnt,
      reclustered_cnt,
      first_level_cnt,
      second_level_cnt,
      third_level_cnt,
      type_cnt,
      deleted_cnt,
      expired_cnt,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{timePeriod,jdbcType=BIGINT},
      #{startTime,jdbcType=TIMESTAMP},
      #{endTime,jdbcType=TIMESTAMP},
      #{taskId,jdbcType=BIGINT},
      #{taskDescription,jdbcType=VARCHAR},
      #{type,jdbcType=BIGINT},
      #{logCnt,jdbcType=DOUBLE},
      #{numberMetric,jdbcType=DOUBLE},
      #{anomalyType,jdbcType=BIGINT},
      #{anomalyCnt,jdbcType=BIGINT},
      #{anomalyRelatedCnt,jdbcType=BIGINT},
      #{anomalyMetric,jdbcType=DOUBLE},
      #{metric,jdbcType=DOUBLE},
      #{remainCnt,jdbcType=BIGINT},
      #{libraryCnt,jdbcType=BIGINT},
      #{afterFilteredCnt,jdbcType=BIGINT},
      #{clusteredCnt,jdbcType=BIGINT},
      #{reclusteredCnt,jdbcType=BIGINT},
      #{firstLevelCnt,jdbcType=BIGINT},
      #{secondLevelCnt,jdbcType=BIGINT},
      #{thirdLevelCnt,jdbcType=BIGINT},
      #{typeCnt,jdbcType=BIGINT},
      #{deletedCnt,jdbcType=BIGINT},
      #{expiredCnt,jdbcType=BIGINT},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      time_period = #{timePeriod,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      task_id = #{taskId,jdbcType=BIGINT},
      task_description = #{taskDescription,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=BIGINT},
      log_cnt = #{logCnt,jdbcType=DOUBLE},
      number_metric = #{numberMetric,jdbcType=DOUBLE},
      anomaly_type = #{anomalyType,jdbcType=BIGINT},
      anomaly_cnt = #{anomalyCnt,jdbcType=BIGINT},
      anomaly_related_cnt = #{anomalyRelatedCnt,jdbcType=BIGINT},
      anomaly_metric = #{anomalyMetric,jdbcType=DOUBLE},
      metric = #{metric,jdbcType=DOUBLE},
      remain_cnt = #{remainCnt,jdbcType=BIGINT},
      library_cnt = #{libraryCnt,jdbcType=BIGINT},
      after_filtered_cnt = #{afterFilteredCnt,jdbcType=BIGINT},
      clustered_cnt = #{clusteredCnt,jdbcType=BIGINT},
      reclustered_cnt = #{reclusteredCnt,jdbcType=BIGINT},
      first_level_cnt = #{firstLevelCnt,jdbcType=BIGINT},
      second_level_cnt = #{secondLevelCnt,jdbcType=BIGINT},
      third_level_cnt = #{thirdLevelCnt,jdbcType=BIGINT},
      type_cnt = #{typeCnt,jdbcType=BIGINT},
      deleted_cnt = #{deletedCnt,jdbcType=BIGINT},
      expired_cnt = #{expiredCnt,jdbcType=BIGINT},
    </trim>
  </insert>
  <select id="selectOneByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogClusterMetricExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from log_cluster_metric
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from log_cluster_metric
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
</mapper>