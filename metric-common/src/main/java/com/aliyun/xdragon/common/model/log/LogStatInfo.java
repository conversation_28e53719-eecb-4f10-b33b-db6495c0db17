package com.aliyun.xdragon.common.model.log;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.aliyun.xdragon.common.generate.model.LogDetailStatistics;
import com.aliyun.xdragon.common.generate.model.LogOverallStatistics;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/20
 */
@Data
public class LogStatInfo {
    private Date startDate;
    private Date endDate;
    private Integer validTaskIdCnt;
    private Integer validRegionCidCnt;
    private Integer monitorTaskCnt;
    private Long totalPatternTypeCnt;
    private Long totalPatternCnt;
    private List<LogStatDetail> detailList;

    public LogStatInfo() {}

    public LogStatInfo(Date start, Date end, Integer taskCnt, Integer cidCnt, Integer monitorCnt, Long type, Long cnt,
        List<LogStatDetail> detailList) {
        this.startDate = start;
        this.endDate = end;
        this.validTaskIdCnt = taskCnt;
        this.validRegionCidCnt = cidCnt;
        this.monitorTaskCnt = monitorCnt;
        this.totalPatternTypeCnt = type;
        this.totalPatternCnt = cnt;
        this.detailList = detailList;
    }

    public LogStatInfo(LogOverallStatistics overall, List<LogDetailStatistics> detailList) {
        startDate = overall.getStartTime();
        endDate = overall.getEndTime();
        validTaskIdCnt = overall.getTaskCnt();
        validRegionCidCnt = overall.getCidCnt();
        monitorTaskCnt = overall.getMonitorCnt();
        totalPatternTypeCnt = overall.getTotalType();
        totalPatternCnt = overall.getTotolCnt();
        this.detailList = new ArrayList<>(detailList.size());
        detailList.forEach(d -> this.detailList.add(new LogStatDetail(d)));
    }

    public Long getTotalNewPatternTypeCnt() {
        return detailList.stream().mapToLong(LogStatDetail::getNewPatternTypeCnt).sum();
    }
}
