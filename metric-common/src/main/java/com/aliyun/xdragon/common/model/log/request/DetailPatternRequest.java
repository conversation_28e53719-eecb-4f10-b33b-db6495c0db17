package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/01/30
 */
@Data
public class DetailPatternRequest extends BasePatternRequest implements Serializable {
    private static final long serialVersionUID = -4686510054678819523L;

    /**
     * md5 for request pattern
     */
    @NotNull
    String patternMd5;

    /**
     * field name
     */
    String field = "ncip";

    /**
     * condition map for advanced search
     */
    Map<String, List<String>> conditionMap = null;

    List<PatternCondition> conditions;
}
