package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/24
 */
@Data
public class QueryAssociationInfoDistributionsRequest extends BaseTaskRequest implements Serializable {
    private static final long serialVersionUID = -4258095597915068820L;

    /**
     * region list, null or empty for all region
     */
    List<String> regions;
    /**
     * pattern md5
     */
    @NotBlank
    String patternMd5;
    /**
     * query start time, in ms
     */
    @NotNull
    @Positive
    Long startMs;
    /**
     * query end time, in ms
     */
    @NotNull
    @Positive
    Long endMs;

    /**
     * association name
     */
    @NotBlank
    String associationName;
}
