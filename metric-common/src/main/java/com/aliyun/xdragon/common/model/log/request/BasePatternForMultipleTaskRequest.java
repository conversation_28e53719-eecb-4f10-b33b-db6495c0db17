package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/07/07
 */
@Data
public class BasePatternForMultipleTaskRequest extends BaseMultipleTaskRequest implements Serializable {
    private static final long serialVersionUID = 7165971182086899875L;

    /**
     * query start time, in ms
     */
    @NotNull
    private Long startMs;

    /**
     * query end time, in ms
     */
    @NotNull
    private Long endMs;

    public BasePatternForMultipleTaskRequest() {}

    public BasePatternForMultipleTaskRequest(List<Long> taskIds, Long startMs, Long endMs) {
        setTaskIds(taskIds);
        this.startMs = startMs;
        this.endMs = endMs;
    }
}
