package com.aliyun.xdragon.common.exception;

/**
 * <AUTHOR>
 * @date 2022/04/12
 */
public class EvalDataSourceException extends XdragonMetricSystemException {

    private static final String CODE = "EVAL_DATASOURCE_ERROR";

    public EvalDataSourceException(String message) {
        super(message);
    }

    public EvalDataSourceException(String message, Throwable e) {
        super(message, e);
    }

    public EvalDataSourceException(Throwable e) {
        super(e);
    }

    @Override
    public String getCode() {
        return CODE;
    }
}
