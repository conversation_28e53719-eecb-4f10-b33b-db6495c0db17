package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/08/30
 */
@Data
public class UpdateLibraryPatternRequest extends BaseTaskRequest implements Serializable {
    private static final long serialVersionUID = 2530894278193536460L;

    @NotBlank
    String md5;

    @NotBlank
    String pattern;

    Integer status;
}
