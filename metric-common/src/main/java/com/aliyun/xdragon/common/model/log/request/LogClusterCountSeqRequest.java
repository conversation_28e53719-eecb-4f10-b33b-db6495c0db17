package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.aliyun.xdragon.common.enumeration.log.PatternTagType;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/09/23
 */

@Data
public class LogClusterCountSeqRequest extends BasePatternRequest implements Serializable {
    private static final long serialVersionUID = -1634423121436106800L;

    /**
     * pattern tag type
     */
    PatternTagType type;
    /**
     * query tag filter
     */
    TagFlag anomalyFlag;
    /**
     * condition map
     */
    Map<String, List<String>> conditionMap;

    List<PatternCondition> conditions;

    public LogClusterCountSeqRequest() {}

    public LogClusterCountSeqRequest(Long taskId, Long startMs, Long endMs, List<String> regions, PatternTagType type,
        TagFlag anomalyFlag, Map<String, List<String>> conditionMap) {
        setTaskId(taskId);
        this.startMs = startMs;
        this.endMs = endMs;
        this.regions = regions;
        this.type = type;
        this.anomalyFlag = anomalyFlag;
        this.conditionMap = conditionMap;
    }

    public boolean check() {
        if (getTaskId() == null && startMs == null && endMs == null && type == null) {
            return false;
        }
        if (type.equals(PatternTagType.UNKNOWN) || (type.equals(PatternTagType.ANOMALY) && anomalyFlag == null)) {
            return false;
        }
        return true;
    }
}
