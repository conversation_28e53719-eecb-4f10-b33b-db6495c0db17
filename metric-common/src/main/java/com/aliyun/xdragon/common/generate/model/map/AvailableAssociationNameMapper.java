package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.AvailableAssociationName;
import com.aliyun.xdragon.common.generate.model.AvailableAssociationNameExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface AvailableAssociationNameMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table available_association_name
     *
     * @mbg.generated
     */
    long countByExample(AvailableAssociationNameExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table available_association_name
     *
     * @mbg.generated
     */
    int deleteByExample(AvailableAssociationNameExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table available_association_name
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long taskId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table available_association_name
     *
     * @mbg.generated
     */
    int insert(AvailableAssociationName record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table available_association_name
     *
     * @mbg.generated
     */
    int insertSelective(AvailableAssociationName record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table available_association_name
     *
     * @mbg.generated
     */
    List<AvailableAssociationName> selectByExampleWithRowbounds(AvailableAssociationNameExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table available_association_name
     *
     * @mbg.generated
     */
    List<AvailableAssociationName> selectByExample(AvailableAssociationNameExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table available_association_name
     *
     * @mbg.generated
     */
    AvailableAssociationName selectByPrimaryKey(Long taskId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table available_association_name
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") AvailableAssociationName record, @Param("example") AvailableAssociationNameExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table available_association_name
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") AvailableAssociationName record, @Param("example") AvailableAssociationNameExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table available_association_name
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AvailableAssociationName record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table available_association_name
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AvailableAssociationName record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table available_association_name
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<AvailableAssociationName> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table available_association_name
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<AvailableAssociationName> list, @Param("selective") AvailableAssociationName.Column ... selective);
}