
package com.aliyun.xdragon.common.model;

import java.io.Serializable;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class Customfield implements Serializable {

    private static final long serialVersionUID = -1444148934767830962L;
    @SerializedName("isRemember")
    @Expose
    private Boolean isRemember;
    @SerializedName("nameI18N")
    @Expose
    private String nameI18N;
    @SerializedName("defaultValue")
    @Expose
    private String defaultValue;
    @SerializedName("minLength")
    @Expose
    private Integer minLength;
    @SerializedName("isForbiddenEditRequired")
    @Expose
    private Boolean isForbiddenEditRequired;
    @SerializedName("description")
    @Expose
    private String description;
    @SerializedName("type")
    @Expose
    private String type;
    @SerializedName("createdAt")
    @Expose
    private Long createdAt;
    @SerializedName("dynamic")
    @Expose
    private Boolean dynamic;
    @SerializedName("id")
    @Expose
    private Long id;
    @SerializedName("updatedAt")
    @Expose
    private Long updatedAt;
    @SerializedName("fieldFormat")
    @Expose
    private String fieldFormat;
    @SerializedName("possibleValues")
    @Expose
    private String possibleValues;
    @SerializedName("isRequired")
    @Expose
    private Boolean isRequired;
    @SerializedName("isDelete")
    @Expose
    private Boolean isDelete;
    @SerializedName("editable")
    @Expose
    private Boolean editable;
    @SerializedName("userId")
    @Expose
    private Long userId;
    @SerializedName("name")
    @Expose
    private String name;
    @SerializedName("maxLength")
    @Expose
    private Integer maxLength;

    public Boolean getIsRemember() {
        return isRemember;
    }

    public void setIsRemember(Boolean isRemember) {
        this.isRemember = isRemember;
    }

    public String getNameI18N() {
        return nameI18N;
    }

    public void setNameI18N(String nameI18N) {
        this.nameI18N = nameI18N;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Integer getMinLength() {
        return minLength;
    }

    public void setMinLength(Integer minLength) {
        this.minLength = minLength;
    }

    public Boolean getIsForbiddenEditRequired() {
        return isForbiddenEditRequired;
    }

    public void setIsForbiddenEditRequired(Boolean isForbiddenEditRequired) {
        this.isForbiddenEditRequired = isForbiddenEditRequired;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Boolean getDynamic() {
        return dynamic;
    }

    public void setDynamic(Boolean dynamic) {
        this.dynamic = dynamic;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getFieldFormat() {
        return fieldFormat;
    }

    public void setFieldFormat(String fieldFormat) {
        this.fieldFormat = fieldFormat;
    }

    public String getPossibleValues() {
        return possibleValues;
    }

    public void setPossibleValues(String possibleValues) {
        this.possibleValues = possibleValues;
    }

    public Boolean getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Boolean isRequired) {
        this.isRequired = isRequired;
    }

    public Boolean getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    public Boolean getEditable() {
        return editable;
    }

    public void setEditable(Boolean editable) {
        this.editable = editable;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(Integer maxLength) {
        this.maxLength = maxLength;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(Customfield.class.getName()).append('@').append(Integer.toHexString(System.identityHashCode(this))).append('[');
        sb.append("isRemember");
        sb.append('=');
        sb.append(((this.isRemember == null)?"<null>":this.isRemember));
        sb.append(',');
        sb.append("nameI18N");
        sb.append('=');
        sb.append(((this.nameI18N == null)?"<null>":this.nameI18N));
        sb.append(',');
        sb.append("defaultValue");
        sb.append('=');
        sb.append(((this.defaultValue == null)?"<null>":this.defaultValue));
        sb.append(',');
        sb.append("minLength");
        sb.append('=');
        sb.append(((this.minLength == null)?"<null>":this.minLength));
        sb.append(',');
        sb.append("isForbiddenEditRequired");
        sb.append('=');
        sb.append(((this.isForbiddenEditRequired == null)?"<null>":this.isForbiddenEditRequired));
        sb.append(',');
        sb.append("description");
        sb.append('=');
        sb.append(((this.description == null)?"<null>":this.description));
        sb.append(',');
        sb.append("type");
        sb.append('=');
        sb.append(((this.type == null)?"<null>":this.type));
        sb.append(',');
        sb.append("createdAt");
        sb.append('=');
        sb.append(((this.createdAt == null)?"<null>":this.createdAt));
        sb.append(',');
        sb.append("dynamic");
        sb.append('=');
        sb.append(((this.dynamic == null)?"<null>":this.dynamic));
        sb.append(',');
        sb.append("id");
        sb.append('=');
        sb.append(((this.id == null)?"<null>":this.id));
        sb.append(',');
        sb.append("updatedAt");
        sb.append('=');
        sb.append(((this.updatedAt == null)?"<null>":this.updatedAt));
        sb.append(',');
        sb.append("fieldFormat");
        sb.append('=');
        sb.append(((this.fieldFormat == null)?"<null>":this.fieldFormat));
        sb.append(',');
        sb.append("possibleValues");
        sb.append('=');
        sb.append(((this.possibleValues == null)?"<null>":this.possibleValues));
        sb.append(',');
        sb.append("isRequired");
        sb.append('=');
        sb.append(((this.isRequired == null)?"<null>":this.isRequired));
        sb.append(',');
        sb.append("isDelete");
        sb.append('=');
        sb.append(((this.isDelete == null)?"<null>":this.isDelete));
        sb.append(',');
        sb.append("editable");
        sb.append('=');
        sb.append(((this.editable == null)?"<null>":this.editable));
        sb.append(',');
        sb.append("userId");
        sb.append('=');
        sb.append(((this.userId == null)?"<null>":this.userId));
        sb.append(',');
        sb.append("name");
        sb.append('=');
        sb.append(((this.name == null)?"<null>":this.name));
        sb.append(',');
        sb.append("maxLength");
        sb.append('=');
        sb.append(((this.maxLength == null)?"<null>":this.maxLength));
        sb.append(',');
        if (sb.charAt((sb.length()- 1)) == ',') {
            sb.setCharAt((sb.length()- 1), ']');
        } else {
            sb.append(']');
        }
        return sb.toString();
    }

    @Override
    public int hashCode() {
        int result = 1;
        result = ((result* 31)+((this.fieldFormat == null)? 0 :this.fieldFormat.hashCode()));
        result = ((result* 31)+((this.possibleValues == null)? 0 :this.possibleValues.hashCode()));
        result = ((result* 31)+((this.isRequired == null)? 0 :this.isRequired.hashCode()));
        result = ((result* 31)+((this.isRemember == null)? 0 :this.isRemember.hashCode()));
        result = ((result* 31)+((this.nameI18N == null)? 0 :this.nameI18N.hashCode()));
        result = ((result* 31)+((this.defaultValue == null)? 0 :this.defaultValue.hashCode()));
        result = ((result* 31)+((this.isDelete == null)? 0 :this.isDelete.hashCode()));
        result = ((result* 31)+((this.editable == null)? 0 :this.editable.hashCode()));
        result = ((result* 31)+((this.minLength == null)? 0 :this.minLength.hashCode()));
        result = ((result* 31)+((this.isForbiddenEditRequired == null)? 0 :this.isForbiddenEditRequired.hashCode()));
        result = ((result* 31)+((this.description == null)? 0 :this.description.hashCode()));
        result = ((result* 31)+((this.type == null)? 0 :this.type.hashCode()));
        result = ((result* 31)+((this.userId == null)? 0 :this.userId.hashCode()));
        result = ((result* 31)+((this.createdAt == null)? 0 :this.createdAt.hashCode()));
        result = ((result* 31)+((this.name == null)? 0 :this.name.hashCode()));
        result = ((result* 31)+((this.dynamic == null)? 0 :this.dynamic.hashCode()));
        result = ((result* 31)+((this.id == null)? 0 :this.id.hashCode()));
        result = ((result* 31)+((this.maxLength == null)? 0 :this.maxLength.hashCode()));
        result = ((result* 31)+((this.updatedAt == null)? 0 :this.updatedAt.hashCode()));
        return result;
    }
}
