package com.aliyun.xdragon.common.util;

import com.aliyun.xdragon.common.enumeration.BaseNameEnum;

/**
 * <AUTHOR>
 * @date 2023/05/15
 */
public class NameEnumUtil {
    public static <E extends Enum<?> & BaseNameEnum> E nameOf(Class<E> enumClass, String name) {
        E[] enumConstants = enumClass.getEnumConstants();
        for (E e : enumConstants) {
            if (e.getName().equals(name)) {
                return e;
            }
        }
        return null;
    }
}
