package com.aliyun.xdragon.common.generate.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

public class ClientResource implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column client_resource.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column client_resource.emp_id
     *
     * @mbg.generated
     */
    private String empId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column client_resource.resource_type
     *
     * @mbg.generated
     */
    private String resourceType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column client_resource.create_timestamp
     *
     * @mbg.generated
     */
    private Long createTimestamp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column client_resource.modify_timestamp
     *
     * @mbg.generated
     */
    private Long modifyTimestamp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column client_resource.resources
     *
     * @mbg.generated
     */
    private String resources;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column client_resource.bpms_id
     *
     * @mbg.generated
     */
    private String bpmsId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column client_resource.comment
     *
     * @mbg.generated
     */
    private String comment;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column client_resource.status
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table client_resource
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column client_resource.id
     *
     * @return the value of client_resource.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column client_resource.id
     *
     * @param id the value for client_resource.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column client_resource.emp_id
     *
     * @return the value of client_resource.emp_id
     *
     * @mbg.generated
     */
    public String getEmpId() {
        return empId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column client_resource.emp_id
     *
     * @param empId the value for client_resource.emp_id
     *
     * @mbg.generated
     */
    public void setEmpId(String empId) {
        this.empId = empId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column client_resource.resource_type
     *
     * @return the value of client_resource.resource_type
     *
     * @mbg.generated
     */
    public String getResourceType() {
        return resourceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column client_resource.resource_type
     *
     * @param resourceType the value for client_resource.resource_type
     *
     * @mbg.generated
     */
    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column client_resource.create_timestamp
     *
     * @return the value of client_resource.create_timestamp
     *
     * @mbg.generated
     */
    public Long getCreateTimestamp() {
        return createTimestamp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column client_resource.create_timestamp
     *
     * @param createTimestamp the value for client_resource.create_timestamp
     *
     * @mbg.generated
     */
    public void setCreateTimestamp(Long createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column client_resource.modify_timestamp
     *
     * @return the value of client_resource.modify_timestamp
     *
     * @mbg.generated
     */
    public Long getModifyTimestamp() {
        return modifyTimestamp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column client_resource.modify_timestamp
     *
     * @param modifyTimestamp the value for client_resource.modify_timestamp
     *
     * @mbg.generated
     */
    public void setModifyTimestamp(Long modifyTimestamp) {
        this.modifyTimestamp = modifyTimestamp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column client_resource.resources
     *
     * @return the value of client_resource.resources
     *
     * @mbg.generated
     */
    public String getResources() {
        return resources;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column client_resource.resources
     *
     * @param resources the value for client_resource.resources
     *
     * @mbg.generated
     */
    public void setResources(String resources) {
        this.resources = resources;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column client_resource.bpms_id
     *
     * @return the value of client_resource.bpms_id
     *
     * @mbg.generated
     */
    public String getBpmsId() {
        return bpmsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column client_resource.bpms_id
     *
     * @param bpmsId the value for client_resource.bpms_id
     *
     * @mbg.generated
     */
    public void setBpmsId(String bpmsId) {
        this.bpmsId = bpmsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column client_resource.comment
     *
     * @return the value of client_resource.comment
     *
     * @mbg.generated
     */
    public String getComment() {
        return comment;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column client_resource.comment
     *
     * @param comment the value for client_resource.comment
     *
     * @mbg.generated
     */
    public void setComment(String comment) {
        this.comment = comment;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column client_resource.status
     *
     * @return the value of client_resource.status
     *
     * @mbg.generated
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column client_resource.status
     *
     * @param status the value for client_resource.status
     *
     * @mbg.generated
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table client_resource
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ClientResource other = (ClientResource) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getEmpId() == null ? other.getEmpId() == null : this.getEmpId().equals(other.getEmpId()))
            && (this.getResourceType() == null ? other.getResourceType() == null : this.getResourceType().equals(other.getResourceType()))
            && (this.getCreateTimestamp() == null ? other.getCreateTimestamp() == null : this.getCreateTimestamp().equals(other.getCreateTimestamp()))
            && (this.getModifyTimestamp() == null ? other.getModifyTimestamp() == null : this.getModifyTimestamp().equals(other.getModifyTimestamp()))
            && (this.getResources() == null ? other.getResources() == null : this.getResources().equals(other.getResources()))
            && (this.getBpmsId() == null ? other.getBpmsId() == null : this.getBpmsId().equals(other.getBpmsId()))
            && (this.getComment() == null ? other.getComment() == null : this.getComment().equals(other.getComment()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table client_resource
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getEmpId() == null) ? 0 : getEmpId().hashCode());
        result = prime * result + ((getResourceType() == null) ? 0 : getResourceType().hashCode());
        result = prime * result + ((getCreateTimestamp() == null) ? 0 : getCreateTimestamp().hashCode());
        result = prime * result + ((getModifyTimestamp() == null) ? 0 : getModifyTimestamp().hashCode());
        result = prime * result + ((getResources() == null) ? 0 : getResources().hashCode());
        result = prime * result + ((getBpmsId() == null) ? 0 : getBpmsId().hashCode());
        result = prime * result + ((getComment() == null) ? 0 : getComment().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table client_resource
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", empId=").append(empId);
        sb.append(", resourceType=").append(resourceType);
        sb.append(", createTimestamp=").append(createTimestamp);
        sb.append(", modifyTimestamp=").append(modifyTimestamp);
        sb.append(", resources=").append(resources);
        sb.append(", bpmsId=").append(bpmsId);
        sb.append(", comment=").append(comment);
        sb.append(", status=").append(status);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table client_resource
     *
     * @mbg.generated
     */
    public enum Column {
        id("id", "id", "INTEGER", false),
        empId("emp_id", "empId", "VARCHAR", false),
        resourceType("resource_type", "resourceType", "VARCHAR", false),
        createTimestamp("create_timestamp", "createTimestamp", "BIGINT", false),
        modifyTimestamp("modify_timestamp", "modifyTimestamp", "BIGINT", false),
        resources("resources", "resources", "VARCHAR", false),
        bpmsId("bpms_id", "bpmsId", "VARCHAR", false),
        comment("comment", "comment", "VARCHAR", true),
        status("status", "status", "INTEGER", true);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table client_resource
         *
         * @mbg.generated
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}