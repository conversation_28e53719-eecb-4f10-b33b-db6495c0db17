package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/24
 */
@Data
public class StatRegionByPatternWithTopNRequest extends BaseTaskRequest implements Serializable {
    private static final long serialVersionUID = -4396104040100640138L;
    /**
     * stat start time, in ms
     */
    @NotNull
    @Positive
    Long startMs;
    /**
     * stat end time, in ms
     */
    @NotNull
    @Positive
    Long endMs;

    /**
     * pattern md5
     */
    @NotBlank
    String patternMd5;
    /**
     * stat region, null or empty for all
     */
    List<String> regions;

    @NotNull
    @Positive
    @Min(1)
    @Max(30)
    Integer n;
}
