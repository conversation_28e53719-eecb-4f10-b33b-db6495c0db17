package com.aliyun.xdragon.common.model.log.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

@Data
public class MatchLogRegexRequest implements Serializable {
    private static final long serialVersionUID = 1095255178371151926L;

    /**
     * task id
     */
    @NotNull(message = "task is null")
    @Positive
    private Long taskId;
    /**
     * 输入的原始日志信息
     */
    private String raw;
    /**
     * 正则预处理表达式
     */
    private String customer;
    /**
     * 聚类切分行数
     */
    private Integer maxLineCnt;
    /**
     * 聚类分词个数
     */
    private Integer maxWordCnt;
}
