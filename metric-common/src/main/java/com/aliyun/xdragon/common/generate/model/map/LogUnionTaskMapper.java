package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.LogUnionTask;
import com.aliyun.xdragon.common.generate.model.LogUnionTaskExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface LogUnionTaskMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    long countByExample(LogUnionTaskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    int deleteByExample(LogUnionTaskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long unionId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    int insert(LogUnionTask record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    int insertSelective(LogUnionTask record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    List<LogUnionTask> selectByExampleWithRowbounds(LogUnionTaskExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    List<LogUnionTask> selectByExampleSelective(@Param("example") LogUnionTaskExample example, @Param("selective") LogUnionTask.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    LogUnionTask selectOneByExample(LogUnionTaskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    LogUnionTask selectOneByExampleSelective(@Param("example") LogUnionTaskExample example, @Param("selective") LogUnionTask.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    List<LogUnionTask> selectByExample(LogUnionTaskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    LogUnionTask selectByPrimaryKeySelective(@Param("unionId") Long unionId, @Param("selective") LogUnionTask.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    LogUnionTask selectByPrimaryKey(Long unionId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") LogUnionTask record, @Param("example") LogUnionTaskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") LogUnionTask record, @Param("example") LogUnionTaskExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(LogUnionTask record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(LogUnionTask record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<LogUnionTask> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<LogUnionTask> list, @Param("selective") LogUnionTask.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    int upsert(LogUnionTask record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_union_task
     *
     * @mbg.generated
     */
    int upsertSelective(LogUnionTask record);
}