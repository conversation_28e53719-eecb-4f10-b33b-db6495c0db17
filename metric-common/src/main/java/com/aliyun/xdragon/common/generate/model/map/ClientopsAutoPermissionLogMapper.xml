<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.model.map.ClientopsAutoPermissionLogMapper">
  <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="empid" jdbcType="VARCHAR" property="empid" />
    <result column="permission" jdbcType="VARCHAR" property="permission" />
    <result column="grant_time" jdbcType="INTEGER" property="grantTime" />
    <result column="expire_time" jdbcType="INTEGER" property="expireTime" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, `source`, empid, permission, grant_time, expire_time, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from clientops_auto_permission_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="example != null and example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from clientops_auto_permission_log
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from clientops_auto_permission_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from clientops_auto_permission_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from clientops_auto_permission_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLogExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from clientops_auto_permission_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into clientops_auto_permission_log (`source`, empid, permission, 
      grant_time, expire_time, create_time, 
      update_time)
    values (#{source,jdbcType=INTEGER}, #{empid,jdbcType=VARCHAR}, #{permission,jdbcType=VARCHAR}, 
      #{grantTime,jdbcType=INTEGER}, #{expireTime,jdbcType=INTEGER}, #{createTime,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into clientops_auto_permission_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="source != null">
        `source`,
      </if>
      <if test="empid != null">
        empid,
      </if>
      <if test="permission != null">
        permission,
      </if>
      <if test="grantTime != null">
        grant_time,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="empid != null">
        #{empid,jdbcType=VARCHAR},
      </if>
      <if test="permission != null">
        #{permission,jdbcType=VARCHAR},
      </if>
      <if test="grantTime != null">
        #{grantTime,jdbcType=INTEGER},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLogExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from clientops_auto_permission_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update clientops_auto_permission_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.source != null">
        `source` = #{record.source,jdbcType=INTEGER},
      </if>
      <if test="record.empid != null">
        empid = #{record.empid,jdbcType=VARCHAR},
      </if>
      <if test="record.permission != null">
        permission = #{record.permission,jdbcType=VARCHAR},
      </if>
      <if test="record.grantTime != null">
        grant_time = #{record.grantTime,jdbcType=INTEGER},
      </if>
      <if test="record.expireTime != null">
        expire_time = #{record.expireTime,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=INTEGER},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update clientops_auto_permission_log
    set id = #{record.id,jdbcType=INTEGER},
      `source` = #{record.source,jdbcType=INTEGER},
      empid = #{record.empid,jdbcType=VARCHAR},
      permission = #{record.permission,jdbcType=VARCHAR},
      grant_time = #{record.grantTime,jdbcType=INTEGER},
      expire_time = #{record.expireTime,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=INTEGER},
      update_time = #{record.updateTime,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update clientops_auto_permission_log
    <set>
      <if test="source != null">
        `source` = #{source,jdbcType=INTEGER},
      </if>
      <if test="empid != null">
        empid = #{empid,jdbcType=VARCHAR},
      </if>
      <if test="permission != null">
        permission = #{permission,jdbcType=VARCHAR},
      </if>
      <if test="grantTime != null">
        grant_time = #{grantTime,jdbcType=INTEGER},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update clientops_auto_permission_log
    set `source` = #{source,jdbcType=INTEGER},
      empid = #{empid,jdbcType=VARCHAR},
      permission = #{permission,jdbcType=VARCHAR},
      grant_time = #{grantTime,jdbcType=INTEGER},
      expire_time = #{expireTime,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from clientops_auto_permission_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into clientops_auto_permission_log
    (`source`, empid, permission, grant_time, expire_time, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.source,jdbcType=INTEGER}, #{item.empid,jdbcType=VARCHAR}, #{item.permission,jdbcType=VARCHAR}, 
        #{item.grantTime,jdbcType=INTEGER}, #{item.expireTime,jdbcType=INTEGER}, #{item.createTime,jdbcType=INTEGER}, 
        #{item.updateTime,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into clientops_auto_permission_log (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=INTEGER}
        </if>
        <if test="'empid'.toString() == column.value">
          #{item.empid,jdbcType=VARCHAR}
        </if>
        <if test="'permission'.toString() == column.value">
          #{item.permission,jdbcType=VARCHAR}
        </if>
        <if test="'grant_time'.toString() == column.value">
          #{item.grantTime,jdbcType=INTEGER}
        </if>
        <if test="'expire_time'.toString() == column.value">
          #{item.expireTime,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=INTEGER}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
  <insert id="upsertSelective" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLog" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into clientops_auto_permission_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="empid != null">
        empid,
      </if>
      <if test="permission != null">
        permission,
      </if>
      <if test="grantTime != null">
        grant_time,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="empid != null">
        #{empid,jdbcType=VARCHAR},
      </if>
      <if test="permission != null">
        #{permission,jdbcType=VARCHAR},
      </if>
      <if test="grantTime != null">
        #{grantTime,jdbcType=INTEGER},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=INTEGER},
      </if>
      <if test="empid != null">
        empid = #{empid,jdbcType=VARCHAR},
      </if>
      <if test="permission != null">
        permission = #{permission,jdbcType=VARCHAR},
      </if>
      <if test="grantTime != null">
        grant_time = #{grantTime,jdbcType=INTEGER},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <insert id="upsert" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLog" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into clientops_auto_permission_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      `source`,
      empid,
      permission,
      grant_time,
      expire_time,
      create_time,
      update_time,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      #{source,jdbcType=INTEGER},
      #{empid,jdbcType=VARCHAR},
      #{permission,jdbcType=VARCHAR},
      #{grantTime,jdbcType=INTEGER},
      #{expireTime,jdbcType=INTEGER},
      #{createTime,jdbcType=INTEGER},
      #{updateTime,jdbcType=INTEGER},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=INTEGER},
      </if>
      `source` = #{source,jdbcType=INTEGER},
      empid = #{empid,jdbcType=VARCHAR},
      permission = #{permission,jdbcType=VARCHAR},
      grant_time = #{grantTime,jdbcType=INTEGER},
      expire_time = #{expireTime,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=INTEGER},
    </trim>
  </insert>
  <select id="selectOneByExample" parameterType="com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from clientops_auto_permission_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from clientops_auto_permission_log
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
</mapper>