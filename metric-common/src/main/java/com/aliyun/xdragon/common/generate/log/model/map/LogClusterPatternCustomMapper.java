package com.aliyun.xdragon.common.generate.log.model.map;

import com.aliyun.xdragon.common.generate.log.model.LogClusterPattern;
import com.aliyun.xdragon.common.model.log.LogPatternRank;
import com.aliyun.xdragon.common.model.log.LogPatternSummary;
import com.aliyun.xdragon.common.model.log.LogTimeRange;
import com.aliyun.xdragon.common.model.log.response.RecallPatternResponse;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface LogClusterPatternCustomMapper {
    @Select("<script>"
            + "   select sum(support) from log_cluster_pattern where time between #{start} and #{end} "
            + "   <if test=\"taskId != null\">"
            + "      and task_id = #{taskId} "
            + "   </if>"
            + "   <if test=\"md5s != null and md5s.size > 0\">"
            + "    <if test=\"flag\"> "
            + "      and `md5` in "
            + "    </if> "
            + "    <if test=\"!flag\"> "
            + "      and `md5` not in "
            + "    </if> "
            + "      <foreach collection=\"md5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
            + "         #{m} "
            + "      </foreach>"
            + "   </if>"
            + " </script>"
    )
    @ResultType(Long.class)
    Long getSum(@Param("taskId") Long taskId, @Param("start") Date start, @Param("end") Date end,
                @Param("md5s") Collection<String> md5List, @Param("flag") boolean includeFlag);

    @Select("<script>"
            + "   select sum(support) from log_cluster_pattern where time between #{start} and #{end} "
            + "   <if test=\"taskId != null\">"
            + "      and task_id = #{taskId} "
            + "   </if>"
            + "   <if test=\"regions != null and regions.size > 0\">"
            + "      and `region` in "
            + "      <foreach collection=\"regions\" item=\"r\" open=\"(\" close=\")\" separator=\",\">"
            + "         #{r} "
            + "      </foreach>"
            + "   </if>"
            + "   <if test=\"md5s != null and md5s.size > 0\">"
            + "    <if test=\"flag\"> "
            + "      and `md5` in "
            + "    </if> "
            + "    <if test=\"!flag\"> "
            + "      and `md5` not in "
            + "    </if> "
            + "      <foreach collection=\"md5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
            + "         #{m} "
            + "      </foreach>"
            + "   </if>"
            + " </script>"
    )
    @ResultType(Long.class)
    Long getSumWithRegions(@Param("taskId") Long taskId, @Param("start") Date start, @Param("end") Date end,
                           @Param("regions") Collection<String> regions, @Param("md5s") Collection<String> md5s,
                           @Param("flag") boolean includeFlag);

    @Select("<script>"
            + "   select count(distinct(md5)) from log_cluster_pattern where time between #{start} and #{end} "
            + "   <if test=\"taskId != null\">"
            + "      and task_id = #{taskId} "
            + "   </if>"
            + "   <if test=\"md5s != null and md5s.size > 0\">"
            + "    <if test=\"flag\"> "
            + "      and `md5` in "
            + "    </if> "
            + "    <if test=\"!flag\"> "
            + "      and `md5` not in "
            + "    </if> "
            + "      <foreach collection=\"md5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
            + "         #{m} "
            + "      </foreach>"
            + "   </if>"
            + " </script>"
    )
    @ResultType(Long.class)
    Long getTypeCnt(@Param("taskId") Long taskId, @Param("start") Date start, @Param("end") Date end,
                    @Param("md5s") Collection<String> md5s, @Param("flag") boolean includeFlag);

    @Select("<script>"
            + "  select count(distinct(md5)) from log_cluster_pattern where md5 not in ( "
            + "  select distinct md5 from log_cluster_pattern where time between #{beforeStart} and #{beforeEnd} "
            + "  <if test=\"taskId != null\">"
            + "    and task_id = #{taskId} "
            + "  </if>"
            + "  <if test=\"md5s != null and md5s.size > 0\">"
            + "   <if test=\"flag\"> "
            + "     and `md5` in "
            + "   </if> "
            + "   <if test=\"!flag\"> "
            + "     and `md5` not in "
            + "   </if> "
            + "     <foreach collection=\"md5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
            + "        #{m} "
            + "     </foreach>"
            + "  </if>"
            + "  ) and time between #{start} and #{end}"
            + "  <if test=\"taskId != null\">"
            + "    and task_id = #{taskId} "
            + "  </if>"
            + "  <if test=\"md5s != null and md5s.size > 0\">"
            + "   <if test=\"flag\"> "
            + "     and `md5` in "
            + "   </if> "
            + "   <if test=\"!flag\"> "
            + "     and `md5` not in "
            + "   </if> "
            + "     <foreach collection=\"md5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
            + "        #{m} "
            + "     </foreach>"
            + "  </if>"
            + " </script>"
    )
    @ResultType(Long.class)
    Long getNewTypeCnt(@Param("taskId") Long taskId, @Param("beforeStart") Date beforeStart,
                       @Param("beforeEnd") Date beforeEnd, @Param("start") Date start, @Param("end") Date end,
                       @Param("md5s") Collection<String> md5s, @Param("flag") boolean includeFlag);

    @Select("<script>"
            + " select max(`time`) from log_cluster_pattern where task_id = #{taskId} and region = #{region}"
            + " </script>"
    )
    @ResultType(String.class)
    String getMaxTime(@Param("taskId") Long taskId, @Param("region") String region);

    @Select("<script>"
            + "  select task_id, pattern, hashcode, `md5`, sum(support) as support from log_cluster_pattern where  "
            + "  task_id=#{taskId} and time between #{startDate} and #{endDate} "
            + "  <if test=\"regions != null and regions.size>0\">"
            + "   and region in "
            + "    <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\">"
            + "      #{region} "
            + "    </foreach>"
            + "  </if>"
            + "  <if test=\"md5s != null and md5s.size>0\">"
            + "   and `md5` in "
            + "    <foreach collection=\"md5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
            + "      #{m} "
            + "    </foreach>"
            + "  </if>"
            + "   group by `md5` order by support desc, md5 desc "
            + " </script>"
    )
    @ResultType(LogClusterPattern.class)
    @ResultMap("com.aliyun.xdragon.common.generate.log.model.map.LogClusterPatternMapper.BaseResultMap")
    List<LogClusterPattern> statPatternIncludeMd5s(@Param("taskId") Long taskId, @Param("startDate") Date startDate,
                                                   @Param("endDate") Date endDate, @Param("regions") List<String> regions, @Param("md5s") List<String> md5s);

    @Select("<script> "
            + "  select task_id, pattern, hashcode, `md5`, sum(support) as support from log_cluster_pattern where  "
            + "  task_id=#{taskId} and time between #{startDate} and #{endDate} "
            + "  <if test=\"regions != null and regions.size>0\"> "
            + "   and region in "
            + "    <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\"> "
            + "      #{region} "
            + "    </foreach> "
            + "  </if> "
            + "  <if test=\"md5s != null and md5s.size>0\"> "
            + "   and `md5` not in "
            + "    <foreach collection=\"md5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\"> "
            + "      #{m} "
            + "    </foreach> "
            + "  </if> "
            + "   group by `md5` order by support desc, md5 desc "
            + " </script> "
    )
    @ResultType(LogClusterPattern.class)
    @ResultMap("com.aliyun.xdragon.common.generate.log.model.map.LogClusterPatternMapper.BaseResultMap")
    List<LogClusterPattern> statPatternExcludeMd5s(@Param("taskId") Long taskId, @Param("startDate") Date startDate,
                                                   @Param("endDate") Date endDate, @Param("regions") List<String> regions, @Param("md5s") List<String> md5s);

    @Select("<script> "
            + "  select task_id, region, pattern, hashcode, `md5`, sum(support) as support from log_cluster_pattern where  "
            + "  task_id=#{taskId} and time between #{startDate} and #{endDate} "
            + "  <if test=\"patternMd5s != null and patternMd5s.size>0\"> "
            + "   and `md5` in "
            + "    <foreach collection=\"patternMd5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\"> "
            + "      #{m} "
            + "    </foreach> "
            + "  </if> "
            + "   group by region, `md5` order by support desc, `md5` desc "
            + " </script> "
    )
    @ResultType(LogClusterPattern.class)
    @ResultMap("com.aliyun.xdragon.common.generate.log.model.map.LogClusterPatternMapper.BaseResultMap")
    List<LogClusterPattern> statRegionByPattern(@Param("taskId") Long taskId, @Param("startDate") Date startDate,
                                                @Param("endDate") Date endDate, @Param("patternMd5s") List<String> patternMd5s);

    @Select("<script>"
            + "select distinct region from log_cluster_pattern where task_id=#{taskId}"
            + "</script>"
    )
    @ResultType(String.class)
    List<String> getRegionById(@Param("taskId") Long taskId);

    @Select("<script>"
            + "select distinct region from log_cluster_pattern where task_id=#{taskId} and time between #{start} and #{end}"
            + "</script>"
    )
    @ResultType(String.class)
    List<String> getNcRegionById(@Param("taskId") Long taskId, @Param("start") Date start, @Param("end") Date end);

    @Select(
            "<script> "
                    + "  select task_id, pattern, `md5`, hashcode, sum(support) as support from log_cluster_pattern where  "
                    + "  task_id=#{taskId} and time between #{startDate} and #{endDate} "
                    + "  <if test=\"regions != null and regions.size>0\"> "
                    + "    and region in "
                    + "    <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\"> "
                    + "      #{region} "
                    + "    </foreach> "
                    + "  </if> "
                    + "  <if test=\"patternMd5s != null and patternMd5s.size>0\"> "
                    + "    and `md5` in "
                    + "    <foreach collection=\"patternMd5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\"> "
                    + "      #{m} "
                    + "    </foreach> "
                    + "  </if> "
                    + "  group by task_id, `md5`, hashcode, pattern order by  "
                    + "  <choose> "
                    + "    <when test=\"orderCol != null and orderCol != ''\"> "
                    + "      ${orderCol}  "
                    + "    </when> "
                    + "    <otherwise> "
                    + "      support  "
                    + "    </otherwise> "
                    + "  </choose> "
                    + "  <choose> "
                    + "    <when test=\"orderBy != null and orderBy != ''\"> "
                    + "      ${orderBy}  "
                    + "    </when> "
                    + "    <otherwise> "
                    + "      desc  "
                    + "    </otherwise> "
                    + "  </choose> "
                    + "  , `md5` desc limit #{start}, #{pageSize} "
                    + "</script>"
    )
    @ResultType(LogClusterPattern.class)
    @ResultMap("com.aliyun.xdragon.common.generate.log.model.map.LogClusterPatternMapper.BaseResultMap")
    List<LogClusterPattern> listSumPatterns(@Param("taskId") Long taskId, @Param("startDate") Date startDate,
                                            @Param("endDate") Date endDate, @Param("regions") List<String> regions,
                                            @Param("patternMd5s") Collection<String> patternMd5s, @Param("orderCol") String orderCol,
                                            @Param("orderBy") String orderBy, @Param("start") Integer start, @Param("pageSize") Integer pageSize);

    @Select(
            "<script> "
                    + "  select task_id, pattern, `md5`, sum(support) as support from log_cluster_pattern where "
                    + "  time between #{startDate} and #{endDate} "
                    + "  <if test=\"idMd5s != null and idMd5s.size>0\"> "
                    + "    and concat(`task_id`, '|', `md5`) in "
                    + "    <foreach collection=\"idMd5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\"> "
                    + "      #{m} "
                    + "    </foreach> "
                    + "  </if> "
                    + "  group by task_id, `md5`, pattern order by  "
                    + "  <choose> "
                    + "    <when test=\"orderCol != null and orderCol != ''\"> "
                    + "      ${orderCol}  "
                    + "    </when> "
                    + "    <otherwise> "
                    + "      support  "
                    + "    </otherwise> "
                    + "  </choose> "
                    + "  <choose> "
                    + "    <when test=\"orderBy != null and orderBy != ''\"> "
                    + "      ${orderBy}  "
                    + "    </when> "
                    + "    <otherwise> "
                    + "      desc  "
                    + "    </otherwise> "
                    + "  </choose> "
                    + "  , `md5` desc limit #{start}, #{pageSize} "
                    + "</script>"
    )
    @ResultType(LogClusterPattern.class)
    @ResultMap("com.aliyun.xdragon.common.generate.log.model.map.LogClusterPatternMapper.BaseResultMap")
    List<LogClusterPattern> listSumPatternsForMultiTask(@Param("startDate") Date startDate,
                                                        @Param("endDate") Date endDate, @Param("idMd5s") Collection<String> idMd5s, @Param("orderCol") String orderCol,
                                                        @Param("orderBy") String orderBy, @Param("start") Integer start, @Param("pageSize") Integer pageSize);

    @Select("<script> "
            + "    select `md5`, pattern from log_cluster_pattern where  "
            + "    task_id=#{taskId} and time between #{startDate} and #{endDate} "
            + "    <if test=\"regions != null and regions.size>0\"> "
            + "        and region in "
            + "        <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\"> "
            + "            #{region} "
            + "        </foreach> "
            + "    </if> "
            + "    <if test=\"patternMd5s != null and patternMd5s.size>0\"> "
            + "        and `md5` in "
            + "        <foreach collection=\"patternMd5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\"> "
            + "            #{m} "
            + "        </foreach> "
            + "    </if> "
            + "    group by `md5`, pattern"
            + "</script>")
    @ResultType(LogClusterPattern.class)
    @ResultMap("com.aliyun.xdragon.common.generate.log.model.map.LogClusterPatternMapper.BaseResultMap")
    List<LogClusterPattern> listPatternMd5(@Param("taskId") Long taskId, @Param("startDate") Date startDate,
                                           @Param("endDate") Date endDate, @Param("regions") List<String> regions,
                                           @Param("patternMd5s") List<String> patternMd5s);

    @Select("<script> "
            + "select distinct task_id from log_cluster_pattern where time between #{startDate} and #{endDate} "
            + "</script>")
    @ResultType(Long.class)
    List<Long> listClusteredTasks(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script> "
            + "    select distinct task_id, `md5`，pattern from log_cluster_pattern where  "
            + "    time between #{startDate} and #{endDate} "
            + "    <if test=\"idMd5s != null and idMd5s.size>0\"> "
            + "        and concat(`task_id`, '|', `md5`) in "
            + "        <foreach collection=\"idMd5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\"> "
            + "            #{m} "
            + "        </foreach> "
            + "    </if>"
            + "</script>")
    @ResultType(LogClusterPattern.class)
    @ResultMap("com.aliyun.xdragon.common.generate.log.model.map.LogClusterPatternMapper.BaseResultMap")
    List<LogClusterPattern> listPatternMd5ForMultiTask(@Param("startDate") Date startDate,
                                                       @Param("endDate") Date endDate, @Param("idMd5s") List<String> idMd5s);

    @Select(
            "<script>"
                    + "select task_id, pattern, `md5`, sum(support) as support from log_cluster_pattern where  "
                    + "task_id=#{taskId} and time between #{startDate} and #{endDate} "
                    + "<if test=\"regions != null and regions.size>0\">"
                    + "    and region in "
                    + "    <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\">"
                    + "        #{region} "
                    + "    </foreach>"
                    + "</if>"
                    + "<if test=\"md5s != null and md5s.size>0\">"
                    + "    and `md5` not in  "
                    + "    <foreach collection=\"md5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
                    + "        #{m} "
                    + "    </foreach>"
                    + "</if>"
                    + "<if test=\"patternFilter != null and patternFilter.length>0\">"
                    + "  and pattern "
                    + "  <if test=\"!filterFlag\">"
                    + "    not "
                    + "  </if>"
                    + "  like #{patternFilter} "
                    + "</if>"
                    + "group by `md5` order by "
                    + "<choose>"
                    + "    <when test=\"orderCol != null and orderCol != ''\">"
                    + "        ${orderCol}  "
                    + "    </when>"
                    + "    <otherwise>"
                    + "        support  "
                    + "    </otherwise>"
                    + "</choose>"
                    + "<choose>"
                    + "    <when test=\"orderBy != null and orderBy != ''\">"
                    + "        ${orderBy} "
                    + "    </when>"
                    + "    <otherwise>"
                    + "        desc   "
                    + "    </otherwise>"
                    + "</choose>"
                    + ", `md5` desc limit #{start}, #{pageSize}"
                    + "</script>"
    )
    @ResultType(LogClusterPattern.class)
    @ResultMap("com.aliyun.xdragon.common.generate.log.model.map.LogClusterPatternMapper.BaseResultMap")
    List<LogClusterPattern> listSumPatternsExcludeMd5s(@Param("taskId") Long taskId, @Param("startDate") Date startDate,
                                                       @Param("endDate") Date endDate, @Param("regions") List<String> regions, @Param("md5s") Collection<String> md5s,
                                                       @Param("patternFilter") String patternFilter, @Param("filterFlag") boolean filterFlag,
                                                       @Param("orderCol") String orderCol, @Param("orderBy") String orderBy, @Param("start") Integer start,
                                                       @Param("pageSize") Integer pageSize);

    @Select(
            "<script>"
                    + "select task_id, pattern, `md5`, sum(support) as support from log_cluster_pattern where "
                    + "time between #{startDate} and #{endDate} "
                    + "<if test=\"taskIds != null and taskIds.size>0\">"
                    + "    and task_id in "
                    + "    <foreach collection=\"taskIds\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
                    + "        #{m} "
                    + "    </foreach>"
                    + "</if>"
                    + "<if test=\"idMd5s != null and idMd5s.size>0\">"
                    + "    and concat(task_id, '|', `md5`) not in  "
                    + "    <foreach collection=\"idMd5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
                    + "        #{m} "
                    + "    </foreach>"
                    + "</if>"
                    + "<if test=\"patternFilter != null and patternFilter.length>0\">"
                    + "  and pattern "
                    + "  <if test=\"!filterFlag\">"
                    + "    not "
                    + "  </if>"
                    + "  like #{patternFilter} "
                    + "</if>"
                    + "group by task_id, `md5`, pattern order by "
                    + "<choose>"
                    + "    <when test=\"orderCol != null and orderCol != ''\">"
                    + "        ${orderCol}  "
                    + "    </when>"
                    + "    <otherwise>"
                    + "        support  "
                    + "    </otherwise>"
                    + "</choose>"
                    + "<choose>"
                    + "    <when test=\"orderBy != null and orderBy != ''\">"
                    + "        ${orderBy} "
                    + "    </when>"
                    + "    <otherwise>"
                    + "        desc   "
                    + "    </otherwise>"
                    + "</choose>"
                    + ", `md5` desc limit #{start}, #{pageSize}"
                    + "</script>"
    )
    @ResultType(LogClusterPattern.class)
    @ResultMap("com.aliyun.xdragon.common.generate.log.model.map.LogClusterPatternMapper.BaseResultMap")
    List<LogClusterPattern> listSumPatternsExcludeMd5sForMultiTask(@Param("taskIds") List<Long> taskIds,
                                                                   @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("idMd5s") Collection<String> idMd5s,
                                                                   @Param("patternFilter") String patternFilter, @Param("filterFlag") boolean filterFlag,
                                                                   @Param("orderCol") String orderCol, @Param("orderBy") String orderBy, @Param("start") Integer start,
                                                                   @Param("pageSize") Integer pageSize);

    @Select(
            "<script> "
                    + "  select count(*) from ( "
                    + "  select count(*), `md5` from log_cluster_pattern where  "
                    + "  task_id=#{taskId} and time between #{startDate} and #{endDate} "
                    + "  <if test=\"regions != null and regions.size>0\"> "
                    + "    and region in "
                    + "    <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\"> "
                    + "    #{region} "
                    + "    </foreach> "
                    + "  </if> "
                    + "  <if test=\"patternMd5 != null and patternMd5.size>0\"> "
                    + "    and `md5` in "
                    + "    <foreach collection=\"patternMd5\" item=\"m\" open=\"(\" close=\")\" separator=\",\"> "
                    + "    #{m} "
                    + "    </foreach> "
                    + "  </if> "
                    + "  group by `md5`"
                    + "  ) t"
                    + "</script>"
    )
    @ResultType(Long.class)
    Long countSumPatterns(@Param("taskId") Long taskId, @Param("startDate") Date startDate,
                          @Param("endDate") Date endDate, @Param("regions") List<String> regions,
                          @Param("patternMd5") List<String> patternMd5);

    @Select(
            "<script> "
                    + "  select count(*) from ( "
                    + "  select count(*), `md5` from log_cluster_pattern where  "
                    + "  time between #{startDate} and #{endDate} "
                    + "  <if test=\"idMd5s != null and idMd5s.size>0\"> "
                    + "    and concat(`task_id`, '|', `md5`) in "
                    + "    <foreach collection=\"idMd5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\"> "
                    + "     #{m} "
                    + "    </foreach> "
                    + "  </if> "
                    + "  group by `md5`"
                    + "  ) t"
                    + "</script>"
    )
    @ResultType(Long.class)
    Long countSumPatternsForMultiTask(@Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                      @Param("idMd5s") List<String> idMd5s);

    @Select(
            "<script>"
                    + "select count(*) from ("
                    + "select count(*), `md5` from log_cluster_pattern where  "
                    + "task_id=#{taskId} and time between #{startDate} and #{endDate} "
                    + "<if test=\"regions != null and regions.size>0\">"
                    + "    and region in "
                    + "    <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\">"
                    + "    #{region}"
                    + "    </foreach>"
                    + "</if>"
                    + "<if test=\"md5s != null and md5s.size>0\">"
                    + "    and `md5` not in "
                    + "    <foreach collection=\"md5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
                    + "    #{m}"
                    + "    </foreach>"
                    + "</if>"
                    + "<if test=\"patternFilter != null and patternFilter.length>0\">"
                    + "  and pattern "
                    + "  <if test=\"!filterFlag\">"
                    + "    not "
                    + "  </if>"
                    + "  like #{patternFilter} "
                    + "</if>"
                    + "group by `md5`"
                    + ") t"
                    + "</script>"
    )
    @ResultType(Long.class)
    Long countSumPatternsExcludeMd5s(@Param("taskId") Long taskId, @Param("startDate") Date startDate,
                                     @Param("endDate") Date endDate, @Param("regions") List<String> regions, @Param("md5s") List<String> patternMd5s,
                                     @Param("patternFilter") String patternFilter, @Param("filterFlag") boolean filterFlag);

    @Select(
            "<script>"
                    + "select count(*) from ("
                    + "select count(*), `md5` from log_cluster_pattern where  "
                    + "time between #{startDate} and #{endDate} "
                    + "<if test=\"taskIds != null and taskIds.size>0\">"
                    + "    and `task_id` in "
                    + "    <foreach collection=\"taskIds\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
                    + "      #{m}"
                    + "    </foreach>"
                    + "</if>"
                    + "<if test=\"idMd5s != null and idMd5s.size>0\">"
                    + "    and concat(`task_id`, '|', `md5`) not in "
                    + "    <foreach collection=\"idMd5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
                    + "      #{m}"
                    + "    </foreach>"
                    + "</if>"
                    + "<if test=\"patternFilter != null and patternFilter.length>0\">"
                    + "  and pattern "
                    + "  <if test=\"!filterFlag\">"
                    + "    not "
                    + "  </if>"
                    + "  like #{patternFilter} "
                    + "</if>"
                    + "group by `md5`"
                    + ") t"
                    + "</script>"
    )
    @ResultType(Long.class)
    Long countSumPatternsExcludeMd5sForMultiTask(@Param("taskIds") List<Long> taskIds,
                                                 @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("idMd5s") List<String> idMd5s,
                                                 @Param("patternFilter") String patternFilter, @Param("filterFlag") boolean filterFlag);

    @Select(
            "<script> "
                    + "  select sum(support) from log_cluster_pattern where  "
                    + "  task_id = #{taskId} and `md5` = #{patternMd5} and time between #{startDate} and #{endDate} "
                    + "  <if test=\"regions != null and regions.size > 0\"> "
                    + "    and region in "
                    + "    <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\"> "
                    + "    #{region} "
                    + "    </foreach> "
                    + "  </if> "
                    + "</script>"
    )
    @ResultType(Long.class)
    Long sumMd5(@Param("taskId") Long taskId, @Param("patternMd5") String patternMd5,
                @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("regions") List<String> regions);

    @Select(
            "<script> "
                    + "  select region, `time`, window_size, count(*) as cnt from log_cluster_pattern where  "
                    + "  task_id = #{taskId} and time between #{startDate} and #{endDate} "
                    + "  <if test=\"regions != null and regions.size > 0\"> "
                    + "    and region in "
                    + "    <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\"> "
                    + "    #{region} "
                    + "    </foreach> "
                    + "  </if> "
                    + "  <if test=\"patternMd5s != null and patternMd5s.size > 0\"> "
                    + "    and `md5` in "
                    + "    <foreach collection=\"patternMd5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\"> "
                    + "    #{m} "
                    + "    </foreach> "
                    + "  </if> "
                    + "  group by region, time, window_size "
                    + "  order by region, time, window_size "
                    + "</script>"
    )
    @ResultType(Map.class)
    List<Map<String, Object>> getCountSeqWithDayMd5(@Param("taskId") Long taskId, @Param("startDate") Date startDate,
                                                    @Param("endDate") Date endDate, @Param("regions") List<String> regions, @Param("patternMd5s") List<String> patternMd5s);

    @Select(
            "<script> "
                    + "  select `region`, `time`, window_size, count(*) as cnt from log_cluster_pattern where  "
                    + "  task_id = #{taskId} and time between #{startDate} and #{endDate} "
                    + "  <if test=\"regions != null and regions.size > 0\"> "
                    + "    and region in "
                    + "    <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\"> "
                    + "    #{region} "
                    + "    </foreach> "
                    + "  </if> "
                    + "  <if test=\"patternMd5s != null and patternMd5s.size > 0\"> "
                    + "    <if test=\"flag\"> "
                    + "      and `md5` in "
                    + "    </if> "
                    + "    <if test=\"!flag\"> "
                    + "      and `md5` not in "
                    + "    </if> "
                    + "    <foreach collection=\"patternMd5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\"> "
                    + "    #{m} "
                    + "    </foreach> "
                    + "  </if> "
                    + "  group by `region`, time, window_size "
                    + "  order by `region`, time, window_size "
                    + "</script>"
    )
    @ResultType(Map.class)
    List<Map<String, Object>> getCountSeqWithMd5s(@Param("taskId") Long taskId, @Param("startDate") Date startDate,
                                                  @Param("endDate") Date endDate, @Param("regions") List<String> regions, @Param("patternMd5s") Set<String> patternMd5s,
                                                  @Param("flag") boolean includeFlag);

    @Select("<script> "
            + "  select md5, hashcode, max(time) as max_time, min(time) as min_time from log_cluster_pattern  "
            + "  where task_id=#{taskId} and time between #{startDate} and #{endDate} and `md5` in  "
            + "  <foreach collection=\"md5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\"> "
            + "    #{m} "
            + "  </foreach> "
            + "  <if test=\"regions != null and regions.size > 0\"> "
            + "    and region in "
            + "    <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\"> "
            + "    #{region} "
            + "    </foreach> "
            + "  </if> "
            + "  group by md5 "
            + "</script>")
    @ResultType(Map.class)
    List<Map<String, Object>> getPatternTimeRange(@Param("taskId") Long taskId, @Param("regions") List<String> regions,
                                                  @Param("md5s") Collection<String> md5s, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script> "
            + "  select task_id as taskId, md5, hashcode, max(time) as maxTime, min(time) as minTime from "
            + "  log_cluster_pattern where time between #{startDate} and #{endDate} and concat(task_id, '|', `md5`) in "
            + "  <foreach collection=\"idMd5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\"> "
            + "    #{m} "
            + "  </foreach> "
            + "  group by task_id, md5, hashcode"
            + "</script>")
    @ResultType(LogTimeRange.class)
    List<LogTimeRange> getPatternTimeRangeForMultiTask(@Param("idMd5s") Collection<String> idMd5s,
                                                       @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script> "
            + "  select task_id, region, count(pattern) as cnt from log_cluster_pattern  "
            + "  where time = #{t} and task_id in  "
            + "  <foreach collection=\"taskIds\" item=\"id\" open=\"(\" close=\")\" separator=\",\"> "
            + "    #{id} "
            + "  </foreach> "
            + "  and region in  "
            + "  <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\"> "
            + "    #{region} "
            + "  </foreach> "
            + "  group by task_id, region "
            + "</script>")
    @ResultType(Map.class)
    List<Map<String, Object>> listPatternTypeCntWithRegion(@Param("taskIds") Collection<Long> taskIds,
                                                           @Param("regions") Collection<String> regions, @Param("t") Date t);

    @Select("<script> "
            + "  select task_id, count(pattern) as cnt from log_cluster_pattern  "
            + "  where time = #{t} and task_id in  "
            + "  <foreach collection=\"taskIds\" item=\"id\" open=\"(\" close=\")\" separator=\",\"> "
            + "    #{id} "
            + "  </foreach> "
            + "  group by task_id "
            + "</script>")
    @ResultType(Map.class)
    List<Map<String, Object>> listPatternTypeCnt(@Param("taskIds") List<Long> taskIds, @Param("t") Date t);

    @Select("<script> "
            + "  select distinct md5, pattern from log_cluster_pattern  "
            + "  where time between #{start} and #{end} "
            + "  <if test=\"taskId != null\"> "
            + "    and task_id = #{taskId} "
            + "  </if> "
            + "  and `md5` in "
            + "  <foreach collection=\"md5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\"> "
            + "    #{m} "
            + "  </foreach> "
            + "  <if test=\"regions != null and regions.size > 0\"> "
            + "    and region in "
            + "    <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\"> "
            + "    #{region} "
            + "    </foreach> "
            + "  </if> "
            + "</script>")
    @ResultType(Map.class)
    List<Map<String, Object>> getMd5PatternMap(@Param("taskId") Long taskId, @Param("start") Date start,
                                               @Param("end") Date end, @Param("regions") List<String> regions, @Param("md5s") Collection<String> md5s);

    @Select("<script> "
            + "  select distinct concat(task_id, '|', md5) as idMd5, pattern from log_cluster_pattern  "
            + "  where time between #{start} and #{end} "
            + "  and concat(task_id, '|', `md5`) in "
            + "  <foreach collection=\"idMd5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\"> "
            + "    #{m} "
            + "  </foreach> "
            + "</script>")
    @ResultType(Map.class)
    List<Map<String, Object>> getIdMd5PatternMap(@Param("start") Date start, @Param("end") Date end,
                                                 @Param("idMd5s") Collection<String> idMd5s);

    @Select("<script> "
            + "  select distinct pattern from log_cluster_pattern  "
            + "  where time between #{start} and #{end} and task_id = #{taskId} "
            + "  <if test=\"regions != null and regions.size > 0\"> "
            + "    and region in "
            + "    <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\"> "
            + "    #{region} "
            + "    </foreach> "
            + "  </if> "
            + "  <if test=\"md5s != null and md5s.size > 0\"> "
            + "    and md5 in "
            + "    <foreach collection=\"md5s\" item=\"md\" open=\"(\" close=\")\" separator=\",\"> "
            + "    #{md} "
            + "    </foreach> "
            + "  </if> "
            + "</script>")
    @ResultType(String.class)
    List<String> getPatterns(@Param("taskId") Long taskId, @Param("regions") List<String> regions,
                             @Param("start") Date start, @Param("end") Date end, @Param("md5s") Collection<String> md5s);

    @Select("<script> "
            + " select pattern, md5, min(time) as startDate, max(time) as endDate, sum(support) as cnt from "
            + " log_cluster_pattern where time between #{start} and #{end} and task_id = #{taskId} "
            + "  <if test=\"md5 != null\"> "
            + "    and md5 = #{md5} "
            + "  </if> "
            + "  <if test=\"pattern != null\"> "
            + "    and pattern = #{pattern} "
            + "  </if> "
            + "  group by pattern, md5"
            + "</script>")
    @ResultType(LogPatternSummary.class)
    List<LogPatternSummary> getLogPatternSummary(@Param("taskId") Long taskId, @Param("regions") List<String> regions,
                                                 @Param("start") Date start, @Param("end") Date end, @Param("pattern") String pattern, @Param("md5") String md5);

    @Select("<script> "
            + " select logstore, region, unix_timestamp(min(time)) * 1000 as startMs, unix_timestamp(max(time)) * 1000 as"
            + " endMs, sum(support) as cnt, min(window_size) as windowSize from log_cluster_pattern where "
            + " time between #{start} and #{end} and task_id = #{taskId} "
            + "  <if test=\"regions != null and regions.size > 0\"> "
            + "    and region in "
            + "    <foreach collection=\"regions\" item=\"region\" open=\"(\" close=\")\" separator=\",\"> "
            + "     #{region} "
            + "    </foreach> "
            + "  </if> "
            + "  <if test=\"pattern != null\"> "
            + "    and pattern = #{pattern} "
            + "  </if> "
            + "  group by logstore, region"
            + "</script>")
    @ResultType(RecallPatternResponse.class)
    List<RecallPatternResponse> getRecallInfos(@Param("taskId") Long taskId, @Param("start") Date start,
                                               @Param("end") Date end, @Param("regions") List<String> regions, @Param("pattern") String pattern);

    @Select("<script> "
            + "  select region, unix_timestamp(max(time)) * 1000 as t from log_cluster_pattern  "
            + "  where 1 = 1"
            + "  <if test=\"taskId != null\"> "
            + "    and task_id = #{taskId} "
            + "  </if> "
            + "  group by region order by region"
            + "</script>")
    @ResultType(Map.class)
    List<Map<String, Object>> getRegionMaxTime(@Param("taskId") Long taskId);

    @Select(
            "<script>"
                    + "   select md5, log(sum(`support`)) as lg from log_cluster_pattern where time between #{start} and #{end}"
                    + "   <if test=\"taskId != null\">"
                    + "      and task_id = #{taskId} "
                    + "   </if>"
                    + "   <if test=\"md5s != null and md5s.size > 0\">"
                    + "      and `md5` not in "
                    + "      <foreach collection=\"md5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
                    + "         #{m} "
                    + "      </foreach>"
                    + "   </if>"
                    + "   group by md5"
                    + " </script>"
    )
    @ResultType(Map.class)
    List<Map<String, Object>> getRawLogNumberMetric(@Param("taskId") Long taskId, @Param("start") Date start,
                                                    @Param("end") Date end, @Param("md5s") Collection<String> excludeMd5s);

    @Select("<script>"
            + "   select md5, sum(`support`) as sum from log_cluster_pattern where time between #{start} and #{end}"
            + "   <if test=\"taskId != null\">"
            + "      and task_id = #{taskId} "
            + "   </if>"
            + "   <if test=\"md5s != null and md5s.size > 0\">"
            + "      and `md5` in "
            + "      <foreach collection=\"md5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
            + "         #{m} "
            + "      </foreach>"
            + "   </if>"
            + "   group by md5"
            + " </script>"
    )
    @ResultType(Map.class)
    List<Map<String, Object>> getMd5SumMap(@Param("taskId") Long taskId, @Param("start") Date start,
                                           @Param("end") Date end, @Param("md5s") Collection<String> md5s);

    @Select("<script>"
            + "   select * from log_cluster_pattern where time between #{start} and #{end}"
            + "   <if test=\"idMd5s != null and idMd5s.size > 0\">"
            + "      and concat(task_id, '|', `md5`) in "
            + "      <foreach collection=\"idMd5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
            + "         #{m} "
            + "      </foreach>"
            + "   </if>"
            + "   <if test=\"condition != null and condition.length() > 0\"> order by #{condition} </if>"
            + " </script>"
    )
    @ResultType(LogClusterPattern.class)
    @ResultMap("com.aliyun.xdragon.common.generate.log.model.map.LogClusterPatternMapper.BaseResultMap")
    List<LogClusterPattern> listPatternsForMultiTask(@Param("start") Date start, @Param("end") Date end,
                                                     @Param("idMd5s") Collection<String> idMd5s, @Param("condition") String condition);

    @Select("<script>"
            + " select * from log_cluster_pattern where time between #{start} and #{end}"
            + " <if test=\"idMd5s != null and idMd5s.size > 0\">"
            + "    and concat(`task_id`, '|', `md5`) in "
            + "    <foreach collection=\"idMd5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
            + "       #{m} "
            + "    </foreach>"
            + " </if>"
            + " and `pattern` <if test=\"!flag\"> not </if> like '${filter}'"
            + " </script>"
    )
    @ResultType(LogClusterPattern.class)
    @ResultMap("com.aliyun.xdragon.common.generate.log.model.map.LogClusterPatternMapper.BaseResultMap")
    List<LogClusterPattern> filterIdMd5(@Param("idMd5s") Collection<String> idMd5s, @Param("start") Date start,
                                        @Param("end") Date end, @Param("filter") String patternFilter, @Param("flag") boolean likeFlag);

    @Select("<script>"
            + "select md5, pattern, COUNT(*) as cnt, SUM(support) as total, MAX(time) as recent from "
            + "log_cluster_pattern where task_id = #{taskId} and time between #{begin} and #{end} "
            + "group by md5, pattern order by #{sortBy} desc limit #{offset}, #{limit}"
            + "</script>")
    @ResultType(LogPatternRank.class)
    List<LogPatternRank> getPatternRank(@Param("taskId") Long taskId, @Param("begin") Date begin,
                                        @Param("end") Date end, @Param("sortBy") String sortBy, @Param("offset") int offset, @Param("limit") int limit);

    @Select("<script>"
            + "select md5, pattern, COUNT(*) as cnt, SUM(support) as total, MAX(time) as recent from "
            + "log_cluster_pattern where task_id = #{taskId}"
            + "<if test=\"begin != null\"> and `time` <![CDATA[>=]]> #{begin} </if>"
            + "<if test=\"end != null\"> and `time` <![CDATA[<=]]> #{end} </if>"
            + "<if test=\"md5s != null and md5s.size > 0\">"
            + "  and `md5` in "
            + "    <foreach collection=\"md5s\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
            + "       #{m} "
            + "    </foreach>"
            + "</if>"
            + "group by md5, pattern"
            + "</script>")
    @ResultType(LogPatternRank.class)
    List<LogPatternRank> getPatternRankByMd5(@Param("taskId") Long taskId, @Param("begin") Date begin,
                                             @Param("end") Date end, @Param("md5s") Collection<String> md5s);

    @Select("<script>"
            + "select distinct md5, pattern from log_cluster_pattern "
            + "where time between #{start} and #{end} and task_id = #{taskId} "
            + "</script>")
    @ResultType(Map.class)
    List<Map<String, Object>> getAllMd5TaskMap(@Param("taskId") Long taskId, @Param("start") Date start,
                                               @Param("end") Date end);

    @Select("<script>"
        + "select max(`time`) from log_cluster_pattern where task_id = #{taskId} and hashcode = #{patternType}"
        + "<if test=\"region != null and region.trim() != ''\">"
        + " and region = #{region}"
        + "</if>"
        + " and `time` &gt;= #{checkTime}"
        + "</script>")
    @ResultType(Date.class)
    Date getNewestRecordTime(@Param("taskId") Long taskId, @Param("patternType") Integer patternType, @Param("region") String region, @Param("checkTime") Date checkTime);

    @Select("<script>"
            + "select region, max(`time`) as mx_time from log_cluster_pattern where task_id = #{taskId} and hashcode = #{patternType}"
            + " and `time` &gt;= #{checkTime}"
            + "<if test=\"regions != null and regions.size > 0\">"
            + "  and `region` in "
            + "    <foreach collection=\"regions\" item=\"m\" open=\"(\" close=\")\" separator=\",\">"
            + "       #{m} "
            + "    </foreach>"
            + "  group by region"
            + "</if>"
            + " </script>"
    )
    @ResultType(Map.class)
    List<Map<String, Object>> batchGetNewestRecordTime(@Param("taskId") Long taskId, @Param("patternType") Integer patternType, @Param("regions") Collection<String> regions, @Param("checkTime") Date checkTime);

    @Select("<script>"
            + "SELECT task_id, count(distinct `md5`) as cnt from `log_cluster_pattern` where task_id IN "
            + " <foreach collection=\"taskIds\" item=\"id\" open=\"(\" close=\")\" separator=\",\">"
            + " #{id} "
            + "</foreach>"
            + " and `time` &gt;= #{startDate} and `time` &lt; #{endDate} "
            + "GROUP BY task_id"
            + "</script>")
    @ResultType(Map.class)
    List<Map<String, Object>> getPatternCount(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("taskIds") Collection<Long> taskIds);

    @Select("<script>"
        + "SELECT task_id, count(distinct `md5`) as cnt from `log_cluster_pattern` where task_id IN "
        + " <foreach collection=\"taskIds\" item=\"id\" open=\"(\" close=\")\" separator=\",\">"
        + " #{id} "
        + "</foreach>"
        + " and `time` &gt;= #{startDate} and `time` &lt; #{endDate} and `md5` in ("
        + "select distinct `md5`  from `log_pattern_tag` where `task_id` in "
        + " <foreach collection=\"taskIds\" item=\"id\" open=\"(\" close=\")\" separator=\",\">"
        + " #{id} "
        + "</foreach>"
        + "and ${tagType}=#{tagValue}"
        + ") GROUP BY task_id"
        + "</script>")
    @ResultType(Map.class)
    List<Map<String, Object>> getPatternCountWithTag(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("taskIds") Collection<Long> taskIds, @Param("tagType")String tagType, @Param("tagValue")int tagValue);

    @Select("<script>"
            + "SELECT hashcode as source, `md5`, `pattern`, count(*) as cnt, sum(support) as support from `log_cluster_pattern` "
            + "where task_id = #{taskId} and `time` between #{startTime} and #{endTime} GROUP BY task_id, md5"
            + "</script>")
    @ResultType(Map.class)
    List<Map<String, Object>> getPatternAnalysisData(@Param("taskId") Long taskId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("<script>"
            + "select md5, hashcode, pattern, min(time) as first_appear from log_cluster_pattern"
            + " where task_id = #{taskId} and time between #{start} and #{end} group by md5, hashcode, pattern"
            + " having first_appear >= #{minAppear}"
            + "</script>")
    @ResultType(Map.class)
    List<Map<String, Object>> getPatternWithFirstAppear(@Param("taskId") Long taskId, @Param("start") Date start,
                                                        @Param("end") Date end, @Param("minAppear") Date minAppearTime);

    @Select("<script>"
            + "select p.md5, p.hashcode, p.pattern, p.first_appear, lib.status, lib.modify_time from"
            + " (select r.md5, r.first_appear, lcp.hashcode, lcp.pattern from log_cluster_pattern lcp"
            + " join (select md5, min(time) as first_appear from log_cluster_pattern where"
            + " task_id = #{taskId} and time between #{start} and #{end} group by md5 having first_appear >= #{minTime}"
            + " ) AS r on lcp.`md5` = r.md5 and lcp.time = r.first_appear) as p left join log_pattern_library as lib"
            + " on lib.task_id = #{taskId} and p.md5 = lib.md5 and p.pattern = lib.pattern"
            + "</script>")
    @ResultType(Map.class)
    List<Map<String, Object>> getPatternAccuracyData(@Param("taskId") Long taskId, @Param("start") Date start,
                                                     @Param("end") Date end, @Param("minTime") Date minAppearTime);
}