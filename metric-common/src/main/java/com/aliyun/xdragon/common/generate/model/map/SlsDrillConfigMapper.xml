<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.model.map.SlsDrillConfigMapper">
  <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.model.SlsDrillConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="drill_id" jdbcType="BIGINT" property="drillId" />
    <result column="processor_id" jdbcType="INTEGER" property="processorId" />
    <result column="project" jdbcType="VARCHAR" property="project" />
    <result column="logstore" jdbcType="VARCHAR" property="logstore" />
    <result column="region" jdbcType="VARCHAR" property="region" />
    <result column="user" jdbcType="VARCHAR" property="user" />
    <result column="metric" jdbcType="VARCHAR" property="metric" />
    <result column="sls_query" jdbcType="VARCHAR" property="slsQuery" />
    <result column="sls_sql" jdbcType="VARCHAR" property="slsSql" />
    <result column="data_interval" jdbcType="INTEGER" property="dataInterval" />
    <result column="max_query_range" jdbcType="INTEGER" property="maxQueryRange" />
    <result column="window_align" jdbcType="VARCHAR" property="windowAlign" />
    <result column="dimension_cols" jdbcType="VARCHAR" property="dimensionCols" />
    <result column="ts_col" jdbcType="VARCHAR" property="tsCol" />
    <result column="value_col" jdbcType="VARCHAR" property="valueCol" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    drill_id, processor_id, project, logstore, region, `user`, metric, sls_query, sls_sql, 
    data_interval, max_query_range, window_align, dimension_cols, ts_col, value_col, 
    gmt_modified, gmt_create
  </sql>
  <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.model.SlsDrillConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sls_drill_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="example != null and example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from sls_drill_config
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from sls_drill_config
    where drill_id = #{drillId,jdbcType=BIGINT}
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from sls_drill_config
    where drill_id = #{drillId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from sls_drill_config
    where drill_id = #{drillId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.model.SlsDrillConfigExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from sls_drill_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.model.SlsDrillConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="drillId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sls_drill_config (processor_id, project, logstore, 
      region, `user`, metric, 
      sls_query, sls_sql, data_interval, 
      max_query_range, window_align, dimension_cols, 
      ts_col, value_col, gmt_modified, 
      gmt_create)
    values (#{processorId,jdbcType=INTEGER}, #{project,jdbcType=VARCHAR}, #{logstore,jdbcType=VARCHAR}, 
      #{region,jdbcType=VARCHAR}, #{user,jdbcType=VARCHAR}, #{metric,jdbcType=VARCHAR}, 
      #{slsQuery,jdbcType=VARCHAR}, #{slsSql,jdbcType=VARCHAR}, #{dataInterval,jdbcType=INTEGER}, 
      #{maxQueryRange,jdbcType=INTEGER}, #{windowAlign,jdbcType=VARCHAR}, #{dimensionCols,jdbcType=VARCHAR}, 
      #{tsCol,jdbcType=VARCHAR}, #{valueCol,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtCreate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.model.SlsDrillConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="drillId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sls_drill_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="processorId != null">
        processor_id,
      </if>
      <if test="project != null">
        project,
      </if>
      <if test="logstore != null">
        logstore,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="user != null">
        `user`,
      </if>
      <if test="metric != null">
        metric,
      </if>
      <if test="slsQuery != null">
        sls_query,
      </if>
      <if test="slsSql != null">
        sls_sql,
      </if>
      <if test="dataInterval != null">
        data_interval,
      </if>
      <if test="maxQueryRange != null">
        max_query_range,
      </if>
      <if test="windowAlign != null">
        window_align,
      </if>
      <if test="dimensionCols != null">
        dimension_cols,
      </if>
      <if test="tsCol != null">
        ts_col,
      </if>
      <if test="valueCol != null">
        value_col,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="processorId != null">
        #{processorId,jdbcType=INTEGER},
      </if>
      <if test="project != null">
        #{project,jdbcType=VARCHAR},
      </if>
      <if test="logstore != null">
        #{logstore,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="user != null">
        #{user,jdbcType=VARCHAR},
      </if>
      <if test="metric != null">
        #{metric,jdbcType=VARCHAR},
      </if>
      <if test="slsQuery != null">
        #{slsQuery,jdbcType=VARCHAR},
      </if>
      <if test="slsSql != null">
        #{slsSql,jdbcType=VARCHAR},
      </if>
      <if test="dataInterval != null">
        #{dataInterval,jdbcType=INTEGER},
      </if>
      <if test="maxQueryRange != null">
        #{maxQueryRange,jdbcType=INTEGER},
      </if>
      <if test="windowAlign != null">
        #{windowAlign,jdbcType=VARCHAR},
      </if>
      <if test="dimensionCols != null">
        #{dimensionCols,jdbcType=VARCHAR},
      </if>
      <if test="tsCol != null">
        #{tsCol,jdbcType=VARCHAR},
      </if>
      <if test="valueCol != null">
        #{valueCol,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.model.SlsDrillConfigExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from sls_drill_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sls_drill_config
    <set>
      <if test="record.drillId != null">
        drill_id = #{record.drillId,jdbcType=BIGINT},
      </if>
      <if test="record.processorId != null">
        processor_id = #{record.processorId,jdbcType=INTEGER},
      </if>
      <if test="record.project != null">
        project = #{record.project,jdbcType=VARCHAR},
      </if>
      <if test="record.logstore != null">
        logstore = #{record.logstore,jdbcType=VARCHAR},
      </if>
      <if test="record.region != null">
        region = #{record.region,jdbcType=VARCHAR},
      </if>
      <if test="record.user != null">
        `user` = #{record.user,jdbcType=VARCHAR},
      </if>
      <if test="record.metric != null">
        metric = #{record.metric,jdbcType=VARCHAR},
      </if>
      <if test="record.slsQuery != null">
        sls_query = #{record.slsQuery,jdbcType=VARCHAR},
      </if>
      <if test="record.slsSql != null">
        sls_sql = #{record.slsSql,jdbcType=VARCHAR},
      </if>
      <if test="record.dataInterval != null">
        data_interval = #{record.dataInterval,jdbcType=INTEGER},
      </if>
      <if test="record.maxQueryRange != null">
        max_query_range = #{record.maxQueryRange,jdbcType=INTEGER},
      </if>
      <if test="record.windowAlign != null">
        window_align = #{record.windowAlign,jdbcType=VARCHAR},
      </if>
      <if test="record.dimensionCols != null">
        dimension_cols = #{record.dimensionCols,jdbcType=VARCHAR},
      </if>
      <if test="record.tsCol != null">
        ts_col = #{record.tsCol,jdbcType=VARCHAR},
      </if>
      <if test="record.valueCol != null">
        value_col = #{record.valueCol,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sls_drill_config
    set drill_id = #{record.drillId,jdbcType=BIGINT},
      processor_id = #{record.processorId,jdbcType=INTEGER},
      project = #{record.project,jdbcType=VARCHAR},
      logstore = #{record.logstore,jdbcType=VARCHAR},
      region = #{record.region,jdbcType=VARCHAR},
      `user` = #{record.user,jdbcType=VARCHAR},
      metric = #{record.metric,jdbcType=VARCHAR},
      sls_query = #{record.slsQuery,jdbcType=VARCHAR},
      sls_sql = #{record.slsSql,jdbcType=VARCHAR},
      data_interval = #{record.dataInterval,jdbcType=INTEGER},
      max_query_range = #{record.maxQueryRange,jdbcType=INTEGER},
      window_align = #{record.windowAlign,jdbcType=VARCHAR},
      dimension_cols = #{record.dimensionCols,jdbcType=VARCHAR},
      ts_col = #{record.tsCol,jdbcType=VARCHAR},
      value_col = #{record.valueCol,jdbcType=VARCHAR},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.xdragon.common.generate.model.SlsDrillConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sls_drill_config
    <set>
      <if test="processorId != null">
        processor_id = #{processorId,jdbcType=INTEGER},
      </if>
      <if test="project != null">
        project = #{project,jdbcType=VARCHAR},
      </if>
      <if test="logstore != null">
        logstore = #{logstore,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        region = #{region,jdbcType=VARCHAR},
      </if>
      <if test="user != null">
        `user` = #{user,jdbcType=VARCHAR},
      </if>
      <if test="metric != null">
        metric = #{metric,jdbcType=VARCHAR},
      </if>
      <if test="slsQuery != null">
        sls_query = #{slsQuery,jdbcType=VARCHAR},
      </if>
      <if test="slsSql != null">
        sls_sql = #{slsSql,jdbcType=VARCHAR},
      </if>
      <if test="dataInterval != null">
        data_interval = #{dataInterval,jdbcType=INTEGER},
      </if>
      <if test="maxQueryRange != null">
        max_query_range = #{maxQueryRange,jdbcType=INTEGER},
      </if>
      <if test="windowAlign != null">
        window_align = #{windowAlign,jdbcType=VARCHAR},
      </if>
      <if test="dimensionCols != null">
        dimension_cols = #{dimensionCols,jdbcType=VARCHAR},
      </if>
      <if test="tsCol != null">
        ts_col = #{tsCol,jdbcType=VARCHAR},
      </if>
      <if test="valueCol != null">
        value_col = #{valueCol,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where drill_id = #{drillId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.model.SlsDrillConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sls_drill_config
    set processor_id = #{processorId,jdbcType=INTEGER},
      project = #{project,jdbcType=VARCHAR},
      logstore = #{logstore,jdbcType=VARCHAR},
      region = #{region,jdbcType=VARCHAR},
      `user` = #{user,jdbcType=VARCHAR},
      metric = #{metric,jdbcType=VARCHAR},
      sls_query = #{slsQuery,jdbcType=VARCHAR},
      sls_sql = #{slsSql,jdbcType=VARCHAR},
      data_interval = #{dataInterval,jdbcType=INTEGER},
      max_query_range = #{maxQueryRange,jdbcType=INTEGER},
      window_align = #{windowAlign,jdbcType=VARCHAR},
      dimension_cols = #{dimensionCols,jdbcType=VARCHAR},
      ts_col = #{tsCol,jdbcType=VARCHAR},
      value_col = #{valueCol,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
    where drill_id = #{drillId,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.model.SlsDrillConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sls_drill_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert" keyColumn="drill_id" keyProperty="drillId" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into sls_drill_config
    (processor_id, project, logstore, region, `user`, metric, sls_query, sls_sql, data_interval, 
      max_query_range, window_align, dimension_cols, ts_col, value_col, gmt_modified, 
      gmt_create)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.processorId,jdbcType=INTEGER}, #{item.project,jdbcType=VARCHAR}, #{item.logstore,jdbcType=VARCHAR}, 
        #{item.region,jdbcType=VARCHAR}, #{item.user,jdbcType=VARCHAR}, #{item.metric,jdbcType=VARCHAR}, 
        #{item.slsQuery,jdbcType=VARCHAR}, #{item.slsSql,jdbcType=VARCHAR}, #{item.dataInterval,jdbcType=INTEGER}, 
        #{item.maxQueryRange,jdbcType=INTEGER}, #{item.windowAlign,jdbcType=VARCHAR}, #{item.dimensionCols,jdbcType=VARCHAR}, 
        #{item.tsCol,jdbcType=VARCHAR}, #{item.valueCol,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.gmtCreate,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="drill_id" keyProperty="list.drillId" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into sls_drill_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'processor_id'.toString() == column.value">
          #{item.processorId,jdbcType=INTEGER}
        </if>
        <if test="'project'.toString() == column.value">
          #{item.project,jdbcType=VARCHAR}
        </if>
        <if test="'logstore'.toString() == column.value">
          #{item.logstore,jdbcType=VARCHAR}
        </if>
        <if test="'region'.toString() == column.value">
          #{item.region,jdbcType=VARCHAR}
        </if>
        <if test="'user'.toString() == column.value">
          #{item.user,jdbcType=VARCHAR}
        </if>
        <if test="'metric'.toString() == column.value">
          #{item.metric,jdbcType=VARCHAR}
        </if>
        <if test="'sls_query'.toString() == column.value">
          #{item.slsQuery,jdbcType=VARCHAR}
        </if>
        <if test="'sls_sql'.toString() == column.value">
          #{item.slsSql,jdbcType=VARCHAR}
        </if>
        <if test="'data_interval'.toString() == column.value">
          #{item.dataInterval,jdbcType=INTEGER}
        </if>
        <if test="'max_query_range'.toString() == column.value">
          #{item.maxQueryRange,jdbcType=INTEGER}
        </if>
        <if test="'window_align'.toString() == column.value">
          #{item.windowAlign,jdbcType=VARCHAR}
        </if>
        <if test="'dimension_cols'.toString() == column.value">
          #{item.dimensionCols,jdbcType=VARCHAR}
        </if>
        <if test="'ts_col'.toString() == column.value">
          #{item.tsCol,jdbcType=VARCHAR}
        </if>
        <if test="'value_col'.toString() == column.value">
          #{item.valueCol,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_create'.toString() == column.value">
          #{item.gmtCreate,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
  <insert id="upsertSelective" keyColumn="drill_id" keyProperty="drillId" parameterType="com.aliyun.xdragon.common.generate.model.SlsDrillConfig" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into sls_drill_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="drillId != null">
        drill_id,
      </if>
      <if test="processorId != null">
        processor_id,
      </if>
      <if test="project != null">
        project,
      </if>
      <if test="logstore != null">
        logstore,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="user != null">
        `user`,
      </if>
      <if test="metric != null">
        metric,
      </if>
      <if test="slsQuery != null">
        sls_query,
      </if>
      <if test="slsSql != null">
        sls_sql,
      </if>
      <if test="dataInterval != null">
        data_interval,
      </if>
      <if test="maxQueryRange != null">
        max_query_range,
      </if>
      <if test="windowAlign != null">
        window_align,
      </if>
      <if test="dimensionCols != null">
        dimension_cols,
      </if>
      <if test="tsCol != null">
        ts_col,
      </if>
      <if test="valueCol != null">
        value_col,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="drillId != null">
        #{drillId,jdbcType=BIGINT},
      </if>
      <if test="processorId != null">
        #{processorId,jdbcType=INTEGER},
      </if>
      <if test="project != null">
        #{project,jdbcType=VARCHAR},
      </if>
      <if test="logstore != null">
        #{logstore,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="user != null">
        #{user,jdbcType=VARCHAR},
      </if>
      <if test="metric != null">
        #{metric,jdbcType=VARCHAR},
      </if>
      <if test="slsQuery != null">
        #{slsQuery,jdbcType=VARCHAR},
      </if>
      <if test="slsSql != null">
        #{slsSql,jdbcType=VARCHAR},
      </if>
      <if test="dataInterval != null">
        #{dataInterval,jdbcType=INTEGER},
      </if>
      <if test="maxQueryRange != null">
        #{maxQueryRange,jdbcType=INTEGER},
      </if>
      <if test="windowAlign != null">
        #{windowAlign,jdbcType=VARCHAR},
      </if>
      <if test="dimensionCols != null">
        #{dimensionCols,jdbcType=VARCHAR},
      </if>
      <if test="tsCol != null">
        #{tsCol,jdbcType=VARCHAR},
      </if>
      <if test="valueCol != null">
        #{valueCol,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="drillId != null">
        drill_id = #{drillId,jdbcType=BIGINT},
      </if>
      <if test="processorId != null">
        processor_id = #{processorId,jdbcType=INTEGER},
      </if>
      <if test="project != null">
        project = #{project,jdbcType=VARCHAR},
      </if>
      <if test="logstore != null">
        logstore = #{logstore,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        region = #{region,jdbcType=VARCHAR},
      </if>
      <if test="user != null">
        `user` = #{user,jdbcType=VARCHAR},
      </if>
      <if test="metric != null">
        metric = #{metric,jdbcType=VARCHAR},
      </if>
      <if test="slsQuery != null">
        sls_query = #{slsQuery,jdbcType=VARCHAR},
      </if>
      <if test="slsSql != null">
        sls_sql = #{slsSql,jdbcType=VARCHAR},
      </if>
      <if test="dataInterval != null">
        data_interval = #{dataInterval,jdbcType=INTEGER},
      </if>
      <if test="maxQueryRange != null">
        max_query_range = #{maxQueryRange,jdbcType=INTEGER},
      </if>
      <if test="windowAlign != null">
        window_align = #{windowAlign,jdbcType=VARCHAR},
      </if>
      <if test="dimensionCols != null">
        dimension_cols = #{dimensionCols,jdbcType=VARCHAR},
      </if>
      <if test="tsCol != null">
        ts_col = #{tsCol,jdbcType=VARCHAR},
      </if>
      <if test="valueCol != null">
        value_col = #{valueCol,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="upsert" keyColumn="drill_id" keyProperty="drillId" parameterType="com.aliyun.xdragon.common.generate.model.SlsDrillConfig" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into sls_drill_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="drillId != null">
        drill_id,
      </if>
      processor_id,
      project,
      logstore,
      region,
      `user`,
      metric,
      sls_query,
      sls_sql,
      data_interval,
      max_query_range,
      window_align,
      dimension_cols,
      ts_col,
      value_col,
      gmt_modified,
      gmt_create,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="drillId != null">
        #{drillId,jdbcType=BIGINT},
      </if>
      #{processorId,jdbcType=INTEGER},
      #{project,jdbcType=VARCHAR},
      #{logstore,jdbcType=VARCHAR},
      #{region,jdbcType=VARCHAR},
      #{user,jdbcType=VARCHAR},
      #{metric,jdbcType=VARCHAR},
      #{slsQuery,jdbcType=VARCHAR},
      #{slsSql,jdbcType=VARCHAR},
      #{dataInterval,jdbcType=INTEGER},
      #{maxQueryRange,jdbcType=INTEGER},
      #{windowAlign,jdbcType=VARCHAR},
      #{dimensionCols,jdbcType=VARCHAR},
      #{tsCol,jdbcType=VARCHAR},
      #{valueCol,jdbcType=VARCHAR},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{gmtCreate,jdbcType=TIMESTAMP},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="drillId != null">
        drill_id = #{drillId,jdbcType=BIGINT},
      </if>
      processor_id = #{processorId,jdbcType=INTEGER},
      project = #{project,jdbcType=VARCHAR},
      logstore = #{logstore,jdbcType=VARCHAR},
      region = #{region,jdbcType=VARCHAR},
      `user` = #{user,jdbcType=VARCHAR},
      metric = #{metric,jdbcType=VARCHAR},
      sls_query = #{slsQuery,jdbcType=VARCHAR},
      sls_sql = #{slsSql,jdbcType=VARCHAR},
      data_interval = #{dataInterval,jdbcType=INTEGER},
      max_query_range = #{maxQueryRange,jdbcType=INTEGER},
      window_align = #{windowAlign,jdbcType=VARCHAR},
      dimension_cols = #{dimensionCols,jdbcType=VARCHAR},
      ts_col = #{tsCol,jdbcType=VARCHAR},
      value_col = #{valueCol,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
    </trim>
  </insert>
  <select id="selectOneByExample" parameterType="com.aliyun.xdragon.common.generate.model.SlsDrillConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from sls_drill_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from sls_drill_config
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
</mapper>