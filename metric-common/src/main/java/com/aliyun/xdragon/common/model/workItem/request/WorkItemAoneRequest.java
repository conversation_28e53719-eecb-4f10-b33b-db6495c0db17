package com.aliyun.xdragon.common.model.workItem.request;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/15
 */
@Data
public class WorkItemAoneRequest implements Serializable {

    private static final long serialVersionUID = 3303913076042219012L;
    /**
     * id
     */
    @NotNull(message = "id is null")
    @Positive
    private Long id;

    /**
     * sourceId
     */
    @NotNull(message = "sourceId is null")
    @Positive
    private Long sourceId;

    /**
     * aone comment
     */
    @NotBlank(message = "comment empty")
    private String comment;

    /**
     * submitter name
     */
    @NotBlank
    private String submitter;

    /**
     * aone assigned emp id
     */
    private String assignedEmpId;
}
