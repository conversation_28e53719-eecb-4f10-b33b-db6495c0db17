package com.aliyun.xdragon.common.generate.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class ChatOpsExecuteResult implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column chat_ops_execute_result.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column chat_ops_execute_result.workflow_instance_id
     *
     * @mbg.generated
     */
    private Long workflowInstanceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column chat_ops_execute_result.workitem_id
     *
     * @mbg.generated
     */
    private Long workitemId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column chat_ops_execute_result.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column chat_ops_execute_result.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column chat_ops_execute_result.cmd
     *
     * @mbg.generated
     */
    private String cmd;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column chat_ops_execute_result.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column chat_ops_execute_result.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column chat_ops_execute_result.result
     *
     * @mbg.generated
     */
    private String result;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table chat_ops_execute_result
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column chat_ops_execute_result.id
     *
     * @return the value of chat_ops_execute_result.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column chat_ops_execute_result.id
     *
     * @param id the value for chat_ops_execute_result.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column chat_ops_execute_result.workflow_instance_id
     *
     * @return the value of chat_ops_execute_result.workflow_instance_id
     *
     * @mbg.generated
     */
    public Long getWorkflowInstanceId() {
        return workflowInstanceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column chat_ops_execute_result.workflow_instance_id
     *
     * @param workflowInstanceId the value for chat_ops_execute_result.workflow_instance_id
     *
     * @mbg.generated
     */
    public void setWorkflowInstanceId(Long workflowInstanceId) {
        this.workflowInstanceId = workflowInstanceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column chat_ops_execute_result.workitem_id
     *
     * @return the value of chat_ops_execute_result.workitem_id
     *
     * @mbg.generated
     */
    public Long getWorkitemId() {
        return workitemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column chat_ops_execute_result.workitem_id
     *
     * @param workitemId the value for chat_ops_execute_result.workitem_id
     *
     * @mbg.generated
     */
    public void setWorkitemId(Long workitemId) {
        this.workitemId = workitemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column chat_ops_execute_result.name
     *
     * @return the value of chat_ops_execute_result.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column chat_ops_execute_result.name
     *
     * @param name the value for chat_ops_execute_result.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column chat_ops_execute_result.status
     *
     * @return the value of chat_ops_execute_result.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column chat_ops_execute_result.status
     *
     * @param status the value for chat_ops_execute_result.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column chat_ops_execute_result.cmd
     *
     * @return the value of chat_ops_execute_result.cmd
     *
     * @mbg.generated
     */
    public String getCmd() {
        return cmd;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column chat_ops_execute_result.cmd
     *
     * @param cmd the value for chat_ops_execute_result.cmd
     *
     * @mbg.generated
     */
    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column chat_ops_execute_result.gmt_create
     *
     * @return the value of chat_ops_execute_result.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column chat_ops_execute_result.gmt_create
     *
     * @param gmtCreate the value for chat_ops_execute_result.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column chat_ops_execute_result.gmt_modified
     *
     * @return the value of chat_ops_execute_result.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column chat_ops_execute_result.gmt_modified
     *
     * @param gmtModified the value for chat_ops_execute_result.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column chat_ops_execute_result.result
     *
     * @return the value of chat_ops_execute_result.result
     *
     * @mbg.generated
     */
    public String getResult() {
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column chat_ops_execute_result.result
     *
     * @param result the value for chat_ops_execute_result.result
     *
     * @mbg.generated
     */
    public void setResult(String result) {
        this.result = result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table chat_ops_execute_result
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ChatOpsExecuteResult other = (ChatOpsExecuteResult) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getWorkflowInstanceId() == null ? other.getWorkflowInstanceId() == null : this.getWorkflowInstanceId().equals(other.getWorkflowInstanceId()))
            && (this.getWorkitemId() == null ? other.getWorkitemId() == null : this.getWorkitemId().equals(other.getWorkitemId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getCmd() == null ? other.getCmd() == null : this.getCmd().equals(other.getCmd()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtModified() == null ? other.getGmtModified() == null : this.getGmtModified().equals(other.getGmtModified()))
            && (this.getResult() == null ? other.getResult() == null : this.getResult().equals(other.getResult()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table chat_ops_execute_result
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getWorkflowInstanceId() == null) ? 0 : getWorkflowInstanceId().hashCode());
        result = prime * result + ((getWorkitemId() == null) ? 0 : getWorkitemId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getCmd() == null) ? 0 : getCmd().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtModified() == null) ? 0 : getGmtModified().hashCode());
        result = prime * result + ((getResult() == null) ? 0 : getResult().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table chat_ops_execute_result
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", workflowInstanceId=").append(workflowInstanceId);
        sb.append(", workitemId=").append(workitemId);
        sb.append(", name=").append(name);
        sb.append(", status=").append(status);
        sb.append(", cmd=").append(cmd);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", result=").append(result);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table chat_ops_execute_result
     *
     * @mbg.generated
     */
    public enum Column {
        id("id", "id", "BIGINT", false),
        workflowInstanceId("workflow_instance_id", "workflowInstanceId", "BIGINT", false),
        workitemId("workitem_id", "workitemId", "BIGINT", false),
        name("name", "name", "VARCHAR", true),
        status("status", "status", "TINYINT", true),
        cmd("cmd", "cmd", "VARCHAR", false),
        gmtCreate("gmt_create", "gmtCreate", "TIMESTAMP", false),
        gmtModified("gmt_modified", "gmtModified", "TIMESTAMP", false),
        result("result", "result", "LONGVARCHAR", true);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table chat_ops_execute_result
         *
         * @mbg.generated
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}