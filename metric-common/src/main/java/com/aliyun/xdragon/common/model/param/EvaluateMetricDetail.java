package com.aliyun.xdragon.common.model.param;

import com.aliyun.xdragon.common.generate.model.EvaluateMetricRawMonitor;
import com.aliyun.xdragon.common.generate.model.EvaluateMonitorPercentile;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
public class EvaluateMetricDetail implements Serializable {

    private static final long serialVersionUID = 5729142123007108L;

    private List<EvaluateMetricRawMonitor> rawMonitorList;

    private List<EvaluateMonitorPercentile> monitorPercentileList;

    public List<EvaluateMetricRawMonitor> getRawMonitorList() {
        return rawMonitorList;
    }

    public void setRawMonitorList(List<EvaluateMetricRawMonitor> rawMonitorList) {
        this.rawMonitorList = rawMonitorList;
    }

    public List<EvaluateMonitorPercentile> getMonitorPercentileList() {
        return monitorPercentileList;
    }

    public void setMonitorPercentileList(List<EvaluateMonitorPercentile> monitorPercentileList) {
        this.monitorPercentileList = monitorPercentileList;
    }
}
