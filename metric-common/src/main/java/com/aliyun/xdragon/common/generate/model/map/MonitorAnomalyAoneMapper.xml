<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.model.map.MonitorAnomalyAoneMapper">
    <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.model.MonitorAnomalyAone">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="anomaly_desc" jdbcType="VARCHAR" property="anomalyDesc"/>
        <result column="anomaly_type" jdbcType="VARCHAR" property="anomalyType"/>
        <result column="critical_attribute" jdbcType="VARCHAR" property="criticalAttribute"/>
        <result column="detection_name" jdbcType="VARCHAR" property="detectionName"/>
        <result column="link" jdbcType="VARCHAR" property="link"/>
        <result column="monitor_type" jdbcType="VARCHAR" property="monitorType"/>
        <result column="monitor_name" jdbcType="VARCHAR" property="monitorName"/>
        <result column="owner" jdbcType="VARCHAR" property="owner"/>
        <result column="value" jdbcType="DOUBLE" property="value"/>
        <result column="aone" jdbcType="VARCHAR" property="aone"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="comment" jdbcType="VARCHAR" property="comment"/>
        <result column="machineIds" jdbcType="VARCHAR" property="machineids"/>
        <result column="ds" jdbcType="TIMESTAMP" property="ds"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, anomaly_desc, anomaly_type, critical_attribute, detection_name, link, monitor_type,
        monitor_name, `owner`, `value`, aone, `status`, description, `comment`, machineIds,
        ds
    </sql>
    <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.model.MonitorAnomalyAoneExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from monitor_anomaly_aone
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="example != null and example.distinct">
            distinct
        </if>
        <choose>
            <when test="selective != null and selective.length &gt; 0">
                <foreach collection="selective" item="column" separator=",">
                    ${column.aliasedEscapedColumnName}
                </foreach>
            </when>
            <otherwise>
                <include refid="Base_Column_List"/>
            </otherwise>
        </choose>
        from monitor_anomaly_aone
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
        <if test="example != null and example.orderByClause != null">
            order by ${example.orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from monitor_anomaly_aone
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <choose>
            <when test="selective != null and selective.length &gt; 0">
                <foreach collection="selective" item="column" separator=",">
                    ${column.aliasedEscapedColumnName}
                </foreach>
            </when>
            <otherwise>
                <include refid="Base_Column_List"/>
            </otherwise>
        </choose>
        from monitor_anomaly_aone
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from monitor_anomaly_aone
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.model.MonitorAnomalyAoneExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from monitor_anomaly_aone
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.model.MonitorAnomalyAone">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into monitor_anomaly_aone (id, anomaly_desc, anomaly_type,
        critical_attribute, detection_name, link,
        monitor_type, monitor_name, `owner`,
        `value`, aone, `status`,
        description, `comment`, machineIds,
        ds)
        values (#{id,jdbcType=INTEGER}, #{anomalyDesc,jdbcType=VARCHAR}, #{anomalyType,jdbcType=VARCHAR},
        #{criticalAttribute,jdbcType=VARCHAR}, #{detectionName,jdbcType=VARCHAR}, #{link,jdbcType=VARCHAR},
        #{monitorType,jdbcType=VARCHAR}, #{monitorName,jdbcType=VARCHAR}, #{owner,jdbcType=VARCHAR},
        #{value,jdbcType=DOUBLE}, #{aone,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
        #{description,jdbcType=VARCHAR}, #{comment,jdbcType=VARCHAR}, #{machineids,jdbcType=VARCHAR},
        #{ds,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.model.MonitorAnomalyAone">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into monitor_anomaly_aone
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="anomalyDesc != null">
                anomaly_desc,
            </if>
            <if test="anomalyType != null">
                anomaly_type,
            </if>
            <if test="criticalAttribute != null">
                critical_attribute,
            </if>
            <if test="detectionName != null">
                detection_name,
            </if>
            <if test="link != null">
                link,
            </if>
            <if test="monitorType != null">
                monitor_type,
            </if>
            <if test="monitorName != null">
                monitor_name,
            </if>
            <if test="owner != null">
                `owner`,
            </if>
            <if test="value != null">
                `value`,
            </if>
            <if test="aone != null">
                aone,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="comment != null">
                `comment`,
            </if>
            <if test="machineids != null">
                machineIds,
            </if>
            <if test="ds != null">
                ds,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="anomalyDesc != null">
                #{anomalyDesc,jdbcType=VARCHAR},
            </if>
            <if test="anomalyType != null">
                #{anomalyType,jdbcType=VARCHAR},
            </if>
            <if test="criticalAttribute != null">
                #{criticalAttribute,jdbcType=VARCHAR},
            </if>
            <if test="detectionName != null">
                #{detectionName,jdbcType=VARCHAR},
            </if>
            <if test="link != null">
                #{link,jdbcType=VARCHAR},
            </if>
            <if test="monitorType != null">
                #{monitorType,jdbcType=VARCHAR},
            </if>
            <if test="monitorName != null">
                #{monitorName,jdbcType=VARCHAR},
            </if>
            <if test="owner != null">
                #{owner,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                #{value,jdbcType=DOUBLE},
            </if>
            <if test="aone != null">
                #{aone,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="comment != null">
                #{comment,jdbcType=VARCHAR},
            </if>
            <if test="machineids != null">
                #{machineids,jdbcType=VARCHAR},
            </if>
            <if test="ds != null">
                #{ds,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.model.MonitorAnomalyAoneExample"
            resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select count(*) from monitor_anomaly_aone
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update monitor_anomaly_aone
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.anomalyDesc != null">
                anomaly_desc = #{record.anomalyDesc,jdbcType=VARCHAR},
            </if>
            <if test="record.anomalyType != null">
                anomaly_type = #{record.anomalyType,jdbcType=VARCHAR},
            </if>
            <if test="record.criticalAttribute != null">
                critical_attribute = #{record.criticalAttribute,jdbcType=VARCHAR},
            </if>
            <if test="record.detectionName != null">
                detection_name = #{record.detectionName,jdbcType=VARCHAR},
            </if>
            <if test="record.link != null">
                link = #{record.link,jdbcType=VARCHAR},
            </if>
            <if test="record.monitorType != null">
                monitor_type = #{record.monitorType,jdbcType=VARCHAR},
            </if>
            <if test="record.monitorName != null">
                monitor_name = #{record.monitorName,jdbcType=VARCHAR},
            </if>
            <if test="record.owner != null">
                `owner` = #{record.owner,jdbcType=VARCHAR},
            </if>
            <if test="record.value != null">
                `value` = #{record.value,jdbcType=DOUBLE},
            </if>
            <if test="record.aone != null">
                aone = #{record.aone,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                `status` = #{record.status,jdbcType=INTEGER},
            </if>
            <if test="record.description != null">
                description = #{record.description,jdbcType=VARCHAR},
            </if>
            <if test="record.comment != null">
                `comment` = #{record.comment,jdbcType=VARCHAR},
            </if>
            <if test="record.machineids != null">
                machineIds = #{record.machineids,jdbcType=VARCHAR},
            </if>
            <if test="record.ds != null">
                ds = #{record.ds,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update monitor_anomaly_aone
        set id = #{record.id,jdbcType=INTEGER},
        anomaly_desc = #{record.anomalyDesc,jdbcType=VARCHAR},
        anomaly_type = #{record.anomalyType,jdbcType=VARCHAR},
        critical_attribute = #{record.criticalAttribute,jdbcType=VARCHAR},
        detection_name = #{record.detectionName,jdbcType=VARCHAR},
        link = #{record.link,jdbcType=VARCHAR},
        monitor_type = #{record.monitorType,jdbcType=VARCHAR},
        monitor_name = #{record.monitorName,jdbcType=VARCHAR},
        `owner` = #{record.owner,jdbcType=VARCHAR},
        `value` = #{record.value,jdbcType=DOUBLE},
        aone = #{record.aone,jdbcType=VARCHAR},
        `status` = #{record.status,jdbcType=INTEGER},
        description = #{record.description,jdbcType=VARCHAR},
        `comment` = #{record.comment,jdbcType=VARCHAR},
        machineIds = #{record.machineids,jdbcType=VARCHAR},
        ds = #{record.ds,jdbcType=TIMESTAMP}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.aliyun.xdragon.common.generate.model.MonitorAnomalyAone">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update monitor_anomaly_aone
        <set>
            <if test="anomalyDesc != null">
                anomaly_desc = #{anomalyDesc,jdbcType=VARCHAR},
            </if>
            <if test="anomalyType != null">
                anomaly_type = #{anomalyType,jdbcType=VARCHAR},
            </if>
            <if test="criticalAttribute != null">
                critical_attribute = #{criticalAttribute,jdbcType=VARCHAR},
            </if>
            <if test="detectionName != null">
                detection_name = #{detectionName,jdbcType=VARCHAR},
            </if>
            <if test="link != null">
                link = #{link,jdbcType=VARCHAR},
            </if>
            <if test="monitorType != null">
                monitor_type = #{monitorType,jdbcType=VARCHAR},
            </if>
            <if test="monitorName != null">
                monitor_name = #{monitorName,jdbcType=VARCHAR},
            </if>
            <if test="owner != null">
                `owner` = #{owner,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                `value` = #{value,jdbcType=DOUBLE},
            </if>
            <if test="aone != null">
                aone = #{aone,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="comment != null">
                `comment` = #{comment,jdbcType=VARCHAR},
            </if>
            <if test="machineids != null">
                machineIds = #{machineids,jdbcType=VARCHAR},
            </if>
            <if test="ds != null">
                ds = #{ds,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.model.MonitorAnomalyAone">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update monitor_anomaly_aone
        set anomaly_desc = #{anomalyDesc,jdbcType=VARCHAR},
        anomaly_type = #{anomalyType,jdbcType=VARCHAR},
        critical_attribute = #{criticalAttribute,jdbcType=VARCHAR},
        detection_name = #{detectionName,jdbcType=VARCHAR},
        link = #{link,jdbcType=VARCHAR},
        monitor_type = #{monitorType,jdbcType=VARCHAR},
        monitor_name = #{monitorName,jdbcType=VARCHAR},
        `owner` = #{owner,jdbcType=VARCHAR},
        `value` = #{value,jdbcType=DOUBLE},
        aone = #{aone,jdbcType=VARCHAR},
        `status` = #{status,jdbcType=INTEGER},
        description = #{description,jdbcType=VARCHAR},
        `comment` = #{comment,jdbcType=VARCHAR},
        machineIds = #{machineids,jdbcType=VARCHAR},
        ds = #{ds,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByExampleWithRowbounds"
            parameterType="com.aliyun.xdragon.common.generate.model.MonitorAnomalyAoneExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from monitor_anomaly_aone
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <insert id="batchInsert" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into monitor_anomaly_aone
        (id, anomaly_desc, anomaly_type, critical_attribute, detection_name, link, monitor_type,
        monitor_name, `owner`, `value`, aone, `status`, description, `comment`, machineIds,
        ds)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=INTEGER}, #{item.anomalyDesc,jdbcType=VARCHAR}, #{item.anomalyType,jdbcType=VARCHAR},
            #{item.criticalAttribute,jdbcType=VARCHAR}, #{item.detectionName,jdbcType=VARCHAR},
            #{item.link,jdbcType=VARCHAR}, #{item.monitorType,jdbcType=VARCHAR}, #{item.monitorName,jdbcType=VARCHAR},
            #{item.owner,jdbcType=VARCHAR}, #{item.value,jdbcType=DOUBLE}, #{item.aone,jdbcType=VARCHAR},
            #{item.status,jdbcType=INTEGER}, #{item.description,jdbcType=VARCHAR}, #{item.comment,jdbcType=VARCHAR},
            #{item.machineids,jdbcType=VARCHAR}, #{item.ds,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="batchInsertSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into monitor_anomaly_aone (
        <foreach collection="selective" item="column" separator=",">
            ${column.escapedColumnName}
        </foreach>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            <foreach collection="selective" item="column" separator=",">
                <if test="'id'.toString() == column.value">
                    #{item.id,jdbcType=INTEGER}
                </if>
                <if test="'anomaly_desc'.toString() == column.value">
                    #{item.anomalyDesc,jdbcType=VARCHAR}
                </if>
                <if test="'anomaly_type'.toString() == column.value">
                    #{item.anomalyType,jdbcType=VARCHAR}
                </if>
                <if test="'critical_attribute'.toString() == column.value">
                    #{item.criticalAttribute,jdbcType=VARCHAR}
                </if>
                <if test="'detection_name'.toString() == column.value">
                    #{item.detectionName,jdbcType=VARCHAR}
                </if>
                <if test="'link'.toString() == column.value">
                    #{item.link,jdbcType=VARCHAR}
                </if>
                <if test="'monitor_type'.toString() == column.value">
                    #{item.monitorType,jdbcType=VARCHAR}
                </if>
                <if test="'monitor_name'.toString() == column.value">
                    #{item.monitorName,jdbcType=VARCHAR}
                </if>
                <if test="'owner'.toString() == column.value">
                    #{item.owner,jdbcType=VARCHAR}
                </if>
                <if test="'value'.toString() == column.value">
                    #{item.value,jdbcType=DOUBLE}
                </if>
                <if test="'aone'.toString() == column.value">
                    #{item.aone,jdbcType=VARCHAR}
                </if>
                <if test="'status'.toString() == column.value">
                    #{item.status,jdbcType=INTEGER}
                </if>
                <if test="'description'.toString() == column.value">
                    #{item.description,jdbcType=VARCHAR}
                </if>
                <if test="'comment'.toString() == column.value">
                    #{item.comment,jdbcType=VARCHAR}
                </if>
                <if test="'machineIds'.toString() == column.value">
                    #{item.machineids,jdbcType=VARCHAR}
                </if>
                <if test="'ds'.toString() == column.value">
                    #{item.ds,jdbcType=TIMESTAMP}
                </if>
            </foreach>
            )
        </foreach>
    </insert>
    <insert id="upsertSelective" parameterType="com.aliyun.xdragon.common.generate.model.MonitorAnomalyAone">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into monitor_anomaly_aone
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="anomalyDesc != null">
                anomaly_desc,
            </if>
            <if test="anomalyType != null">
                anomaly_type,
            </if>
            <if test="criticalAttribute != null">
                critical_attribute,
            </if>
            <if test="detectionName != null">
                detection_name,
            </if>
            <if test="link != null">
                link,
            </if>
            <if test="monitorType != null">
                monitor_type,
            </if>
            <if test="monitorName != null">
                monitor_name,
            </if>
            <if test="owner != null">
                `owner`,
            </if>
            <if test="value != null">
                `value`,
            </if>
            <if test="aone != null">
                aone,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="comment != null">
                `comment`,
            </if>
            <if test="machineids != null">
                machineIds,
            </if>
            <if test="ds != null">
                ds,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="anomalyDesc != null">
                #{anomalyDesc,jdbcType=VARCHAR},
            </if>
            <if test="anomalyType != null">
                #{anomalyType,jdbcType=VARCHAR},
            </if>
            <if test="criticalAttribute != null">
                #{criticalAttribute,jdbcType=VARCHAR},
            </if>
            <if test="detectionName != null">
                #{detectionName,jdbcType=VARCHAR},
            </if>
            <if test="link != null">
                #{link,jdbcType=VARCHAR},
            </if>
            <if test="monitorType != null">
                #{monitorType,jdbcType=VARCHAR},
            </if>
            <if test="monitorName != null">
                #{monitorName,jdbcType=VARCHAR},
            </if>
            <if test="owner != null">
                #{owner,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                #{value,jdbcType=DOUBLE},
            </if>
            <if test="aone != null">
                #{aone,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="comment != null">
                #{comment,jdbcType=VARCHAR},
            </if>
            <if test="machineids != null">
                #{machineids,jdbcType=VARCHAR},
            </if>
            <if test="ds != null">
                #{ds,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=INTEGER},
            </if>
            <if test="anomalyDesc != null">
                anomaly_desc = #{anomalyDesc,jdbcType=VARCHAR},
            </if>
            <if test="anomalyType != null">
                anomaly_type = #{anomalyType,jdbcType=VARCHAR},
            </if>
            <if test="criticalAttribute != null">
                critical_attribute = #{criticalAttribute,jdbcType=VARCHAR},
            </if>
            <if test="detectionName != null">
                detection_name = #{detectionName,jdbcType=VARCHAR},
            </if>
            <if test="link != null">
                link = #{link,jdbcType=VARCHAR},
            </if>
            <if test="monitorType != null">
                monitor_type = #{monitorType,jdbcType=VARCHAR},
            </if>
            <if test="monitorName != null">
                monitor_name = #{monitorName,jdbcType=VARCHAR},
            </if>
            <if test="owner != null">
                `owner` = #{owner,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                `value` = #{value,jdbcType=DOUBLE},
            </if>
            <if test="aone != null">
                aone = #{aone,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="comment != null">
                `comment` = #{comment,jdbcType=VARCHAR},
            </if>
            <if test="machineids != null">
                machineIds = #{machineids,jdbcType=VARCHAR},
            </if>
            <if test="ds != null">
                ds = #{ds,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="upsert" parameterType="com.aliyun.xdragon.common.generate.model.MonitorAnomalyAone">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into monitor_anomaly_aone
        (id, anomaly_desc, anomaly_type, critical_attribute, detection_name, link, monitor_type,
        monitor_name, `owner`, `value`, aone, `status`, description, `comment`, machineIds,
        ds)
        values
        (#{id,jdbcType=INTEGER}, #{anomalyDesc,jdbcType=VARCHAR}, #{anomalyType,jdbcType=VARCHAR},
        #{criticalAttribute,jdbcType=VARCHAR}, #{detectionName,jdbcType=VARCHAR}, #{link,jdbcType=VARCHAR},
        #{monitorType,jdbcType=VARCHAR}, #{monitorName,jdbcType=VARCHAR}, #{owner,jdbcType=VARCHAR},
        #{value,jdbcType=DOUBLE}, #{aone,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
        #{description,jdbcType=VARCHAR}, #{comment,jdbcType=VARCHAR}, #{machineids,jdbcType=VARCHAR},
        #{ds,jdbcType=TIMESTAMP})
        on duplicate key update
        id = #{id,jdbcType=INTEGER},
        anomaly_desc = #{anomalyDesc,jdbcType=VARCHAR},
        anomaly_type = #{anomalyType,jdbcType=VARCHAR},
        critical_attribute = #{criticalAttribute,jdbcType=VARCHAR},
        detection_name = #{detectionName,jdbcType=VARCHAR},
        link = #{link,jdbcType=VARCHAR},
        monitor_type = #{monitorType,jdbcType=VARCHAR},
        monitor_name = #{monitorName,jdbcType=VARCHAR},
        `owner` = #{owner,jdbcType=VARCHAR},
        `value` = #{value,jdbcType=DOUBLE},
        aone = #{aone,jdbcType=VARCHAR},
        `status` = #{status,jdbcType=INTEGER},
        description = #{description,jdbcType=VARCHAR},
        `comment` = #{comment,jdbcType=VARCHAR},
        machineIds = #{machineids,jdbcType=VARCHAR},
        ds = #{ds,jdbcType=TIMESTAMP}
    </insert>
    <select id="selectOneByExample" parameterType="com.aliyun.xdragon.common.generate.model.MonitorAnomalyAoneExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from monitor_anomaly_aone
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        limit 1
    </select>
    <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <choose>
            <when test="selective != null and selective.length &gt; 0">
                <foreach collection="selective" item="column" separator=",">
                    ${column.aliasedEscapedColumnName}
                </foreach>
            </when>
            <otherwise>
                <include refid="Base_Column_List"/>
            </otherwise>
        </choose>
        from monitor_anomaly_aone
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
        <if test="example != null and example.orderByClause != null">
            order by ${example.orderByClause}
        </if>
        limit 1
    </select>
</mapper>