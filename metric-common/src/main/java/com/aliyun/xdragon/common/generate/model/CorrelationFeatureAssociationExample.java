package com.aliyun.xdragon.common.generate.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CorrelationFeatureAssociationExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    public CorrelationFeatureAssociationExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAntecedentIsNull() {
            addCriterion("antecedent is null");
            return (Criteria) this;
        }

        public Criteria andAntecedentIsNotNull() {
            addCriterion("antecedent is not null");
            return (Criteria) this;
        }

        public Criteria andAntecedentEqualTo(String value) {
            addCriterion("antecedent =", value, "antecedent");
            return (Criteria) this;
        }

        public Criteria andAntecedentNotEqualTo(String value) {
            addCriterion("antecedent <>", value, "antecedent");
            return (Criteria) this;
        }

        public Criteria andAntecedentGreaterThan(String value) {
            addCriterion("antecedent >", value, "antecedent");
            return (Criteria) this;
        }

        public Criteria andAntecedentGreaterThanOrEqualTo(String value) {
            addCriterion("antecedent >=", value, "antecedent");
            return (Criteria) this;
        }

        public Criteria andAntecedentLessThan(String value) {
            addCriterion("antecedent <", value, "antecedent");
            return (Criteria) this;
        }

        public Criteria andAntecedentLessThanOrEqualTo(String value) {
            addCriterion("antecedent <=", value, "antecedent");
            return (Criteria) this;
        }

        public Criteria andAntecedentLike(String value) {
            addCriterion("antecedent like", value, "antecedent");
            return (Criteria) this;
        }

        public Criteria andAntecedentNotLike(String value) {
            addCriterion("antecedent not like", value, "antecedent");
            return (Criteria) this;
        }

        public Criteria andAntecedentIn(List<String> values) {
            addCriterion("antecedent in", values, "antecedent");
            return (Criteria) this;
        }

        public Criteria andAntecedentNotIn(List<String> values) {
            addCriterion("antecedent not in", values, "antecedent");
            return (Criteria) this;
        }

        public Criteria andAntecedentBetween(String value1, String value2) {
            addCriterion("antecedent between", value1, value2, "antecedent");
            return (Criteria) this;
        }

        public Criteria andAntecedentNotBetween(String value1, String value2) {
            addCriterion("antecedent not between", value1, value2, "antecedent");
            return (Criteria) this;
        }

        public Criteria andConsequentIsNull() {
            addCriterion("consequent is null");
            return (Criteria) this;
        }

        public Criteria andConsequentIsNotNull() {
            addCriterion("consequent is not null");
            return (Criteria) this;
        }

        public Criteria andConsequentEqualTo(String value) {
            addCriterion("consequent =", value, "consequent");
            return (Criteria) this;
        }

        public Criteria andConsequentNotEqualTo(String value) {
            addCriterion("consequent <>", value, "consequent");
            return (Criteria) this;
        }

        public Criteria andConsequentGreaterThan(String value) {
            addCriterion("consequent >", value, "consequent");
            return (Criteria) this;
        }

        public Criteria andConsequentGreaterThanOrEqualTo(String value) {
            addCriterion("consequent >=", value, "consequent");
            return (Criteria) this;
        }

        public Criteria andConsequentLessThan(String value) {
            addCriterion("consequent <", value, "consequent");
            return (Criteria) this;
        }

        public Criteria andConsequentLessThanOrEqualTo(String value) {
            addCriterion("consequent <=", value, "consequent");
            return (Criteria) this;
        }

        public Criteria andConsequentLike(String value) {
            addCriterion("consequent like", value, "consequent");
            return (Criteria) this;
        }

        public Criteria andConsequentNotLike(String value) {
            addCriterion("consequent not like", value, "consequent");
            return (Criteria) this;
        }

        public Criteria andConsequentIn(List<String> values) {
            addCriterion("consequent in", values, "consequent");
            return (Criteria) this;
        }

        public Criteria andConsequentNotIn(List<String> values) {
            addCriterion("consequent not in", values, "consequent");
            return (Criteria) this;
        }

        public Criteria andConsequentBetween(String value1, String value2) {
            addCriterion("consequent between", value1, value2, "consequent");
            return (Criteria) this;
        }

        public Criteria andConsequentNotBetween(String value1, String value2) {
            addCriterion("consequent not between", value1, value2, "consequent");
            return (Criteria) this;
        }

        public Criteria andTransactionNumIsNull() {
            addCriterion("transaction_num is null");
            return (Criteria) this;
        }

        public Criteria andTransactionNumIsNotNull() {
            addCriterion("transaction_num is not null");
            return (Criteria) this;
        }

        public Criteria andTransactionNumEqualTo(Long value) {
            addCriterion("transaction_num =", value, "transactionNum");
            return (Criteria) this;
        }

        public Criteria andTransactionNumNotEqualTo(Long value) {
            addCriterion("transaction_num <>", value, "transactionNum");
            return (Criteria) this;
        }

        public Criteria andTransactionNumGreaterThan(Long value) {
            addCriterion("transaction_num >", value, "transactionNum");
            return (Criteria) this;
        }

        public Criteria andTransactionNumGreaterThanOrEqualTo(Long value) {
            addCriterion("transaction_num >=", value, "transactionNum");
            return (Criteria) this;
        }

        public Criteria andTransactionNumLessThan(Long value) {
            addCriterion("transaction_num <", value, "transactionNum");
            return (Criteria) this;
        }

        public Criteria andTransactionNumLessThanOrEqualTo(Long value) {
            addCriterion("transaction_num <=", value, "transactionNum");
            return (Criteria) this;
        }

        public Criteria andTransactionNumIn(List<Long> values) {
            addCriterion("transaction_num in", values, "transactionNum");
            return (Criteria) this;
        }

        public Criteria andTransactionNumNotIn(List<Long> values) {
            addCriterion("transaction_num not in", values, "transactionNum");
            return (Criteria) this;
        }

        public Criteria andTransactionNumBetween(Long value1, Long value2) {
            addCriterion("transaction_num between", value1, value2, "transactionNum");
            return (Criteria) this;
        }

        public Criteria andTransactionNumNotBetween(Long value1, Long value2) {
            addCriterion("transaction_num not between", value1, value2, "transactionNum");
            return (Criteria) this;
        }

        public Criteria andAntecedentNumIsNull() {
            addCriterion("antecedent_num is null");
            return (Criteria) this;
        }

        public Criteria andAntecedentNumIsNotNull() {
            addCriterion("antecedent_num is not null");
            return (Criteria) this;
        }

        public Criteria andAntecedentNumEqualTo(Long value) {
            addCriterion("antecedent_num =", value, "antecedentNum");
            return (Criteria) this;
        }

        public Criteria andAntecedentNumNotEqualTo(Long value) {
            addCriterion("antecedent_num <>", value, "antecedentNum");
            return (Criteria) this;
        }

        public Criteria andAntecedentNumGreaterThan(Long value) {
            addCriterion("antecedent_num >", value, "antecedentNum");
            return (Criteria) this;
        }

        public Criteria andAntecedentNumGreaterThanOrEqualTo(Long value) {
            addCriterion("antecedent_num >=", value, "antecedentNum");
            return (Criteria) this;
        }

        public Criteria andAntecedentNumLessThan(Long value) {
            addCriterion("antecedent_num <", value, "antecedentNum");
            return (Criteria) this;
        }

        public Criteria andAntecedentNumLessThanOrEqualTo(Long value) {
            addCriterion("antecedent_num <=", value, "antecedentNum");
            return (Criteria) this;
        }

        public Criteria andAntecedentNumIn(List<Long> values) {
            addCriterion("antecedent_num in", values, "antecedentNum");
            return (Criteria) this;
        }

        public Criteria andAntecedentNumNotIn(List<Long> values) {
            addCriterion("antecedent_num not in", values, "antecedentNum");
            return (Criteria) this;
        }

        public Criteria andAntecedentNumBetween(Long value1, Long value2) {
            addCriterion("antecedent_num between", value1, value2, "antecedentNum");
            return (Criteria) this;
        }

        public Criteria andAntecedentNumNotBetween(Long value1, Long value2) {
            addCriterion("antecedent_num not between", value1, value2, "antecedentNum");
            return (Criteria) this;
        }

        public Criteria andConsequentNumIsNull() {
            addCriterion("consequent_num is null");
            return (Criteria) this;
        }

        public Criteria andConsequentNumIsNotNull() {
            addCriterion("consequent_num is not null");
            return (Criteria) this;
        }

        public Criteria andConsequentNumEqualTo(Long value) {
            addCriterion("consequent_num =", value, "consequentNum");
            return (Criteria) this;
        }

        public Criteria andConsequentNumNotEqualTo(Long value) {
            addCriterion("consequent_num <>", value, "consequentNum");
            return (Criteria) this;
        }

        public Criteria andConsequentNumGreaterThan(Long value) {
            addCriterion("consequent_num >", value, "consequentNum");
            return (Criteria) this;
        }

        public Criteria andConsequentNumGreaterThanOrEqualTo(Long value) {
            addCriterion("consequent_num >=", value, "consequentNum");
            return (Criteria) this;
        }

        public Criteria andConsequentNumLessThan(Long value) {
            addCriterion("consequent_num <", value, "consequentNum");
            return (Criteria) this;
        }

        public Criteria andConsequentNumLessThanOrEqualTo(Long value) {
            addCriterion("consequent_num <=", value, "consequentNum");
            return (Criteria) this;
        }

        public Criteria andConsequentNumIn(List<Long> values) {
            addCriterion("consequent_num in", values, "consequentNum");
            return (Criteria) this;
        }

        public Criteria andConsequentNumNotIn(List<Long> values) {
            addCriterion("consequent_num not in", values, "consequentNum");
            return (Criteria) this;
        }

        public Criteria andConsequentNumBetween(Long value1, Long value2) {
            addCriterion("consequent_num between", value1, value2, "consequentNum");
            return (Criteria) this;
        }

        public Criteria andConsequentNumNotBetween(Long value1, Long value2) {
            addCriterion("consequent_num not between", value1, value2, "consequentNum");
            return (Criteria) this;
        }

        public Criteria andCoOccurrenceIsNull() {
            addCriterion("co_occurrence is null");
            return (Criteria) this;
        }

        public Criteria andCoOccurrenceIsNotNull() {
            addCriterion("co_occurrence is not null");
            return (Criteria) this;
        }

        public Criteria andCoOccurrenceEqualTo(Long value) {
            addCriterion("co_occurrence =", value, "coOccurrence");
            return (Criteria) this;
        }

        public Criteria andCoOccurrenceNotEqualTo(Long value) {
            addCriterion("co_occurrence <>", value, "coOccurrence");
            return (Criteria) this;
        }

        public Criteria andCoOccurrenceGreaterThan(Long value) {
            addCriterion("co_occurrence >", value, "coOccurrence");
            return (Criteria) this;
        }

        public Criteria andCoOccurrenceGreaterThanOrEqualTo(Long value) {
            addCriterion("co_occurrence >=", value, "coOccurrence");
            return (Criteria) this;
        }

        public Criteria andCoOccurrenceLessThan(Long value) {
            addCriterion("co_occurrence <", value, "coOccurrence");
            return (Criteria) this;
        }

        public Criteria andCoOccurrenceLessThanOrEqualTo(Long value) {
            addCriterion("co_occurrence <=", value, "coOccurrence");
            return (Criteria) this;
        }

        public Criteria andCoOccurrenceIn(List<Long> values) {
            addCriterion("co_occurrence in", values, "coOccurrence");
            return (Criteria) this;
        }

        public Criteria andCoOccurrenceNotIn(List<Long> values) {
            addCriterion("co_occurrence not in", values, "coOccurrence");
            return (Criteria) this;
        }

        public Criteria andCoOccurrenceBetween(Long value1, Long value2) {
            addCriterion("co_occurrence between", value1, value2, "coOccurrence");
            return (Criteria) this;
        }

        public Criteria andCoOccurrenceNotBetween(Long value1, Long value2) {
            addCriterion("co_occurrence not between", value1, value2, "coOccurrence");
            return (Criteria) this;
        }

        public Criteria andConfidenceIsNull() {
            addCriterion("confidence is null");
            return (Criteria) this;
        }

        public Criteria andConfidenceIsNotNull() {
            addCriterion("confidence is not null");
            return (Criteria) this;
        }

        public Criteria andConfidenceEqualTo(Double value) {
            addCriterion("confidence =", value, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceNotEqualTo(Double value) {
            addCriterion("confidence <>", value, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceGreaterThan(Double value) {
            addCriterion("confidence >", value, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceGreaterThanOrEqualTo(Double value) {
            addCriterion("confidence >=", value, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceLessThan(Double value) {
            addCriterion("confidence <", value, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceLessThanOrEqualTo(Double value) {
            addCriterion("confidence <=", value, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceIn(List<Double> values) {
            addCriterion("confidence in", values, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceNotIn(List<Double> values) {
            addCriterion("confidence not in", values, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceBetween(Double value1, Double value2) {
            addCriterion("confidence between", value1, value2, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceNotBetween(Double value1, Double value2) {
            addCriterion("confidence not between", value1, value2, "confidence");
            return (Criteria) this;
        }

        public Criteria andLiftIsNull() {
            addCriterion("lift is null");
            return (Criteria) this;
        }

        public Criteria andLiftIsNotNull() {
            addCriterion("lift is not null");
            return (Criteria) this;
        }

        public Criteria andLiftEqualTo(Double value) {
            addCriterion("lift =", value, "lift");
            return (Criteria) this;
        }

        public Criteria andLiftNotEqualTo(Double value) {
            addCriterion("lift <>", value, "lift");
            return (Criteria) this;
        }

        public Criteria andLiftGreaterThan(Double value) {
            addCriterion("lift >", value, "lift");
            return (Criteria) this;
        }

        public Criteria andLiftGreaterThanOrEqualTo(Double value) {
            addCriterion("lift >=", value, "lift");
            return (Criteria) this;
        }

        public Criteria andLiftLessThan(Double value) {
            addCriterion("lift <", value, "lift");
            return (Criteria) this;
        }

        public Criteria andLiftLessThanOrEqualTo(Double value) {
            addCriterion("lift <=", value, "lift");
            return (Criteria) this;
        }

        public Criteria andLiftIn(List<Double> values) {
            addCriterion("lift in", values, "lift");
            return (Criteria) this;
        }

        public Criteria andLiftNotIn(List<Double> values) {
            addCriterion("lift not in", values, "lift");
            return (Criteria) this;
        }

        public Criteria andLiftBetween(Double value1, Double value2) {
            addCriterion("lift between", value1, value2, "lift");
            return (Criteria) this;
        }

        public Criteria andLiftNotBetween(Double value1, Double value2) {
            addCriterion("lift not between", value1, value2, "lift");
            return (Criteria) this;
        }

        public Criteria andKulcIsNull() {
            addCriterion("kulc is null");
            return (Criteria) this;
        }

        public Criteria andKulcIsNotNull() {
            addCriterion("kulc is not null");
            return (Criteria) this;
        }

        public Criteria andKulcEqualTo(Double value) {
            addCriterion("kulc =", value, "kulc");
            return (Criteria) this;
        }

        public Criteria andKulcNotEqualTo(Double value) {
            addCriterion("kulc <>", value, "kulc");
            return (Criteria) this;
        }

        public Criteria andKulcGreaterThan(Double value) {
            addCriterion("kulc >", value, "kulc");
            return (Criteria) this;
        }

        public Criteria andKulcGreaterThanOrEqualTo(Double value) {
            addCriterion("kulc >=", value, "kulc");
            return (Criteria) this;
        }

        public Criteria andKulcLessThan(Double value) {
            addCriterion("kulc <", value, "kulc");
            return (Criteria) this;
        }

        public Criteria andKulcLessThanOrEqualTo(Double value) {
            addCriterion("kulc <=", value, "kulc");
            return (Criteria) this;
        }

        public Criteria andKulcIn(List<Double> values) {
            addCriterion("kulc in", values, "kulc");
            return (Criteria) this;
        }

        public Criteria andKulcNotIn(List<Double> values) {
            addCriterion("kulc not in", values, "kulc");
            return (Criteria) this;
        }

        public Criteria andKulcBetween(Double value1, Double value2) {
            addCriterion("kulc between", value1, value2, "kulc");
            return (Criteria) this;
        }

        public Criteria andKulcNotBetween(Double value1, Double value2) {
            addCriterion("kulc not between", value1, value2, "kulc");
            return (Criteria) this;
        }

        public Criteria andImbalanceRatioIsNull() {
            addCriterion("imbalance_ratio is null");
            return (Criteria) this;
        }

        public Criteria andImbalanceRatioIsNotNull() {
            addCriterion("imbalance_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andImbalanceRatioEqualTo(Double value) {
            addCriterion("imbalance_ratio =", value, "imbalanceRatio");
            return (Criteria) this;
        }

        public Criteria andImbalanceRatioNotEqualTo(Double value) {
            addCriterion("imbalance_ratio <>", value, "imbalanceRatio");
            return (Criteria) this;
        }

        public Criteria andImbalanceRatioGreaterThan(Double value) {
            addCriterion("imbalance_ratio >", value, "imbalanceRatio");
            return (Criteria) this;
        }

        public Criteria andImbalanceRatioGreaterThanOrEqualTo(Double value) {
            addCriterion("imbalance_ratio >=", value, "imbalanceRatio");
            return (Criteria) this;
        }

        public Criteria andImbalanceRatioLessThan(Double value) {
            addCriterion("imbalance_ratio <", value, "imbalanceRatio");
            return (Criteria) this;
        }

        public Criteria andImbalanceRatioLessThanOrEqualTo(Double value) {
            addCriterion("imbalance_ratio <=", value, "imbalanceRatio");
            return (Criteria) this;
        }

        public Criteria andImbalanceRatioIn(List<Double> values) {
            addCriterion("imbalance_ratio in", values, "imbalanceRatio");
            return (Criteria) this;
        }

        public Criteria andImbalanceRatioNotIn(List<Double> values) {
            addCriterion("imbalance_ratio not in", values, "imbalanceRatio");
            return (Criteria) this;
        }

        public Criteria andImbalanceRatioBetween(Double value1, Double value2) {
            addCriterion("imbalance_ratio between", value1, value2, "imbalanceRatio");
            return (Criteria) this;
        }

        public Criteria andImbalanceRatioNotBetween(Double value1, Double value2) {
            addCriterion("imbalance_ratio not between", value1, value2, "imbalanceRatio");
            return (Criteria) this;
        }

        public Criteria andPeriodIsNull() {
            addCriterion("period is null");
            return (Criteria) this;
        }

        public Criteria andPeriodIsNotNull() {
            addCriterion("period is not null");
            return (Criteria) this;
        }

        public Criteria andPeriodEqualTo(Integer value) {
            addCriterion("period =", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodNotEqualTo(Integer value) {
            addCriterion("period <>", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodGreaterThan(Integer value) {
            addCriterion("period >", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodGreaterThanOrEqualTo(Integer value) {
            addCriterion("period >=", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodLessThan(Integer value) {
            addCriterion("period <", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodLessThanOrEqualTo(Integer value) {
            addCriterion("period <=", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodIn(List<Integer> values) {
            addCriterion("period in", values, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodNotIn(List<Integer> values) {
            addCriterion("period not in", values, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodBetween(Integer value1, Integer value2) {
            addCriterion("period between", value1, value2, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodNotBetween(Integer value1, Integer value2) {
            addCriterion("period not between", value1, value2, "period");
            return (Criteria) this;
        }

        public Criteria andDsIsNull() {
            addCriterion("ds is null");
            return (Criteria) this;
        }

        public Criteria andDsIsNotNull() {
            addCriterion("ds is not null");
            return (Criteria) this;
        }

        public Criteria andDsEqualTo(String value) {
            addCriterion("ds =", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsNotEqualTo(String value) {
            addCriterion("ds <>", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsGreaterThan(String value) {
            addCriterion("ds >", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsGreaterThanOrEqualTo(String value) {
            addCriterion("ds >=", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsLessThan(String value) {
            addCriterion("ds <", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsLessThanOrEqualTo(String value) {
            addCriterion("ds <=", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsLike(String value) {
            addCriterion("ds like", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsNotLike(String value) {
            addCriterion("ds not like", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsIn(List<String> values) {
            addCriterion("ds in", values, "ds");
            return (Criteria) this;
        }

        public Criteria andDsNotIn(List<String> values) {
            addCriterion("ds not in", values, "ds");
            return (Criteria) this;
        }

        public Criteria andDsBetween(String value1, String value2) {
            addCriterion("ds between", value1, value2, "ds");
            return (Criteria) this;
        }

        public Criteria andDsNotBetween(String value1, String value2) {
            addCriterion("ds not between", value1, value2, "ds");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andProblemClassificationIsNull() {
            addCriterion("problem_classification is null");
            return (Criteria) this;
        }

        public Criteria andProblemClassificationIsNotNull() {
            addCriterion("problem_classification is not null");
            return (Criteria) this;
        }

        public Criteria andProblemClassificationEqualTo(String value) {
            addCriterion("problem_classification =", value, "problemClassification");
            return (Criteria) this;
        }

        public Criteria andProblemClassificationNotEqualTo(String value) {
            addCriterion("problem_classification <>", value, "problemClassification");
            return (Criteria) this;
        }

        public Criteria andProblemClassificationGreaterThan(String value) {
            addCriterion("problem_classification >", value, "problemClassification");
            return (Criteria) this;
        }

        public Criteria andProblemClassificationGreaterThanOrEqualTo(String value) {
            addCriterion("problem_classification >=", value, "problemClassification");
            return (Criteria) this;
        }

        public Criteria andProblemClassificationLessThan(String value) {
            addCriterion("problem_classification <", value, "problemClassification");
            return (Criteria) this;
        }

        public Criteria andProblemClassificationLessThanOrEqualTo(String value) {
            addCriterion("problem_classification <=", value, "problemClassification");
            return (Criteria) this;
        }

        public Criteria andProblemClassificationLike(String value) {
            addCriterion("problem_classification like", value, "problemClassification");
            return (Criteria) this;
        }

        public Criteria andProblemClassificationNotLike(String value) {
            addCriterion("problem_classification not like", value, "problemClassification");
            return (Criteria) this;
        }

        public Criteria andProblemClassificationIn(List<String> values) {
            addCriterion("problem_classification in", values, "problemClassification");
            return (Criteria) this;
        }

        public Criteria andProblemClassificationNotIn(List<String> values) {
            addCriterion("problem_classification not in", values, "problemClassification");
            return (Criteria) this;
        }

        public Criteria andProblemClassificationBetween(String value1, String value2) {
            addCriterion("problem_classification between", value1, value2, "problemClassification");
            return (Criteria) this;
        }

        public Criteria andProblemClassificationNotBetween(String value1, String value2) {
            addCriterion("problem_classification not between", value1, value2, "problemClassification");
            return (Criteria) this;
        }

        public Criteria andValidIsNull() {
            addCriterion("`valid` is null");
            return (Criteria) this;
        }

        public Criteria andValidIsNotNull() {
            addCriterion("`valid` is not null");
            return (Criteria) this;
        }

        public Criteria andValidEqualTo(Boolean value) {
            addCriterion("`valid` =", value, "valid");
            return (Criteria) this;
        }

        public Criteria andValidNotEqualTo(Boolean value) {
            addCriterion("`valid` <>", value, "valid");
            return (Criteria) this;
        }

        public Criteria andValidGreaterThan(Boolean value) {
            addCriterion("`valid` >", value, "valid");
            return (Criteria) this;
        }

        public Criteria andValidGreaterThanOrEqualTo(Boolean value) {
            addCriterion("`valid` >=", value, "valid");
            return (Criteria) this;
        }

        public Criteria andValidLessThan(Boolean value) {
            addCriterion("`valid` <", value, "valid");
            return (Criteria) this;
        }

        public Criteria andValidLessThanOrEqualTo(Boolean value) {
            addCriterion("`valid` <=", value, "valid");
            return (Criteria) this;
        }

        public Criteria andValidIn(List<Boolean> values) {
            addCriterion("`valid` in", values, "valid");
            return (Criteria) this;
        }

        public Criteria andValidNotIn(List<Boolean> values) {
            addCriterion("`valid` not in", values, "valid");
            return (Criteria) this;
        }

        public Criteria andValidBetween(Boolean value1, Boolean value2) {
            addCriterion("`valid` between", value1, value2, "valid");
            return (Criteria) this;
        }

        public Criteria andValidNotBetween(Boolean value1, Boolean value2) {
            addCriterion("`valid` not between", value1, value2, "valid");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table correlation_feature_association
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}