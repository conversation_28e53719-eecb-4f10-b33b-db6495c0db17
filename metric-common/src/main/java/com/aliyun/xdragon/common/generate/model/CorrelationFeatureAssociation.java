package com.aliyun.xdragon.common.generate.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class CorrelationFeatureAssociation implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.antecedent
     *
     * @mbg.generated
     */
    private String antecedent;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.consequent
     *
     * @mbg.generated
     */
    private String consequent;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.transaction_num
     *
     * @mbg.generated
     */
    private Long transactionNum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.antecedent_num
     *
     * @mbg.generated
     */
    private Long antecedentNum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.consequent_num
     *
     * @mbg.generated
     */
    private Long consequentNum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.co_occurrence
     *
     * @mbg.generated
     */
    private Long coOccurrence;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.confidence
     *
     * @mbg.generated
     */
    private Double confidence;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.lift
     *
     * @mbg.generated
     */
    private Double lift;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.kulc
     *
     * @mbg.generated
     */
    private Double kulc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.imbalance_ratio
     *
     * @mbg.generated
     */
    private Double imbalanceRatio;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.period
     *
     * @mbg.generated
     */
    private Integer period;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.ds
     *
     * @mbg.generated
     */
    private String ds;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.problem_classification
     *
     * @mbg.generated
     */
    private String problemClassification;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column correlation_feature_association.valid
     *
     * @mbg.generated
     */
    private Boolean valid;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.id
     *
     * @return the value of correlation_feature_association.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.id
     *
     * @param id the value for correlation_feature_association.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.antecedent
     *
     * @return the value of correlation_feature_association.antecedent
     *
     * @mbg.generated
     */
    public String getAntecedent() {
        return antecedent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.antecedent
     *
     * @param antecedent the value for correlation_feature_association.antecedent
     *
     * @mbg.generated
     */
    public void setAntecedent(String antecedent) {
        this.antecedent = antecedent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.consequent
     *
     * @return the value of correlation_feature_association.consequent
     *
     * @mbg.generated
     */
    public String getConsequent() {
        return consequent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.consequent
     *
     * @param consequent the value for correlation_feature_association.consequent
     *
     * @mbg.generated
     */
    public void setConsequent(String consequent) {
        this.consequent = consequent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.transaction_num
     *
     * @return the value of correlation_feature_association.transaction_num
     *
     * @mbg.generated
     */
    public Long getTransactionNum() {
        return transactionNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.transaction_num
     *
     * @param transactionNum the value for correlation_feature_association.transaction_num
     *
     * @mbg.generated
     */
    public void setTransactionNum(Long transactionNum) {
        this.transactionNum = transactionNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.antecedent_num
     *
     * @return the value of correlation_feature_association.antecedent_num
     *
     * @mbg.generated
     */
    public Long getAntecedentNum() {
        return antecedentNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.antecedent_num
     *
     * @param antecedentNum the value for correlation_feature_association.antecedent_num
     *
     * @mbg.generated
     */
    public void setAntecedentNum(Long antecedentNum) {
        this.antecedentNum = antecedentNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.consequent_num
     *
     * @return the value of correlation_feature_association.consequent_num
     *
     * @mbg.generated
     */
    public Long getConsequentNum() {
        return consequentNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.consequent_num
     *
     * @param consequentNum the value for correlation_feature_association.consequent_num
     *
     * @mbg.generated
     */
    public void setConsequentNum(Long consequentNum) {
        this.consequentNum = consequentNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.co_occurrence
     *
     * @return the value of correlation_feature_association.co_occurrence
     *
     * @mbg.generated
     */
    public Long getCoOccurrence() {
        return coOccurrence;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.co_occurrence
     *
     * @param coOccurrence the value for correlation_feature_association.co_occurrence
     *
     * @mbg.generated
     */
    public void setCoOccurrence(Long coOccurrence) {
        this.coOccurrence = coOccurrence;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.confidence
     *
     * @return the value of correlation_feature_association.confidence
     *
     * @mbg.generated
     */
    public Double getConfidence() {
        return confidence;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.confidence
     *
     * @param confidence the value for correlation_feature_association.confidence
     *
     * @mbg.generated
     */
    public void setConfidence(Double confidence) {
        this.confidence = confidence;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.lift
     *
     * @return the value of correlation_feature_association.lift
     *
     * @mbg.generated
     */
    public Double getLift() {
        return lift;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.lift
     *
     * @param lift the value for correlation_feature_association.lift
     *
     * @mbg.generated
     */
    public void setLift(Double lift) {
        this.lift = lift;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.kulc
     *
     * @return the value of correlation_feature_association.kulc
     *
     * @mbg.generated
     */
    public Double getKulc() {
        return kulc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.kulc
     *
     * @param kulc the value for correlation_feature_association.kulc
     *
     * @mbg.generated
     */
    public void setKulc(Double kulc) {
        this.kulc = kulc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.imbalance_ratio
     *
     * @return the value of correlation_feature_association.imbalance_ratio
     *
     * @mbg.generated
     */
    public Double getImbalanceRatio() {
        return imbalanceRatio;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.imbalance_ratio
     *
     * @param imbalanceRatio the value for correlation_feature_association.imbalance_ratio
     *
     * @mbg.generated
     */
    public void setImbalanceRatio(Double imbalanceRatio) {
        this.imbalanceRatio = imbalanceRatio;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.period
     *
     * @return the value of correlation_feature_association.period
     *
     * @mbg.generated
     */
    public Integer getPeriod() {
        return period;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.period
     *
     * @param period the value for correlation_feature_association.period
     *
     * @mbg.generated
     */
    public void setPeriod(Integer period) {
        this.period = period;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.ds
     *
     * @return the value of correlation_feature_association.ds
     *
     * @mbg.generated
     */
    public String getDs() {
        return ds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.ds
     *
     * @param ds the value for correlation_feature_association.ds
     *
     * @mbg.generated
     */
    public void setDs(String ds) {
        this.ds = ds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.gmt_create
     *
     * @return the value of correlation_feature_association.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.gmt_create
     *
     * @param gmtCreate the value for correlation_feature_association.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.gmt_modified
     *
     * @return the value of correlation_feature_association.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.gmt_modified
     *
     * @param gmtModified the value for correlation_feature_association.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.problem_classification
     *
     * @return the value of correlation_feature_association.problem_classification
     *
     * @mbg.generated
     */
    public String getProblemClassification() {
        return problemClassification;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.problem_classification
     *
     * @param problemClassification the value for correlation_feature_association.problem_classification
     *
     * @mbg.generated
     */
    public void setProblemClassification(String problemClassification) {
        this.problemClassification = problemClassification;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column correlation_feature_association.valid
     *
     * @return the value of correlation_feature_association.valid
     *
     * @mbg.generated
     */
    public Boolean getValid() {
        return valid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column correlation_feature_association.valid
     *
     * @param valid the value for correlation_feature_association.valid
     *
     * @mbg.generated
     */
    public void setValid(Boolean valid) {
        this.valid = valid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CorrelationFeatureAssociation other = (CorrelationFeatureAssociation) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAntecedent() == null ? other.getAntecedent() == null : this.getAntecedent().equals(other.getAntecedent()))
            && (this.getConsequent() == null ? other.getConsequent() == null : this.getConsequent().equals(other.getConsequent()))
            && (this.getTransactionNum() == null ? other.getTransactionNum() == null : this.getTransactionNum().equals(other.getTransactionNum()))
            && (this.getAntecedentNum() == null ? other.getAntecedentNum() == null : this.getAntecedentNum().equals(other.getAntecedentNum()))
            && (this.getConsequentNum() == null ? other.getConsequentNum() == null : this.getConsequentNum().equals(other.getConsequentNum()))
            && (this.getCoOccurrence() == null ? other.getCoOccurrence() == null : this.getCoOccurrence().equals(other.getCoOccurrence()))
            && (this.getConfidence() == null ? other.getConfidence() == null : this.getConfidence().equals(other.getConfidence()))
            && (this.getLift() == null ? other.getLift() == null : this.getLift().equals(other.getLift()))
            && (this.getKulc() == null ? other.getKulc() == null : this.getKulc().equals(other.getKulc()))
            && (this.getImbalanceRatio() == null ? other.getImbalanceRatio() == null : this.getImbalanceRatio().equals(other.getImbalanceRatio()))
            && (this.getPeriod() == null ? other.getPeriod() == null : this.getPeriod().equals(other.getPeriod()))
            && (this.getDs() == null ? other.getDs() == null : this.getDs().equals(other.getDs()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtModified() == null ? other.getGmtModified() == null : this.getGmtModified().equals(other.getGmtModified()))
            && (this.getProblemClassification() == null ? other.getProblemClassification() == null : this.getProblemClassification().equals(other.getProblemClassification()))
            && (this.getValid() == null ? other.getValid() == null : this.getValid().equals(other.getValid()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAntecedent() == null) ? 0 : getAntecedent().hashCode());
        result = prime * result + ((getConsequent() == null) ? 0 : getConsequent().hashCode());
        result = prime * result + ((getTransactionNum() == null) ? 0 : getTransactionNum().hashCode());
        result = prime * result + ((getAntecedentNum() == null) ? 0 : getAntecedentNum().hashCode());
        result = prime * result + ((getConsequentNum() == null) ? 0 : getConsequentNum().hashCode());
        result = prime * result + ((getCoOccurrence() == null) ? 0 : getCoOccurrence().hashCode());
        result = prime * result + ((getConfidence() == null) ? 0 : getConfidence().hashCode());
        result = prime * result + ((getLift() == null) ? 0 : getLift().hashCode());
        result = prime * result + ((getKulc() == null) ? 0 : getKulc().hashCode());
        result = prime * result + ((getImbalanceRatio() == null) ? 0 : getImbalanceRatio().hashCode());
        result = prime * result + ((getPeriod() == null) ? 0 : getPeriod().hashCode());
        result = prime * result + ((getDs() == null) ? 0 : getDs().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtModified() == null) ? 0 : getGmtModified().hashCode());
        result = prime * result + ((getProblemClassification() == null) ? 0 : getProblemClassification().hashCode());
        result = prime * result + ((getValid() == null) ? 0 : getValid().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", antecedent=").append(antecedent);
        sb.append(", consequent=").append(consequent);
        sb.append(", transactionNum=").append(transactionNum);
        sb.append(", antecedentNum=").append(antecedentNum);
        sb.append(", consequentNum=").append(consequentNum);
        sb.append(", coOccurrence=").append(coOccurrence);
        sb.append(", confidence=").append(confidence);
        sb.append(", lift=").append(lift);
        sb.append(", kulc=").append(kulc);
        sb.append(", imbalanceRatio=").append(imbalanceRatio);
        sb.append(", period=").append(period);
        sb.append(", ds=").append(ds);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", problemClassification=").append(problemClassification);
        sb.append(", valid=").append(valid);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table correlation_feature_association
     *
     * @mbg.generated
     */
    public enum Column {
        id("id", "id", "BIGINT", false),
        antecedent("antecedent", "antecedent", "VARCHAR", false),
        consequent("consequent", "consequent", "VARCHAR", false),
        transactionNum("transaction_num", "transactionNum", "BIGINT", false),
        antecedentNum("antecedent_num", "antecedentNum", "BIGINT", false),
        consequentNum("consequent_num", "consequentNum", "BIGINT", false),
        coOccurrence("co_occurrence", "coOccurrence", "BIGINT", false),
        confidence("confidence", "confidence", "DOUBLE", false),
        lift("lift", "lift", "DOUBLE", false),
        kulc("kulc", "kulc", "DOUBLE", false),
        imbalanceRatio("imbalance_ratio", "imbalanceRatio", "DOUBLE", false),
        period("period", "period", "INTEGER", false),
        ds("ds", "ds", "VARCHAR", false),
        gmtCreate("gmt_create", "gmtCreate", "TIMESTAMP", false),
        gmtModified("gmt_modified", "gmtModified", "TIMESTAMP", false),
        problemClassification("problem_classification", "problemClassification", "VARCHAR", false),
        valid("valid", "valid", "BIT", true);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table correlation_feature_association
         *
         * @mbg.generated
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}