package com.aliyun.xdragon.common.generate.log.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class ClusterDistributionDetail implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cluster_distribution_detail.time
     *
     * @mbg.generated
     */
    private Date time;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cluster_distribution_detail.task_id
     *
     * @mbg.generated
     */
    private Long taskId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cluster_distribution_detail.region
     *
     * @mbg.generated
     */
    private String region;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cluster_distribution_detail.logstore
     *
     * @mbg.generated
     */
    private String logstore;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cluster_distribution_detail.hashcode
     *
     * @mbg.generated
     */
    private Long hashcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cluster_distribution_detail.support
     *
     * @mbg.generated
     */
    private Long support;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cluster_distribution_detail.type_id
     *
     * @mbg.generated
     */
    private Long typeId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cluster_distribution_detail.detail_key
     *
     * @mbg.generated
     */
    private String detailKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cluster_distribution_detail.detail_support
     *
     * @mbg.generated
     */
    private Long detailSupport;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cluster_distribution_detail.md5
     *
     * @mbg.generated
     */
    private String md5;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cluster_distribution_detail.sls_source
     *
     * @mbg.generated
     */
    private String slsSource;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cluster_distribution_detail
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cluster_distribution_detail.time
     *
     * @return the value of cluster_distribution_detail.time
     *
     * @mbg.generated
     */
    public Date getTime() {
        return time;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cluster_distribution_detail.time
     *
     * @param time the value for cluster_distribution_detail.time
     *
     * @mbg.generated
     */
    public void setTime(Date time) {
        this.time = time;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cluster_distribution_detail.task_id
     *
     * @return the value of cluster_distribution_detail.task_id
     *
     * @mbg.generated
     */
    public Long getTaskId() {
        return taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cluster_distribution_detail.task_id
     *
     * @param taskId the value for cluster_distribution_detail.task_id
     *
     * @mbg.generated
     */
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cluster_distribution_detail.region
     *
     * @return the value of cluster_distribution_detail.region
     *
     * @mbg.generated
     */
    public String getRegion() {
        return region;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cluster_distribution_detail.region
     *
     * @param region the value for cluster_distribution_detail.region
     *
     * @mbg.generated
     */
    public void setRegion(String region) {
        this.region = region;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cluster_distribution_detail.logstore
     *
     * @return the value of cluster_distribution_detail.logstore
     *
     * @mbg.generated
     */
    public String getLogstore() {
        return logstore;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cluster_distribution_detail.logstore
     *
     * @param logstore the value for cluster_distribution_detail.logstore
     *
     * @mbg.generated
     */
    public void setLogstore(String logstore) {
        this.logstore = logstore;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cluster_distribution_detail.hashcode
     *
     * @return the value of cluster_distribution_detail.hashcode
     *
     * @mbg.generated
     */
    public Long getHashcode() {
        return hashcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cluster_distribution_detail.hashcode
     *
     * @param hashcode the value for cluster_distribution_detail.hashcode
     *
     * @mbg.generated
     */
    public void setHashcode(Long hashcode) {
        this.hashcode = hashcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cluster_distribution_detail.support
     *
     * @return the value of cluster_distribution_detail.support
     *
     * @mbg.generated
     */
    public Long getSupport() {
        return support;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cluster_distribution_detail.support
     *
     * @param support the value for cluster_distribution_detail.support
     *
     * @mbg.generated
     */
    public void setSupport(Long support) {
        this.support = support;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cluster_distribution_detail.type_id
     *
     * @return the value of cluster_distribution_detail.type_id
     *
     * @mbg.generated
     */
    public Long getTypeId() {
        return typeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cluster_distribution_detail.type_id
     *
     * @param typeId the value for cluster_distribution_detail.type_id
     *
     * @mbg.generated
     */
    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cluster_distribution_detail.detail_key
     *
     * @return the value of cluster_distribution_detail.detail_key
     *
     * @mbg.generated
     */
    public String getDetailKey() {
        return detailKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cluster_distribution_detail.detail_key
     *
     * @param detailKey the value for cluster_distribution_detail.detail_key
     *
     * @mbg.generated
     */
    public void setDetailKey(String detailKey) {
        this.detailKey = detailKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cluster_distribution_detail.detail_support
     *
     * @return the value of cluster_distribution_detail.detail_support
     *
     * @mbg.generated
     */
    public Long getDetailSupport() {
        return detailSupport;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cluster_distribution_detail.detail_support
     *
     * @param detailSupport the value for cluster_distribution_detail.detail_support
     *
     * @mbg.generated
     */
    public void setDetailSupport(Long detailSupport) {
        this.detailSupport = detailSupport;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cluster_distribution_detail.md5
     *
     * @return the value of cluster_distribution_detail.md5
     *
     * @mbg.generated
     */
    public String getMd5() {
        return md5;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cluster_distribution_detail.md5
     *
     * @param md5 the value for cluster_distribution_detail.md5
     *
     * @mbg.generated
     */
    public void setMd5(String md5) {
        this.md5 = md5;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cluster_distribution_detail.sls_source
     *
     * @return the value of cluster_distribution_detail.sls_source
     *
     * @mbg.generated
     */
    public String getSlsSource() {
        return slsSource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cluster_distribution_detail.sls_source
     *
     * @param slsSource the value for cluster_distribution_detail.sls_source
     *
     * @mbg.generated
     */
    public void setSlsSource(String slsSource) {
        this.slsSource = slsSource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cluster_distribution_detail
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ClusterDistributionDetail other = (ClusterDistributionDetail) that;
        return (this.getTime() == null ? other.getTime() == null : this.getTime().equals(other.getTime()))
            && (this.getTaskId() == null ? other.getTaskId() == null : this.getTaskId().equals(other.getTaskId()))
            && (this.getRegion() == null ? other.getRegion() == null : this.getRegion().equals(other.getRegion()))
            && (this.getLogstore() == null ? other.getLogstore() == null : this.getLogstore().equals(other.getLogstore()))
            && (this.getHashcode() == null ? other.getHashcode() == null : this.getHashcode().equals(other.getHashcode()))
            && (this.getSupport() == null ? other.getSupport() == null : this.getSupport().equals(other.getSupport()))
            && (this.getTypeId() == null ? other.getTypeId() == null : this.getTypeId().equals(other.getTypeId()))
            && (this.getDetailKey() == null ? other.getDetailKey() == null : this.getDetailKey().equals(other.getDetailKey()))
            && (this.getDetailSupport() == null ? other.getDetailSupport() == null : this.getDetailSupport().equals(other.getDetailSupport()))
            && (this.getMd5() == null ? other.getMd5() == null : this.getMd5().equals(other.getMd5()))
            && (this.getSlsSource() == null ? other.getSlsSource() == null : this.getSlsSource().equals(other.getSlsSource()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cluster_distribution_detail
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getTime() == null) ? 0 : getTime().hashCode());
        result = prime * result + ((getTaskId() == null) ? 0 : getTaskId().hashCode());
        result = prime * result + ((getRegion() == null) ? 0 : getRegion().hashCode());
        result = prime * result + ((getLogstore() == null) ? 0 : getLogstore().hashCode());
        result = prime * result + ((getHashcode() == null) ? 0 : getHashcode().hashCode());
        result = prime * result + ((getSupport() == null) ? 0 : getSupport().hashCode());
        result = prime * result + ((getTypeId() == null) ? 0 : getTypeId().hashCode());
        result = prime * result + ((getDetailKey() == null) ? 0 : getDetailKey().hashCode());
        result = prime * result + ((getDetailSupport() == null) ? 0 : getDetailSupport().hashCode());
        result = prime * result + ((getMd5() == null) ? 0 : getMd5().hashCode());
        result = prime * result + ((getSlsSource() == null) ? 0 : getSlsSource().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cluster_distribution_detail
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", time=").append(time);
        sb.append(", taskId=").append(taskId);
        sb.append(", region=").append(region);
        sb.append(", logstore=").append(logstore);
        sb.append(", hashcode=").append(hashcode);
        sb.append(", support=").append(support);
        sb.append(", typeId=").append(typeId);
        sb.append(", detailKey=").append(detailKey);
        sb.append(", detailSupport=").append(detailSupport);
        sb.append(", md5=").append(md5);
        sb.append(", slsSource=").append(slsSource);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table cluster_distribution_detail
     *
     * @mbg.generated
     */
    public enum Column {
        time("time", "time", "TIMESTAMP", true),
        taskId("task_id", "taskId", "BIGINT", false),
        region("region", "region", "VARCHAR", false),
        logstore("logstore", "logstore", "VARCHAR", false),
        hashcode("hashcode", "hashcode", "BIGINT", false),
        support("support", "support", "BIGINT", false),
        typeId("type_id", "typeId", "BIGINT", false),
        detailKey("detail_key", "detailKey", "VARCHAR", false),
        detailSupport("detail_support", "detailSupport", "BIGINT", false),
        md5("md5", "md5", "VARCHAR", false),
        slsSource("sls_source", "slsSource", "VARCHAR", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table cluster_distribution_detail
         *
         * @mbg.generated
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}