package com.aliyun.xdragon.common.model.log;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import org.apache.commons.lang3.tuple.Pair;

/**
 * <AUTHOR>
 * @date 2022/09/21
 */
public class LogClusterCountSeq implements Serializable {
    private Long taskId;
    private Long windowSize;
    private String region;
    private LinkedList<Pair<Long, Long>> detail;
    private static final long serialVersionUID = -512934405013159940L;

    public LogClusterCountSeq(Long taskId, Long windowSize, String region, LinkedList<Pair<Long, Long>> detail) {
        this.taskId = taskId;
        this.windowSize = windowSize;
        this.region = region;
        this.detail = fillZeroForDetail(detail);
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getWindowSize() {
        return windowSize;
    }

    public void setWindowSize(Long windowSize) {
        this.windowSize = windowSize;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public List<Pair<Long, Long>> getDetail() {
        return detail;
    }

    public void setDetail(LinkedList<Pair<Long, Long>> detail) {
        this.detail = detail;
    }

    public void addDetailItem(Pair<Long, Long> item) {
        if (detail == null) {
            detail = new LinkedList<>();
        }
        if (detail.isEmpty()) {
            detail.add(item);
            return;
        }
        if (windowSize == null || windowSize <= 0) {
            throw new RuntimeException("Invalid windowSize with add LogClusterCountSeq detail Item!");
        }
        Long nextTimestamp = detail.getLast().getKey() + windowSize * 60L * 1000L;
        while (nextTimestamp < item.getKey()) {
            detail.add(Pair.of(nextTimestamp, 0L));
            nextTimestamp += windowSize * 60L * 1000L;
        }
        detail.add(item);
    }

    private LinkedList<Pair<Long, Long>> fillZeroForDetail(LinkedList<Pair<Long, Long>> detail) {
        if (detail == null || detail.size() <= 1) {
            return detail;
        }

        LinkedList<Pair<Long, Long>> filledDetail = new LinkedList<>();
        Long curTimestamp = detail.get(0).getKey();
        Long lastTimestamp = detail.get(detail.size() - 1).getKey();
        int index = 0;
        while (curTimestamp <= lastTimestamp) {
            filledDetail.add(detail.get(index).getKey() > curTimestamp ? Pair.of(curTimestamp, 0L) : detail.get(index++));
            curTimestamp += windowSize * 60L * 1000L;
        }
        return filledDetail;
    }
}
