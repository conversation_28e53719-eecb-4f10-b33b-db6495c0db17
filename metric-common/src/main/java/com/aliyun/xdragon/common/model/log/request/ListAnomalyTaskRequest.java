package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/2/8
 */
@Data
public class ListAnomalyTaskRequest implements Serializable {
    private static final long serialVersionUID = 2181538937767358957L;

    /**
     * ip
     */
    @NotNull
    String ip;

    /**
     * start time in ms
     */
    @NotNull
    Long startMs;

    /**
     * end time in ms
     */
    @NotNull
    Long endMs;
}
