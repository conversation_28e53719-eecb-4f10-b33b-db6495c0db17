package com.aliyun.xdragon.common.generate.model;

import java.util.ArrayList;
import java.util.List;

public class WorkItemLabelDetailExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    public WorkItemLabelDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andWorkItemIdIsNull() {
            addCriterion("work_item_id is null");
            return (Criteria) this;
        }

        public Criteria andWorkItemIdIsNotNull() {
            addCriterion("work_item_id is not null");
            return (Criteria) this;
        }

        public Criteria andWorkItemIdEqualTo(Long value) {
            addCriterion("work_item_id =", value, "workItemId");
            return (Criteria) this;
        }

        public Criteria andWorkItemIdNotEqualTo(Long value) {
            addCriterion("work_item_id <>", value, "workItemId");
            return (Criteria) this;
        }

        public Criteria andWorkItemIdGreaterThan(Long value) {
            addCriterion("work_item_id >", value, "workItemId");
            return (Criteria) this;
        }

        public Criteria andWorkItemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("work_item_id >=", value, "workItemId");
            return (Criteria) this;
        }

        public Criteria andWorkItemIdLessThan(Long value) {
            addCriterion("work_item_id <", value, "workItemId");
            return (Criteria) this;
        }

        public Criteria andWorkItemIdLessThanOrEqualTo(Long value) {
            addCriterion("work_item_id <=", value, "workItemId");
            return (Criteria) this;
        }

        public Criteria andWorkItemIdIn(List<Long> values) {
            addCriterion("work_item_id in", values, "workItemId");
            return (Criteria) this;
        }

        public Criteria andWorkItemIdNotIn(List<Long> values) {
            addCriterion("work_item_id not in", values, "workItemId");
            return (Criteria) this;
        }

        public Criteria andWorkItemIdBetween(Long value1, Long value2) {
            addCriterion("work_item_id between", value1, value2, "workItemId");
            return (Criteria) this;
        }

        public Criteria andWorkItemIdNotBetween(Long value1, Long value2) {
            addCriterion("work_item_id not between", value1, value2, "workItemId");
            return (Criteria) this;
        }

        public Criteria andLabelIdIsNull() {
            addCriterion("label_id is null");
            return (Criteria) this;
        }

        public Criteria andLabelIdIsNotNull() {
            addCriterion("label_id is not null");
            return (Criteria) this;
        }

        public Criteria andLabelIdEqualTo(Long value) {
            addCriterion("label_id =", value, "labelId");
            return (Criteria) this;
        }

        public Criteria andLabelIdNotEqualTo(Long value) {
            addCriterion("label_id <>", value, "labelId");
            return (Criteria) this;
        }

        public Criteria andLabelIdGreaterThan(Long value) {
            addCriterion("label_id >", value, "labelId");
            return (Criteria) this;
        }

        public Criteria andLabelIdGreaterThanOrEqualTo(Long value) {
            addCriterion("label_id >=", value, "labelId");
            return (Criteria) this;
        }

        public Criteria andLabelIdLessThan(Long value) {
            addCriterion("label_id <", value, "labelId");
            return (Criteria) this;
        }

        public Criteria andLabelIdLessThanOrEqualTo(Long value) {
            addCriterion("label_id <=", value, "labelId");
            return (Criteria) this;
        }

        public Criteria andLabelIdIn(List<Long> values) {
            addCriterion("label_id in", values, "labelId");
            return (Criteria) this;
        }

        public Criteria andLabelIdNotIn(List<Long> values) {
            addCriterion("label_id not in", values, "labelId");
            return (Criteria) this;
        }

        public Criteria andLabelIdBetween(Long value1, Long value2) {
            addCriterion("label_id between", value1, value2, "labelId");
            return (Criteria) this;
        }

        public Criteria andLabelIdNotBetween(Long value1, Long value2) {
            addCriterion("label_id not between", value1, value2, "labelId");
            return (Criteria) this;
        }

        public Criteria andTagLevelIsNull() {
            addCriterion("tag_level is null");
            return (Criteria) this;
        }

        public Criteria andTagLevelIsNotNull() {
            addCriterion("tag_level is not null");
            return (Criteria) this;
        }

        public Criteria andTagLevelEqualTo(Byte value) {
            addCriterion("tag_level =", value, "tagLevel");
            return (Criteria) this;
        }

        public Criteria andTagLevelNotEqualTo(Byte value) {
            addCriterion("tag_level <>", value, "tagLevel");
            return (Criteria) this;
        }

        public Criteria andTagLevelGreaterThan(Byte value) {
            addCriterion("tag_level >", value, "tagLevel");
            return (Criteria) this;
        }

        public Criteria andTagLevelGreaterThanOrEqualTo(Byte value) {
            addCriterion("tag_level >=", value, "tagLevel");
            return (Criteria) this;
        }

        public Criteria andTagLevelLessThan(Byte value) {
            addCriterion("tag_level <", value, "tagLevel");
            return (Criteria) this;
        }

        public Criteria andTagLevelLessThanOrEqualTo(Byte value) {
            addCriterion("tag_level <=", value, "tagLevel");
            return (Criteria) this;
        }

        public Criteria andTagLevelIn(List<Byte> values) {
            addCriterion("tag_level in", values, "tagLevel");
            return (Criteria) this;
        }

        public Criteria andTagLevelNotIn(List<Byte> values) {
            addCriterion("tag_level not in", values, "tagLevel");
            return (Criteria) this;
        }

        public Criteria andTagLevelBetween(Byte value1, Byte value2) {
            addCriterion("tag_level between", value1, value2, "tagLevel");
            return (Criteria) this;
        }

        public Criteria andTagLevelNotBetween(Byte value1, Byte value2) {
            addCriterion("tag_level not between", value1, value2, "tagLevel");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table work_item_label_detail
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table work_item_label_detail
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}