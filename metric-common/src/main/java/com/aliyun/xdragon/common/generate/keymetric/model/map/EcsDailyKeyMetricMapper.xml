<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.keymetric.model.map.EcsDailyKeyMetricMapper">
    <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.keymetric.model.EcsDailyKeyMetric">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <result column="unavailable_time" jdbcType="DOUBLE" property="unavailableTime"/>
        <result column="performance_time" jdbcType="DOUBLE" property="performanceTime"/>
        <result column="control_time" jdbcType="DOUBLE" property="controlTime"/>
        <result column="damaged_vm" jdbcType="BIGINT" property="damagedVm"/>
        <result column="total_vm" jdbcType="BIGINT" property="totalVm"/>
        <result column="total_life_time" jdbcType="BIGINT" property="totalLifeTime"/>
        <result column="ds" jdbcType="VARCHAR" property="ds"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        unavailable_time, performance_time, control_time, damaged_vm, total_vm, total_life_time,
        ds
    </sql>
    <select id="selectByExample"
            parameterType="com.aliyun.xdragon.common.generate.keymetric.model.EcsDailyKeyMetricExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from ecs_daily_key_metric
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="example != null and example.distinct">
            distinct
        </if>
        <choose>
            <when test="selective != null and selective.length > 0">
                <foreach collection="selective" item="column" separator=",">
                    ${column.aliasedEscapedColumnName}
                </foreach>
            </when>
            <otherwise>
                <include refid="Base_Column_List"/>
            </otherwise>
        </choose>
        from ecs_daily_key_metric
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
        <if test="example != null and example.orderByClause != null">
            order by ${example.orderByClause}
        </if>
    </select>
    <delete id="deleteByExample"
            parameterType="com.aliyun.xdragon.common.generate.keymetric.model.EcsDailyKeyMetricExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from ecs_daily_key_metric
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.keymetric.model.EcsDailyKeyMetric">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into ecs_daily_key_metric (unavailable_time, performance_time, control_time,
        damaged_vm, total_vm, total_life_time,
        ds)
        values (#{unavailableTime,jdbcType=DOUBLE}, #{performanceTime,jdbcType=DOUBLE}, #{controlTime,jdbcType=DOUBLE},
        #{damagedVm,jdbcType=BIGINT}, #{totalVm,jdbcType=BIGINT}, #{totalLifeTime,jdbcType=BIGINT},
        #{ds,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.keymetric.model.EcsDailyKeyMetric">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into ecs_daily_key_metric
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unavailableTime != null">
                unavailable_time,
            </if>
            <if test="performanceTime != null">
                performance_time,
            </if>
            <if test="controlTime != null">
                control_time,
            </if>
            <if test="damagedVm != null">
                damaged_vm,
            </if>
            <if test="totalVm != null">
                total_vm,
            </if>
            <if test="totalLifeTime != null">
                total_life_time,
            </if>
            <if test="ds != null">
                ds,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="unavailableTime != null">
                #{unavailableTime,jdbcType=DOUBLE},
            </if>
            <if test="performanceTime != null">
                #{performanceTime,jdbcType=DOUBLE},
            </if>
            <if test="controlTime != null">
                #{controlTime,jdbcType=DOUBLE},
            </if>
            <if test="damagedVm != null">
                #{damagedVm,jdbcType=BIGINT},
            </if>
            <if test="totalVm != null">
                #{totalVm,jdbcType=BIGINT},
            </if>
            <if test="totalLifeTime != null">
                #{totalLifeTime,jdbcType=BIGINT},
            </if>
            <if test="ds != null">
                #{ds,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample"
            parameterType="com.aliyun.xdragon.common.generate.keymetric.model.EcsDailyKeyMetricExample"
            resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select count(*) from ecs_daily_key_metric
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update ecs_daily_key_metric
        <set>
            <if test="record.unavailableTime != null">
                unavailable_time = #{record.unavailableTime,jdbcType=DOUBLE},
            </if>
            <if test="record.performanceTime != null">
                performance_time = #{record.performanceTime,jdbcType=DOUBLE},
            </if>
            <if test="record.controlTime != null">
                control_time = #{record.controlTime,jdbcType=DOUBLE},
            </if>
            <if test="record.damagedVm != null">
                damaged_vm = #{record.damagedVm,jdbcType=BIGINT},
            </if>
            <if test="record.totalVm != null">
                total_vm = #{record.totalVm,jdbcType=BIGINT},
            </if>
            <if test="record.totalLifeTime != null">
                total_life_time = #{record.totalLifeTime,jdbcType=BIGINT},
            </if>
            <if test="record.ds != null">
                ds = #{record.ds,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update ecs_daily_key_metric
        set unavailable_time = #{record.unavailableTime,jdbcType=DOUBLE},
        performance_time = #{record.performanceTime,jdbcType=DOUBLE},
        control_time = #{record.controlTime,jdbcType=DOUBLE},
        damaged_vm = #{record.damagedVm,jdbcType=BIGINT},
        total_vm = #{record.totalVm,jdbcType=BIGINT},
        total_life_time = #{record.totalLifeTime,jdbcType=BIGINT},
        ds = #{record.ds,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <select id="selectByExampleWithRowbounds"
            parameterType="com.aliyun.xdragon.common.generate.keymetric.model.EcsDailyKeyMetricExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from ecs_daily_key_metric
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <insert id="batchInsert" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into ecs_daily_key_metric
        (unavailable_time, performance_time, control_time, damaged_vm, total_vm, total_life_time,
        ds)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.unavailableTime,jdbcType=DOUBLE}, #{item.performanceTime,jdbcType=DOUBLE},
            #{item.controlTime,jdbcType=DOUBLE}, #{item.damagedVm,jdbcType=BIGINT}, #{item.totalVm,jdbcType=BIGINT},
            #{item.totalLifeTime,jdbcType=BIGINT}, #{item.ds,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="batchInsertSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into ecs_daily_key_metric (
        <foreach collection="selective" item="column" separator=",">
            ${column.escapedColumnName}
        </foreach>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            <foreach collection="selective" item="column" separator=",">
                <if test="'unavailable_time'.toString() == column.value">
                    #{item.unavailableTime,jdbcType=DOUBLE}
                </if>
                <if test="'performance_time'.toString() == column.value">
                    #{item.performanceTime,jdbcType=DOUBLE}
                </if>
                <if test="'control_time'.toString() == column.value">
                    #{item.controlTime,jdbcType=DOUBLE}
                </if>
                <if test="'damaged_vm'.toString() == column.value">
                    #{item.damagedVm,jdbcType=BIGINT}
                </if>
                <if test="'total_vm'.toString() == column.value">
                    #{item.totalVm,jdbcType=BIGINT}
                </if>
                <if test="'total_life_time'.toString() == column.value">
                    #{item.totalLifeTime,jdbcType=BIGINT}
                </if>
                <if test="'ds'.toString() == column.value">
                    #{item.ds,jdbcType=VARCHAR}
                </if>
            </foreach>
            )
        </foreach>
    </insert>
    <insert id="upsertSelective" parameterType="com.aliyun.xdragon.common.generate.keymetric.model.EcsDailyKeyMetric">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into ecs_daily_key_metric
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unavailableTime != null">
                unavailable_time,
            </if>
            <if test="performanceTime != null">
                performance_time,
            </if>
            <if test="controlTime != null">
                control_time,
            </if>
            <if test="damagedVm != null">
                damaged_vm,
            </if>
            <if test="totalVm != null">
                total_vm,
            </if>
            <if test="totalLifeTime != null">
                total_life_time,
            </if>
            <if test="ds != null">
                ds,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unavailableTime != null">
                #{unavailableTime,jdbcType=DOUBLE},
            </if>
            <if test="performanceTime != null">
                #{performanceTime,jdbcType=DOUBLE},
            </if>
            <if test="controlTime != null">
                #{controlTime,jdbcType=DOUBLE},
            </if>
            <if test="damagedVm != null">
                #{damagedVm,jdbcType=BIGINT},
            </if>
            <if test="totalVm != null">
                #{totalVm,jdbcType=BIGINT},
            </if>
            <if test="totalLifeTime != null">
                #{totalLifeTime,jdbcType=BIGINT},
            </if>
            <if test="ds != null">
                #{ds,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="unavailableTime != null">
                unavailable_time = #{unavailableTime,jdbcType=DOUBLE},
            </if>
            <if test="performanceTime != null">
                performance_time = #{performanceTime,jdbcType=DOUBLE},
            </if>
            <if test="controlTime != null">
                control_time = #{controlTime,jdbcType=DOUBLE},
            </if>
            <if test="damagedVm != null">
                damaged_vm = #{damagedVm,jdbcType=BIGINT},
            </if>
            <if test="totalVm != null">
                total_vm = #{totalVm,jdbcType=BIGINT},
            </if>
            <if test="totalLifeTime != null">
                total_life_time = #{totalLifeTime,jdbcType=BIGINT},
            </if>
            <if test="ds != null">
                ds = #{ds,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="upsert" parameterType="com.aliyun.xdragon.common.generate.keymetric.model.EcsDailyKeyMetric">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into ecs_daily_key_metric
        (unavailable_time, performance_time, control_time, damaged_vm, total_vm, total_life_time,
        ds)
        values
        (#{unavailableTime,jdbcType=DOUBLE}, #{performanceTime,jdbcType=DOUBLE}, #{controlTime,jdbcType=DOUBLE},
        #{damagedVm,jdbcType=BIGINT}, #{totalVm,jdbcType=BIGINT}, #{totalLifeTime,jdbcType=BIGINT},
        #{ds,jdbcType=VARCHAR})
        on duplicate key update
        unavailable_time = #{unavailableTime,jdbcType=DOUBLE},
        performance_time = #{performanceTime,jdbcType=DOUBLE},
        control_time = #{controlTime,jdbcType=DOUBLE},
        damaged_vm = #{damagedVm,jdbcType=BIGINT},
        total_vm = #{totalVm,jdbcType=BIGINT},
        total_life_time = #{totalLifeTime,jdbcType=BIGINT},
        ds = #{ds,jdbcType=VARCHAR}
    </insert>
    <select id="selectOneByExample"
            parameterType="com.aliyun.xdragon.common.generate.keymetric.model.EcsDailyKeyMetricExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from ecs_daily_key_metric
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        limit 1
    </select>
    <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <choose>
            <when test="selective != null and selective.length > 0">
                <foreach collection="selective" item="column" separator=",">
                    ${column.aliasedEscapedColumnName}
                </foreach>
            </when>
            <otherwise>
                <include refid="Base_Column_List"/>
            </otherwise>
        </choose>
        from ecs_daily_key_metric
        <if test="example != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
        <if test="example != null and example.orderByClause != null">
            order by ${example.orderByClause}
        </if>
        limit 1
    </select>
</mapper>