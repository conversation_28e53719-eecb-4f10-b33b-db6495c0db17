package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.model.ChatMessageInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ChatMessageInfoCustomMapper {
    @Select(
        "<script>"
            + " select `source`, date_format(`gmt_create`, '%Y-%m-%d') as `day`, count(*) as cnt from chat_message"
            + " where `sender` = 'human' and `gmt_create` between #{start} and #{end}"
            + " <if test=\"sessions != null and sessions.size() > 0\">"
            + "  and session_id in "
            + "    <foreach collection=\"sessions\" item=\"session\" open=\"(\" close=\")\" separator=\",\">"
            + "      #{session} "
            + "    </foreach>"
            + " </if >"
            + " group by `source`, date_format(gmt_create, '%Y-%m-%d') </script>"
    )
    @ResultType(Map.class)
    List<Map<String, Object>> statChatMessage(@Param("start") Date startTime, @Param("end") Date endTime,
                                              @Param("sessions") Collection<String> sessions);

    @Select(
            "<script>"
                    + " select `cm`.`id` AS id, `message`, `sender` FROM `chat_message` AS cm " +
                    "  LEFT JOIN `chat_session` AS cs ON cm.`session_id` = cs.`session_id` " +
                    "  WHERE cs.`ext` = #{aoneId} and sender in ('human', 'ai')" +
                    "  ORDER BY `cm`.`id` ASC " +
                    "</script>"
    )
    @ResultType(ChatMessageInfo.class)
    List<ChatMessageInfo> chatMessageInfoCustomMapper(@Param("aoneId")String aoneId);

    @Select(
        "<script>"
            + " select cm.`session_id` as sessionId, cs.`ext` as workItemId, cm.`sender`, cm.`view_content`"
            + " from "
            + " (select * from chat_session"
            + "  where `ext` in "
            + "      <foreach close=\")\" collection=\"workItemIds\" item=\"item\" open=\"(\" separator=\",\">"
            + "          #{item}"
            + "      </foreach>) as cs "
            + " left join chat_message as cm on cs.`session_id` = cm.`session_id`"
            + " where cm.`source` = 5"
            + " order by cm.`id` </script>"
    )
    @ResultType(Map.class)
    List<Map<String, String>> queryWorkItemMessageInfo(@Param("workItemIds") List<String> workItemIds);
}
