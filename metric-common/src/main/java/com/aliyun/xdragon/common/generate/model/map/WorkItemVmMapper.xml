<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.model.map.WorkItemVmMapper">
  <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.model.WorkItemVm">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="instance_id" jdbcType="VARCHAR" property="instanceId" />
    <result column="nc_ip" jdbcType="VARCHAR" property="ncIp" />
    <result column="cluster" jdbcType="VARCHAR" property="cluster" />
    <result column="region" jdbcType="VARCHAR" property="region" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="vcpu_mod" jdbcType="VARCHAR" property="vcpuMod" />
    <result column="cores" jdbcType="INTEGER" property="cores" />
    <result column="ali_uid" jdbcType="VARCHAR" property="aliUid" />
    <result column="host_name" jdbcType="VARCHAR" property="hostName" />
    <result column="instance_type_family" jdbcType="VARCHAR" property="instanceTypeFamily" />
    <result column="exception_time" jdbcType="TIMESTAMP" property="exceptionTime" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <result column="is_mix" jdbcType="TINYINT" property="isMix" />
    <result column="is_oversale" jdbcType="TINYINT" property="isOversale" />
    <result column="is_local_disk" jdbcType="TINYINT" property="isLocalDisk" />
    <result column="physical_model" jdbcType="VARCHAR" property="physicalModel" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.aliyun.xdragon.common.generate.model.WorkItemVm">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="reason" jdbcType="LONGVARCHAR" property="reason" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, gmt_create, gmt_modified, instance_id, nc_ip, `cluster`, region, product_name, 
    vcpu_mod, cores, ali_uid, host_name, instance_type_family, exception_time, source_id, 
    data_source, is_mix, is_oversale, is_local_disk, physical_model
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    reason
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVmExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from work_item_vm
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVmExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from work_item_vm
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="example != null and example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
      </otherwise>
    </choose>
    from work_item_vm
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from work_item_vm
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
      </otherwise>
    </choose>
    from work_item_vm
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from work_item_vm
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVmExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from work_item_vm
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVm">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into work_item_vm (gmt_create, gmt_modified, instance_id, 
      nc_ip, `cluster`, region, 
      product_name, vcpu_mod, cores, 
      ali_uid, host_name, instance_type_family, 
      exception_time, source_id, data_source, 
      is_mix, is_oversale, is_local_disk, 
      physical_model, reason)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{instanceId,jdbcType=VARCHAR}, 
      #{ncIp,jdbcType=VARCHAR}, #{cluster,jdbcType=VARCHAR}, #{region,jdbcType=VARCHAR}, 
      #{productName,jdbcType=VARCHAR}, #{vcpuMod,jdbcType=VARCHAR}, #{cores,jdbcType=INTEGER}, 
      #{aliUid,jdbcType=VARCHAR}, #{hostName,jdbcType=VARCHAR}, #{instanceTypeFamily,jdbcType=VARCHAR}, 
      #{exceptionTime,jdbcType=TIMESTAMP}, #{sourceId,jdbcType=VARCHAR}, #{dataSource,jdbcType=VARCHAR}, 
      #{isMix,jdbcType=TINYINT}, #{isOversale,jdbcType=TINYINT}, #{isLocalDisk,jdbcType=TINYINT}, 
      #{physicalModel,jdbcType=VARCHAR}, #{reason,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVm">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into work_item_vm
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="instanceId != null">
        instance_id,
      </if>
      <if test="ncIp != null">
        nc_ip,
      </if>
      <if test="cluster != null">
        `cluster`,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="vcpuMod != null">
        vcpu_mod,
      </if>
      <if test="cores != null">
        cores,
      </if>
      <if test="aliUid != null">
        ali_uid,
      </if>
      <if test="hostName != null">
        host_name,
      </if>
      <if test="instanceTypeFamily != null">
        instance_type_family,
      </if>
      <if test="exceptionTime != null">
        exception_time,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="dataSource != null">
        data_source,
      </if>
      <if test="isMix != null">
        is_mix,
      </if>
      <if test="isOversale != null">
        is_oversale,
      </if>
      <if test="isLocalDisk != null">
        is_local_disk,
      </if>
      <if test="physicalModel != null">
        physical_model,
      </if>
      <if test="reason != null">
        reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="instanceId != null">
        #{instanceId,jdbcType=VARCHAR},
      </if>
      <if test="ncIp != null">
        #{ncIp,jdbcType=VARCHAR},
      </if>
      <if test="cluster != null">
        #{cluster,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="vcpuMod != null">
        #{vcpuMod,jdbcType=VARCHAR},
      </if>
      <if test="cores != null">
        #{cores,jdbcType=INTEGER},
      </if>
      <if test="aliUid != null">
        #{aliUid,jdbcType=VARCHAR},
      </if>
      <if test="hostName != null">
        #{hostName,jdbcType=VARCHAR},
      </if>
      <if test="instanceTypeFamily != null">
        #{instanceTypeFamily,jdbcType=VARCHAR},
      </if>
      <if test="exceptionTime != null">
        #{exceptionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="dataSource != null">
        #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="isMix != null">
        #{isMix,jdbcType=TINYINT},
      </if>
      <if test="isOversale != null">
        #{isOversale,jdbcType=TINYINT},
      </if>
      <if test="isLocalDisk != null">
        #{isLocalDisk,jdbcType=TINYINT},
      </if>
      <if test="physicalModel != null">
        #{physicalModel,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVmExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from work_item_vm
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update work_item_vm
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.instanceId != null">
        instance_id = #{record.instanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.ncIp != null">
        nc_ip = #{record.ncIp,jdbcType=VARCHAR},
      </if>
      <if test="record.cluster != null">
        `cluster` = #{record.cluster,jdbcType=VARCHAR},
      </if>
      <if test="record.region != null">
        region = #{record.region,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.vcpuMod != null">
        vcpu_mod = #{record.vcpuMod,jdbcType=VARCHAR},
      </if>
      <if test="record.cores != null">
        cores = #{record.cores,jdbcType=INTEGER},
      </if>
      <if test="record.aliUid != null">
        ali_uid = #{record.aliUid,jdbcType=VARCHAR},
      </if>
      <if test="record.hostName != null">
        host_name = #{record.hostName,jdbcType=VARCHAR},
      </if>
      <if test="record.instanceTypeFamily != null">
        instance_type_family = #{record.instanceTypeFamily,jdbcType=VARCHAR},
      </if>
      <if test="record.exceptionTime != null">
        exception_time = #{record.exceptionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sourceId != null">
        source_id = #{record.sourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataSource != null">
        data_source = #{record.dataSource,jdbcType=VARCHAR},
      </if>
      <if test="record.isMix != null">
        is_mix = #{record.isMix,jdbcType=TINYINT},
      </if>
      <if test="record.isOversale != null">
        is_oversale = #{record.isOversale,jdbcType=TINYINT},
      </if>
      <if test="record.isLocalDisk != null">
        is_local_disk = #{record.isLocalDisk,jdbcType=TINYINT},
      </if>
      <if test="record.physicalModel != null">
        physical_model = #{record.physicalModel,jdbcType=VARCHAR},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update work_item_vm
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      instance_id = #{record.instanceId,jdbcType=VARCHAR},
      nc_ip = #{record.ncIp,jdbcType=VARCHAR},
      `cluster` = #{record.cluster,jdbcType=VARCHAR},
      region = #{record.region,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      vcpu_mod = #{record.vcpuMod,jdbcType=VARCHAR},
      cores = #{record.cores,jdbcType=INTEGER},
      ali_uid = #{record.aliUid,jdbcType=VARCHAR},
      host_name = #{record.hostName,jdbcType=VARCHAR},
      instance_type_family = #{record.instanceTypeFamily,jdbcType=VARCHAR},
      exception_time = #{record.exceptionTime,jdbcType=TIMESTAMP},
      source_id = #{record.sourceId,jdbcType=VARCHAR},
      data_source = #{record.dataSource,jdbcType=VARCHAR},
      is_mix = #{record.isMix,jdbcType=TINYINT},
      is_oversale = #{record.isOversale,jdbcType=TINYINT},
      is_local_disk = #{record.isLocalDisk,jdbcType=TINYINT},
      physical_model = #{record.physicalModel,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update work_item_vm
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      instance_id = #{record.instanceId,jdbcType=VARCHAR},
      nc_ip = #{record.ncIp,jdbcType=VARCHAR},
      `cluster` = #{record.cluster,jdbcType=VARCHAR},
      region = #{record.region,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      vcpu_mod = #{record.vcpuMod,jdbcType=VARCHAR},
      cores = #{record.cores,jdbcType=INTEGER},
      ali_uid = #{record.aliUid,jdbcType=VARCHAR},
      host_name = #{record.hostName,jdbcType=VARCHAR},
      instance_type_family = #{record.instanceTypeFamily,jdbcType=VARCHAR},
      exception_time = #{record.exceptionTime,jdbcType=TIMESTAMP},
      source_id = #{record.sourceId,jdbcType=VARCHAR},
      data_source = #{record.dataSource,jdbcType=VARCHAR},
      is_mix = #{record.isMix,jdbcType=TINYINT},
      is_oversale = #{record.isOversale,jdbcType=TINYINT},
      is_local_disk = #{record.isLocalDisk,jdbcType=TINYINT},
      physical_model = #{record.physicalModel,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVm">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update work_item_vm
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="instanceId != null">
        instance_id = #{instanceId,jdbcType=VARCHAR},
      </if>
      <if test="ncIp != null">
        nc_ip = #{ncIp,jdbcType=VARCHAR},
      </if>
      <if test="cluster != null">
        `cluster` = #{cluster,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        region = #{region,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="vcpuMod != null">
        vcpu_mod = #{vcpuMod,jdbcType=VARCHAR},
      </if>
      <if test="cores != null">
        cores = #{cores,jdbcType=INTEGER},
      </if>
      <if test="aliUid != null">
        ali_uid = #{aliUid,jdbcType=VARCHAR},
      </if>
      <if test="hostName != null">
        host_name = #{hostName,jdbcType=VARCHAR},
      </if>
      <if test="instanceTypeFamily != null">
        instance_type_family = #{instanceTypeFamily,jdbcType=VARCHAR},
      </if>
      <if test="exceptionTime != null">
        exception_time = #{exceptionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="dataSource != null">
        data_source = #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="isMix != null">
        is_mix = #{isMix,jdbcType=TINYINT},
      </if>
      <if test="isOversale != null">
        is_oversale = #{isOversale,jdbcType=TINYINT},
      </if>
      <if test="isLocalDisk != null">
        is_local_disk = #{isLocalDisk,jdbcType=TINYINT},
      </if>
      <if test="physicalModel != null">
        physical_model = #{physicalModel,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVm">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update work_item_vm
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      instance_id = #{instanceId,jdbcType=VARCHAR},
      nc_ip = #{ncIp,jdbcType=VARCHAR},
      `cluster` = #{cluster,jdbcType=VARCHAR},
      region = #{region,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      vcpu_mod = #{vcpuMod,jdbcType=VARCHAR},
      cores = #{cores,jdbcType=INTEGER},
      ali_uid = #{aliUid,jdbcType=VARCHAR},
      host_name = #{hostName,jdbcType=VARCHAR},
      instance_type_family = #{instanceTypeFamily,jdbcType=VARCHAR},
      exception_time = #{exceptionTime,jdbcType=TIMESTAMP},
      source_id = #{sourceId,jdbcType=VARCHAR},
      data_source = #{dataSource,jdbcType=VARCHAR},
      is_mix = #{isMix,jdbcType=TINYINT},
      is_oversale = #{isOversale,jdbcType=TINYINT},
      is_local_disk = #{isLocalDisk,jdbcType=TINYINT},
      physical_model = #{physicalModel,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVm">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update work_item_vm
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      instance_id = #{instanceId,jdbcType=VARCHAR},
      nc_ip = #{ncIp,jdbcType=VARCHAR},
      `cluster` = #{cluster,jdbcType=VARCHAR},
      region = #{region,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      vcpu_mod = #{vcpuMod,jdbcType=VARCHAR},
      cores = #{cores,jdbcType=INTEGER},
      ali_uid = #{aliUid,jdbcType=VARCHAR},
      host_name = #{hostName,jdbcType=VARCHAR},
      instance_type_family = #{instanceTypeFamily,jdbcType=VARCHAR},
      exception_time = #{exceptionTime,jdbcType=TIMESTAMP},
      source_id = #{sourceId,jdbcType=VARCHAR},
      data_source = #{dataSource,jdbcType=VARCHAR},
      is_mix = #{isMix,jdbcType=TINYINT},
      is_oversale = #{isOversale,jdbcType=TINYINT},
      is_local_disk = #{isLocalDisk,jdbcType=TINYINT},
      physical_model = #{physicalModel,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVmExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from work_item_vm
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVmExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from work_item_vm
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into work_item_vm
    (gmt_create, gmt_modified, instance_id, nc_ip, `cluster`, region, product_name, vcpu_mod, 
      cores, ali_uid, host_name, instance_type_family, exception_time, source_id, data_source, 
      is_mix, is_oversale, is_local_disk, physical_model, reason)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.instanceId,jdbcType=VARCHAR}, 
        #{item.ncIp,jdbcType=VARCHAR}, #{item.cluster,jdbcType=VARCHAR}, #{item.region,jdbcType=VARCHAR}, 
        #{item.productName,jdbcType=VARCHAR}, #{item.vcpuMod,jdbcType=VARCHAR}, #{item.cores,jdbcType=INTEGER}, 
        #{item.aliUid,jdbcType=VARCHAR}, #{item.hostName,jdbcType=VARCHAR}, #{item.instanceTypeFamily,jdbcType=VARCHAR}, 
        #{item.exceptionTime,jdbcType=TIMESTAMP}, #{item.sourceId,jdbcType=VARCHAR}, #{item.dataSource,jdbcType=VARCHAR}, 
        #{item.isMix,jdbcType=TINYINT}, #{item.isOversale,jdbcType=TINYINT}, #{item.isLocalDisk,jdbcType=TINYINT}, 
        #{item.physicalModel,jdbcType=VARCHAR}, #{item.reason,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into work_item_vm (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'gmt_create'.toString() == column.value">
          #{item.gmtCreate,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'instance_id'.toString() == column.value">
          #{item.instanceId,jdbcType=VARCHAR}
        </if>
        <if test="'nc_ip'.toString() == column.value">
          #{item.ncIp,jdbcType=VARCHAR}
        </if>
        <if test="'cluster'.toString() == column.value">
          #{item.cluster,jdbcType=VARCHAR}
        </if>
        <if test="'region'.toString() == column.value">
          #{item.region,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'vcpu_mod'.toString() == column.value">
          #{item.vcpuMod,jdbcType=VARCHAR}
        </if>
        <if test="'cores'.toString() == column.value">
          #{item.cores,jdbcType=INTEGER}
        </if>
        <if test="'ali_uid'.toString() == column.value">
          #{item.aliUid,jdbcType=VARCHAR}
        </if>
        <if test="'host_name'.toString() == column.value">
          #{item.hostName,jdbcType=VARCHAR}
        </if>
        <if test="'instance_type_family'.toString() == column.value">
          #{item.instanceTypeFamily,jdbcType=VARCHAR}
        </if>
        <if test="'exception_time'.toString() == column.value">
          #{item.exceptionTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'source_id'.toString() == column.value">
          #{item.sourceId,jdbcType=VARCHAR}
        </if>
        <if test="'data_source'.toString() == column.value">
          #{item.dataSource,jdbcType=VARCHAR}
        </if>
        <if test="'is_mix'.toString() == column.value">
          #{item.isMix,jdbcType=TINYINT}
        </if>
        <if test="'is_oversale'.toString() == column.value">
          #{item.isOversale,jdbcType=TINYINT}
        </if>
        <if test="'is_local_disk'.toString() == column.value">
          #{item.isLocalDisk,jdbcType=TINYINT}
        </if>
        <if test="'physical_model'.toString() == column.value">
          #{item.physicalModel,jdbcType=VARCHAR}
        </if>
        <if test="'reason'.toString() == column.value">
          #{item.reason,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
  <insert id="upsertSelective" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVm" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into work_item_vm
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="instanceId != null">
        instance_id,
      </if>
      <if test="ncIp != null">
        nc_ip,
      </if>
      <if test="cluster != null">
        `cluster`,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="vcpuMod != null">
        vcpu_mod,
      </if>
      <if test="cores != null">
        cores,
      </if>
      <if test="aliUid != null">
        ali_uid,
      </if>
      <if test="hostName != null">
        host_name,
      </if>
      <if test="instanceTypeFamily != null">
        instance_type_family,
      </if>
      <if test="exceptionTime != null">
        exception_time,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="dataSource != null">
        data_source,
      </if>
      <if test="isMix != null">
        is_mix,
      </if>
      <if test="isOversale != null">
        is_oversale,
      </if>
      <if test="isLocalDisk != null">
        is_local_disk,
      </if>
      <if test="physicalModel != null">
        physical_model,
      </if>
      <if test="reason != null">
        reason,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="instanceId != null">
        #{instanceId,jdbcType=VARCHAR},
      </if>
      <if test="ncIp != null">
        #{ncIp,jdbcType=VARCHAR},
      </if>
      <if test="cluster != null">
        #{cluster,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="vcpuMod != null">
        #{vcpuMod,jdbcType=VARCHAR},
      </if>
      <if test="cores != null">
        #{cores,jdbcType=INTEGER},
      </if>
      <if test="aliUid != null">
        #{aliUid,jdbcType=VARCHAR},
      </if>
      <if test="hostName != null">
        #{hostName,jdbcType=VARCHAR},
      </if>
      <if test="instanceTypeFamily != null">
        #{instanceTypeFamily,jdbcType=VARCHAR},
      </if>
      <if test="exceptionTime != null">
        #{exceptionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="dataSource != null">
        #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="isMix != null">
        #{isMix,jdbcType=TINYINT},
      </if>
      <if test="isOversale != null">
        #{isOversale,jdbcType=TINYINT},
      </if>
      <if test="isLocalDisk != null">
        #{isLocalDisk,jdbcType=TINYINT},
      </if>
      <if test="physicalModel != null">
        #{physicalModel,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="instanceId != null">
        instance_id = #{instanceId,jdbcType=VARCHAR},
      </if>
      <if test="ncIp != null">
        nc_ip = #{ncIp,jdbcType=VARCHAR},
      </if>
      <if test="cluster != null">
        `cluster` = #{cluster,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        region = #{region,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="vcpuMod != null">
        vcpu_mod = #{vcpuMod,jdbcType=VARCHAR},
      </if>
      <if test="cores != null">
        cores = #{cores,jdbcType=INTEGER},
      </if>
      <if test="aliUid != null">
        ali_uid = #{aliUid,jdbcType=VARCHAR},
      </if>
      <if test="hostName != null">
        host_name = #{hostName,jdbcType=VARCHAR},
      </if>
      <if test="instanceTypeFamily != null">
        instance_type_family = #{instanceTypeFamily,jdbcType=VARCHAR},
      </if>
      <if test="exceptionTime != null">
        exception_time = #{exceptionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="dataSource != null">
        data_source = #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="isMix != null">
        is_mix = #{isMix,jdbcType=TINYINT},
      </if>
      <if test="isOversale != null">
        is_oversale = #{isOversale,jdbcType=TINYINT},
      </if>
      <if test="isLocalDisk != null">
        is_local_disk = #{isLocalDisk,jdbcType=TINYINT},
      </if>
      <if test="physicalModel != null">
        physical_model = #{physicalModel,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="upsert" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVm" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into work_item_vm
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      gmt_create,
      gmt_modified,
      instance_id,
      nc_ip,
      `cluster`,
      region,
      product_name,
      vcpu_mod,
      cores,
      ali_uid,
      host_name,
      instance_type_family,
      exception_time,
      source_id,
      data_source,
      is_mix,
      is_oversale,
      is_local_disk,
      physical_model,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{instanceId,jdbcType=VARCHAR},
      #{ncIp,jdbcType=VARCHAR},
      #{cluster,jdbcType=VARCHAR},
      #{region,jdbcType=VARCHAR},
      #{productName,jdbcType=VARCHAR},
      #{vcpuMod,jdbcType=VARCHAR},
      #{cores,jdbcType=INTEGER},
      #{aliUid,jdbcType=VARCHAR},
      #{hostName,jdbcType=VARCHAR},
      #{instanceTypeFamily,jdbcType=VARCHAR},
      #{exceptionTime,jdbcType=TIMESTAMP},
      #{sourceId,jdbcType=VARCHAR},
      #{dataSource,jdbcType=VARCHAR},
      #{isMix,jdbcType=TINYINT},
      #{isOversale,jdbcType=TINYINT},
      #{isLocalDisk,jdbcType=TINYINT},
      #{physicalModel,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      instance_id = #{instanceId,jdbcType=VARCHAR},
      nc_ip = #{ncIp,jdbcType=VARCHAR},
      `cluster` = #{cluster,jdbcType=VARCHAR},
      region = #{region,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      vcpu_mod = #{vcpuMod,jdbcType=VARCHAR},
      cores = #{cores,jdbcType=INTEGER},
      ali_uid = #{aliUid,jdbcType=VARCHAR},
      host_name = #{hostName,jdbcType=VARCHAR},
      instance_type_family = #{instanceTypeFamily,jdbcType=VARCHAR},
      exception_time = #{exceptionTime,jdbcType=TIMESTAMP},
      source_id = #{sourceId,jdbcType=VARCHAR},
      data_source = #{dataSource,jdbcType=VARCHAR},
      is_mix = #{isMix,jdbcType=TINYINT},
      is_oversale = #{isOversale,jdbcType=TINYINT},
      is_local_disk = #{isLocalDisk,jdbcType=TINYINT},
      physical_model = #{physicalModel,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="upsertWithBLOBs" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVm" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into work_item_vm
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      gmt_create,
      gmt_modified,
      instance_id,
      nc_ip,
      `cluster`,
      region,
      product_name,
      vcpu_mod,
      cores,
      ali_uid,
      host_name,
      instance_type_family,
      exception_time,
      source_id,
      data_source,
      is_mix,
      is_oversale,
      is_local_disk,
      physical_model,
      reason,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtModified,jdbcType=TIMESTAMP},
      #{instanceId,jdbcType=VARCHAR},
      #{ncIp,jdbcType=VARCHAR},
      #{cluster,jdbcType=VARCHAR},
      #{region,jdbcType=VARCHAR},
      #{productName,jdbcType=VARCHAR},
      #{vcpuMod,jdbcType=VARCHAR},
      #{cores,jdbcType=INTEGER},
      #{aliUid,jdbcType=VARCHAR},
      #{hostName,jdbcType=VARCHAR},
      #{instanceTypeFamily,jdbcType=VARCHAR},
      #{exceptionTime,jdbcType=TIMESTAMP},
      #{sourceId,jdbcType=VARCHAR},
      #{dataSource,jdbcType=VARCHAR},
      #{isMix,jdbcType=TINYINT},
      #{isOversale,jdbcType=TINYINT},
      #{isLocalDisk,jdbcType=TINYINT},
      #{physicalModel,jdbcType=VARCHAR},
      #{reason,jdbcType=LONGVARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      instance_id = #{instanceId,jdbcType=VARCHAR},
      nc_ip = #{ncIp,jdbcType=VARCHAR},
      `cluster` = #{cluster,jdbcType=VARCHAR},
      region = #{region,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      vcpu_mod = #{vcpuMod,jdbcType=VARCHAR},
      cores = #{cores,jdbcType=INTEGER},
      ali_uid = #{aliUid,jdbcType=VARCHAR},
      host_name = #{hostName,jdbcType=VARCHAR},
      instance_type_family = #{instanceTypeFamily,jdbcType=VARCHAR},
      exception_time = #{exceptionTime,jdbcType=TIMESTAMP},
      source_id = #{sourceId,jdbcType=VARCHAR},
      data_source = #{dataSource,jdbcType=VARCHAR},
      is_mix = #{isMix,jdbcType=TINYINT},
      is_oversale = #{isOversale,jdbcType=TINYINT},
      is_local_disk = #{isLocalDisk,jdbcType=TINYINT},
      physical_model = #{physicalModel,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=LONGVARCHAR},
    </trim>
  </insert>
  <select id="selectOneByExample" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVmExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from work_item_vm
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleWithBLOBs" parameterType="com.aliyun.xdragon.common.generate.model.WorkItemVmExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from work_item_vm
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
      </otherwise>
    </choose>
    from work_item_vm
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
</mapper>