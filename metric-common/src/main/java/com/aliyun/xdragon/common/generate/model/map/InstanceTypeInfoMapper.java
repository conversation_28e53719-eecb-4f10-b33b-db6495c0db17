package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.InstanceTypeInfo;
import com.aliyun.xdragon.common.generate.model.InstanceTypeInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface InstanceTypeInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    long countByExample(InstanceTypeInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    int deleteByExample(InstanceTypeInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    int insert(InstanceTypeInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    int insertSelective(InstanceTypeInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    List<InstanceTypeInfo> selectByExampleWithRowbounds(InstanceTypeInfoExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    List<InstanceTypeInfo> selectByExampleSelective(@Param("example") InstanceTypeInfoExample example, @Param("selective") InstanceTypeInfo.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    InstanceTypeInfo selectOneByExample(InstanceTypeInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    InstanceTypeInfo selectOneByExampleSelective(@Param("example") InstanceTypeInfoExample example, @Param("selective") InstanceTypeInfo.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    List<InstanceTypeInfo> selectByExample(InstanceTypeInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    InstanceTypeInfo selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") InstanceTypeInfo.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    InstanceTypeInfo selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") InstanceTypeInfo record, @Param("example") InstanceTypeInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") InstanceTypeInfo record, @Param("example") InstanceTypeInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(InstanceTypeInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(InstanceTypeInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<InstanceTypeInfo> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<InstanceTypeInfo> list, @Param("selective") InstanceTypeInfo.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    int upsert(InstanceTypeInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table instance_type_info
     *
     * @mbg.generated
     */
    int upsertSelective(InstanceTypeInfo record);
}