package com.aliyun.xdragon.common.model.clientops.response;

import java.io.Serializable;
import java.util.List;

import com.aliyun.xdragon.common.model.clientops.topo.EcsTopoNode;
import com.aliyun.xdragon.common.model.clientops.topo.NcTopoNode;
import com.aliyun.xdragon.common.model.clientops.topo.TopoNode;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/09/06
 */
@Data
public class OnEcsTopoResponse implements Serializable {
    private static final long serialVersionUID = 2819294759860041716L;

    private EcsTopoNode ecsTopoNode;
    private NcTopoNode ncTopoNode;
    private List<TopoNode> onEcsTopoNodeList;

    public OnEcsTopoResponse(EcsTopoNode ecsTopoNode, List<TopoNode> onEcsTopoNodeList, NcTopoNode ncTopoNode) {
        this.ecsTopoNode = ecsTopoNode;
        this.onEcsTopoNodeList = onEcsTopoNodeList;
        this.ncTopoNode = ncTopoNode;
    }
}
