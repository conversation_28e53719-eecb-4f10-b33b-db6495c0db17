package com.aliyun.xdragon.common.util;

import com.google.common.base.Joiner;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class StringUtil {
    public static String toString(List<String> list) {
        if (list == null) {
            return null;
        }
        if (list.size() == 0) {
            return "";
        }
        return Joiner.on(',').join(list);
    }

    public static List<String> splitAndTrimedByChar(String input, char split) {
        if (Objects.isNull(input)) {
            return null;
        }
        return Arrays.asList(StringUtils.split(input, split)).stream().map(x -> x.trim()).collect(
                Collectors.toList());
    }

    public static List<String> splitByCommaAndTrimed(String input) {
        return splitAndTrimedByChar(input, ',');
    }

}
