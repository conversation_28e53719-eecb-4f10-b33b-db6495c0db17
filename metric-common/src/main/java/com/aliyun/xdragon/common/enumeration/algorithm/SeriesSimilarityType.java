package com.aliyun.xdragon.common.enumeration.algorithm;

/**
 * <AUTHOR>
 * @date 2022/11/7
 * @description
 */
public enum SeriesSimilarityType {
    /**
     * 欧式距离
     */
    Euclidean("欧式距离（各维度差距平方和的平方根），取值恒大于等于0"),
    /**
     * 曼哈顿距离
     */
    Manhattan("曼哈顿距离（各维度绝对差距之和），取值恒大于等于0"),
    /**
     * 余弦距离
     */
    Cosine("基于余弦相似度的距离，取值为[0,1]"),
    /**
     * Pearson距离
     */
    Pearson("基于Pearson相似度的距离，取值为[0,1]"),
    /**
     * DTW距离
     */
    DTW("动态时间规整距离，容忍时间维度的偏移，取值恒大于等于0");

    private String note;


    SeriesSimilarityType(String note) {
        this.note = note;
    }


    public String getNote() {
        return note;
    }
}
