package com.aliyun.xdragon.common.enumeration;

/**
 * <AUTHOR>
 * @date 2023/05/15
 */

public enum MatchingRule implements BaseNameEnum {
    /**
     * 前缀匹配
     */
    PREFIX("prefix"),
    /**
     * 完全匹配
     */
    COMPLETE("complete");

    private final String name;


    @Override
    public String getName() {
        return name;
    }


    MatchingRule(String name) {
        this.name = name;
    }


    public static MatchingRule of(String name) {
        for (MatchingRule matchingRule : MatchingRule.values()) {
            if (matchingRule.getName().equals(name)) {
                return matchingRule;
            }
        }
        return null;
    }
}
