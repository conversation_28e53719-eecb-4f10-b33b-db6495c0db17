package com.aliyun.xdragon.common.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ChatMessageInfo implements Serializable {
    private static final long serialVersionUID = -3773135102432255326L;
    private Long id;

    private Long questionId;

    private String sessionId;

    private String message;

    private String viewContent;

    private boolean expired;

    private String sender;

    private Integer approve;

    private String scene;

    private String tool;

    private Integer source;

    private boolean isFinish;

    private String token;

    private Date gmtCreate;

    private Date gmtModified;
}
