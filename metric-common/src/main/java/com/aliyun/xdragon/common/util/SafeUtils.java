package com.aliyun.xdragon.common.util;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class SafeUtils {

    public static <T> List<T> safeList(List<T> t) {
        if (null == t) {
            return Collections.emptyList();
        }
        return t;
    }

    public static <K, V> Map<K, V> safeMap(Map<K, V> map) {
        if (null == map) {
            return Collections.emptyMap();
        }
        return map;
    }
}