package com.aliyun.xdragon.common.generate.log.model.map;

import com.aliyun.xdragon.common.generate.log.model.NcAssociationInfo;
import com.aliyun.xdragon.common.generate.log.model.NcAssociationInfoExample;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface NcAssociationInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_association_info
     *
     * @mbg.generated
     */
    long countByExample(NcAssociationInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_association_info
     *
     * @mbg.generated
     */
    int deleteByExample(NcAssociationInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_association_info
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Date time);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_association_info
     *
     * @mbg.generated
     */
    int insert(NcAssociationInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_association_info
     *
     * @mbg.generated
     */
    int insertSelective(NcAssociationInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_association_info
     *
     * @mbg.generated
     */
    List<NcAssociationInfo> selectByExampleWithRowbounds(NcAssociationInfoExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_association_info
     *
     * @mbg.generated
     */
    List<NcAssociationInfo> selectByExample(NcAssociationInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_association_info
     *
     * @mbg.generated
     */
    NcAssociationInfo selectByPrimaryKey(Date time);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_association_info
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") NcAssociationInfo record, @Param("example") NcAssociationInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_association_info
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") NcAssociationInfo record, @Param("example") NcAssociationInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_association_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(NcAssociationInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_association_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(NcAssociationInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_association_info
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<NcAssociationInfo> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table nc_association_info
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<NcAssociationInfo> list, @Param("selective") NcAssociationInfo.Column ... selective);
}