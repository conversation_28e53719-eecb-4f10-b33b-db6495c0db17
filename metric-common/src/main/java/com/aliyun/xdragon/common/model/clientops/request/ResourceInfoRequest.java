package com.aliyun.xdragon.common.model.clientops.request;

import java.io.Serializable;

import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR> @date 2024/02
 */
@Data
public class ResourceInfoRequest implements Serializable {

    private static final long serialVersionUID = -5371299498581223265L;
    private String uid;

    private String aliuid;

    private Integer start;

    private Integer size = 200;

    String instanceId;

    String runStatusList;

    String region;

    String azone;

    String instanceType;
}
