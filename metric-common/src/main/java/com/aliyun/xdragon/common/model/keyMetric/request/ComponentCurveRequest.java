package com.aliyun.xdragon.common.model.keyMetric.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ComponentCurveRequest implements Serializable {
    private static final long serialVersionUID = -5178982156416509855L;

    /**
     * Date format as (yyyy-MM-dd)
     */
    @NotNull
    @NotEmpty
    String startDate;

    /**
     * Date format as (yyyy-MM-dd)
     */
    @NotNull
    @NotEmpty
    String endDate;

    /**
     * Component name
     */
    @NotNull
    @NotEmpty
    String component;
}
