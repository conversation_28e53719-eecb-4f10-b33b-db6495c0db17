package com.aliyun.xdragon.common.util;

import org.apache.commons.lang3.StringUtils;
import java.util.regex.Pattern;

/**
 * Machine ID utility class for identifying machine ID types (Instance ID or NC IP)
 * 
 */
public class MachineIdUtil {
    // ECS实例ID的正则表达式 (从DiagnoseServiceImpl中提取)
    private static final String ECS_INSTANCE_REGEX = "^(i-(?!auto)|hbm-|AY|eci-|cp-|ic-|ay|acs-)[a-z0-9-]+$";
    // IPv4的正则表达式 (从现有代码中提取)
    private static final String IPV4_REGEX = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$";
    
    /**
     * 判断给定的machineId是实例ID还是NC IP
     * @param machineId 机器ID
     * @return MachineIdType 枚举类型
     */
    public static MachineIdType getMachineIdType(String machineId) {
        if (StringUtils.isBlank(machineId)) {
            return MachineIdType.UNKNOWN;
        }
        
        // 检查是否为ECS实例ID
        if (Pattern.matches(ECS_INSTANCE_REGEX, machineId)) {
            return MachineIdType.INSTANCE_ID;
        }
        
        // 检查是否为IP地址
        if (Pattern.matches(IPV4_REGEX, machineId)) {
            return MachineIdType.NC_IP;
        }
        
        return MachineIdType.UNKNOWN;
    }
    
    /**
     * 判断给定的machineId是否为实例ID
     * @param machineId 机器ID
     * @return boolean
     */
    public static boolean isInstanceId(String machineId) {
        return getMachineIdType(machineId) == MachineIdType.INSTANCE_ID;
    }
    
    /**
     * 判断给定的machineId是否为NC IP
     * @param machineId 机器ID
     * @return boolean
     */
    public static boolean isNcIp(String machineId) {
        return getMachineIdType(machineId) == MachineIdType.NC_IP;
    }
    
    public enum MachineIdType {
        INSTANCE_ID,
        NC_IP,
        UNKNOWN
    }
}