package com.aliyun.xdragon.common.generate.log.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AllinoneRiskVmDetailExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    public AllinoneRiskVmDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andVmDetailIdIsNull() {
            addCriterion("vm_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andVmDetailIdIsNotNull() {
            addCriterion("vm_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andVmDetailIdEqualTo(Long value) {
            addCriterion("vm_detail_id =", value, "vmDetailId");
            return (Criteria) this;
        }

        public Criteria andVmDetailIdNotEqualTo(Long value) {
            addCriterion("vm_detail_id <>", value, "vmDetailId");
            return (Criteria) this;
        }

        public Criteria andVmDetailIdGreaterThan(Long value) {
            addCriterion("vm_detail_id >", value, "vmDetailId");
            return (Criteria) this;
        }

        public Criteria andVmDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("vm_detail_id >=", value, "vmDetailId");
            return (Criteria) this;
        }

        public Criteria andVmDetailIdLessThan(Long value) {
            addCriterion("vm_detail_id <", value, "vmDetailId");
            return (Criteria) this;
        }

        public Criteria andVmDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("vm_detail_id <=", value, "vmDetailId");
            return (Criteria) this;
        }

        public Criteria andVmDetailIdIn(List<Long> values) {
            addCriterion("vm_detail_id in", values, "vmDetailId");
            return (Criteria) this;
        }

        public Criteria andVmDetailIdNotIn(List<Long> values) {
            addCriterion("vm_detail_id not in", values, "vmDetailId");
            return (Criteria) this;
        }

        public Criteria andVmDetailIdBetween(Long value1, Long value2) {
            addCriterion("vm_detail_id between", value1, value2, "vmDetailId");
            return (Criteria) this;
        }

        public Criteria andVmDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("vm_detail_id not between", value1, value2, "vmDetailId");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeIsNull() {
            addCriterion("exception_time is null");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeIsNotNull() {
            addCriterion("exception_time is not null");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeEqualTo(String value) {
            addCriterion("exception_time =", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeNotEqualTo(String value) {
            addCriterion("exception_time <>", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeGreaterThan(String value) {
            addCriterion("exception_time >", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeGreaterThanOrEqualTo(String value) {
            addCriterion("exception_time >=", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeLessThan(String value) {
            addCriterion("exception_time <", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeLessThanOrEqualTo(String value) {
            addCriterion("exception_time <=", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeLike(String value) {
            addCriterion("exception_time like", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeNotLike(String value) {
            addCriterion("exception_time not like", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeIn(List<String> values) {
            addCriterion("exception_time in", values, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeNotIn(List<String> values) {
            addCriterion("exception_time not in", values, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeBetween(String value1, String value2) {
            addCriterion("exception_time between", value1, value2, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeNotBetween(String value1, String value2) {
            addCriterion("exception_time not between", value1, value2, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andRiskIdIsNull() {
            addCriterion("risk_id is null");
            return (Criteria) this;
        }

        public Criteria andRiskIdIsNotNull() {
            addCriterion("risk_id is not null");
            return (Criteria) this;
        }

        public Criteria andRiskIdEqualTo(Long value) {
            addCriterion("risk_id =", value, "riskId");
            return (Criteria) this;
        }

        public Criteria andRiskIdNotEqualTo(Long value) {
            addCriterion("risk_id <>", value, "riskId");
            return (Criteria) this;
        }

        public Criteria andRiskIdGreaterThan(Long value) {
            addCriterion("risk_id >", value, "riskId");
            return (Criteria) this;
        }

        public Criteria andRiskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("risk_id >=", value, "riskId");
            return (Criteria) this;
        }

        public Criteria andRiskIdLessThan(Long value) {
            addCriterion("risk_id <", value, "riskId");
            return (Criteria) this;
        }

        public Criteria andRiskIdLessThanOrEqualTo(Long value) {
            addCriterion("risk_id <=", value, "riskId");
            return (Criteria) this;
        }

        public Criteria andRiskIdIn(List<Long> values) {
            addCriterion("risk_id in", values, "riskId");
            return (Criteria) this;
        }

        public Criteria andRiskIdNotIn(List<Long> values) {
            addCriterion("risk_id not in", values, "riskId");
            return (Criteria) this;
        }

        public Criteria andRiskIdBetween(Long value1, Long value2) {
            addCriterion("risk_id between", value1, value2, "riskId");
            return (Criteria) this;
        }

        public Criteria andRiskIdNotBetween(Long value1, Long value2) {
            addCriterion("risk_id not between", value1, value2, "riskId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdIsNull() {
            addCriterion("instance_id is null");
            return (Criteria) this;
        }

        public Criteria andInstanceIdIsNotNull() {
            addCriterion("instance_id is not null");
            return (Criteria) this;
        }

        public Criteria andInstanceIdEqualTo(String value) {
            addCriterion("instance_id =", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdNotEqualTo(String value) {
            addCriterion("instance_id <>", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdGreaterThan(String value) {
            addCriterion("instance_id >", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("instance_id >=", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdLessThan(String value) {
            addCriterion("instance_id <", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("instance_id <=", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdLike(String value) {
            addCriterion("instance_id like", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdNotLike(String value) {
            addCriterion("instance_id not like", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdIn(List<String> values) {
            addCriterion("instance_id in", values, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdNotIn(List<String> values) {
            addCriterion("instance_id not in", values, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdBetween(String value1, String value2) {
            addCriterion("instance_id between", value1, value2, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdNotBetween(String value1, String value2) {
            addCriterion("instance_id not between", value1, value2, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceCreateTimeIsNull() {
            addCriterion("instance_create_time is null");
            return (Criteria) this;
        }

        public Criteria andInstanceCreateTimeIsNotNull() {
            addCriterion("instance_create_time is not null");
            return (Criteria) this;
        }

        public Criteria andInstanceCreateTimeEqualTo(String value) {
            addCriterion("instance_create_time =", value, "instanceCreateTime");
            return (Criteria) this;
        }

        public Criteria andInstanceCreateTimeNotEqualTo(String value) {
            addCriterion("instance_create_time <>", value, "instanceCreateTime");
            return (Criteria) this;
        }

        public Criteria andInstanceCreateTimeGreaterThan(String value) {
            addCriterion("instance_create_time >", value, "instanceCreateTime");
            return (Criteria) this;
        }

        public Criteria andInstanceCreateTimeGreaterThanOrEqualTo(String value) {
            addCriterion("instance_create_time >=", value, "instanceCreateTime");
            return (Criteria) this;
        }

        public Criteria andInstanceCreateTimeLessThan(String value) {
            addCriterion("instance_create_time <", value, "instanceCreateTime");
            return (Criteria) this;
        }

        public Criteria andInstanceCreateTimeLessThanOrEqualTo(String value) {
            addCriterion("instance_create_time <=", value, "instanceCreateTime");
            return (Criteria) this;
        }

        public Criteria andInstanceCreateTimeLike(String value) {
            addCriterion("instance_create_time like", value, "instanceCreateTime");
            return (Criteria) this;
        }

        public Criteria andInstanceCreateTimeNotLike(String value) {
            addCriterion("instance_create_time not like", value, "instanceCreateTime");
            return (Criteria) this;
        }

        public Criteria andInstanceCreateTimeIn(List<String> values) {
            addCriterion("instance_create_time in", values, "instanceCreateTime");
            return (Criteria) this;
        }

        public Criteria andInstanceCreateTimeNotIn(List<String> values) {
            addCriterion("instance_create_time not in", values, "instanceCreateTime");
            return (Criteria) this;
        }

        public Criteria andInstanceCreateTimeBetween(String value1, String value2) {
            addCriterion("instance_create_time between", value1, value2, "instanceCreateTime");
            return (Criteria) this;
        }

        public Criteria andInstanceCreateTimeNotBetween(String value1, String value2) {
            addCriterion("instance_create_time not between", value1, value2, "instanceCreateTime");
            return (Criteria) this;
        }

        public Criteria andAliUidIsNull() {
            addCriterion("ali_uid is null");
            return (Criteria) this;
        }

        public Criteria andAliUidIsNotNull() {
            addCriterion("ali_uid is not null");
            return (Criteria) this;
        }

        public Criteria andAliUidEqualTo(String value) {
            addCriterion("ali_uid =", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidNotEqualTo(String value) {
            addCriterion("ali_uid <>", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidGreaterThan(String value) {
            addCriterion("ali_uid >", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidGreaterThanOrEqualTo(String value) {
            addCriterion("ali_uid >=", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidLessThan(String value) {
            addCriterion("ali_uid <", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidLessThanOrEqualTo(String value) {
            addCriterion("ali_uid <=", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidLike(String value) {
            addCriterion("ali_uid like", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidNotLike(String value) {
            addCriterion("ali_uid not like", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidIn(List<String> values) {
            addCriterion("ali_uid in", values, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidNotIn(List<String> values) {
            addCriterion("ali_uid not in", values, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidBetween(String value1, String value2) {
            addCriterion("ali_uid between", value1, value2, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidNotBetween(String value1, String value2) {
            addCriterion("ali_uid not between", value1, value2, "aliUid");
            return (Criteria) this;
        }

        public Criteria andGcLevelIsNull() {
            addCriterion("gc_level is null");
            return (Criteria) this;
        }

        public Criteria andGcLevelIsNotNull() {
            addCriterion("gc_level is not null");
            return (Criteria) this;
        }

        public Criteria andGcLevelEqualTo(String value) {
            addCriterion("gc_level =", value, "gcLevel");
            return (Criteria) this;
        }

        public Criteria andGcLevelNotEqualTo(String value) {
            addCriterion("gc_level <>", value, "gcLevel");
            return (Criteria) this;
        }

        public Criteria andGcLevelGreaterThan(String value) {
            addCriterion("gc_level >", value, "gcLevel");
            return (Criteria) this;
        }

        public Criteria andGcLevelGreaterThanOrEqualTo(String value) {
            addCriterion("gc_level >=", value, "gcLevel");
            return (Criteria) this;
        }

        public Criteria andGcLevelLessThan(String value) {
            addCriterion("gc_level <", value, "gcLevel");
            return (Criteria) this;
        }

        public Criteria andGcLevelLessThanOrEqualTo(String value) {
            addCriterion("gc_level <=", value, "gcLevel");
            return (Criteria) this;
        }

        public Criteria andGcLevelLike(String value) {
            addCriterion("gc_level like", value, "gcLevel");
            return (Criteria) this;
        }

        public Criteria andGcLevelNotLike(String value) {
            addCriterion("gc_level not like", value, "gcLevel");
            return (Criteria) this;
        }

        public Criteria andGcLevelIn(List<String> values) {
            addCriterion("gc_level in", values, "gcLevel");
            return (Criteria) this;
        }

        public Criteria andGcLevelNotIn(List<String> values) {
            addCriterion("gc_level not in", values, "gcLevel");
            return (Criteria) this;
        }

        public Criteria andGcLevelBetween(String value1, String value2) {
            addCriterion("gc_level between", value1, value2, "gcLevel");
            return (Criteria) this;
        }

        public Criteria andGcLevelNotBetween(String value1, String value2) {
            addCriterion("gc_level not between", value1, value2, "gcLevel");
            return (Criteria) this;
        }

        public Criteria andVcpuIsNull() {
            addCriterion("vcpu is null");
            return (Criteria) this;
        }

        public Criteria andVcpuIsNotNull() {
            addCriterion("vcpu is not null");
            return (Criteria) this;
        }

        public Criteria andVcpuEqualTo(Long value) {
            addCriterion("vcpu =", value, "vcpu");
            return (Criteria) this;
        }

        public Criteria andVcpuNotEqualTo(Long value) {
            addCriterion("vcpu <>", value, "vcpu");
            return (Criteria) this;
        }

        public Criteria andVcpuGreaterThan(Long value) {
            addCriterion("vcpu >", value, "vcpu");
            return (Criteria) this;
        }

        public Criteria andVcpuGreaterThanOrEqualTo(Long value) {
            addCriterion("vcpu >=", value, "vcpu");
            return (Criteria) this;
        }

        public Criteria andVcpuLessThan(Long value) {
            addCriterion("vcpu <", value, "vcpu");
            return (Criteria) this;
        }

        public Criteria andVcpuLessThanOrEqualTo(Long value) {
            addCriterion("vcpu <=", value, "vcpu");
            return (Criteria) this;
        }

        public Criteria andVcpuIn(List<Long> values) {
            addCriterion("vcpu in", values, "vcpu");
            return (Criteria) this;
        }

        public Criteria andVcpuNotIn(List<Long> values) {
            addCriterion("vcpu not in", values, "vcpu");
            return (Criteria) this;
        }

        public Criteria andVcpuBetween(Long value1, Long value2) {
            addCriterion("vcpu between", value1, value2, "vcpu");
            return (Criteria) this;
        }

        public Criteria andVcpuNotBetween(Long value1, Long value2) {
            addCriterion("vcpu not between", value1, value2, "vcpu");
            return (Criteria) this;
        }

        public Criteria andOsTypeIsNull() {
            addCriterion("os_type is null");
            return (Criteria) this;
        }

        public Criteria andOsTypeIsNotNull() {
            addCriterion("os_type is not null");
            return (Criteria) this;
        }

        public Criteria andOsTypeEqualTo(String value) {
            addCriterion("os_type =", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeNotEqualTo(String value) {
            addCriterion("os_type <>", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeGreaterThan(String value) {
            addCriterion("os_type >", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeGreaterThanOrEqualTo(String value) {
            addCriterion("os_type >=", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeLessThan(String value) {
            addCriterion("os_type <", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeLessThanOrEqualTo(String value) {
            addCriterion("os_type <=", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeLike(String value) {
            addCriterion("os_type like", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeNotLike(String value) {
            addCriterion("os_type not like", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeIn(List<String> values) {
            addCriterion("os_type in", values, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeNotIn(List<String> values) {
            addCriterion("os_type not in", values, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeBetween(String value1, String value2) {
            addCriterion("os_type between", value1, value2, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeNotBetween(String value1, String value2) {
            addCriterion("os_type not between", value1, value2, "osType");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeIsNull() {
            addCriterion("instance_type is null");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeIsNotNull() {
            addCriterion("instance_type is not null");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeEqualTo(String value) {
            addCriterion("instance_type =", value, "instanceType");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeNotEqualTo(String value) {
            addCriterion("instance_type <>", value, "instanceType");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeGreaterThan(String value) {
            addCriterion("instance_type >", value, "instanceType");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeGreaterThanOrEqualTo(String value) {
            addCriterion("instance_type >=", value, "instanceType");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeLessThan(String value) {
            addCriterion("instance_type <", value, "instanceType");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeLessThanOrEqualTo(String value) {
            addCriterion("instance_type <=", value, "instanceType");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeLike(String value) {
            addCriterion("instance_type like", value, "instanceType");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeNotLike(String value) {
            addCriterion("instance_type not like", value, "instanceType");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeIn(List<String> values) {
            addCriterion("instance_type in", values, "instanceType");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeNotIn(List<String> values) {
            addCriterion("instance_type not in", values, "instanceType");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeBetween(String value1, String value2) {
            addCriterion("instance_type between", value1, value2, "instanceType");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeNotBetween(String value1, String value2) {
            addCriterion("instance_type not between", value1, value2, "instanceType");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyIsNull() {
            addCriterion("instance_type_family is null");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyIsNotNull() {
            addCriterion("instance_type_family is not null");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyEqualTo(String value) {
            addCriterion("instance_type_family =", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyNotEqualTo(String value) {
            addCriterion("instance_type_family <>", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyGreaterThan(String value) {
            addCriterion("instance_type_family >", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyGreaterThanOrEqualTo(String value) {
            addCriterion("instance_type_family >=", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyLessThan(String value) {
            addCriterion("instance_type_family <", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyLessThanOrEqualTo(String value) {
            addCriterion("instance_type_family <=", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyLike(String value) {
            addCriterion("instance_type_family like", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyNotLike(String value) {
            addCriterion("instance_type_family not like", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyIn(List<String> values) {
            addCriterion("instance_type_family in", values, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyNotIn(List<String> values) {
            addCriterion("instance_type_family not in", values, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyBetween(String value1, String value2) {
            addCriterion("instance_type_family between", value1, value2, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyNotBetween(String value1, String value2) {
            addCriterion("instance_type_family not between", value1, value2, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andImageNoIsNull() {
            addCriterion("image_no is null");
            return (Criteria) this;
        }

        public Criteria andImageNoIsNotNull() {
            addCriterion("image_no is not null");
            return (Criteria) this;
        }

        public Criteria andImageNoEqualTo(String value) {
            addCriterion("image_no =", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoNotEqualTo(String value) {
            addCriterion("image_no <>", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoGreaterThan(String value) {
            addCriterion("image_no >", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoGreaterThanOrEqualTo(String value) {
            addCriterion("image_no >=", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoLessThan(String value) {
            addCriterion("image_no <", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoLessThanOrEqualTo(String value) {
            addCriterion("image_no <=", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoLike(String value) {
            addCriterion("image_no like", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoNotLike(String value) {
            addCriterion("image_no not like", value, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoIn(List<String> values) {
            addCriterion("image_no in", values, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoNotIn(List<String> values) {
            addCriterion("image_no not in", values, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoBetween(String value1, String value2) {
            addCriterion("image_no between", value1, value2, "imageNo");
            return (Criteria) this;
        }

        public Criteria andImageNoNotBetween(String value1, String value2) {
            addCriterion("image_no not between", value1, value2, "imageNo");
            return (Criteria) this;
        }

        public Criteria andIsVpcIsNull() {
            addCriterion("is_vpc is null");
            return (Criteria) this;
        }

        public Criteria andIsVpcIsNotNull() {
            addCriterion("is_vpc is not null");
            return (Criteria) this;
        }

        public Criteria andIsVpcEqualTo(String value) {
            addCriterion("is_vpc =", value, "isVpc");
            return (Criteria) this;
        }

        public Criteria andIsVpcNotEqualTo(String value) {
            addCriterion("is_vpc <>", value, "isVpc");
            return (Criteria) this;
        }

        public Criteria andIsVpcGreaterThan(String value) {
            addCriterion("is_vpc >", value, "isVpc");
            return (Criteria) this;
        }

        public Criteria andIsVpcGreaterThanOrEqualTo(String value) {
            addCriterion("is_vpc >=", value, "isVpc");
            return (Criteria) this;
        }

        public Criteria andIsVpcLessThan(String value) {
            addCriterion("is_vpc <", value, "isVpc");
            return (Criteria) this;
        }

        public Criteria andIsVpcLessThanOrEqualTo(String value) {
            addCriterion("is_vpc <=", value, "isVpc");
            return (Criteria) this;
        }

        public Criteria andIsVpcLike(String value) {
            addCriterion("is_vpc like", value, "isVpc");
            return (Criteria) this;
        }

        public Criteria andIsVpcNotLike(String value) {
            addCriterion("is_vpc not like", value, "isVpc");
            return (Criteria) this;
        }

        public Criteria andIsVpcIn(List<String> values) {
            addCriterion("is_vpc in", values, "isVpc");
            return (Criteria) this;
        }

        public Criteria andIsVpcNotIn(List<String> values) {
            addCriterion("is_vpc not in", values, "isVpc");
            return (Criteria) this;
        }

        public Criteria andIsVpcBetween(String value1, String value2) {
            addCriterion("is_vpc between", value1, value2, "isVpc");
            return (Criteria) this;
        }

        public Criteria andIsVpcNotBetween(String value1, String value2) {
            addCriterion("is_vpc not between", value1, value2, "isVpc");
            return (Criteria) this;
        }

        public Criteria andInternetIpTypeIsNull() {
            addCriterion("internet_ip_type is null");
            return (Criteria) this;
        }

        public Criteria andInternetIpTypeIsNotNull() {
            addCriterion("internet_ip_type is not null");
            return (Criteria) this;
        }

        public Criteria andInternetIpTypeEqualTo(String value) {
            addCriterion("internet_ip_type =", value, "internetIpType");
            return (Criteria) this;
        }

        public Criteria andInternetIpTypeNotEqualTo(String value) {
            addCriterion("internet_ip_type <>", value, "internetIpType");
            return (Criteria) this;
        }

        public Criteria andInternetIpTypeGreaterThan(String value) {
            addCriterion("internet_ip_type >", value, "internetIpType");
            return (Criteria) this;
        }

        public Criteria andInternetIpTypeGreaterThanOrEqualTo(String value) {
            addCriterion("internet_ip_type >=", value, "internetIpType");
            return (Criteria) this;
        }

        public Criteria andInternetIpTypeLessThan(String value) {
            addCriterion("internet_ip_type <", value, "internetIpType");
            return (Criteria) this;
        }

        public Criteria andInternetIpTypeLessThanOrEqualTo(String value) {
            addCriterion("internet_ip_type <=", value, "internetIpType");
            return (Criteria) this;
        }

        public Criteria andInternetIpTypeLike(String value) {
            addCriterion("internet_ip_type like", value, "internetIpType");
            return (Criteria) this;
        }

        public Criteria andInternetIpTypeNotLike(String value) {
            addCriterion("internet_ip_type not like", value, "internetIpType");
            return (Criteria) this;
        }

        public Criteria andInternetIpTypeIn(List<String> values) {
            addCriterion("internet_ip_type in", values, "internetIpType");
            return (Criteria) this;
        }

        public Criteria andInternetIpTypeNotIn(List<String> values) {
            addCriterion("internet_ip_type not in", values, "internetIpType");
            return (Criteria) this;
        }

        public Criteria andInternetIpTypeBetween(String value1, String value2) {
            addCriterion("internet_ip_type between", value1, value2, "internetIpType");
            return (Criteria) this;
        }

        public Criteria andInternetIpTypeNotBetween(String value1, String value2) {
            addCriterion("internet_ip_type not between", value1, value2, "internetIpType");
            return (Criteria) this;
        }

        public Criteria andOnEcsIsNull() {
            addCriterion("on_ecs is null");
            return (Criteria) this;
        }

        public Criteria andOnEcsIsNotNull() {
            addCriterion("on_ecs is not null");
            return (Criteria) this;
        }

        public Criteria andOnEcsEqualTo(String value) {
            addCriterion("on_ecs =", value, "onEcs");
            return (Criteria) this;
        }

        public Criteria andOnEcsNotEqualTo(String value) {
            addCriterion("on_ecs <>", value, "onEcs");
            return (Criteria) this;
        }

        public Criteria andOnEcsGreaterThan(String value) {
            addCriterion("on_ecs >", value, "onEcs");
            return (Criteria) this;
        }

        public Criteria andOnEcsGreaterThanOrEqualTo(String value) {
            addCriterion("on_ecs >=", value, "onEcs");
            return (Criteria) this;
        }

        public Criteria andOnEcsLessThan(String value) {
            addCriterion("on_ecs <", value, "onEcs");
            return (Criteria) this;
        }

        public Criteria andOnEcsLessThanOrEqualTo(String value) {
            addCriterion("on_ecs <=", value, "onEcs");
            return (Criteria) this;
        }

        public Criteria andOnEcsLike(String value) {
            addCriterion("on_ecs like", value, "onEcs");
            return (Criteria) this;
        }

        public Criteria andOnEcsNotLike(String value) {
            addCriterion("on_ecs not like", value, "onEcs");
            return (Criteria) this;
        }

        public Criteria andOnEcsIn(List<String> values) {
            addCriterion("on_ecs in", values, "onEcs");
            return (Criteria) this;
        }

        public Criteria andOnEcsNotIn(List<String> values) {
            addCriterion("on_ecs not in", values, "onEcs");
            return (Criteria) this;
        }

        public Criteria andOnEcsBetween(String value1, String value2) {
            addCriterion("on_ecs between", value1, value2, "onEcs");
            return (Criteria) this;
        }

        public Criteria andOnEcsNotBetween(String value1, String value2) {
            addCriterion("on_ecs not between", value1, value2, "onEcs");
            return (Criteria) this;
        }

        public Criteria andEcsCategory1IsNull() {
            addCriterion("ecs_category_1 is null");
            return (Criteria) this;
        }

        public Criteria andEcsCategory1IsNotNull() {
            addCriterion("ecs_category_1 is not null");
            return (Criteria) this;
        }

        public Criteria andEcsCategory1EqualTo(String value) {
            addCriterion("ecs_category_1 =", value, "ecsCategory1");
            return (Criteria) this;
        }

        public Criteria andEcsCategory1NotEqualTo(String value) {
            addCriterion("ecs_category_1 <>", value, "ecsCategory1");
            return (Criteria) this;
        }

        public Criteria andEcsCategory1GreaterThan(String value) {
            addCriterion("ecs_category_1 >", value, "ecsCategory1");
            return (Criteria) this;
        }

        public Criteria andEcsCategory1GreaterThanOrEqualTo(String value) {
            addCriterion("ecs_category_1 >=", value, "ecsCategory1");
            return (Criteria) this;
        }

        public Criteria andEcsCategory1LessThan(String value) {
            addCriterion("ecs_category_1 <", value, "ecsCategory1");
            return (Criteria) this;
        }

        public Criteria andEcsCategory1LessThanOrEqualTo(String value) {
            addCriterion("ecs_category_1 <=", value, "ecsCategory1");
            return (Criteria) this;
        }

        public Criteria andEcsCategory1Like(String value) {
            addCriterion("ecs_category_1 like", value, "ecsCategory1");
            return (Criteria) this;
        }

        public Criteria andEcsCategory1NotLike(String value) {
            addCriterion("ecs_category_1 not like", value, "ecsCategory1");
            return (Criteria) this;
        }

        public Criteria andEcsCategory1In(List<String> values) {
            addCriterion("ecs_category_1 in", values, "ecsCategory1");
            return (Criteria) this;
        }

        public Criteria andEcsCategory1NotIn(List<String> values) {
            addCriterion("ecs_category_1 not in", values, "ecsCategory1");
            return (Criteria) this;
        }

        public Criteria andEcsCategory1Between(String value1, String value2) {
            addCriterion("ecs_category_1 between", value1, value2, "ecsCategory1");
            return (Criteria) this;
        }

        public Criteria andEcsCategory1NotBetween(String value1, String value2) {
            addCriterion("ecs_category_1 not between", value1, value2, "ecsCategory1");
            return (Criteria) this;
        }

        public Criteria andEcsCategory2IsNull() {
            addCriterion("ecs_category_2 is null");
            return (Criteria) this;
        }

        public Criteria andEcsCategory2IsNotNull() {
            addCriterion("ecs_category_2 is not null");
            return (Criteria) this;
        }

        public Criteria andEcsCategory2EqualTo(String value) {
            addCriterion("ecs_category_2 =", value, "ecsCategory2");
            return (Criteria) this;
        }

        public Criteria andEcsCategory2NotEqualTo(String value) {
            addCriterion("ecs_category_2 <>", value, "ecsCategory2");
            return (Criteria) this;
        }

        public Criteria andEcsCategory2GreaterThan(String value) {
            addCriterion("ecs_category_2 >", value, "ecsCategory2");
            return (Criteria) this;
        }

        public Criteria andEcsCategory2GreaterThanOrEqualTo(String value) {
            addCriterion("ecs_category_2 >=", value, "ecsCategory2");
            return (Criteria) this;
        }

        public Criteria andEcsCategory2LessThan(String value) {
            addCriterion("ecs_category_2 <", value, "ecsCategory2");
            return (Criteria) this;
        }

        public Criteria andEcsCategory2LessThanOrEqualTo(String value) {
            addCriterion("ecs_category_2 <=", value, "ecsCategory2");
            return (Criteria) this;
        }

        public Criteria andEcsCategory2Like(String value) {
            addCriterion("ecs_category_2 like", value, "ecsCategory2");
            return (Criteria) this;
        }

        public Criteria andEcsCategory2NotLike(String value) {
            addCriterion("ecs_category_2 not like", value, "ecsCategory2");
            return (Criteria) this;
        }

        public Criteria andEcsCategory2In(List<String> values) {
            addCriterion("ecs_category_2 in", values, "ecsCategory2");
            return (Criteria) this;
        }

        public Criteria andEcsCategory2NotIn(List<String> values) {
            addCriterion("ecs_category_2 not in", values, "ecsCategory2");
            return (Criteria) this;
        }

        public Criteria andEcsCategory2Between(String value1, String value2) {
            addCriterion("ecs_category_2 between", value1, value2, "ecsCategory2");
            return (Criteria) this;
        }

        public Criteria andEcsCategory2NotBetween(String value1, String value2) {
            addCriterion("ecs_category_2 not between", value1, value2, "ecsCategory2");
            return (Criteria) this;
        }

        public Criteria andEcsCategory3IsNull() {
            addCriterion("ecs_category_3 is null");
            return (Criteria) this;
        }

        public Criteria andEcsCategory3IsNotNull() {
            addCriterion("ecs_category_3 is not null");
            return (Criteria) this;
        }

        public Criteria andEcsCategory3EqualTo(String value) {
            addCriterion("ecs_category_3 =", value, "ecsCategory3");
            return (Criteria) this;
        }

        public Criteria andEcsCategory3NotEqualTo(String value) {
            addCriterion("ecs_category_3 <>", value, "ecsCategory3");
            return (Criteria) this;
        }

        public Criteria andEcsCategory3GreaterThan(String value) {
            addCriterion("ecs_category_3 >", value, "ecsCategory3");
            return (Criteria) this;
        }

        public Criteria andEcsCategory3GreaterThanOrEqualTo(String value) {
            addCriterion("ecs_category_3 >=", value, "ecsCategory3");
            return (Criteria) this;
        }

        public Criteria andEcsCategory3LessThan(String value) {
            addCriterion("ecs_category_3 <", value, "ecsCategory3");
            return (Criteria) this;
        }

        public Criteria andEcsCategory3LessThanOrEqualTo(String value) {
            addCriterion("ecs_category_3 <=", value, "ecsCategory3");
            return (Criteria) this;
        }

        public Criteria andEcsCategory3Like(String value) {
            addCriterion("ecs_category_3 like", value, "ecsCategory3");
            return (Criteria) this;
        }

        public Criteria andEcsCategory3NotLike(String value) {
            addCriterion("ecs_category_3 not like", value, "ecsCategory3");
            return (Criteria) this;
        }

        public Criteria andEcsCategory3In(List<String> values) {
            addCriterion("ecs_category_3 in", values, "ecsCategory3");
            return (Criteria) this;
        }

        public Criteria andEcsCategory3NotIn(List<String> values) {
            addCriterion("ecs_category_3 not in", values, "ecsCategory3");
            return (Criteria) this;
        }

        public Criteria andEcsCategory3Between(String value1, String value2) {
            addCriterion("ecs_category_3 between", value1, value2, "ecsCategory3");
            return (Criteria) this;
        }

        public Criteria andEcsCategory3NotBetween(String value1, String value2) {
            addCriterion("ecs_category_3 not between", value1, value2, "ecsCategory3");
            return (Criteria) this;
        }

        public Criteria andIsNoChargeForStopIsNull() {
            addCriterion("is_no_charge_for_stop is null");
            return (Criteria) this;
        }

        public Criteria andIsNoChargeForStopIsNotNull() {
            addCriterion("is_no_charge_for_stop is not null");
            return (Criteria) this;
        }

        public Criteria andIsNoChargeForStopEqualTo(Integer value) {
            addCriterion("is_no_charge_for_stop =", value, "isNoChargeForStop");
            return (Criteria) this;
        }

        public Criteria andIsNoChargeForStopNotEqualTo(Integer value) {
            addCriterion("is_no_charge_for_stop <>", value, "isNoChargeForStop");
            return (Criteria) this;
        }

        public Criteria andIsNoChargeForStopGreaterThan(Integer value) {
            addCriterion("is_no_charge_for_stop >", value, "isNoChargeForStop");
            return (Criteria) this;
        }

        public Criteria andIsNoChargeForStopGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_no_charge_for_stop >=", value, "isNoChargeForStop");
            return (Criteria) this;
        }

        public Criteria andIsNoChargeForStopLessThan(Integer value) {
            addCriterion("is_no_charge_for_stop <", value, "isNoChargeForStop");
            return (Criteria) this;
        }

        public Criteria andIsNoChargeForStopLessThanOrEqualTo(Integer value) {
            addCriterion("is_no_charge_for_stop <=", value, "isNoChargeForStop");
            return (Criteria) this;
        }

        public Criteria andIsNoChargeForStopIn(List<Integer> values) {
            addCriterion("is_no_charge_for_stop in", values, "isNoChargeForStop");
            return (Criteria) this;
        }

        public Criteria andIsNoChargeForStopNotIn(List<Integer> values) {
            addCriterion("is_no_charge_for_stop not in", values, "isNoChargeForStop");
            return (Criteria) this;
        }

        public Criteria andIsNoChargeForStopBetween(Integer value1, Integer value2) {
            addCriterion("is_no_charge_for_stop between", value1, value2, "isNoChargeForStop");
            return (Criteria) this;
        }

        public Criteria andIsNoChargeForStopNotBetween(Integer value1, Integer value2) {
            addCriterion("is_no_charge_for_stop not between", value1, value2, "isNoChargeForStop");
            return (Criteria) this;
        }

        public Criteria andExceptionNameIsNull() {
            addCriterion("exception_name is null");
            return (Criteria) this;
        }

        public Criteria andExceptionNameIsNotNull() {
            addCriterion("exception_name is not null");
            return (Criteria) this;
        }

        public Criteria andExceptionNameEqualTo(String value) {
            addCriterion("exception_name =", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameNotEqualTo(String value) {
            addCriterion("exception_name <>", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameGreaterThan(String value) {
            addCriterion("exception_name >", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameGreaterThanOrEqualTo(String value) {
            addCriterion("exception_name >=", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameLessThan(String value) {
            addCriterion("exception_name <", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameLessThanOrEqualTo(String value) {
            addCriterion("exception_name <=", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameLike(String value) {
            addCriterion("exception_name like", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameNotLike(String value) {
            addCriterion("exception_name not like", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameIn(List<String> values) {
            addCriterion("exception_name in", values, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameNotIn(List<String> values) {
            addCriterion("exception_name not in", values, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameBetween(String value1, String value2) {
            addCriterion("exception_name between", value1, value2, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameNotBetween(String value1, String value2) {
            addCriterion("exception_name not between", value1, value2, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andRecoverTimeIsNull() {
            addCriterion("recover_time is null");
            return (Criteria) this;
        }

        public Criteria andRecoverTimeIsNotNull() {
            addCriterion("recover_time is not null");
            return (Criteria) this;
        }

        public Criteria andRecoverTimeEqualTo(String value) {
            addCriterion("recover_time =", value, "recoverTime");
            return (Criteria) this;
        }

        public Criteria andRecoverTimeNotEqualTo(String value) {
            addCriterion("recover_time <>", value, "recoverTime");
            return (Criteria) this;
        }

        public Criteria andRecoverTimeGreaterThan(String value) {
            addCriterion("recover_time >", value, "recoverTime");
            return (Criteria) this;
        }

        public Criteria andRecoverTimeGreaterThanOrEqualTo(String value) {
            addCriterion("recover_time >=", value, "recoverTime");
            return (Criteria) this;
        }

        public Criteria andRecoverTimeLessThan(String value) {
            addCriterion("recover_time <", value, "recoverTime");
            return (Criteria) this;
        }

        public Criteria andRecoverTimeLessThanOrEqualTo(String value) {
            addCriterion("recover_time <=", value, "recoverTime");
            return (Criteria) this;
        }

        public Criteria andRecoverTimeLike(String value) {
            addCriterion("recover_time like", value, "recoverTime");
            return (Criteria) this;
        }

        public Criteria andRecoverTimeNotLike(String value) {
            addCriterion("recover_time not like", value, "recoverTime");
            return (Criteria) this;
        }

        public Criteria andRecoverTimeIn(List<String> values) {
            addCriterion("recover_time in", values, "recoverTime");
            return (Criteria) this;
        }

        public Criteria andRecoverTimeNotIn(List<String> values) {
            addCriterion("recover_time not in", values, "recoverTime");
            return (Criteria) this;
        }

        public Criteria andRecoverTimeBetween(String value1, String value2) {
            addCriterion("recover_time between", value1, value2, "recoverTime");
            return (Criteria) this;
        }

        public Criteria andRecoverTimeNotBetween(String value1, String value2) {
            addCriterion("recover_time not between", value1, value2, "recoverTime");
            return (Criteria) this;
        }

        public Criteria andNcIpIsNull() {
            addCriterion("nc_ip is null");
            return (Criteria) this;
        }

        public Criteria andNcIpIsNotNull() {
            addCriterion("nc_ip is not null");
            return (Criteria) this;
        }

        public Criteria andNcIpEqualTo(String value) {
            addCriterion("nc_ip =", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpNotEqualTo(String value) {
            addCriterion("nc_ip <>", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpGreaterThan(String value) {
            addCriterion("nc_ip >", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpGreaterThanOrEqualTo(String value) {
            addCriterion("nc_ip >=", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpLessThan(String value) {
            addCriterion("nc_ip <", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpLessThanOrEqualTo(String value) {
            addCriterion("nc_ip <=", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpLike(String value) {
            addCriterion("nc_ip like", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpNotLike(String value) {
            addCriterion("nc_ip not like", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpIn(List<String> values) {
            addCriterion("nc_ip in", values, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpNotIn(List<String> values) {
            addCriterion("nc_ip not in", values, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpBetween(String value1, String value2) {
            addCriterion("nc_ip between", value1, value2, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpNotBetween(String value1, String value2) {
            addCriterion("nc_ip not between", value1, value2, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIdIsNull() {
            addCriterion("nc_id is null");
            return (Criteria) this;
        }

        public Criteria andNcIdIsNotNull() {
            addCriterion("nc_id is not null");
            return (Criteria) this;
        }

        public Criteria andNcIdEqualTo(String value) {
            addCriterion("nc_id =", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdNotEqualTo(String value) {
            addCriterion("nc_id <>", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdGreaterThan(String value) {
            addCriterion("nc_id >", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdGreaterThanOrEqualTo(String value) {
            addCriterion("nc_id >=", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdLessThan(String value) {
            addCriterion("nc_id <", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdLessThanOrEqualTo(String value) {
            addCriterion("nc_id <=", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdLike(String value) {
            addCriterion("nc_id like", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdNotLike(String value) {
            addCriterion("nc_id not like", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdIn(List<String> values) {
            addCriterion("nc_id in", values, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdNotIn(List<String> values) {
            addCriterion("nc_id not in", values, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdBetween(String value1, String value2) {
            addCriterion("nc_id between", value1, value2, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdNotBetween(String value1, String value2) {
            addCriterion("nc_id not between", value1, value2, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcSnIsNull() {
            addCriterion("nc_sn is null");
            return (Criteria) this;
        }

        public Criteria andNcSnIsNotNull() {
            addCriterion("nc_sn is not null");
            return (Criteria) this;
        }

        public Criteria andNcSnEqualTo(String value) {
            addCriterion("nc_sn =", value, "ncSn");
            return (Criteria) this;
        }

        public Criteria andNcSnNotEqualTo(String value) {
            addCriterion("nc_sn <>", value, "ncSn");
            return (Criteria) this;
        }

        public Criteria andNcSnGreaterThan(String value) {
            addCriterion("nc_sn >", value, "ncSn");
            return (Criteria) this;
        }

        public Criteria andNcSnGreaterThanOrEqualTo(String value) {
            addCriterion("nc_sn >=", value, "ncSn");
            return (Criteria) this;
        }

        public Criteria andNcSnLessThan(String value) {
            addCriterion("nc_sn <", value, "ncSn");
            return (Criteria) this;
        }

        public Criteria andNcSnLessThanOrEqualTo(String value) {
            addCriterion("nc_sn <=", value, "ncSn");
            return (Criteria) this;
        }

        public Criteria andNcSnLike(String value) {
            addCriterion("nc_sn like", value, "ncSn");
            return (Criteria) this;
        }

        public Criteria andNcSnNotLike(String value) {
            addCriterion("nc_sn not like", value, "ncSn");
            return (Criteria) this;
        }

        public Criteria andNcSnIn(List<String> values) {
            addCriterion("nc_sn in", values, "ncSn");
            return (Criteria) this;
        }

        public Criteria andNcSnNotIn(List<String> values) {
            addCriterion("nc_sn not in", values, "ncSn");
            return (Criteria) this;
        }

        public Criteria andNcSnBetween(String value1, String value2) {
            addCriterion("nc_sn between", value1, value2, "ncSn");
            return (Criteria) this;
        }

        public Criteria andNcSnNotBetween(String value1, String value2) {
            addCriterion("nc_sn not between", value1, value2, "ncSn");
            return (Criteria) this;
        }

        public Criteria andHostnameIsNull() {
            addCriterion("hostname is null");
            return (Criteria) this;
        }

        public Criteria andHostnameIsNotNull() {
            addCriterion("hostname is not null");
            return (Criteria) this;
        }

        public Criteria andHostnameEqualTo(String value) {
            addCriterion("hostname =", value, "hostname");
            return (Criteria) this;
        }

        public Criteria andHostnameNotEqualTo(String value) {
            addCriterion("hostname <>", value, "hostname");
            return (Criteria) this;
        }

        public Criteria andHostnameGreaterThan(String value) {
            addCriterion("hostname >", value, "hostname");
            return (Criteria) this;
        }

        public Criteria andHostnameGreaterThanOrEqualTo(String value) {
            addCriterion("hostname >=", value, "hostname");
            return (Criteria) this;
        }

        public Criteria andHostnameLessThan(String value) {
            addCriterion("hostname <", value, "hostname");
            return (Criteria) this;
        }

        public Criteria andHostnameLessThanOrEqualTo(String value) {
            addCriterion("hostname <=", value, "hostname");
            return (Criteria) this;
        }

        public Criteria andHostnameLike(String value) {
            addCriterion("hostname like", value, "hostname");
            return (Criteria) this;
        }

        public Criteria andHostnameNotLike(String value) {
            addCriterion("hostname not like", value, "hostname");
            return (Criteria) this;
        }

        public Criteria andHostnameIn(List<String> values) {
            addCriterion("hostname in", values, "hostname");
            return (Criteria) this;
        }

        public Criteria andHostnameNotIn(List<String> values) {
            addCriterion("hostname not in", values, "hostname");
            return (Criteria) this;
        }

        public Criteria andHostnameBetween(String value1, String value2) {
            addCriterion("hostname between", value1, value2, "hostname");
            return (Criteria) this;
        }

        public Criteria andHostnameNotBetween(String value1, String value2) {
            addCriterion("hostname not between", value1, value2, "hostname");
            return (Criteria) this;
        }

        public Criteria andClusterIsNull() {
            addCriterion("`cluster` is null");
            return (Criteria) this;
        }

        public Criteria andClusterIsNotNull() {
            addCriterion("`cluster` is not null");
            return (Criteria) this;
        }

        public Criteria andClusterEqualTo(String value) {
            addCriterion("`cluster` =", value, "cluster");
            return (Criteria) this;
        }

        public Criteria andClusterNotEqualTo(String value) {
            addCriterion("`cluster` <>", value, "cluster");
            return (Criteria) this;
        }

        public Criteria andClusterGreaterThan(String value) {
            addCriterion("`cluster` >", value, "cluster");
            return (Criteria) this;
        }

        public Criteria andClusterGreaterThanOrEqualTo(String value) {
            addCriterion("`cluster` >=", value, "cluster");
            return (Criteria) this;
        }

        public Criteria andClusterLessThan(String value) {
            addCriterion("`cluster` <", value, "cluster");
            return (Criteria) this;
        }

        public Criteria andClusterLessThanOrEqualTo(String value) {
            addCriterion("`cluster` <=", value, "cluster");
            return (Criteria) this;
        }

        public Criteria andClusterLike(String value) {
            addCriterion("`cluster` like", value, "cluster");
            return (Criteria) this;
        }

        public Criteria andClusterNotLike(String value) {
            addCriterion("`cluster` not like", value, "cluster");
            return (Criteria) this;
        }

        public Criteria andClusterIn(List<String> values) {
            addCriterion("`cluster` in", values, "cluster");
            return (Criteria) this;
        }

        public Criteria andClusterNotIn(List<String> values) {
            addCriterion("`cluster` not in", values, "cluster");
            return (Criteria) this;
        }

        public Criteria andClusterBetween(String value1, String value2) {
            addCriterion("`cluster` between", value1, value2, "cluster");
            return (Criteria) this;
        }

        public Criteria andClusterNotBetween(String value1, String value2) {
            addCriterion("`cluster` not between", value1, value2, "cluster");
            return (Criteria) this;
        }

        public Criteria andClusterUsageIsNull() {
            addCriterion("cluster_usage is null");
            return (Criteria) this;
        }

        public Criteria andClusterUsageIsNotNull() {
            addCriterion("cluster_usage is not null");
            return (Criteria) this;
        }

        public Criteria andClusterUsageEqualTo(String value) {
            addCriterion("cluster_usage =", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageNotEqualTo(String value) {
            addCriterion("cluster_usage <>", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageGreaterThan(String value) {
            addCriterion("cluster_usage >", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageGreaterThanOrEqualTo(String value) {
            addCriterion("cluster_usage >=", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageLessThan(String value) {
            addCriterion("cluster_usage <", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageLessThanOrEqualTo(String value) {
            addCriterion("cluster_usage <=", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageLike(String value) {
            addCriterion("cluster_usage like", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageNotLike(String value) {
            addCriterion("cluster_usage not like", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageIn(List<String> values) {
            addCriterion("cluster_usage in", values, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageNotIn(List<String> values) {
            addCriterion("cluster_usage not in", values, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageBetween(String value1, String value2) {
            addCriterion("cluster_usage between", value1, value2, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageNotBetween(String value1, String value2) {
            addCriterion("cluster_usage not between", value1, value2, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andAzoneIsNull() {
            addCriterion("azone is null");
            return (Criteria) this;
        }

        public Criteria andAzoneIsNotNull() {
            addCriterion("azone is not null");
            return (Criteria) this;
        }

        public Criteria andAzoneEqualTo(String value) {
            addCriterion("azone =", value, "azone");
            return (Criteria) this;
        }

        public Criteria andAzoneNotEqualTo(String value) {
            addCriterion("azone <>", value, "azone");
            return (Criteria) this;
        }

        public Criteria andAzoneGreaterThan(String value) {
            addCriterion("azone >", value, "azone");
            return (Criteria) this;
        }

        public Criteria andAzoneGreaterThanOrEqualTo(String value) {
            addCriterion("azone >=", value, "azone");
            return (Criteria) this;
        }

        public Criteria andAzoneLessThan(String value) {
            addCriterion("azone <", value, "azone");
            return (Criteria) this;
        }

        public Criteria andAzoneLessThanOrEqualTo(String value) {
            addCriterion("azone <=", value, "azone");
            return (Criteria) this;
        }

        public Criteria andAzoneLike(String value) {
            addCriterion("azone like", value, "azone");
            return (Criteria) this;
        }

        public Criteria andAzoneNotLike(String value) {
            addCriterion("azone not like", value, "azone");
            return (Criteria) this;
        }

        public Criteria andAzoneIn(List<String> values) {
            addCriterion("azone in", values, "azone");
            return (Criteria) this;
        }

        public Criteria andAzoneNotIn(List<String> values) {
            addCriterion("azone not in", values, "azone");
            return (Criteria) this;
        }

        public Criteria andAzoneBetween(String value1, String value2) {
            addCriterion("azone between", value1, value2, "azone");
            return (Criteria) this;
        }

        public Criteria andAzoneNotBetween(String value1, String value2) {
            addCriterion("azone not between", value1, value2, "azone");
            return (Criteria) this;
        }

        public Criteria andRegionIsNull() {
            addCriterion("region is null");
            return (Criteria) this;
        }

        public Criteria andRegionIsNotNull() {
            addCriterion("region is not null");
            return (Criteria) this;
        }

        public Criteria andRegionEqualTo(String value) {
            addCriterion("region =", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotEqualTo(String value) {
            addCriterion("region <>", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionGreaterThan(String value) {
            addCriterion("region >", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionGreaterThanOrEqualTo(String value) {
            addCriterion("region >=", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLessThan(String value) {
            addCriterion("region <", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLessThanOrEqualTo(String value) {
            addCriterion("region <=", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLike(String value) {
            addCriterion("region like", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotLike(String value) {
            addCriterion("region not like", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionIn(List<String> values) {
            addCriterion("region in", values, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotIn(List<String> values) {
            addCriterion("region not in", values, "region");
            return (Criteria) this;
        }

        public Criteria andRegionBetween(String value1, String value2) {
            addCriterion("region between", value1, value2, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotBetween(String value1, String value2) {
            addCriterion("region not between", value1, value2, "region");
            return (Criteria) this;
        }

        public Criteria andIdcIsNull() {
            addCriterion("idc is null");
            return (Criteria) this;
        }

        public Criteria andIdcIsNotNull() {
            addCriterion("idc is not null");
            return (Criteria) this;
        }

        public Criteria andIdcEqualTo(String value) {
            addCriterion("idc =", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcNotEqualTo(String value) {
            addCriterion("idc <>", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcGreaterThan(String value) {
            addCriterion("idc >", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcGreaterThanOrEqualTo(String value) {
            addCriterion("idc >=", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcLessThan(String value) {
            addCriterion("idc <", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcLessThanOrEqualTo(String value) {
            addCriterion("idc <=", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcLike(String value) {
            addCriterion("idc like", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcNotLike(String value) {
            addCriterion("idc not like", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcIn(List<String> values) {
            addCriterion("idc in", values, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcNotIn(List<String> values) {
            addCriterion("idc not in", values, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcBetween(String value1, String value2) {
            addCriterion("idc between", value1, value2, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcNotBetween(String value1, String value2) {
            addCriterion("idc not between", value1, value2, "idc");
            return (Criteria) this;
        }

        public Criteria andRoomIsNull() {
            addCriterion("room is null");
            return (Criteria) this;
        }

        public Criteria andRoomIsNotNull() {
            addCriterion("room is not null");
            return (Criteria) this;
        }

        public Criteria andRoomEqualTo(String value) {
            addCriterion("room =", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomNotEqualTo(String value) {
            addCriterion("room <>", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomGreaterThan(String value) {
            addCriterion("room >", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomGreaterThanOrEqualTo(String value) {
            addCriterion("room >=", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomLessThan(String value) {
            addCriterion("room <", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomLessThanOrEqualTo(String value) {
            addCriterion("room <=", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomLike(String value) {
            addCriterion("room like", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomNotLike(String value) {
            addCriterion("room not like", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomIn(List<String> values) {
            addCriterion("room in", values, "room");
            return (Criteria) this;
        }

        public Criteria andRoomNotIn(List<String> values) {
            addCriterion("room not in", values, "room");
            return (Criteria) this;
        }

        public Criteria andRoomBetween(String value1, String value2) {
            addCriterion("room between", value1, value2, "room");
            return (Criteria) this;
        }

        public Criteria andRoomNotBetween(String value1, String value2) {
            addCriterion("room not between", value1, value2, "room");
            return (Criteria) this;
        }

        public Criteria andRackIsNull() {
            addCriterion("rack is null");
            return (Criteria) this;
        }

        public Criteria andRackIsNotNull() {
            addCriterion("rack is not null");
            return (Criteria) this;
        }

        public Criteria andRackEqualTo(String value) {
            addCriterion("rack =", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackNotEqualTo(String value) {
            addCriterion("rack <>", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackGreaterThan(String value) {
            addCriterion("rack >", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackGreaterThanOrEqualTo(String value) {
            addCriterion("rack >=", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackLessThan(String value) {
            addCriterion("rack <", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackLessThanOrEqualTo(String value) {
            addCriterion("rack <=", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackLike(String value) {
            addCriterion("rack like", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackNotLike(String value) {
            addCriterion("rack not like", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackIn(List<String> values) {
            addCriterion("rack in", values, "rack");
            return (Criteria) this;
        }

        public Criteria andRackNotIn(List<String> values) {
            addCriterion("rack not in", values, "rack");
            return (Criteria) this;
        }

        public Criteria andRackBetween(String value1, String value2) {
            addCriterion("rack between", value1, value2, "rack");
            return (Criteria) this;
        }

        public Criteria andRackNotBetween(String value1, String value2) {
            addCriterion("rack not between", value1, value2, "rack");
            return (Criteria) this;
        }

        public Criteria andAswIdIsNull() {
            addCriterion("asw_id is null");
            return (Criteria) this;
        }

        public Criteria andAswIdIsNotNull() {
            addCriterion("asw_id is not null");
            return (Criteria) this;
        }

        public Criteria andAswIdEqualTo(String value) {
            addCriterion("asw_id =", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdNotEqualTo(String value) {
            addCriterion("asw_id <>", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdGreaterThan(String value) {
            addCriterion("asw_id >", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdGreaterThanOrEqualTo(String value) {
            addCriterion("asw_id >=", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdLessThan(String value) {
            addCriterion("asw_id <", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdLessThanOrEqualTo(String value) {
            addCriterion("asw_id <=", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdLike(String value) {
            addCriterion("asw_id like", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdNotLike(String value) {
            addCriterion("asw_id not like", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdIn(List<String> values) {
            addCriterion("asw_id in", values, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdNotIn(List<String> values) {
            addCriterion("asw_id not in", values, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdBetween(String value1, String value2) {
            addCriterion("asw_id between", value1, value2, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdNotBetween(String value1, String value2) {
            addCriterion("asw_id not between", value1, value2, "aswId");
            return (Criteria) this;
        }

        public Criteria andVcpuModIsNull() {
            addCriterion("vcpu_mod is null");
            return (Criteria) this;
        }

        public Criteria andVcpuModIsNotNull() {
            addCriterion("vcpu_mod is not null");
            return (Criteria) this;
        }

        public Criteria andVcpuModEqualTo(String value) {
            addCriterion("vcpu_mod =", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModNotEqualTo(String value) {
            addCriterion("vcpu_mod <>", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModGreaterThan(String value) {
            addCriterion("vcpu_mod >", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModGreaterThanOrEqualTo(String value) {
            addCriterion("vcpu_mod >=", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModLessThan(String value) {
            addCriterion("vcpu_mod <", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModLessThanOrEqualTo(String value) {
            addCriterion("vcpu_mod <=", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModLike(String value) {
            addCriterion("vcpu_mod like", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModNotLike(String value) {
            addCriterion("vcpu_mod not like", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModIn(List<String> values) {
            addCriterion("vcpu_mod in", values, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModNotIn(List<String> values) {
            addCriterion("vcpu_mod not in", values, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModBetween(String value1, String value2) {
            addCriterion("vcpu_mod between", value1, value2, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModNotBetween(String value1, String value2) {
            addCriterion("vcpu_mod not between", value1, value2, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelIsNull() {
            addCriterion("physical_model is null");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelIsNotNull() {
            addCriterion("physical_model is not null");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelEqualTo(String value) {
            addCriterion("physical_model =", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelNotEqualTo(String value) {
            addCriterion("physical_model <>", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelGreaterThan(String value) {
            addCriterion("physical_model >", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelGreaterThanOrEqualTo(String value) {
            addCriterion("physical_model >=", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelLessThan(String value) {
            addCriterion("physical_model <", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelLessThanOrEqualTo(String value) {
            addCriterion("physical_model <=", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelLike(String value) {
            addCriterion("physical_model like", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelNotLike(String value) {
            addCriterion("physical_model not like", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelIn(List<String> values) {
            addCriterion("physical_model in", values, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelNotIn(List<String> values) {
            addCriterion("physical_model not in", values, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelBetween(String value1, String value2) {
            addCriterion("physical_model between", value1, value2, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelNotBetween(String value1, String value2) {
            addCriterion("physical_model not between", value1, value2, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andCpuModelIsNull() {
            addCriterion("cpu_model is null");
            return (Criteria) this;
        }

        public Criteria andCpuModelIsNotNull() {
            addCriterion("cpu_model is not null");
            return (Criteria) this;
        }

        public Criteria andCpuModelEqualTo(String value) {
            addCriterion("cpu_model =", value, "cpuModel");
            return (Criteria) this;
        }

        public Criteria andCpuModelNotEqualTo(String value) {
            addCriterion("cpu_model <>", value, "cpuModel");
            return (Criteria) this;
        }

        public Criteria andCpuModelGreaterThan(String value) {
            addCriterion("cpu_model >", value, "cpuModel");
            return (Criteria) this;
        }

        public Criteria andCpuModelGreaterThanOrEqualTo(String value) {
            addCriterion("cpu_model >=", value, "cpuModel");
            return (Criteria) this;
        }

        public Criteria andCpuModelLessThan(String value) {
            addCriterion("cpu_model <", value, "cpuModel");
            return (Criteria) this;
        }

        public Criteria andCpuModelLessThanOrEqualTo(String value) {
            addCriterion("cpu_model <=", value, "cpuModel");
            return (Criteria) this;
        }

        public Criteria andCpuModelLike(String value) {
            addCriterion("cpu_model like", value, "cpuModel");
            return (Criteria) this;
        }

        public Criteria andCpuModelNotLike(String value) {
            addCriterion("cpu_model not like", value, "cpuModel");
            return (Criteria) this;
        }

        public Criteria andCpuModelIn(List<String> values) {
            addCriterion("cpu_model in", values, "cpuModel");
            return (Criteria) this;
        }

        public Criteria andCpuModelNotIn(List<String> values) {
            addCriterion("cpu_model not in", values, "cpuModel");
            return (Criteria) this;
        }

        public Criteria andCpuModelBetween(String value1, String value2) {
            addCriterion("cpu_model between", value1, value2, "cpuModel");
            return (Criteria) this;
        }

        public Criteria andCpuModelNotBetween(String value1, String value2) {
            addCriterion("cpu_model not between", value1, value2, "cpuModel");
            return (Criteria) this;
        }

        public Criteria andNcVcpuIsNull() {
            addCriterion("nc_vcpu is null");
            return (Criteria) this;
        }

        public Criteria andNcVcpuIsNotNull() {
            addCriterion("nc_vcpu is not null");
            return (Criteria) this;
        }

        public Criteria andNcVcpuEqualTo(String value) {
            addCriterion("nc_vcpu =", value, "ncVcpu");
            return (Criteria) this;
        }

        public Criteria andNcVcpuNotEqualTo(String value) {
            addCriterion("nc_vcpu <>", value, "ncVcpu");
            return (Criteria) this;
        }

        public Criteria andNcVcpuGreaterThan(String value) {
            addCriterion("nc_vcpu >", value, "ncVcpu");
            return (Criteria) this;
        }

        public Criteria andNcVcpuGreaterThanOrEqualTo(String value) {
            addCriterion("nc_vcpu >=", value, "ncVcpu");
            return (Criteria) this;
        }

        public Criteria andNcVcpuLessThan(String value) {
            addCriterion("nc_vcpu <", value, "ncVcpu");
            return (Criteria) this;
        }

        public Criteria andNcVcpuLessThanOrEqualTo(String value) {
            addCriterion("nc_vcpu <=", value, "ncVcpu");
            return (Criteria) this;
        }

        public Criteria andNcVcpuLike(String value) {
            addCriterion("nc_vcpu like", value, "ncVcpu");
            return (Criteria) this;
        }

        public Criteria andNcVcpuNotLike(String value) {
            addCriterion("nc_vcpu not like", value, "ncVcpu");
            return (Criteria) this;
        }

        public Criteria andNcVcpuIn(List<String> values) {
            addCriterion("nc_vcpu in", values, "ncVcpu");
            return (Criteria) this;
        }

        public Criteria andNcVcpuNotIn(List<String> values) {
            addCriterion("nc_vcpu not in", values, "ncVcpu");
            return (Criteria) this;
        }

        public Criteria andNcVcpuBetween(String value1, String value2) {
            addCriterion("nc_vcpu between", value1, value2, "ncVcpu");
            return (Criteria) this;
        }

        public Criteria andNcVcpuNotBetween(String value1, String value2) {
            addCriterion("nc_vcpu not between", value1, value2, "ncVcpu");
            return (Criteria) this;
        }

        public Criteria andAvsVersionIsNull() {
            addCriterion("avs_version is null");
            return (Criteria) this;
        }

        public Criteria andAvsVersionIsNotNull() {
            addCriterion("avs_version is not null");
            return (Criteria) this;
        }

        public Criteria andAvsVersionEqualTo(String value) {
            addCriterion("avs_version =", value, "avsVersion");
            return (Criteria) this;
        }

        public Criteria andAvsVersionNotEqualTo(String value) {
            addCriterion("avs_version <>", value, "avsVersion");
            return (Criteria) this;
        }

        public Criteria andAvsVersionGreaterThan(String value) {
            addCriterion("avs_version >", value, "avsVersion");
            return (Criteria) this;
        }

        public Criteria andAvsVersionGreaterThanOrEqualTo(String value) {
            addCriterion("avs_version >=", value, "avsVersion");
            return (Criteria) this;
        }

        public Criteria andAvsVersionLessThan(String value) {
            addCriterion("avs_version <", value, "avsVersion");
            return (Criteria) this;
        }

        public Criteria andAvsVersionLessThanOrEqualTo(String value) {
            addCriterion("avs_version <=", value, "avsVersion");
            return (Criteria) this;
        }

        public Criteria andAvsVersionLike(String value) {
            addCriterion("avs_version like", value, "avsVersion");
            return (Criteria) this;
        }

        public Criteria andAvsVersionNotLike(String value) {
            addCriterion("avs_version not like", value, "avsVersion");
            return (Criteria) this;
        }

        public Criteria andAvsVersionIn(List<String> values) {
            addCriterion("avs_version in", values, "avsVersion");
            return (Criteria) this;
        }

        public Criteria andAvsVersionNotIn(List<String> values) {
            addCriterion("avs_version not in", values, "avsVersion");
            return (Criteria) this;
        }

        public Criteria andAvsVersionBetween(String value1, String value2) {
            addCriterion("avs_version between", value1, value2, "avsVersion");
            return (Criteria) this;
        }

        public Criteria andAvsVersionNotBetween(String value1, String value2) {
            addCriterion("avs_version not between", value1, value2, "avsVersion");
            return (Criteria) this;
        }

        public Criteria andNcCategoryIsNull() {
            addCriterion("nc_category is null");
            return (Criteria) this;
        }

        public Criteria andNcCategoryIsNotNull() {
            addCriterion("nc_category is not null");
            return (Criteria) this;
        }

        public Criteria andNcCategoryEqualTo(String value) {
            addCriterion("nc_category =", value, "ncCategory");
            return (Criteria) this;
        }

        public Criteria andNcCategoryNotEqualTo(String value) {
            addCriterion("nc_category <>", value, "ncCategory");
            return (Criteria) this;
        }

        public Criteria andNcCategoryGreaterThan(String value) {
            addCriterion("nc_category >", value, "ncCategory");
            return (Criteria) this;
        }

        public Criteria andNcCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("nc_category >=", value, "ncCategory");
            return (Criteria) this;
        }

        public Criteria andNcCategoryLessThan(String value) {
            addCriterion("nc_category <", value, "ncCategory");
            return (Criteria) this;
        }

        public Criteria andNcCategoryLessThanOrEqualTo(String value) {
            addCriterion("nc_category <=", value, "ncCategory");
            return (Criteria) this;
        }

        public Criteria andNcCategoryLike(String value) {
            addCriterion("nc_category like", value, "ncCategory");
            return (Criteria) this;
        }

        public Criteria andNcCategoryNotLike(String value) {
            addCriterion("nc_category not like", value, "ncCategory");
            return (Criteria) this;
        }

        public Criteria andNcCategoryIn(List<String> values) {
            addCriterion("nc_category in", values, "ncCategory");
            return (Criteria) this;
        }

        public Criteria andNcCategoryNotIn(List<String> values) {
            addCriterion("nc_category not in", values, "ncCategory");
            return (Criteria) this;
        }

        public Criteria andNcCategoryBetween(String value1, String value2) {
            addCriterion("nc_category between", value1, value2, "ncCategory");
            return (Criteria) this;
        }

        public Criteria andNcCategoryNotBetween(String value1, String value2) {
            addCriterion("nc_category not between", value1, value2, "ncCategory");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNull() {
            addCriterion("product_name is null");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNotNull() {
            addCriterion("product_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductNameEqualTo(String value) {
            addCriterion("product_name =", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotEqualTo(String value) {
            addCriterion("product_name <>", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThan(String value) {
            addCriterion("product_name >", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_name >=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThan(String value) {
            addCriterion("product_name <", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThanOrEqualTo(String value) {
            addCriterion("product_name <=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLike(String value) {
            addCriterion("product_name like", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotLike(String value) {
            addCriterion("product_name not like", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameIn(List<String> values) {
            addCriterion("product_name in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotIn(List<String> values) {
            addCriterion("product_name not in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameBetween(String value1, String value2) {
            addCriterion("product_name between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotBetween(String value1, String value2) {
            addCriterion("product_name not between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andVirtTypeIsNull() {
            addCriterion("virt_type is null");
            return (Criteria) this;
        }

        public Criteria andVirtTypeIsNotNull() {
            addCriterion("virt_type is not null");
            return (Criteria) this;
        }

        public Criteria andVirtTypeEqualTo(String value) {
            addCriterion("virt_type =", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeNotEqualTo(String value) {
            addCriterion("virt_type <>", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeGreaterThan(String value) {
            addCriterion("virt_type >", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeGreaterThanOrEqualTo(String value) {
            addCriterion("virt_type >=", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeLessThan(String value) {
            addCriterion("virt_type <", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeLessThanOrEqualTo(String value) {
            addCriterion("virt_type <=", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeLike(String value) {
            addCriterion("virt_type like", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeNotLike(String value) {
            addCriterion("virt_type not like", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeIn(List<String> values) {
            addCriterion("virt_type in", values, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeNotIn(List<String> values) {
            addCriterion("virt_type not in", values, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeBetween(String value1, String value2) {
            addCriterion("virt_type between", value1, value2, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeNotBetween(String value1, String value2) {
            addCriterion("virt_type not between", value1, value2, "virtType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeIsNull() {
            addCriterion("river_type is null");
            return (Criteria) this;
        }

        public Criteria andRiverTypeIsNotNull() {
            addCriterion("river_type is not null");
            return (Criteria) this;
        }

        public Criteria andRiverTypeEqualTo(String value) {
            addCriterion("river_type =", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeNotEqualTo(String value) {
            addCriterion("river_type <>", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeGreaterThan(String value) {
            addCriterion("river_type >", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeGreaterThanOrEqualTo(String value) {
            addCriterion("river_type >=", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeLessThan(String value) {
            addCriterion("river_type <", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeLessThanOrEqualTo(String value) {
            addCriterion("river_type <=", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeLike(String value) {
            addCriterion("river_type like", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeNotLike(String value) {
            addCriterion("river_type not like", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeIn(List<String> values) {
            addCriterion("river_type in", values, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeNotIn(List<String> values) {
            addCriterion("river_type not in", values, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeBetween(String value1, String value2) {
            addCriterion("river_type between", value1, value2, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeNotBetween(String value1, String value2) {
            addCriterion("river_type not between", value1, value2, "riverType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeIsNull() {
            addCriterion("network_type is null");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeIsNotNull() {
            addCriterion("network_type is not null");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeEqualTo(String value) {
            addCriterion("network_type =", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeNotEqualTo(String value) {
            addCriterion("network_type <>", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeGreaterThan(String value) {
            addCriterion("network_type >", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeGreaterThanOrEqualTo(String value) {
            addCriterion("network_type >=", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeLessThan(String value) {
            addCriterion("network_type <", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeLessThanOrEqualTo(String value) {
            addCriterion("network_type <=", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeLike(String value) {
            addCriterion("network_type like", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeNotLike(String value) {
            addCriterion("network_type not like", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeIn(List<String> values) {
            addCriterion("network_type in", values, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeNotIn(List<String> values) {
            addCriterion("network_type not in", values, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeBetween(String value1, String value2) {
            addCriterion("network_type between", value1, value2, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeNotBetween(String value1, String value2) {
            addCriterion("network_type not between", value1, value2, "networkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeIsNull() {
            addCriterion("storage_network_type is null");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeIsNotNull() {
            addCriterion("storage_network_type is not null");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeEqualTo(String value) {
            addCriterion("storage_network_type =", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeNotEqualTo(String value) {
            addCriterion("storage_network_type <>", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeGreaterThan(String value) {
            addCriterion("storage_network_type >", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeGreaterThanOrEqualTo(String value) {
            addCriterion("storage_network_type >=", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeLessThan(String value) {
            addCriterion("storage_network_type <", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeLessThanOrEqualTo(String value) {
            addCriterion("storage_network_type <=", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeLike(String value) {
            addCriterion("storage_network_type like", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeNotLike(String value) {
            addCriterion("storage_network_type not like", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeIn(List<String> values) {
            addCriterion("storage_network_type in", values, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeNotIn(List<String> values) {
            addCriterion("storage_network_type not in", values, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeBetween(String value1, String value2) {
            addCriterion("storage_network_type between", value1, value2, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeNotBetween(String value1, String value2) {
            addCriterion("storage_network_type not between", value1, value2, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeIsNull() {
            addCriterion("storage_type is null");
            return (Criteria) this;
        }

        public Criteria andStorageTypeIsNotNull() {
            addCriterion("storage_type is not null");
            return (Criteria) this;
        }

        public Criteria andStorageTypeEqualTo(String value) {
            addCriterion("storage_type =", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeNotEqualTo(String value) {
            addCriterion("storage_type <>", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeGreaterThan(String value) {
            addCriterion("storage_type >", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeGreaterThanOrEqualTo(String value) {
            addCriterion("storage_type >=", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeLessThan(String value) {
            addCriterion("storage_type <", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeLessThanOrEqualTo(String value) {
            addCriterion("storage_type <=", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeLike(String value) {
            addCriterion("storage_type like", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeNotLike(String value) {
            addCriterion("storage_type not like", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeIn(List<String> values) {
            addCriterion("storage_type in", values, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeNotIn(List<String> values) {
            addCriterion("storage_type not in", values, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeBetween(String value1, String value2) {
            addCriterion("storage_type between", value1, value2, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeNotBetween(String value1, String value2) {
            addCriterion("storage_type not between", value1, value2, "storageType");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationIsNull() {
            addCriterion("cpu_generation is null");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationIsNotNull() {
            addCriterion("cpu_generation is not null");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationEqualTo(String value) {
            addCriterion("cpu_generation =", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationNotEqualTo(String value) {
            addCriterion("cpu_generation <>", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationGreaterThan(String value) {
            addCriterion("cpu_generation >", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationGreaterThanOrEqualTo(String value) {
            addCriterion("cpu_generation >=", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationLessThan(String value) {
            addCriterion("cpu_generation <", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationLessThanOrEqualTo(String value) {
            addCriterion("cpu_generation <=", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationLike(String value) {
            addCriterion("cpu_generation like", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationNotLike(String value) {
            addCriterion("cpu_generation not like", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationIn(List<String> values) {
            addCriterion("cpu_generation in", values, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationNotIn(List<String> values) {
            addCriterion("cpu_generation not in", values, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationBetween(String value1, String value2) {
            addCriterion("cpu_generation between", value1, value2, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationNotBetween(String value1, String value2) {
            addCriterion("cpu_generation not between", value1, value2, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andBizStatusIsNull() {
            addCriterion("biz_status is null");
            return (Criteria) this;
        }

        public Criteria andBizStatusIsNotNull() {
            addCriterion("biz_status is not null");
            return (Criteria) this;
        }

        public Criteria andBizStatusEqualTo(String value) {
            addCriterion("biz_status =", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusNotEqualTo(String value) {
            addCriterion("biz_status <>", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusGreaterThan(String value) {
            addCriterion("biz_status >", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusGreaterThanOrEqualTo(String value) {
            addCriterion("biz_status >=", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusLessThan(String value) {
            addCriterion("biz_status <", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusLessThanOrEqualTo(String value) {
            addCriterion("biz_status <=", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusLike(String value) {
            addCriterion("biz_status like", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusNotLike(String value) {
            addCriterion("biz_status not like", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusIn(List<String> values) {
            addCriterion("biz_status in", values, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusNotIn(List<String> values) {
            addCriterion("biz_status not in", values, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusBetween(String value1, String value2) {
            addCriterion("biz_status between", value1, value2, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusNotBetween(String value1, String value2) {
            addCriterion("biz_status not between", value1, value2, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andGmtCreatedIsNull() {
            addCriterion("gmt_created is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreatedIsNotNull() {
            addCriterion("gmt_created is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreatedEqualTo(Date value) {
            addCriterion("gmt_created =", value, "gmtCreated");
            return (Criteria) this;
        }

        public Criteria andGmtCreatedNotEqualTo(Date value) {
            addCriterion("gmt_created <>", value, "gmtCreated");
            return (Criteria) this;
        }

        public Criteria andGmtCreatedGreaterThan(Date value) {
            addCriterion("gmt_created >", value, "gmtCreated");
            return (Criteria) this;
        }

        public Criteria andGmtCreatedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_created >=", value, "gmtCreated");
            return (Criteria) this;
        }

        public Criteria andGmtCreatedLessThan(Date value) {
            addCriterion("gmt_created <", value, "gmtCreated");
            return (Criteria) this;
        }

        public Criteria andGmtCreatedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_created <=", value, "gmtCreated");
            return (Criteria) this;
        }

        public Criteria andGmtCreatedIn(List<Date> values) {
            addCriterion("gmt_created in", values, "gmtCreated");
            return (Criteria) this;
        }

        public Criteria andGmtCreatedNotIn(List<Date> values) {
            addCriterion("gmt_created not in", values, "gmtCreated");
            return (Criteria) this;
        }

        public Criteria andGmtCreatedBetween(Date value1, Date value2) {
            addCriterion("gmt_created between", value1, value2, "gmtCreated");
            return (Criteria) this;
        }

        public Criteria andGmtCreatedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_created not between", value1, value2, "gmtCreated");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table allinone_risk_vm_detail
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}