package com.aliyun.xdragon.common.generate.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WorkItemClassificationDetailExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    public WorkItemClassificationDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andSourceIdIsNull() {
            addCriterion("source_id is null");
            return (Criteria) this;
        }

        public Criteria andSourceIdIsNotNull() {
            addCriterion("source_id is not null");
            return (Criteria) this;
        }

        public Criteria andSourceIdEqualTo(String value) {
            addCriterion("source_id =", value, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdNotEqualTo(String value) {
            addCriterion("source_id <>", value, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdGreaterThan(String value) {
            addCriterion("source_id >", value, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdGreaterThanOrEqualTo(String value) {
            addCriterion("source_id >=", value, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdLessThan(String value) {
            addCriterion("source_id <", value, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdLessThanOrEqualTo(String value) {
            addCriterion("source_id <=", value, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdLike(String value) {
            addCriterion("source_id like", value, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdNotLike(String value) {
            addCriterion("source_id not like", value, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdIn(List<String> values) {
            addCriterion("source_id in", values, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdNotIn(List<String> values) {
            addCriterion("source_id not in", values, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdBetween(String value1, String value2) {
            addCriterion("source_id between", value1, value2, "sourceId");
            return (Criteria) this;
        }

        public Criteria andSourceIdNotBetween(String value1, String value2) {
            addCriterion("source_id not between", value1, value2, "sourceId");
            return (Criteria) this;
        }

        public Criteria andIpInfoIsNull() {
            addCriterion("ip_info is null");
            return (Criteria) this;
        }

        public Criteria andIpInfoIsNotNull() {
            addCriterion("ip_info is not null");
            return (Criteria) this;
        }

        public Criteria andIpInfoEqualTo(String value) {
            addCriterion("ip_info =", value, "ipInfo");
            return (Criteria) this;
        }

        public Criteria andIpInfoNotEqualTo(String value) {
            addCriterion("ip_info <>", value, "ipInfo");
            return (Criteria) this;
        }

        public Criteria andIpInfoGreaterThan(String value) {
            addCriterion("ip_info >", value, "ipInfo");
            return (Criteria) this;
        }

        public Criteria andIpInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ip_info >=", value, "ipInfo");
            return (Criteria) this;
        }

        public Criteria andIpInfoLessThan(String value) {
            addCriterion("ip_info <", value, "ipInfo");
            return (Criteria) this;
        }

        public Criteria andIpInfoLessThanOrEqualTo(String value) {
            addCriterion("ip_info <=", value, "ipInfo");
            return (Criteria) this;
        }

        public Criteria andIpInfoLike(String value) {
            addCriterion("ip_info like", value, "ipInfo");
            return (Criteria) this;
        }

        public Criteria andIpInfoNotLike(String value) {
            addCriterion("ip_info not like", value, "ipInfo");
            return (Criteria) this;
        }

        public Criteria andIpInfoIn(List<String> values) {
            addCriterion("ip_info in", values, "ipInfo");
            return (Criteria) this;
        }

        public Criteria andIpInfoNotIn(List<String> values) {
            addCriterion("ip_info not in", values, "ipInfo");
            return (Criteria) this;
        }

        public Criteria andIpInfoBetween(String value1, String value2) {
            addCriterion("ip_info between", value1, value2, "ipInfo");
            return (Criteria) this;
        }

        public Criteria andIpInfoNotBetween(String value1, String value2) {
            addCriterion("ip_info not between", value1, value2, "ipInfo");
            return (Criteria) this;
        }

        public Criteria andProductIsNull() {
            addCriterion("product is null");
            return (Criteria) this;
        }

        public Criteria andProductIsNotNull() {
            addCriterion("product is not null");
            return (Criteria) this;
        }

        public Criteria andProductEqualTo(String value) {
            addCriterion("product =", value, "product");
            return (Criteria) this;
        }

        public Criteria andProductNotEqualTo(String value) {
            addCriterion("product <>", value, "product");
            return (Criteria) this;
        }

        public Criteria andProductGreaterThan(String value) {
            addCriterion("product >", value, "product");
            return (Criteria) this;
        }

        public Criteria andProductGreaterThanOrEqualTo(String value) {
            addCriterion("product >=", value, "product");
            return (Criteria) this;
        }

        public Criteria andProductLessThan(String value) {
            addCriterion("product <", value, "product");
            return (Criteria) this;
        }

        public Criteria andProductLessThanOrEqualTo(String value) {
            addCriterion("product <=", value, "product");
            return (Criteria) this;
        }

        public Criteria andProductLike(String value) {
            addCriterion("product like", value, "product");
            return (Criteria) this;
        }

        public Criteria andProductNotLike(String value) {
            addCriterion("product not like", value, "product");
            return (Criteria) this;
        }

        public Criteria andProductIn(List<String> values) {
            addCriterion("product in", values, "product");
            return (Criteria) this;
        }

        public Criteria andProductNotIn(List<String> values) {
            addCriterion("product not in", values, "product");
            return (Criteria) this;
        }

        public Criteria andProductBetween(String value1, String value2) {
            addCriterion("product between", value1, value2, "product");
            return (Criteria) this;
        }

        public Criteria andProductNotBetween(String value1, String value2) {
            addCriterion("product not between", value1, value2, "product");
            return (Criteria) this;
        }

        public Criteria andProjectIsNull() {
            addCriterion("project is null");
            return (Criteria) this;
        }

        public Criteria andProjectIsNotNull() {
            addCriterion("project is not null");
            return (Criteria) this;
        }

        public Criteria andProjectEqualTo(String value) {
            addCriterion("project =", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotEqualTo(String value) {
            addCriterion("project <>", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThan(String value) {
            addCriterion("project >", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanOrEqualTo(String value) {
            addCriterion("project >=", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLessThan(String value) {
            addCriterion("project <", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLessThanOrEqualTo(String value) {
            addCriterion("project <=", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLike(String value) {
            addCriterion("project like", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotLike(String value) {
            addCriterion("project not like", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectIn(List<String> values) {
            addCriterion("project in", values, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotIn(List<String> values) {
            addCriterion("project not in", values, "project");
            return (Criteria) this;
        }

        public Criteria andProjectBetween(String value1, String value2) {
            addCriterion("project between", value1, value2, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotBetween(String value1, String value2) {
            addCriterion("project not between", value1, value2, "project");
            return (Criteria) this;
        }

        public Criteria andCommitTimeIsNull() {
            addCriterion("commit_time is null");
            return (Criteria) this;
        }

        public Criteria andCommitTimeIsNotNull() {
            addCriterion("commit_time is not null");
            return (Criteria) this;
        }

        public Criteria andCommitTimeEqualTo(String value) {
            addCriterion("commit_time =", value, "commitTime");
            return (Criteria) this;
        }

        public Criteria andCommitTimeNotEqualTo(String value) {
            addCriterion("commit_time <>", value, "commitTime");
            return (Criteria) this;
        }

        public Criteria andCommitTimeGreaterThan(String value) {
            addCriterion("commit_time >", value, "commitTime");
            return (Criteria) this;
        }

        public Criteria andCommitTimeGreaterThanOrEqualTo(String value) {
            addCriterion("commit_time >=", value, "commitTime");
            return (Criteria) this;
        }

        public Criteria andCommitTimeLessThan(String value) {
            addCriterion("commit_time <", value, "commitTime");
            return (Criteria) this;
        }

        public Criteria andCommitTimeLessThanOrEqualTo(String value) {
            addCriterion("commit_time <=", value, "commitTime");
            return (Criteria) this;
        }

        public Criteria andCommitTimeLike(String value) {
            addCriterion("commit_time like", value, "commitTime");
            return (Criteria) this;
        }

        public Criteria andCommitTimeNotLike(String value) {
            addCriterion("commit_time not like", value, "commitTime");
            return (Criteria) this;
        }

        public Criteria andCommitTimeIn(List<String> values) {
            addCriterion("commit_time in", values, "commitTime");
            return (Criteria) this;
        }

        public Criteria andCommitTimeNotIn(List<String> values) {
            addCriterion("commit_time not in", values, "commitTime");
            return (Criteria) this;
        }

        public Criteria andCommitTimeBetween(String value1, String value2) {
            addCriterion("commit_time between", value1, value2, "commitTime");
            return (Criteria) this;
        }

        public Criteria andCommitTimeNotBetween(String value1, String value2) {
            addCriterion("commit_time not between", value1, value2, "commitTime");
            return (Criteria) this;
        }

        public Criteria andSourceUrlIsNull() {
            addCriterion("source_url is null");
            return (Criteria) this;
        }

        public Criteria andSourceUrlIsNotNull() {
            addCriterion("source_url is not null");
            return (Criteria) this;
        }

        public Criteria andSourceUrlEqualTo(String value) {
            addCriterion("source_url =", value, "sourceUrl");
            return (Criteria) this;
        }

        public Criteria andSourceUrlNotEqualTo(String value) {
            addCriterion("source_url <>", value, "sourceUrl");
            return (Criteria) this;
        }

        public Criteria andSourceUrlGreaterThan(String value) {
            addCriterion("source_url >", value, "sourceUrl");
            return (Criteria) this;
        }

        public Criteria andSourceUrlGreaterThanOrEqualTo(String value) {
            addCriterion("source_url >=", value, "sourceUrl");
            return (Criteria) this;
        }

        public Criteria andSourceUrlLessThan(String value) {
            addCriterion("source_url <", value, "sourceUrl");
            return (Criteria) this;
        }

        public Criteria andSourceUrlLessThanOrEqualTo(String value) {
            addCriterion("source_url <=", value, "sourceUrl");
            return (Criteria) this;
        }

        public Criteria andSourceUrlLike(String value) {
            addCriterion("source_url like", value, "sourceUrl");
            return (Criteria) this;
        }

        public Criteria andSourceUrlNotLike(String value) {
            addCriterion("source_url not like", value, "sourceUrl");
            return (Criteria) this;
        }

        public Criteria andSourceUrlIn(List<String> values) {
            addCriterion("source_url in", values, "sourceUrl");
            return (Criteria) this;
        }

        public Criteria andSourceUrlNotIn(List<String> values) {
            addCriterion("source_url not in", values, "sourceUrl");
            return (Criteria) this;
        }

        public Criteria andSourceUrlBetween(String value1, String value2) {
            addCriterion("source_url between", value1, value2, "sourceUrl");
            return (Criteria) this;
        }

        public Criteria andSourceUrlNotBetween(String value1, String value2) {
            addCriterion("source_url not between", value1, value2, "sourceUrl");
            return (Criteria) this;
        }

        public Criteria andDataSourceIsNull() {
            addCriterion("data_source is null");
            return (Criteria) this;
        }

        public Criteria andDataSourceIsNotNull() {
            addCriterion("data_source is not null");
            return (Criteria) this;
        }

        public Criteria andDataSourceEqualTo(String value) {
            addCriterion("data_source =", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotEqualTo(String value) {
            addCriterion("data_source <>", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceGreaterThan(String value) {
            addCriterion("data_source >", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceGreaterThanOrEqualTo(String value) {
            addCriterion("data_source >=", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceLessThan(String value) {
            addCriterion("data_source <", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceLessThanOrEqualTo(String value) {
            addCriterion("data_source <=", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceLike(String value) {
            addCriterion("data_source like", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotLike(String value) {
            addCriterion("data_source not like", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceIn(List<String> values) {
            addCriterion("data_source in", values, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotIn(List<String> values) {
            addCriterion("data_source not in", values, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceBetween(String value1, String value2) {
            addCriterion("data_source between", value1, value2, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotBetween(String value1, String value2) {
            addCriterion("data_source not between", value1, value2, "dataSource");
            return (Criteria) this;
        }

        public Criteria andIssueUrlIsNull() {
            addCriterion("issue_url is null");
            return (Criteria) this;
        }

        public Criteria andIssueUrlIsNotNull() {
            addCriterion("issue_url is not null");
            return (Criteria) this;
        }

        public Criteria andIssueUrlEqualTo(String value) {
            addCriterion("issue_url =", value, "issueUrl");
            return (Criteria) this;
        }

        public Criteria andIssueUrlNotEqualTo(String value) {
            addCriterion("issue_url <>", value, "issueUrl");
            return (Criteria) this;
        }

        public Criteria andIssueUrlGreaterThan(String value) {
            addCriterion("issue_url >", value, "issueUrl");
            return (Criteria) this;
        }

        public Criteria andIssueUrlGreaterThanOrEqualTo(String value) {
            addCriterion("issue_url >=", value, "issueUrl");
            return (Criteria) this;
        }

        public Criteria andIssueUrlLessThan(String value) {
            addCriterion("issue_url <", value, "issueUrl");
            return (Criteria) this;
        }

        public Criteria andIssueUrlLessThanOrEqualTo(String value) {
            addCriterion("issue_url <=", value, "issueUrl");
            return (Criteria) this;
        }

        public Criteria andIssueUrlLike(String value) {
            addCriterion("issue_url like", value, "issueUrl");
            return (Criteria) this;
        }

        public Criteria andIssueUrlNotLike(String value) {
            addCriterion("issue_url not like", value, "issueUrl");
            return (Criteria) this;
        }

        public Criteria andIssueUrlIn(List<String> values) {
            addCriterion("issue_url in", values, "issueUrl");
            return (Criteria) this;
        }

        public Criteria andIssueUrlNotIn(List<String> values) {
            addCriterion("issue_url not in", values, "issueUrl");
            return (Criteria) this;
        }

        public Criteria andIssueUrlBetween(String value1, String value2) {
            addCriterion("issue_url between", value1, value2, "issueUrl");
            return (Criteria) this;
        }

        public Criteria andIssueUrlNotBetween(String value1, String value2) {
            addCriterion("issue_url not between", value1, value2, "issueUrl");
            return (Criteria) this;
        }

        public Criteria andLabelOneIdIsNull() {
            addCriterion("label_one_id is null");
            return (Criteria) this;
        }

        public Criteria andLabelOneIdIsNotNull() {
            addCriterion("label_one_id is not null");
            return (Criteria) this;
        }

        public Criteria andLabelOneIdEqualTo(Long value) {
            addCriterion("label_one_id =", value, "labelOneId");
            return (Criteria) this;
        }

        public Criteria andLabelOneIdNotEqualTo(Long value) {
            addCriterion("label_one_id <>", value, "labelOneId");
            return (Criteria) this;
        }

        public Criteria andLabelOneIdGreaterThan(Long value) {
            addCriterion("label_one_id >", value, "labelOneId");
            return (Criteria) this;
        }

        public Criteria andLabelOneIdGreaterThanOrEqualTo(Long value) {
            addCriterion("label_one_id >=", value, "labelOneId");
            return (Criteria) this;
        }

        public Criteria andLabelOneIdLessThan(Long value) {
            addCriterion("label_one_id <", value, "labelOneId");
            return (Criteria) this;
        }

        public Criteria andLabelOneIdLessThanOrEqualTo(Long value) {
            addCriterion("label_one_id <=", value, "labelOneId");
            return (Criteria) this;
        }

        public Criteria andLabelOneIdIn(List<Long> values) {
            addCriterion("label_one_id in", values, "labelOneId");
            return (Criteria) this;
        }

        public Criteria andLabelOneIdNotIn(List<Long> values) {
            addCriterion("label_one_id not in", values, "labelOneId");
            return (Criteria) this;
        }

        public Criteria andLabelOneIdBetween(Long value1, Long value2) {
            addCriterion("label_one_id between", value1, value2, "labelOneId");
            return (Criteria) this;
        }

        public Criteria andLabelOneIdNotBetween(Long value1, Long value2) {
            addCriterion("label_one_id not between", value1, value2, "labelOneId");
            return (Criteria) this;
        }

        public Criteria andCorrectedLabelOneIdIsNull() {
            addCriterion("corrected_label_one_id is null");
            return (Criteria) this;
        }

        public Criteria andCorrectedLabelOneIdIsNotNull() {
            addCriterion("corrected_label_one_id is not null");
            return (Criteria) this;
        }

        public Criteria andCorrectedLabelOneIdEqualTo(Long value) {
            addCriterion("corrected_label_one_id =", value, "correctedLabelOneId");
            return (Criteria) this;
        }

        public Criteria andCorrectedLabelOneIdNotEqualTo(Long value) {
            addCriterion("corrected_label_one_id <>", value, "correctedLabelOneId");
            return (Criteria) this;
        }

        public Criteria andCorrectedLabelOneIdGreaterThan(Long value) {
            addCriterion("corrected_label_one_id >", value, "correctedLabelOneId");
            return (Criteria) this;
        }

        public Criteria andCorrectedLabelOneIdGreaterThanOrEqualTo(Long value) {
            addCriterion("corrected_label_one_id >=", value, "correctedLabelOneId");
            return (Criteria) this;
        }

        public Criteria andCorrectedLabelOneIdLessThan(Long value) {
            addCriterion("corrected_label_one_id <", value, "correctedLabelOneId");
            return (Criteria) this;
        }

        public Criteria andCorrectedLabelOneIdLessThanOrEqualTo(Long value) {
            addCriterion("corrected_label_one_id <=", value, "correctedLabelOneId");
            return (Criteria) this;
        }

        public Criteria andCorrectedLabelOneIdIn(List<Long> values) {
            addCriterion("corrected_label_one_id in", values, "correctedLabelOneId");
            return (Criteria) this;
        }

        public Criteria andCorrectedLabelOneIdNotIn(List<Long> values) {
            addCriterion("corrected_label_one_id not in", values, "correctedLabelOneId");
            return (Criteria) this;
        }

        public Criteria andCorrectedLabelOneIdBetween(Long value1, Long value2) {
            addCriterion("corrected_label_one_id between", value1, value2, "correctedLabelOneId");
            return (Criteria) this;
        }

        public Criteria andCorrectedLabelOneIdNotBetween(Long value1, Long value2) {
            addCriterion("corrected_label_one_id not between", value1, value2, "correctedLabelOneId");
            return (Criteria) this;
        }

        public Criteria andLabelTwoIdIsNull() {
            addCriterion("label_two_id is null");
            return (Criteria) this;
        }

        public Criteria andLabelTwoIdIsNotNull() {
            addCriterion("label_two_id is not null");
            return (Criteria) this;
        }

        public Criteria andLabelTwoIdEqualTo(String value) {
            addCriterion("label_two_id =", value, "labelTwoId");
            return (Criteria) this;
        }

        public Criteria andLabelTwoIdNotEqualTo(String value) {
            addCriterion("label_two_id <>", value, "labelTwoId");
            return (Criteria) this;
        }

        public Criteria andLabelTwoIdGreaterThan(String value) {
            addCriterion("label_two_id >", value, "labelTwoId");
            return (Criteria) this;
        }

        public Criteria andLabelTwoIdGreaterThanOrEqualTo(String value) {
            addCriterion("label_two_id >=", value, "labelTwoId");
            return (Criteria) this;
        }

        public Criteria andLabelTwoIdLessThan(String value) {
            addCriterion("label_two_id <", value, "labelTwoId");
            return (Criteria) this;
        }

        public Criteria andLabelTwoIdLessThanOrEqualTo(String value) {
            addCriterion("label_two_id <=", value, "labelTwoId");
            return (Criteria) this;
        }

        public Criteria andLabelTwoIdLike(String value) {
            addCriterion("label_two_id like", value, "labelTwoId");
            return (Criteria) this;
        }

        public Criteria andLabelTwoIdNotLike(String value) {
            addCriterion("label_two_id not like", value, "labelTwoId");
            return (Criteria) this;
        }

        public Criteria andLabelTwoIdIn(List<String> values) {
            addCriterion("label_two_id in", values, "labelTwoId");
            return (Criteria) this;
        }

        public Criteria andLabelTwoIdNotIn(List<String> values) {
            addCriterion("label_two_id not in", values, "labelTwoId");
            return (Criteria) this;
        }

        public Criteria andLabelTwoIdBetween(String value1, String value2) {
            addCriterion("label_two_id between", value1, value2, "labelTwoId");
            return (Criteria) this;
        }

        public Criteria andLabelTwoIdNotBetween(String value1, String value2) {
            addCriterion("label_two_id not between", value1, value2, "labelTwoId");
            return (Criteria) this;
        }

        public Criteria andSourceStateIsNull() {
            addCriterion("source_state is null");
            return (Criteria) this;
        }

        public Criteria andSourceStateIsNotNull() {
            addCriterion("source_state is not null");
            return (Criteria) this;
        }

        public Criteria andSourceStateEqualTo(String value) {
            addCriterion("source_state =", value, "sourceState");
            return (Criteria) this;
        }

        public Criteria andSourceStateNotEqualTo(String value) {
            addCriterion("source_state <>", value, "sourceState");
            return (Criteria) this;
        }

        public Criteria andSourceStateGreaterThan(String value) {
            addCriterion("source_state >", value, "sourceState");
            return (Criteria) this;
        }

        public Criteria andSourceStateGreaterThanOrEqualTo(String value) {
            addCriterion("source_state >=", value, "sourceState");
            return (Criteria) this;
        }

        public Criteria andSourceStateLessThan(String value) {
            addCriterion("source_state <", value, "sourceState");
            return (Criteria) this;
        }

        public Criteria andSourceStateLessThanOrEqualTo(String value) {
            addCriterion("source_state <=", value, "sourceState");
            return (Criteria) this;
        }

        public Criteria andSourceStateLike(String value) {
            addCriterion("source_state like", value, "sourceState");
            return (Criteria) this;
        }

        public Criteria andSourceStateNotLike(String value) {
            addCriterion("source_state not like", value, "sourceState");
            return (Criteria) this;
        }

        public Criteria andSourceStateIn(List<String> values) {
            addCriterion("source_state in", values, "sourceState");
            return (Criteria) this;
        }

        public Criteria andSourceStateNotIn(List<String> values) {
            addCriterion("source_state not in", values, "sourceState");
            return (Criteria) this;
        }

        public Criteria andSourceStateBetween(String value1, String value2) {
            addCriterion("source_state between", value1, value2, "sourceState");
            return (Criteria) this;
        }

        public Criteria andSourceStateNotBetween(String value1, String value2) {
            addCriterion("source_state not between", value1, value2, "sourceState");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeIsNull() {
            addCriterion("exception_time is null");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeIsNotNull() {
            addCriterion("exception_time is not null");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeEqualTo(String value) {
            addCriterion("exception_time =", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeNotEqualTo(String value) {
            addCriterion("exception_time <>", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeGreaterThan(String value) {
            addCriterion("exception_time >", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeGreaterThanOrEqualTo(String value) {
            addCriterion("exception_time >=", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeLessThan(String value) {
            addCriterion("exception_time <", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeLessThanOrEqualTo(String value) {
            addCriterion("exception_time <=", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeLike(String value) {
            addCriterion("exception_time like", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeNotLike(String value) {
            addCriterion("exception_time not like", value, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeIn(List<String> values) {
            addCriterion("exception_time in", values, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeNotIn(List<String> values) {
            addCriterion("exception_time not in", values, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeBetween(String value1, String value2) {
            addCriterion("exception_time between", value1, value2, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andExceptionTimeNotBetween(String value1, String value2) {
            addCriterion("exception_time not between", value1, value2, "exceptionTime");
            return (Criteria) this;
        }

        public Criteria andAoneUrlIsNull() {
            addCriterion("aone_url is null");
            return (Criteria) this;
        }

        public Criteria andAoneUrlIsNotNull() {
            addCriterion("aone_url is not null");
            return (Criteria) this;
        }

        public Criteria andAoneUrlEqualTo(String value) {
            addCriterion("aone_url =", value, "aoneUrl");
            return (Criteria) this;
        }

        public Criteria andAoneUrlNotEqualTo(String value) {
            addCriterion("aone_url <>", value, "aoneUrl");
            return (Criteria) this;
        }

        public Criteria andAoneUrlGreaterThan(String value) {
            addCriterion("aone_url >", value, "aoneUrl");
            return (Criteria) this;
        }

        public Criteria andAoneUrlGreaterThanOrEqualTo(String value) {
            addCriterion("aone_url >=", value, "aoneUrl");
            return (Criteria) this;
        }

        public Criteria andAoneUrlLessThan(String value) {
            addCriterion("aone_url <", value, "aoneUrl");
            return (Criteria) this;
        }

        public Criteria andAoneUrlLessThanOrEqualTo(String value) {
            addCriterion("aone_url <=", value, "aoneUrl");
            return (Criteria) this;
        }

        public Criteria andAoneUrlLike(String value) {
            addCriterion("aone_url like", value, "aoneUrl");
            return (Criteria) this;
        }

        public Criteria andAoneUrlNotLike(String value) {
            addCriterion("aone_url not like", value, "aoneUrl");
            return (Criteria) this;
        }

        public Criteria andAoneUrlIn(List<String> values) {
            addCriterion("aone_url in", values, "aoneUrl");
            return (Criteria) this;
        }

        public Criteria andAoneUrlNotIn(List<String> values) {
            addCriterion("aone_url not in", values, "aoneUrl");
            return (Criteria) this;
        }

        public Criteria andAoneUrlBetween(String value1, String value2) {
            addCriterion("aone_url between", value1, value2, "aoneUrl");
            return (Criteria) this;
        }

        public Criteria andAoneUrlNotBetween(String value1, String value2) {
            addCriterion("aone_url not between", value1, value2, "aoneUrl");
            return (Criteria) this;
        }

        public Criteria andAoneStateIsNull() {
            addCriterion("aone_state is null");
            return (Criteria) this;
        }

        public Criteria andAoneStateIsNotNull() {
            addCriterion("aone_state is not null");
            return (Criteria) this;
        }

        public Criteria andAoneStateEqualTo(String value) {
            addCriterion("aone_state =", value, "aoneState");
            return (Criteria) this;
        }

        public Criteria andAoneStateNotEqualTo(String value) {
            addCriterion("aone_state <>", value, "aoneState");
            return (Criteria) this;
        }

        public Criteria andAoneStateGreaterThan(String value) {
            addCriterion("aone_state >", value, "aoneState");
            return (Criteria) this;
        }

        public Criteria andAoneStateGreaterThanOrEqualTo(String value) {
            addCriterion("aone_state >=", value, "aoneState");
            return (Criteria) this;
        }

        public Criteria andAoneStateLessThan(String value) {
            addCriterion("aone_state <", value, "aoneState");
            return (Criteria) this;
        }

        public Criteria andAoneStateLessThanOrEqualTo(String value) {
            addCriterion("aone_state <=", value, "aoneState");
            return (Criteria) this;
        }

        public Criteria andAoneStateLike(String value) {
            addCriterion("aone_state like", value, "aoneState");
            return (Criteria) this;
        }

        public Criteria andAoneStateNotLike(String value) {
            addCriterion("aone_state not like", value, "aoneState");
            return (Criteria) this;
        }

        public Criteria andAoneStateIn(List<String> values) {
            addCriterion("aone_state in", values, "aoneState");
            return (Criteria) this;
        }

        public Criteria andAoneStateNotIn(List<String> values) {
            addCriterion("aone_state not in", values, "aoneState");
            return (Criteria) this;
        }

        public Criteria andAoneStateBetween(String value1, String value2) {
            addCriterion("aone_state between", value1, value2, "aoneState");
            return (Criteria) this;
        }

        public Criteria andAoneStateNotBetween(String value1, String value2) {
            addCriterion("aone_state not between", value1, value2, "aoneState");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNull() {
            addCriterion("`owner` is null");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNotNull() {
            addCriterion("`owner` is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerEqualTo(String value) {
            addCriterion("`owner` =", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotEqualTo(String value) {
            addCriterion("`owner` <>", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThan(String value) {
            addCriterion("`owner` >", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("`owner` >=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThan(String value) {
            addCriterion("`owner` <", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThanOrEqualTo(String value) {
            addCriterion("`owner` <=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLike(String value) {
            addCriterion("`owner` like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotLike(String value) {
            addCriterion("`owner` not like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerIn(List<String> values) {
            addCriterion("`owner` in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotIn(List<String> values) {
            addCriterion("`owner` not in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerBetween(String value1, String value2) {
            addCriterion("`owner` between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotBetween(String value1, String value2) {
            addCriterion("`owner` not between", value1, value2, "owner");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}