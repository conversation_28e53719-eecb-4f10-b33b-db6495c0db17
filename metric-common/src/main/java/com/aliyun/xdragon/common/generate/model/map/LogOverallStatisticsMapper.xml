<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.model.map.LogOverallStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.model.LogOverallStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="task_cnt" jdbcType="INTEGER" property="taskCnt" />
    <result column="cid_cnt" jdbcType="INTEGER" property="cidCnt" />
    <result column="monitor_cnt" jdbcType="INTEGER" property="monitorCnt" />
    <result column="total_type" jdbcType="BIGINT" property="totalType" />
    <result column="totol_cnt" jdbcType="BIGINT" property="totolCnt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, start_time, end_time, task_cnt, cid_cnt, monitor_cnt, total_type, totol_cnt
  </sql>
  <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogOverallStatisticsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from log_overall_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from log_overall_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from log_overall_statistics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogOverallStatisticsExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from log_overall_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.model.LogOverallStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_overall_statistics (id, start_time, end_time, 
      task_cnt, cid_cnt, monitor_cnt, 
      total_type, totol_cnt)
    values (#{id,jdbcType=BIGINT}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{taskCnt,jdbcType=INTEGER}, #{cidCnt,jdbcType=INTEGER}, #{monitorCnt,jdbcType=INTEGER}, 
      #{totalType,jdbcType=BIGINT}, #{totolCnt,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.model.LogOverallStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_overall_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="taskCnt != null">
        task_cnt,
      </if>
      <if test="cidCnt != null">
        cid_cnt,
      </if>
      <if test="monitorCnt != null">
        monitor_cnt,
      </if>
      <if test="totalType != null">
        total_type,
      </if>
      <if test="totolCnt != null">
        totol_cnt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskCnt != null">
        #{taskCnt,jdbcType=INTEGER},
      </if>
      <if test="cidCnt != null">
        #{cidCnt,jdbcType=INTEGER},
      </if>
      <if test="monitorCnt != null">
        #{monitorCnt,jdbcType=INTEGER},
      </if>
      <if test="totalType != null">
        #{totalType,jdbcType=BIGINT},
      </if>
      <if test="totolCnt != null">
        #{totolCnt,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogOverallStatisticsExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from log_overall_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_overall_statistics
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskCnt != null">
        task_cnt = #{record.taskCnt,jdbcType=INTEGER},
      </if>
      <if test="record.cidCnt != null">
        cid_cnt = #{record.cidCnt,jdbcType=INTEGER},
      </if>
      <if test="record.monitorCnt != null">
        monitor_cnt = #{record.monitorCnt,jdbcType=INTEGER},
      </if>
      <if test="record.totalType != null">
        total_type = #{record.totalType,jdbcType=BIGINT},
      </if>
      <if test="record.totolCnt != null">
        totol_cnt = #{record.totolCnt,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_overall_statistics
    set id = #{record.id,jdbcType=BIGINT},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      task_cnt = #{record.taskCnt,jdbcType=INTEGER},
      cid_cnt = #{record.cidCnt,jdbcType=INTEGER},
      monitor_cnt = #{record.monitorCnt,jdbcType=INTEGER},
      total_type = #{record.totalType,jdbcType=BIGINT},
      totol_cnt = #{record.totolCnt,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.xdragon.common.generate.model.LogOverallStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_overall_statistics
    <set>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskCnt != null">
        task_cnt = #{taskCnt,jdbcType=INTEGER},
      </if>
      <if test="cidCnt != null">
        cid_cnt = #{cidCnt,jdbcType=INTEGER},
      </if>
      <if test="monitorCnt != null">
        monitor_cnt = #{monitorCnt,jdbcType=INTEGER},
      </if>
      <if test="totalType != null">
        total_type = #{totalType,jdbcType=BIGINT},
      </if>
      <if test="totolCnt != null">
        totol_cnt = #{totolCnt,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.model.LogOverallStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_overall_statistics
    set start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      task_cnt = #{taskCnt,jdbcType=INTEGER},
      cid_cnt = #{cidCnt,jdbcType=INTEGER},
      monitor_cnt = #{monitorCnt,jdbcType=INTEGER},
      total_type = #{totalType,jdbcType=BIGINT},
      totol_cnt = #{totolCnt,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.model.LogOverallStatisticsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from log_overall_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_overall_statistics
    (id, start_time, end_time, task_cnt, cid_cnt, monitor_cnt, total_type, totol_cnt)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.startTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP}, 
        #{item.taskCnt,jdbcType=INTEGER}, #{item.cidCnt,jdbcType=INTEGER}, #{item.monitorCnt,jdbcType=INTEGER}, 
        #{item.totalType,jdbcType=BIGINT}, #{item.totolCnt,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_overall_statistics (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'start_time'.toString() == column.value">
          #{item.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'task_cnt'.toString() == column.value">
          #{item.taskCnt,jdbcType=INTEGER}
        </if>
        <if test="'cid_cnt'.toString() == column.value">
          #{item.cidCnt,jdbcType=INTEGER}
        </if>
        <if test="'monitor_cnt'.toString() == column.value">
          #{item.monitorCnt,jdbcType=INTEGER}
        </if>
        <if test="'total_type'.toString() == column.value">
          #{item.totalType,jdbcType=BIGINT}
        </if>
        <if test="'totol_cnt'.toString() == column.value">
          #{item.totolCnt,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>