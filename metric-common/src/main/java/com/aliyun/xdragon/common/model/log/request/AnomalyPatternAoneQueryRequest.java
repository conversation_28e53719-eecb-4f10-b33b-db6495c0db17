package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/12
 */
@Data
public class AnomalyPatternAoneQueryRequest extends BaseTaskRequest implements Serializable {
    private static final long serialVersionUID = -3033961483736763445L;

    @NotBlank
    private String patternMd5;

    private String anomalyType;

    private Integer anomylyTime;

}
