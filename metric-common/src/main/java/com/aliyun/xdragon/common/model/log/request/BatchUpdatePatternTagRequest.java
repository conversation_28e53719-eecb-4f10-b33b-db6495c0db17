package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/21
 */
@Data
public class BatchUpdatePatternTagRequest implements Serializable {
    private static final long serialVersionUID = -2088712355572339278L;
    @Valid
    @NotNull
    @Size(min = 1, max = 25)
    List<UpdatePatternTagRequest> requests;
}
