package com.aliyun.xdragon.common.model.clientops.response;

import java.io.Serializable;
import java.util.List;

import com.aliyun.xdragon.common.model.clientops.UserCpuMetric;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/29
 */
@Data
public class RegionUserCpuMetricsResponse implements Serializable {
    private static final long serialVersionUID = 3899563118498619110L;
    /**
     * {@link UserCpuMetric}
     */
    List<UserCpuMetric> metrics;
}
