package com.aliyun.xdragon.common.generate.plan.model.map;

import com.aliyun.xdragon.common.generate.plan.model.GammaHost;
import com.aliyun.xdragon.common.generate.plan.model.GammaHostExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface GammaHostMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    long countByExample(GammaHostExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    int deleteByExample(GammaHostExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    int insert(GammaHost record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    int insertSelective(GammaHost record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    List<GammaHost> selectByExampleWithRowbounds(GammaHostExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    List<GammaHost> selectByExampleSelective(@Param("example") GammaHostExample example, @Param("selective") GammaHost.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    GammaHost selectOneByExample(GammaHostExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    GammaHost selectOneByExampleSelective(@Param("example") GammaHostExample example, @Param("selective") GammaHost.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    List<GammaHost> selectByExample(GammaHostExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    GammaHost selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") GammaHost.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    GammaHost selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") GammaHost record, @Param("example") GammaHostExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") GammaHost record, @Param("example") GammaHostExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(GammaHost record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(GammaHost record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<GammaHost> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<GammaHost> list, @Param("selective") GammaHost.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    int upsert(GammaHost record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_release_plan_gamma_host_v2
     *
     * @mbg.generated
     */
    int upsertSelective(GammaHost record);
}