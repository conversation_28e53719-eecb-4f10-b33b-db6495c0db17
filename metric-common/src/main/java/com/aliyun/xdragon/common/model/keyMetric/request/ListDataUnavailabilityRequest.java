package com.aliyun.xdragon.common.model.keyMetric.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;

/**
 * <AUTHOR> 2023/11/23
 */
@Data
public class ListDataUnavailabilityRequest extends BaseClientOpsRequest{

    private static final long serialVersionUID = -1745537869491149803L;

    @NotNull
    @PositiveOrZero
    Integer start;

    @NotNull
    @Positive
    Integer pageSize;
}
