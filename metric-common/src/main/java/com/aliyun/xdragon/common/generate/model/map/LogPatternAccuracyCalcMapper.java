package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalc;
import com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalcExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface LogPatternAccuracyCalcMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    long countByExample(LogPatternAccuracyCalcExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    int deleteByExample(LogPatternAccuracyCalcExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    int insert(LogPatternAccuracyCalc record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    int insertSelective(LogPatternAccuracyCalc record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    List<LogPatternAccuracyCalc> selectByExampleWithRowbounds(LogPatternAccuracyCalcExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    List<LogPatternAccuracyCalc> selectByExampleSelective(@Param("example") LogPatternAccuracyCalcExample example, @Param("selective") LogPatternAccuracyCalc.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    LogPatternAccuracyCalc selectOneByExample(LogPatternAccuracyCalcExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    LogPatternAccuracyCalc selectOneByExampleSelective(@Param("example") LogPatternAccuracyCalcExample example, @Param("selective") LogPatternAccuracyCalc.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    List<LogPatternAccuracyCalc> selectByExample(LogPatternAccuracyCalcExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    LogPatternAccuracyCalc selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") LogPatternAccuracyCalc.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    LogPatternAccuracyCalc selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") LogPatternAccuracyCalc record, @Param("example") LogPatternAccuracyCalcExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") LogPatternAccuracyCalc record, @Param("example") LogPatternAccuracyCalcExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(LogPatternAccuracyCalc record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(LogPatternAccuracyCalc record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<LogPatternAccuracyCalc> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<LogPatternAccuracyCalc> list, @Param("selective") LogPatternAccuracyCalc.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    int upsert(LogPatternAccuracyCalc record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_pattern_accuracy_calc
     *
     * @mbg.generated
     */
    int upsertSelective(LogPatternAccuracyCalc record);
}