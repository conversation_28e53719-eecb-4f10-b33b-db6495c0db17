package com.aliyun.xdragon.common.model.log;

import java.io.Serializable;
import java.util.List;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import org.apache.commons.lang3.tuple.Pair;

/**
 * <AUTHOR>
 * @date 2022/08/22
 */
public class ExtensionDistribution implements Serializable {
    @SerializedName("name")
    @Expose
    private String name;
    @SerializedName("tatal")
    @Expose
    private Long total;
    @SerializedName("detail")
    @Expose
    private List<Pair<String, Long>> detail;
    private static final long serialVersionUID = -1648143681415910226L;

    public ExtensionDistribution() {}

    public ExtensionDistribution(String name, Long total, List<Pair<String, Long>> detail) {
        this.name = name;
        this.total = total;
        this.detail = detail;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<Pair<String, Long>> getDetailMap() {
        return detail;
    }

    public void setDetailMap(List<Pair<String, Long>> detail) {
        this.detail = detail;
    }

    public List<Pair<String, Long>> getDetail() {
        return detail;
    }

    public void setDetail(List<Pair<String, Long>> detail) {
        this.detail = detail;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(ExtensionDistribution.class.getName()).append('@')
            .append(Integer.toHexString(System.identityHashCode(this)));
        sb.append('[');
        sb.append("name").append('=').append(name == null ? "<null>" : name);
        sb.append(',');
        sb.append("total").append('=').append(total == null ? "<null>" : total);
        sb.append(',');
        sb.append("detail").append('=');
        if (detail == null) {
            sb.append("<null>");
        } else {
            sb.append('{');
            for (Pair<String, Long> entry : detail) {
                sb.append(entry.getKey()).append('=').append(entry.getValue());
                sb.append(',');
            }
            if (sb.charAt(sb.length() - 1) == ',') {
                sb.delete(sb.length() - 1, sb.length());
            }
            sb.append('}');
        }
        sb.append(']');
        return sb.toString();
    }
}
