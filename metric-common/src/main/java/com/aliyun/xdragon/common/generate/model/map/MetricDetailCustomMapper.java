package com.aliyun.xdragon.common.generate.model.map;

import java.util.List;

import com.aliyun.xdragon.common.generate.model.MetricDetail;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2022/12/01
 */
public interface MetricDetailCustomMapper {
    int batchInsertOrUpdateSelective(@Param("list") List<MetricDetail> list, @Param("selective") MetricDetail.Column ... selective);
}
