package com.aliyun.xdragon.common.generate.log.model.map;

import java.util.List;

import com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetail;
import org.apache.ibatis.annotations.Param;

public interface AllinoneRiskNcDetailCustomMapper {
    int batchUpsert(@Param("list") List<AllinoneRiskNcDetail> list);

    Integer queryNcCntByRiskIds(@Param("riskIds") List<Long> riskIds);

    List<AllinoneRiskNcDetail> queryNcDetailByRiskIds(@Param("riskIds") List<Long> riskIds, @Param("offSet") Integer offSet, @Param("pageSize") Integer pageSize);
}
