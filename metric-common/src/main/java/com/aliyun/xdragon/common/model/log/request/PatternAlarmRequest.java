package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/02/16
 */
@Data
public class PatternAlarmRequest extends BasePatternAlarmRequest implements Serializable {
    private static final long serialVersionUID = -2669983401532151147L;

    /**
     * md5 list for alarm pattern
     */
    @NotNull
    List<String> patternMd5s;
}
