package com.aliyun.xdragon.common.model;

import java.io.Serializable;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/06/24
 */
@Data
public class SlsRegionProject implements Serializable {
    @SerializedName("real_project")
    @Expose
    private String realProject;
    @SerializedName("region")
    @Expose
    private String region;
    @SerializedName("endpoint")
    @Expose
    private String endpoint;
    @SerializedName("user")
    @Expose
    private String user;
    private final static long serialVersionUID = -8337241672922056441L;
}
