package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.MetricAnomalyConfig;
import com.aliyun.xdragon.common.generate.model.MetricAnomalyConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface MetricAnomalyConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    long countByExample(MetricAnomalyConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    int deleteByExample(MetricAnomalyConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long configId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    int insert(MetricAnomalyConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    int insertSelective(MetricAnomalyConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    List<MetricAnomalyConfig> selectByExampleWithRowbounds(MetricAnomalyConfigExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    List<MetricAnomalyConfig> selectByExampleSelective(@Param("example") MetricAnomalyConfigExample example, @Param("selective") MetricAnomalyConfig.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    MetricAnomalyConfig selectOneByExample(MetricAnomalyConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    MetricAnomalyConfig selectOneByExampleSelective(@Param("example") MetricAnomalyConfigExample example, @Param("selective") MetricAnomalyConfig.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    List<MetricAnomalyConfig> selectByExample(MetricAnomalyConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    MetricAnomalyConfig selectByPrimaryKeySelective(@Param("configId") Long configId, @Param("selective") MetricAnomalyConfig.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    MetricAnomalyConfig selectByPrimaryKey(Long configId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") MetricAnomalyConfig record, @Param("example") MetricAnomalyConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MetricAnomalyConfig record, @Param("example") MetricAnomalyConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MetricAnomalyConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MetricAnomalyConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<MetricAnomalyConfig> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<MetricAnomalyConfig> list, @Param("selective") MetricAnomalyConfig.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    int upsert(MetricAnomalyConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_anomaly
     *
     * @mbg.generated
     */
    int upsertSelective(MetricAnomalyConfig record);
}