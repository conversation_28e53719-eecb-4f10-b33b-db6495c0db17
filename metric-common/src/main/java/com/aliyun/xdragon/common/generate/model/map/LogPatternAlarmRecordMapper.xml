<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.model.map.LogPatternAlarmRecordMapper">
  <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.model.LogPatternAlarmRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="md5" jdbcType="VARCHAR" property="md5" />
    <result column="hashcode" jdbcType="INTEGER" property="hashcode" />
    <result column="region" jdbcType="VARCHAR" property="region" />
    <result column="logstore" jdbcType="VARCHAR" property="logstore" />
    <result column="support" jdbcType="BIGINT" property="support" />
    <result column="hit_release" jdbcType="BIT" property="hitRelease" />
    <result column="gen_aone" jdbcType="BIGINT" property="genAone" />
    <result column="anomaly_time" jdbcType="TIMESTAMP" property="anomalyTime" />
    <result column="alarm_time" jdbcType="TIMESTAMP" property="alarmTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="anomaly_type" jdbcType="VARCHAR" property="anomalyType" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="break_point_score" jdbcType="DOUBLE" property="breakPointScore" />
    <result column="weight" jdbcType="DOUBLE" property="weight" />
    <result column="trend_score" jdbcType="DOUBLE" property="trendScore" />
    <result column="volatility_score" jdbcType="DOUBLE" property="volatilityScore" />
    <result column="aone_type" jdbcType="INTEGER" property="aoneType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, task_id, md5, hashcode, region, logstore, support, hit_release, gen_aone, anomaly_time, 
    alarm_time, `status`, anomaly_type, `owner`, break_point_score, weight, trend_score, 
    volatility_score, aone_type
  </sql>
  <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAlarmRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from log_pattern_alarm_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="example != null and example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from log_pattern_alarm_record
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from log_pattern_alarm_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from log_pattern_alarm_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from log_pattern_alarm_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAlarmRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from log_pattern_alarm_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAlarmRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into log_pattern_alarm_record (task_id, md5, hashcode, 
      region, logstore, support, 
      hit_release, gen_aone, anomaly_time, 
      alarm_time, `status`, anomaly_type, 
      `owner`, break_point_score, weight, 
      trend_score, volatility_score, aone_type
      )
    values (#{taskId,jdbcType=BIGINT}, #{md5,jdbcType=VARCHAR}, #{hashcode,jdbcType=INTEGER}, 
      #{region,jdbcType=VARCHAR}, #{logstore,jdbcType=VARCHAR}, #{support,jdbcType=BIGINT}, 
      #{hitRelease,jdbcType=BIT}, #{genAone,jdbcType=BIGINT}, #{anomalyTime,jdbcType=TIMESTAMP}, 
      #{alarmTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{anomalyType,jdbcType=VARCHAR}, 
      #{owner,jdbcType=VARCHAR}, #{breakPointScore,jdbcType=DOUBLE}, #{weight,jdbcType=DOUBLE}, 
      #{trendScore,jdbcType=DOUBLE}, #{volatilityScore,jdbcType=DOUBLE}, #{aoneType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAlarmRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into log_pattern_alarm_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="md5 != null">
        md5,
      </if>
      <if test="hashcode != null">
        hashcode,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="logstore != null">
        logstore,
      </if>
      <if test="support != null">
        support,
      </if>
      <if test="hitRelease != null">
        hit_release,
      </if>
      <if test="genAone != null">
        gen_aone,
      </if>
      <if test="anomalyTime != null">
        anomaly_time,
      </if>
      <if test="alarmTime != null">
        alarm_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="anomalyType != null">
        anomaly_type,
      </if>
      <if test="owner != null">
        `owner`,
      </if>
      <if test="breakPointScore != null">
        break_point_score,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="trendScore != null">
        trend_score,
      </if>
      <if test="volatilityScore != null">
        volatility_score,
      </if>
      <if test="aoneType != null">
        aone_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="md5 != null">
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="hashcode != null">
        #{hashcode,jdbcType=INTEGER},
      </if>
      <if test="region != null">
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="logstore != null">
        #{logstore,jdbcType=VARCHAR},
      </if>
      <if test="support != null">
        #{support,jdbcType=BIGINT},
      </if>
      <if test="hitRelease != null">
        #{hitRelease,jdbcType=BIT},
      </if>
      <if test="genAone != null">
        #{genAone,jdbcType=BIGINT},
      </if>
      <if test="anomalyTime != null">
        #{anomalyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="alarmTime != null">
        #{alarmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="anomalyType != null">
        #{anomalyType,jdbcType=VARCHAR},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="breakPointScore != null">
        #{breakPointScore,jdbcType=DOUBLE},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=DOUBLE},
      </if>
      <if test="trendScore != null">
        #{trendScore,jdbcType=DOUBLE},
      </if>
      <if test="volatilityScore != null">
        #{volatilityScore,jdbcType=DOUBLE},
      </if>
      <if test="aoneType != null">
        #{aoneType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAlarmRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from log_pattern_alarm_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_pattern_alarm_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.md5 != null">
        md5 = #{record.md5,jdbcType=VARCHAR},
      </if>
      <if test="record.hashcode != null">
        hashcode = #{record.hashcode,jdbcType=INTEGER},
      </if>
      <if test="record.region != null">
        region = #{record.region,jdbcType=VARCHAR},
      </if>
      <if test="record.logstore != null">
        logstore = #{record.logstore,jdbcType=VARCHAR},
      </if>
      <if test="record.support != null">
        support = #{record.support,jdbcType=BIGINT},
      </if>
      <if test="record.hitRelease != null">
        hit_release = #{record.hitRelease,jdbcType=BIT},
      </if>
      <if test="record.genAone != null">
        gen_aone = #{record.genAone,jdbcType=BIGINT},
      </if>
      <if test="record.anomalyTime != null">
        anomaly_time = #{record.anomalyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.alarmTime != null">
        alarm_time = #{record.alarmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.anomalyType != null">
        anomaly_type = #{record.anomalyType,jdbcType=VARCHAR},
      </if>
      <if test="record.owner != null">
        `owner` = #{record.owner,jdbcType=VARCHAR},
      </if>
      <if test="record.breakPointScore != null">
        break_point_score = #{record.breakPointScore,jdbcType=DOUBLE},
      </if>
      <if test="record.weight != null">
        weight = #{record.weight,jdbcType=DOUBLE},
      </if>
      <if test="record.trendScore != null">
        trend_score = #{record.trendScore,jdbcType=DOUBLE},
      </if>
      <if test="record.volatilityScore != null">
        volatility_score = #{record.volatilityScore,jdbcType=DOUBLE},
      </if>
      <if test="record.aoneType != null">
        aone_type = #{record.aoneType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_pattern_alarm_record
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      md5 = #{record.md5,jdbcType=VARCHAR},
      hashcode = #{record.hashcode,jdbcType=INTEGER},
      region = #{record.region,jdbcType=VARCHAR},
      logstore = #{record.logstore,jdbcType=VARCHAR},
      support = #{record.support,jdbcType=BIGINT},
      hit_release = #{record.hitRelease,jdbcType=BIT},
      gen_aone = #{record.genAone,jdbcType=BIGINT},
      anomaly_time = #{record.anomalyTime,jdbcType=TIMESTAMP},
      alarm_time = #{record.alarmTime,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=INTEGER},
      anomaly_type = #{record.anomalyType,jdbcType=VARCHAR},
      `owner` = #{record.owner,jdbcType=VARCHAR},
      break_point_score = #{record.breakPointScore,jdbcType=DOUBLE},
      weight = #{record.weight,jdbcType=DOUBLE},
      trend_score = #{record.trendScore,jdbcType=DOUBLE},
      volatility_score = #{record.volatilityScore,jdbcType=DOUBLE},
      aone_type = #{record.aoneType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAlarmRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_pattern_alarm_record
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="md5 != null">
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="hashcode != null">
        hashcode = #{hashcode,jdbcType=INTEGER},
      </if>
      <if test="region != null">
        region = #{region,jdbcType=VARCHAR},
      </if>
      <if test="logstore != null">
        logstore = #{logstore,jdbcType=VARCHAR},
      </if>
      <if test="support != null">
        support = #{support,jdbcType=BIGINT},
      </if>
      <if test="hitRelease != null">
        hit_release = #{hitRelease,jdbcType=BIT},
      </if>
      <if test="genAone != null">
        gen_aone = #{genAone,jdbcType=BIGINT},
      </if>
      <if test="anomalyTime != null">
        anomaly_time = #{anomalyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="alarmTime != null">
        alarm_time = #{alarmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="anomalyType != null">
        anomaly_type = #{anomalyType,jdbcType=VARCHAR},
      </if>
      <if test="owner != null">
        `owner` = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="breakPointScore != null">
        break_point_score = #{breakPointScore,jdbcType=DOUBLE},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=DOUBLE},
      </if>
      <if test="trendScore != null">
        trend_score = #{trendScore,jdbcType=DOUBLE},
      </if>
      <if test="volatilityScore != null">
        volatility_score = #{volatilityScore,jdbcType=DOUBLE},
      </if>
      <if test="aoneType != null">
        aone_type = #{aoneType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAlarmRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update log_pattern_alarm_record
    set task_id = #{taskId,jdbcType=BIGINT},
      md5 = #{md5,jdbcType=VARCHAR},
      hashcode = #{hashcode,jdbcType=INTEGER},
      region = #{region,jdbcType=VARCHAR},
      logstore = #{logstore,jdbcType=VARCHAR},
      support = #{support,jdbcType=BIGINT},
      hit_release = #{hitRelease,jdbcType=BIT},
      gen_aone = #{genAone,jdbcType=BIGINT},
      anomaly_time = #{anomalyTime,jdbcType=TIMESTAMP},
      alarm_time = #{alarmTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      anomaly_type = #{anomalyType,jdbcType=VARCHAR},
      `owner` = #{owner,jdbcType=VARCHAR},
      break_point_score = #{breakPointScore,jdbcType=DOUBLE},
      weight = #{weight,jdbcType=DOUBLE},
      trend_score = #{trendScore,jdbcType=DOUBLE},
      volatility_score = #{volatilityScore,jdbcType=DOUBLE},
      aone_type = #{aoneType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAlarmRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from log_pattern_alarm_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_pattern_alarm_record
    (task_id, md5, hashcode, region, logstore, support, hit_release, gen_aone, anomaly_time, 
      alarm_time, `status`, anomaly_type, `owner`, break_point_score, weight, trend_score, 
      volatility_score, aone_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.taskId,jdbcType=BIGINT}, #{item.md5,jdbcType=VARCHAR}, #{item.hashcode,jdbcType=INTEGER}, 
        #{item.region,jdbcType=VARCHAR}, #{item.logstore,jdbcType=VARCHAR}, #{item.support,jdbcType=BIGINT}, 
        #{item.hitRelease,jdbcType=BIT}, #{item.genAone,jdbcType=BIGINT}, #{item.anomalyTime,jdbcType=TIMESTAMP}, 
        #{item.alarmTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=INTEGER}, #{item.anomalyType,jdbcType=VARCHAR}, 
        #{item.owner,jdbcType=VARCHAR}, #{item.breakPointScore,jdbcType=DOUBLE}, #{item.weight,jdbcType=DOUBLE}, 
        #{item.trendScore,jdbcType=DOUBLE}, #{item.volatilityScore,jdbcType=DOUBLE}, #{item.aoneType,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_pattern_alarm_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'task_id'.toString() == column.value">
          #{item.taskId,jdbcType=BIGINT}
        </if>
        <if test="'md5'.toString() == column.value">
          #{item.md5,jdbcType=VARCHAR}
        </if>
        <if test="'hashcode'.toString() == column.value">
          #{item.hashcode,jdbcType=INTEGER}
        </if>
        <if test="'region'.toString() == column.value">
          #{item.region,jdbcType=VARCHAR}
        </if>
        <if test="'logstore'.toString() == column.value">
          #{item.logstore,jdbcType=VARCHAR}
        </if>
        <if test="'support'.toString() == column.value">
          #{item.support,jdbcType=BIGINT}
        </if>
        <if test="'hit_release'.toString() == column.value">
          #{item.hitRelease,jdbcType=BIT}
        </if>
        <if test="'gen_aone'.toString() == column.value">
          #{item.genAone,jdbcType=BIGINT}
        </if>
        <if test="'anomaly_time'.toString() == column.value">
          #{item.anomalyTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'alarm_time'.toString() == column.value">
          #{item.alarmTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'anomaly_type'.toString() == column.value">
          #{item.anomalyType,jdbcType=VARCHAR}
        </if>
        <if test="'owner'.toString() == column.value">
          #{item.owner,jdbcType=VARCHAR}
        </if>
        <if test="'break_point_score'.toString() == column.value">
          #{item.breakPointScore,jdbcType=DOUBLE}
        </if>
        <if test="'weight'.toString() == column.value">
          #{item.weight,jdbcType=DOUBLE}
        </if>
        <if test="'trend_score'.toString() == column.value">
          #{item.trendScore,jdbcType=DOUBLE}
        </if>
        <if test="'volatility_score'.toString() == column.value">
          #{item.volatilityScore,jdbcType=DOUBLE}
        </if>
        <if test="'aone_type'.toString() == column.value">
          #{item.aoneType,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
  <insert id="upsertSelective" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAlarmRecord" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_pattern_alarm_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="md5 != null">
        md5,
      </if>
      <if test="hashcode != null">
        hashcode,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="logstore != null">
        logstore,
      </if>
      <if test="support != null">
        support,
      </if>
      <if test="hitRelease != null">
        hit_release,
      </if>
      <if test="genAone != null">
        gen_aone,
      </if>
      <if test="anomalyTime != null">
        anomaly_time,
      </if>
      <if test="alarmTime != null">
        alarm_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="anomalyType != null">
        anomaly_type,
      </if>
      <if test="owner != null">
        `owner`,
      </if>
      <if test="breakPointScore != null">
        break_point_score,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="trendScore != null">
        trend_score,
      </if>
      <if test="volatilityScore != null">
        volatility_score,
      </if>
      <if test="aoneType != null">
        aone_type,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="md5 != null">
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="hashcode != null">
        #{hashcode,jdbcType=INTEGER},
      </if>
      <if test="region != null">
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="logstore != null">
        #{logstore,jdbcType=VARCHAR},
      </if>
      <if test="support != null">
        #{support,jdbcType=BIGINT},
      </if>
      <if test="hitRelease != null">
        #{hitRelease,jdbcType=BIT},
      </if>
      <if test="genAone != null">
        #{genAone,jdbcType=BIGINT},
      </if>
      <if test="anomalyTime != null">
        #{anomalyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="alarmTime != null">
        #{alarmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="anomalyType != null">
        #{anomalyType,jdbcType=VARCHAR},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="breakPointScore != null">
        #{breakPointScore,jdbcType=DOUBLE},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=DOUBLE},
      </if>
      <if test="trendScore != null">
        #{trendScore,jdbcType=DOUBLE},
      </if>
      <if test="volatilityScore != null">
        #{volatilityScore,jdbcType=DOUBLE},
      </if>
      <if test="aoneType != null">
        #{aoneType,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="md5 != null">
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="hashcode != null">
        hashcode = #{hashcode,jdbcType=INTEGER},
      </if>
      <if test="region != null">
        region = #{region,jdbcType=VARCHAR},
      </if>
      <if test="logstore != null">
        logstore = #{logstore,jdbcType=VARCHAR},
      </if>
      <if test="support != null">
        support = #{support,jdbcType=BIGINT},
      </if>
      <if test="hitRelease != null">
        hit_release = #{hitRelease,jdbcType=BIT},
      </if>
      <if test="genAone != null">
        gen_aone = #{genAone,jdbcType=BIGINT},
      </if>
      <if test="anomalyTime != null">
        anomaly_time = #{anomalyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="alarmTime != null">
        alarm_time = #{alarmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="anomalyType != null">
        anomaly_type = #{anomalyType,jdbcType=VARCHAR},
      </if>
      <if test="owner != null">
        `owner` = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="breakPointScore != null">
        break_point_score = #{breakPointScore,jdbcType=DOUBLE},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=DOUBLE},
      </if>
      <if test="trendScore != null">
        trend_score = #{trendScore,jdbcType=DOUBLE},
      </if>
      <if test="volatilityScore != null">
        volatility_score = #{volatilityScore,jdbcType=DOUBLE},
      </if>
      <if test="aoneType != null">
        aone_type = #{aoneType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <insert id="upsert" keyColumn="id" keyProperty="id" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAlarmRecord" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into log_pattern_alarm_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      task_id,
      md5,
      hashcode,
      region,
      logstore,
      support,
      hit_release,
      gen_aone,
      anomaly_time,
      alarm_time,
      `status`,
      anomaly_type,
      `owner`,
      break_point_score,
      weight,
      trend_score,
      volatility_score,
      aone_type,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{taskId,jdbcType=BIGINT},
      #{md5,jdbcType=VARCHAR},
      #{hashcode,jdbcType=INTEGER},
      #{region,jdbcType=VARCHAR},
      #{logstore,jdbcType=VARCHAR},
      #{support,jdbcType=BIGINT},
      #{hitRelease,jdbcType=BIT},
      #{genAone,jdbcType=BIGINT},
      #{anomalyTime,jdbcType=TIMESTAMP},
      #{alarmTime,jdbcType=TIMESTAMP},
      #{status,jdbcType=INTEGER},
      #{anomalyType,jdbcType=VARCHAR},
      #{owner,jdbcType=VARCHAR},
      #{breakPointScore,jdbcType=DOUBLE},
      #{weight,jdbcType=DOUBLE},
      #{trendScore,jdbcType=DOUBLE},
      #{volatilityScore,jdbcType=DOUBLE},
      #{aoneType,jdbcType=INTEGER},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      task_id = #{taskId,jdbcType=BIGINT},
      md5 = #{md5,jdbcType=VARCHAR},
      hashcode = #{hashcode,jdbcType=INTEGER},
      region = #{region,jdbcType=VARCHAR},
      logstore = #{logstore,jdbcType=VARCHAR},
      support = #{support,jdbcType=BIGINT},
      hit_release = #{hitRelease,jdbcType=BIT},
      gen_aone = #{genAone,jdbcType=BIGINT},
      anomaly_time = #{anomalyTime,jdbcType=TIMESTAMP},
      alarm_time = #{alarmTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      anomaly_type = #{anomalyType,jdbcType=VARCHAR},
      `owner` = #{owner,jdbcType=VARCHAR},
      break_point_score = #{breakPointScore,jdbcType=DOUBLE},
      weight = #{weight,jdbcType=DOUBLE},
      trend_score = #{trendScore,jdbcType=DOUBLE},
      volatility_score = #{volatilityScore,jdbcType=DOUBLE},
      aone_type = #{aoneType,jdbcType=INTEGER},
    </trim>
  </insert>
  <select id="selectOneByExample" parameterType="com.aliyun.xdragon.common.generate.model.LogPatternAlarmRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from log_pattern_alarm_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from log_pattern_alarm_record
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
</mapper>