package com.aliyun.xdragon.common.generate.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

public class WorkItemClassificationDetailWithBLOBs extends WorkItemClassificationDetail implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column work_item_classification_detail.subject
     *
     * @mbg.generated
     */
    private String subject;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column work_item_classification_detail.description
     *
     * @mbg.generated
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column work_item_classification_detail.instance_info
     *
     * @mbg.generated
     */
    private String instanceInfo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column work_item_classification_detail.action_detail
     *
     * @mbg.generated
     */
    private String actionDetail;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column work_item_classification_detail.comments
     *
     * @mbg.generated
     */
    private String comments;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column work_item_classification_detail.feed_back
     *
     * @mbg.generated
     */
    private String feedBack;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column work_item_classification_detail.subject
     *
     * @return the value of work_item_classification_detail.subject
     *
     * @mbg.generated
     */
    public String getSubject() {
        return subject;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column work_item_classification_detail.subject
     *
     * @param subject the value for work_item_classification_detail.subject
     *
     * @mbg.generated
     */
    public void setSubject(String subject) {
        this.subject = subject;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column work_item_classification_detail.description
     *
     * @return the value of work_item_classification_detail.description
     *
     * @mbg.generated
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column work_item_classification_detail.description
     *
     * @param description the value for work_item_classification_detail.description
     *
     * @mbg.generated
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column work_item_classification_detail.instance_info
     *
     * @return the value of work_item_classification_detail.instance_info
     *
     * @mbg.generated
     */
    public String getInstanceInfo() {
        return instanceInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column work_item_classification_detail.instance_info
     *
     * @param instanceInfo the value for work_item_classification_detail.instance_info
     *
     * @mbg.generated
     */
    public void setInstanceInfo(String instanceInfo) {
        this.instanceInfo = instanceInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column work_item_classification_detail.action_detail
     *
     * @return the value of work_item_classification_detail.action_detail
     *
     * @mbg.generated
     */
    public String getActionDetail() {
        return actionDetail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column work_item_classification_detail.action_detail
     *
     * @param actionDetail the value for work_item_classification_detail.action_detail
     *
     * @mbg.generated
     */
    public void setActionDetail(String actionDetail) {
        this.actionDetail = actionDetail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column work_item_classification_detail.comments
     *
     * @return the value of work_item_classification_detail.comments
     *
     * @mbg.generated
     */
    public String getComments() {
        return comments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column work_item_classification_detail.comments
     *
     * @param comments the value for work_item_classification_detail.comments
     *
     * @mbg.generated
     */
    public void setComments(String comments) {
        this.comments = comments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column work_item_classification_detail.feed_back
     *
     * @return the value of work_item_classification_detail.feed_back
     *
     * @mbg.generated
     */
    public String getFeedBack() {
        return feedBack;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column work_item_classification_detail.feed_back
     *
     * @param feedBack the value for work_item_classification_detail.feed_back
     *
     * @mbg.generated
     */
    public void setFeedBack(String feedBack) {
        this.feedBack = feedBack;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        WorkItemClassificationDetailWithBLOBs other = (WorkItemClassificationDetailWithBLOBs) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtModified() == null ? other.getGmtModified() == null : this.getGmtModified().equals(other.getGmtModified()))
            && (this.getSourceId() == null ? other.getSourceId() == null : this.getSourceId().equals(other.getSourceId()))
            && (this.getIpInfo() == null ? other.getIpInfo() == null : this.getIpInfo().equals(other.getIpInfo()))
            && (this.getProduct() == null ? other.getProduct() == null : this.getProduct().equals(other.getProduct()))
            && (this.getProject() == null ? other.getProject() == null : this.getProject().equals(other.getProject()))
            && (this.getCommitTime() == null ? other.getCommitTime() == null : this.getCommitTime().equals(other.getCommitTime()))
            && (this.getSourceUrl() == null ? other.getSourceUrl() == null : this.getSourceUrl().equals(other.getSourceUrl()))
            && (this.getDataSource() == null ? other.getDataSource() == null : this.getDataSource().equals(other.getDataSource()))
            && (this.getIssueUrl() == null ? other.getIssueUrl() == null : this.getIssueUrl().equals(other.getIssueUrl()))
            && (this.getLabelOneId() == null ? other.getLabelOneId() == null : this.getLabelOneId().equals(other.getLabelOneId()))
            && (this.getCorrectedLabelOneId() == null ? other.getCorrectedLabelOneId() == null : this.getCorrectedLabelOneId().equals(other.getCorrectedLabelOneId()))
            && (this.getLabelTwoId() == null ? other.getLabelTwoId() == null : this.getLabelTwoId().equals(other.getLabelTwoId()))
            && (this.getSourceState() == null ? other.getSourceState() == null : this.getSourceState().equals(other.getSourceState()))
            && (this.getExceptionTime() == null ? other.getExceptionTime() == null : this.getExceptionTime().equals(other.getExceptionTime()))
            && (this.getAoneUrl() == null ? other.getAoneUrl() == null : this.getAoneUrl().equals(other.getAoneUrl()))
            && (this.getAoneState() == null ? other.getAoneState() == null : this.getAoneState().equals(other.getAoneState()))
            && (this.getOwner() == null ? other.getOwner() == null : this.getOwner().equals(other.getOwner()))
            && (this.getSubject() == null ? other.getSubject() == null : this.getSubject().equals(other.getSubject()))
            && (this.getDescription() == null ? other.getDescription() == null : this.getDescription().equals(other.getDescription()))
            && (this.getInstanceInfo() == null ? other.getInstanceInfo() == null : this.getInstanceInfo().equals(other.getInstanceInfo()))
            && (this.getActionDetail() == null ? other.getActionDetail() == null : this.getActionDetail().equals(other.getActionDetail()))
            && (this.getComments() == null ? other.getComments() == null : this.getComments().equals(other.getComments()))
            && (this.getFeedBack() == null ? other.getFeedBack() == null : this.getFeedBack().equals(other.getFeedBack()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtModified() == null) ? 0 : getGmtModified().hashCode());
        result = prime * result + ((getSourceId() == null) ? 0 : getSourceId().hashCode());
        result = prime * result + ((getIpInfo() == null) ? 0 : getIpInfo().hashCode());
        result = prime * result + ((getProduct() == null) ? 0 : getProduct().hashCode());
        result = prime * result + ((getProject() == null) ? 0 : getProject().hashCode());
        result = prime * result + ((getCommitTime() == null) ? 0 : getCommitTime().hashCode());
        result = prime * result + ((getSourceUrl() == null) ? 0 : getSourceUrl().hashCode());
        result = prime * result + ((getDataSource() == null) ? 0 : getDataSource().hashCode());
        result = prime * result + ((getIssueUrl() == null) ? 0 : getIssueUrl().hashCode());
        result = prime * result + ((getLabelOneId() == null) ? 0 : getLabelOneId().hashCode());
        result = prime * result + ((getCorrectedLabelOneId() == null) ? 0 : getCorrectedLabelOneId().hashCode());
        result = prime * result + ((getLabelTwoId() == null) ? 0 : getLabelTwoId().hashCode());
        result = prime * result + ((getSourceState() == null) ? 0 : getSourceState().hashCode());
        result = prime * result + ((getExceptionTime() == null) ? 0 : getExceptionTime().hashCode());
        result = prime * result + ((getAoneUrl() == null) ? 0 : getAoneUrl().hashCode());
        result = prime * result + ((getAoneState() == null) ? 0 : getAoneState().hashCode());
        result = prime * result + ((getOwner() == null) ? 0 : getOwner().hashCode());
        result = prime * result + ((getSubject() == null) ? 0 : getSubject().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getInstanceInfo() == null) ? 0 : getInstanceInfo().hashCode());
        result = prime * result + ((getActionDetail() == null) ? 0 : getActionDetail().hashCode());
        result = prime * result + ((getComments() == null) ? 0 : getComments().hashCode());
        result = prime * result + ((getFeedBack() == null) ? 0 : getFeedBack().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", subject=").append(subject);
        sb.append(", description=").append(description);
        sb.append(", instanceInfo=").append(instanceInfo);
        sb.append(", actionDetail=").append(actionDetail);
        sb.append(", comments=").append(comments);
        sb.append(", feedBack=").append(feedBack);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table work_item_classification_detail
     *
     * @mbg.generated
     */
    public enum Column {
        id("id", "id", "BIGINT", false),
        gmtCreate("gmt_create", "gmtCreate", "TIMESTAMP", false),
        gmtModified("gmt_modified", "gmtModified", "TIMESTAMP", false),
        sourceId("source_id", "sourceId", "VARCHAR", false),
        ipInfo("ip_info", "ipInfo", "VARCHAR", false),
        product("product", "product", "VARCHAR", false),
        project("project", "project", "VARCHAR", false),
        commitTime("commit_time", "commitTime", "VARCHAR", false),
        sourceUrl("source_url", "sourceUrl", "VARCHAR", false),
        dataSource("data_source", "dataSource", "VARCHAR", false),
        issueUrl("issue_url", "issueUrl", "VARCHAR", false),
        labelOneId("label_one_id", "labelOneId", "BIGINT", false),
        correctedLabelOneId("corrected_label_one_id", "correctedLabelOneId", "BIGINT", false),
        labelTwoId("label_two_id", "labelTwoId", "VARCHAR", false),
        sourceState("source_state", "sourceState", "VARCHAR", false),
        exceptionTime("exception_time", "exceptionTime", "VARCHAR", false),
        aoneUrl("aone_url", "aoneUrl", "VARCHAR", false),
        aoneState("aone_state", "aoneState", "VARCHAR", false),
        owner("owner", "owner", "VARCHAR", true),
        subject("subject", "subject", "LONGVARCHAR", false),
        description("description", "description", "LONGVARCHAR", false),
        instanceInfo("instance_info", "instanceInfo", "LONGVARCHAR", false),
        actionDetail("action_detail", "actionDetail", "LONGVARCHAR", false),
        comments("comments", "comments", "LONGVARCHAR", false),
        feedBack("feed_back", "feedBack", "LONGVARCHAR", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table work_item_classification_detail
         *
         * @mbg.generated
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}