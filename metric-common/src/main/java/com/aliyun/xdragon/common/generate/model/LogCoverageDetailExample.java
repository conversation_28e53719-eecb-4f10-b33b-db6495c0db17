package com.aliyun.xdragon.common.generate.model;

import java.util.ArrayList;
import java.util.List;

public class LogCoverageDetailExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    public LogCoverageDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andMetricIsNull() {
            addCriterion("metric is null");
            return (Criteria) this;
        }

        public Criteria andMetricIsNotNull() {
            addCriterion("metric is not null");
            return (Criteria) this;
        }

        public Criteria andMetricEqualTo(String value) {
            addCriterion("metric =", value, "metric");
            return (Criteria) this;
        }

        public Criteria andMetricNotEqualTo(String value) {
            addCriterion("metric <>", value, "metric");
            return (Criteria) this;
        }

        public Criteria andMetricGreaterThan(String value) {
            addCriterion("metric >", value, "metric");
            return (Criteria) this;
        }

        public Criteria andMetricGreaterThanOrEqualTo(String value) {
            addCriterion("metric >=", value, "metric");
            return (Criteria) this;
        }

        public Criteria andMetricLessThan(String value) {
            addCriterion("metric <", value, "metric");
            return (Criteria) this;
        }

        public Criteria andMetricLessThanOrEqualTo(String value) {
            addCriterion("metric <=", value, "metric");
            return (Criteria) this;
        }

        public Criteria andMetricLike(String value) {
            addCriterion("metric like", value, "metric");
            return (Criteria) this;
        }

        public Criteria andMetricNotLike(String value) {
            addCriterion("metric not like", value, "metric");
            return (Criteria) this;
        }

        public Criteria andMetricIn(List<String> values) {
            addCriterion("metric in", values, "metric");
            return (Criteria) this;
        }

        public Criteria andMetricNotIn(List<String> values) {
            addCriterion("metric not in", values, "metric");
            return (Criteria) this;
        }

        public Criteria andMetricBetween(String value1, String value2) {
            addCriterion("metric between", value1, value2, "metric");
            return (Criteria) this;
        }

        public Criteria andMetricNotBetween(String value1, String value2) {
            addCriterion("metric not between", value1, value2, "metric");
            return (Criteria) this;
        }

        public Criteria andTimestampIsNull() {
            addCriterion("`timestamp` is null");
            return (Criteria) this;
        }

        public Criteria andTimestampIsNotNull() {
            addCriterion("`timestamp` is not null");
            return (Criteria) this;
        }

        public Criteria andTimestampEqualTo(Integer value) {
            addCriterion("`timestamp` =", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampNotEqualTo(Integer value) {
            addCriterion("`timestamp` <>", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampGreaterThan(Integer value) {
            addCriterion("`timestamp` >", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampGreaterThanOrEqualTo(Integer value) {
            addCriterion("`timestamp` >=", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampLessThan(Integer value) {
            addCriterion("`timestamp` <", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampLessThanOrEqualTo(Integer value) {
            addCriterion("`timestamp` <=", value, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampIn(List<Integer> values) {
            addCriterion("`timestamp` in", values, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampNotIn(List<Integer> values) {
            addCriterion("`timestamp` not in", values, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampBetween(Integer value1, Integer value2) {
            addCriterion("`timestamp` between", value1, value2, "timestamp");
            return (Criteria) this;
        }

        public Criteria andTimestampNotBetween(Integer value1, Integer value2) {
            addCriterion("`timestamp` not between", value1, value2, "timestamp");
            return (Criteria) this;
        }

        public Criteria andValueIsNull() {
            addCriterion("`value` is null");
            return (Criteria) this;
        }

        public Criteria andValueIsNotNull() {
            addCriterion("`value` is not null");
            return (Criteria) this;
        }

        public Criteria andValueEqualTo(String value) {
            addCriterion("`value` =", value, "value");
            return (Criteria) this;
        }

        public Criteria andValueNotEqualTo(String value) {
            addCriterion("`value` <>", value, "value");
            return (Criteria) this;
        }

        public Criteria andValueGreaterThan(String value) {
            addCriterion("`value` >", value, "value");
            return (Criteria) this;
        }

        public Criteria andValueGreaterThanOrEqualTo(String value) {
            addCriterion("`value` >=", value, "value");
            return (Criteria) this;
        }

        public Criteria andValueLessThan(String value) {
            addCriterion("`value` <", value, "value");
            return (Criteria) this;
        }

        public Criteria andValueLessThanOrEqualTo(String value) {
            addCriterion("`value` <=", value, "value");
            return (Criteria) this;
        }

        public Criteria andValueLike(String value) {
            addCriterion("`value` like", value, "value");
            return (Criteria) this;
        }

        public Criteria andValueNotLike(String value) {
            addCriterion("`value` not like", value, "value");
            return (Criteria) this;
        }

        public Criteria andValueIn(List<String> values) {
            addCriterion("`value` in", values, "value");
            return (Criteria) this;
        }

        public Criteria andValueNotIn(List<String> values) {
            addCriterion("`value` not in", values, "value");
            return (Criteria) this;
        }

        public Criteria andValueBetween(String value1, String value2) {
            addCriterion("`value` between", value1, value2, "value");
            return (Criteria) this;
        }

        public Criteria andValueNotBetween(String value1, String value2) {
            addCriterion("`value` not between", value1, value2, "value");
            return (Criteria) this;
        }

        public Criteria andRegionIsNull() {
            addCriterion("region is null");
            return (Criteria) this;
        }

        public Criteria andRegionIsNotNull() {
            addCriterion("region is not null");
            return (Criteria) this;
        }

        public Criteria andRegionEqualTo(String value) {
            addCriterion("region =", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotEqualTo(String value) {
            addCriterion("region <>", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionGreaterThan(String value) {
            addCriterion("region >", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionGreaterThanOrEqualTo(String value) {
            addCriterion("region >=", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLessThan(String value) {
            addCriterion("region <", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLessThanOrEqualTo(String value) {
            addCriterion("region <=", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionLike(String value) {
            addCriterion("region like", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotLike(String value) {
            addCriterion("region not like", value, "region");
            return (Criteria) this;
        }

        public Criteria andRegionIn(List<String> values) {
            addCriterion("region in", values, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotIn(List<String> values) {
            addCriterion("region not in", values, "region");
            return (Criteria) this;
        }

        public Criteria andRegionBetween(String value1, String value2) {
            addCriterion("region between", value1, value2, "region");
            return (Criteria) this;
        }

        public Criteria andRegionNotBetween(String value1, String value2) {
            addCriterion("region not between", value1, value2, "region");
            return (Criteria) this;
        }

        public Criteria andTotalNcIsNull() {
            addCriterion("total_nc is null");
            return (Criteria) this;
        }

        public Criteria andTotalNcIsNotNull() {
            addCriterion("total_nc is not null");
            return (Criteria) this;
        }

        public Criteria andTotalNcEqualTo(Integer value) {
            addCriterion("total_nc =", value, "totalNc");
            return (Criteria) this;
        }

        public Criteria andTotalNcNotEqualTo(Integer value) {
            addCriterion("total_nc <>", value, "totalNc");
            return (Criteria) this;
        }

        public Criteria andTotalNcGreaterThan(Integer value) {
            addCriterion("total_nc >", value, "totalNc");
            return (Criteria) this;
        }

        public Criteria andTotalNcGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_nc >=", value, "totalNc");
            return (Criteria) this;
        }

        public Criteria andTotalNcLessThan(Integer value) {
            addCriterion("total_nc <", value, "totalNc");
            return (Criteria) this;
        }

        public Criteria andTotalNcLessThanOrEqualTo(Integer value) {
            addCriterion("total_nc <=", value, "totalNc");
            return (Criteria) this;
        }

        public Criteria andTotalNcIn(List<Integer> values) {
            addCriterion("total_nc in", values, "totalNc");
            return (Criteria) this;
        }

        public Criteria andTotalNcNotIn(List<Integer> values) {
            addCriterion("total_nc not in", values, "totalNc");
            return (Criteria) this;
        }

        public Criteria andTotalNcBetween(Integer value1, Integer value2) {
            addCriterion("total_nc between", value1, value2, "totalNc");
            return (Criteria) this;
        }

        public Criteria andTotalNcNotBetween(Integer value1, Integer value2) {
            addCriterion("total_nc not between", value1, value2, "totalNc");
            return (Criteria) this;
        }

        public Criteria andLogNcIsNull() {
            addCriterion("log_nc is null");
            return (Criteria) this;
        }

        public Criteria andLogNcIsNotNull() {
            addCriterion("log_nc is not null");
            return (Criteria) this;
        }

        public Criteria andLogNcEqualTo(Integer value) {
            addCriterion("log_nc =", value, "logNc");
            return (Criteria) this;
        }

        public Criteria andLogNcNotEqualTo(Integer value) {
            addCriterion("log_nc <>", value, "logNc");
            return (Criteria) this;
        }

        public Criteria andLogNcGreaterThan(Integer value) {
            addCriterion("log_nc >", value, "logNc");
            return (Criteria) this;
        }

        public Criteria andLogNcGreaterThanOrEqualTo(Integer value) {
            addCriterion("log_nc >=", value, "logNc");
            return (Criteria) this;
        }

        public Criteria andLogNcLessThan(Integer value) {
            addCriterion("log_nc <", value, "logNc");
            return (Criteria) this;
        }

        public Criteria andLogNcLessThanOrEqualTo(Integer value) {
            addCriterion("log_nc <=", value, "logNc");
            return (Criteria) this;
        }

        public Criteria andLogNcIn(List<Integer> values) {
            addCriterion("log_nc in", values, "logNc");
            return (Criteria) this;
        }

        public Criteria andLogNcNotIn(List<Integer> values) {
            addCriterion("log_nc not in", values, "logNc");
            return (Criteria) this;
        }

        public Criteria andLogNcBetween(Integer value1, Integer value2) {
            addCriterion("log_nc between", value1, value2, "logNc");
            return (Criteria) this;
        }

        public Criteria andLogNcNotBetween(Integer value1, Integer value2) {
            addCriterion("log_nc not between", value1, value2, "logNc");
            return (Criteria) this;
        }

        public Criteria andProjectIsNull() {
            addCriterion("project is null");
            return (Criteria) this;
        }

        public Criteria andProjectIsNotNull() {
            addCriterion("project is not null");
            return (Criteria) this;
        }

        public Criteria andProjectEqualTo(String value) {
            addCriterion("project =", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotEqualTo(String value) {
            addCriterion("project <>", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThan(String value) {
            addCriterion("project >", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanOrEqualTo(String value) {
            addCriterion("project >=", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLessThan(String value) {
            addCriterion("project <", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLessThanOrEqualTo(String value) {
            addCriterion("project <=", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLike(String value) {
            addCriterion("project like", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotLike(String value) {
            addCriterion("project not like", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectIn(List<String> values) {
            addCriterion("project in", values, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotIn(List<String> values) {
            addCriterion("project not in", values, "project");
            return (Criteria) this;
        }

        public Criteria andProjectBetween(String value1, String value2) {
            addCriterion("project between", value1, value2, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotBetween(String value1, String value2) {
            addCriterion("project not between", value1, value2, "project");
            return (Criteria) this;
        }

        public Criteria andLogstoreIsNull() {
            addCriterion("logstore is null");
            return (Criteria) this;
        }

        public Criteria andLogstoreIsNotNull() {
            addCriterion("logstore is not null");
            return (Criteria) this;
        }

        public Criteria andLogstoreEqualTo(String value) {
            addCriterion("logstore =", value, "logstore");
            return (Criteria) this;
        }

        public Criteria andLogstoreNotEqualTo(String value) {
            addCriterion("logstore <>", value, "logstore");
            return (Criteria) this;
        }

        public Criteria andLogstoreGreaterThan(String value) {
            addCriterion("logstore >", value, "logstore");
            return (Criteria) this;
        }

        public Criteria andLogstoreGreaterThanOrEqualTo(String value) {
            addCriterion("logstore >=", value, "logstore");
            return (Criteria) this;
        }

        public Criteria andLogstoreLessThan(String value) {
            addCriterion("logstore <", value, "logstore");
            return (Criteria) this;
        }

        public Criteria andLogstoreLessThanOrEqualTo(String value) {
            addCriterion("logstore <=", value, "logstore");
            return (Criteria) this;
        }

        public Criteria andLogstoreLike(String value) {
            addCriterion("logstore like", value, "logstore");
            return (Criteria) this;
        }

        public Criteria andLogstoreNotLike(String value) {
            addCriterion("logstore not like", value, "logstore");
            return (Criteria) this;
        }

        public Criteria andLogstoreIn(List<String> values) {
            addCriterion("logstore in", values, "logstore");
            return (Criteria) this;
        }

        public Criteria andLogstoreNotIn(List<String> values) {
            addCriterion("logstore not in", values, "logstore");
            return (Criteria) this;
        }

        public Criteria andLogstoreBetween(String value1, String value2) {
            addCriterion("logstore between", value1, value2, "logstore");
            return (Criteria) this;
        }

        public Criteria andLogstoreNotBetween(String value1, String value2) {
            addCriterion("logstore not between", value1, value2, "logstore");
            return (Criteria) this;
        }

        public Criteria andPickIntervalIsNull() {
            addCriterion("pick_interval is null");
            return (Criteria) this;
        }

        public Criteria andPickIntervalIsNotNull() {
            addCriterion("pick_interval is not null");
            return (Criteria) this;
        }

        public Criteria andPickIntervalEqualTo(Integer value) {
            addCriterion("pick_interval =", value, "pickInterval");
            return (Criteria) this;
        }

        public Criteria andPickIntervalNotEqualTo(Integer value) {
            addCriterion("pick_interval <>", value, "pickInterval");
            return (Criteria) this;
        }

        public Criteria andPickIntervalGreaterThan(Integer value) {
            addCriterion("pick_interval >", value, "pickInterval");
            return (Criteria) this;
        }

        public Criteria andPickIntervalGreaterThanOrEqualTo(Integer value) {
            addCriterion("pick_interval >=", value, "pickInterval");
            return (Criteria) this;
        }

        public Criteria andPickIntervalLessThan(Integer value) {
            addCriterion("pick_interval <", value, "pickInterval");
            return (Criteria) this;
        }

        public Criteria andPickIntervalLessThanOrEqualTo(Integer value) {
            addCriterion("pick_interval <=", value, "pickInterval");
            return (Criteria) this;
        }

        public Criteria andPickIntervalIn(List<Integer> values) {
            addCriterion("pick_interval in", values, "pickInterval");
            return (Criteria) this;
        }

        public Criteria andPickIntervalNotIn(List<Integer> values) {
            addCriterion("pick_interval not in", values, "pickInterval");
            return (Criteria) this;
        }

        public Criteria andPickIntervalBetween(Integer value1, Integer value2) {
            addCriterion("pick_interval between", value1, value2, "pickInterval");
            return (Criteria) this;
        }

        public Criteria andPickIntervalNotBetween(Integer value1, Integer value2) {
            addCriterion("pick_interval not between", value1, value2, "pickInterval");
            return (Criteria) this;
        }

        public Criteria andIsTotalIsNull() {
            addCriterion("is_total is null");
            return (Criteria) this;
        }

        public Criteria andIsTotalIsNotNull() {
            addCriterion("is_total is not null");
            return (Criteria) this;
        }

        public Criteria andIsTotalEqualTo(Boolean value) {
            addCriterion("is_total =", value, "isTotal");
            return (Criteria) this;
        }

        public Criteria andIsTotalNotEqualTo(Boolean value) {
            addCriterion("is_total <>", value, "isTotal");
            return (Criteria) this;
        }

        public Criteria andIsTotalGreaterThan(Boolean value) {
            addCriterion("is_total >", value, "isTotal");
            return (Criteria) this;
        }

        public Criteria andIsTotalGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_total >=", value, "isTotal");
            return (Criteria) this;
        }

        public Criteria andIsTotalLessThan(Boolean value) {
            addCriterion("is_total <", value, "isTotal");
            return (Criteria) this;
        }

        public Criteria andIsTotalLessThanOrEqualTo(Boolean value) {
            addCriterion("is_total <=", value, "isTotal");
            return (Criteria) this;
        }

        public Criteria andIsTotalIn(List<Boolean> values) {
            addCriterion("is_total in", values, "isTotal");
            return (Criteria) this;
        }

        public Criteria andIsTotalNotIn(List<Boolean> values) {
            addCriterion("is_total not in", values, "isTotal");
            return (Criteria) this;
        }

        public Criteria andIsTotalBetween(Boolean value1, Boolean value2) {
            addCriterion("is_total between", value1, value2, "isTotal");
            return (Criteria) this;
        }

        public Criteria andIsTotalNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_total not between", value1, value2, "isTotal");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Long value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Long value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Long value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Long value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Long value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Long value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Long> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Long> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Long value1, Long value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Long value1, Long value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Long value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Long value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Long value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Long value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Long value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Long value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Long> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Long> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Long value1, Long value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Long value1, Long value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andIsFixedIsNull() {
            addCriterion("is_fixed is null");
            return (Criteria) this;
        }

        public Criteria andIsFixedIsNotNull() {
            addCriterion("is_fixed is not null");
            return (Criteria) this;
        }

        public Criteria andIsFixedEqualTo(Boolean value) {
            addCriterion("is_fixed =", value, "isFixed");
            return (Criteria) this;
        }

        public Criteria andIsFixedNotEqualTo(Boolean value) {
            addCriterion("is_fixed <>", value, "isFixed");
            return (Criteria) this;
        }

        public Criteria andIsFixedGreaterThan(Boolean value) {
            addCriterion("is_fixed >", value, "isFixed");
            return (Criteria) this;
        }

        public Criteria andIsFixedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_fixed >=", value, "isFixed");
            return (Criteria) this;
        }

        public Criteria andIsFixedLessThan(Boolean value) {
            addCriterion("is_fixed <", value, "isFixed");
            return (Criteria) this;
        }

        public Criteria andIsFixedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_fixed <=", value, "isFixed");
            return (Criteria) this;
        }

        public Criteria andIsFixedIn(List<Boolean> values) {
            addCriterion("is_fixed in", values, "isFixed");
            return (Criteria) this;
        }

        public Criteria andIsFixedNotIn(List<Boolean> values) {
            addCriterion("is_fixed not in", values, "isFixed");
            return (Criteria) this;
        }

        public Criteria andIsFixedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_fixed between", value1, value2, "isFixed");
            return (Criteria) this;
        }

        public Criteria andIsFixedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_fixed not between", value1, value2, "isFixed");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table log_coverage_detail
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table log_coverage_detail
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}