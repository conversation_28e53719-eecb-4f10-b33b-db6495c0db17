package com.aliyun.xdragon.common.generate.keymetric.model;

import java.util.ArrayList;
import java.util.List;

public class EcsKeyMetricRawEventVminfoExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    public EcsKeyMetricRawEventVminfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andEqualTo(String field, String value){
            addCriterion(field + " =", value, field);
            return (Criteria) this;
        }

        public Criteria andInstanceIdIsNull() {
            addCriterion("instance_id is null");
            return (Criteria) this;
        }

        public Criteria andInstanceIdIsNotNull() {
            addCriterion("instance_id is not null");
            return (Criteria) this;
        }

        public Criteria andInstanceIdEqualTo(String value) {
            addCriterion("instance_id =", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdNotEqualTo(String value) {
            addCriterion("instance_id <>", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdGreaterThan(String value) {
            addCriterion("instance_id >", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("instance_id >=", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdLessThan(String value) {
            addCriterion("instance_id <", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("instance_id <=", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdLike(String value) {
            addCriterion("instance_id like", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdNotLike(String value) {
            addCriterion("instance_id not like", value, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdIn(List<String> values) {
            addCriterion("instance_id in", values, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdNotIn(List<String> values) {
            addCriterion("instance_id not in", values, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdBetween(String value1, String value2) {
            addCriterion("instance_id between", value1, value2, "instanceId");
            return (Criteria) this;
        }

        public Criteria andInstanceIdNotBetween(String value1, String value2) {
            addCriterion("instance_id not between", value1, value2, "instanceId");
            return (Criteria) this;
        }

        public Criteria andDsIsNull() {
            addCriterion("ds is null");
            return (Criteria) this;
        }

        public Criteria andDsIsNotNull() {
            addCriterion("ds is not null");
            return (Criteria) this;
        }

        public Criteria andDsEqualTo(String value) {
            addCriterion("ds =", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsNotEqualTo(String value) {
            addCriterion("ds <>", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsGreaterThan(String value) {
            addCriterion("ds >", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsGreaterThanOrEqualTo(String value) {
            addCriterion("ds >=", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsLessThan(String value) {
            addCriterion("ds <", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsLessThanOrEqualTo(String value) {
            addCriterion("ds <=", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsLike(String value) {
            addCriterion("ds like", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsNotLike(String value) {
            addCriterion("ds not like", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsIn(List<String> values) {
            addCriterion("ds in", values, "ds");
            return (Criteria) this;
        }

        public Criteria andDsNotIn(List<String> values) {
            addCriterion("ds not in", values, "ds");
            return (Criteria) this;
        }

        public Criteria andDsBetween(String value1, String value2) {
            addCriterion("ds between", value1, value2, "ds");
            return (Criteria) this;
        }

        public Criteria andDsNotBetween(String value1, String value2) {
            addCriterion("ds not between", value1, value2, "ds");
            return (Criteria) this;
        }

        public Criteria andExceptionNameIsNull() {
            addCriterion("exception_name is null");
            return (Criteria) this;
        }

        public Criteria andExceptionNameIsNotNull() {
            addCriterion("exception_name is not null");
            return (Criteria) this;
        }

        public Criteria andExceptionNameEqualTo(String value) {
            addCriterion("exception_name =", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameNotEqualTo(String value) {
            addCriterion("exception_name <>", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameGreaterThan(String value) {
            addCriterion("exception_name >", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameGreaterThanOrEqualTo(String value) {
            addCriterion("exception_name >=", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameLessThan(String value) {
            addCriterion("exception_name <", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameLessThanOrEqualTo(String value) {
            addCriterion("exception_name <=", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameLike(String value) {
            addCriterion("exception_name like", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameNotLike(String value) {
            addCriterion("exception_name not like", value, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameIn(List<String> values) {
            addCriterion("exception_name in", values, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameNotIn(List<String> values) {
            addCriterion("exception_name not in", values, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameBetween(String value1, String value2) {
            addCriterion("exception_name between", value1, value2, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andExceptionNameNotBetween(String value1, String value2) {
            addCriterion("exception_name not between", value1, value2, "exceptionName");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(String value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(String value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(String value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(String value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(String value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(String value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLike(String value) {
            addCriterion("start_time like", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotLike(String value) {
            addCriterion("start_time not like", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<String> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<String> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(String value1, String value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(String value1, String value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(String value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(String value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(String value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(String value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(String value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(String value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLike(String value) {
            addCriterion("end_time like", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotLike(String value) {
            addCriterion("end_time not like", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<String> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<String> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(String value1, String value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(String value1, String value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andDurationIsNull() {
            addCriterion("duration is null");
            return (Criteria) this;
        }

        public Criteria andDurationIsNotNull() {
            addCriterion("duration is not null");
            return (Criteria) this;
        }

        public Criteria andDurationEqualTo(Double value) {
            addCriterion("duration =", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotEqualTo(Double value) {
            addCriterion("duration <>", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThan(Double value) {
            addCriterion("duration >", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThanOrEqualTo(Double value) {
            addCriterion("duration >=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThan(Double value) {
            addCriterion("duration <", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThanOrEqualTo(Double value) {
            addCriterion("duration <=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationIn(List<Double> values) {
            addCriterion("duration in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotIn(List<Double> values) {
            addCriterion("duration not in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationBetween(Double value1, Double value2) {
            addCriterion("duration between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotBetween(Double value1, Double value2) {
            addCriterion("duration not between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeIsNull() {
            addCriterion("monitor_type is null");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeIsNotNull() {
            addCriterion("monitor_type is not null");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeEqualTo(String value) {
            addCriterion("monitor_type =", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeNotEqualTo(String value) {
            addCriterion("monitor_type <>", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeGreaterThan(String value) {
            addCriterion("monitor_type >", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeGreaterThanOrEqualTo(String value) {
            addCriterion("monitor_type >=", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeLessThan(String value) {
            addCriterion("monitor_type <", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeLessThanOrEqualTo(String value) {
            addCriterion("monitor_type <=", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeLike(String value) {
            addCriterion("monitor_type like", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeNotLike(String value) {
            addCriterion("monitor_type not like", value, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeIn(List<String> values) {
            addCriterion("monitor_type in", values, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeNotIn(List<String> values) {
            addCriterion("monitor_type not in", values, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeBetween(String value1, String value2) {
            addCriterion("monitor_type between", value1, value2, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorTypeNotBetween(String value1, String value2) {
            addCriterion("monitor_type not between", value1, value2, "monitorType");
            return (Criteria) this;
        }

        public Criteria andMonitorLevelIsNull() {
            addCriterion("monitor_level is null");
            return (Criteria) this;
        }

        public Criteria andMonitorLevelIsNotNull() {
            addCriterion("monitor_level is not null");
            return (Criteria) this;
        }

        public Criteria andMonitorLevelEqualTo(String value) {
            addCriterion("monitor_level =", value, "monitorLevel");
            return (Criteria) this;
        }

        public Criteria andMonitorLevelNotEqualTo(String value) {
            addCriterion("monitor_level <>", value, "monitorLevel");
            return (Criteria) this;
        }

        public Criteria andMonitorLevelGreaterThan(String value) {
            addCriterion("monitor_level >", value, "monitorLevel");
            return (Criteria) this;
        }

        public Criteria andMonitorLevelGreaterThanOrEqualTo(String value) {
            addCriterion("monitor_level >=", value, "monitorLevel");
            return (Criteria) this;
        }

        public Criteria andMonitorLevelLessThan(String value) {
            addCriterion("monitor_level <", value, "monitorLevel");
            return (Criteria) this;
        }

        public Criteria andMonitorLevelLessThanOrEqualTo(String value) {
            addCriterion("monitor_level <=", value, "monitorLevel");
            return (Criteria) this;
        }

        public Criteria andMonitorLevelLike(String value) {
            addCriterion("monitor_level like", value, "monitorLevel");
            return (Criteria) this;
        }

        public Criteria andMonitorLevelNotLike(String value) {
            addCriterion("monitor_level not like", value, "monitorLevel");
            return (Criteria) this;
        }

        public Criteria andMonitorLevelIn(List<String> values) {
            addCriterion("monitor_level in", values, "monitorLevel");
            return (Criteria) this;
        }

        public Criteria andMonitorLevelNotIn(List<String> values) {
            addCriterion("monitor_level not in", values, "monitorLevel");
            return (Criteria) this;
        }

        public Criteria andMonitorLevelBetween(String value1, String value2) {
            addCriterion("monitor_level between", value1, value2, "monitorLevel");
            return (Criteria) this;
        }

        public Criteria andMonitorLevelNotBetween(String value1, String value2) {
            addCriterion("monitor_level not between", value1, value2, "monitorLevel");
            return (Criteria) this;
        }

        public Criteria andNcIpIsNull() {
            addCriterion("nc_ip is null");
            return (Criteria) this;
        }

        public Criteria andNcIpIsNotNull() {
            addCriterion("nc_ip is not null");
            return (Criteria) this;
        }

        public Criteria andNcIpEqualTo(String value) {
            addCriterion("nc_ip =", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpNotEqualTo(String value) {
            addCriterion("nc_ip <>", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpGreaterThan(String value) {
            addCriterion("nc_ip >", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpGreaterThanOrEqualTo(String value) {
            addCriterion("nc_ip >=", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpLessThan(String value) {
            addCriterion("nc_ip <", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpLessThanOrEqualTo(String value) {
            addCriterion("nc_ip <=", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpLike(String value) {
            addCriterion("nc_ip like", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpNotLike(String value) {
            addCriterion("nc_ip not like", value, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpIn(List<String> values) {
            addCriterion("nc_ip in", values, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpNotIn(List<String> values) {
            addCriterion("nc_ip not in", values, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpBetween(String value1, String value2) {
            addCriterion("nc_ip between", value1, value2, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIpNotBetween(String value1, String value2) {
            addCriterion("nc_ip not between", value1, value2, "ncIp");
            return (Criteria) this;
        }

        public Criteria andNcIdIsNull() {
            addCriterion("nc_id is null");
            return (Criteria) this;
        }

        public Criteria andNcIdIsNotNull() {
            addCriterion("nc_id is not null");
            return (Criteria) this;
        }

        public Criteria andNcIdEqualTo(String value) {
            addCriterion("nc_id =", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdNotEqualTo(String value) {
            addCriterion("nc_id <>", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdGreaterThan(String value) {
            addCriterion("nc_id >", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdGreaterThanOrEqualTo(String value) {
            addCriterion("nc_id >=", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdLessThan(String value) {
            addCriterion("nc_id <", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdLessThanOrEqualTo(String value) {
            addCriterion("nc_id <=", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdLike(String value) {
            addCriterion("nc_id like", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdNotLike(String value) {
            addCriterion("nc_id not like", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdIn(List<String> values) {
            addCriterion("nc_id in", values, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdNotIn(List<String> values) {
            addCriterion("nc_id not in", values, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdBetween(String value1, String value2) {
            addCriterion("nc_id between", value1, value2, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdNotBetween(String value1, String value2) {
            addCriterion("nc_id not between", value1, value2, "ncId");
            return (Criteria) this;
        }

        public Criteria andSnIsNull() {
            addCriterion("sn is null");
            return (Criteria) this;
        }

        public Criteria andSnIsNotNull() {
            addCriterion("sn is not null");
            return (Criteria) this;
        }

        public Criteria andSnEqualTo(String value) {
            addCriterion("sn =", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotEqualTo(String value) {
            addCriterion("sn <>", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThan(String value) {
            addCriterion("sn >", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThanOrEqualTo(String value) {
            addCriterion("sn >=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThan(String value) {
            addCriterion("sn <", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThanOrEqualTo(String value) {
            addCriterion("sn <=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLike(String value) {
            addCriterion("sn like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotLike(String value) {
            addCriterion("sn not like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnIn(List<String> values) {
            addCriterion("sn in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotIn(List<String> values) {
            addCriterion("sn not in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnBetween(String value1, String value2) {
            addCriterion("sn between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotBetween(String value1, String value2) {
            addCriterion("sn not between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andIsLocalDiskIsNull() {
            addCriterion("is_local_disk is null");
            return (Criteria) this;
        }

        public Criteria andIsLocalDiskIsNotNull() {
            addCriterion("is_local_disk is not null");
            return (Criteria) this;
        }

        public Criteria andIsLocalDiskEqualTo(String value) {
            addCriterion("is_local_disk =", value, "isLocalDisk");
            return (Criteria) this;
        }

        public Criteria andIsLocalDiskNotEqualTo(String value) {
            addCriterion("is_local_disk <>", value, "isLocalDisk");
            return (Criteria) this;
        }

        public Criteria andIsLocalDiskGreaterThan(String value) {
            addCriterion("is_local_disk >", value, "isLocalDisk");
            return (Criteria) this;
        }

        public Criteria andIsLocalDiskGreaterThanOrEqualTo(String value) {
            addCriterion("is_local_disk >=", value, "isLocalDisk");
            return (Criteria) this;
        }

        public Criteria andIsLocalDiskLessThan(String value) {
            addCriterion("is_local_disk <", value, "isLocalDisk");
            return (Criteria) this;
        }

        public Criteria andIsLocalDiskLessThanOrEqualTo(String value) {
            addCriterion("is_local_disk <=", value, "isLocalDisk");
            return (Criteria) this;
        }

        public Criteria andIsLocalDiskLike(String value) {
            addCriterion("is_local_disk like", value, "isLocalDisk");
            return (Criteria) this;
        }

        public Criteria andIsLocalDiskNotLike(String value) {
            addCriterion("is_local_disk not like", value, "isLocalDisk");
            return (Criteria) this;
        }

        public Criteria andIsLocalDiskIn(List<String> values) {
            addCriterion("is_local_disk in", values, "isLocalDisk");
            return (Criteria) this;
        }

        public Criteria andIsLocalDiskNotIn(List<String> values) {
            addCriterion("is_local_disk not in", values, "isLocalDisk");
            return (Criteria) this;
        }

        public Criteria andIsLocalDiskBetween(String value1, String value2) {
            addCriterion("is_local_disk between", value1, value2, "isLocalDisk");
            return (Criteria) this;
        }

        public Criteria andIsLocalDiskNotBetween(String value1, String value2) {
            addCriterion("is_local_disk not between", value1, value2, "isLocalDisk");
            return (Criteria) this;
        }

        public Criteria andAliUidIsNull() {
            addCriterion("ali_uid is null");
            return (Criteria) this;
        }

        public Criteria andAliUidIsNotNull() {
            addCriterion("ali_uid is not null");
            return (Criteria) this;
        }

        public Criteria andAliUidEqualTo(String value) {
            addCriterion("ali_uid =", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidNotEqualTo(String value) {
            addCriterion("ali_uid <>", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidGreaterThan(String value) {
            addCriterion("ali_uid >", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidGreaterThanOrEqualTo(String value) {
            addCriterion("ali_uid >=", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidLessThan(String value) {
            addCriterion("ali_uid <", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidLessThanOrEqualTo(String value) {
            addCriterion("ali_uid <=", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidLike(String value) {
            addCriterion("ali_uid like", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidNotLike(String value) {
            addCriterion("ali_uid not like", value, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidIn(List<String> values) {
            addCriterion("ali_uid in", values, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidNotIn(List<String> values) {
            addCriterion("ali_uid not in", values, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidBetween(String value1, String value2) {
            addCriterion("ali_uid between", value1, value2, "aliUid");
            return (Criteria) this;
        }

        public Criteria andAliUidNotBetween(String value1, String value2) {
            addCriterion("ali_uid not between", value1, value2, "aliUid");
            return (Criteria) this;
        }

        public Criteria andClusterAliasIsNull() {
            addCriterion("cluster_alias is null");
            return (Criteria) this;
        }

        public Criteria andClusterAliasIsNotNull() {
            addCriterion("cluster_alias is not null");
            return (Criteria) this;
        }

        public Criteria andClusterAliasEqualTo(String value) {
            addCriterion("cluster_alias =", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasNotEqualTo(String value) {
            addCriterion("cluster_alias <>", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasGreaterThan(String value) {
            addCriterion("cluster_alias >", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasGreaterThanOrEqualTo(String value) {
            addCriterion("cluster_alias >=", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasLessThan(String value) {
            addCriterion("cluster_alias <", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasLessThanOrEqualTo(String value) {
            addCriterion("cluster_alias <=", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasLike(String value) {
            addCriterion("cluster_alias like", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasNotLike(String value) {
            addCriterion("cluster_alias not like", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasIn(List<String> values) {
            addCriterion("cluster_alias in", values, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasNotIn(List<String> values) {
            addCriterion("cluster_alias not in", values, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasBetween(String value1, String value2) {
            addCriterion("cluster_alias between", value1, value2, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasNotBetween(String value1, String value2) {
            addCriterion("cluster_alias not between", value1, value2, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andAzNameIsNull() {
            addCriterion("az_name is null");
            return (Criteria) this;
        }

        public Criteria andAzNameIsNotNull() {
            addCriterion("az_name is not null");
            return (Criteria) this;
        }

        public Criteria andAzNameEqualTo(String value) {
            addCriterion("az_name =", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameNotEqualTo(String value) {
            addCriterion("az_name <>", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameGreaterThan(String value) {
            addCriterion("az_name >", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameGreaterThanOrEqualTo(String value) {
            addCriterion("az_name >=", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameLessThan(String value) {
            addCriterion("az_name <", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameLessThanOrEqualTo(String value) {
            addCriterion("az_name <=", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameLike(String value) {
            addCriterion("az_name like", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameNotLike(String value) {
            addCriterion("az_name not like", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameIn(List<String> values) {
            addCriterion("az_name in", values, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameNotIn(List<String> values) {
            addCriterion("az_name not in", values, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameBetween(String value1, String value2) {
            addCriterion("az_name between", value1, value2, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameNotBetween(String value1, String value2) {
            addCriterion("az_name not between", value1, value2, "azName");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyIsNull() {
            addCriterion("instance_type_family is null");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyIsNotNull() {
            addCriterion("instance_type_family is not null");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyEqualTo(String value) {
            addCriterion("instance_type_family =", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyNotEqualTo(String value) {
            addCriterion("instance_type_family <>", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyGreaterThan(String value) {
            addCriterion("instance_type_family >", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyGreaterThanOrEqualTo(String value) {
            addCriterion("instance_type_family >=", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyLessThan(String value) {
            addCriterion("instance_type_family <", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyLessThanOrEqualTo(String value) {
            addCriterion("instance_type_family <=", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyLike(String value) {
            addCriterion("instance_type_family like", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyNotLike(String value) {
            addCriterion("instance_type_family not like", value, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyIn(List<String> values) {
            addCriterion("instance_type_family in", values, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyNotIn(List<String> values) {
            addCriterion("instance_type_family not in", values, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyBetween(String value1, String value2) {
            addCriterion("instance_type_family between", value1, value2, "instanceTypeFamily");
            return (Criteria) this;
        }

        public Criteria andInstanceTypeFamilyNotBetween(String value1, String value2) {
            addCriterion("instance_type_family not between", value1, value2, "instanceTypeFamily");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table ecs_key_metric_raw_event_vminfo
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}