package com.aliyun.xdragon.common.model.param;

import java.io.Serializable;

import com.aliyun.xdragon.common.enumeration.NameFetchType;

/**
 * <AUTHOR>
 * @date 2022/05/27
 */
public class OdpsCheckParam extends DataSourceCheckParam implements Serializable {
    private final static long serialVersionUID = -8831320720094182401L;

    private String tableName;
    private String colNames;
    private String partFormat;
    private NameFetchType nameType;
    private String name;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getColNames() {
        return colNames;
    }

    public void setColNames(String colNames) {
        this.colNames = colNames;
    }

    public String getPartFormat() {
        return partFormat;
    }

    public void setPartFormat(String partFormat) {
        this.partFormat = partFormat;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public NameFetchType getNameType() {
        return nameType;
    }

    public void setNameType(NameFetchType nameType) {
        this.nameType = nameType;
    }

    @Override
    public String toString() {
        return "OdpsCheckParam{" +
            "tableName='" + tableName + '\'' +
            ", colNames='" + colNames + '\'' +
            ", partFormat='" + partFormat + '\'' +
            ", nameType=" + nameType +
            ", name='" + name + '\'' +
            '}';
    }
}
