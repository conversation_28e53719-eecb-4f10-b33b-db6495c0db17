package com.aliyun.xdragon.common.generate.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class HostStaticInfoExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    public HostStaticInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSnIsNull() {
            addCriterion("sn is null");
            return (Criteria) this;
        }

        public Criteria andSnIsNotNull() {
            addCriterion("sn is not null");
            return (Criteria) this;
        }

        public Criteria andSnEqualTo(String value) {
            addCriterion("sn =", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotEqualTo(String value) {
            addCriterion("sn <>", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThan(String value) {
            addCriterion("sn >", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThanOrEqualTo(String value) {
            addCriterion("sn >=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThan(String value) {
            addCriterion("sn <", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThanOrEqualTo(String value) {
            addCriterion("sn <=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLike(String value) {
            addCriterion("sn like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotLike(String value) {
            addCriterion("sn not like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnIn(List<String> values) {
            addCriterion("sn in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotIn(List<String> values) {
            addCriterion("sn not in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnBetween(String value1, String value2) {
            addCriterion("sn between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotBetween(String value1, String value2) {
            addCriterion("sn not between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andIpIsNull() {
            addCriterion("ip is null");
            return (Criteria) this;
        }

        public Criteria andIpIsNotNull() {
            addCriterion("ip is not null");
            return (Criteria) this;
        }

        public Criteria andIpEqualTo(String value) {
            addCriterion("ip =", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpNotEqualTo(String value) {
            addCriterion("ip <>", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpGreaterThan(String value) {
            addCriterion("ip >", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpGreaterThanOrEqualTo(String value) {
            addCriterion("ip >=", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpLessThan(String value) {
            addCriterion("ip <", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpLessThanOrEqualTo(String value) {
            addCriterion("ip <=", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpLike(String value) {
            addCriterion("ip like", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpNotLike(String value) {
            addCriterion("ip not like", value, "ip");
            return (Criteria) this;
        }

        public Criteria andIpIn(List<String> values) {
            addCriterion("ip in", values, "ip");
            return (Criteria) this;
        }

        public Criteria andIpNotIn(List<String> values) {
            addCriterion("ip not in", values, "ip");
            return (Criteria) this;
        }

        public Criteria andIpBetween(String value1, String value2) {
            addCriterion("ip between", value1, value2, "ip");
            return (Criteria) this;
        }

        public Criteria andIpNotBetween(String value1, String value2) {
            addCriterion("ip not between", value1, value2, "ip");
            return (Criteria) this;
        }

        public Criteria andTagNameIsNull() {
            addCriterion("tag_name is null");
            return (Criteria) this;
        }

        public Criteria andTagNameIsNotNull() {
            addCriterion("tag_name is not null");
            return (Criteria) this;
        }

        public Criteria andTagNameEqualTo(String value) {
            addCriterion("tag_name =", value, "tagName");
            return (Criteria) this;
        }

        public Criteria andTagNameNotEqualTo(String value) {
            addCriterion("tag_name <>", value, "tagName");
            return (Criteria) this;
        }

        public Criteria andTagNameGreaterThan(String value) {
            addCriterion("tag_name >", value, "tagName");
            return (Criteria) this;
        }

        public Criteria andTagNameGreaterThanOrEqualTo(String value) {
            addCriterion("tag_name >=", value, "tagName");
            return (Criteria) this;
        }

        public Criteria andTagNameLessThan(String value) {
            addCriterion("tag_name <", value, "tagName");
            return (Criteria) this;
        }

        public Criteria andTagNameLessThanOrEqualTo(String value) {
            addCriterion("tag_name <=", value, "tagName");
            return (Criteria) this;
        }

        public Criteria andTagNameLike(String value) {
            addCriterion("tag_name like", value, "tagName");
            return (Criteria) this;
        }

        public Criteria andTagNameNotLike(String value) {
            addCriterion("tag_name not like", value, "tagName");
            return (Criteria) this;
        }

        public Criteria andTagNameIn(List<String> values) {
            addCriterion("tag_name in", values, "tagName");
            return (Criteria) this;
        }

        public Criteria andTagNameNotIn(List<String> values) {
            addCriterion("tag_name not in", values, "tagName");
            return (Criteria) this;
        }

        public Criteria andTagNameBetween(String value1, String value2) {
            addCriterion("tag_name between", value1, value2, "tagName");
            return (Criteria) this;
        }

        public Criteria andTagNameNotBetween(String value1, String value2) {
            addCriterion("tag_name not between", value1, value2, "tagName");
            return (Criteria) this;
        }

        public Criteria andDomainGroupNameIsNull() {
            addCriterion("domain_group_name is null");
            return (Criteria) this;
        }

        public Criteria andDomainGroupNameIsNotNull() {
            addCriterion("domain_group_name is not null");
            return (Criteria) this;
        }

        public Criteria andDomainGroupNameEqualTo(String value) {
            addCriterion("domain_group_name =", value, "domainGroupName");
            return (Criteria) this;
        }

        public Criteria andDomainGroupNameNotEqualTo(String value) {
            addCriterion("domain_group_name <>", value, "domainGroupName");
            return (Criteria) this;
        }

        public Criteria andDomainGroupNameGreaterThan(String value) {
            addCriterion("domain_group_name >", value, "domainGroupName");
            return (Criteria) this;
        }

        public Criteria andDomainGroupNameGreaterThanOrEqualTo(String value) {
            addCriterion("domain_group_name >=", value, "domainGroupName");
            return (Criteria) this;
        }

        public Criteria andDomainGroupNameLessThan(String value) {
            addCriterion("domain_group_name <", value, "domainGroupName");
            return (Criteria) this;
        }

        public Criteria andDomainGroupNameLessThanOrEqualTo(String value) {
            addCriterion("domain_group_name <=", value, "domainGroupName");
            return (Criteria) this;
        }

        public Criteria andDomainGroupNameLike(String value) {
            addCriterion("domain_group_name like", value, "domainGroupName");
            return (Criteria) this;
        }

        public Criteria andDomainGroupNameNotLike(String value) {
            addCriterion("domain_group_name not like", value, "domainGroupName");
            return (Criteria) this;
        }

        public Criteria andDomainGroupNameIn(List<String> values) {
            addCriterion("domain_group_name in", values, "domainGroupName");
            return (Criteria) this;
        }

        public Criteria andDomainGroupNameNotIn(List<String> values) {
            addCriterion("domain_group_name not in", values, "domainGroupName");
            return (Criteria) this;
        }

        public Criteria andDomainGroupNameBetween(String value1, String value2) {
            addCriterion("domain_group_name between", value1, value2, "domainGroupName");
            return (Criteria) this;
        }

        public Criteria andDomainGroupNameNotBetween(String value1, String value2) {
            addCriterion("domain_group_name not between", value1, value2, "domainGroupName");
            return (Criteria) this;
        }

        public Criteria andDomainNameIsNull() {
            addCriterion("domain_name is null");
            return (Criteria) this;
        }

        public Criteria andDomainNameIsNotNull() {
            addCriterion("domain_name is not null");
            return (Criteria) this;
        }

        public Criteria andDomainNameEqualTo(String value) {
            addCriterion("domain_name =", value, "domainName");
            return (Criteria) this;
        }

        public Criteria andDomainNameNotEqualTo(String value) {
            addCriterion("domain_name <>", value, "domainName");
            return (Criteria) this;
        }

        public Criteria andDomainNameGreaterThan(String value) {
            addCriterion("domain_name >", value, "domainName");
            return (Criteria) this;
        }

        public Criteria andDomainNameGreaterThanOrEqualTo(String value) {
            addCriterion("domain_name >=", value, "domainName");
            return (Criteria) this;
        }

        public Criteria andDomainNameLessThan(String value) {
            addCriterion("domain_name <", value, "domainName");
            return (Criteria) this;
        }

        public Criteria andDomainNameLessThanOrEqualTo(String value) {
            addCriterion("domain_name <=", value, "domainName");
            return (Criteria) this;
        }

        public Criteria andDomainNameLike(String value) {
            addCriterion("domain_name like", value, "domainName");
            return (Criteria) this;
        }

        public Criteria andDomainNameNotLike(String value) {
            addCriterion("domain_name not like", value, "domainName");
            return (Criteria) this;
        }

        public Criteria andDomainNameIn(List<String> values) {
            addCriterion("domain_name in", values, "domainName");
            return (Criteria) this;
        }

        public Criteria andDomainNameNotIn(List<String> values) {
            addCriterion("domain_name not in", values, "domainName");
            return (Criteria) this;
        }

        public Criteria andDomainNameBetween(String value1, String value2) {
            addCriterion("domain_name between", value1, value2, "domainName");
            return (Criteria) this;
        }

        public Criteria andDomainNameNotBetween(String value1, String value2) {
            addCriterion("domain_name not between", value1, value2, "domainName");
            return (Criteria) this;
        }

        public Criteria andRegionNameIsNull() {
            addCriterion("region_name is null");
            return (Criteria) this;
        }

        public Criteria andRegionNameIsNotNull() {
            addCriterion("region_name is not null");
            return (Criteria) this;
        }

        public Criteria andRegionNameEqualTo(String value) {
            addCriterion("region_name =", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotEqualTo(String value) {
            addCriterion("region_name <>", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThan(String value) {
            addCriterion("region_name >", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThanOrEqualTo(String value) {
            addCriterion("region_name >=", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThan(String value) {
            addCriterion("region_name <", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThanOrEqualTo(String value) {
            addCriterion("region_name <=", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameLike(String value) {
            addCriterion("region_name like", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotLike(String value) {
            addCriterion("region_name not like", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameIn(List<String> values) {
            addCriterion("region_name in", values, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotIn(List<String> values) {
            addCriterion("region_name not in", values, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameBetween(String value1, String value2) {
            addCriterion("region_name between", value1, value2, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotBetween(String value1, String value2) {
            addCriterion("region_name not between", value1, value2, "regionName");
            return (Criteria) this;
        }

        public Criteria andAzNameIsNull() {
            addCriterion("az_name is null");
            return (Criteria) this;
        }

        public Criteria andAzNameIsNotNull() {
            addCriterion("az_name is not null");
            return (Criteria) this;
        }

        public Criteria andAzNameEqualTo(String value) {
            addCriterion("az_name =", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameNotEqualTo(String value) {
            addCriterion("az_name <>", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameGreaterThan(String value) {
            addCriterion("az_name >", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameGreaterThanOrEqualTo(String value) {
            addCriterion("az_name >=", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameLessThan(String value) {
            addCriterion("az_name <", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameLessThanOrEqualTo(String value) {
            addCriterion("az_name <=", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameLike(String value) {
            addCriterion("az_name like", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameNotLike(String value) {
            addCriterion("az_name not like", value, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameIn(List<String> values) {
            addCriterion("az_name in", values, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameNotIn(List<String> values) {
            addCriterion("az_name not in", values, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameBetween(String value1, String value2) {
            addCriterion("az_name between", value1, value2, "azName");
            return (Criteria) this;
        }

        public Criteria andAzNameNotBetween(String value1, String value2) {
            addCriterion("az_name not between", value1, value2, "azName");
            return (Criteria) this;
        }

        public Criteria andClusterNameIsNull() {
            addCriterion("cluster_name is null");
            return (Criteria) this;
        }

        public Criteria andClusterNameIsNotNull() {
            addCriterion("cluster_name is not null");
            return (Criteria) this;
        }

        public Criteria andClusterNameEqualTo(String value) {
            addCriterion("cluster_name =", value, "clusterName");
            return (Criteria) this;
        }

        public Criteria andClusterNameNotEqualTo(String value) {
            addCriterion("cluster_name <>", value, "clusterName");
            return (Criteria) this;
        }

        public Criteria andClusterNameGreaterThan(String value) {
            addCriterion("cluster_name >", value, "clusterName");
            return (Criteria) this;
        }

        public Criteria andClusterNameGreaterThanOrEqualTo(String value) {
            addCriterion("cluster_name >=", value, "clusterName");
            return (Criteria) this;
        }

        public Criteria andClusterNameLessThan(String value) {
            addCriterion("cluster_name <", value, "clusterName");
            return (Criteria) this;
        }

        public Criteria andClusterNameLessThanOrEqualTo(String value) {
            addCriterion("cluster_name <=", value, "clusterName");
            return (Criteria) this;
        }

        public Criteria andClusterNameLike(String value) {
            addCriterion("cluster_name like", value, "clusterName");
            return (Criteria) this;
        }

        public Criteria andClusterNameNotLike(String value) {
            addCriterion("cluster_name not like", value, "clusterName");
            return (Criteria) this;
        }

        public Criteria andClusterNameIn(List<String> values) {
            addCriterion("cluster_name in", values, "clusterName");
            return (Criteria) this;
        }

        public Criteria andClusterNameNotIn(List<String> values) {
            addCriterion("cluster_name not in", values, "clusterName");
            return (Criteria) this;
        }

        public Criteria andClusterNameBetween(String value1, String value2) {
            addCriterion("cluster_name between", value1, value2, "clusterName");
            return (Criteria) this;
        }

        public Criteria andClusterNameNotBetween(String value1, String value2) {
            addCriterion("cluster_name not between", value1, value2, "clusterName");
            return (Criteria) this;
        }

        public Criteria andClusterAliasIsNull() {
            addCriterion("cluster_alias is null");
            return (Criteria) this;
        }

        public Criteria andClusterAliasIsNotNull() {
            addCriterion("cluster_alias is not null");
            return (Criteria) this;
        }

        public Criteria andClusterAliasEqualTo(String value) {
            addCriterion("cluster_alias =", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasNotEqualTo(String value) {
            addCriterion("cluster_alias <>", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasGreaterThan(String value) {
            addCriterion("cluster_alias >", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasGreaterThanOrEqualTo(String value) {
            addCriterion("cluster_alias >=", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasLessThan(String value) {
            addCriterion("cluster_alias <", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasLessThanOrEqualTo(String value) {
            addCriterion("cluster_alias <=", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasLike(String value) {
            addCriterion("cluster_alias like", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasNotLike(String value) {
            addCriterion("cluster_alias not like", value, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasIn(List<String> values) {
            addCriterion("cluster_alias in", values, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasNotIn(List<String> values) {
            addCriterion("cluster_alias not in", values, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasBetween(String value1, String value2) {
            addCriterion("cluster_alias between", value1, value2, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andClusterAliasNotBetween(String value1, String value2) {
            addCriterion("cluster_alias not between", value1, value2, "clusterAlias");
            return (Criteria) this;
        }

        public Criteria andZoneNoIsNull() {
            addCriterion("zone_no is null");
            return (Criteria) this;
        }

        public Criteria andZoneNoIsNotNull() {
            addCriterion("zone_no is not null");
            return (Criteria) this;
        }

        public Criteria andZoneNoEqualTo(String value) {
            addCriterion("zone_no =", value, "zoneNo");
            return (Criteria) this;
        }

        public Criteria andZoneNoNotEqualTo(String value) {
            addCriterion("zone_no <>", value, "zoneNo");
            return (Criteria) this;
        }

        public Criteria andZoneNoGreaterThan(String value) {
            addCriterion("zone_no >", value, "zoneNo");
            return (Criteria) this;
        }

        public Criteria andZoneNoGreaterThanOrEqualTo(String value) {
            addCriterion("zone_no >=", value, "zoneNo");
            return (Criteria) this;
        }

        public Criteria andZoneNoLessThan(String value) {
            addCriterion("zone_no <", value, "zoneNo");
            return (Criteria) this;
        }

        public Criteria andZoneNoLessThanOrEqualTo(String value) {
            addCriterion("zone_no <=", value, "zoneNo");
            return (Criteria) this;
        }

        public Criteria andZoneNoLike(String value) {
            addCriterion("zone_no like", value, "zoneNo");
            return (Criteria) this;
        }

        public Criteria andZoneNoNotLike(String value) {
            addCriterion("zone_no not like", value, "zoneNo");
            return (Criteria) this;
        }

        public Criteria andZoneNoIn(List<String> values) {
            addCriterion("zone_no in", values, "zoneNo");
            return (Criteria) this;
        }

        public Criteria andZoneNoNotIn(List<String> values) {
            addCriterion("zone_no not in", values, "zoneNo");
            return (Criteria) this;
        }

        public Criteria andZoneNoBetween(String value1, String value2) {
            addCriterion("zone_no between", value1, value2, "zoneNo");
            return (Criteria) this;
        }

        public Criteria andZoneNoNotBetween(String value1, String value2) {
            addCriterion("zone_no not between", value1, value2, "zoneNo");
            return (Criteria) this;
        }

        public Criteria andCpuCoreCountIsNull() {
            addCriterion("cpu_core_count is null");
            return (Criteria) this;
        }

        public Criteria andCpuCoreCountIsNotNull() {
            addCriterion("cpu_core_count is not null");
            return (Criteria) this;
        }

        public Criteria andCpuCoreCountEqualTo(Integer value) {
            addCriterion("cpu_core_count =", value, "cpuCoreCount");
            return (Criteria) this;
        }

        public Criteria andCpuCoreCountNotEqualTo(Integer value) {
            addCriterion("cpu_core_count <>", value, "cpuCoreCount");
            return (Criteria) this;
        }

        public Criteria andCpuCoreCountGreaterThan(Integer value) {
            addCriterion("cpu_core_count >", value, "cpuCoreCount");
            return (Criteria) this;
        }

        public Criteria andCpuCoreCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("cpu_core_count >=", value, "cpuCoreCount");
            return (Criteria) this;
        }

        public Criteria andCpuCoreCountLessThan(Integer value) {
            addCriterion("cpu_core_count <", value, "cpuCoreCount");
            return (Criteria) this;
        }

        public Criteria andCpuCoreCountLessThanOrEqualTo(Integer value) {
            addCriterion("cpu_core_count <=", value, "cpuCoreCount");
            return (Criteria) this;
        }

        public Criteria andCpuCoreCountIn(List<Integer> values) {
            addCriterion("cpu_core_count in", values, "cpuCoreCount");
            return (Criteria) this;
        }

        public Criteria andCpuCoreCountNotIn(List<Integer> values) {
            addCriterion("cpu_core_count not in", values, "cpuCoreCount");
            return (Criteria) this;
        }

        public Criteria andCpuCoreCountBetween(Integer value1, Integer value2) {
            addCriterion("cpu_core_count between", value1, value2, "cpuCoreCount");
            return (Criteria) this;
        }

        public Criteria andCpuCoreCountNotBetween(Integer value1, Integer value2) {
            addCriterion("cpu_core_count not between", value1, value2, "cpuCoreCount");
            return (Criteria) this;
        }

        public Criteria andMemorySizeIsNull() {
            addCriterion("memory_size is null");
            return (Criteria) this;
        }

        public Criteria andMemorySizeIsNotNull() {
            addCriterion("memory_size is not null");
            return (Criteria) this;
        }

        public Criteria andMemorySizeEqualTo(Integer value) {
            addCriterion("memory_size =", value, "memorySize");
            return (Criteria) this;
        }

        public Criteria andMemorySizeNotEqualTo(Integer value) {
            addCriterion("memory_size <>", value, "memorySize");
            return (Criteria) this;
        }

        public Criteria andMemorySizeGreaterThan(Integer value) {
            addCriterion("memory_size >", value, "memorySize");
            return (Criteria) this;
        }

        public Criteria andMemorySizeGreaterThanOrEqualTo(Integer value) {
            addCriterion("memory_size >=", value, "memorySize");
            return (Criteria) this;
        }

        public Criteria andMemorySizeLessThan(Integer value) {
            addCriterion("memory_size <", value, "memorySize");
            return (Criteria) this;
        }

        public Criteria andMemorySizeLessThanOrEqualTo(Integer value) {
            addCriterion("memory_size <=", value, "memorySize");
            return (Criteria) this;
        }

        public Criteria andMemorySizeIn(List<Integer> values) {
            addCriterion("memory_size in", values, "memorySize");
            return (Criteria) this;
        }

        public Criteria andMemorySizeNotIn(List<Integer> values) {
            addCriterion("memory_size not in", values, "memorySize");
            return (Criteria) this;
        }

        public Criteria andMemorySizeBetween(Integer value1, Integer value2) {
            addCriterion("memory_size between", value1, value2, "memorySize");
            return (Criteria) this;
        }

        public Criteria andMemorySizeNotBetween(Integer value1, Integer value2) {
            addCriterion("memory_size not between", value1, value2, "memorySize");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelIsNull() {
            addCriterion("physical_model is null");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelIsNotNull() {
            addCriterion("physical_model is not null");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelEqualTo(String value) {
            addCriterion("physical_model =", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelNotEqualTo(String value) {
            addCriterion("physical_model <>", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelGreaterThan(String value) {
            addCriterion("physical_model >", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelGreaterThanOrEqualTo(String value) {
            addCriterion("physical_model >=", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelLessThan(String value) {
            addCriterion("physical_model <", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelLessThanOrEqualTo(String value) {
            addCriterion("physical_model <=", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelLike(String value) {
            addCriterion("physical_model like", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelNotLike(String value) {
            addCriterion("physical_model not like", value, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelIn(List<String> values) {
            addCriterion("physical_model in", values, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelNotIn(List<String> values) {
            addCriterion("physical_model not in", values, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelBetween(String value1, String value2) {
            addCriterion("physical_model between", value1, value2, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andPhysicalModelNotBetween(String value1, String value2) {
            addCriterion("physical_model not between", value1, value2, "physicalModel");
            return (Criteria) this;
        }

        public Criteria andVirtTypeIsNull() {
            addCriterion("virt_type is null");
            return (Criteria) this;
        }

        public Criteria andVirtTypeIsNotNull() {
            addCriterion("virt_type is not null");
            return (Criteria) this;
        }

        public Criteria andVirtTypeEqualTo(String value) {
            addCriterion("virt_type =", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeNotEqualTo(String value) {
            addCriterion("virt_type <>", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeGreaterThan(String value) {
            addCriterion("virt_type >", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeGreaterThanOrEqualTo(String value) {
            addCriterion("virt_type >=", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeLessThan(String value) {
            addCriterion("virt_type <", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeLessThanOrEqualTo(String value) {
            addCriterion("virt_type <=", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeLike(String value) {
            addCriterion("virt_type like", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeNotLike(String value) {
            addCriterion("virt_type not like", value, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeIn(List<String> values) {
            addCriterion("virt_type in", values, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeNotIn(List<String> values) {
            addCriterion("virt_type not in", values, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeBetween(String value1, String value2) {
            addCriterion("virt_type between", value1, value2, "virtType");
            return (Criteria) this;
        }

        public Criteria andVirtTypeNotBetween(String value1, String value2) {
            addCriterion("virt_type not between", value1, value2, "virtType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeIsNull() {
            addCriterion("river_type is null");
            return (Criteria) this;
        }

        public Criteria andRiverTypeIsNotNull() {
            addCriterion("river_type is not null");
            return (Criteria) this;
        }

        public Criteria andRiverTypeEqualTo(String value) {
            addCriterion("river_type =", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeNotEqualTo(String value) {
            addCriterion("river_type <>", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeGreaterThan(String value) {
            addCriterion("river_type >", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeGreaterThanOrEqualTo(String value) {
            addCriterion("river_type >=", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeLessThan(String value) {
            addCriterion("river_type <", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeLessThanOrEqualTo(String value) {
            addCriterion("river_type <=", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeLike(String value) {
            addCriterion("river_type like", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeNotLike(String value) {
            addCriterion("river_type not like", value, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeIn(List<String> values) {
            addCriterion("river_type in", values, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeNotIn(List<String> values) {
            addCriterion("river_type not in", values, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeBetween(String value1, String value2) {
            addCriterion("river_type between", value1, value2, "riverType");
            return (Criteria) this;
        }

        public Criteria andRiverTypeNotBetween(String value1, String value2) {
            addCriterion("river_type not between", value1, value2, "riverType");
            return (Criteria) this;
        }

        public Criteria andClusterUsageIsNull() {
            addCriterion("cluster_usage is null");
            return (Criteria) this;
        }

        public Criteria andClusterUsageIsNotNull() {
            addCriterion("cluster_usage is not null");
            return (Criteria) this;
        }

        public Criteria andClusterUsageEqualTo(String value) {
            addCriterion("cluster_usage =", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageNotEqualTo(String value) {
            addCriterion("cluster_usage <>", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageGreaterThan(String value) {
            addCriterion("cluster_usage >", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageGreaterThanOrEqualTo(String value) {
            addCriterion("cluster_usage >=", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageLessThan(String value) {
            addCriterion("cluster_usage <", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageLessThanOrEqualTo(String value) {
            addCriterion("cluster_usage <=", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageLike(String value) {
            addCriterion("cluster_usage like", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageNotLike(String value) {
            addCriterion("cluster_usage not like", value, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageIn(List<String> values) {
            addCriterion("cluster_usage in", values, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageNotIn(List<String> values) {
            addCriterion("cluster_usage not in", values, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageBetween(String value1, String value2) {
            addCriterion("cluster_usage between", value1, value2, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andClusterUsageNotBetween(String value1, String value2) {
            addCriterion("cluster_usage not between", value1, value2, "clusterUsage");
            return (Criteria) this;
        }

        public Criteria andNcIdIsNull() {
            addCriterion("nc_id is null");
            return (Criteria) this;
        }

        public Criteria andNcIdIsNotNull() {
            addCriterion("nc_id is not null");
            return (Criteria) this;
        }

        public Criteria andNcIdEqualTo(String value) {
            addCriterion("nc_id =", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdNotEqualTo(String value) {
            addCriterion("nc_id <>", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdGreaterThan(String value) {
            addCriterion("nc_id >", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdGreaterThanOrEqualTo(String value) {
            addCriterion("nc_id >=", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdLessThan(String value) {
            addCriterion("nc_id <", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdLessThanOrEqualTo(String value) {
            addCriterion("nc_id <=", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdLike(String value) {
            addCriterion("nc_id like", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdNotLike(String value) {
            addCriterion("nc_id not like", value, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdIn(List<String> values) {
            addCriterion("nc_id in", values, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdNotIn(List<String> values) {
            addCriterion("nc_id not in", values, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdBetween(String value1, String value2) {
            addCriterion("nc_id between", value1, value2, "ncId");
            return (Criteria) this;
        }

        public Criteria andNcIdNotBetween(String value1, String value2) {
            addCriterion("nc_id not between", value1, value2, "ncId");
            return (Criteria) this;
        }

        public Criteria andVcpuModIsNull() {
            addCriterion("vcpu_mod is null");
            return (Criteria) this;
        }

        public Criteria andVcpuModIsNotNull() {
            addCriterion("vcpu_mod is not null");
            return (Criteria) this;
        }

        public Criteria andVcpuModEqualTo(String value) {
            addCriterion("vcpu_mod =", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModNotEqualTo(String value) {
            addCriterion("vcpu_mod <>", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModGreaterThan(String value) {
            addCriterion("vcpu_mod >", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModGreaterThanOrEqualTo(String value) {
            addCriterion("vcpu_mod >=", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModLessThan(String value) {
            addCriterion("vcpu_mod <", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModLessThanOrEqualTo(String value) {
            addCriterion("vcpu_mod <=", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModLike(String value) {
            addCriterion("vcpu_mod like", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModNotLike(String value) {
            addCriterion("vcpu_mod not like", value, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModIn(List<String> values) {
            addCriterion("vcpu_mod in", values, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModNotIn(List<String> values) {
            addCriterion("vcpu_mod not in", values, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModBetween(String value1, String value2) {
            addCriterion("vcpu_mod between", value1, value2, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andVcpuModNotBetween(String value1, String value2) {
            addCriterion("vcpu_mod not between", value1, value2, "vcpuMod");
            return (Criteria) this;
        }

        public Criteria andSupportedFamilyIsNull() {
            addCriterion("supported_family is null");
            return (Criteria) this;
        }

        public Criteria andSupportedFamilyIsNotNull() {
            addCriterion("supported_family is not null");
            return (Criteria) this;
        }

        public Criteria andSupportedFamilyEqualTo(String value) {
            addCriterion("supported_family =", value, "supportedFamily");
            return (Criteria) this;
        }

        public Criteria andSupportedFamilyNotEqualTo(String value) {
            addCriterion("supported_family <>", value, "supportedFamily");
            return (Criteria) this;
        }

        public Criteria andSupportedFamilyGreaterThan(String value) {
            addCriterion("supported_family >", value, "supportedFamily");
            return (Criteria) this;
        }

        public Criteria andSupportedFamilyGreaterThanOrEqualTo(String value) {
            addCriterion("supported_family >=", value, "supportedFamily");
            return (Criteria) this;
        }

        public Criteria andSupportedFamilyLessThan(String value) {
            addCriterion("supported_family <", value, "supportedFamily");
            return (Criteria) this;
        }

        public Criteria andSupportedFamilyLessThanOrEqualTo(String value) {
            addCriterion("supported_family <=", value, "supportedFamily");
            return (Criteria) this;
        }

        public Criteria andSupportedFamilyLike(String value) {
            addCriterion("supported_family like", value, "supportedFamily");
            return (Criteria) this;
        }

        public Criteria andSupportedFamilyNotLike(String value) {
            addCriterion("supported_family not like", value, "supportedFamily");
            return (Criteria) this;
        }

        public Criteria andSupportedFamilyIn(List<String> values) {
            addCriterion("supported_family in", values, "supportedFamily");
            return (Criteria) this;
        }

        public Criteria andSupportedFamilyNotIn(List<String> values) {
            addCriterion("supported_family not in", values, "supportedFamily");
            return (Criteria) this;
        }

        public Criteria andSupportedFamilyBetween(String value1, String value2) {
            addCriterion("supported_family between", value1, value2, "supportedFamily");
            return (Criteria) this;
        }

        public Criteria andSupportedFamilyNotBetween(String value1, String value2) {
            addCriterion("supported_family not between", value1, value2, "supportedFamily");
            return (Criteria) this;
        }

        public Criteria andFamilySummaryIsNull() {
            addCriterion("family_summary is null");
            return (Criteria) this;
        }

        public Criteria andFamilySummaryIsNotNull() {
            addCriterion("family_summary is not null");
            return (Criteria) this;
        }

        public Criteria andFamilySummaryEqualTo(String value) {
            addCriterion("family_summary =", value, "familySummary");
            return (Criteria) this;
        }

        public Criteria andFamilySummaryNotEqualTo(String value) {
            addCriterion("family_summary <>", value, "familySummary");
            return (Criteria) this;
        }

        public Criteria andFamilySummaryGreaterThan(String value) {
            addCriterion("family_summary >", value, "familySummary");
            return (Criteria) this;
        }

        public Criteria andFamilySummaryGreaterThanOrEqualTo(String value) {
            addCriterion("family_summary >=", value, "familySummary");
            return (Criteria) this;
        }

        public Criteria andFamilySummaryLessThan(String value) {
            addCriterion("family_summary <", value, "familySummary");
            return (Criteria) this;
        }

        public Criteria andFamilySummaryLessThanOrEqualTo(String value) {
            addCriterion("family_summary <=", value, "familySummary");
            return (Criteria) this;
        }

        public Criteria andFamilySummaryLike(String value) {
            addCriterion("family_summary like", value, "familySummary");
            return (Criteria) this;
        }

        public Criteria andFamilySummaryNotLike(String value) {
            addCriterion("family_summary not like", value, "familySummary");
            return (Criteria) this;
        }

        public Criteria andFamilySummaryIn(List<String> values) {
            addCriterion("family_summary in", values, "familySummary");
            return (Criteria) this;
        }

        public Criteria andFamilySummaryNotIn(List<String> values) {
            addCriterion("family_summary not in", values, "familySummary");
            return (Criteria) this;
        }

        public Criteria andFamilySummaryBetween(String value1, String value2) {
            addCriterion("family_summary between", value1, value2, "familySummary");
            return (Criteria) this;
        }

        public Criteria andFamilySummaryNotBetween(String value1, String value2) {
            addCriterion("family_summary not between", value1, value2, "familySummary");
            return (Criteria) this;
        }

        public Criteria andIdcIsNull() {
            addCriterion("idc is null");
            return (Criteria) this;
        }

        public Criteria andIdcIsNotNull() {
            addCriterion("idc is not null");
            return (Criteria) this;
        }

        public Criteria andIdcEqualTo(String value) {
            addCriterion("idc =", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcNotEqualTo(String value) {
            addCriterion("idc <>", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcGreaterThan(String value) {
            addCriterion("idc >", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcGreaterThanOrEqualTo(String value) {
            addCriterion("idc >=", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcLessThan(String value) {
            addCriterion("idc <", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcLessThanOrEqualTo(String value) {
            addCriterion("idc <=", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcLike(String value) {
            addCriterion("idc like", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcNotLike(String value) {
            addCriterion("idc not like", value, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcIn(List<String> values) {
            addCriterion("idc in", values, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcNotIn(List<String> values) {
            addCriterion("idc not in", values, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcBetween(String value1, String value2) {
            addCriterion("idc between", value1, value2, "idc");
            return (Criteria) this;
        }

        public Criteria andIdcNotBetween(String value1, String value2) {
            addCriterion("idc not between", value1, value2, "idc");
            return (Criteria) this;
        }

        public Criteria andRoomIsNull() {
            addCriterion("room is null");
            return (Criteria) this;
        }

        public Criteria andRoomIsNotNull() {
            addCriterion("room is not null");
            return (Criteria) this;
        }

        public Criteria andRoomEqualTo(String value) {
            addCriterion("room =", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomNotEqualTo(String value) {
            addCriterion("room <>", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomGreaterThan(String value) {
            addCriterion("room >", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomGreaterThanOrEqualTo(String value) {
            addCriterion("room >=", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomLessThan(String value) {
            addCriterion("room <", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomLessThanOrEqualTo(String value) {
            addCriterion("room <=", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomLike(String value) {
            addCriterion("room like", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomNotLike(String value) {
            addCriterion("room not like", value, "room");
            return (Criteria) this;
        }

        public Criteria andRoomIn(List<String> values) {
            addCriterion("room in", values, "room");
            return (Criteria) this;
        }

        public Criteria andRoomNotIn(List<String> values) {
            addCriterion("room not in", values, "room");
            return (Criteria) this;
        }

        public Criteria andRoomBetween(String value1, String value2) {
            addCriterion("room between", value1, value2, "room");
            return (Criteria) this;
        }

        public Criteria andRoomNotBetween(String value1, String value2) {
            addCriterion("room not between", value1, value2, "room");
            return (Criteria) this;
        }

        public Criteria andRackIsNull() {
            addCriterion("rack is null");
            return (Criteria) this;
        }

        public Criteria andRackIsNotNull() {
            addCriterion("rack is not null");
            return (Criteria) this;
        }

        public Criteria andRackEqualTo(String value) {
            addCriterion("rack =", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackNotEqualTo(String value) {
            addCriterion("rack <>", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackGreaterThan(String value) {
            addCriterion("rack >", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackGreaterThanOrEqualTo(String value) {
            addCriterion("rack >=", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackLessThan(String value) {
            addCriterion("rack <", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackLessThanOrEqualTo(String value) {
            addCriterion("rack <=", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackLike(String value) {
            addCriterion("rack like", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackNotLike(String value) {
            addCriterion("rack not like", value, "rack");
            return (Criteria) this;
        }

        public Criteria andRackIn(List<String> values) {
            addCriterion("rack in", values, "rack");
            return (Criteria) this;
        }

        public Criteria andRackNotIn(List<String> values) {
            addCriterion("rack not in", values, "rack");
            return (Criteria) this;
        }

        public Criteria andRackBetween(String value1, String value2) {
            addCriterion("rack between", value1, value2, "rack");
            return (Criteria) this;
        }

        public Criteria andRackNotBetween(String value1, String value2) {
            addCriterion("rack not between", value1, value2, "rack");
            return (Criteria) this;
        }

        public Criteria andHostNameIsNull() {
            addCriterion("host_name is null");
            return (Criteria) this;
        }

        public Criteria andHostNameIsNotNull() {
            addCriterion("host_name is not null");
            return (Criteria) this;
        }

        public Criteria andHostNameEqualTo(String value) {
            addCriterion("host_name =", value, "hostName");
            return (Criteria) this;
        }

        public Criteria andHostNameNotEqualTo(String value) {
            addCriterion("host_name <>", value, "hostName");
            return (Criteria) this;
        }

        public Criteria andHostNameGreaterThan(String value) {
            addCriterion("host_name >", value, "hostName");
            return (Criteria) this;
        }

        public Criteria andHostNameGreaterThanOrEqualTo(String value) {
            addCriterion("host_name >=", value, "hostName");
            return (Criteria) this;
        }

        public Criteria andHostNameLessThan(String value) {
            addCriterion("host_name <", value, "hostName");
            return (Criteria) this;
        }

        public Criteria andHostNameLessThanOrEqualTo(String value) {
            addCriterion("host_name <=", value, "hostName");
            return (Criteria) this;
        }

        public Criteria andHostNameLike(String value) {
            addCriterion("host_name like", value, "hostName");
            return (Criteria) this;
        }

        public Criteria andHostNameNotLike(String value) {
            addCriterion("host_name not like", value, "hostName");
            return (Criteria) this;
        }

        public Criteria andHostNameIn(List<String> values) {
            addCriterion("host_name in", values, "hostName");
            return (Criteria) this;
        }

        public Criteria andHostNameNotIn(List<String> values) {
            addCriterion("host_name not in", values, "hostName");
            return (Criteria) this;
        }

        public Criteria andHostNameBetween(String value1, String value2) {
            addCriterion("host_name between", value1, value2, "hostName");
            return (Criteria) this;
        }

        public Criteria andHostNameNotBetween(String value1, String value2) {
            addCriterion("host_name not between", value1, value2, "hostName");
            return (Criteria) this;
        }

        public Criteria andStateIsNull() {
            addCriterion("`state` is null");
            return (Criteria) this;
        }

        public Criteria andStateIsNotNull() {
            addCriterion("`state` is not null");
            return (Criteria) this;
        }

        public Criteria andStateEqualTo(String value) {
            addCriterion("`state` =", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotEqualTo(String value) {
            addCriterion("`state` <>", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThan(String value) {
            addCriterion("`state` >", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThanOrEqualTo(String value) {
            addCriterion("`state` >=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThan(String value) {
            addCriterion("`state` <", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThanOrEqualTo(String value) {
            addCriterion("`state` <=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLike(String value) {
            addCriterion("`state` like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotLike(String value) {
            addCriterion("`state` not like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateIn(List<String> values) {
            addCriterion("`state` in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotIn(List<String> values) {
            addCriterion("`state` not in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateBetween(String value1, String value2) {
            addCriterion("`state` between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotBetween(String value1, String value2) {
            addCriterion("`state` not between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andAppGroupIsNull() {
            addCriterion("app_group is null");
            return (Criteria) this;
        }

        public Criteria andAppGroupIsNotNull() {
            addCriterion("app_group is not null");
            return (Criteria) this;
        }

        public Criteria andAppGroupEqualTo(String value) {
            addCriterion("app_group =", value, "appGroup");
            return (Criteria) this;
        }

        public Criteria andAppGroupNotEqualTo(String value) {
            addCriterion("app_group <>", value, "appGroup");
            return (Criteria) this;
        }

        public Criteria andAppGroupGreaterThan(String value) {
            addCriterion("app_group >", value, "appGroup");
            return (Criteria) this;
        }

        public Criteria andAppGroupGreaterThanOrEqualTo(String value) {
            addCriterion("app_group >=", value, "appGroup");
            return (Criteria) this;
        }

        public Criteria andAppGroupLessThan(String value) {
            addCriterion("app_group <", value, "appGroup");
            return (Criteria) this;
        }

        public Criteria andAppGroupLessThanOrEqualTo(String value) {
            addCriterion("app_group <=", value, "appGroup");
            return (Criteria) this;
        }

        public Criteria andAppGroupLike(String value) {
            addCriterion("app_group like", value, "appGroup");
            return (Criteria) this;
        }

        public Criteria andAppGroupNotLike(String value) {
            addCriterion("app_group not like", value, "appGroup");
            return (Criteria) this;
        }

        public Criteria andAppGroupIn(List<String> values) {
            addCriterion("app_group in", values, "appGroup");
            return (Criteria) this;
        }

        public Criteria andAppGroupNotIn(List<String> values) {
            addCriterion("app_group not in", values, "appGroup");
            return (Criteria) this;
        }

        public Criteria andAppGroupBetween(String value1, String value2) {
            addCriterion("app_group between", value1, value2, "appGroup");
            return (Criteria) this;
        }

        public Criteria andAppGroupNotBetween(String value1, String value2) {
            addCriterion("app_group not between", value1, value2, "appGroup");
            return (Criteria) this;
        }

        public Criteria andStorageTypeIsNull() {
            addCriterion("storage_type is null");
            return (Criteria) this;
        }

        public Criteria andStorageTypeIsNotNull() {
            addCriterion("storage_type is not null");
            return (Criteria) this;
        }

        public Criteria andStorageTypeEqualTo(String value) {
            addCriterion("storage_type =", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeNotEqualTo(String value) {
            addCriterion("storage_type <>", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeGreaterThan(String value) {
            addCriterion("storage_type >", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeGreaterThanOrEqualTo(String value) {
            addCriterion("storage_type >=", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeLessThan(String value) {
            addCriterion("storage_type <", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeLessThanOrEqualTo(String value) {
            addCriterion("storage_type <=", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeLike(String value) {
            addCriterion("storage_type like", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeNotLike(String value) {
            addCriterion("storage_type not like", value, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeIn(List<String> values) {
            addCriterion("storage_type in", values, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeNotIn(List<String> values) {
            addCriterion("storage_type not in", values, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeBetween(String value1, String value2) {
            addCriterion("storage_type between", value1, value2, "storageType");
            return (Criteria) this;
        }

        public Criteria andStorageTypeNotBetween(String value1, String value2) {
            addCriterion("storage_type not between", value1, value2, "storageType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeIsNull() {
            addCriterion("network_type is null");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeIsNotNull() {
            addCriterion("network_type is not null");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeEqualTo(String value) {
            addCriterion("network_type =", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeNotEqualTo(String value) {
            addCriterion("network_type <>", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeGreaterThan(String value) {
            addCriterion("network_type >", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeGreaterThanOrEqualTo(String value) {
            addCriterion("network_type >=", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeLessThan(String value) {
            addCriterion("network_type <", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeLessThanOrEqualTo(String value) {
            addCriterion("network_type <=", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeLike(String value) {
            addCriterion("network_type like", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeNotLike(String value) {
            addCriterion("network_type not like", value, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeIn(List<String> values) {
            addCriterion("network_type in", values, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeNotIn(List<String> values) {
            addCriterion("network_type not in", values, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeBetween(String value1, String value2) {
            addCriterion("network_type between", value1, value2, "networkType");
            return (Criteria) this;
        }

        public Criteria andNetworkTypeNotBetween(String value1, String value2) {
            addCriterion("network_type not between", value1, value2, "networkType");
            return (Criteria) this;
        }

        public Criteria andDsIsNull() {
            addCriterion("ds is null");
            return (Criteria) this;
        }

        public Criteria andDsIsNotNull() {
            addCriterion("ds is not null");
            return (Criteria) this;
        }

        public Criteria andDsEqualTo(String value) {
            addCriterion("ds =", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsNotEqualTo(String value) {
            addCriterion("ds <>", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsGreaterThan(String value) {
            addCriterion("ds >", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsGreaterThanOrEqualTo(String value) {
            addCriterion("ds >=", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsLessThan(String value) {
            addCriterion("ds <", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsLessThanOrEqualTo(String value) {
            addCriterion("ds <=", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsLike(String value) {
            addCriterion("ds like", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsNotLike(String value) {
            addCriterion("ds not like", value, "ds");
            return (Criteria) this;
        }

        public Criteria andDsIn(List<String> values) {
            addCriterion("ds in", values, "ds");
            return (Criteria) this;
        }

        public Criteria andDsNotIn(List<String> values) {
            addCriterion("ds not in", values, "ds");
            return (Criteria) this;
        }

        public Criteria andDsBetween(String value1, String value2) {
            addCriterion("ds between", value1, value2, "ds");
            return (Criteria) this;
        }

        public Criteria andDsNotBetween(String value1, String value2) {
            addCriterion("ds not between", value1, value2, "ds");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeIsNull() {
            addCriterion("storage_network_type is null");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeIsNotNull() {
            addCriterion("storage_network_type is not null");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeEqualTo(String value) {
            addCriterion("storage_network_type =", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeNotEqualTo(String value) {
            addCriterion("storage_network_type <>", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeGreaterThan(String value) {
            addCriterion("storage_network_type >", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeGreaterThanOrEqualTo(String value) {
            addCriterion("storage_network_type >=", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeLessThan(String value) {
            addCriterion("storage_network_type <", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeLessThanOrEqualTo(String value) {
            addCriterion("storage_network_type <=", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeLike(String value) {
            addCriterion("storage_network_type like", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeNotLike(String value) {
            addCriterion("storage_network_type not like", value, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeIn(List<String> values) {
            addCriterion("storage_network_type in", values, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeNotIn(List<String> values) {
            addCriterion("storage_network_type not in", values, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeBetween(String value1, String value2) {
            addCriterion("storage_network_type between", value1, value2, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andStorageNetworkTypeNotBetween(String value1, String value2) {
            addCriterion("storage_network_type not between", value1, value2, "storageNetworkType");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationIsNull() {
            addCriterion("cpu_generation is null");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationIsNotNull() {
            addCriterion("cpu_generation is not null");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationEqualTo(String value) {
            addCriterion("cpu_generation =", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationNotEqualTo(String value) {
            addCriterion("cpu_generation <>", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationGreaterThan(String value) {
            addCriterion("cpu_generation >", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationGreaterThanOrEqualTo(String value) {
            addCriterion("cpu_generation >=", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationLessThan(String value) {
            addCriterion("cpu_generation <", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationLessThanOrEqualTo(String value) {
            addCriterion("cpu_generation <=", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationLike(String value) {
            addCriterion("cpu_generation like", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationNotLike(String value) {
            addCriterion("cpu_generation not like", value, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationIn(List<String> values) {
            addCriterion("cpu_generation in", values, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationNotIn(List<String> values) {
            addCriterion("cpu_generation not in", values, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationBetween(String value1, String value2) {
            addCriterion("cpu_generation between", value1, value2, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andCpuGenerationNotBetween(String value1, String value2) {
            addCriterion("cpu_generation not between", value1, value2, "cpuGeneration");
            return (Criteria) this;
        }

        public Criteria andRegionAliasIsNull() {
            addCriterion("region_alias is null");
            return (Criteria) this;
        }

        public Criteria andRegionAliasIsNotNull() {
            addCriterion("region_alias is not null");
            return (Criteria) this;
        }

        public Criteria andRegionAliasEqualTo(String value) {
            addCriterion("region_alias =", value, "regionAlias");
            return (Criteria) this;
        }

        public Criteria andRegionAliasNotEqualTo(String value) {
            addCriterion("region_alias <>", value, "regionAlias");
            return (Criteria) this;
        }

        public Criteria andRegionAliasGreaterThan(String value) {
            addCriterion("region_alias >", value, "regionAlias");
            return (Criteria) this;
        }

        public Criteria andRegionAliasGreaterThanOrEqualTo(String value) {
            addCriterion("region_alias >=", value, "regionAlias");
            return (Criteria) this;
        }

        public Criteria andRegionAliasLessThan(String value) {
            addCriterion("region_alias <", value, "regionAlias");
            return (Criteria) this;
        }

        public Criteria andRegionAliasLessThanOrEqualTo(String value) {
            addCriterion("region_alias <=", value, "regionAlias");
            return (Criteria) this;
        }

        public Criteria andRegionAliasLike(String value) {
            addCriterion("region_alias like", value, "regionAlias");
            return (Criteria) this;
        }

        public Criteria andRegionAliasNotLike(String value) {
            addCriterion("region_alias not like", value, "regionAlias");
            return (Criteria) this;
        }

        public Criteria andRegionAliasIn(List<String> values) {
            addCriterion("region_alias in", values, "regionAlias");
            return (Criteria) this;
        }

        public Criteria andRegionAliasNotIn(List<String> values) {
            addCriterion("region_alias not in", values, "regionAlias");
            return (Criteria) this;
        }

        public Criteria andRegionAliasBetween(String value1, String value2) {
            addCriterion("region_alias between", value1, value2, "regionAlias");
            return (Criteria) this;
        }

        public Criteria andRegionAliasNotBetween(String value1, String value2) {
            addCriterion("region_alias not between", value1, value2, "regionAlias");
            return (Criteria) this;
        }

        public Criteria andFirstSupportedFamilyIsNull() {
            addCriterion("first_supported_family is null");
            return (Criteria) this;
        }

        public Criteria andFirstSupportedFamilyIsNotNull() {
            addCriterion("first_supported_family is not null");
            return (Criteria) this;
        }

        public Criteria andFirstSupportedFamilyEqualTo(String value) {
            addCriterion("first_supported_family =", value, "firstSupportedFamily");
            return (Criteria) this;
        }

        public Criteria andFirstSupportedFamilyNotEqualTo(String value) {
            addCriterion("first_supported_family <>", value, "firstSupportedFamily");
            return (Criteria) this;
        }

        public Criteria andFirstSupportedFamilyGreaterThan(String value) {
            addCriterion("first_supported_family >", value, "firstSupportedFamily");
            return (Criteria) this;
        }

        public Criteria andFirstSupportedFamilyGreaterThanOrEqualTo(String value) {
            addCriterion("first_supported_family >=", value, "firstSupportedFamily");
            return (Criteria) this;
        }

        public Criteria andFirstSupportedFamilyLessThan(String value) {
            addCriterion("first_supported_family <", value, "firstSupportedFamily");
            return (Criteria) this;
        }

        public Criteria andFirstSupportedFamilyLessThanOrEqualTo(String value) {
            addCriterion("first_supported_family <=", value, "firstSupportedFamily");
            return (Criteria) this;
        }

        public Criteria andFirstSupportedFamilyLike(String value) {
            addCriterion("first_supported_family like", value, "firstSupportedFamily");
            return (Criteria) this;
        }

        public Criteria andFirstSupportedFamilyNotLike(String value) {
            addCriterion("first_supported_family not like", value, "firstSupportedFamily");
            return (Criteria) this;
        }

        public Criteria andFirstSupportedFamilyIn(List<String> values) {
            addCriterion("first_supported_family in", values, "firstSupportedFamily");
            return (Criteria) this;
        }

        public Criteria andFirstSupportedFamilyNotIn(List<String> values) {
            addCriterion("first_supported_family not in", values, "firstSupportedFamily");
            return (Criteria) this;
        }

        public Criteria andFirstSupportedFamilyBetween(String value1, String value2) {
            addCriterion("first_supported_family between", value1, value2, "firstSupportedFamily");
            return (Criteria) this;
        }

        public Criteria andFirstSupportedFamilyNotBetween(String value1, String value2) {
            addCriterion("first_supported_family not between", value1, value2, "firstSupportedFamily");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifyIsNull() {
            addCriterion("gmt_modify is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifyIsNotNull() {
            addCriterion("gmt_modify is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifyEqualTo(Date value) {
            addCriterion("gmt_modify =", value, "gmtModify");
            return (Criteria) this;
        }

        public Criteria andGmtModifyNotEqualTo(Date value) {
            addCriterion("gmt_modify <>", value, "gmtModify");
            return (Criteria) this;
        }

        public Criteria andGmtModifyGreaterThan(Date value) {
            addCriterion("gmt_modify >", value, "gmtModify");
            return (Criteria) this;
        }

        public Criteria andGmtModifyGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modify >=", value, "gmtModify");
            return (Criteria) this;
        }

        public Criteria andGmtModifyLessThan(Date value) {
            addCriterion("gmt_modify <", value, "gmtModify");
            return (Criteria) this;
        }

        public Criteria andGmtModifyLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modify <=", value, "gmtModify");
            return (Criteria) this;
        }

        public Criteria andGmtModifyIn(List<Date> values) {
            addCriterion("gmt_modify in", values, "gmtModify");
            return (Criteria) this;
        }

        public Criteria andGmtModifyNotIn(List<Date> values) {
            addCriterion("gmt_modify not in", values, "gmtModify");
            return (Criteria) this;
        }

        public Criteria andGmtModifyBetween(Date value1, Date value2) {
            addCriterion("gmt_modify between", value1, value2, "gmtModify");
            return (Criteria) this;
        }

        public Criteria andGmtModifyNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modify not between", value1, value2, "gmtModify");
            return (Criteria) this;
        }

        public Criteria andParentServiceTagIsNull() {
            addCriterion("parent_service_tag is null");
            return (Criteria) this;
        }

        public Criteria andParentServiceTagIsNotNull() {
            addCriterion("parent_service_tag is not null");
            return (Criteria) this;
        }

        public Criteria andParentServiceTagEqualTo(String value) {
            addCriterion("parent_service_tag =", value, "parentServiceTag");
            return (Criteria) this;
        }

        public Criteria andParentServiceTagNotEqualTo(String value) {
            addCriterion("parent_service_tag <>", value, "parentServiceTag");
            return (Criteria) this;
        }

        public Criteria andParentServiceTagGreaterThan(String value) {
            addCriterion("parent_service_tag >", value, "parentServiceTag");
            return (Criteria) this;
        }

        public Criteria andParentServiceTagGreaterThanOrEqualTo(String value) {
            addCriterion("parent_service_tag >=", value, "parentServiceTag");
            return (Criteria) this;
        }

        public Criteria andParentServiceTagLessThan(String value) {
            addCriterion("parent_service_tag <", value, "parentServiceTag");
            return (Criteria) this;
        }

        public Criteria andParentServiceTagLessThanOrEqualTo(String value) {
            addCriterion("parent_service_tag <=", value, "parentServiceTag");
            return (Criteria) this;
        }

        public Criteria andParentServiceTagLike(String value) {
            addCriterion("parent_service_tag like", value, "parentServiceTag");
            return (Criteria) this;
        }

        public Criteria andParentServiceTagNotLike(String value) {
            addCriterion("parent_service_tag not like", value, "parentServiceTag");
            return (Criteria) this;
        }

        public Criteria andParentServiceTagIn(List<String> values) {
            addCriterion("parent_service_tag in", values, "parentServiceTag");
            return (Criteria) this;
        }

        public Criteria andParentServiceTagNotIn(List<String> values) {
            addCriterion("parent_service_tag not in", values, "parentServiceTag");
            return (Criteria) this;
        }

        public Criteria andParentServiceTagBetween(String value1, String value2) {
            addCriterion("parent_service_tag between", value1, value2, "parentServiceTag");
            return (Criteria) this;
        }

        public Criteria andParentServiceTagNotBetween(String value1, String value2) {
            addCriterion("parent_service_tag not between", value1, value2, "parentServiceTag");
            return (Criteria) this;
        }

        public Criteria andAswIdIsNull() {
            addCriterion("asw_id is null");
            return (Criteria) this;
        }

        public Criteria andAswIdIsNotNull() {
            addCriterion("asw_id is not null");
            return (Criteria) this;
        }

        public Criteria andAswIdEqualTo(String value) {
            addCriterion("asw_id =", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdNotEqualTo(String value) {
            addCriterion("asw_id <>", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdGreaterThan(String value) {
            addCriterion("asw_id >", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdGreaterThanOrEqualTo(String value) {
            addCriterion("asw_id >=", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdLessThan(String value) {
            addCriterion("asw_id <", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdLessThanOrEqualTo(String value) {
            addCriterion("asw_id <=", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdLike(String value) {
            addCriterion("asw_id like", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdNotLike(String value) {
            addCriterion("asw_id not like", value, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdIn(List<String> values) {
            addCriterion("asw_id in", values, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdNotIn(List<String> values) {
            addCriterion("asw_id not in", values, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdBetween(String value1, String value2) {
            addCriterion("asw_id between", value1, value2, "aswId");
            return (Criteria) this;
        }

        public Criteria andAswIdNotBetween(String value1, String value2) {
            addCriterion("asw_id not between", value1, value2, "aswId");
            return (Criteria) this;
        }

        public Criteria andIsLocationPublicInSaleIsNull() {
            addCriterion("is_location_public_in_sale is null");
            return (Criteria) this;
        }

        public Criteria andIsLocationPublicInSaleIsNotNull() {
            addCriterion("is_location_public_in_sale is not null");
            return (Criteria) this;
        }

        public Criteria andIsLocationPublicInSaleEqualTo(Boolean value) {
            addCriterion("is_location_public_in_sale =", value, "isLocationPublicInSale");
            return (Criteria) this;
        }

        public Criteria andIsLocationPublicInSaleNotEqualTo(Boolean value) {
            addCriterion("is_location_public_in_sale <>", value, "isLocationPublicInSale");
            return (Criteria) this;
        }

        public Criteria andIsLocationPublicInSaleGreaterThan(Boolean value) {
            addCriterion("is_location_public_in_sale >", value, "isLocationPublicInSale");
            return (Criteria) this;
        }

        public Criteria andIsLocationPublicInSaleGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_location_public_in_sale >=", value, "isLocationPublicInSale");
            return (Criteria) this;
        }

        public Criteria andIsLocationPublicInSaleLessThan(Boolean value) {
            addCriterion("is_location_public_in_sale <", value, "isLocationPublicInSale");
            return (Criteria) this;
        }

        public Criteria andIsLocationPublicInSaleLessThanOrEqualTo(Boolean value) {
            addCriterion("is_location_public_in_sale <=", value, "isLocationPublicInSale");
            return (Criteria) this;
        }

        public Criteria andIsLocationPublicInSaleIn(List<Boolean> values) {
            addCriterion("is_location_public_in_sale in", values, "isLocationPublicInSale");
            return (Criteria) this;
        }

        public Criteria andIsLocationPublicInSaleNotIn(List<Boolean> values) {
            addCriterion("is_location_public_in_sale not in", values, "isLocationPublicInSale");
            return (Criteria) this;
        }

        public Criteria andIsLocationPublicInSaleBetween(Boolean value1, Boolean value2) {
            addCriterion("is_location_public_in_sale between", value1, value2, "isLocationPublicInSale");
            return (Criteria) this;
        }

        public Criteria andIsLocationPublicInSaleNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_location_public_in_sale not between", value1, value2, "isLocationPublicInSale");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNull() {
            addCriterion("product_name is null");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNotNull() {
            addCriterion("product_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductNameEqualTo(String value) {
            addCriterion("product_name =", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotEqualTo(String value) {
            addCriterion("product_name <>", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThan(String value) {
            addCriterion("product_name >", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_name >=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThan(String value) {
            addCriterion("product_name <", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThanOrEqualTo(String value) {
            addCriterion("product_name <=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLike(String value) {
            addCriterion("product_name like", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotLike(String value) {
            addCriterion("product_name not like", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameIn(List<String> values) {
            addCriterion("product_name in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotIn(List<String> values) {
            addCriterion("product_name not in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameBetween(String value1, String value2) {
            addCriterion("product_name between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotBetween(String value1, String value2) {
            addCriterion("product_name not between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andCnSnIsNull() {
            addCriterion("cn_sn is null");
            return (Criteria) this;
        }

        public Criteria andCnSnIsNotNull() {
            addCriterion("cn_sn is not null");
            return (Criteria) this;
        }

        public Criteria andCnSnEqualTo(String value) {
            addCriterion("cn_sn =", value, "cnSn");
            return (Criteria) this;
        }

        public Criteria andCnSnNotEqualTo(String value) {
            addCriterion("cn_sn <>", value, "cnSn");
            return (Criteria) this;
        }

        public Criteria andCnSnGreaterThan(String value) {
            addCriterion("cn_sn >", value, "cnSn");
            return (Criteria) this;
        }

        public Criteria andCnSnGreaterThanOrEqualTo(String value) {
            addCriterion("cn_sn >=", value, "cnSn");
            return (Criteria) this;
        }

        public Criteria andCnSnLessThan(String value) {
            addCriterion("cn_sn <", value, "cnSn");
            return (Criteria) this;
        }

        public Criteria andCnSnLessThanOrEqualTo(String value) {
            addCriterion("cn_sn <=", value, "cnSn");
            return (Criteria) this;
        }

        public Criteria andCnSnLike(String value) {
            addCriterion("cn_sn like", value, "cnSn");
            return (Criteria) this;
        }

        public Criteria andCnSnNotLike(String value) {
            addCriterion("cn_sn not like", value, "cnSn");
            return (Criteria) this;
        }

        public Criteria andCnSnIn(List<String> values) {
            addCriterion("cn_sn in", values, "cnSn");
            return (Criteria) this;
        }

        public Criteria andCnSnNotIn(List<String> values) {
            addCriterion("cn_sn not in", values, "cnSn");
            return (Criteria) this;
        }

        public Criteria andCnSnBetween(String value1, String value2) {
            addCriterion("cn_sn between", value1, value2, "cnSn");
            return (Criteria) this;
        }

        public Criteria andCnSnNotBetween(String value1, String value2) {
            addCriterion("cn_sn not between", value1, value2, "cnSn");
            return (Criteria) this;
        }

        public Criteria andIsAcacheEnabledIsNull() {
            addCriterion("is_acache_enabled is null");
            return (Criteria) this;
        }

        public Criteria andIsAcacheEnabledIsNotNull() {
            addCriterion("is_acache_enabled is not null");
            return (Criteria) this;
        }

        public Criteria andIsAcacheEnabledEqualTo(Boolean value) {
            addCriterion("is_acache_enabled =", value, "isAcacheEnabled");
            return (Criteria) this;
        }

        public Criteria andIsAcacheEnabledNotEqualTo(Boolean value) {
            addCriterion("is_acache_enabled <>", value, "isAcacheEnabled");
            return (Criteria) this;
        }

        public Criteria andIsAcacheEnabledGreaterThan(Boolean value) {
            addCriterion("is_acache_enabled >", value, "isAcacheEnabled");
            return (Criteria) this;
        }

        public Criteria andIsAcacheEnabledGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_acache_enabled >=", value, "isAcacheEnabled");
            return (Criteria) this;
        }

        public Criteria andIsAcacheEnabledLessThan(Boolean value) {
            addCriterion("is_acache_enabled <", value, "isAcacheEnabled");
            return (Criteria) this;
        }

        public Criteria andIsAcacheEnabledLessThanOrEqualTo(Boolean value) {
            addCriterion("is_acache_enabled <=", value, "isAcacheEnabled");
            return (Criteria) this;
        }

        public Criteria andIsAcacheEnabledIn(List<Boolean> values) {
            addCriterion("is_acache_enabled in", values, "isAcacheEnabled");
            return (Criteria) this;
        }

        public Criteria andIsAcacheEnabledNotIn(List<Boolean> values) {
            addCriterion("is_acache_enabled not in", values, "isAcacheEnabled");
            return (Criteria) this;
        }

        public Criteria andIsAcacheEnabledBetween(Boolean value1, Boolean value2) {
            addCriterion("is_acache_enabled between", value1, value2, "isAcacheEnabled");
            return (Criteria) this;
        }

        public Criteria andIsAcacheEnabledNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_acache_enabled not between", value1, value2, "isAcacheEnabled");
            return (Criteria) this;
        }

        public Criteria andMachineFrameSnIsNull() {
            addCriterion("machine_frame_sn is null");
            return (Criteria) this;
        }

        public Criteria andMachineFrameSnIsNotNull() {
            addCriterion("machine_frame_sn is not null");
            return (Criteria) this;
        }

        public Criteria andMachineFrameSnEqualTo(String value) {
            addCriterion("machine_frame_sn =", value, "machineFrameSn");
            return (Criteria) this;
        }

        public Criteria andMachineFrameSnNotEqualTo(String value) {
            addCriterion("machine_frame_sn <>", value, "machineFrameSn");
            return (Criteria) this;
        }

        public Criteria andMachineFrameSnGreaterThan(String value) {
            addCriterion("machine_frame_sn >", value, "machineFrameSn");
            return (Criteria) this;
        }

        public Criteria andMachineFrameSnGreaterThanOrEqualTo(String value) {
            addCriterion("machine_frame_sn >=", value, "machineFrameSn");
            return (Criteria) this;
        }

        public Criteria andMachineFrameSnLessThan(String value) {
            addCriterion("machine_frame_sn <", value, "machineFrameSn");
            return (Criteria) this;
        }

        public Criteria andMachineFrameSnLessThanOrEqualTo(String value) {
            addCriterion("machine_frame_sn <=", value, "machineFrameSn");
            return (Criteria) this;
        }

        public Criteria andMachineFrameSnLike(String value) {
            addCriterion("machine_frame_sn like", value, "machineFrameSn");
            return (Criteria) this;
        }

        public Criteria andMachineFrameSnNotLike(String value) {
            addCriterion("machine_frame_sn not like", value, "machineFrameSn");
            return (Criteria) this;
        }

        public Criteria andMachineFrameSnIn(List<String> values) {
            addCriterion("machine_frame_sn in", values, "machineFrameSn");
            return (Criteria) this;
        }

        public Criteria andMachineFrameSnNotIn(List<String> values) {
            addCriterion("machine_frame_sn not in", values, "machineFrameSn");
            return (Criteria) this;
        }

        public Criteria andMachineFrameSnBetween(String value1, String value2) {
            addCriterion("machine_frame_sn between", value1, value2, "machineFrameSn");
            return (Criteria) this;
        }

        public Criteria andMachineFrameSnNotBetween(String value1, String value2) {
            addCriterion("machine_frame_sn not between", value1, value2, "machineFrameSn");
            return (Criteria) this;
        }

        public Criteria andDeviceModelModelNameIsNull() {
            addCriterion("device_model_model_name is null");
            return (Criteria) this;
        }

        public Criteria andDeviceModelModelNameIsNotNull() {
            addCriterion("device_model_model_name is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceModelModelNameEqualTo(String value) {
            addCriterion("device_model_model_name =", value, "deviceModelModelName");
            return (Criteria) this;
        }

        public Criteria andDeviceModelModelNameNotEqualTo(String value) {
            addCriterion("device_model_model_name <>", value, "deviceModelModelName");
            return (Criteria) this;
        }

        public Criteria andDeviceModelModelNameGreaterThan(String value) {
            addCriterion("device_model_model_name >", value, "deviceModelModelName");
            return (Criteria) this;
        }

        public Criteria andDeviceModelModelNameGreaterThanOrEqualTo(String value) {
            addCriterion("device_model_model_name >=", value, "deviceModelModelName");
            return (Criteria) this;
        }

        public Criteria andDeviceModelModelNameLessThan(String value) {
            addCriterion("device_model_model_name <", value, "deviceModelModelName");
            return (Criteria) this;
        }

        public Criteria andDeviceModelModelNameLessThanOrEqualTo(String value) {
            addCriterion("device_model_model_name <=", value, "deviceModelModelName");
            return (Criteria) this;
        }

        public Criteria andDeviceModelModelNameLike(String value) {
            addCriterion("device_model_model_name like", value, "deviceModelModelName");
            return (Criteria) this;
        }

        public Criteria andDeviceModelModelNameNotLike(String value) {
            addCriterion("device_model_model_name not like", value, "deviceModelModelName");
            return (Criteria) this;
        }

        public Criteria andDeviceModelModelNameIn(List<String> values) {
            addCriterion("device_model_model_name in", values, "deviceModelModelName");
            return (Criteria) this;
        }

        public Criteria andDeviceModelModelNameNotIn(List<String> values) {
            addCriterion("device_model_model_name not in", values, "deviceModelModelName");
            return (Criteria) this;
        }

        public Criteria andDeviceModelModelNameBetween(String value1, String value2) {
            addCriterion("device_model_model_name between", value1, value2, "deviceModelModelName");
            return (Criteria) this;
        }

        public Criteria andDeviceModelModelNameNotBetween(String value1, String value2) {
            addCriterion("device_model_model_name not between", value1, value2, "deviceModelModelName");
            return (Criteria) this;
        }

        public Criteria andDeviceBrandBrandNameIsNull() {
            addCriterion("device_brand_brand_name is null");
            return (Criteria) this;
        }

        public Criteria andDeviceBrandBrandNameIsNotNull() {
            addCriterion("device_brand_brand_name is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceBrandBrandNameEqualTo(String value) {
            addCriterion("device_brand_brand_name =", value, "deviceBrandBrandName");
            return (Criteria) this;
        }

        public Criteria andDeviceBrandBrandNameNotEqualTo(String value) {
            addCriterion("device_brand_brand_name <>", value, "deviceBrandBrandName");
            return (Criteria) this;
        }

        public Criteria andDeviceBrandBrandNameGreaterThan(String value) {
            addCriterion("device_brand_brand_name >", value, "deviceBrandBrandName");
            return (Criteria) this;
        }

        public Criteria andDeviceBrandBrandNameGreaterThanOrEqualTo(String value) {
            addCriterion("device_brand_brand_name >=", value, "deviceBrandBrandName");
            return (Criteria) this;
        }

        public Criteria andDeviceBrandBrandNameLessThan(String value) {
            addCriterion("device_brand_brand_name <", value, "deviceBrandBrandName");
            return (Criteria) this;
        }

        public Criteria andDeviceBrandBrandNameLessThanOrEqualTo(String value) {
            addCriterion("device_brand_brand_name <=", value, "deviceBrandBrandName");
            return (Criteria) this;
        }

        public Criteria andDeviceBrandBrandNameLike(String value) {
            addCriterion("device_brand_brand_name like", value, "deviceBrandBrandName");
            return (Criteria) this;
        }

        public Criteria andDeviceBrandBrandNameNotLike(String value) {
            addCriterion("device_brand_brand_name not like", value, "deviceBrandBrandName");
            return (Criteria) this;
        }

        public Criteria andDeviceBrandBrandNameIn(List<String> values) {
            addCriterion("device_brand_brand_name in", values, "deviceBrandBrandName");
            return (Criteria) this;
        }

        public Criteria andDeviceBrandBrandNameNotIn(List<String> values) {
            addCriterion("device_brand_brand_name not in", values, "deviceBrandBrandName");
            return (Criteria) this;
        }

        public Criteria andDeviceBrandBrandNameBetween(String value1, String value2) {
            addCriterion("device_brand_brand_name between", value1, value2, "deviceBrandBrandName");
            return (Criteria) this;
        }

        public Criteria andDeviceBrandBrandNameNotBetween(String value1, String value2) {
            addCriterion("device_brand_brand_name not between", value1, value2, "deviceBrandBrandName");
            return (Criteria) this;
        }

        public Criteria andBuyTimeIsNull() {
            addCriterion("buy_time is null");
            return (Criteria) this;
        }

        public Criteria andBuyTimeIsNotNull() {
            addCriterion("buy_time is not null");
            return (Criteria) this;
        }

        public Criteria andBuyTimeEqualTo(Date value) {
            addCriterion("buy_time =", value, "buyTime");
            return (Criteria) this;
        }

        public Criteria andBuyTimeNotEqualTo(Date value) {
            addCriterion("buy_time <>", value, "buyTime");
            return (Criteria) this;
        }

        public Criteria andBuyTimeGreaterThan(Date value) {
            addCriterion("buy_time >", value, "buyTime");
            return (Criteria) this;
        }

        public Criteria andBuyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("buy_time >=", value, "buyTime");
            return (Criteria) this;
        }

        public Criteria andBuyTimeLessThan(Date value) {
            addCriterion("buy_time <", value, "buyTime");
            return (Criteria) this;
        }

        public Criteria andBuyTimeLessThanOrEqualTo(Date value) {
            addCriterion("buy_time <=", value, "buyTime");
            return (Criteria) this;
        }

        public Criteria andBuyTimeIn(List<Date> values) {
            addCriterion("buy_time in", values, "buyTime");
            return (Criteria) this;
        }

        public Criteria andBuyTimeNotIn(List<Date> values) {
            addCriterion("buy_time not in", values, "buyTime");
            return (Criteria) this;
        }

        public Criteria andBuyTimeBetween(Date value1, Date value2) {
            addCriterion("buy_time between", value1, value2, "buyTime");
            return (Criteria) this;
        }

        public Criteria andBuyTimeNotBetween(Date value1, Date value2) {
            addCriterion("buy_time not between", value1, value2, "buyTime");
            return (Criteria) this;
        }

        public Criteria andAppParentNameIsNull() {
            addCriterion("app_parent_name is null");
            return (Criteria) this;
        }

        public Criteria andAppParentNameIsNotNull() {
            addCriterion("app_parent_name is not null");
            return (Criteria) this;
        }

        public Criteria andAppParentNameEqualTo(String value) {
            addCriterion("app_parent_name =", value, "appParentName");
            return (Criteria) this;
        }

        public Criteria andAppParentNameNotEqualTo(String value) {
            addCriterion("app_parent_name <>", value, "appParentName");
            return (Criteria) this;
        }

        public Criteria andAppParentNameGreaterThan(String value) {
            addCriterion("app_parent_name >", value, "appParentName");
            return (Criteria) this;
        }

        public Criteria andAppParentNameGreaterThanOrEqualTo(String value) {
            addCriterion("app_parent_name >=", value, "appParentName");
            return (Criteria) this;
        }

        public Criteria andAppParentNameLessThan(String value) {
            addCriterion("app_parent_name <", value, "appParentName");
            return (Criteria) this;
        }

        public Criteria andAppParentNameLessThanOrEqualTo(String value) {
            addCriterion("app_parent_name <=", value, "appParentName");
            return (Criteria) this;
        }

        public Criteria andAppParentNameLike(String value) {
            addCriterion("app_parent_name like", value, "appParentName");
            return (Criteria) this;
        }

        public Criteria andAppParentNameNotLike(String value) {
            addCriterion("app_parent_name not like", value, "appParentName");
            return (Criteria) this;
        }

        public Criteria andAppParentNameIn(List<String> values) {
            addCriterion("app_parent_name in", values, "appParentName");
            return (Criteria) this;
        }

        public Criteria andAppParentNameNotIn(List<String> values) {
            addCriterion("app_parent_name not in", values, "appParentName");
            return (Criteria) this;
        }

        public Criteria andAppParentNameBetween(String value1, String value2) {
            addCriterion("app_parent_name between", value1, value2, "appParentName");
            return (Criteria) this;
        }

        public Criteria andAppParentNameNotBetween(String value1, String value2) {
            addCriterion("app_parent_name not between", value1, value2, "appParentName");
            return (Criteria) this;
        }

        public Criteria andOutGuaranteeTimeIsNull() {
            addCriterion("out_guarantee_time is null");
            return (Criteria) this;
        }

        public Criteria andOutGuaranteeTimeIsNotNull() {
            addCriterion("out_guarantee_time is not null");
            return (Criteria) this;
        }

        public Criteria andOutGuaranteeTimeEqualTo(String value) {
            addCriterion("out_guarantee_time =", value, "outGuaranteeTime");
            return (Criteria) this;
        }

        public Criteria andOutGuaranteeTimeNotEqualTo(String value) {
            addCriterion("out_guarantee_time <>", value, "outGuaranteeTime");
            return (Criteria) this;
        }

        public Criteria andOutGuaranteeTimeGreaterThan(String value) {
            addCriterion("out_guarantee_time >", value, "outGuaranteeTime");
            return (Criteria) this;
        }

        public Criteria andOutGuaranteeTimeGreaterThanOrEqualTo(String value) {
            addCriterion("out_guarantee_time >=", value, "outGuaranteeTime");
            return (Criteria) this;
        }

        public Criteria andOutGuaranteeTimeLessThan(String value) {
            addCriterion("out_guarantee_time <", value, "outGuaranteeTime");
            return (Criteria) this;
        }

        public Criteria andOutGuaranteeTimeLessThanOrEqualTo(String value) {
            addCriterion("out_guarantee_time <=", value, "outGuaranteeTime");
            return (Criteria) this;
        }

        public Criteria andOutGuaranteeTimeLike(String value) {
            addCriterion("out_guarantee_time like", value, "outGuaranteeTime");
            return (Criteria) this;
        }

        public Criteria andOutGuaranteeTimeNotLike(String value) {
            addCriterion("out_guarantee_time not like", value, "outGuaranteeTime");
            return (Criteria) this;
        }

        public Criteria andOutGuaranteeTimeIn(List<String> values) {
            addCriterion("out_guarantee_time in", values, "outGuaranteeTime");
            return (Criteria) this;
        }

        public Criteria andOutGuaranteeTimeNotIn(List<String> values) {
            addCriterion("out_guarantee_time not in", values, "outGuaranteeTime");
            return (Criteria) this;
        }

        public Criteria andOutGuaranteeTimeBetween(String value1, String value2) {
            addCriterion("out_guarantee_time between", value1, value2, "outGuaranteeTime");
            return (Criteria) this;
        }

        public Criteria andOutGuaranteeTimeNotBetween(String value1, String value2) {
            addCriterion("out_guarantee_time not between", value1, value2, "outGuaranteeTime");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andCompanyIsNull() {
            addCriterion("company is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIsNotNull() {
            addCriterion("company is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyEqualTo(String value) {
            addCriterion("company =", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotEqualTo(String value) {
            addCriterion("company <>", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyGreaterThan(String value) {
            addCriterion("company >", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyGreaterThanOrEqualTo(String value) {
            addCriterion("company >=", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLessThan(String value) {
            addCriterion("company <", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLessThanOrEqualTo(String value) {
            addCriterion("company <=", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLike(String value) {
            addCriterion("company like", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotLike(String value) {
            addCriterion("company not like", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyIn(List<String> values) {
            addCriterion("company in", values, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotIn(List<String> values) {
            addCriterion("company not in", values, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyBetween(String value1, String value2) {
            addCriterion("company between", value1, value2, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotBetween(String value1, String value2) {
            addCriterion("company not between", value1, value2, "company");
            return (Criteria) this;
        }

        public Criteria andOsTypeIsNull() {
            addCriterion("os_type is null");
            return (Criteria) this;
        }

        public Criteria andOsTypeIsNotNull() {
            addCriterion("os_type is not null");
            return (Criteria) this;
        }

        public Criteria andOsTypeEqualTo(String value) {
            addCriterion("os_type =", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeNotEqualTo(String value) {
            addCriterion("os_type <>", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeGreaterThan(String value) {
            addCriterion("os_type >", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeGreaterThanOrEqualTo(String value) {
            addCriterion("os_type >=", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeLessThan(String value) {
            addCriterion("os_type <", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeLessThanOrEqualTo(String value) {
            addCriterion("os_type <=", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeLike(String value) {
            addCriterion("os_type like", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeNotLike(String value) {
            addCriterion("os_type not like", value, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeIn(List<String> values) {
            addCriterion("os_type in", values, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeNotIn(List<String> values) {
            addCriterion("os_type not in", values, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeBetween(String value1, String value2) {
            addCriterion("os_type between", value1, value2, "osType");
            return (Criteria) this;
        }

        public Criteria andOsTypeNotBetween(String value1, String value2) {
            addCriterion("os_type not between", value1, value2, "osType");
            return (Criteria) this;
        }

        public Criteria andOsClonedAtIsNull() {
            addCriterion("os_cloned_at is null");
            return (Criteria) this;
        }

        public Criteria andOsClonedAtIsNotNull() {
            addCriterion("os_cloned_at is not null");
            return (Criteria) this;
        }

        public Criteria andOsClonedAtEqualTo(String value) {
            addCriterion("os_cloned_at =", value, "osClonedAt");
            return (Criteria) this;
        }

        public Criteria andOsClonedAtNotEqualTo(String value) {
            addCriterion("os_cloned_at <>", value, "osClonedAt");
            return (Criteria) this;
        }

        public Criteria andOsClonedAtGreaterThan(String value) {
            addCriterion("os_cloned_at >", value, "osClonedAt");
            return (Criteria) this;
        }

        public Criteria andOsClonedAtGreaterThanOrEqualTo(String value) {
            addCriterion("os_cloned_at >=", value, "osClonedAt");
            return (Criteria) this;
        }

        public Criteria andOsClonedAtLessThan(String value) {
            addCriterion("os_cloned_at <", value, "osClonedAt");
            return (Criteria) this;
        }

        public Criteria andOsClonedAtLessThanOrEqualTo(String value) {
            addCriterion("os_cloned_at <=", value, "osClonedAt");
            return (Criteria) this;
        }

        public Criteria andOsClonedAtLike(String value) {
            addCriterion("os_cloned_at like", value, "osClonedAt");
            return (Criteria) this;
        }

        public Criteria andOsClonedAtNotLike(String value) {
            addCriterion("os_cloned_at not like", value, "osClonedAt");
            return (Criteria) this;
        }

        public Criteria andOsClonedAtIn(List<String> values) {
            addCriterion("os_cloned_at in", values, "osClonedAt");
            return (Criteria) this;
        }

        public Criteria andOsClonedAtNotIn(List<String> values) {
            addCriterion("os_cloned_at not in", values, "osClonedAt");
            return (Criteria) this;
        }

        public Criteria andOsClonedAtBetween(String value1, String value2) {
            addCriterion("os_cloned_at between", value1, value2, "osClonedAt");
            return (Criteria) this;
        }

        public Criteria andOsClonedAtNotBetween(String value1, String value2) {
            addCriterion("os_cloned_at not between", value1, value2, "osClonedAt");
            return (Criteria) this;
        }

        public Criteria andOsReleaseVersionIsNull() {
            addCriterion("os_release_version is null");
            return (Criteria) this;
        }

        public Criteria andOsReleaseVersionIsNotNull() {
            addCriterion("os_release_version is not null");
            return (Criteria) this;
        }

        public Criteria andOsReleaseVersionEqualTo(String value) {
            addCriterion("os_release_version =", value, "osReleaseVersion");
            return (Criteria) this;
        }

        public Criteria andOsReleaseVersionNotEqualTo(String value) {
            addCriterion("os_release_version <>", value, "osReleaseVersion");
            return (Criteria) this;
        }

        public Criteria andOsReleaseVersionGreaterThan(String value) {
            addCriterion("os_release_version >", value, "osReleaseVersion");
            return (Criteria) this;
        }

        public Criteria andOsReleaseVersionGreaterThanOrEqualTo(String value) {
            addCriterion("os_release_version >=", value, "osReleaseVersion");
            return (Criteria) this;
        }

        public Criteria andOsReleaseVersionLessThan(String value) {
            addCriterion("os_release_version <", value, "osReleaseVersion");
            return (Criteria) this;
        }

        public Criteria andOsReleaseVersionLessThanOrEqualTo(String value) {
            addCriterion("os_release_version <=", value, "osReleaseVersion");
            return (Criteria) this;
        }

        public Criteria andOsReleaseVersionLike(String value) {
            addCriterion("os_release_version like", value, "osReleaseVersion");
            return (Criteria) this;
        }

        public Criteria andOsReleaseVersionNotLike(String value) {
            addCriterion("os_release_version not like", value, "osReleaseVersion");
            return (Criteria) this;
        }

        public Criteria andOsReleaseVersionIn(List<String> values) {
            addCriterion("os_release_version in", values, "osReleaseVersion");
            return (Criteria) this;
        }

        public Criteria andOsReleaseVersionNotIn(List<String> values) {
            addCriterion("os_release_version not in", values, "osReleaseVersion");
            return (Criteria) this;
        }

        public Criteria andOsReleaseVersionBetween(String value1, String value2) {
            addCriterion("os_release_version between", value1, value2, "osReleaseVersion");
            return (Criteria) this;
        }

        public Criteria andOsReleaseVersionNotBetween(String value1, String value2) {
            addCriterion("os_release_version not between", value1, value2, "osReleaseVersion");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedHostIsNull() {
            addCriterion("is_dedicated_host is null");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedHostIsNotNull() {
            addCriterion("is_dedicated_host is not null");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedHostEqualTo(Byte value) {
            addCriterion("is_dedicated_host =", value, "isDedicatedHost");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedHostNotEqualTo(Byte value) {
            addCriterion("is_dedicated_host <>", value, "isDedicatedHost");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedHostGreaterThan(Byte value) {
            addCriterion("is_dedicated_host >", value, "isDedicatedHost");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedHostGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_dedicated_host >=", value, "isDedicatedHost");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedHostLessThan(Byte value) {
            addCriterion("is_dedicated_host <", value, "isDedicatedHost");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedHostLessThanOrEqualTo(Byte value) {
            addCriterion("is_dedicated_host <=", value, "isDedicatedHost");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedHostIn(List<Byte> values) {
            addCriterion("is_dedicated_host in", values, "isDedicatedHost");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedHostNotIn(List<Byte> values) {
            addCriterion("is_dedicated_host not in", values, "isDedicatedHost");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedHostBetween(Byte value1, Byte value2) {
            addCriterion("is_dedicated_host between", value1, value2, "isDedicatedHost");
            return (Criteria) this;
        }

        public Criteria andIsDedicatedHostNotBetween(Byte value1, Byte value2) {
            addCriterion("is_dedicated_host not between", value1, value2, "isDedicatedHost");
            return (Criteria) this;
        }

        public Criteria andFrameShapeIsNull() {
            addCriterion("frame_shape is null");
            return (Criteria) this;
        }

        public Criteria andFrameShapeIsNotNull() {
            addCriterion("frame_shape is not null");
            return (Criteria) this;
        }

        public Criteria andFrameShapeEqualTo(String value) {
            addCriterion("frame_shape =", value, "frameShape");
            return (Criteria) this;
        }

        public Criteria andFrameShapeNotEqualTo(String value) {
            addCriterion("frame_shape <>", value, "frameShape");
            return (Criteria) this;
        }

        public Criteria andFrameShapeGreaterThan(String value) {
            addCriterion("frame_shape >", value, "frameShape");
            return (Criteria) this;
        }

        public Criteria andFrameShapeGreaterThanOrEqualTo(String value) {
            addCriterion("frame_shape >=", value, "frameShape");
            return (Criteria) this;
        }

        public Criteria andFrameShapeLessThan(String value) {
            addCriterion("frame_shape <", value, "frameShape");
            return (Criteria) this;
        }

        public Criteria andFrameShapeLessThanOrEqualTo(String value) {
            addCriterion("frame_shape <=", value, "frameShape");
            return (Criteria) this;
        }

        public Criteria andFrameShapeLike(String value) {
            addCriterion("frame_shape like", value, "frameShape");
            return (Criteria) this;
        }

        public Criteria andFrameShapeNotLike(String value) {
            addCriterion("frame_shape not like", value, "frameShape");
            return (Criteria) this;
        }

        public Criteria andFrameShapeIn(List<String> values) {
            addCriterion("frame_shape in", values, "frameShape");
            return (Criteria) this;
        }

        public Criteria andFrameShapeNotIn(List<String> values) {
            addCriterion("frame_shape not in", values, "frameShape");
            return (Criteria) this;
        }

        public Criteria andFrameShapeBetween(String value1, String value2) {
            addCriterion("frame_shape between", value1, value2, "frameShape");
            return (Criteria) this;
        }

        public Criteria andFrameShapeNotBetween(String value1, String value2) {
            addCriterion("frame_shape not between", value1, value2, "frameShape");
            return (Criteria) this;
        }

        public Criteria andNetworkArchIsNull() {
            addCriterion("network_arch is null");
            return (Criteria) this;
        }

        public Criteria andNetworkArchIsNotNull() {
            addCriterion("network_arch is not null");
            return (Criteria) this;
        }

        public Criteria andNetworkArchEqualTo(Integer value) {
            addCriterion("network_arch =", value, "networkArch");
            return (Criteria) this;
        }

        public Criteria andNetworkArchNotEqualTo(Integer value) {
            addCriterion("network_arch <>", value, "networkArch");
            return (Criteria) this;
        }

        public Criteria andNetworkArchGreaterThan(Integer value) {
            addCriterion("network_arch >", value, "networkArch");
            return (Criteria) this;
        }

        public Criteria andNetworkArchGreaterThanOrEqualTo(Integer value) {
            addCriterion("network_arch >=", value, "networkArch");
            return (Criteria) this;
        }

        public Criteria andNetworkArchLessThan(Integer value) {
            addCriterion("network_arch <", value, "networkArch");
            return (Criteria) this;
        }

        public Criteria andNetworkArchLessThanOrEqualTo(Integer value) {
            addCriterion("network_arch <=", value, "networkArch");
            return (Criteria) this;
        }

        public Criteria andNetworkArchIn(List<Integer> values) {
            addCriterion("network_arch in", values, "networkArch");
            return (Criteria) this;
        }

        public Criteria andNetworkArchNotIn(List<Integer> values) {
            addCriterion("network_arch not in", values, "networkArch");
            return (Criteria) this;
        }

        public Criteria andNetworkArchBetween(Integer value1, Integer value2) {
            addCriterion("network_arch between", value1, value2, "networkArch");
            return (Criteria) this;
        }

        public Criteria andNetworkArchNotBetween(Integer value1, Integer value2) {
            addCriterion("network_arch not between", value1, value2, "networkArch");
            return (Criteria) this;
        }

        public Criteria andFirstPublicDayIsNull() {
            addCriterion("first_public_day is null");
            return (Criteria) this;
        }

        public Criteria andFirstPublicDayIsNotNull() {
            addCriterion("first_public_day is not null");
            return (Criteria) this;
        }

        public Criteria andFirstPublicDayEqualTo(String value) {
            addCriterion("first_public_day =", value, "firstPublicDay");
            return (Criteria) this;
        }

        public Criteria andFirstPublicDayNotEqualTo(String value) {
            addCriterion("first_public_day <>", value, "firstPublicDay");
            return (Criteria) this;
        }

        public Criteria andFirstPublicDayGreaterThan(String value) {
            addCriterion("first_public_day >", value, "firstPublicDay");
            return (Criteria) this;
        }

        public Criteria andFirstPublicDayGreaterThanOrEqualTo(String value) {
            addCriterion("first_public_day >=", value, "firstPublicDay");
            return (Criteria) this;
        }

        public Criteria andFirstPublicDayLessThan(String value) {
            addCriterion("first_public_day <", value, "firstPublicDay");
            return (Criteria) this;
        }

        public Criteria andFirstPublicDayLessThanOrEqualTo(String value) {
            addCriterion("first_public_day <=", value, "firstPublicDay");
            return (Criteria) this;
        }

        public Criteria andFirstPublicDayLike(String value) {
            addCriterion("first_public_day like", value, "firstPublicDay");
            return (Criteria) this;
        }

        public Criteria andFirstPublicDayNotLike(String value) {
            addCriterion("first_public_day not like", value, "firstPublicDay");
            return (Criteria) this;
        }

        public Criteria andFirstPublicDayIn(List<String> values) {
            addCriterion("first_public_day in", values, "firstPublicDay");
            return (Criteria) this;
        }

        public Criteria andFirstPublicDayNotIn(List<String> values) {
            addCriterion("first_public_day not in", values, "firstPublicDay");
            return (Criteria) this;
        }

        public Criteria andFirstPublicDayBetween(String value1, String value2) {
            addCriterion("first_public_day between", value1, value2, "firstPublicDay");
            return (Criteria) this;
        }

        public Criteria andFirstPublicDayNotBetween(String value1, String value2) {
            addCriterion("first_public_day not between", value1, value2, "firstPublicDay");
            return (Criteria) this;
        }

        public Criteria andMicrocodeIsNull() {
            addCriterion("microcode is null");
            return (Criteria) this;
        }

        public Criteria andMicrocodeIsNotNull() {
            addCriterion("microcode is not null");
            return (Criteria) this;
        }

        public Criteria andMicrocodeEqualTo(String value) {
            addCriterion("microcode =", value, "microcode");
            return (Criteria) this;
        }

        public Criteria andMicrocodeNotEqualTo(String value) {
            addCriterion("microcode <>", value, "microcode");
            return (Criteria) this;
        }

        public Criteria andMicrocodeGreaterThan(String value) {
            addCriterion("microcode >", value, "microcode");
            return (Criteria) this;
        }

        public Criteria andMicrocodeGreaterThanOrEqualTo(String value) {
            addCriterion("microcode >=", value, "microcode");
            return (Criteria) this;
        }

        public Criteria andMicrocodeLessThan(String value) {
            addCriterion("microcode <", value, "microcode");
            return (Criteria) this;
        }

        public Criteria andMicrocodeLessThanOrEqualTo(String value) {
            addCriterion("microcode <=", value, "microcode");
            return (Criteria) this;
        }

        public Criteria andMicrocodeLike(String value) {
            addCriterion("microcode like", value, "microcode");
            return (Criteria) this;
        }

        public Criteria andMicrocodeNotLike(String value) {
            addCriterion("microcode not like", value, "microcode");
            return (Criteria) this;
        }

        public Criteria andMicrocodeIn(List<String> values) {
            addCriterion("microcode in", values, "microcode");
            return (Criteria) this;
        }

        public Criteria andMicrocodeNotIn(List<String> values) {
            addCriterion("microcode not in", values, "microcode");
            return (Criteria) this;
        }

        public Criteria andMicrocodeBetween(String value1, String value2) {
            addCriterion("microcode between", value1, value2, "microcode");
            return (Criteria) this;
        }

        public Criteria andMicrocodeNotBetween(String value1, String value2) {
            addCriterion("microcode not between", value1, value2, "microcode");
            return (Criteria) this;
        }

        public Criteria andClusterConfigIsNull() {
            addCriterion("cluster_config is null");
            return (Criteria) this;
        }

        public Criteria andClusterConfigIsNotNull() {
            addCriterion("cluster_config is not null");
            return (Criteria) this;
        }

        public Criteria andClusterConfigEqualTo(Long value) {
            addCriterion("cluster_config =", value, "clusterConfig");
            return (Criteria) this;
        }

        public Criteria andClusterConfigNotEqualTo(Long value) {
            addCriterion("cluster_config <>", value, "clusterConfig");
            return (Criteria) this;
        }

        public Criteria andClusterConfigGreaterThan(Long value) {
            addCriterion("cluster_config >", value, "clusterConfig");
            return (Criteria) this;
        }

        public Criteria andClusterConfigGreaterThanOrEqualTo(Long value) {
            addCriterion("cluster_config >=", value, "clusterConfig");
            return (Criteria) this;
        }

        public Criteria andClusterConfigLessThan(Long value) {
            addCriterion("cluster_config <", value, "clusterConfig");
            return (Criteria) this;
        }

        public Criteria andClusterConfigLessThanOrEqualTo(Long value) {
            addCriterion("cluster_config <=", value, "clusterConfig");
            return (Criteria) this;
        }

        public Criteria andClusterConfigIn(List<Long> values) {
            addCriterion("cluster_config in", values, "clusterConfig");
            return (Criteria) this;
        }

        public Criteria andClusterConfigNotIn(List<Long> values) {
            addCriterion("cluster_config not in", values, "clusterConfig");
            return (Criteria) this;
        }

        public Criteria andClusterConfigBetween(Long value1, Long value2) {
            addCriterion("cluster_config between", value1, value2, "clusterConfig");
            return (Criteria) this;
        }

        public Criteria andClusterConfigNotBetween(Long value1, Long value2) {
            addCriterion("cluster_config not between", value1, value2, "clusterConfig");
            return (Criteria) this;
        }

        public Criteria andAvsSessionSizeIsNull() {
            addCriterion("avs_session_size is null");
            return (Criteria) this;
        }

        public Criteria andAvsSessionSizeIsNotNull() {
            addCriterion("avs_session_size is not null");
            return (Criteria) this;
        }

        public Criteria andAvsSessionSizeEqualTo(Long value) {
            addCriterion("avs_session_size =", value, "avsSessionSize");
            return (Criteria) this;
        }

        public Criteria andAvsSessionSizeNotEqualTo(Long value) {
            addCriterion("avs_session_size <>", value, "avsSessionSize");
            return (Criteria) this;
        }

        public Criteria andAvsSessionSizeGreaterThan(Long value) {
            addCriterion("avs_session_size >", value, "avsSessionSize");
            return (Criteria) this;
        }

        public Criteria andAvsSessionSizeGreaterThanOrEqualTo(Long value) {
            addCriterion("avs_session_size >=", value, "avsSessionSize");
            return (Criteria) this;
        }

        public Criteria andAvsSessionSizeLessThan(Long value) {
            addCriterion("avs_session_size <", value, "avsSessionSize");
            return (Criteria) this;
        }

        public Criteria andAvsSessionSizeLessThanOrEqualTo(Long value) {
            addCriterion("avs_session_size <=", value, "avsSessionSize");
            return (Criteria) this;
        }

        public Criteria andAvsSessionSizeIn(List<Long> values) {
            addCriterion("avs_session_size in", values, "avsSessionSize");
            return (Criteria) this;
        }

        public Criteria andAvsSessionSizeNotIn(List<Long> values) {
            addCriterion("avs_session_size not in", values, "avsSessionSize");
            return (Criteria) this;
        }

        public Criteria andAvsSessionSizeBetween(Long value1, Long value2) {
            addCriterion("avs_session_size between", value1, value2, "avsSessionSize");
            return (Criteria) this;
        }

        public Criteria andAvsSessionSizeNotBetween(Long value1, Long value2) {
            addCriterion("avs_session_size not between", value1, value2, "avsSessionSize");
            return (Criteria) this;
        }

        public Criteria andZoneGroupFlagIsNull() {
            addCriterion("zone_group_flag is null");
            return (Criteria) this;
        }

        public Criteria andZoneGroupFlagIsNotNull() {
            addCriterion("zone_group_flag is not null");
            return (Criteria) this;
        }

        public Criteria andZoneGroupFlagEqualTo(String value) {
            addCriterion("zone_group_flag =", value, "zoneGroupFlag");
            return (Criteria) this;
        }

        public Criteria andZoneGroupFlagNotEqualTo(String value) {
            addCriterion("zone_group_flag <>", value, "zoneGroupFlag");
            return (Criteria) this;
        }

        public Criteria andZoneGroupFlagGreaterThan(String value) {
            addCriterion("zone_group_flag >", value, "zoneGroupFlag");
            return (Criteria) this;
        }

        public Criteria andZoneGroupFlagGreaterThanOrEqualTo(String value) {
            addCriterion("zone_group_flag >=", value, "zoneGroupFlag");
            return (Criteria) this;
        }

        public Criteria andZoneGroupFlagLessThan(String value) {
            addCriterion("zone_group_flag <", value, "zoneGroupFlag");
            return (Criteria) this;
        }

        public Criteria andZoneGroupFlagLessThanOrEqualTo(String value) {
            addCriterion("zone_group_flag <=", value, "zoneGroupFlag");
            return (Criteria) this;
        }

        public Criteria andZoneGroupFlagLike(String value) {
            addCriterion("zone_group_flag like", value, "zoneGroupFlag");
            return (Criteria) this;
        }

        public Criteria andZoneGroupFlagNotLike(String value) {
            addCriterion("zone_group_flag not like", value, "zoneGroupFlag");
            return (Criteria) this;
        }

        public Criteria andZoneGroupFlagIn(List<String> values) {
            addCriterion("zone_group_flag in", values, "zoneGroupFlag");
            return (Criteria) this;
        }

        public Criteria andZoneGroupFlagNotIn(List<String> values) {
            addCriterion("zone_group_flag not in", values, "zoneGroupFlag");
            return (Criteria) this;
        }

        public Criteria andZoneGroupFlagBetween(String value1, String value2) {
            addCriterion("zone_group_flag between", value1, value2, "zoneGroupFlag");
            return (Criteria) this;
        }

        public Criteria andZoneGroupFlagNotBetween(String value1, String value2) {
            addCriterion("zone_group_flag not between", value1, value2, "zoneGroupFlag");
            return (Criteria) this;
        }

        public Criteria andBusinessOversoldSwitchIsNull() {
            addCriterion("business_oversold_switch is null");
            return (Criteria) this;
        }

        public Criteria andBusinessOversoldSwitchIsNotNull() {
            addCriterion("business_oversold_switch is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessOversoldSwitchEqualTo(Byte value) {
            addCriterion("business_oversold_switch =", value, "businessOversoldSwitch");
            return (Criteria) this;
        }

        public Criteria andBusinessOversoldSwitchNotEqualTo(Byte value) {
            addCriterion("business_oversold_switch <>", value, "businessOversoldSwitch");
            return (Criteria) this;
        }

        public Criteria andBusinessOversoldSwitchGreaterThan(Byte value) {
            addCriterion("business_oversold_switch >", value, "businessOversoldSwitch");
            return (Criteria) this;
        }

        public Criteria andBusinessOversoldSwitchGreaterThanOrEqualTo(Byte value) {
            addCriterion("business_oversold_switch >=", value, "businessOversoldSwitch");
            return (Criteria) this;
        }

        public Criteria andBusinessOversoldSwitchLessThan(Byte value) {
            addCriterion("business_oversold_switch <", value, "businessOversoldSwitch");
            return (Criteria) this;
        }

        public Criteria andBusinessOversoldSwitchLessThanOrEqualTo(Byte value) {
            addCriterion("business_oversold_switch <=", value, "businessOversoldSwitch");
            return (Criteria) this;
        }

        public Criteria andBusinessOversoldSwitchIn(List<Byte> values) {
            addCriterion("business_oversold_switch in", values, "businessOversoldSwitch");
            return (Criteria) this;
        }

        public Criteria andBusinessOversoldSwitchNotIn(List<Byte> values) {
            addCriterion("business_oversold_switch not in", values, "businessOversoldSwitch");
            return (Criteria) this;
        }

        public Criteria andBusinessOversoldSwitchBetween(Byte value1, Byte value2) {
            addCriterion("business_oversold_switch between", value1, value2, "businessOversoldSwitch");
            return (Criteria) this;
        }

        public Criteria andBusinessOversoldSwitchNotBetween(Byte value1, Byte value2) {
            addCriterion("business_oversold_switch not between", value1, value2, "businessOversoldSwitch");
            return (Criteria) this;
        }

        public Criteria andMemManufacturerIsNull() {
            addCriterion("mem_manufacturer is null");
            return (Criteria) this;
        }

        public Criteria andMemManufacturerIsNotNull() {
            addCriterion("mem_manufacturer is not null");
            return (Criteria) this;
        }

        public Criteria andMemManufacturerEqualTo(String value) {
            addCriterion("mem_manufacturer =", value, "memManufacturer");
            return (Criteria) this;
        }

        public Criteria andMemManufacturerNotEqualTo(String value) {
            addCriterion("mem_manufacturer <>", value, "memManufacturer");
            return (Criteria) this;
        }

        public Criteria andMemManufacturerGreaterThan(String value) {
            addCriterion("mem_manufacturer >", value, "memManufacturer");
            return (Criteria) this;
        }

        public Criteria andMemManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("mem_manufacturer >=", value, "memManufacturer");
            return (Criteria) this;
        }

        public Criteria andMemManufacturerLessThan(String value) {
            addCriterion("mem_manufacturer <", value, "memManufacturer");
            return (Criteria) this;
        }

        public Criteria andMemManufacturerLessThanOrEqualTo(String value) {
            addCriterion("mem_manufacturer <=", value, "memManufacturer");
            return (Criteria) this;
        }

        public Criteria andMemManufacturerLike(String value) {
            addCriterion("mem_manufacturer like", value, "memManufacturer");
            return (Criteria) this;
        }

        public Criteria andMemManufacturerNotLike(String value) {
            addCriterion("mem_manufacturer not like", value, "memManufacturer");
            return (Criteria) this;
        }

        public Criteria andMemManufacturerIn(List<String> values) {
            addCriterion("mem_manufacturer in", values, "memManufacturer");
            return (Criteria) this;
        }

        public Criteria andMemManufacturerNotIn(List<String> values) {
            addCriterion("mem_manufacturer not in", values, "memManufacturer");
            return (Criteria) this;
        }

        public Criteria andMemManufacturerBetween(String value1, String value2) {
            addCriterion("mem_manufacturer between", value1, value2, "memManufacturer");
            return (Criteria) this;
        }

        public Criteria andMemManufacturerNotBetween(String value1, String value2) {
            addCriterion("mem_manufacturer not between", value1, value2, "memManufacturer");
            return (Criteria) this;
        }

        public Criteria andMemoryPnIsNull() {
            addCriterion("memory_pn is null");
            return (Criteria) this;
        }

        public Criteria andMemoryPnIsNotNull() {
            addCriterion("memory_pn is not null");
            return (Criteria) this;
        }

        public Criteria andMemoryPnEqualTo(String value) {
            addCriterion("memory_pn =", value, "memoryPn");
            return (Criteria) this;
        }

        public Criteria andMemoryPnNotEqualTo(String value) {
            addCriterion("memory_pn <>", value, "memoryPn");
            return (Criteria) this;
        }

        public Criteria andMemoryPnGreaterThan(String value) {
            addCriterion("memory_pn >", value, "memoryPn");
            return (Criteria) this;
        }

        public Criteria andMemoryPnGreaterThanOrEqualTo(String value) {
            addCriterion("memory_pn >=", value, "memoryPn");
            return (Criteria) this;
        }

        public Criteria andMemoryPnLessThan(String value) {
            addCriterion("memory_pn <", value, "memoryPn");
            return (Criteria) this;
        }

        public Criteria andMemoryPnLessThanOrEqualTo(String value) {
            addCriterion("memory_pn <=", value, "memoryPn");
            return (Criteria) this;
        }

        public Criteria andMemoryPnLike(String value) {
            addCriterion("memory_pn like", value, "memoryPn");
            return (Criteria) this;
        }

        public Criteria andMemoryPnNotLike(String value) {
            addCriterion("memory_pn not like", value, "memoryPn");
            return (Criteria) this;
        }

        public Criteria andMemoryPnIn(List<String> values) {
            addCriterion("memory_pn in", values, "memoryPn");
            return (Criteria) this;
        }

        public Criteria andMemoryPnNotIn(List<String> values) {
            addCriterion("memory_pn not in", values, "memoryPn");
            return (Criteria) this;
        }

        public Criteria andMemoryPnBetween(String value1, String value2) {
            addCriterion("memory_pn between", value1, value2, "memoryPn");
            return (Criteria) this;
        }

        public Criteria andMemoryPnNotBetween(String value1, String value2) {
            addCriterion("memory_pn not between", value1, value2, "memoryPn");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table host_static_info_ecs_alarm
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}