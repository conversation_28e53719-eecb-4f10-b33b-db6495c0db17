package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.MetricBatchRiskNewResource;
import com.aliyun.xdragon.common.generate.model.MetricBatchRiskNewResourceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface MetricBatchRiskNewResourceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    long countByExample(MetricBatchRiskNewResourceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    int deleteByExample(MetricBatchRiskNewResourceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    int insert(MetricBatchRiskNewResource record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    int insertSelective(MetricBatchRiskNewResource record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    List<MetricBatchRiskNewResource> selectByExampleWithRowbounds(MetricBatchRiskNewResourceExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    List<MetricBatchRiskNewResource> selectByExampleSelective(@Param("example") MetricBatchRiskNewResourceExample example, @Param("selective") MetricBatchRiskNewResource.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    MetricBatchRiskNewResource selectOneByExample(MetricBatchRiskNewResourceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    MetricBatchRiskNewResource selectOneByExampleSelective(@Param("example") MetricBatchRiskNewResourceExample example, @Param("selective") MetricBatchRiskNewResource.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    List<MetricBatchRiskNewResource> selectByExample(MetricBatchRiskNewResourceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    MetricBatchRiskNewResource selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") MetricBatchRiskNewResource.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    MetricBatchRiskNewResource selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") MetricBatchRiskNewResource record, @Param("example") MetricBatchRiskNewResourceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MetricBatchRiskNewResource record, @Param("example") MetricBatchRiskNewResourceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MetricBatchRiskNewResource record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MetricBatchRiskNewResource record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<MetricBatchRiskNewResource> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<MetricBatchRiskNewResource> list, @Param("selective") MetricBatchRiskNewResource.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    int upsert(MetricBatchRiskNewResource record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table metric_batch_risk_new_resource
     *
     * @mbg.generated
     */
    int upsertSelective(MetricBatchRiskNewResource record);
}