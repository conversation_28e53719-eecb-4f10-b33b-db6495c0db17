package com.aliyun.xdragon.common.exception;

/**
 * <AUTHOR>
 * @date 2022/04/25
 */
public class RedisUtilException extends XdragonMetricSystemException{
    private static final String CODE = "REDIS_CMD_ERROR";

    public RedisUtilException(String message) {
        super(message);
    }

    public RedisUtilException(String message, Throwable e) {
        super(message, e);
    }

    public RedisUtilException(Throwable e) {
        super(e);
    }

    @Override
    public String getCode() {
        return CODE;
    }
}
