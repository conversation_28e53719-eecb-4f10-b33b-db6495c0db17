package com.aliyun.xdragon.common.model.metric.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/04/04
 */
@Data
public class QueryMetricAnomalyRequest implements Serializable {
    @NotNull
    @Size(min = 1, max = 25)
    private List<String> metricNames;

    @NotNull
    @Positive
    private Integer startTs;

    @NotNull
    @Positive
    private Integer endTs;

    @NotNull
    private Long sourceId;

    private String searchValue;
}
