package com.aliyun.xdragon.common.generate.log.model.map;

import com.aliyun.xdragon.common.generate.log.model.HiddenTroubleRequest;
import com.aliyun.xdragon.common.generate.log.model.HiddenTroubleResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AllinoneRiskCustomMapper {

    Integer queryTotalByParams(HiddenTroubleRequest request);

    Integer queryTotalFailureByParams(HiddenTroubleRequest request);

    List<HiddenTroubleResponse> queryRiskByParams(HiddenTroubleRequest request);

    List<HiddenTroubleResponse> queryFailureRiskByParams(HiddenTroubleRequest request);
}
