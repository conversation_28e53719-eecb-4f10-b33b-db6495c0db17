package com.aliyun.xdragon.common.model.keyMetric.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class KeyMetricValueResponse implements Serializable {
    private static final long serialVersionUID = -5301437044053742431L;
    double performance;
    double unavailable;
    double control;

    public KeyMetricValueResponse() {
    }

    public KeyMetricValueResponse(double unavailable, double performance, double control) {
        this.performance = performance;
        this.unavailable = unavailable;
        this.control = control;
    }
}
