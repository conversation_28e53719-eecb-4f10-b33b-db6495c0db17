package com.aliyun.xdragon.common.generate.model.map;

import com.aliyun.xdragon.common.generate.model.CorrelationRuleMining;
import com.aliyun.xdragon.common.generate.model.CorrelationRuleMiningExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface CorrelationRuleMiningMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    long countByExample(CorrelationRuleMiningExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int deleteByExample(CorrelationRuleMiningExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int insert(CorrelationRuleMining record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int insertSelective(CorrelationRuleMining record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    List<CorrelationRuleMining> selectByExampleWithBLOBsWithRowbounds(CorrelationRuleMiningExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    List<CorrelationRuleMining> selectByExampleSelective(@Param("example") CorrelationRuleMiningExample example, @Param("selective") CorrelationRuleMining.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    CorrelationRuleMining selectOneByExample(CorrelationRuleMiningExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    CorrelationRuleMining selectOneByExampleSelective(@Param("example") CorrelationRuleMiningExample example, @Param("selective") CorrelationRuleMining.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    CorrelationRuleMining selectOneByExampleWithBLOBs(CorrelationRuleMiningExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    List<CorrelationRuleMining> selectByExampleWithBLOBs(CorrelationRuleMiningExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    List<CorrelationRuleMining> selectByExampleWithRowbounds(CorrelationRuleMiningExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    List<CorrelationRuleMining> selectByExample(CorrelationRuleMiningExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    CorrelationRuleMining selectByPrimaryKeySelective(@Param("id") Long id, @Param("selective") CorrelationRuleMining.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    CorrelationRuleMining selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") CorrelationRuleMining record, @Param("example") CorrelationRuleMiningExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") CorrelationRuleMining record, @Param("example") CorrelationRuleMiningExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") CorrelationRuleMining record, @Param("example") CorrelationRuleMiningExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(CorrelationRuleMining record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(CorrelationRuleMining record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(CorrelationRuleMining record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int batchInsert(@Param("list") List<CorrelationRuleMining> list);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int batchInsertSelective(@Param("list") List<CorrelationRuleMining> list, @Param("selective") CorrelationRuleMining.Column ... selective);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int upsert(CorrelationRuleMining record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int upsertSelective(CorrelationRuleMining record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table correlation_rule_mining
     *
     * @mbg.generated
     */
    int upsertWithBLOBs(CorrelationRuleMining record);
}