package com.aliyun.xdragon.common.model.param;

/**
 * <AUTHOR>
 * @date 2022/04/11
 */

import java.io.Serializable;
import java.util.List;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class ManualTaskAction implements Serializable {

    @SerializedName("name")
    @Expose
    private String name;
    @SerializedName("data_set")
    @Expose
    private List<List<Double>> dataSet = null;
    private final static long serialVersionUID = 204296478051814164L;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<List<Double>> getDataSet() {
        return dataSet;
    }

    public void setDataSet(List<List<Double>> dataSet) {
        this.dataSet = dataSet;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(ManualTaskAction.class.getName()).append('@').append(Integer.toHexString(System.identityHashCode(this))).append('[');
        sb.append("name");
        sb.append('=');
        sb.append(((this.name == null)?"<null>":this.name));
        sb.append(',');
        sb.append("dataSet");
        sb.append('=');
        sb.append(((this.dataSet == null)?"<null>":this.dataSet));
        sb.append(',');
        if (sb.charAt((sb.length()- 1)) == ',') {
            sb.setCharAt((sb.length()- 1), ']');
        } else {
            sb.append(']');
        }
        return sb.toString();
    }

}
