package com.aliyun.xdragon.common.generate.model.map;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;

public interface WorkItemLabelCustomMapper {

    @Select(
        "<script> "
            + "select distinct(`${key}`) from work_item_label where "
            + "tag_level = #{level}"
            + " </script> "
    )
    @ResultType(String.class)
    List<String> getDistrictKeys(@Param("key") String key, @Param("level") int level);
}
