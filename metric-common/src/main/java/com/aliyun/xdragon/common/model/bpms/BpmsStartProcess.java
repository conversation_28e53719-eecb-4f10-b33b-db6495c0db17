
package com.aliyun.xdragon.common.model.bpms;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class BpmsStartProcess extends BpmsBase{
    @SerializedName("content")
    @Expose
    private Content content;

    @Data
    public class Content {
        @SerializedName("processInstanceId")
        @Expose
        private String processInstanceId;
        @SerializedName("instStatus")
        @Expose
        private String instStatus;
        @SerializedName("processVersion")
        @Expose
        private String processVersion;
        @SerializedName("title")
        @Expose
        private String title;
        @SerializedName("dynamicProcess")
        @Expose
        private Boolean dynamicProcess;
        @SerializedName("titleEn")
        @Expose
        private String titleEn;
        @SerializedName("createTime")
        @Expose
        private String createTime;
        @SerializedName("processCode")
        @Expose
        private String processCode;
        @SerializedName("originatorId")
        @Expose
        private String originatorId;
    }

}
