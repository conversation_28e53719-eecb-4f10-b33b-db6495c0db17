package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/01/31
 */
@Data
public class DeleteSubTaskConfigsRequest extends BaseTaskRequest implements Serializable {
    private static final long serialVersionUID = -1064868598535439251L;
    /**
     * region config id list
     */
    @NotEmpty
    @Size(min = 1)
    List<Long> cids;
}
