package com.aliyun.xdragon.common.model.log.request;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/25
 */
@Data
public class BaseLogHealthRequest implements Serializable {
    private static final long serialVersionUID = 8909596084933738661L;
    /**
     * task id
     */
    @NotNull(message = "task is null")
    @Positive
    protected Long taskId;
    /**
     * 开始时间， 如果为null就统计endTime前的数据
     */
    protected String startTime;
    /**
     * 结束时间
     */
    @NotBlank
    protected String endTime;
}
