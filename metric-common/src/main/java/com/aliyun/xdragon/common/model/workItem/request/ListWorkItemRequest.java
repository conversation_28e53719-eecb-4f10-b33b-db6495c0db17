package com.aliyun.xdragon.common.model.workItem.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/10
 */

@Data
public class ListWorkItemRequest implements Serializable {
    private static final long serialVersionUID = 2604269549345915800L;

    /**
     * query start time, in ms
     */
    @NotNull
    Long startMs;

    /**
     * query end time, in ms
     */
    @NotNull
    Long endMs;

    /**
     * query start pos
     */
    @NotNull
    Integer start;

    /**
     * query page size
     */
    @NotNull
    Integer pageSize;

    /**
     * query sourceId
     */
    String sourceId;

    /**
     * query first labels
     */
    List<String> firstLabels;

    /**
     * query secondary labels
     */
    List<String> secondaryLabels;

    /**
     * query owners
     */
    List<String> owners;

    /**
     * query teams
     */
    List<String> teams;

    /**
     * query sources
     */
    List<String> sources;

    /**
     * query products
     */
    String product;

    /**
     * query improve action switch
     */
    Boolean actionSwitch;

    /**
     * query work item by keyword
     */
    List<String> keywords;

    /**
     * query work item joinStr or | and
     */
    String joinStr;

    public ListWorkItemRequest() {}

    public ListWorkItemRequest(
        Long startMs,
        Long endMs,
        Integer start,
        Integer pageSize,
        String sourceId,
        String product,
        List<String> sources,
        List<String> firstLabels,
        List<String> secondaryLabels,
        List<String> teams,
        List<String> owners,
        Boolean actionSwitch,
        List<String> keywords,
        String joinStr
    ) {
        this.startMs = startMs;
        this.endMs = endMs;
        this.start = start;
        this.pageSize = pageSize;
        this.sourceId = sourceId;
        this.firstLabels = firstLabels;
        this.secondaryLabels = secondaryLabels;
        this.teams = teams;
        this.owners = owners;
        this.sources = sources;
        this.product = product;
        this.actionSwitch = actionSwitch;
        this.keywords = keywords;
        this.joinStr = joinStr;
    }
}
