package com.aliyun.xdragon.common.model.metric.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/18
 */

@Data
public class PerformanceDiagnosisRequest implements Serializable {

    private static final long serialVersionUID = -2714181358692020515L;
    /**
     * 被诊断的实例id。必要参数
     */
    @NotNull
    private String instanceId;

    /**
     * 实例所在的NC的IP。非必要参数，如果不指定，则会自动调用API获取
     */
    private String ncIp;
    /**
     * 实例所在的CN。非必要参数，如果不指定，则会自动调用API获取
     */
    private List<Integer> cn;
    /**
     * 实例所在的socket。非必要参数，如果不指定，则会自动调用API获取
     */
    private List<Integer> socket;
    /**
     * 实例所在的的node。非必要参数，如果不指定，则会自动调用API获取
     */
    private List<Integer> node;
    /**
     * 实例所在的ccd。非必要参数，如果不指定，则会自动调用API获取
     */
    private List<Integer> ccd;
    /**
     * 实例所在的cpu Core。非必要参数，如果不指定，则会自动调用API获取
     */
    private List<Integer> core;
    /**
     * 实例的CPU型号。非必要参数，如果不指定，则会自动调用API获取
     */
    private String cpuModel;
    /**
     * 实例的NC上的CN数量。非必要参数，如果不指定，则会自动调用API获取
     */
    private Integer multiCnNum;
    /**
     * 与该实例在同一个CN上的所有邻居实例的id。非必要参数，如果不指定，则会自动调用API获取
     */
    private List<String> neighbors;
    /**
     * 与该实例在同一个socket上的所有邻居实例的id。非必要参数，如果不指定，则会自动调用API获取
     */
    private List<String> socketNeighbors;
    /**
     * 与该实例在同一个node上的所有邻居实例的id。非必要参数，如果不指定，则会自动调用API获取
     */
    private List<String> nodeNeighbors;
    /**
     * 与该实例在同一个ccd上的所有邻居实例的id。非必要参数，如果不指定，则会自动调用API获取
     */
    private List<String> ccdNeighbors;

    /**
     * 诊断场景列表。必要参数。支持的场景见Diamond的DIAGNOSIS_STRATEGY配置的scenarios列表下各元素的scenario字段
     */
    @NotNull
    @Size(max = 100)
    private List<String> scenarios;
    /**
     * 诊断开始时间（秒级时间戳）。必要参数
     */
    @NotNull
    @Positive
    private Integer startTs;
    /**
     * 诊断结束时间（秒级时间戳）。必要参数
     */
    @NotNull
    @Positive
    private Integer endTs;

}
