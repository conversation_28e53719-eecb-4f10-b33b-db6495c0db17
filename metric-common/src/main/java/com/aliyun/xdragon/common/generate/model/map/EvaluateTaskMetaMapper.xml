<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.model.map.EvaluateTaskMetaMapper">
  <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.model.EvaluateTaskMeta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="task_type" jdbcType="TINYINT" property="taskType" />
    <result column="task_status" jdbcType="TINYINT" property="taskStatus" />
    <result column="param" jdbcType="VARCHAR" property="param" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="cron" jdbcType="VARCHAR" property="cron" />
    <result column="run_lock" jdbcType="TINYINT" property="runLock" />
    <result column="run_lock_time" jdbcType="BIGINT" property="runLockTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    task_id, `name`, description, creator, task_type, task_status, param, gmt_create, 
    gmt_modified, cron, run_lock, run_lock_time
  </sql>
  <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateTaskMetaExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from evaluate_task_meta
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from evaluate_task_meta
    where task_id = #{taskId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from evaluate_task_meta
    where task_id = #{taskId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateTaskMetaExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from evaluate_task_meta
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateTaskMeta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="taskId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into evaluate_task_meta (`name`, description, creator, 
      task_type, task_status, param, 
      gmt_create, gmt_modified, cron, 
      run_lock, run_lock_time)
    values (#{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{taskType,jdbcType=TINYINT}, #{taskStatus,jdbcType=TINYINT}, #{param,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{cron,jdbcType=VARCHAR}, 
      #{runLock,jdbcType=TINYINT}, #{runLockTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateTaskMeta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="taskId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into evaluate_task_meta
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="param != null">
        param,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="cron != null">
        cron,
      </if>
      <if test="runLock != null">
        run_lock,
      </if>
      <if test="runLockTime != null">
        run_lock_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=TINYINT},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="param != null">
        #{param,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="cron != null">
        #{cron,jdbcType=VARCHAR},
      </if>
      <if test="runLock != null">
        #{runLock,jdbcType=TINYINT},
      </if>
      <if test="runLockTime != null">
        #{runLockTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateTaskMetaExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from evaluate_task_meta
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluate_task_meta
    <set>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=TINYINT},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=TINYINT},
      </if>
      <if test="record.param != null">
        param = #{record.param,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cron != null">
        cron = #{record.cron,jdbcType=VARCHAR},
      </if>
      <if test="record.runLock != null">
        run_lock = #{record.runLock,jdbcType=TINYINT},
      </if>
      <if test="record.runLockTime != null">
        run_lock_time = #{record.runLockTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluate_task_meta
    set task_id = #{record.taskId,jdbcType=BIGINT},
      `name` = #{record.name,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      task_type = #{record.taskType,jdbcType=TINYINT},
      task_status = #{record.taskStatus,jdbcType=TINYINT},
      param = #{record.param,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      cron = #{record.cron,jdbcType=VARCHAR},
      run_lock = #{record.runLock,jdbcType=TINYINT},
      run_lock_time = #{record.runLockTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateTaskMeta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluate_task_meta
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=TINYINT},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="param != null">
        param = #{param,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="cron != null">
        cron = #{cron,jdbcType=VARCHAR},
      </if>
      <if test="runLock != null">
        run_lock = #{runLock,jdbcType=TINYINT},
      </if>
      <if test="runLockTime != null">
        run_lock_time = #{runLockTime,jdbcType=BIGINT},
      </if>
    </set>
    where task_id = #{taskId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateTaskMeta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluate_task_meta
    set `name` = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=TINYINT},
      task_status = #{taskStatus,jdbcType=TINYINT},
      param = #{param,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      cron = #{cron,jdbcType=VARCHAR},
      run_lock = #{runLock,jdbcType=TINYINT},
      run_lock_time = #{runLockTime,jdbcType=BIGINT}
    where task_id = #{taskId,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateTaskMetaExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from evaluate_task_meta
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert" keyColumn="task_id" keyProperty="taskId" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into evaluate_task_meta
    (`name`, description, creator, task_type, task_status, param, gmt_create, gmt_modified, 
      cron, run_lock, run_lock_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.taskType,jdbcType=TINYINT}, #{item.taskStatus,jdbcType=TINYINT}, #{item.param,jdbcType=VARCHAR}, 
        #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.cron,jdbcType=VARCHAR}, 
        #{item.runLock,jdbcType=TINYINT}, #{item.runLockTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="task_id" keyProperty="list.taskId" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into evaluate_task_meta (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'task_type'.toString() == column.value">
          #{item.taskType,jdbcType=TINYINT}
        </if>
        <if test="'task_status'.toString() == column.value">
          #{item.taskStatus,jdbcType=TINYINT}
        </if>
        <if test="'param'.toString() == column.value">
          #{item.param,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_create'.toString() == column.value">
          #{item.gmtCreate,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'cron'.toString() == column.value">
          #{item.cron,jdbcType=VARCHAR}
        </if>
        <if test="'run_lock'.toString() == column.value">
          #{item.runLock,jdbcType=TINYINT}
        </if>
        <if test="'run_lock_time'.toString() == column.value">
          #{item.runLockTime,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>