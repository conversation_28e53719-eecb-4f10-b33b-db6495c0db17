<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliyun.xdragon.common.generate.model.map.EvaluateInstanceMapper">
  <resultMap id="BaseResultMap" type="com.aliyun.xdragon.common.generate.model.EvaluateInstance">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="batch_id" jdbcType="BIGINT" property="batchId" />
    <result column="task_time" jdbcType="BIGINT" property="taskTime" />
    <result column="run_status" jdbcType="TINYINT" property="runStatus" />
    <result column="optimal_action" jdbcType="VARCHAR" property="optimalAction" />
    <result column="power" jdbcType="DECIMAL" property="power" />
    <result column="recommend_percent" jdbcType="TINYINT" property="recommendPercent" />
    <result column="is_significant" jdbcType="TINYINT" property="isSignificant" />
    <result column="pair_compare_result" jdbcType="VARCHAR" property="pairCompareResult" />
    <result column="sample_size" jdbcType="VARCHAR" property="sampleSize" />
    <result column="sample_mean" jdbcType="VARCHAR" property="sampleMean" />
    <result column="statistics" jdbcType="VARCHAR" property="statistics" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="metric_name" jdbcType="VARCHAR" property="metricName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, task_id, batch_id, task_time, run_status, optimal_action, `power`, recommend_percent, 
    is_significant, pair_compare_result, sample_size, sample_mean, `statistics`, message, 
    gmt_create, gmt_modified, metric_name
  </sql>
  <select id="selectByExample" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateInstanceExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from evaluate_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from evaluate_instance
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from evaluate_instance
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateInstanceExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from evaluate_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateInstance">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into evaluate_instance (task_id, batch_id, task_time, 
      run_status, optimal_action, `power`, 
      recommend_percent, is_significant, pair_compare_result, 
      sample_size, sample_mean, `statistics`, 
      message, gmt_create, gmt_modified, 
      metric_name)
    values (#{taskId,jdbcType=BIGINT}, #{batchId,jdbcType=BIGINT}, #{taskTime,jdbcType=BIGINT}, 
      #{runStatus,jdbcType=TINYINT}, #{optimalAction,jdbcType=VARCHAR}, #{power,jdbcType=DECIMAL}, 
      #{recommendPercent,jdbcType=TINYINT}, #{isSignificant,jdbcType=TINYINT}, #{pairCompareResult,jdbcType=VARCHAR}, 
      #{sampleSize,jdbcType=VARCHAR}, #{sampleMean,jdbcType=VARCHAR}, #{statistics,jdbcType=VARCHAR}, 
      #{message,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{metricName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateInstance">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into evaluate_instance
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="taskTime != null">
        task_time,
      </if>
      <if test="runStatus != null">
        run_status,
      </if>
      <if test="optimalAction != null">
        optimal_action,
      </if>
      <if test="power != null">
        `power`,
      </if>
      <if test="recommendPercent != null">
        recommend_percent,
      </if>
      <if test="isSignificant != null">
        is_significant,
      </if>
      <if test="pairCompareResult != null">
        pair_compare_result,
      </if>
      <if test="sampleSize != null">
        sample_size,
      </if>
      <if test="sampleMean != null">
        sample_mean,
      </if>
      <if test="statistics != null">
        `statistics`,
      </if>
      <if test="message != null">
        message,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="metricName != null">
        metric_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="batchId != null">
        #{batchId,jdbcType=BIGINT},
      </if>
      <if test="taskTime != null">
        #{taskTime,jdbcType=BIGINT},
      </if>
      <if test="runStatus != null">
        #{runStatus,jdbcType=TINYINT},
      </if>
      <if test="optimalAction != null">
        #{optimalAction,jdbcType=VARCHAR},
      </if>
      <if test="power != null">
        #{power,jdbcType=DECIMAL},
      </if>
      <if test="recommendPercent != null">
        #{recommendPercent,jdbcType=TINYINT},
      </if>
      <if test="isSignificant != null">
        #{isSignificant,jdbcType=TINYINT},
      </if>
      <if test="pairCompareResult != null">
        #{pairCompareResult,jdbcType=VARCHAR},
      </if>
      <if test="sampleSize != null">
        #{sampleSize,jdbcType=VARCHAR},
      </if>
      <if test="sampleMean != null">
        #{sampleMean,jdbcType=VARCHAR},
      </if>
      <if test="statistics != null">
        #{statistics,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="metricName != null">
        #{metricName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateInstanceExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from evaluate_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluate_instance
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.batchId != null">
        batch_id = #{record.batchId,jdbcType=BIGINT},
      </if>
      <if test="record.taskTime != null">
        task_time = #{record.taskTime,jdbcType=BIGINT},
      </if>
      <if test="record.runStatus != null">
        run_status = #{record.runStatus,jdbcType=TINYINT},
      </if>
      <if test="record.optimalAction != null">
        optimal_action = #{record.optimalAction,jdbcType=VARCHAR},
      </if>
      <if test="record.power != null">
        `power` = #{record.power,jdbcType=DECIMAL},
      </if>
      <if test="record.recommendPercent != null">
        recommend_percent = #{record.recommendPercent,jdbcType=TINYINT},
      </if>
      <if test="record.isSignificant != null">
        is_significant = #{record.isSignificant,jdbcType=TINYINT},
      </if>
      <if test="record.pairCompareResult != null">
        pair_compare_result = #{record.pairCompareResult,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleSize != null">
        sample_size = #{record.sampleSize,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleMean != null">
        sample_mean = #{record.sampleMean,jdbcType=VARCHAR},
      </if>
      <if test="record.statistics != null">
        `statistics` = #{record.statistics,jdbcType=VARCHAR},
      </if>
      <if test="record.message != null">
        message = #{record.message,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.metricName != null">
        metric_name = #{record.metricName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluate_instance
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      batch_id = #{record.batchId,jdbcType=BIGINT},
      task_time = #{record.taskTime,jdbcType=BIGINT},
      run_status = #{record.runStatus,jdbcType=TINYINT},
      optimal_action = #{record.optimalAction,jdbcType=VARCHAR},
      `power` = #{record.power,jdbcType=DECIMAL},
      recommend_percent = #{record.recommendPercent,jdbcType=TINYINT},
      is_significant = #{record.isSignificant,jdbcType=TINYINT},
      pair_compare_result = #{record.pairCompareResult,jdbcType=VARCHAR},
      sample_size = #{record.sampleSize,jdbcType=VARCHAR},
      sample_mean = #{record.sampleMean,jdbcType=VARCHAR},
      `statistics` = #{record.statistics,jdbcType=VARCHAR},
      message = #{record.message,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      metric_name = #{record.metricName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateInstance">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluate_instance
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="batchId != null">
        batch_id = #{batchId,jdbcType=BIGINT},
      </if>
      <if test="taskTime != null">
        task_time = #{taskTime,jdbcType=BIGINT},
      </if>
      <if test="runStatus != null">
        run_status = #{runStatus,jdbcType=TINYINT},
      </if>
      <if test="optimalAction != null">
        optimal_action = #{optimalAction,jdbcType=VARCHAR},
      </if>
      <if test="power != null">
        `power` = #{power,jdbcType=DECIMAL},
      </if>
      <if test="recommendPercent != null">
        recommend_percent = #{recommendPercent,jdbcType=TINYINT},
      </if>
      <if test="isSignificant != null">
        is_significant = #{isSignificant,jdbcType=TINYINT},
      </if>
      <if test="pairCompareResult != null">
        pair_compare_result = #{pairCompareResult,jdbcType=VARCHAR},
      </if>
      <if test="sampleSize != null">
        sample_size = #{sampleSize,jdbcType=VARCHAR},
      </if>
      <if test="sampleMean != null">
        sample_mean = #{sampleMean,jdbcType=VARCHAR},
      </if>
      <if test="statistics != null">
        `statistics` = #{statistics,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="metricName != null">
        metric_name = #{metricName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateInstance">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update evaluate_instance
    set task_id = #{taskId,jdbcType=BIGINT},
      batch_id = #{batchId,jdbcType=BIGINT},
      task_time = #{taskTime,jdbcType=BIGINT},
      run_status = #{runStatus,jdbcType=TINYINT},
      optimal_action = #{optimalAction,jdbcType=VARCHAR},
      `power` = #{power,jdbcType=DECIMAL},
      recommend_percent = #{recommendPercent,jdbcType=TINYINT},
      is_significant = #{isSignificant,jdbcType=TINYINT},
      pair_compare_result = #{pairCompareResult,jdbcType=VARCHAR},
      sample_size = #{sampleSize,jdbcType=VARCHAR},
      sample_mean = #{sampleMean,jdbcType=VARCHAR},
      `statistics` = #{statistics,jdbcType=VARCHAR},
      message = #{message,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      metric_name = #{metricName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.aliyun.xdragon.common.generate.model.EvaluateInstanceExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from evaluate_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into evaluate_instance
    (task_id, batch_id, task_time, run_status, optimal_action, `power`, recommend_percent, 
      is_significant, pair_compare_result, sample_size, sample_mean, `statistics`, message, 
      gmt_create, gmt_modified, metric_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.taskId,jdbcType=BIGINT}, #{item.batchId,jdbcType=BIGINT}, #{item.taskTime,jdbcType=BIGINT}, 
        #{item.runStatus,jdbcType=TINYINT}, #{item.optimalAction,jdbcType=VARCHAR}, #{item.power,jdbcType=DECIMAL}, 
        #{item.recommendPercent,jdbcType=TINYINT}, #{item.isSignificant,jdbcType=TINYINT}, 
        #{item.pairCompareResult,jdbcType=VARCHAR}, #{item.sampleSize,jdbcType=VARCHAR}, 
        #{item.sampleMean,jdbcType=VARCHAR}, #{item.statistics,jdbcType=VARCHAR}, #{item.message,jdbcType=VARCHAR}, 
        #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.metricName,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into evaluate_instance (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'task_id'.toString() == column.value">
          #{item.taskId,jdbcType=BIGINT}
        </if>
        <if test="'batch_id'.toString() == column.value">
          #{item.batchId,jdbcType=BIGINT}
        </if>
        <if test="'task_time'.toString() == column.value">
          #{item.taskTime,jdbcType=BIGINT}
        </if>
        <if test="'run_status'.toString() == column.value">
          #{item.runStatus,jdbcType=TINYINT}
        </if>
        <if test="'optimal_action'.toString() == column.value">
          #{item.optimalAction,jdbcType=VARCHAR}
        </if>
        <if test="'power'.toString() == column.value">
          #{item.power,jdbcType=DECIMAL}
        </if>
        <if test="'recommend_percent'.toString() == column.value">
          #{item.recommendPercent,jdbcType=TINYINT}
        </if>
        <if test="'is_significant'.toString() == column.value">
          #{item.isSignificant,jdbcType=TINYINT}
        </if>
        <if test="'pair_compare_result'.toString() == column.value">
          #{item.pairCompareResult,jdbcType=VARCHAR}
        </if>
        <if test="'sample_size'.toString() == column.value">
          #{item.sampleSize,jdbcType=VARCHAR}
        </if>
        <if test="'sample_mean'.toString() == column.value">
          #{item.sampleMean,jdbcType=VARCHAR}
        </if>
        <if test="'statistics'.toString() == column.value">
          #{item.statistics,jdbcType=VARCHAR}
        </if>
        <if test="'message'.toString() == column.value">
          #{item.message,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_create'.toString() == column.value">
          #{item.gmtCreate,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'metric_name'.toString() == column.value">
          #{item.metricName,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>