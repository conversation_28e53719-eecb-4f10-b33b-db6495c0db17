package com.aliyun.xdragon.common.model.log;

import java.util.Map;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/06/09
 */
@Data
public class DelayConfig {
    /**
     * task id to not consumer count
     */
    private Map<Long, Integer> tids;

    /**
     * region cid to not consumer count
     */
    private Map<Long, Integer> cids;

    public Integer getDelayCountByCid(Long cid) {
        if (cids != null && cids.containsKey(cid)) {
            return cids.get(cid);
        } else {
            return null;
        }
    }

    public Integer getDelayCountByTid(Long tid) {
        if (tids != null && tids.containsKey(tid)) {
            return tids.get(tid);
        } else {
            return null;
        }
    }
}
