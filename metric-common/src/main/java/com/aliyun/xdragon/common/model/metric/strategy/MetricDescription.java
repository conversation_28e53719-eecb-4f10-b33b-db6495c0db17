package com.aliyun.xdragon.common.model.metric.strategy;

import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/14
 */
@Data
public class MetricDescription {

    private String type;

    private String metric;

    private List<String> detailMetrics;

    private int interval;

    private int startOffset;

    private int endOffset;

    private double threshold;

    private String displayName;

    private Map<String, String> comparisonParams;

    public MetricDescription() {
    }

    public MetricDescription(String metric, int interval, String type, double threshold) {
        this.metric = metric;
        this.interval = interval;
        this.type = type;
        this.threshold = threshold;
    }

    public MetricDescription(MetricDescription another){
        this.type = another.getType();
        this.metric = another.getMetric();
        this.detailMetrics = another.getDetailMetrics();
        this.interval = another.getInterval();
        this.startOffset = another.getStartOffset();
        this.endOffset = another.getEndOffset();
        this.threshold = another.getThreshold();
        this.displayName = another.getDisplayName();
        this.comparisonParams = another.getComparisonParams();
    }
}
