package com.aliyun.xdragon.common.model.keyMetric.response;

import com.aliyun.xdragon.common.model.keyMetric.ComponentMonitorValueParam;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ComponentMonitorValueResponse implements Serializable {

    private static final long serialVersionUID = -5280620341091258989L;

    List<ComponentMonitorValueParam> componentMonitors;
    
    public ComponentMonitorValueResponse(List<ComponentMonitorValueParam> componentMonitors) {
        this.componentMonitors = componentMonitors;
    }
}
