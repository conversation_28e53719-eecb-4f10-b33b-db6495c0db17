package com.aliyun.xdragon.common.model.log.response;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/02/23
 */
@Data
public class MatchPatternResponse implements Serializable {
    private static final long serialVersionUID = -853498874768799357L;
    /**
     * task id
     */
    Long taskId;

    /**
     * log cluster pattern
     */
    String pattern;

    /**
     * pattern md5
     */
    String md5;

    /**
     * log cluster check start time in ms
     */
    Long checkStartMs;

    /**
     * log cluster check end time in ms
     */
    Long checkEndMs;

    /**
     * pattern appear start time in ms
     */
    Long startMs;

    /**
     * pattern appear end time in ms
     */
    Long endMs;

    /**
     * total cnt in check time range
     */
    Long cnt;
}
