package com.aliyun.xdragon.common.model.log;

import com.google.gson.Gson;
import org.junit.Assert;
import org.junit.Test;

public class TaskStartParamTest {
    @Test
    public void testBasic() {
        TaskStartParam param = new TaskStartParam();
        param.setStartTsMs(111111L);
        param.setEndTsMs(222222L);
        Assert.assertEquals(Long.valueOf(111111L), param.getStartTsMs());
        Assert.assertEquals(Long.valueOf(222222L), param.getEndTsMs());
        Assert.assertTrue(param.toString().contains("common.model.log.TaskStartParam"));
        Assert.assertTrue(param.toString().contains("[startTsMs=111111,endTsMs=222222]"));
    }

    @Test
    public void testCopy() {
        TaskStartParam param = new TaskStartParam();
        param.setStartTsMs(111111L);
        param.setEndTsMs(222222L);
        TaskStartParam copy = param.copy();
        Assert.assertEquals(Long.valueOf(111111L), copy.getStartTsMs());
        Assert.assertEquals(Long.valueOf(222222L), copy.getEndTsMs());
        Assert.assertNotSame(param, copy);
    }

    @Test
    public void testSerialize() {
        TaskStartParam param = new TaskStartParam();
        param.setStartTsMs(111111L);
        param.setEndTsMs(222222L);
        Gson GSON = new Gson();
        String json = GSON.toJson(param);
        Assert.assertEquals("{\"startTsMs\":111111,\"endTsMs\":222222}", json);

        TaskStartParam fromJson = GSON.fromJson("{\"startTsMs\":1704780900000}", TaskStartParam.class);
        Assert.assertEquals(Long.valueOf(1704780900000L), fromJson.getStartTsMs());
        Assert.assertNull(fromJson.getEndTsMs());
    }
}