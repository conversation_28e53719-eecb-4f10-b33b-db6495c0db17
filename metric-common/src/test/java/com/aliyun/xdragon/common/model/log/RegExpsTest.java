package com.aliyun.xdragon.common.model.log;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.Assert;
import org.junit.Test;

public class RegExpsTest {
    @Test
    public void testToIndicesStr() {
        RegExps exps = new RegExps();
        List<String> dummy = Collections.emptyList();
        exps.setIp(dummy);
        exps.setNumber(dummy);
        exps.setUrl(dummy);
        exps.setCustomer(dummy);
        String indicesStr = exps.toIndicesStr();
        Assert.assertNotNull(indicesStr);
        Assert.assertEquals("1", indicesStr);

        exps.setCustomer(Arrays.asList("1;2;3;4"));
        indicesStr = exps.toIndicesStr();
        Assert.assertEquals("1;2;3;4", indicesStr);

        dummy = Arrays.asList("");
        exps.setIp(dummy);
        exps.setNumber(dummy);
        exps.setUrl(dummy);
        indicesStr = exps.toIndicesStr();
        Assert.assertEquals("1;2;3;4", indicesStr);
    }
}
