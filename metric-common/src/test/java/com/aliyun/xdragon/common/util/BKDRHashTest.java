package com.aliyun.xdragon.common.util;

import org.junit.Assert;
import org.junit.Test;

public class BKDRHashTest {
    @Test
    public void testBKDRHashInCase() {
        Assert.assertEquals(Integer.valueOf(0), BKDRHash.hash(""));

        String str1 = "The quick brown fox jumps over the lazy dog";
        String str2 = "THE QUICK BROWN FOR JUMPS OVER THE LAZY DOG";
        String str3 = "the quick brown fox jumps over the lazy dog";
        Assert.assertEquals(Integer.valueOf(1159206503), BKDRHash.hash(str1));
        Assert.assertEquals(Integer.valueOf(1143752481), BKDRHash.hash(str2));
        Assert.assertEquals(Integer.valueOf(619256711), BKDRHash.hash(str3));
    }

    @Test
    public void testBKDRHashWithSameStr() {
        String str1 = new String("Hello world!");
        String str2 = new String("Hello world!");
        Assert.assertNotSame(str1, str2);
        Assert.assertTrue(str1.equals(str2) && str2.equals(str1));
        Assert.assertEquals(BKDRHash.hash(str1), BKDRHash.hash(str2));

        String str3 = new String("Hello world.");
        Assert.assertNotEquals(BKDRHash.hash(str1), BKDRHash.hash(str3));
    }
}
