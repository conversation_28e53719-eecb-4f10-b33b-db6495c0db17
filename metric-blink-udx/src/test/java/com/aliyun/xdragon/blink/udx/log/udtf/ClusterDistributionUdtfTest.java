package com.aliyun.xdragon.blink.udx.log.udtf;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import com.aliyun.xdragon.algorithm.log.model.entities.ClusterInfo;
import com.google.gson.Gson;
import org.apache.flink.api.common.accumulators.DoubleCounter;
import org.apache.flink.api.common.accumulators.Histogram;
import org.apache.flink.api.common.accumulators.IntCounter;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.metrics.MetricGroup;
import org.apache.flink.table.functions.FunctionContext;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR>
 */
public class ClusterDistributionUdtfTest {
    private static final Gson gson = new Gson();

    /* Dummy class for FunctionContext implement */
    class DummyFunctionContext implements FunctionContext {
        public MetricGroup getMetricGroup() {
            return null;
        }

        public File getCachedFile(String var1) {
            return null;
        }

        public int getNumberOfParallelSubtasks() {
            return 0;
        }

        public int getIndexOfThisSubtask() {
            return 0;
        }

        public int getMaxNumberOfParallelSubtasks() {
            return 0;
        }

        public IntCounter getIntCounter(String var1) {
            return null;
        }

        public LongCounter getLongCounter(String var1) {
            return null;
        }

        public DoubleCounter getDoubleCounter(String var1) {
            return null;
        }

        public Histogram getHistogram(String var1) {
            return null;
        }

        public String getJobParameter(String var1, String var2) {
            return var2;
        }
    }

    private ClusterDistributionUdtf clusterDistributionUdtf;

    @Before
    public void initClusterFlatMapUdtf() {
        clusterDistributionUdtf = new ClusterDistributionUdtf();
    }

    @Test
    public void testOpen() throws Exception {
        DummyFunctionContext context = new DummyFunctionContext();
        Assert.assertNotNull(clusterDistributionUdtf);
        clusterDistributionUdtf.open(context);
    }

    @Test
    public void testEval() {
        List<String> clusterList = new ArrayList<>(2);
        ClusterInfo info = new ClusterInfo();
        clusterList.add(gson.toJson(info));

        Assert.assertNotNull(clusterDistributionUdtf);
        clusterDistributionUdtf.eval(clusterList);
    }
}
