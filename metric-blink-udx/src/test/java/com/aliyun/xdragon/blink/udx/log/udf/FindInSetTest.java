package com.aliyun.xdragon.blink.udx.log.udf;

import com.aliyun.xdragon.blink.udx.log.udf.FindInSet;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2023/09/05
 */
public class FindInSetTest {
    @Test
    public void eval() throws Exception {
        FindInSet findInSet = new FindInSet();

        boolean status = findInSet.eval("", "a,b,c");
        Assert.assertEquals(false, status);

        status = findInSet.eval("a", "a,b,c");
        Assert.assertEquals(true, status);

        status = findInSet.eval("d", "a,b,c");
        Assert.assertEquals(false, status);

        status = findInSet.eval("d", "*");
        Assert.assertEquals(true, status);
    }
}
