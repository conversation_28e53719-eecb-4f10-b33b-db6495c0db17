package com.aliyun.xdragon.blink.udx.log.udaf;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.aliyun.xdragon.algorithm.log.model.entities.ClusterInfo;
import com.aliyun.xdragon.algorithm.log.config.ClusterConfig;
import com.aliyun.xdragon.algorithm.log.model.LogCluster;
import com.aliyun.xdragon.algorithm.log.model.entities.CandidateEntity;
import com.aliyun.xdragon.algorithm.log.model.utils.MaskingInstruction;
import com.aliyun.xdragon.common.util.RegexConsts;
import com.google.gson.Gson;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class LogClusterUdaf extends AggregateFunction<List<String>, List<List<String>>> {
    private static final Logger logger = LoggerFactory.getLogger(LogClusterUdaf.class);
    private static final Gson GSON = new Gson();

    private int support;
    private int rsupport;
    private int cutLines;
    private int cutLength;
    private double weightThreshold;
    private String maskPrefix;
    private String maskSuffix;
    private String indicesStr;
    private String maskStr;

    @Override
    public void open(FunctionContext context) throws Exception {
        support = Integer.parseInt(context.getJobParameter("cluster.support", "3"));
        rsupport = Integer.parseInt(context.getJobParameter("cluster.rsupport", "3"));
        cutLines = Integer.parseInt(context.getJobParameter("cluster.cutLines", "5"));
        cutLength = Integer.parseInt(context.getJobParameter("cluster.cutLength", "30"));
        weightThreshold = Double.parseDouble(context.getJobParameter("cluster.weightThreshold", "0.04"));
        maskPrefix = context.getJobParameter("cluster.maskPrefix", "");
        maskSuffix = context.getJobParameter("cluster.maskSuffix", "");
        indicesStr = context.getJobParameter("cluster.indicesStr", "0");
        maskStr = context.getJobParameter("cluster.maskStr", " REGEX ");
    }

    @Override
    public List<List<String>> createAccumulator() {
        return new LinkedList<List<String>>();
    }

    @Override
    public List<String> getValue(List<List<String>> accumulator) {
        MaskingInstruction[] maskingInstructions = getMaskingInstructions(indicesStr, maskStr);

        ClusterConfig clusterConfig = new ClusterConfig(
            support, rsupport, cutLines, cutLength, weightThreshold, maskPrefix, maskSuffix,
            maskingInstructions,
            new String[] {"[", "]", ",", ":", "：", "，", "=", "!", "\"", "u'", "'", "{", "}", "#", "(", ")", "&", "<",
                ">", "\t"}
        );

        LogCluster logCluster = new LogCluster(clusterConfig);
        Map<Integer, CandidateEntity> clusterDict = logCluster.run(accumulator);
        logger.info("[UDAF getValue]Get {} log patterns", clusterDict.size());
        return getClusterInfoStrList(clusterDict);
    }

    public void accumulate(List<List<String>> accumulator, String value, String... exts) {
        List<String> log = new ArrayList<>(exts.length + 1);
        log.add(value);
        for (int i = 0; i < exts.length; ++i) {
            log.add(exts[i]);
        }
        accumulator.add(log);
    }

    public void merge(List<List<String>> accumulator, Iterable<List<List<String>>> its) {
        for (List<List<String>> strList : its) {
            accumulator.addAll(strList);
        }
    }

    protected MaskingInstruction[] getMaskingInstructions(String indicesStr, String maskStr) {
        Set<Integer> indices = splitIndicesStr(indicesStr);
        String[] patterns = RegexConsts.getRegexPatterns(indices);

        List<MaskingInstruction> maskingInstructionList = new ArrayList(patterns.length);
        for (int i = 0; i < patterns.length; ++i) {
            maskingInstructionList.add(new MaskingInstruction(patterns[i], maskStr));
        }

        MaskingInstruction[] maskingInstructions = new MaskingInstruction[maskingInstructionList.size()];
        maskingInstructionList.toArray(maskingInstructions);
        return maskingInstructions;
    }

    protected Set<Integer> splitIndicesStr(String indicesStr) {
        if (indicesStr == null || indicesStr.isEmpty()) {
            return Collections.EMPTY_SET;
        }
        String[] indicesArray = indicesStr.split(";");
        Set<Integer> indices = new HashSet<>(indicesArray.length);

        Integer index = Integer.valueOf(-1);
        for (String indexStr : indicesArray) {
            try {
                index = Integer.valueOf(indexStr);
            } catch (NumberFormatException e) {
                continue;
            }
            indices.add(index);
        }

        return indices;
    }

    private List<String> getClusterInfoStrList(Map<Integer, CandidateEntity> clusterDict) {
        if (clusterDict == null || clusterDict.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> clusterInfoStrList = new ArrayList<>(clusterDict.size());
        for (Map.Entry<Integer, CandidateEntity> entry : clusterDict.entrySet()) {
            ClusterInfo clusterInfo = new ClusterInfo();
            clusterInfo.setKey(entry.getKey());
            CandidateEntity cluster = entry.getValue();
            clusterInfo.setSupport(cluster.getSupport());
            clusterInfo.setPattern(String.join(" ", cluster.generatePattern()));
            clusterInfo.setExt(cluster.getExtensionInfoStr());
            clusterInfoStrList.add(GSON.toJson(clusterInfo));
        }
        return clusterInfoStrList;
    }
}
