package com.aliyun.xdragon.service.common.repository;

import java.util.LinkedList;
import java.util.List;

import com.aliyun.xdragon.common.generate.model.HostStaticInfo;
import com.aliyun.xdragon.common.generate.model.HostStaticInfoExample;
import com.aliyun.xdragon.common.generate.model.map.HostStaticInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022/07/27
 */
@Repository
public class HostInfoDao {
    @Autowired
    private HostStaticInfoMapper mapper;

    //public HostStaticInfo getNcByIp(String ncIp) {
    //    HostStaticInfoExample e = new HostStaticInfoExample();
    //    HostStaticInfoExample.Criteria c = e.createCriteria();
    //    c.andIpEqualTo(ncIp);
    //    List<HostStaticInfo> hosts = mapper.selectByExample(e);
    //    if (hosts.size() == 0) {
    //        return null;
    //    } else {
    //        return hosts.get(0);
    //    }
    //}

    public List<HostStaticInfo> getNcByIps(List<String> ips) {
        if (ips == null || ips.size() == 0) {
            return new LinkedList<>();
        } else {
            HostStaticInfoExample e = new HostStaticInfoExample();
            HostStaticInfoExample.Criteria c = e.createCriteria();
            c.andIpIn(ips);
            return mapper.selectByExample(e);
        }
    }

    public void addHost(HostStaticInfo host) {
        mapper.insertSelective(host);
    }

}
