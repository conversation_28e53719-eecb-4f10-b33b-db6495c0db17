package com.aliyun.xdragon.service.common.service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.aliyun.odps.Column;
import com.aliyun.odps.data.Record;
import com.aliyun.xdragon.common.enumeration.AllinoneRiskStatus;
import com.aliyun.xdragon.common.enumeration.AllinoneRiskType;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRisk;
import com.aliyun.xdragon.service.common.agent.OdpsClient;
import com.aliyun.xdragon.service.common.util.Checks;
import com.aliyun.xdragon.service.common.util.DateUtil;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.aliyun.xdragon.service.common.BizConsts.COMMA;
import static java.lang.Thread.sleep;

@Service
public class AllinoneRiskOdpsService {
    public static final Logger logger = org.slf4j.LoggerFactory.getLogger(AllinoneRiskOdpsService.class);

    @Autowired
    public OdpsClient odpsClient;

    public static final int ODPS_PAGE_SIZE = 1000;

    public static final List<String> selectNcIpKeys= new ArrayList<>(Arrays.asList(
        "ip as nc_ip", "nc_id", "sn as nc_sn", "host_name as hostname", "region_alias as region", "cluster_alias as cluster",
        "cluster_usage", "az_name as azone", "idc", "room", "rack", "asw_id", "physical_model", "vcpu_mod", "cpu_model",
        "product_name", "avs_version", "nc_category", "virt_type", "river_type", "network_type", "storage_type",
        "storage_network_type", "storage_type", "cpu_generation", "nc_total_vcpu as nc_vcpu",
        "date_outwarranty", "date_purchase", "manufacturer", "gmt_create as nc_create_time"));

    public static final List<String> selectVmIdKeys= new ArrayList<>(Arrays.asList(
        "instance_id", "ali_uid", "gmt_created as instance_create_time", "nc_ip", "nc_id", "gc_level",
        "vcpu_count as vcpu", "instance_type_name as instance_type", "instance_type_family", "image_no",
        "internet_ip_type", "is_no_charge_for_stop", "is_vpc", "ecs_category_1", "ecs_category_2",
        "ecs_category_3","os_type"));

    public Map<String, Map<String, String>> getNcInfo(String startDs, String endDs, List<String> ncIps)
        throws InterruptedException {
        if (Checks.nullOrEmpty(ncIps)) {
            return new HashMap<>();
        }
        final AtomicInteger counter = new AtomicInteger();
        List<List<String>> ncIpsChunks = new ArrayList<>(ncIps.stream()
            .collect(Collectors.groupingBy(it -> counter.getAndIncrement() / ODPS_PAGE_SIZE)).values());
        Map<String, Map<String, String>> ncInfoMap = new HashMap<>();
        for (List<String> ncIpsChunk : ncIpsChunks) {
            int retryCount = 3;
            while (retryCount-- > 0) {
                try {
                    String sql = "SELECT DISTINCT %s\n" +
                        "FROM   ecs_dw.nc_full_table t\n" +
                        "WHERE  ds between %s and %s and state = 'working_online' and ip in (%s)\n" +
                        "ORDER BY gmt_create DESC;";
                    List<Record> records = odpsClient.runSql(String.format(sql, String.join(COMMA, selectNcIpKeys),
                        startDs, endDs,
                        ncIpsChunk.stream().map(it -> "'" + it + "'").collect(Collectors.joining(COMMA))));
                    for (Record record : records) {
                        HashMap<String, String> ncInfo = new HashMap<>();
                        for (Column column : record.getColumns()) {
                            ncInfo.put(column.getName(), record.getString(column.getName()));
                        }
                        ncInfoMap.put(record.getString("nc_ip"), ncInfo);
                    }
                    break;
                } catch (Exception e) {
                    logger.error("VmNcInfoService get nc info error, retryCount: {}, error message: ", retryCount, e);
                }
                sleep(5000);
            }
        }
        for (String ncIp : ncIps) {
            if (!ncInfoMap.containsKey(ncIp)) {
                ncInfoMap.put(ncIp, new HashMap<>());
            }
        }

        return ncInfoMap;
    }

    public Map<String, Map<String, String>> getVmInfo(String startDs, String endDs, List<String> vmIds)
        throws InterruptedException {
        final AtomicInteger counter = new AtomicInteger();
        List<List<String>> ncIdsChunks = new ArrayList<>(vmIds.stream()
            .collect(Collectors.groupingBy(it -> counter.getAndIncrement() / ODPS_PAGE_SIZE)).values());
        Map<String, Map<String, String>> vmInfoMap = new HashMap<>();
        for (List<String> ncIpsChunk : ncIdsChunks) {
            int retryCount = 3;
            while (retryCount-- > 0) {
                try {
                    String sql = "SELECT %s\n" +
                        "FROM   ecs_dw.vm_full_table t\n" +
                        "WHERE  ds BETWEEN %s and %s and instance_id in (%s);";
                    List<Record> records = odpsClient.runSql(String.format(sql, String.join(COMMA, selectVmIdKeys),
                        startDs, endDs, ncIpsChunk.stream().map(it -> "'" + it + "'").collect(Collectors.joining(COMMA))));
                    for (Record record : records) {
                        HashMap<String, String> vmInfo = new HashMap<>();
                        for (Column column : record.getColumns()) {
                            vmInfo.put(column.getName(), record.getString(column.getName()));
                        }
                        vmInfoMap.put(record.getString("instance_id"), vmInfo);
                    }
                    break;
                } catch (Exception e) {
                    logger.error("VmNcInfoService get vm info error, retryCount: {}, error message: ", retryCount, e);
                }
                sleep(5000);
            }
        }
        for (String vmId : vmIds) {
            if (!vmInfoMap.containsKey(vmId)) {
                vmInfoMap.put(vmId, new HashMap<>());
            }
        }

        return vmInfoMap;
    }

    public List<AllinoneRisk> getReleaseRisks(String ds) throws ParseException {
        List<AllinoneRisk> risks = new ArrayList<>();

        // 每次返回1000条
        String sql = "SELECT service_name, exception_name, first_exception_time, alert_name, task_id, id, comment, "
            + "tag_time, operator, resource_type, alert_time, reason \n" +
            "     FROM   ecs_dw.ecs_release_plan_linkage_result_comment t\n" +
            "     WHERE  ds = %s and exception_name != 'canary_deploy_failed' and impact_flag = 1 and "
            + "log_source in ('tianji', 'tianji_vm') and reason in ('ComponentBug', 'DeployScriptIssue') "
            + "and tag_time >= '%s' \n" +
            "     LIMIT %s,%s;";
        String minTimeStr;
        Date minTime = DateUtil.genDateFromStr(ds, "yyyyMMdd");
        minTimeStr = DateUtil.dateFormat(minTime, "yyyy-MM-dd 00:00:00");

        int pageNum = 0;
        List<Record> records = odpsClient.runSql(String.format(sql, ds, minTimeStr, 0, ODPS_PAGE_SIZE));
        while (!Checks.nullOrEmpty(records)) {
            for (Record record: records) {
                try {
                    AllinoneRisk risk = new AllinoneRisk();
                    risk.setRiskName(record.getString("alert_name"));
                    risk.setRiskType(AllinoneRiskType.DEPLOY.getName());
                    risk.setSourceId(record.getString("id"));
                    risk.setSourceType(record.getString("reason"));
                    risk.setExceptionName(record.getString("exception_name"));
                    risk.setServerRole(record.getString("service_name"));
                    risk.setDeployTaskId(record.getString("task_id"));
                    risk.setGmtStart(DateUtil.genDateFromStr(record.getString("first_exception_time")));
                    risk.setGmtEnd(DateUtil.genDateFromStr(record.getString("alert_time")));
                    risk.setStatus(AllinoneRiskStatus.FINISHED.getName());
                    risk.setReason(record.getString("comment"));
                    risk.setComments(record.getString("comment"));
                    risks.add(risk);
                } catch (Exception e) {
                    logger.error("release plan to risk job get risk from odps failed", e);
                }
            }
            if (records.size() < ODPS_PAGE_SIZE) {
                break;
            }
            pageNum += 1;
            records = odpsClient.runSql(String.format(sql, ds, minTimeStr, pageNum * ODPS_PAGE_SIZE, ODPS_PAGE_SIZE));
        }
        return risks;
    }

    private List<Record> getReleaseNcDetailFromOdps(String ds, AllinoneRisk risk, int pageNum) {
        // 每次返回1000条
        String sql = "SELECT task_id, deploy_time, exception_name, exception_time, exception_time as recover_time, "
            + "vm_name as instance_id, nc_ip, cluster, iz, region, ali_uid\n" +
            "     FROM   ecs_dw.ecs_release_plan_linkage_result t\n" +
            "     WHERE  ds = %s and resource_type = 'nc' and task_id = '%s' and exception_name = '%s'\n" +
            "     LIMIT %s,%s;";
        return odpsClient.runSql(String.format(sql, ds, risk.getDeployTaskId(), risk.getExceptionName(),
            pageNum * ODPS_PAGE_SIZE, ODPS_PAGE_SIZE));
    }

    private List<Record> getReleaseVmDetailFromOdps(String ds, AllinoneRisk risk, int pageNum) {
        // 每次返回1000条
        String sql = "SELECT task_id, deploy_time, exception_name, exception_time, vm_name as instance_id, nc_ip, cluster, iz, region, ali_uid\n" +
            "     FROM   ecs_dw.ecs_release_plan_linkage_result t\n" +
            "     WHERE  ds = %s and resource_type = 'vm' and task_id = '%s' and exception_name = '%s'\n" +
            "     LIMIT %s,%s;";
        return odpsClient.runSql(String.format(sql, ds, risk.getDeployTaskId(), risk.getExceptionName(),
            pageNum * ODPS_PAGE_SIZE, ODPS_PAGE_SIZE));
    }

    public Map<String, Map<String, String>> getReleaseRiskDetailInfo(String ds, AllinoneRisk risk, String targetType) {
        int detailPageNum = 0;
        List<Record> detailRecords = new ArrayList<>();
        if (targetType.equals("instance_id")) {
            detailRecords = getReleaseVmDetailFromOdps(ds, risk, detailPageNum);
        }
        if (targetType.equals("nc_ip")) {
            detailRecords = getReleaseNcDetailFromOdps(ds, risk, detailPageNum);
        }

        Map<String, Map<String, String>> detailInfo = new HashMap<>();
        while (!detailRecords.isEmpty()) {
            for (Record detailRecord : detailRecords) {
                HashMap<String, String> innerInfo = new HashMap<>();
                for (Column column : detailRecord.getColumns()) {
                    innerInfo.put(column.getName(), detailRecord.getString(column.getName()));
                }
                detailInfo.put(detailRecord.getString(targetType), innerInfo);
            }
            detailPageNum++;

            detailRecords.clear();
            if (targetType.equals("instance_id")) {
                detailRecords = getReleaseVmDetailFromOdps(ds, risk, detailPageNum);
            }
            if (targetType.equals("nc_ip")) {
                detailRecords = getReleaseNcDetailFromOdps(ds, risk, detailPageNum);
            }
        }
        return detailInfo;
    }
}
