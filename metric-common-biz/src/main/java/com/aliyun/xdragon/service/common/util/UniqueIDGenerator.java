package com.aliyun.xdragon.service.common.util;

import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/07/18
 */
public class UniqueIDGenerator {
    public static String genUniqueId() {
        UUID id = UUID.randomUUID();
        return id.toString().replaceAll("-", "");
    }

    public static String genUniqueId(int maxLen) {
        String idStr = genUniqueId();
        return genUniqueId().substring(0, Math.min(idStr.length(), maxLen));
    }
}
