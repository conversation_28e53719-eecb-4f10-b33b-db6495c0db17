package com.aliyun.xdragon.service.common.service.impl;

import java.util.List;
import java.util.Map;

import com.aliyun.ecs.devopsapi.domain.xam.DevOpsDeleteRouterParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsQueryRouterParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsRouterParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsXamPreviewTemplateParam;
import com.aliyun.ecs.devopsapi.model.xam.DevOpsRouterModel;
import com.aliyun.ecs.devopsapi.model.xam.result.DevOpsXamResult;
import com.aliyun.ecs.devopsapi.service.XDragonAlertManagerService;
import com.aliyun.phoenix.api.common.response.PlainResult;
import com.aliyun.xdragon.common.annotation.ServerLog;
import com.aliyun.xdragon.service.common.service.DevOpsApiService;
import com.aliyun.xdragon.service.common.service.XdragonAlertService;
import com.aliyuncs.ecsops.model.v20160401.OpsXDragonEndTransactionalSendAlertRequest;
import com.aliyuncs.ecsops.model.v20160401.OpsXDragonEndTransactionalSendAlertResponse;
import com.aliyuncs.ecsops.model.v20160401.OpsXDragonStartTransactionalSendAlertRequest;
import com.aliyuncs.ecsops.model.v20160401.OpsXDragonStartTransactionalSendAlertResponse;
import com.aliyuncs.ecsops.model.v20160401.OpsXdragonSendAlertRequest;
import com.aliyuncs.ecsops.model.v20160401.OpsXdragonSendAlertRequest.Item;
import com.aliyuncs.ecsops.model.v20160401.OpsXdragonSendAlertResponse;
import com.aliyuncs.exceptions.ClientException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/06/05
 */
@Service
public class XdragonAlertServiceImpl extends DevOpsApiService implements XdragonAlertService {

    @Resource
    private XDragonAlertManagerService xDragonAlertManagerService;

    @Override
    @ServerLog
    public OpsXdragonSendAlertResponse sendAlert(List<String> monitorLabels, String fromSource, List<Item> items,
        String commitId, Map<String, String> auditParam) throws ClientException {
        OpsXdragonSendAlertRequest request = new OpsXdragonSendAlertRequest();
        if (StringUtils.isNotBlank(commitId)) {
            request.setCommitId(commitId);
        }
        request.setMonitorLabels(monitorLabels);
        request.setFromSource(fromSource);
        request.setSysRegionId(defaultOxsOpsApiRegion);
        if (auditParam != null && !auditParam.isEmpty()) {
            request.setAuditParamStr(DevOpsApiService.paramToJson(auditParam));
        }
        if (items != null && !items.isEmpty()) {
            request.setItems(items);
        }
        return queryOpsApi(request, 3);
    }

    @Override
    @ServerLog
    public OpsXDragonStartTransactionalSendAlertResponse startTransactionalSendAlert(Map<String, String> auditParam)
        throws ClientException {
        OpsXDragonStartTransactionalSendAlertRequest request = new OpsXDragonStartTransactionalSendAlertRequest();
        if (auditParam != null && !auditParam.isEmpty()) {
            request.setAuditParamStr(DevOpsApiService.paramToJson(auditParam));
        }
        request.setSysRegionId(defaultOxsOpsApiRegion);
        return queryOpsApi(request, 3);
    }

    @Override
    @ServerLog
    public OpsXDragonEndTransactionalSendAlertResponse endTransactionalSendAlert(String commitId, Map<String, String> auditParam)
        throws ClientException {
        OpsXDragonEndTransactionalSendAlertRequest request = new OpsXDragonEndTransactionalSendAlertRequest();
        request.setCommitId(commitId);
        if (auditParam != null && !auditParam.isEmpty()) {
            request.setAuditParamStr(DevOpsApiService.paramToJson(auditParam));
        }
        request.setSysRegionId(defaultOxsOpsApiRegion);
        return queryOpsApi(request, 3);
    }

    @Override
    @ServerLog
    public DevOpsXamResult<Integer> addNewRouter(DevOpsRouterParam param) {
        return xDragonAlertManagerService.addNewRouter(param);
    }

    @Override
    @ServerLog
    public DevOpsXamResult<Boolean> updateRouter(DevOpsRouterParam param) {
        return xDragonAlertManagerService.updateRouterByRouterName(param);
    }

    @Override
    @ServerLog
    public DevOpsXamResult<Boolean> deleteRouter(DevOpsDeleteRouterParam param) {
        return xDragonAlertManagerService.deleteRouterByRouterName(param);
    }

    @Override
    @ServerLog
    public DevOpsXamResult<DevOpsRouterModel> getRouter(String routerName) {
        DevOpsQueryRouterParam param = new DevOpsQueryRouterParam();
        param.setRouterName(routerName);
        return xDragonAlertManagerService.getRouterByRouterName(param);
    }

    @Override
    @ServerLog
    public PlainResult<String> previewTemplate(DevOpsXamPreviewTemplateParam previewTemplateParam) {
        return xDragonAlertManagerService.previewTemplateByItemDetail(previewTemplateParam);
    }
}
