package com.aliyun.xdragon.service.common.config;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import com.aliyun.xdragon.common.enumeration.CacheKeys;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * <AUTHOR>
 * @date 2022/4/21
 */
@EnableCaching
@Configuration
public class RedisCacheConfig extends CachingConfigurerSupport {
    private static final int DEFAULT_CACHE_EXPIRATION_SECONDS = 60 * 10;

    @Bean
    public RedisTemplate<String, Object> redisTemplate(LettuceConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        redisTemplate.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        redisTemplate.setConnectionFactory(connectionFactory);
        return redisTemplate;
    }

    /**
     * redis缓存管理器
     */
    @Bean
    @Primary
    public CacheManager cacheManager(LettuceConnectionFactory connectionFactory) {
        // init expired seconds
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(
                new GenericJackson2JsonRedisSerializer()))
            .entryTtl(Duration.ofSeconds(DEFAULT_CACHE_EXPIRATION_SECONDS));

        Map<String, RedisCacheConfiguration> cacheNamesConfigurationMap = new HashMap<>(CacheKeys.Item.values().length);
        for (CacheKeys.Item item: CacheKeys.Item.values()) {
            cacheNamesConfigurationMap.put(item.getKeyPrefix(), config.entryTtl(Duration.ofSeconds(item.getExpiredSeconds())));

        }
        return RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(config)
            .withInitialCacheConfigurations(cacheNamesConfigurationMap)
            .transactionAware()
            .build();
    }

    @Bean
    @Override
    public CacheErrorHandler errorHandler() {
        return new XdragonCacheErrorHandler();
    }

}


