package com.aliyun.xdragon.service.common.repository;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.xdragon.common.util.SQLUtil;
import com.aliyun.xdragon.service.common.config.FlinkMetricSlsConfig;
import com.aliyun.xdragon.service.common.config.diamond.ConfigService;
import com.aliyun.xdragon.service.common.model.SLSQueryResult;
import com.aliyun.xdragon.service.common.util.SlsUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/17
 */
@Component
public class FlinkMetricDao {
    private static final Logger logger = LoggerFactory.getLogger(FlinkMetricDao.class);

    public static final String DELAY_METRIC_NAME = "flink_taskmanager_job_task_operator_currentEmitEventTimeLag";
    public static final String RESTART_METRIC_NAME = "flink_jobmanager_job_numRestarts";

    public static final String UPTIME_METRIC_NAME = "flink_jobmanager_job_uptime";
    public static final String CHECKPOINT_METRIC_NAME = "flink_jobmanager_job_numberOfCompletedCheckpoints";

    @Autowired
    private FlinkMetricSlsConfig flinkMetricSlsConfig;

    @Autowired
    private ConfigService configService;

    private Client client;

    @PostConstruct
    public void init() {
        client = configService.getSlsClient(flinkMetricSlsConfig.getUser(), flinkMetricSlsConfig.getRegion(), null);
        if (client == null) {
            logger.error("init flink metric sls client null");
        }
    }

    public Integer queryUpTime(String deploymentId, int startTs, int endTs) {
        if (client == null) {
            logger.warn("sls client init not ok");
            return null;
        }
        String sql = String.format(
            "deploymentId: %s and __name__: %s | select __value__ as v, __time_nano__ as t order by t desc limit 1",
            SQLUtil.trimSql(deploymentId), UPTIME_METRIC_NAME);

        SLSQueryResult queryResult = SlsUtil.querySLS(client, flinkMetricSlsConfig.getProject(),
            flinkMetricSlsConfig.getLogStore(), startTs, endTs, "", sql);
        if (queryResult.getError() == null && queryResult.isCompleted()) {
            List<QueriedLog> logs = queryResult.getLogs();
            if (logs.isEmpty()) {
                logger.warn("no flink metric about deploymentId {} and metric {}, sql: {}, time range {}:{}",
                    deploymentId, UPTIME_METRIC_NAME, sql, startTs, endTs);
                return null;
            } else {
                Map<String, String> m = SlsUtil.parseLogs(logs.get(0));
                Double d = Double.parseDouble(m.get("v")) / 1000;
                return d.intValue();
            }
        } else {
            return null;
        }
    }

    public List<Pair<String, Long>> queryDelay(String deploymentId, int startTs, int endTs) {
        if (client == null) {
            logger.warn("sls client init not ok");
            return null;
        }
        String sql = String.format("deploymentId: %s and __name__: %s | select host, subtask_index, operator_name, "
                + "avg(__value__) as ms from log group by host, subtask_index, operator_name order by ms desc limit "
                + "1000",
            SQLUtil.trimSql(deploymentId), DELAY_METRIC_NAME);

        SLSQueryResult queryResult = SlsUtil.querySLS(client, flinkMetricSlsConfig.getProject(),
            flinkMetricSlsConfig.getLogStore(), startTs, endTs, "", sql);
        if (queryResult.getError() == null && queryResult.isCompleted()) {
            List<QueriedLog> logs = queryResult.getLogs();
            if (logs.isEmpty()) {
                logger.warn("no flink metric about deploymentId {} and metric {}, sql: {}, time range {}:{}",
                    deploymentId, DELAY_METRIC_NAME, sql, startTs, endTs);
                return new ArrayList<>();
            }
            List<Map<String, String>> logLists = SlsUtil.parseLogs(logs);
            List<Pair<String, Long>> hostsDelay = new LinkedList<>();
            logLists.forEach(m -> {
                String host = String.format("%s_%s_%s", m.get("host"), m.get("operator_name"), m.get("subtask_index"));
                Double d = Double.parseDouble(m.get("ms"));
                hostsDelay.add(Pair.of(host, d.longValue()));
            });
            return hostsDelay;
        } else {
            logger.warn("query sls error, return null result");
            return null;
        }
    }

    public Integer queryRestart(String deploymentId, int startTs, int endTs) {
        Pair<Integer, Integer> p = queryRestartWithDetail(deploymentId, startTs, endTs);
        if (p != null && p.getLeft() != -1 && p.getRight() != -1) {
            return p.getLeft() - p.getRight();
        } else {
            return null;
        }
    }

    public Pair<Integer, Integer> queryRestartWithDetail(String deploymentId, int startTs, int endTs) {
        if (client == null) {
            logger.warn("sls client init not ok");
            return null;
        }
        String sql = String.format(
            "deploymentId: %s and __name__: %s | select __value__ as v, __time_nano__ as t, job_id order by t desc "
                + "limit 1000", SQLUtil.trimSql(deploymentId), RESTART_METRIC_NAME);
        SLSQueryResult queryResult = SlsUtil.querySLS(client, flinkMetricSlsConfig.getProject(),
            flinkMetricSlsConfig.getLogStore(), startTs, endTs, "", sql);
        if (queryResult.getError() == null && queryResult.isCompleted()) {
            List<QueriedLog> logs = queryResult.getLogs();
            if (logs.isEmpty()) {
                logger.warn("no flink metric about deploymentId {} and metric {}, sql: {}, time range {}:{}",
                    deploymentId, RESTART_METRIC_NAME, sql, startTs, endTs);
                return Pair.of(-1, -1);
            }
            List<Map<String, String>> logLists = SlsUtil.parseLogs(logs);
            String currentJob = logLists.get(0).get("job_id");
            logLists.removeIf(m -> !m.get("job_id").equals(currentJob));
            Double max = Double.parseDouble(logLists.get(0).get("v"));
            int pos = logLists.size() == 1 ? 0 : logLists.size() - 1;
            Double min = Double.parseDouble(logLists.get(pos).get("v"));
            return Pair.of(max.intValue(), min.intValue());
        } else {
            logger.warn("query sls error, return null result");
            return null;
        }

    }

    public Integer queryCheckpoint(String deploymentId, int startTs, int endTs) {
        if (client == null) {
            logger.warn("sls client init not ok");
            return null;
        }
        String sql = String.format(
            "deploymentId:%s and __name__:%s | select __value__ as v, __time_nano__ as t, job_id order by t desc "
                + "limit 1000", SQLUtil.trimSql(deploymentId), CHECKPOINT_METRIC_NAME);

        SLSQueryResult queryResult = SlsUtil.querySLS(client, flinkMetricSlsConfig.getProject(),
            flinkMetricSlsConfig.getLogStore(), startTs, endTs, "", sql);
        if (queryResult.getError() == null && queryResult.isCompleted()) {
            List<QueriedLog> logs = queryResult.getLogs();
            if (logs.isEmpty()) {
                logger.warn("no flink metric about deploymentId {} and metric {}, sql: {}, time range {}:{}",
                    deploymentId, CHECKPOINT_METRIC_NAME, sql, startTs, endTs);
                return null;
            }
            List<Map<String, String>> logLists = SlsUtil.parseLogs(logs);
            String currentJob = logLists.get(0).get("job_id");
            logLists.removeIf(m -> !m.get("job_id").equals(currentJob));
            Double lastCount = Double.parseDouble(logLists.get(0).get("v"));
            int pos = logLists.size() == 1 ? 0 : logLists.size() - 1;
            Double oldCount = Double.parseDouble(logLists.get(pos).get("v"));
            return lastCount.intValue() - oldCount.intValue();
        } else {
            logger.warn("query sls error, return null result");
            return null;
        }
    }

    public Integer getNewestTime(String deploymentId) {
        if (client == null) {
            logger.warn("sls client init not ok");
            return null;
        }
        int endTs = (int)(System.currentTimeMillis() / 1000);
        int startTs = endTs - 6 * 3600;
        String sql = String.format("deploymentId:%s| select max(__time__) as mt limit 1",
            SQLUtil.trimSql(deploymentId));
        SLSQueryResult queryResult = SlsUtil.querySLS(client, flinkMetricSlsConfig.getProject(),
            flinkMetricSlsConfig.getLogStore(), startTs, endTs, "", sql);
        if (queryResult.getError() == null && queryResult.isCompleted()) {
            List<QueriedLog> logs = queryResult.getLogs();
            if (logs.isEmpty()) {
                return null;
            }
            List<Map<String, String>> logLists = SlsUtil.parseLogs(logs);
            if (!logLists.get(0).containsKey("mt") || logLists.get(0).get("mt") == null || "null".equals(
                logLists.get(0).get("mt"))) {
                return null;
            } else {
                return Integer.parseInt(logLists.get(0).get("mt"));
            }

        } else {
            logger.warn("query sls error, return null result");
            return null;
        }
    }
}
