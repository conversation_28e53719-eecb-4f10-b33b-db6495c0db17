package com.aliyun.xdragon.service.common.agent;

import java.util.Map;

import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.alibaba.xxpt.gateway.shared.client.http.PostClient;

import com.aliyun.xdragon.common.enumeration.SvrResultStatus;
import com.aliyun.xdragon.common.model.EpaasError;
import com.aliyun.xdragon.common.util.ServerLogContextUtil;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/05/31
 */
@Component
public class EpaasClient {
    private static final Logger logger = LoggerFactory.getLogger(EpaasClient.class);

    private static final Gson gson = new Gson();

    @Autowired
    protected ExecutableClient executableClient;

    public String post(String api, Map<String, String> parameters) {
        PostClient postClient = this.executableClient.newPostClient(api);

        for (Map.Entry<String, String> param : parameters.entrySet()) {
            postClient.addParameter(param.getKey(), param.getValue());
        }

        return postClient.post();
    }

    protected void parseEpaasError(String responseContent) {
        logger.error("post epaas fail, error content: {}", responseContent);
        int startIndex = responseContent.indexOf("{");
        int endIndex = responseContent.lastIndexOf("}");
        if (startIndex != -1 && endIndex != -1 && startIndex < endIndex) {
            responseContent = responseContent.substring(startIndex, endIndex + 1);
            EpaasError epaasError = gson.fromJson(responseContent, EpaasError.class);
            ServerLogContextUtil.setErrorInfo(SvrResultStatus.SYSTEM_ERROR.name(), epaasError.getCode(),
                epaasError.getMessage(), epaasError.getRequestId());
        } else {
            ServerLogContextUtil.setErrorInfo(SvrResultStatus.SYSTEM_ERROR.name(), "500",
                responseContent, "");
        }
    }
}
