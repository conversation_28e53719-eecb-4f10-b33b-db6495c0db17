package com.aliyun.xdragon.service.common.util;

import com.google.gson.Gson;
import org.springframework.beans.BeanUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ObjectFillUtil {


    public static <T> T fillObjectAttributes(Map<String, Object> values, T object) throws InvocationTargetException, IllegalAccessException {
        PropertyDescriptor[] propertyDescriptors = BeanUtils.getPropertyDescriptors(object.getClass());
        for(PropertyDescriptor descriptor : propertyDescriptors) {
            String propertyName = descriptor.getName();
            if (values.containsKey(propertyName)) {
                Method writeMethod = descriptor.getWriteMethod();
                writeMethod.invoke(object, values.get(propertyName));
            }
        }
        return object;
    }

    public static Map<String, String> convertToMap(Object object) {
        Gson gson = new Gson();
        Map<String, String> info = new HashMap<>();
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field:fields) {
            field.setAccessible(true);
            String name = field.getName();
            if ("serialVersionUID".equals(name)) {
                continue;
            }
            try {
                Object value = field.get(object);
                if (value instanceof List) {
                    info.put(name, gson.toJson(value));
                } else {
                    info.put(name, value.toString());
                }
            }catch (Exception e) {
                // 缺失值
                info.put(name, "");
            }
        }
        return info;
    }
}
