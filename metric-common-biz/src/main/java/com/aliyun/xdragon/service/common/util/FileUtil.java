package com.aliyun.xdragon.service.common.util;

import com.aliyun.odps.Column;
import com.aliyun.odps.OdpsType;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.type.TypeInfo;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedWriter;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class FileUtil {

    /**
     * 向目标文件写入数据
     *
     * @param filePath 文件路径
     * @param lines    每一个String一行
     * @param append   true表示追加写入，false表示覆盖写入
     */
    public static void writeToFile(String filePath, List<String> lines, boolean append) {
        BufferedWriter buffWriter = null;
        try {
            buffWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(filePath, append), "GBK"));
            for (String line : lines) {
                if (StringUtils.isNotBlank(line)) {
                    buffWriter.write(line);
                    buffWriter.newLine();
                }
            }
            buffWriter.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                //关闭流
                if (buffWriter != null) {
                    buffWriter.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 将odps表的Record转为String，后续写入到文档中
     *
     * @param records odps表数据
     * @return List<String>
     */
    public static List<String> processRecords(List<Record> records) {
        if (records.size() == 0) {
            System.out.println("there are no data");
            return null;
        }
        List<String> columns = Arrays.stream(records.get(0).getColumns()).parallel().map(Column::getName).collect(Collectors.toList());
        String header = String.join(",", columns);
        List<String> result = new ArrayList<>();
        result.add(header);
        for (Record record : records) {
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < columns.size(); i++) {
                TypeInfo type = record.getColumns()[i].getTypeInfo();
                if (OdpsType.STRING.equals(type.getOdpsType())) {
                    stringBuilder.append(record.getString(i) != null ? record.getString(i).replaceAll(",", "，").replaceAll("(\r|\n)", " ") : "").append(",");
//                    stringBuilder.append(record.getString(i).replaceAll(",", "，").replaceAll("(\r|\n)", " ")).append(",");
                } else {
                    stringBuilder.append(record.get(i) != null ? record.get(i).toString() : "").append(",");
                }
//                stringBuilder.append(record.getString(i).replaceAll(",", "，").replaceAll("(\r|\n)", " ")).append(",");
            }
            result.add(stringBuilder.substring(0, stringBuilder.length() - 1));
        }
        return result;
    }
}
