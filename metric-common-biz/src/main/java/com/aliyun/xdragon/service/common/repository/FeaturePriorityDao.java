package com.aliyun.xdragon.service.common.repository;

import com.aliyun.xdragon.common.generate.model.XdcFeaturePriority;
import com.aliyun.xdragon.common.generate.model.XdcFeaturePriorityExample;
import com.aliyun.xdragon.common.generate.model.map.XdcFeaturePriorityMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class FeaturePriorityDao {

    @Autowired
    private XdcFeaturePriorityMapper mapper;

    /**
     * 查询feature的领域分类
     * @param featureName
     * @return 特征的配置信息，一个特征可能有多个领域模型
     */
    public List<XdcFeaturePriority> queryFeatureByName(String featureName) {
        XdcFeaturePriorityExample example = new XdcFeaturePriorityExample();
        XdcFeaturePriorityExample.Criteria criteria = example.createCriteria();
        criteria.andFeatureNameEqualTo(featureName);
        return mapper.selectByExample(example);
    }
}
