package com.aliyun.xdragon.service.common.util;

import java.util.Properties;

public class ParamsUtil {
    public static Properties parseParams(String paramsStr) {
        // 按逗号分隔字符串

        Properties properties = new Properties();
        if (Checks.nullOrEmpty(paramsStr)) {
            return properties;
        }
        String[] pairs = paramsStr.split(",");

        // 遍历每个键值对
        for (String pair : pairs) {
            String[] keyValue = pair.split("=", 2); // 只分割一次
            if (keyValue.length == 2) {
                properties.put(keyValue[0].trim(), keyValue[1].trim()); // 去掉空格并存入 Properties
            } else {
                throw new IllegalArgumentException("Invalid parameter format: " + pair);
            }
        }
        return properties;
    }
}
