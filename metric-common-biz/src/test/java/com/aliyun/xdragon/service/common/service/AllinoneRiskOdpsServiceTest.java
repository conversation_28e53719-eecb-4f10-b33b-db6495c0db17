package com.aliyun.xdragon.service.common.service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.aliyun.odps.Column;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.type.TypeInfo;
import com.aliyun.odps.type.TypeInfoFactory;
import com.aliyun.xdragon.common.enumeration.AllinoneRiskStatus;
import com.aliyun.xdragon.common.enumeration.AllinoneRiskType;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRisk;
import com.aliyun.xdragon.service.common.agent.OdpsClient;

import com.aliyun.xdragon.service.common.util.DateUtil;
import org.checkerframework.checker.units.qual.C;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2024/11/19
 */
@RunWith(PowerMockRunner.class)
public class AllinoneRiskOdpsServiceTest {
    @InjectMocks
    private AllinoneRiskOdpsService allinoneRiskOdpsService;

    @Mock
    private OdpsClient odpsClient;

    @Mock
    private Record record;

    @Test
    public void test() throws ParseException, InterruptedException {
        AllinoneRisk risk = new AllinoneRisk();
        risk.setRiskType(AllinoneRiskType.DEPLOY.getName());
        Mockito.when(record.getColumns()).thenReturn(new Column[] {
                new Column("task_id", TypeInfoFactory.STRING),
                new Column("deploy_time", TypeInfoFactory.STRING),
                new Column("exception_name", TypeInfoFactory.STRING),
                new Column("instance_id", TypeInfoFactory.STRING),
                new Column("nc_ip", TypeInfoFactory.STRING),
                new Column("recover_time", TypeInfoFactory.STRING),
                new Column("region", TypeInfoFactory.STRING),
                new Column("ali_uid", TypeInfoFactory.STRING),
                new Column("cluster", TypeInfoFactory.STRING),
                new Column("iz", TypeInfoFactory.STRING),
                new Column("alert_name", TypeInfoFactory.STRING),
                new Column("id", TypeInfoFactory.STRING),
                new Column("reason", TypeInfoFactory.STRING),
                new Column("comment", TypeInfoFactory.STRING),
                new Column("task_id", TypeInfoFactory.STRING),
                new Column("service_name", TypeInfoFactory.STRING),
                new Column("first_exception_time", TypeInfoFactory.STRING),
                new Column("alert_time", TypeInfoFactory.STRING)
            });

        Mockito.when(record.getString("task_id")).thenReturn("task_id_1");
        Mockito.when(record.getString("deploy_time")).thenReturn("20231101");
        Mockito.when(record.getString("exception_name")).thenReturn("exception_name_1");
        Mockito.when(record.getString("instance_id")).thenReturn("instance_id_1");
        Mockito.when(record.getString("nc_ip")).thenReturn("nc_ip_1");
        Mockito.when(record.getString("recover_time")).thenReturn("2023-11-02 12:00:00");
        Mockito.when(record.getString("region")).thenReturn("region_1");
        Mockito.when(record.getString("ali_uid")).thenReturn("ali_uid_1");
        Mockito.when(record.getString("cluster")).thenReturn("cluster_1");
        Mockito.when(record.getString("iz")).thenReturn("iz_1");
        Mockito.when(record.getString("alert_name")).thenReturn("alert_name_1");
        Mockito.when(record.getString("id")).thenReturn("id_1");
        Mockito.when(record.getString("reason")).thenReturn("reason_1");
        Mockito.when(record.getString("comment")).thenReturn("comment_1");
        Mockito.when(record.getString("task_id")).thenReturn("task_id_1");
        Mockito.when(record.getString("service_name")).thenReturn("service_name_1");
        Mockito.when(record.getString("first_exception_time")).thenReturn("2023-11-02 12:00:00");
        Mockito.when(record.getString("alert_time")).thenReturn("2023-11-02 12:00:00");

        Mockito.when(odpsClient.runSql(any())).thenReturn(new ArrayList<>(Collections.nCopies(3, record)));
        Map<String, Map<String, String>> vmInfo = allinoneRiskOdpsService.getReleaseRiskDetailInfo("20231101", risk, "instance_id");
        Assert.assertEquals("nc_ip_1", vmInfo.get("instance_id_1").get("nc_ip"));

        Mockito.when(odpsClient.runSql(any())).thenReturn(new ArrayList<>(Collections.nCopies(3, record)));
        Map<String, Map<String, String>> ncInfo = allinoneRiskOdpsService.getReleaseRiskDetailInfo("20231101", risk, "nc_ip");
        Assert.assertEquals("nc_ip_1", ncInfo.get("nc_ip_1").get("nc_ip"));

        Mockito.when(odpsClient.runSql(any())).thenReturn(new ArrayList<>(Collections.nCopies(3, record)));
        List<AllinoneRisk> releaseRisks = allinoneRiskOdpsService.getReleaseRisks("20231101");
        Assert.assertEquals(3, releaseRisks.size());

        Mockito.when(odpsClient.runSql(any())).thenReturn(new ArrayList<>(Collections.nCopies(3, record)));
        Map<String, Map<String, String>> info = allinoneRiskOdpsService.getVmInfo("2023-11-02 12:00:00",
            "2023-11-02 12:00:00", Arrays.asList("instance_id_1"));
        Assert.assertEquals("nc_ip_1", info.get("instance_id_1").get("nc_ip"));

        Mockito.when(odpsClient.runSql(any())).thenReturn(new ArrayList<>(Collections.nCopies(3, record)));
        info = allinoneRiskOdpsService.getNcInfo("2023-11-02 12:00:00",
            "2023-11-02 12:00:00", Arrays.asList("nc_ip_1"));
        Assert.assertEquals("nc_ip_1", info.get("nc_ip_1").get("nc_ip"));
    }
}
