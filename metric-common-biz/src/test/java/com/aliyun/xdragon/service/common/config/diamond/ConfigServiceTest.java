package com.aliyun.xdragon.service.common.config.diamond;

import com.aliyun.xdragon.common.model.LogTaskOwner;
import com.aliyun.xdragon.service.common.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

public class ConfigServiceTest extends BaseTestCase {

    @Resource
    private ConfigService configService;

    @Test
    public void testLoadAoneModule() {
        Map<String, Integer> map = configService.loadAoneModule();
        Assert.assertTrue(map.size() > 0);
    }

    @Test
    public void testLoadMetricFeatureWeight() {
        Map<String, Double> weight = configService.loadMetricFeatureWeight();
        Assert.assertNotNull(weight);
    }

    @Test
    public void testLoadLogTaskOwner() {
        List<LogTaskOwner> logTaskOwners = configService.loadLogTaskOwner();
        Assert.assertNotNull(logTaskOwners);
    }

    @Test
    public void testLoadWorkItemExceptionTags() {
        Map<String, List<String>> exceptionTags = configService.loadWorkItemExceptionTags();
        Assert.assertNotNull(exceptionTags);
    }

    @Test
    public void testLoadWorkItemCategoryResponsible() {
        Map<String, List<String>> map = configService.loadWorkItemCategoryResponsible();
        Assert.assertNotNull(map);
    }
}
