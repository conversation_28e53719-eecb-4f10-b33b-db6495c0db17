package com.aliyun.xdragon.service.common.util;

import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.junit.Assert;
import org.junit.Test;

import static com.aliyun.xdragon.service.common.util.Checks.nullOrEmpty;

public class ChecksTest {
    @Test
    public void testNullOrEmpty() {
        String str = null;
        Assert.assertTrue(nullOrEmpty(str));
        str = "";
        Assert.assertTrue(nullOrEmpty(str));
        str = "dummy";
        Assert.assertFalse(nullOrEmpty(str));

        List<Long> list = null;
        Assert.assertTrue(nullOrEmpty(list));
        list = new LinkedList<>();
        Assert.assertTrue(nullOrEmpty(list));
        list.add(0L);
        Assert.assertFalse(nullOrEmpty(list));

        Set<Long> set = null;
        Assert.assertTrue(nullOrEmpty(set));
        set = new HashSet<>();
        Assert.assertTrue(nullOrEmpty(set));
        set.add(0L);
        Assert.assertFalse(nullOrEmpty(set));

        Map<Long, Long> map = null;
        Assert.assertTrue(nullOrEmpty(map));
        map = new HashMap<>();
        Assert.assertTrue(nullOrEmpty(map));
        map.put(0L, 1L);
        Assert.assertFalse(nullOrEmpty(map));
    }

    @Test
    public void testNull2Empty() {
        String str = null;
        Assert.assertEquals("", Checks.null2Empty(str));
        str = "";
        Assert.assertEquals("", Checks.null2Empty(str));
        str = "dummy";
        Assert.assertEquals("dummy", Checks.null2Empty(str));
    }
}
