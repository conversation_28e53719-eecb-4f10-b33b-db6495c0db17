package com.aliyun.xdragon.service.common.service;

import com.aliyun.xdragon.service.common.BaseTestCase;
import org.junit.Test;

import javax.annotation.Resource;

public class KeyCenterTest extends BaseTestCase {

    @Resource
    KeyCenterService keyCenterService;

    @Test
    public void testEncrypt() {
        String sourceContent = "123";
        assert keyCenterService.encrypt(sourceContent).equals("uvIFFp3eySqGk5oFHWtZZQ==");
    }

    @Test
    public void testDecrypt() {
        String decryptContent = "uvIFFp3eySqGk5oFHWtZZQ==";
        assert keyCenterService.decrypt(decryptContent).equals("123");
    }
}
