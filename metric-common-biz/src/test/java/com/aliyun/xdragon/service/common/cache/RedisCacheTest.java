package com.aliyun.xdragon.service.common.cache;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;


import com.aliyun.xdragon.common.enumeration.CacheKeys;
import com.aliyun.xdragon.service.common.BaseTestCase;
import com.aliyun.xdragon.service.common.config.RedisCacheConfig;
import junit.framework.TestCase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/4/21
 */
@Configuration
@EnableCaching
public class RedisCacheTest extends BaseTestCase {

    @Autowired
    private RedisCacheConfig redisCacheConfig;

    @Autowired
    private TestQuery testQuery;

    @Test
    public void testRedisCache() {
        TestCase.assertTrue(Objects.nonNull(redisCacheConfig));
        testQuery.clearCache();
        TestCase.assertTrue(testQuery.getAtomicInteger().get() == 0);
        String var = "world";
        String test = testQuery.query(1, var);
        TestCase.assertTrue(testQuery.getAtomicInteger().get() == 1);
        TestCase.assertEquals(test, "hello " + var);
        test = testQuery.query(1, var);
        // cached, no change
        TestCase.assertEquals(test, "hello " + var);
        TestCase.assertTrue(testQuery.getAtomicInteger().get() == 1);

        // another key, cache miss
        test = testQuery.query(2, var + var);
        TestCase.assertEquals(test, "hello " + var + var);
        TestCase.assertTrue(testQuery.getAtomicInteger().get() == 2);
        // hit cache
        test = testQuery.query(2, var + var);
        TestCase.assertEquals(test, "hello " + var + var);
        TestCase.assertTrue(testQuery.getAtomicInteger().get() == 2);

        testQuery.clearCache2();
        test = testQuery.query2(1, "hello");
        TestCase.assertEquals(test, "hello " + "hello");
        TestCase.assertTrue(testQuery.getAtomicInteger().get() == 3);

        // hit cache
        test = testQuery.query2(1, "hello");
        TestCase.assertEquals(test, "hello " + "hello");
        TestCase.assertTrue(testQuery.getAtomicInteger().get() == 3);
    }
}


@Component
class TestQuery {

    private AtomicInteger atomicInteger = new AtomicInteger();

    @Cacheable(cacheNames = {CacheKeys.Keys.TEST_CACHE_NAME_1_PREFIX})
    public String query(Integer x, String y) {
        atomicInteger.getAndIncrement();
        return "hello " + y;
    }

    @CacheEvict(cacheNames = {CacheKeys.Keys.TEST_CACHE_NAME_1_PREFIX}, allEntries = true)
    public void clearCache() {
    }

    @Cacheable(cacheNames = {CacheKeys.Keys.TEST_CACHE_NAME_2_PREFIX})
    public String query2(Integer x, String y) {
        atomicInteger.getAndIncrement();
        return "hello " + y;
    }

    @CacheEvict(cacheNames = {CacheKeys.Keys.TEST_CACHE_NAME_2_PREFIX}, allEntries = true)
    public void clearCache2() {
    }

    public AtomicInteger getAtomicInteger() {
        return atomicInteger;
    }
}