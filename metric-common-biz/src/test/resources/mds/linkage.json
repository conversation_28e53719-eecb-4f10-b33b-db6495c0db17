{"recordsFiltered": 127, "code": 200, "data": [{"query": "* and not exceptionName: deploy_detect_feature", "analyse": "select taskId, 'nc' as resourceType, 'autoBatch' as faultSource, serviceName, cluster, iz, region, ncIp, isGamma, groupId, groupSequenceNo, exceptionTime, exceptionName, deployTime, causeInfo, causeInfoV2, rootCauses, additionalInfo, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY groupId))) as ncCount from (select distinct task_id as taskId, server_role as serviceName, region, clusterName as cluster, azone as iz, nc_ip as ncIp, isGamma, group_id as groupId, groupSequenceNo, exceptionTime, exceptionName, deployTime, causeInfo, causeInfoV2, rootCauses, additionalInfo from log  where (1 = 1) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["1 = 1"], "enable": true, "notifyEnable": true, "name": "batch_exception_alert_warning", "doc": "变更关联批量异常，警告但不熔断", "end": 0, "interval": 600, "logstore": "ecs_change_plan_exception_detect_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0 && ncCount<6", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and not exceptionName: deploy_detect_feature", "analyse": "select taskId, 'nc' as resourceType, 'autoBatch' as faultSource, serviceName, cluster, iz, region, ncIp, isGamma, groupId, groupSequenceNo, exceptionTime, exceptionName, deployTime, causeInfo, causeInfoV2, rootCauses, additionalInfo, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY groupId))) as ncCount from (select distinct task_id as taskId, server_role as serviceName, region, clusterName as cluster, azone as iz, nc_ip as ncIp, isGamma, group_id as groupId, groupSequenceNo, exceptionTime, exceptionName, deployTime, causeInfo, causeInfoV2, rootCauses, additionalInfo from log  where (1 = 1) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["1 = 1"], "enable": true, "notifyEnable": true, "name": "batch_exception_alert", "doc": "变更导致批量异常，达到熔断级别", "end": 0, "interval": 600, "logstore": "ecs_change_plan_exception_detect_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>5", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and risk_type : batch_risk and (batch_risk_level: warning or batch_risk_level: critical or batch_risk_level: fatal) ", "analyse": "select distinct exception_name as exceptionName, exception_time as exceptionTime, '' as bizStatus, aliuid as aliUid, cluster as clusterAlias, nc_vcpu as cores, azone as iz, region, '' as faultSource, 'nc' as targetType, product_name as productName, vcpu_mod as vcpuMod, virt_type as virtType, nc_ip as resourceId  from log  where (exceptionName not in ('vm_heartbeat_loss_too_many','vm_uptime_drop_event_increased','vm_panic_event_too_many') and exceptionName not like '%hardware_%') limit 10000", "start": 0, "project": "ecs-predict-result", "whereStatement": ["exceptionName not in ('vm_heartbeat_loss_too_many','vm_uptime_drop_event_increased','vm_panic_event_too_many') and exceptionName not like '%hardware_%'"], "enable": true, "notifyEnable": true, "name": "allinonerisk_traceback_alert", "doc": "【dryrun】批量隐患回溯历史变更，仅预警不熔断", "end": 0, "interval": 600, "logstore": "allinonerisk", "alertValue": 0, "levels": [{"expression": "serviceLevel>30 && ncCount>5 && deployRate>0.8 && totalRank<5", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 605}, {"query": "* and risk_type : limit", "analyse": "select distinct exception_name as exceptionName, exception_time as exceptionTime, '' as bizStatus, aliuid as aliUid, cluster as clusterAlias, nc_vcpu as cores, azone as iz, region, '' as faultSource, 'nc' as targetType, product_name as productName, vcpu_mod as vcpuMod, virt_type as virtType, nc_ip as resourceId, risk_name as additionalInfo from log  where (exceptionName not in ('vm_heartbeat_loss_too_many','vm_uptime_drop_event_increased','vm_panic_event_too_many') and exceptionName not like '%hardware_%') limit 10000", "start": 0, "project": "ecs-predict-result", "whereStatement": ["exceptionName not in ('vm_heartbeat_loss_too_many','vm_uptime_drop_event_increased','vm_panic_event_too_many') and exceptionName not like '%hardware_%'"], "enable": true, "notifyEnable": true, "name": "allinonerisk_traceback_alert_limit", "doc": "【dryrun】批量隐患流控回溯历史变更，仅预警不熔断", "end": 0, "interval": 600, "logstore": "allinonerisk", "alertValue": 0, "levels": [{"expression": "serviceLevel>30 && ncCount>5 && deployRate>0.8 && totalRank<5", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 605}, {"query": "* and not additionalInfo: active and not lock_reason:nc_deploy and not cluster_alias:\"\" and not state: wait_online and not exceptionSource:xdc_rule", "analyse": "select distinct exceptionName, exceptionTime, biz_status as bizStatus, aliUid as aliUid, cluster_alias as clusterAlias, cores, iz, region, '' as faultSource, 'nc' as targetType, product_name as productName, vcpu_mod as vcpuMod, virt_type as virtType, ncIp as resourceId from log  where (exceptionName in ('cloudops_ops_flow_limit') and (additionalInfo like '%nc_down%')) limit 10000", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('cloudops_ops_flow_limit') and (additionalInfo like '%nc_down%')"], "enable": true, "notifyEnable": true, "name": "ops_flow_limit_traceback_alert", "doc": "【dryrun】流控预警回溯历史变更，仅预警不熔断", "end": 0, "interval": 600, "logstore": "monitor_exception_sls_alert", "alertValue": 0, "levels": [{"expression": "serviceLevel>30 && ncCount>5 && deployRate>0.8 && totalRank<5", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 605}, {"query": "* and not additionalInfo: active and not lock_reason:nc_deploy and not cluster_alias:\"\" and not state: wait_online and not exceptionSource:xdc_rule", "analyse": "select distinct exceptionName, exceptionTime, biz_status as bizStatus, aliUid as aliUid, cluster_alias as clusterAlias, cores, iz, region, exceptionSource as faultSource, 'nc' as targetType, product_name as productName, vcpu_mod as vcpuMod, virt_type as virtType, ncIp as resourceId from log  where (exceptionName in ({{GOC_EVENT}}) and exceptionName not in ('vm_heartbeat_loss_too_many','vm_uptime_drop_event_increased','vm_panic_event_too_many')) limit 10000", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ({{GOC_EVENT}}) and exceptionName not in ('vm_heartbeat_loss_too_many','vm_uptime_drop_event_increased','vm_panic_event_too_many')"], "enable": true, "notifyEnable": false, "name": "goc_exception_traceback_alert", "doc": "【dryrun】GOC预警回溯历史变更，仅预警不熔断", "end": 0, "interval": 600, "logstore": "monitor_exception_sls_alert", "alertValue": 0, "levels": [{"expression": "serviceLevel>30 && ncCount>4 && deployRate>0.9 && totalRank<5", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 605}, {"query": "* and (window_size: 030005 or window_size: 010005) and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement or log_source: sysvm or log_source: woodpecker or log_source: woodpecker-pro or log_source: opsx or log_source: rpm or log_source: ncrs) and not isGamma: 1 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked and not plan_id:xdragon_hot_upgrade_fpga_public.Command#", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,ncIp))) as timeCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName like 'xdc_rule_%' and additionalInfo not in ('nc_rpm_error_offline')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName like 'xdc_rule_%' and additionalInfo not in ('nc_rpm_error_offline')"], "enable": true, "notifyEnable": true, "name": "xdc_rule_exception_alert", "doc": "发布批量命中主动运维规则,警告但不熔断", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "low_warning"}, {"expression": "ncCount>4 || taskSum>9", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and window_size: 010005 and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement or log_source: sysvm or log_source: woodpecker or log_source: woodpecker-pro or log_source: opsx or log_source: ncrs) and not isGamma: 1 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,ncIp))) as timeCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('xdc_rule_offline','xdc_rule_offlineJustNotify') and reason not like 'hardware_%' and reason not like 'network_cable__%' and reason not like 'rack_power_%' and reason not in ('moc_cn_bmc_cable_error') and additionalInfo not in ('nc_rpm_error_offline')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('xdc_rule_offline','xdc_rule_offlineJustNotify') and reason not like 'hardware_%' and reason not like 'network_cable__%' and reason not like 'rack_power_%' and reason not in ('moc_cn_bmc_cable_error') and additionalInfo not in ('nc_rpm_error_offline')"], "enable": true, "notifyEnable": true, "name": "xdc_rule_exception_alert_s1", "doc": "发布命中主动运维批量下线", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0 && ncCount<2 && taskSum<2", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "low_warning"}, {"expression": "ncCount>1 || taskSum>1", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and window_size: 030005 and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement or log_source: sysvm or log_source: woodpecker or log_source: woodpecker-pro or log_source: opsx or log_source: ncrs) and not isGamma: 1 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,ncIp))) as timeCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('xdc_rule_ncDpdkHighVmLiveMigrate','xdc_rule_ncStorageHighVmLiveMigrate','xdc_rule_shareHostVcpuUtilHighTWindowsLiveMigrate','xdc_rule_mocDownLiveMigrate') and reason not like 'hardware_%' and reason not like 'network_cable__%' and reason not like 'rack_power_%' and additionalInfo not in ('nc_rpm_error_offline')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('xdc_rule_ncDpdkHighVmLiveMigrate','xdc_rule_ncStorageHighVmLiveMigrate','xdc_rule_shareHostVcpuUtilHighTWindowsLiveMigrate','xdc_rule_mocDownLiveMigrate') and reason not like 'hardware_%' and reason not like 'network_cable__%' and reason not like 'rack_power_%' and additionalInfo not in ('nc_rpm_error_offline')"], "enable": true, "notifyEnable": true, "name": "xdc_rule_exception_alert_s2", "doc": "发布后命中主动运维批量迁移", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0 && ncCount<3 && taskSum<5", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "low_warning"}, {"expression": "ncCount>2 || taskSum>4", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and window_size: 010005 and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement or log_source: sysvm or log_source: woodpecker or log_source: woodpecker-pro or log_source: opsx or log_source: ncrs) and not isGamma: 1 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as  vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName = 'xdc_rule_pausedVMRestart') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName = 'xdc_rule_pausedVMRestart'"], "enable": true, "notifyEnable": true, "name": "xdc_rule_exception_alert_vm_s1", "doc": "发布命中vm不可用主动运维", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: high_risk_command and not isGamma: 1 and (window_size: 030005 or window_size: 010005) and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where ((exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')) or (exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))) or (exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','virt_report_exception_ping_failed') and warningLevel ='fatal') or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')", "exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))", "exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','virt_report_exception_ping_failed') and warningLevel ='fatal'", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "human_high_risk_changement_alert", "doc": "高风险人工操作导致批量异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "isHardwareIssue==0 && ncCount>1", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: high_risk_command and not isGamma: 1 and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vm_iohang_start') and warningLevel ='fatal') or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_iohang_start') and warningLevel ='fatal'", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')"], "enable": true, "notifyEnable": true, "name": "human_high_risk_changement_alert_vm", "doc": "高风险人工操作导致批量vm异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>2", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: staragent and not isGamma: 1 and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where ((exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')) or (exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))) or (exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','virt_report_exception_ping_failed') and warningLevel ='fatal') or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')", "exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))", "exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','virt_report_exception_ping_failed') and warningLevel ='fatal'", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "staragent_changement_alert", "doc": "高风险staragent操作导致批量异常", "end": 0, "interval": 300, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>2", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 305}, {"query": "* and log_source: staragent and not isGamma: 1 and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid, gcLevel from log   where (exceptionName in ('vm_iohang_start') and warningLevel ='fatal') or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_iohang_start') and warningLevel ='fatal'", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')"], "enable": true, "notifyEnable": true, "name": "staragent_changement_alert_vm", "doc": "高风险staragent操作导致批量vm异常", "end": 0, "interval": 300, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>4", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 305}, {"query": "* and log_source: xdcDeployHitRule and not isGamma: 1 and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where ((exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')) or (exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))) or (exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','virt_report_exception_ping_failed') and warningLevel ='fatal') or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')", "exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))", "exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','virt_report_exception_ping_failed') and warningLevel ='fatal'", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "xdc_rule_changement_alert", "doc": "运维规则变更导致批量异常", "end": 0, "interval": 300, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>2", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 305}, {"query": "* and log_source: xdcDeployHitRule and not isGamma: 1 and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vm_iohang_start') and warningLevel ='fatal') or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_iohang_start') and warningLevel ='fatal'", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')"], "enable": true, "notifyEnable": true, "name": "xdc_rule_changement_alert_vm", "doc": "运维规则变更导致批量vm异常", "end": 0, "interval": 300, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>4", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 305}, {"query": "* and log_source: staragent and not isGamma: 1 and window_size: 060005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('cloudops_ops_flow_limit')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('cloudops_ops_flow_limit')"], "enable": true, "notifyEnable": true, "name": "xdc_changement_flow_control_alert", "doc": "xdc变更操作导致批量流控", "end": 0, "interval": 600, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>2", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 605}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: EcsSpool.Spool# or plan_id:SpoolTools.SpoolMon#) and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('xdragon_cn_hang_or_down','tdc_start_failed','nc_host_mem_less','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel ='fatal') or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('td_connector','tdc_admin','tdc_upgrade_tool')) or (exceptionName in ('spoold_inspector_service_spooladm_connect_check','spoold_inspector_service_spoold_alive_check','spoold_inspector_io_hang_check','spoold_inspector_io_error_check')) or (exceptionName in ('key_process_segfault') and additionalInfo in ('td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','eal-intr-thread')) or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('xdragon_cn_hang_or_down','tdc_start_failed','nc_host_mem_less','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel ='fatal'", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('td_connector','tdc_admin','tdc_upgrade_tool')", "exceptionName in ('spoold_inspector_service_spooladm_connect_check','spoold_inspector_service_spoold_alive_check','spoold_inspector_io_hang_check','spoold_inspector_io_error_check')", "exceptionName in ('key_process_segfault') and additionalInfo in ('td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','eal-intr-thread')", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "spool_changement_alert", "doc": "spool发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: EcsSpool.Spool#) and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,productName,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart, aliUid, gcLevel, product_name as productName from log   where (exceptionName in ('vm_iohang_start') and warningLevel ='fatal') or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) or (exceptionName in ('blkpmd_check_failed_stop_disk_queue') and warningLevel in ('critical')) or (exceptionName in ('vm_guest_kernel_double_fault')) or (exceptionName in ('vhost_blk_start_failed_too_many')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_iohang_start') and warningLevel ='fatal'", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')", "exceptionName in ('blkpmd_check_failed_stop_disk_queue') and warningLevel in ('critical')", "exceptionName in ('vm_guest_kernel_double_fault')", "exceptionName in ('vhost_blk_start_failed_too_many')"], "enable": true, "notifyEnable": true, "name": "spool_changement_alert_vm", "doc": "spool发布导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: EcsTdc.Tdc# or plan_id: TdcTools.TdcTools# or plan_id: EcsTdcCommand.Command#) and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('taiji_ept_cnt_error','tdc_start_failed','nc_host_mem_less') and warningLevel ='fatal') or (exceptionName in ('fpga_ur_tlp_report') and warningLevel='critical') or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('td_connector','tdc_admin','tdc_upgrade_tool')) or (exceptionName in ('process_init_memory_failed')) or (exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd', 'nc_hang_task_detected', 'nc_hang_task_detected_conman','virt_report_exception_ping_failed') and warningLevel in ('fatal','critical','warning','low_warning')) or (exceptionName in ('key_process_segfault') and additionalInfo in ('td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','eal-intr-thread')) or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('taiji_ept_cnt_error','tdc_start_failed','nc_host_mem_less') and warningLevel ='fatal'", "exceptionName in ('fpga_ur_tlp_report') and warningLevel='critical'", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('td_connector','tdc_admin','tdc_upgrade_tool')", "exceptionName in ('process_init_memory_failed')", "exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd', 'nc_hang_task_detected', 'nc_hang_task_detected_conman','virt_report_exception_ping_failed') and warningLevel in ('fatal','critical','warning','low_warning')", "exceptionName in ('key_process_segfault') and additionalInfo in ('td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','eal-intr-thread')", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "tdc_changement_alert", "doc": "tdc发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: EcsTdc.Tdc# or plan_id: TdcTools.TdcTools# or plan_id: EcsTdcCommand.Command#) and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,productName,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart, aliUid, gcLevel, product_name as productName from log   where (exceptionName in ('guest_os_kernel_panic')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('guest_os_kernel_panic')"], "enable": true, "notifyEnable": true, "name": "tdc_changement_alert_warning", "doc": "tdc发布导致vm异常，警告但不熔断", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0 && (exceptionName!='guest_os_kernel_panic' || vmCount>2)", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: EcsTdc.Tdc# or plan_id: TdcTools.TdcTools# or plan_id: EcsTdcCommand.Command#) and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,productName,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart, aliUid, gcLevel, product_name as productName from log   where (exceptionName in ('vm_iohang_start') and warningLevel ='fatal') or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) or (exceptionName in ('blkpmd_check_failed_stop_disk_queue') and warningLevel in ('critical')) or (exceptionName in ('guest_os_kernel_panic') and warningLevel in ('fatal','critical')) or (exceptionName in ('vm_guest_kernel_double_fault')) or (exceptionName in ('vhost_blk_start_failed_too_many')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_iohang_start') and warningLevel ='fatal'", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')", "exceptionName in ('blkpmd_check_failed_stop_disk_queue') and warningLevel in ('critical')", "exceptionName in ('guest_os_kernel_panic') and warning<PERSON><PERSON>l in ('fatal','critical')", "exceptionName in ('vm_guest_kernel_double_fault')", "exceptionName in ('vhost_blk_start_failed_too_many')"], "enable": true, "notifyEnable": true, "name": "tdc_changement_alert_vm", "doc": "tdc发布导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0 && (exceptionName!='guest_os_kernel_panic' || vmCount>9)", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and (log_source: haitu) and not isGamma: 1 and window_size: 030005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where ((exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')) or (exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))) or (exceptionName in ('xdragon_cn_hang_or_down','tdc_start_failed','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel ='fatal') or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('td_connector','tdc_admin','tdc_upgrade_tool')) or (exceptionName in ('key_process_segfault') and additionalInfo in ('td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','eal-intr-thread')) or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')", "exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))", "exceptionName in ('xdragon_cn_hang_or_down','tdc_start_failed','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel ='fatal'", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('td_connector','tdc_admin','tdc_upgrade_tool')", "exceptionName in ('key_process_segfault') and additionalInfo in ('td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','eal-intr-thread')", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "haitu_changement_alert", "doc": "haitu发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "isHardwareIssue==0 && ncCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and (log_source: haitu) and not isGamma: 1 and (window_size: 030005 or window_size: 010005) and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,productName,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart, aliUid, gcLevel, product_name as productName from log   where (exceptionName in ('vm_iohang_start') and warningLevel ='fatal') or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) or (exceptionName in ('vm_guest_kernel_double_fault')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_iohang_start') and warningLevel ='fatal'", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')", "exceptionName in ('vm_guest_kernel_double_fault')"], "enable": true, "notifyEnable": true, "name": "haitu_changement_alert_vm", "doc": "haitu发布导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: xdragon_hot_upgrade_iohub_2.xdragon_hot_upgrade_iohub_2# or xdragon_hot_upgrade_iohub_2.Command# or xdragon_hot_upgrade_iohub_2.moc15_iohub_seu# or iohub_status_ctrl.set_pcie_aer_mask# or iohub_status_ctrl.update_iohub_check#) and window_size: 010005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount,cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,ncIp))) as timeCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_reboot','tdc_start_failed','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel ='fatal') or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-pcie','iohub-pcie-poll','iohub-bridge','iohub-engine','iohub-mctp','iohub-underlay','dpdkavs','lcore-slave','fpx-daemon','netframe','td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','pangu_povserver','pangu_superviso','td_connector','spoold','tdc_admin','tdc_upgrade_tool','pangu_povserver','isov-moc','libvirtd','eal-intr-thread','system_crash')) or (exceptionName='physical_machine_kernel_issue' and warningLevel = 'critical') or (exceptionName='nc_load_exception' and warningLevel='warning') or (exceptionName in ('virtio_mtu_drop')) or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_reboot','tdc_start_failed','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel ='fatal'", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-pcie','iohub-pcie-poll','iohub-bridge','iohub-engine','iohub-mctp','iohub-underlay','dpdkavs','lcore-slave','fpx-daemon','netframe','td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','pangu_povserver','pangu_superviso','td_connector','spoold','tdc_admin','tdc_upgrade_tool','pangu_povserver','isov-moc','libvirtd','eal-intr-thread','system_crash')", "exceptionName='physical_machine_kernel_issue' and warningLevel = 'critical'", "exceptionName='nc_load_exception' and warningLevel='warning'", "exceptionName in ('virtio_mtu_drop')", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "iohub2_upgrade_alert", "doc": "xdragon_hot_upgrade_iohub_2 发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0 && (exceptionName!='virtio_mtu_drop' || timeCount>2) && (exceptionName!='nc_load_exception' || ncCount>1)", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: xdragon_hot_upgrade_iohub_2.xdragon_hot_upgrade_iohub_2# or xdragon_hot_upgrade_iohub_2.Command# or xdragon_hot_upgrade_iohub_2.moc15_iohub_seu# or iohub_status_ctrl.set_pcie_aer_mask# or iohub_status_ctrl.update_iohub_check#) and window_size: 010005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,productName,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart, aliUid, gcLevel, product_name as productName from log   where (exceptionName in ('vm_iohang_start') and warningLevel ='fatal') or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-ctrl')) or (exceptionName in ('vm_arp_timeout_event_increased','vm_uptime_drop_event_increased','vm_guest_kernel_double_fault')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_iohang_start') and warningLevel ='fatal'", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-ctrl')", "exceptionName in ('vm_arp_timeout_event_increased','vm_uptime_drop_event_increased','vm_guest_kernel_double_fault')"], "enable": true, "notifyEnable": true, "name": "iohub2_upgrade_alert_vm", "doc": "xdragon_hot_upgrade_iohub_2 发布导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: xdragon_hot_upgrade_fpga_public.Command# or plan_id: xdragon_hot_upgrade_fpga_public.Command25# or plan_id: xdragon_hot_upgrade_fpga_public.Command1p5# or plan_id: xdragon_hot_upgrade_fpga_public.Commandv5# or plan_id: xdragon_hot_upgrade_fpga_public.Commandmoc25#) and window_size: 030005  and not exceptionName: null and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where ((exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')) or (exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_reboot','tdc_start_failed','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel ='fatal') or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-pcie','iohub-pcie-poll','iohub-bridge','iohub-engine','iohub-mctp','iohub-underlay','dpdkavs','lcore-slave','fpx-daemon','netframe','td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','pangu_povserver','pangu_superviso','td_connector','spoold','tdc_admin','tdc_upgrade_tool','pangu_povserver','isov-moc','libvirtd','eal-intr-thread')) or (exceptionName='physical_machine_kernel_issue' and warningLevel = 'critical') or (exceptionName = 'core_dump_generated' and additionalInfo='system_crash' and reason='cn_process') or (exceptionName='xdragon_hot_upgrade_fpga_down_event' and warningLevel = 'fatal' and cores>0) or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')", "exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_reboot','tdc_start_failed','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel ='fatal'", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-pcie','iohub-pcie-poll','iohub-bridge','iohub-engine','iohub-mctp','iohub-underlay','dpdkavs','lcore-slave','fpx-daemon','netframe','td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','pangu_povserver','pangu_superviso','td_connector','spoold','tdc_admin','tdc_upgrade_tool','pangu_povserver','isov-moc','libvirtd','eal-intr-thread')", "exceptionName='physical_machine_kernel_issue' and warningLevel = 'critical'", "exceptionName = 'core_dump_generated' and additionalInfo='system_crash' and reason='cn_process'", "exceptionName='xdragon_hot_upgrade_fpga_down_event' and warningLevel = 'fatal' and cores>0", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "moc20_fpga_changement_alert", "doc": "xdragon_hot_upgrade_fpga_public  发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>1", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}, {"expression": "{{ncCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: xdragon_hot_upgrade_fpga_public.Command# or plan_id: xdragon_hot_upgrade_fpga_public.Command25# or plan_id: xdragon_hot_upgrade_fpga_public.Command1p5# or plan_id: xdragon_hot_upgrade_fpga_public.Commandv5# or plan_id:xdragon_hot_upgrade_fpga_public.Commandmoc25#) and window_size: 030005  and not exceptionName: null and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,productName,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount, cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart, aliUid, gcLevel, product_name as productName from log   where (exceptionName in ('vm_iohang_start') and warningLevel ='fatal') or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-ctrl')) or (exceptionName in ('vm_arp_timeout_event_increased','vm_guest_kernel_double_fault')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_iohang_start') and warningLevel ='fatal'", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-ctrl')", "exceptionName in ('vm_arp_timeout_event_increased','vm_guest_kernel_double_fault')"], "enable": true, "notifyEnable": true, "name": "moc20_fpga_changement_alert_vm", "doc": "xdragon_hot_upgrade_fpga_public  发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>1 && vmCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}, {"expression": "ncCount>0 && vmCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: xdragon_hot_upgrade_fpga_public.Command# or plan_id: xdragon_hot_upgrade_fpga_public.Command25# or plan_id: xdragon_hot_upgrade_fpga_public.Command1p5# or plan_id: xdragon_hot_upgrade_fpga_public.Commandv5#) and window_size: 030005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where ((exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')) or (exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_reboot','tdc_start_failed','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel ='fatal') or (exceptionName='xdragon_hot_upgrade_fpga_down_event' and warningLevel = 'fatal' and cores>0) or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('tdc_admin','td_connector','tdc_upgrade_tool','libvirtd','isov-moc','iohub-pcie','iohub-pcie-poll','pangu_povserver','dpdkavs','lcore-slave')) or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')", "exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_reboot','tdc_start_failed','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel ='fatal'", "exceptionName='xdragon_hot_upgrade_fpga_down_event' and warningLevel = 'fatal' and cores>0", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('tdc_admin','td_connector','tdc_upgrade_tool','libvirtd','isov-moc','iohub-pcie','iohub-pcie-poll','pangu_povserver','dpdkavs','lcore-slave')", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "moc_fpga_changement_with_warning_alert", "doc": "xdragon_hot_upgrade_fpga_public  发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>1", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}, {"expression": "{{ncCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: xdragon_hot_upgrade_fpga_public.Command# or plan_id: xdragon_hot_upgrade_fpga_public.Command25# or plan_id: xdragon_hot_upgrade_fpga_public.Command1p5# or plan_id: xdragon_hot_upgrade_fpga_public.Commandv5# or plan_id: xdragon_hot_upgrade_fpga_public.Commandmoc25#) and window_size: 030005  and not exceptionName: null and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName='xdragon_hot_upgrade_fpga_down_event' and warningLevel in ('fatal','critical') and cores>0) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName='xdragon_hot_upgrade_fpga_down_event' and warningLevel in ('fatal','critical') and cores>0"], "enable": false, "notifyEnable": false, "name": "moc20_fpga_changement_alert_downtime", "doc": "xdragon_hot_upgrade_fpga_public 发布导致downtime异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>1 && coreAliUidSum>1", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}, {"expression": "ncCount>0 && coreAliUidSum>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: xdragon_hot_upgrade_fpga_public.Command#) and window_size: 030005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,productName,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount, cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart, aliUid, gcLevel, product_name as productName from log   where (exceptionName in ('vm_iohang_start') and warningLevel ='fatal') or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-ctrl')) or (exceptionName in ('vm_arp_timeout_event_increased','vm_guest_kernel_double_fault')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_iohang_start') and warningLevel ='fatal'", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-ctrl')", "exceptionName in ('vm_arp_timeout_event_increased','vm_guest_kernel_double_fault')"], "enable": true, "notifyEnable": true, "name": "moc_fpga_changement_with_warning_alert_vm", "doc": "xdragon_hot_upgrade_fpga_public  发布导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>1 && vmCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}, {"expression": "ncCount>0 && vmCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji_vm and not isGamma: 1 and not rollingtype: 01 and plan_id: EcsVirtVMUpdateEngine.HotUpgradeCmd# and window_size: 010005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_reboot','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel ='fatal') or (exceptionName in ('host_mem_less_qemu_mem_high')  and warningLevel in ('critical','fatal')) or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('isov-moc','qemu-kvm')) or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_reboot','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel ='fatal'", "exceptionName in ('host_mem_less_qemu_mem_high')  and warningLevel in ('critical','fatal')", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('isov-moc','qemu-kvm')", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "qemu_hot_upgrade_alert", "doc": "qemu热升级发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0 && (exceptionName!='core_dump_generated' || ncCount>1)", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji_vm and not isGamma: 1 and not rollingtype: 01 and plan_id: EcsVirtVMUpdateEngine.HotUpgradeCmd# and window_size: 010005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount, cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vm_iohang_start','vmexit_exception_vm_hang') and warningLevel ='fatal') or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) or (exceptionName in ('vm_paused_exception','vm_livemigrate_exception')  and warningLevel in ('critical','fatal')) or (exceptionName in ('qemu_hot_upgrade_down_time_event') and (warningLevel in ('fatal') or reason='gshellcheck_is_NOT_ok_after_update')) or (exceptionName in ('vm_pasued_resume_check_ntp','cloudassitant_heartbeat_loss_high')  and warningLevel ='critical') or (exceptionName in  ('vm_arp_timeout_event_increased','vm_guest_kernel_double_fault')) or (exceptionName in ('active_vm_ops_event') and additionalInfo like ('%0x30000000001010%')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_iohang_start','vmexit_exception_vm_hang') and warningLevel ='fatal'", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')", "exceptionName in ('vm_paused_exception','vm_livemigrate_exception')  and warningLevel in ('critical','fatal')", "exceptionName in ('qemu_hot_upgrade_down_time_event') and (warningLevel in ('fatal') or reason='gshellcheck_is_NOT_ok_after_update')", "exceptionName in ('vm_pasued_resume_check_ntp','cloudassitant_heartbeat_loss_high')  and warningLevel ='critical'", "exceptionName in  ('vm_arp_timeout_event_increased','vm_guest_kernel_double_fault')", "exceptionName in ('active_vm_ops_event') and additionalInfo like ('%0x30000000001010%')"], "enable": true, "notifyEnable": true, "name": "qemu_hot_upgrade_alert_vm", "doc": "qemu热升级发布导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "(exceptionName == 'vm_crash_event' && vmCount > 0) || (exceptionName == 'qemu_hot_upgrade_down_time_event' && vmCount > 0) || (exceptionName == 'core_dump_generated' && vmCount > 1 && ncCount > 1) || vmCount > 2", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji_vm and not isGamma: 1 and not rollingtype: 01 and plan_id: EcsVirtVMUpdateEngine.HotUpgradeCmd# and window_size: 010005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('qemu_hot_upgrade_down_time_event') and warningLevel in ('critical')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('qemu_hot_upgrade_down_time_event') and warningLevel in ('critical')"], "enable": true, "notifyEnable": true, "name": "qemu_hot_upgrade_alert_vm_dryrun", "doc": "qemu热升级发布导致vm异常,dryrun不熔断", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount > 2", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: ais-hwai-edac.CONF#  or plan_id:system_service.osman_ais_hwai_edac_install or plan_id:system_service.osman_ais_hwai_edac_install_for_cn) and window_size: 010005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('nc_hang_task_detected_conman','nc_hang_task_detected')  and warningLevel ='fatal') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('nc_hang_task_detected_conman','nc_hang_task_detected')  and warningLevel ='fatal'"], "enable": true, "notifyEnable": true, "name": "edac_changement_alert", "doc": "edac发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: aswChangement and plan_id: aswChangement and window_size: 030005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')) or (exceptionName in ('pingmesh_latency_error_in_idc')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')", "exceptionName in ('pingmesh_latency_error_in_idc')"], "enable": true, "notifyEnable": true, "name": "asw_changement_alert", "doc": "asw变更发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "warning"}, {"expression": "isHardwareIssue==0 && (ncCount>1 || taskSum>2)", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: EcsPync.Pync# or plan_id: EcsNcRs.NcRs-sr# or log_source:ncrs) and (window_size: 010005 or (window_size: 060005 and exceptionName: nc_connection_timeout)) and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,ncIp))) as timeCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('pync_exit_exception','pync_heartbeat_error')  and biz_status in ('free','mlock','locked','') and warningLevel in ('critical','fatal')) or (exceptionName in ('avs_monitor_warning') and reason != 'check_vswitchd_rpc_timeout') or (exceptionName in ('nc_connection_timeout') and (cores > 0 or biz_status='free')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('pync_exit_exception','pync_heartbeat_error')  and biz_status in ('free','mlock','locked','') and warningLevel in ('critical','fatal')", "exceptionName in ('avs_monitor_warning') and reason != 'check_vswitchd_rpc_timeout'", "exceptionName in ('nc_connection_timeout') and (cores > 0 or biz_status='free')"], "enable": true, "notifyEnable": true, "name": "pync_exception", "doc": "pync发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>1 && ((exceptionName!='pync_exit_exception' && exceptionName!='nc_connection_timeout') || (ncCount>2 && timeCount>1)) && (exceptionName!='avs_monitor_warning' || ncCount>0)", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: EcsPync.Pync# or plan_id: EcsNcRs.NcRs-sr# or log_source:ncrs) and window_size: 030005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('pync_prepare_vm_exception')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('pync_prepare_vm_exception')"], "enable": true, "notifyEnable": true, "name": "pync_exception_vm", "doc": "pync发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: virt-deploy.virt-upgrade-main# or plan_id: virt-deploy.virt-upgrade-bugfix# or plan_id:kvm-deploy-test.kvm-deploy-test# ) and (window_size: 010005 or window_size: 015005) and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, additionalInfo, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime, nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart,additionalInfo from log   where (exceptionName in ('core_dump_generated') and additionalInfo in ('libvirtd','isov-moc','qemu-kvm','agent_hook')) or (exceptionName = 'agent_postcheck_failed' and additionalInfo in ('deploy_package_check')) or (exceptionName = 'core_dump_generated' and additionalInfo='system_crash') or (exceptionName='cgroup_mem_limitation' and reason='libvirtd') or (exceptionName in ('taiji_ept_cnt_error','memory_fragmentation','xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','virt_report_exception_ping_failed') and warningLevel = 'fatal') or (exceptionName in ('nc_down_alert','cn_hang_task_detected','vm_lost_or_remained_error','virt_upgrade_monitor_error')) or (exceptionName = 'ag_nc_icmp_latency_increase' and warningLevel = 'warning' and biz_status = 'free') or (exceptionName = 'virtio_blk_create_device_failed' and warningLevel = 'warning') or (exceptionName = 'taiji_cpu_schedevent' and warningLevel in ('critical','fatal')) or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('core_dump_generated') and additionalInfo in ('libvirtd','isov-moc','qemu-kvm','agent_hook')", "exceptionName = 'agent_postcheck_failed' and additionalInfo in ('deploy_package_check')", "exceptionName = 'core_dump_generated' and additionalInfo='system_crash'", "exceptionName='cgroup_mem_limitation' and reason='libvirtd'", "exceptionName in ('taiji_ept_cnt_error','memory_fragmentation','xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','virt_report_exception_ping_failed') and warningLevel = 'fatal'", "exceptionName in ('nc_down_alert','cn_hang_task_detected','vm_lost_or_remained_error','virt_upgrade_monitor_error')", "exceptionName = 'ag_nc_icmp_latency_increase' and warningLevel = 'warning' and biz_status = 'free'", "exceptionName = 'virtio_blk_create_device_failed' and warningLevel = 'warning'", "exceptionName = 'taiji_cpu_schedevent' and warningLevel in ('critical','fatal')", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "virt_changement_alert", "doc": "虚拟化发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0 && exceptionName=='agent_postcheck_failed'", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "low_warning"}, {"expression": "isHardwareIssue==0 && ncCount>0 && (exceptionName != 'virtio_blk_create_device_failed' || ncCount>4) && (exceptionName != 'core_dump_generated' || ncCount>1) && (exceptionName != 'agent_postcheck_failed' || taskSum>8 || ncCount>9) && (exceptionName != 'taiji_cpu_schedevent' || ncCount>1)", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: virt-deploy.virt-upgrade-main# or plan_id: virt-deploy.virt-upgrade-bugfix# or plan_id:kvm-deploy-test.kvm-deploy-test# ) and (window_size: 010005 or window_size: 015005) and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, additionalInfo, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime, nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart,additionalInfo from log   where (exceptionName = 'agent_postcheck_failed' and additionalInfo in ('microbvt_check')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName = 'agent_postcheck_failed' and additionalInfo in ('microbvt_check')"], "enable": true, "notifyEnable": true, "name": "virt_changement_alert_microbvt", "doc": "虚拟化发布导致microbvt异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "isHardwareIssue==0 && ncCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: virt-deploy.virt-upgrade-main# or plan_id: virt-deploy.virt-upgrade-bugfix# ) and (window_size: 010005 or window_size: 015005 or (window_size: 030005 and exceptionName: vm_panic_from_guest_asist))  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount, cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vm_arp_timeout_event_increased','vm_guest_kernel_double_fault')) or (exceptionName in ('vm_panic_from_guest_asist')) or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) or (exceptionName in ('vmexit_exception_vm_hang') and warningLevel in ('fatal')) or (exceptionName in ('core_dump_generated') and additionalInfo in ('iohub-ctrl')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_arp_timeout_event_increased','vm_guest_kernel_double_fault')", "exceptionName in ('vm_panic_from_guest_asist')", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')", "exceptionName in ('vmexit_exception_vm_hang') and warningLevel in ('fatal')", "exceptionName in ('core_dump_generated') and additionalInfo in ('iohub-ctrl')"], "enable": true, "notifyEnable": true, "name": "virt_changement_alert_vm", "doc": "虚拟化发布导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0 && (exceptionName != 'core_dump_generated' || vmCount>1) && (exceptionName != 'vmexit_exception_vm_hang' || vmCount>2) && (exceptionName != 'vm_panic_from_guest_asist' || vmCount>2)", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and plan_id: EcsVtpm.swtpm_sr#  and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('libvirtd','qemu-kvm')) or (exceptionName='cgroup_mem_limitation' and reason='libvirtd') or (exceptionName in ('memory_fragmentation','xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','virt_report_exception_ping_failed') and warningLevel = 'fatal') or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('libvirtd','qemu-kvm')", "exceptionName='cgroup_mem_limitation' and reason='libvirtd'", "exceptionName in ('memory_fragmentation','xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','virt_report_exception_ping_failed') and warningLevel = 'fatal'", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "EcsVtpm_changement_alert", "doc": "Vtpm发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and plan_id: EcsVtpm.swtpm_sr#  and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vm_arp_timeout_event_increased')) or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-ctrl')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_arp_timeout_event_increased')", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-ctrl')"], "enable": true, "notifyEnable": true, "name": "EcsVtpm_changement_alert_vm", "doc": "Vtpm发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: ecs_network.libvswitch_sr#  or plan_id: qt_test_service.qt_cp_agent# or plan_id:  ecs_network.Command# or plan_id: ecs_network.Upgrade#) and window_size: 015005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,ncIp))) as timeCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where ((exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')) or (exceptionName in ('avs_monitor_warning') and reason != 'check_vswitchd_rpc_timeout') or (exceptionName='process_oom_exception' and warningLevel = 'critical') or (exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel = 'fatal') or (exceptionName in ('fpga_network_error','inspect_fpga_error_cause_avs_error','virtio_mtu_drop','virtio_vring_err')) or (exceptionName = 'core_dump_generated' and additionalInfo='system_crash' and reason='cn_process') or (exceptionName in ('core_dump_generated') and additionalInfo in ('dpdkavs','lcore-slave')) or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')", "exceptionName in ('avs_monitor_warning') and reason != 'check_vswitchd_rpc_timeout'", "exceptionName='process_oom_exception' and warningLevel = 'critical'", "exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel = 'fatal'", "exceptionName in ('fpga_network_error','inspect_fpga_error_cause_avs_error','virtio_mtu_drop','virtio_vring_err')", "exceptionName = 'core_dump_generated' and additionalInfo='system_crash' and reason='cn_process'", "exceptionName in ('core_dump_generated') and additionalInfo in ('dpdkavs','lcore-slave')", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "avs_upgrade_alert", "doc": "avs发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0 && ((exceptionName != 'fpga_network_error' && exceptionName != 'avs_hotup_vport_downtime') || ncCount > 1) && ((exceptionName!='virtio_mtu_drop')||timeCount>2)", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: ecs_network.libvswitch_sr#  or plan_id: qt_test_service.qt_cp_agent# or plan_id:  ecs_network.Command# or plan_id: ecs_network.Upgrade#) and window_size: 180005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,ncIp))) as timeCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('core_dump_generated') and additionalInfo in ('dpdkavs','lcore-slave')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('core_dump_generated') and additionalInfo in ('dpdkavs','lcore-slave')"], "enable": true, "notifyEnable": true, "name": "avs_upgrade_alert_long", "doc": "avs发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: ecs_network.libvswitch_sr#  or plan_id: qt_test_service.qt_cp_agent# or plan_id:  ecs_network.Command# or plan_id: ecs_network.Upgrade#) and window_size: 015005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vm_arp_timeout_event_increased','vm_guest_kernel_double_fault','vm_iohang_start')) or (exceptionName in ('vm_retry_after_dpdkavs_restart') and warningLevel in ('fatal')) or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_arp_timeout_event_increased','vm_guest_kernel_double_fault','vm_iohang_start')", "exceptionName in ('vm_retry_after_dpdkavs_restart') and warningLevel in ('fatal')", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')"], "enable": true, "notifyEnable": true, "name": "avs_upgrade_alert_vm", "doc": "avs发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0 && ((exceptionName != 'vm_iohang_start') || vmCount > 1)", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: ecs_network.libvswitch_sr#  or plan_id: qt_test_service.qt_cp_agent# or plan_id:  ecs_network.Command# or plan_id: ecs_network.Upgrade#) and window_size: 030005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('avs_hotup_vport_downtime') and warningLevel = 'fatal') or (exceptionName in ('vm_performance_metric_anomaly') and additionalInfo in ('VmNetworkRetryMetric/rx_retry',' VmNetworkRetryMetric/tx_retry')) or (exceptionName in ('dpdk_cpu0_metric_anomaly','vm_retry_metric_anomaly','nc_vxlan_drop_after_dpdkavs_restart')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('avs_hotup_vport_downtime') and warningLevel = 'fatal'", "exceptionName in ('vm_performance_metric_anomaly') and additionalInfo in ('VmNetworkRetryMetric/rx_retry',' VmNetworkRetryMetric/tx_retry')", "exceptionName in ('dpdk_cpu0_metric_anomaly','vm_retry_metric_anomaly','nc_vxlan_drop_after_dpdkavs_restart')"], "enable": true, "notifyEnable": true, "name": "avs_upgrade_alert_dryrun", "doc": "avs发布导致异常,dryrun不熔断", "end": 0, "interval": 3600, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>2 || (exceptionName=='dpdk_cpu0_metric_anomaly' && ncCount>0) || (exceptionName=='vm_retry_metric_anomaly' && ncCount>1)", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 3605}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: ecs_network.libvswitch_sr#  or plan_id: qt_test_service.qt_cp_agent# or plan_id:  ecs_network.Command# or plan_id: ecs_network.Upgrade#) and window_size: 030005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount, cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vm_retry_after_dpdkavs_restart') and region not like '%zhangjiakou%' and warningLevel in ('critical','fatal')) or (exceptionName in ('vm_hw_ratio_drop_after_dpdkavs_restart') and warningLevel in ('critical','fatal')) or (exceptionName in ('nc_vxlan_drop_after_dpdkavs_restart')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_retry_after_dpdkavs_restart') and region not like '%zhangjiakou%' and warningLevel in ('critical','fatal')", "exceptionName in ('vm_hw_ratio_drop_after_dpdkavs_restart') and warningLevel in ('critical','fatal')", "exceptionName in ('nc_vxlan_drop_after_dpdkavs_restart')"], "enable": true, "notifyEnable": true, "name": "avs_upgrade_alert_vm_retry", "doc": "AVS发布导致网络指标异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}, {"expression": "ncCount>2 || serviceBatchSum>2", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: ecs_network.libvswitch_sr#  or plan_id: qt_test_service.qt_cp_agent# or plan_id:  ecs_network.Command# or plan_id: ecs_network.Upgrade#) and window_size: 030005 and exceptionName: vm_retry_after_dpdkavs_restart and not exceptionName: null and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, 'vm_retry_after_dpdkavs_restart_special' as exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount, cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vm_retry_after_dpdkavs_restart','vm_retry_after_dpdkavs_restart_special') and region like '%zhangjiakou%' and warningLevel in ('critical','fatal')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_retry_after_dpdkavs_restart','vm_retry_after_dpdkavs_restart_special') and region like '%zhangjiakou%' and warningLevel in ('critical','fatal')"], "enable": false, "notifyEnable": true, "name": "avs_upgrade_alert_vm_retry_special", "doc": "AVS发布导致网络指标异常(张家口)", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}, {"expression": "ncCount>5 || serviceBatchSum>5", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: EcsLocalStorage.EcsLocalStorage-sr#) and window_size: 015005  and not exceptionName: null and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg(substr(exceptionTime,1,16)) OVER(PARTITION BY taskId,serviceName,exceptionName,instanceId))) as timeCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart, instanceId from log  where ((exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')) or (exceptionName='process_oom_exception' and warningLevel = 'critical') or (exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel = 'fatal') or (exceptionName = 'core_dump_generated' and additionalInfo='system_crash' and reason='cn_process') or (exceptionName = 'ecs_local_storage_health_exception') or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert') or (exceptionName = 'ag_nc_icmp_latency_increase' and biz_status='free' and warningLevel = 'warning')", "exceptionName='process_oom_exception' and warningLevel = 'critical'", "exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel = 'fatal'", "exceptionName = 'core_dump_generated' and additionalInfo='system_crash' and reason='cn_process'", "exceptionName = 'ecs_local_storage_health_exception'", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "ecs_local_storage_upgrade_alert", "doc": "EcsLocalStorage发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "isHardwareIssue==0 && ncCount>0 && (exceptionName!='ecs_local_storage_health_exception'||timeCount>2)", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: EcsLocalStorage.EcsLocalStorage-sr#) and window_size: 015005  and not exceptionName: null and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vm_arp_timeout_event_increased','vm_guest_kernel_double_fault','vm_iohang_start')) or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_arp_timeout_event_increased','vm_guest_kernel_double_fault','vm_iohang_start')", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')"], "enable": true, "notifyEnable": true, "name": "ecs_local_storage_upgrade_alert_vm", "doc": "EcsLocalStorage发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and plan_id: pangu-pov.PanguPovserver# and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_reboot','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel ='fatal') or (exceptionName in ('core_dump_generated') and additionalInfo in ('pangu_povserver')) or (exceptionName='physical_machine_kernel_issue' and warningLevel = 'critical') or (exceptionName='nc_load_exception' and warningLevel='warning') or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_reboot','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel ='fatal'", "exceptionName in ('core_dump_generated') and additionalInfo in ('pangu_povserver')", "exceptionName='physical_machine_kernel_issue' and warningLevel = 'critical'", "exceptionName='nc_load_exception' and warningLevel='warning'", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "pangu_pov_changement_alert", "doc": "pangu_pov发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and plan_id: pangu-pov.PanguPovserver# and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vm_iohang_start') and warningLevel ='fatal') or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_iohang_start') and warningLevel ='fatal'", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')"], "enable": true, "notifyEnable": true, "name": "pangu_pov_changement_alert_vm", "doc": "pangu_pov发布导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and (log_source: woodpecker or log_source: woodpecker-pro ) and not isGamma: 1 and window_size: 030005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName = 'nc_down_alert') or (exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))) or (exceptionName in ('xdragon_cn_hang_or_down','virt_report_exception_ping_failed') and warningLevel in ('critical','fatal')) or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName = 'nc_down_alert'", "exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))", "exceptionName in ('xdragon_cn_hang_or_down','virt_report_exception_ping_failed') and warningLevel in ('critical','fatal')", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "woodpecker_changement_alert", "doc": "woodpecker发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and (log_source: woodpecker or log_source: woodpecker-pro ) and not isGamma: 1  and plan_id: PSU and window_size: 090005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName = 'nc_down_alert') or (exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))) or (exceptionName in ('xdragon_cn_hang_or_down','virt_report_exception_ping_failed') and warningLevel in ('critical','fatal')) or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName = 'nc_down_alert'", "exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))", "exceptionName in ('xdragon_cn_hang_or_down','virt_report_exception_ping_failed') and warningLevel in ('critical','fatal')", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "woodpecker_PSU_changement_alert", "doc": "woodpecker PSU 发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and ((window_size: 010005 and (exceptionName: nc_down_alert or exceptionName: core_dump_generated or (exceptionName: ag_nc_icmp_latency_increase and warningLevel: warning)) and ((log_source: tianji  and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement) ) or (window_size: 030005 and (log_source: woodpecker or log_source: woodpecker-pro or log_source: opsx or log_source: rpm or log_source: ncrs)) ) and not isGamma: 1 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName = 'nc_down_alert') or (exceptionName = 'channel_monitor_for_dragonfly20') or (exceptionName = 'ag_nc_icmp_latency_increase' and warningLevel = 'warning' and biz_status = 'free') or (exceptionName = 'core_dump_generated' and additionalInfo='system_crash') and plan_id not in ('ecs_network.libvswitch_sr#','ecs_network.Command#','ecs_network.Upgrade#','xdragon_hot_upgrade_fpga_public.xdragon_hot_upgrade_fpga_public_moc20#','xdragon_hot_upgrade_fpga_public.Command#','xdragon_hot_upgrade_fpga_public.Command1p5#','xdragon_hot_upgrade_fpga_public.Commandv5#','virt-deploy.virt-upgrade-main#','virt-deploy.virt-upgrade-bugfix#','EcsLocalStorage.EcsLocalStorage-sr#') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName = 'nc_down_alert'", "exceptionName = 'channel_monitor_for_dragonfly20'", "exceptionName = 'ag_nc_icmp_latency_increase' and warningLevel = 'warning' and biz_status = 'free'", "exceptionName = 'core_dump_generated' and additionalInfo='system_crash'"], "enable": true, "notifyEnable": true, "name": "unexcepted_down", "doc": "发布导致非预期宕机或严重异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "isHardwareIssue==1 && ncCount>0", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "low_warning"}, {"expression": "isHardwareIssue==0 && ncCount>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and ( not plan_id: EcsNcResource.NcResourcePackage# ) and (window_size: 030005 or window_size: 010005) and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement or log_source: woodpecker or log_source: woodpecker-pro or log_source: opsx or log_source: rpm or log_source: ncrs) and not isGamma: 1 and (exceptionName: nc_hang_too_long or exceptionName:nc_hang_task_detected or exceptionName:nc_hang_task_detected_conman) and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))) or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') or (exceptionName in ('nc_hang_task_detected','nc_hang_task_detected_conman') and additionalInfo like '%Fatal fault event%') or (exceptionName in ('xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel = 'fatal') or (exceptionName in ('cn_hang_task_detected','physical_machine_kernel_issue') and warningLevel = 'fatal' and additionalInfo like '%vda-: block%') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'", "exceptionName in ('nc_hang_task_detected','nc_hang_task_detected_conman') and additionalInfo like '%Fatal fault event%'", "exceptionName in ('xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel = 'fatal'", "exceptionName in ('cn_hang_task_detected','physical_machine_kernel_issue') and warningLevel = 'fatal' and additionalInfo like '%vda-: block%'"], "enable": true, "notifyEnable": true, "name": "unexcepted_hang", "doc": "发布导致非预期夯机", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and (window_size: 030005 or window_size: 010005) and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: woodpecker or log_source: woodpecker-pro or log_source: opsx or log_source: rpm or log_source: ncrs) and not isGamma: 1 and (exceptionName: nc_hang_task_detected or exceptionName: cn_hang_task_detected) and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (position('qemu-kvm' in reason) > 0 or  position('qemu-system-x86' in reason) > 0 or  position('dragonball' in reason) > 0 or  position('dragonfly' in reason) > 0 or  position('/usr/bin/qemu-kvm' in reason) > 0 or  position('CPU-/KVM' in reason) > 0 or  position('pangu_chunkserv' in reason) > 0 or  position('river_server' in reason) > 0 or  position('td_connector' in reason) > 0 or  position('watchdog_tdc.py' in reason) > 0 or  position('/apsara/tdc/td_connector' in reason) > 0 or  position('vcpu-worker-' in reason) > 0 or  position('systemd' in reason) > 0 or  position('dpdkavs' in reason) > 0 or  position('lcore-slave' in reason) > 0 or  position('libvirtd' in reason) > 0 or  position('iohub-pcie-admi' in reason) > 0 or  position('virsh' in reason) > 0 or  position('river_admin' in reason) > 0 or  position('tdc_admin' in reason) > 0 or  position('avs-sysctl' in reason) > 0 or  position('network-pync-master' in reason) > 0 or  position('pync-master' in reason) > 0 or  position('avs_dhcpd' in reason) > 0 or  position('avs-vswitchd' in reason) > 0 or  position('vswctl' in reason) > 0 or position('vswapd' in reason) > 0 or position('vfio-irqfd-clea' in reason) > 0 or position('request-handler' in reason) > 0) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["position('qemu-kvm' in reason) > 0 or  position('qemu-system-x86' in reason) > 0 or  position('dragonball' in reason) > 0 or  position('dragonfly' in reason) > 0 or  position('/usr/bin/qemu-kvm' in reason) > 0 or  position('CPU-/KVM' in reason) > 0 or  position('pangu_chunkserv' in reason) > 0 or  position('river_server' in reason) > 0 or  position('td_connector' in reason) > 0 or  position('watchdog_tdc.py' in reason) > 0 or  position('/apsara/tdc/td_connector' in reason) > 0 or  position('vcpu-worker-' in reason) > 0 or  position('systemd' in reason) > 0 or  position('dpdkavs' in reason) > 0 or  position('lcore-slave' in reason) > 0 or  position('libvirtd' in reason) > 0 or  position('iohub-pcie-admi' in reason) > 0 or  position('virsh' in reason) > 0 or  position('river_admin' in reason) > 0 or  position('tdc_admin' in reason) > 0 or  position('avs-sysctl' in reason) > 0 or  position('network-pync-master' in reason) > 0 or  position('pync-master' in reason) > 0 or  position('avs_dhcpd' in reason) > 0 or  position('avs-vswitchd' in reason) > 0 or  position('vswctl' in reason) > 0 or position('vswapd' in reason) > 0 or position('vfio-irqfd-clea' in reason) > 0 or position('request-handler' in reason) > 0"], "enable": true, "notifyEnable": true, "name": "key_process_hang", "doc": "发布导致关键进程夯死", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and ((window_size: 010005 and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement or log_source: sysvm) ) or (window_size:030005 and log_source:woodpecker or log_source: opsx or log_source: rpm or log_source: ncrs)) and not isGamma: 1 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount, cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,instanceId))) as timeCount, cardinality(array_distinct(array_agg(aliUid) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as aliUidCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart, aliUid, gcLevel from log   where (exceptionName in ('vm_vsock_icmp_ping_loss','vm_arp_ping_rtt_too_long','start_vm_failed_xml','vm_unexpected_killed_by_other','vm_start_failed')) or (exceptionName in ('vm_guest_network_service_error') and warningLevel in ('critical')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_vsock_icmp_ping_loss','vm_arp_ping_rtt_too_long','start_vm_failed_xml','vm_unexpected_killed_by_other','vm_start_failed')", "exceptionName in ('vm_guest_network_service_error') and warningLevel in ('critical')"], "enable": true, "notifyEnable": true, "name": "unexcepted_vm_down", "doc": "发布导致vm启动失败或不可用", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>5 && ncCount>2 && aliUidCount>1 && (exceptionName!='vm_start_failed' || timeCount>1) && (exceptionName!='start_vm_failed_xml' || timeCount>1)", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and window_size: 010005 and (log_source: rpm or log_source: key_process_restart) and not isGamma: 1 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount, cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,instanceId))) as timeCount, cardinality(array_distinct(array_agg(aliUid) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as aliUidCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart, aliUid, gcLevel from log   where (exceptionName in ('vm_retry_metric_anomaly','xdc_rule_pausedVMRestart','vm_guest_kernel_double_fault','vm_arp_timeout_event_increased','nc_vxlan_drop_after_dpdkavs_restart')) or (exceptionName in ('guest_os_kernel_panic','cloudassitant_heartbeat_loss_high','vm_arp_increase','vm_crash_event') and warningLevel in ('fatal','critical')) or (exceptionName in ('vm_iohang_start') and warningLevel ='fatal') or (exceptionName in ('blkpmd_check_failed_stop_disk_queue','vm_pasued_resume_check_ntp') and warningLevel in ('critical')) or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-ctrl')) or (exceptionName in ('vm_paused_exception','vm_livemigrate_exception')  and warningLevel in ('critical','fatal')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_retry_metric_anomaly','xdc_rule_pausedVMRestart','vm_guest_kernel_double_fault','vm_arp_timeout_event_increased','nc_vxlan_drop_after_dpdkavs_restart')", "exceptionName in ('guest_os_kernel_panic','cloudassitant_heartbeat_loss_high','vm_arp_increase','vm_crash_event') and warningLevel in ('fatal','critical')", "exceptionName in ('vm_iohang_start') and warningLevel ='fatal'", "exceptionName in ('blkpmd_check_failed_stop_disk_queue','vm_pasued_resume_check_ntp') and warningLevel in ('critical')", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-ctrl')", "exceptionName in ('vm_paused_exception','vm_livemigrate_exception')  and warningLevel in ('critical','fatal')"], "enable": true, "notifyEnable": true, "name": "unexcepted_vm_down_s2", "doc": "发布导致vm黑盒判定指标异常【仅Dryrun不熔断】", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>1", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and (window_size: 030005 or window_size: 010005) and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement or log_source: sysvm or log_source: woodpecker or log_source: woodpecker-pro or log_source: opsx or log_source: rpm or log_source: ncrs) and not isGamma: 1 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,ncIp))) as timeCount, additionalInfo from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart, additionalInfo from log   where (exceptionName in ('vm_iohub_check_error_too_many_in_nc','vm_kick_lost_too_many_in_nc','single_nc_in_asw_ping_loss')) or (exceptionName in ('inspect_abnormal_long_process') and warningLevel in ('critical')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_iohub_check_error_too_many_in_nc','vm_kick_lost_too_many_in_nc','single_nc_in_asw_ping_loss')", "exceptionName in ('inspect_abnormal_long_process') and warningLevel in ('critical')"], "enable": true, "notifyEnable": true, "name": "performance_degrade_s1_alert", "doc": "发布导致nc性能受损S1", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>1 || taskSum>1", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and (window_size: 030005 or window_size: 010005) and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement or log_source: sysvm or log_source: woodpecker or log_source: woodpecker-pro or log_source: opsx or log_source: rpm or log_source: ncrs) and not isGamma: 1 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,ncIp))) as timeCount, additionalInfo from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart, additionalInfo from log   where (exceptionName in ('cn_memory_fragmentation','cn_dmar_write_fault','process_init_memory_failed','xdragon_cn_hang_too_long','nc_taiji_error')) or (exceptionName in ('xdragon_cn_hang_or_down','virt_report_exception_ping_failed') and warningLevel in ('critical','fatal')) or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') or (exceptionName in ('avs_monitor_warning') and reason != 'check_vswitchd_rpc_timeout') or (exceptionName in ('xdragon_monitor_fpga_exception','xdragon_monitor_fpga_exception_new') and reason like '%MOC2.0%' and additionalInfo like '%0x00008200%') or (exceptionName in ('core_dump_generated') and additionalInfo in ('iohub-pcie','iohub-pcie-poll','iohub-bridge','iohub-engine','iohub-mctp','iohub-underlay','dpdkavs','lcore-slave','fpx-daemon','netframe','td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','pangu_povserver','pangu_superviso','td_connector','spoold','tdc_admin','tdc_upgrade_tool','pangu_povserver','isov-moc','libvirtd','eal-intr-thread','system_crash','etcd','agent_hook','iohub-daemon','iohub','iohub-fwd','iohubtool')) or (exceptionName in ('key_process_segfault') and additionalInfo in ('td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','eal-intr-thread')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('cn_memory_fragmentation','cn_dmar_write_fault','process_init_memory_failed','xdragon_cn_hang_too_long','nc_taiji_error')", "exceptionName in ('xdragon_cn_hang_or_down','virt_report_exception_ping_failed') and warningLevel in ('critical','fatal')", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'", "exceptionName in ('avs_monitor_warning') and reason != 'check_vswitchd_rpc_timeout'", "exceptionName in ('xdragon_monitor_fpga_exception','xdragon_monitor_fpga_exception_new') and reason like '%MOC2.0%' and additionalInfo like '%0x00008200%'", "exceptionName in ('core_dump_generated') and additionalInfo in ('iohub-pcie','iohub-pcie-poll','iohub-bridge','iohub-engine','iohub-mctp','iohub-underlay','dpdkavs','lcore-slave','fpx-daemon','netframe','td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','pangu_povserver','pangu_superviso','td_connector','spoold','tdc_admin','tdc_upgrade_tool','pangu_povserver','isov-moc','libvirtd','eal-intr-thread','system_crash','etcd','agent_hook','iohub-daemon','iohub','iohub-fwd','iohubtool')", "exceptionName in ('key_process_segfault') and additionalInfo in ('td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','eal-intr-thread')"], "enable": true, "notifyEnable": true, "name": "performance_degrade_s2_alert", "doc": "发布导致nc性能受损S2", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0 && ((exceptionName != 'xdragon_cn_hang_or_down' && exceptionName!='controller_cpu_100') || timeCount > 2) && (exceptionName != 'cn_memory_fragmentation' || (serviceName != 'system_service.osman_intel_microcode_upgrade_origin' && serviceName != 'cpu_micro.osman_intel_microcode_upgrade_origin')) && (exceptionName != 'core_dump_generated' || additionalInfo != 'tdc_admin')", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "low_warning"}, {"expression": "(ncCount>2 || taskSum>2) && ((exceptionName != 'xdragon_cn_hang_or_down' && exceptionName!='controller_cpu_100') || timeCount > 2) && (exceptionName != 'cn_memory_fragmentation' || (serviceName != 'system_service.osman_intel_microcode_upgrade_origin' && serviceName != 'cpu_micro.osman_intel_microcode_upgrade_origin'))", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and (window_size: 030005 or window_size: 010005) and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement or log_source: sysvm or log_source: woodpecker or log_source: woodpecker-pro or log_source: opsx or log_source: rpm or log_source: ncrs) and not isGamma: 1 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,instanceId))) as timeCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vhost_blk_start_failed','vm_guest_kernel_double_fault','start_vm_failed_xml')) or (exceptionName in ('vm_pasued_resume_check_ntp') and warningLevel != 'low_warning') or (exceptionName in ('core_dump_generated') and additionalInfo in ('iohub_ctrl')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vhost_blk_start_failed','vm_guest_kernel_double_fault','start_vm_failed_xml')", "exceptionName in ('vm_pasued_resume_check_ntp') and warningLevel != 'low_warning'", "exceptionName in ('core_dump_generated') and additionalInfo in ('iohub_ctrl')"], "enable": true, "notifyEnable": true, "name": "performance_degrade_s2_alert_vm", "doc": "发布导致vm性能受损S2", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0 && ncCount>0 && (exceptionName!='vhost_blk_start_failed' || timeCount>1) && (exceptionName!='start_vm_failed_xml' || timeCount>1)", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "low_warning"}, {"expression": "(vmCount>2 || taskSum>4) && ncCount>0 && (exceptionName!='vhost_blk_start_failed' || timeCount>1) && (exceptionName!='start_vm_failed_xml' || timeCount>1)", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and (window_size: 030005 or window_size: 010005) and (((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement or log_source: woodpecker or log_source: woodpecker-pro or log_source: opsx or log_source: rpm or log_source: ncrs) and not isGamma: 1 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ) ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime, substr(exceptionTime,1,16) as aggregateTime, ncIp, serviceName, taskId, cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,ncIp))) as timeCount from (select distinct log_source as logSource, exceptionName,  nc_ip as ncIp, plan_id as serviceName, exceptionTime,taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('libvirt_connect_failed','xdragon_cn_ssh_latency_high')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('libvirt_connect_failed','xdragon_cn_ssh_latency_high')"], "enable": true, "notifyEnable": true, "name": "alert_too_long", "doc": "发布导致相同异常在一段时间连续出现", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>5 && timeCount>2", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 305}, {"query": "* and (window_size: 180005 or window_size: 010005) and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement or log_source: sysvm or log_source: woodpecker or log_source: woodpecker-pro or log_source: opsx or log_source: rpm or log_source: ncrs) and not isGamma: 1 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,ncIp))) as timeCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ({{GOC_EVENT}}) and exceptionName not in ('vm_heartbeat_loss_too_many','vm_uptime_drop_event_increased','vm_panic_event_too_many','nc_hang_too_much','goc_ag_icmp_loss')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ({{GOC_EVENT}}) and exceptionName not in ('vm_heartbeat_loss_too_many','vm_uptime_drop_event_increased','vm_panic_event_too_many','nc_hang_too_much','goc_ag_icmp_loss')"], "enable": true, "notifyEnable": true, "name": "goc_sync_batch_alert", "doc": "发布命中goc故障或内部应急事件（3小时聚合）", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>2", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 605}, {"query": "* and (window_size: 180005 or window_size: 010005) and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement or log_source: sysvm or log_source: woodpecker or log_source: woodpecker-pro or log_source: opsx or log_source: rpm or log_source: ncrs) and not isGamma: 1 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,instanceId))) as timeCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ({{GOC_EVENT}}) and exceptionName not in ('vm_heartbeat_loss_too_many','vm_uptime_drop_event_increased','vm_panic_event_too_many','nc_hang_too_much','goc_ag_icmp_loss')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ({{GOC_EVENT}}) and exceptionName not in ('vm_heartbeat_loss_too_many','vm_uptime_drop_event_increased','vm_panic_event_too_many','nc_hang_too_much','goc_ag_icmp_loss')"], "enable": true, "notifyEnable": true, "name": "goc_sync_batch_alert_vm", "doc": "发布命中goc故障或内部应急事件（3小时聚合）", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>2 && vmCount>2", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 605}, {"query": "* and (window_size: 030005 or window_size: 010005) and log_source: tianji_download and not isGamma: 1 and (exceptionName: nc_hang_too_long or exceptionName:nc_hang_task_detected or exceptionName:nc_hang_task_detected_conman) and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))) or (exceptionName in ('nc_hang_task_detected','nc_hang_task_detected_conman') and additionalInfo like '%Fatal fault event%') or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') or (exceptionName in ('xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel = 'fatal') or (exceptionName in ('cn_hang_task_detected','physical_machine_kernel_issue') and warningLevel = 'fatal' and additionalInfo like '%vda-: block%') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName = 'nc_hang_too_long' and (biz_status = 'free' or (biz_status = 'mlock' and cores>0))", "exceptionName in ('nc_hang_task_detected','nc_hang_task_detected_conman') and additionalInfo like '%Fatal fault event%'", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'", "exceptionName in ('xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd','virt_report_exception_ping_failed') and warningLevel = 'fatal'", "exceptionName in ('cn_hang_task_detected','physical_machine_kernel_issue') and warningLevel = 'fatal' and additionalInfo like '%vda-: block%'"], "enable": true, "notifyEnable": true, "name": "tianji_download_unexcepted_hang", "doc": "天基下载阶段导致非预期夯机", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "*  and window_size: 010005 and (exceptionName: gpu_key_process_oom or exceptionName: gpu_key_process_hang ) and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('gpu_key_process_oom','gpu_key_process_hang')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('gpu_key_process_oom','gpu_key_process_hang')"], "enable": true, "notifyEnable": true, "name": "gpu_changement_alert", "doc": "发布导致GPU受损", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and plan_id: plugin.aliyun-logtail and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('monitor_data_loss_too_long') and biz_status in ('free','mlock','rlock')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('monitor_data_loss_too_long') and biz_status in ('free','mlock','rlock')"], "enable": true, "notifyEnable": true, "name": "ilogtail-changement-alert", "doc": "ilogtail发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and plan_id: EcsAlarmAgent.EcsAlarmAgentSR#  and (window_size: 010005 or window_size: 030005) and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('ecs_alarm_agent_cache_increased')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('ecs_alarm_agent_cache_increased')"], "enable": true, "notifyEnable": true, "name": "alarm_agent_changement_30_monitor", "doc": "alarm agent 发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>10 || taskSum>30", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and (plan_id: EcsAlarmAgent.EcsAlarmAgentSR# or plan_id: XdragonHealthProtector.XdragonHealthProtectorSR#) and window_size: 015005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('goc_ag_icmp_loss','goc_batch_nc_down_p4','goc_nc_down_2u4_in_cluster','goc_nc_down_2u4_x_cluster','goc_nc_down_for_local_storage','goc_nc_down_moc_in_cluster','goc_nc_down_moc_x_cluster','goc_nc_down_normal_in_cluster','goc_nc_down_normal_x_cluster','goc_nc_down_xdragon_5u_in_cluster','goc_nc_down_xdragon_5u_x_cluster','inner_batch_nc_down_notify','nc_down_alert','nc_down_event','nc_down_for_local_storage_nc','nc_down_uniform_check','nc_fake_down','nc_hang_too_much','nc_hang_uniform_check','nc_kernel_fatal_panic','physical_machine_kernel_issue','physical_machine_kernel_panic','xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','xdragon_cn_ping_loss_too_much','inspect_fpga_err_iohang_by_pmd','nc_down_hang_uniform_data_loss_too_long','xhp_monitor_data_loss_too_long')) or (exceptionName in ('process_oom_exception') and additionalInfo in ('ecs_alarm_agent')) or (exceptionName in ('monitor_data_loss_too_long') and biz_status in ('free','mlock','rlock')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('goc_ag_icmp_loss','goc_batch_nc_down_p4','goc_nc_down_2u4_in_cluster','goc_nc_down_2u4_x_cluster','goc_nc_down_for_local_storage','goc_nc_down_moc_in_cluster','goc_nc_down_moc_x_cluster','goc_nc_down_normal_in_cluster','goc_nc_down_normal_x_cluster','goc_nc_down_xdragon_5u_in_cluster','goc_nc_down_xdragon_5u_x_cluster','inner_batch_nc_down_notify','nc_down_alert','nc_down_event','nc_down_for_local_storage_nc','nc_down_uniform_check','nc_fake_down','nc_hang_too_much','nc_hang_uniform_check','nc_kernel_fatal_panic','physical_machine_kernel_issue','physical_machine_kernel_panic','xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','xdragon_cn_ping_loss_too_much','inspect_fpga_err_iohang_by_pmd','nc_down_hang_uniform_data_loss_too_long','xhp_monitor_data_loss_too_long')", "exceptionName in ('process_oom_exception') and additionalInfo in ('ecs_alarm_agent')", "exceptionName in ('monitor_data_loss_too_long') and biz_status in ('free','mlock','rlock')"], "enable": true, "notifyEnable": true, "name": "alarm_agent_changement_alert", "doc": "alarm agent 发布导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0 && ncCount<2 && exceptionName!='nc_down_hang_uniform_data_loss_too_long' && exceptionName!='monitor_data_loss_too_long'", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "low_warning"}, {"expression": "isHardwareIssue==0 && (ncCount>1 || taskRate>0.5 || taskSum>2 || (ncCount>0 && exceptionName=='nc_down_hang_uniform_data_loss_too_long') || (ncCount>0 && exceptionName=='monitor_data_loss_too_long'))", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement) and not isGamma: 1 and window_size: 010005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,ncIp))) as timeCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('cn_process_oom_exception','nc_ssh_latency_high','gpu_check_service_vm_fabric_manager','fpga_dma_to_tso_csum_err','tdc_cgroup_memory_ratio_high')) or (exceptionName in ('vm_steal_too_many') and vcpu_mod in ('exclusive')) or (exceptionName in ('process_oom_exception') and additionalInfo in ('ecs_alarm_agent')) or (exceptionName in ('process_exception') and additionalInfo in ('/usr/bin/dragonfly')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('cn_process_oom_exception','nc_ssh_latency_high','gpu_check_service_vm_fabric_manager','fpga_dma_to_tso_csum_err','tdc_cgroup_memory_ratio_high')", "exceptionName in ('vm_steal_too_many') and vcpu_mod in ('exclusive')", "exceptionName in ('process_oom_exception') and additionalInfo in ('ecs_alarm_agent')", "exceptionName in ('process_exception') and additionalInfo in ('/usr/bin/dragonfly')"], "enable": true, "notifyEnable": true, "name": "performance_degrade_s3_alert", "doc": "发布导致nc性能受损S3", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>4 && timeCount>2", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement) and not isGamma: 1 and window_size: 010005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vm_guest_kernel_double_fault','vm_cpu_increased','wild_vm_iohang_event')) or (exceptionName in ('vmexit_exception_vm_hang','guest_os_kernel_panic') and warningLevel in ('fatal','critical')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_guest_kernel_double_fault','vm_cpu_increased','wild_vm_iohang_event')", "exceptionName in ('vmexit_exception_vm_hang','guest_os_kernel_panic') and warningLevel in ('fatal','critical')"], "enable": true, "notifyEnable": true, "name": "performance_degrade_s3_alert_vm", "doc": "发布导致vm性能受损S3", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>9 && ncCount>2 && exceptionName !='guest_os_kernel_panic'", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and ((log_source: tianji and not rollingtype: 01) or log_source: tianji_vm or log_source: aswChangement) and not isGamma: 1 and window_size: 010005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vm_guest_kernel_double_fault','vm_cpu_increased')) or (exceptionName in ('vm_steal_too_many') and vcpu_mod in ('exclusive')) or (exceptionName in ('vmexit_exception_vm_hang','guest_os_kernel_panic') and warningLevel in ('fatal','critical')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_guest_kernel_double_fault','vm_cpu_increased')", "exceptionName in ('vm_steal_too_many') and vcpu_mod in ('exclusive')", "exceptionName in ('vmexit_exception_vm_hang','guest_os_kernel_panic') and warningLevel in ('fatal','critical')"], "enable": true, "notifyEnable": true, "name": "performance_degrade_s3_alert_vm_warning", "doc": "发布导致vm性能受损S3,警告但不熔断", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>9 && ncCount>2 && exceptionName =='guest_os_kernel_panic'", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "warning"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and plan_id: EcsAlarmAgent.EcsAlarmAgentSR#  and window_size: 010005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('batch_vm_start_failed','dragonbox_vm_icmp_ping_loss_increased','nc_multi_user_vm_uptime_drop','vm_arp_ping_rtt_too_long','vm_arp_timeout_event_increased','vm_crash_event_too_many','vm_heartbeat_loss_too_many','vm_iohang_start','vm_panic_event','vm_panic_event_too_many','vm_uptime_drop_event_increased')) or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('batch_vm_start_failed','dragonbox_vm_icmp_ping_loss_increased','nc_multi_user_vm_uptime_drop','vm_arp_ping_rtt_too_long','vm_arp_timeout_event_increased','vm_crash_event_too_many','vm_heartbeat_loss_too_many','vm_iohang_start','vm_panic_event','vm_panic_event_too_many','vm_uptime_drop_event_increased')", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')"], "enable": true, "notifyEnable": true, "name": "alarm_agent_changement_alert_vm", "doc": "alarm agent 发布导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>5", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: yundun and window_size: 010005 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, deployVersion as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vm_panic_event_too_many') and warningLevel in ('low_warning')) or (exceptionName in ('vm_vsock_icmp_ping_loss','vm_arp_ping_rtt_too_long','start_vm_failed_xml','vm_unexpected_killed_by_other','vm_start_failed')) or (exceptionName in ('vm_guest_network_service_error') and warningLevel in ('critical')) or (exceptionName in ('bare_metal_console_log_too_much','xdc_rule_pausedVMRestart','vm_guest_kernel_double_fault','vm_arp_timeout_event_increased','nc_vxlan_drop_after_dpdkavs_restart')) or (exceptionName in ('guest_os_kernel_panic','cloudassitant_heartbeat_loss_high','vm_arp_increase','vm_crash_event') and warningLevel in ('fatal','critical')) or (exceptionName in ('vm_iohang_start') and warningLevel ='fatal') or (exceptionName in ('blkpmd_check_failed_stop_disk_queue','vm_pasued_resume_check_ntp') and warningLevel in ('critical')) or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-ctrl')) or (exceptionName in ('vm_paused_exception','vm_livemigrate_exception')  and warningLevel in ('critical','fatal')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_panic_event_too_many') and warningLevel in ('low_warning')", "exceptionName in ('vm_vsock_icmp_ping_loss','vm_arp_ping_rtt_too_long','start_vm_failed_xml','vm_unexpected_killed_by_other','vm_start_failed')", "exceptionName in ('vm_guest_network_service_error') and warningLevel in ('critical')", "exceptionName in ('bare_metal_console_log_too_much','xdc_rule_pausedVMRestart','vm_guest_kernel_double_fault','vm_arp_timeout_event_increased','nc_vxlan_drop_after_dpdkavs_restart')", "exceptionName in ('guest_os_kernel_panic','cloudassitant_heartbeat_loss_high','vm_arp_increase','vm_crash_event') and warningLevel in ('fatal','critical')", "exceptionName in ('vm_iohang_start') and warningLevel ='fatal'", "exceptionName in ('blkpmd_check_failed_stop_disk_queue','vm_pasued_resume_check_ntp') and warningLevel in ('critical')", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('iohub-ctrl')", "exceptionName in ('vm_paused_exception','vm_livemigrate_exception')  and warningLevel in ('critical','fatal')"], "enable": true, "notifyEnable": true, "name": "yundun_changement_alert", "doc": "云盾发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>3", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: sysvm and window_size: 010005 and not biz_status: nc_down and not biz_status: offline and not biz_status: locked", "analyse": "select planWindowStart, aliUid, gcLevel, logSource, exceptionName, exceptionTime,ncIp,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, deployVersion as taskId, instanceId,plan_window_end as planWindowStart,aliUid,gcLevel from log   where (exceptionName in ('vm_crash_event_too_many','bare_metal_console_log_too_much','vm_guest_kernel_double_fault')) or (exceptionName in ('vm_panic_event_too_many') and warningLevel in ('low_warning')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event_too_many','bare_metal_console_log_too_much','vm_guest_kernel_double_fault')", "exceptionName in ('vm_panic_event_too_many') and warningLevel in ('low_warning')"], "enable": true, "notifyEnable": true, "name": "sysvm_changement_alert", "doc": "sysvm发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>3 || taskSum > 10", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: vmChangePlan and not isGamma: 1 and plan_id: aliyun-client-assist  and window_size: 060005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: rlock ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, 'aliyun-client-assist' as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart from log   where (exceptionName in ('vm_heartbeat_loss_too_many','vm_crash_event_too_many','vm_panic_event_too_many','cloudassitant_multi_agent','cloudassitant_multi_agent_increased')) or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_heartbeat_loss_too_many','vm_crash_event_too_many','vm_panic_event_too_many','cloudassitant_multi_agent','cloudassitant_multi_agent_increased')", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')"], "enable": true, "notifyEnable": false, "name": "aliyun-client-assist-alert", "doc": "云助手发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>5", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: vmChangePlan and not isGamma: 1 and plan_id: eci  and window_size: 010005  and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: rlock ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,instanceId, serviceName,taskId,cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, instanceId,plan_window_end as planWindowStart from log   where (exceptionName in ('vm_heartbeat_loss_too_many','vm_panic_event','vm_crash_event','guest_os_kernel_panic')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_heartbeat_loss_too_many','vm_panic_event','vm_crash_event','guest_os_kernel_panic')"], "enable": true, "notifyEnable": true, "name": "eci-alert", "doc": "ECI发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and log_source: tianji and not isGamma: 1 and not rollingtype: 01 and plan_id: diting-niuer.niuer# and window_size: 010005 and not exceptionName: null  and not biz_status: nc_down and not biz_status: offline and not biz_status: locked ", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime,ncIp, serviceName,taskId,cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as ncCount from (select distinct log_source as logSource, exceptionName, exceptionTime,  nc_ip as ncIp, plan_id as serviceName, taskid as taskId, plan_window_end as planWindowStart from log   where (exceptionName in ('system_readonly')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('system_readonly')"], "enable": true, "notifyEnable": true, "name": "diting_changement_alert", "doc": "谛听发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_change_plan_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-xunjian/ecs_change_plan_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and opstype : 0x30000000000408 and (windowSize: 010005 or (windowSize: 030005 and (exceptionName: nc_hang_too_much or exceptionName: nc_hang_too_long))) and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  batchOpsTaskId,exceptionName))) as ncCount, region, batchOpsTaskId from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region, batchOpsTaskId from log  where (exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_reboot','inspect_fpga_err_iohang_by_pmd') and warningLevel ='fatal') or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('qemu-kvm','isov-moc')) or ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_reboot','inspect_fpga_err_iohang_by_pmd') and warningLevel ='fatal'", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('qemu-kvm','isov-moc')", "(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_qemu_update", "doc": "cloudops qemu热升级 变更导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0 && (exceptionName != 'core_dump_generated' || ncCount>1)", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype : 0x30000000000408 and windowSize: 010005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  batchOpsTaskId,exceptionName))) as vmCount, region, batchOpsTaskId from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId,batchOpsTaskId from log  where (exceptionName in ('vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start','vmexit_exception_vm_hang','vm_arp_timeout_event_increased')) or (exceptionName in ('vm_paused_exception','vm_livemigrate_exception','host_mem_less_qemu_mem_high','qemu_hot_upgrade_down_time_event')  and warningLevel in ('critical','fatal')) or (exceptionName in ('vm_crash_event') and warningLevel in ('critical','warning')) or (exceptionName in ('vm_pasued_resume_check_ntp')  and warningLevel ='critical') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start','vmexit_exception_vm_hang','vm_arp_timeout_event_increased')", "exceptionName in ('vm_paused_exception','vm_livemigrate_exception','host_mem_less_qemu_mem_high','qemu_hot_upgrade_down_time_event')  and warningLevel in ('critical','fatal')", "exceptionName in ('vm_crash_event') and warningLevel in ('critical','warning')", "exceptionName in ('vm_pasued_resume_check_ntp')  and warningLevel ='critical'"], "enable": true, "notifyEnable": true, "name": "cloudOps_qemu_update_vm", "doc": "cloudops qemu热升级 发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "(exceptionName == 'vm_crash_event' && vmCount > 0) || vmCount > 2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and (opstype : 0x40001000000403 or opstype : 0x40001000000404) and (windowSize: 010005 or (windowSize: 030005 and (exceptionName: nc_hang_too_much or exceptionName: nc_hang_too_long))) and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  batchOpsTaskId,exceptionName))) as ncCount, region, batchOpsTaskId from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region, batchOpsTaskId from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) or (exceptionName in ('taiji_ept_cnt_error','tdc_start_failed','nc_host_mem_less') and warningLevel ='fatal') or (exceptionName in ('fpga_ur_tlp_report') and warningLevel='critical') or (exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('td_connector','tdc_admin','tdc_upgrade_tool')) or (exceptionName in ('process_init_memory_failed')) or (exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd', 'nc_hang_task_detected', 'nc_hang_task_detected_conman','virt_report_exception_ping_failed') and warningLevel in ('fatal','critical','warning','low_warning')) or (exceptionName in ('key_process_segfault') and additionalInfo in ('td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','eal-intr-thread')) or (exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))", "exceptionName in ('taiji_ept_cnt_error','tdc_start_failed','nc_host_mem_less') and warningLevel ='fatal'", "exceptionName in ('fpga_ur_tlp_report') and warningLevel='critical'", "exceptionName in ('core_dump_generated','process_oom_exception') and additionalInfo in ('td_connector','tdc_admin','tdc_upgrade_tool')", "exceptionName in ('process_init_memory_failed')", "exceptionName in ('xdragon_cn_hang_or_down','xdragon_cn_hang_too_long','inspect_fpga_err_iohang_by_pmd', 'nc_hang_task_detected', 'nc_hang_task_detected_conman','virt_report_exception_ping_failed') and warningLevel in ('fatal','critical','warning','low_warning')", "exceptionName in ('key_process_segfault') and additionalInfo in ('td_connector','spoold-slave','spoold','pangu_blockserv','pangu_chunkserv','pangu_master','river_server','eal-intr-thread')", "exceptionName in ('virt_report_nc_exception') and reason = 'HA_PANIC_EVENT'"], "enable": true, "notifyEnable": true, "name": "cloudOps_tdc_hotup_update", "doc": "cloudops tdc hotup 变更导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0 && (exceptionName != 'core_dump_generated' || ncCount>1)", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and (opstype : 0x40001000000403 or opstype : 0x40001000000404) and windowSize: 010005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  batchOpsTaskId,exceptionName))) as vmCount, region, batchOpsTaskId from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId,batchOpsTaskId from log  where (exceptionName in ('vm_iohang_start') and warningLevel ='fatal') or (exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')) or (exceptionName in ('blkpmd_check_failed_stop_disk_queue') and warningLevel in ('critical')) or (exceptionName in ('guest_os_kernel_panic') and warningLevel in ('fatal','critical')) or (exceptionName in ('vm_guest_kernel_double_fault')) or (exceptionName in ('vhost_blk_start_failed_too_many')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_iohang_start') and warningLevel ='fatal'", "exceptionName in ('vm_crash_event') and warningLevel in ('warning','critical')", "exceptionName in ('blkpmd_check_failed_stop_disk_queue') and warningLevel in ('critical')", "exceptionName in ('guest_os_kernel_panic') and warning<PERSON><PERSON>l in ('fatal','critical')", "exceptionName in ('vm_guest_kernel_double_fault')", "exceptionName in ('vhost_blk_start_failed_too_many')"], "enable": true, "notifyEnable": true, "name": "cloudOps_tdc_hotup_update_vm", "doc": "cloudops tdc hotup 发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "vmCount>0 && (exceptionName!='guest_os_kernel_panic' || vmCount>9)", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and (opstype : 0x30000000000015 and workitemname : SetVmL3CacheNoLimit) and (windowSize: 010005 or (windowSize: 030005 and (exceptionName: nc_hang_too_much or exceptionName: nc_hang_too_long))) and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_set_vm_llc", "doc": "cloudops规模运维LLC接触运维导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and (opstype : 0x30000000000015 and workitemname : SetVmL3CacheNoLimit) and windowSize: 010005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_set_vm_llc_vm", "doc": "cloudops规模运维LLC接触运维导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and (opstype : 0x4000F000000003 and workitemname : TrimHighOrderMem) and (windowSize: 010005 or (windowSize: 030005 and (exceptionName: nc_hang_too_much or exceptionName: nc_hang_too_long))) and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_trim_high_order_mem", "doc": "cloudops高阶内存整理发布变更导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and (opstype : 0x4000F000000003 and workitemname : TrimHighOrderMem) and windowSize: 010005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_trim_high_order_mem_vm", "doc": "cloudops高阶内存整理发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and (opstype : 0x30000000000403) and (windowSize: 010005 or (windowSize: 030005 and (exceptionName: nc_hang_too_much or exceptionName: nc_hang_too_long))) and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or exceptionName='nc_down_event' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much') and warningLevel in ('fatal','critical','warning') and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or exceptionName='nc_down_event' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much') and warningLevel in ('fatal','critical','warning') and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_resize", "doc": "cloudops发布变更导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and (opstype : 0x30000000000403) and windowSize: 010005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_resize_vm", "doc": "cloudops发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 65}, {"query": "* and opstype : 0x400019 and workitemname : RestrainVm and (windowSize: 010005 or (windowSize: 030005 and (exceptionName: nc_hang_too_much or exceptionName: nc_hang_too_long))) and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_RestrainVm", "doc": "cloudops抑制vm争抢变更导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype : 0x400019 and workitemname : RestrainVm and windowSize: 010005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_RestrainVm_vm", "doc": "cloudops抑制vm争抢发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype : 0x40007000000201 and workitemname : EccProcessCpusetChecker and (windowSize: 010005 or (windowSize: 030005 and (exceptionName: nc_hang_too_much or exceptionName: nc_hang_too_long))) and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_Refresh_cgroup", "doc": "cloudops 刷新cgroup 变更导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype : 0x40007000000201 and workitemname : EccProcessCpusetChecker  and windowSize: 010005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_Refresh_cgroup_vm", "doc": "cloudops 刷新cgroup 发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype : 0x400010000000F6 and (windowSize: 010005 or (windowSize: 030005 and (exceptionName: nc_hang_too_much or exceptionName: nc_hang_too_long))) and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_erase_data", "doc": "cloudops i1数据擦除导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype : 0x400010000000F6 and windowSize: 010005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_erase_data_vm", "doc": "cloudops i1数据擦除导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and (opstype : 0x4000F000000002 or opstype : 0x40007000000c02 or opstype:0x40003000000066 or opstype:0x400070000004FF or opstype:0x40001000000099) and (workitemname : EccOverSoldSetting or workitemname : RefreshCgroup or workitemname: EcsConfigCenterUpdateNC or workitemname : RefreshNCResourceNew or workitemname : EccUpdateCenterByTemplate or workitemname:EccNcRebootDynamicFix or workitemname:EccNcSetting) and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, cardinality(array_distinct(array_agg( substr(exceptionTime,1,16)) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName,ncIp))) as timeCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) or (exceptionName in ('dpdk_cpu0_100') and warningLevel in ('critical','fatal')) or (exceptionName in ('taiji_cpu_schedevent')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))", "exceptionName in ('dpdk_cpu0_100') and warningLevel in ('critical','fatal')", "exceptionName in ('taiji_cpu_schedevent')"], "enable": true, "notifyEnable": true, "name": "cloudOps_Ecc", "doc": "cloudops 在线编配 变更导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0 && (exceptionName!='dpdk_cpu0_100'||timeCount>1) && (exceptionName!='taiji_cpu_schedevent'||ncCount>2)", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and (opstype : 0x4000F000000002 or opstype : 0x40007000000c02 or opstype:0x40003000000066 or opstype:0x400070000004FF or opstype:0x40001000000099) and (workitemname : EccOverSoldSetting or workitemname : RefreshCgroup or workitemname: EcsConfigCenterUpdateNC or workitemname : RefreshNCResourceNew or workitemname : EccUpdateCenterByTemplate or workitemname:EccNcRebootDynamicFix or workitemname:EccNcSetting) and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_Ecc_vm", "doc": "cloudops 在线编配 发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype : 0x400026 and (workitemname : TDCUpgradeResource or workitemname : TDCUpgradeResourcePrecheck) and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_tdc_polling", "doc": "cloudops tdc转polling 变更导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype : 0x400026 and (workitemname : TDCUpgradeResource or workitemname : TDCUpgradeResourcePrecheck) and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_tdc_polling_vm", "doc": "cloudops  tdc转polling 发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype : 0x400070000000A2 and (workitemname : EccReduceHugePage or workitemname : EccBizCheck) and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_ReduceHugePage", "doc": "cloudops大页内存调整变更导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype : 0x400070000000A2 and (workitemname : EccReduceHugePage or workitemname : EccBizCheck) and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_ReduceHugePage_vm", "doc": "cloudops大页内存调整发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype :  0x3000000000802 and workitemname : ResidualVmClean and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_ResidualVmClean", "doc": "cloudopsvm残留清理发布变更导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype :  0x3000000000802 and workitemname : ResidualVmClean and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_panic_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_panic_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_ResidualVmClean_vm", "doc": "cloudops vm残留清理发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype :  0x400070000004A1 and workitemname : CheckHugeTLB and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_CheckHugeTLB", "doc": "cloudops大页坏页修复发布变更导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype :  0x400070000004A1 and workitemname : CheckHugeTLB and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_CheckHugeTLB_vm", "doc": "cloudops大页坏页修复发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype :  0x40007000000410 and (workitemname : UpdateI3SsdFw or workitemname : ExecuteSA) and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_UpdateI3SsdFw", "doc": "cloudops I3 firmware 发布变更导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype :  0x40007000000410 and (workitemname : UpdateI3SsdFw or workitemname : ExecuteSA) and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_UpdateI3SsdFw_vm", "doc": "cloudops I3 firmware 发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype :  0x40007000000402 and workitemname : SetTdcQos and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much')   and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_ModifyDpdkAvsQueue", "doc": "cloudops 修改dpdk队列发布变更导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype :  0x40007000000402 and workitemname : SetTdcQos and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_ModifyDpdkAvsQueue_vm", "doc": "cloudops修改dpdk队列发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and (ops_code:0x30000000000a0 or ops_code:0x************** or ops_code:0x10001000000011) and not isGamma: 1 and window_size:030005 and not exceptionName:null and log_source : live_migrate_new and relation_type : dst_nc and migrate_type: nc_evacuation ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount from (select distinct logSource, exceptionName, exception_time as exceptionTime, ops_code as opsType, nc_ip as ncIp, isGamma, cluster_alias as clusterAlias, '' as iz, '' as region, aliUid, change_time as planWindowStart from log  where (exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning')) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much') and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning')", "exceptionName in ('nc_hang_too_long','nc_hang_too_much') and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "live_migration_dst_nc_down", "doc": "逃逸热迁移导致目的nc宕机或宕机", "end": 0, "interval": 60, "logstore": "live_migrate_exception_linkage_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and log_source:live_migrate_new and (window_size: 030005 or window_size: 010005) and not exceptionName: null and not biz_status:nc_down and not biz_status:locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime, substr(exceptionTime,1,16) as aggregateTime, ncIp, instanceId, serviceName, taskId, cardinality(array_distinct(array_agg(instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount, cardinality(array_distinct(array_agg(substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,instanceId))) as timeCount, additionalInfo from (select distinct log_source as logSource, exceptionName,  nc_ip as ncIp, instanceId, 'cloudOps_live_migration' as serviceName, exceptionTime,instanceId as taskId, change_time as planWindowStart, instanceId as additionalInfo from log   where (exceptionName in ('vm_network_retry_high','vm_all_vcpu_util_high','vm_guest_os_hang')) or (exceptionName in ('vmexit_exception_vm_hang') and warningLevel in ('critical','fatal')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_network_retry_high','vm_all_vcpu_util_high','vm_guest_os_hang')", "exceptionName in ('vmexit_exception_vm_hang') and warningLevel in ('critical','fatal')"], "enable": true, "notifyEnable": true, "name": "live_migration_vm_alert_long", "doc": "热迁移导致vm持续异常", "end": 0, "interval": 60, "logstore": "live_migrate_exception_linkage_tmp", "alertValue": 0, "levels": [{"expression": "isHardwareIssue==0 && vmCount>0 && timeCount>2", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 305}, {"query": "* and log_source:live_migrate_new and (window_size: 030005 or window_size: 010005) and not exceptionName: null and not biz_status:nc_down and not biz_status:locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime, substr(exceptionTime,1,16) as aggregateTime, ncIp, instanceId, serviceName, taskId, cardinality(array_distinct(array_agg(instanceId) OVER(PARTITION BY  taskId,serviceName,exceptionName))) as vmCount, cardinality(array_distinct(array_agg(substr(exceptionTime,1,16)) OVER(PARTITION BY  taskId,serviceName,exceptionName,instanceId))) as timeCount, additionalInfo from (select distinct log_source as logSource, exceptionName,  nc_ip as ncIp, instanceId, 'cloudOps_live_migration' as serviceName, exceptionTime,instanceId as taskId, change_time as planWindowStart, instanceId as additionalInfo from log   where (exceptionName in ('intel_microcode_error') and warningLevel in ('low_warning')) or (exceptionName in ('vm_disk_full_conman') and (additionalInfo like '%dump file%' or additionalInfo like '%kdump%')) or (exceptionName in ('vm_panic_from_guest_asist','vm_panic_event','ecs_panic_trouble','vhost_blk_start_failed','vm_lost_irq_hanlder','vm_net_queue_error','vm_hang_screenshot_ocr_reason','live_migrate_vm_system_error')) or (exceptionName in ('guest_os_kernel_panic','vm_crash_event') and warningLevel in ('warning','fatal')) or (exceptionName in ('vm_paused_exception') and warningLevel in ('fatal')) or (exceptionName in ('live_migrate_downtime_too_high') and warningLevel in ('fatal') and reason not in ('虚拟化上报致命异常','研发测试')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('intel_microcode_error') and warningLevel in ('low_warning')", "exceptionName in ('vm_disk_full_conman') and (additionalInfo like '%dump file%' or additionalInfo like '%kdump%')", "exceptionName in ('vm_panic_from_guest_asist','vm_panic_event','ecs_panic_trouble','vhost_blk_start_failed','vm_lost_irq_hanlder','vm_net_queue_error','vm_hang_screenshot_ocr_reason','live_migrate_vm_system_error')", "exceptionName in ('guest_os_kernel_panic','vm_crash_event') and warningLevel in ('warning','fatal')", "exceptionName in ('vm_paused_exception') and warningLevel in ('fatal')", "exceptionName in ('live_migrate_downtime_too_high') and warningLevel in ('fatal') and reason not in ('虚拟化上报致命异常','研发测试')"], "enable": true, "notifyEnable": true, "name": "live_migration_vm_alert", "doc": "热迁移导致vm异常", "end": 0, "interval": 60, "logstore": "live_migrate_exception_linkage_tmp", "alertValue": 0, "levels": [{"expression": "isHardwareIssue==0 && vmCount>0", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 305}, {"query": "* and log_source:live_migrate_new and (window_size: 030005 or window_size: 010005) and not exceptionName: null and not biz_status:nc_down and not biz_status:locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime, substr(exceptionTime,1,16) as aggregateTime, ncIp, serviceName, taskId, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY taskId,serviceName,exceptionName))) as ncCount, cardinality(array_distinct(array_agg(substr(exceptionTime,1,16)) OVER(PARTITION BY taskId,serviceName,exceptionName,ncIp))) as timeCount, additionalInfo from (select distinct log_source as logSource, exceptionName, nc_ip as ncIp, 'cloudOps_live_migration' as serviceName, exceptionTime,ncIp as taskId, change_time as planWindowStart, instanceId as additionalInfo from log   where (exceptionName in ('nc_hang_too_long','nc_down_alert')) or (exceptionName in ('core_dump_generated','nc_process_segfault_too_many') and (additionalInfo in ('dpdkavs','lcore-slave','netframe') or additionalInfo like 'avs%')) or (exceptionName in ('nc_hang_task_detected','nc_hang_task_detected_conman','cn_hang_task_detected')) or (exceptionName in ('core_dump_generated') and additionalInfo in ('iohub-ctrl')) or (exceptionName in ('process_oom_exception','process_oom_exception_in_band') and warningLevel in ('critical')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('nc_hang_too_long','nc_down_alert')", "exceptionName in ('core_dump_generated','nc_process_segfault_too_many') and (additionalInfo in ('dpdkavs','lcore-slave','netframe') or additionalInfo like 'avs%')", "exceptionName in ('nc_hang_task_detected','nc_hang_task_detected_conman','cn_hang_task_detected')", "exceptionName in ('core_dump_generated') and additionalInfo in ('iohub-ctrl')", "exceptionName in ('process_oom_exception','process_oom_exception_in_band') and warningLevel in ('critical')"], "enable": true, "notifyEnable": true, "name": "live_migration_nc_alert", "doc": "热迁移导致nc异常", "end": 0, "interval": 300, "logstore": "live_migrate_exception_linkage_tmp", "alertValue": 0, "levels": [{"expression": "isHardwareIssue==0 && ncCount>0", "sink": ["ecs-release-plan/ecs_change_plan_exceptions_circuit_breaker_detail_daily_test"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 305}, {"query": "* and (window_size: 030005 or window_size: 120005) and not exceptionName: null  and not biz_status:nc_down and not biz_status:locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime, ncIp, isGamma, instanceId, clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY iz,exceptionName))) as vmCount from (select distinct logSource, exceptionName, exception_time as exceptionTime, ncIp, isGamma, clusterAlias, aliUid, changeTime as planWindowStart, iz, instanceId from log  where (exceptionName in ('vm_livemigrate_exception') and warningLevel in ('fatal')) or (exceptionName in ('vm_stuck_in_migrating_status')) or (exceptionName in ('vm_vsock_icmp_ping_loss_new') and additionalInfo in ('vm_livemigrate_exception')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_livemigrate_exception') and warningLevel in ('fatal')", "exceptionName in ('vm_stuck_in_migrating_status')", "exceptionName in ('vm_vsock_icmp_ping_loss_new') and additionalInfo in ('vm_livemigrate_exception')"], "enable": false, "notifyEnable": true, "name": "cloudOps_live_migration_vm_by_iz", "doc": "热迁移发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "live_migrate_exception_linkage_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>9", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 86400}, {"query": "* and (window_size: 030005 or window_size: 120005) and not exceptionName: null  and not biz_status:nc_down and not biz_status:locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime, ncIp, isGamma, instanceId, clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY clusterAlias,exceptionName))) as vmCount from (select distinct logSource, exceptionName, exception_time as exceptionTime, ncIp, isGamma, clusterAlias, aliUid, changeTime as planWindowStart, iz, instanceId from log  where (exceptionName in ('vm_livemigrate_exception') and warningLevel in ('fatal')) or (exceptionName in ('vm_stuck_in_migrating_status')) or (exceptionName in ('vm_vsock_icmp_ping_loss_new') and additionalInfo in ('vm_livemigrate_exception')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_livemigrate_exception') and warningLevel in ('fatal')", "exceptionName in ('vm_stuck_in_migrating_status')", "exceptionName in ('vm_vsock_icmp_ping_loss_new') and additionalInfo in ('vm_livemigrate_exception')"], "enable": false, "notifyEnable": true, "name": "cloudOps_live_migration_vm_by_cluster", "doc": "热迁移发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "live_migrate_exception_linkage_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>4", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 86400}, {"query": "* and (window_size: 030005 or window_size: 120005) and not exceptionName: null  and not biz_status:nc_down and not biz_status:locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime, ncIp, isGamma, instanceId, clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY aliUid,exceptionName))) as vmCount from (select distinct logSource, exceptionName, exception_time as exceptionTime, ncIp, isGamma, clusterAlias, aliUid, changeTime as planWindowStart, iz, instanceId from log  where (exceptionName in ('vm_livemigrate_exception') and warningLevel in ('fatal')) or (exceptionName in ('vm_stuck_in_migrating_status')) or (exceptionName in ('vm_vsock_icmp_ping_loss_new') and additionalInfo in ('vm_livemigrate_exception')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_livemigrate_exception') and warningLevel in ('fatal')", "exceptionName in ('vm_stuck_in_migrating_status')", "exceptionName in ('vm_vsock_icmp_ping_loss_new') and additionalInfo in ('vm_livemigrate_exception')"], "enable": false, "notifyEnable": true, "name": "cloudOps_live_migration_vm_by_aliUid", "doc": "热迁移发布变更导致vm异常", "end": 0, "interval": 60, "logstore": "live_migrate_exception_linkage_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>4", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 86400}, {"query": "* and window_size: 030005  and not exceptionName: null  and not biz_status:nc_down and not biz_status:locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime, ncIp, isGamma, instanceId, clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY iz,exceptionName))) as ncCount from (select distinct logSource, exceptionName, exception_time as exceptionTime, ncIp, isGamma, clusterAlias, aliUid, changeTime as planWindowStart, iz, instanceId from log  where (exceptionName in ('live_migrate_cause_nc_hang') and warningLevel in ('critical')) or (exceptionName in ('core_dump_generated') and additionalInfo in ('td_connector','tdc_admin','tdc_upgrade_tool') and cluster_alias in ('AY226Q','AY232S','AY239X','AY239Y','AY242U','AY247O','AY247P','AY247Y','AY248L','AY252Y','AY259U','AY271X','AY271Z','AY272B','AY272F','AY272G','AY275S','AY280A','AY280F','AY281D','AY281I','AY283A','AY283O','AY451A')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('live_migrate_cause_nc_hang') and warningLevel in ('critical')", "exceptionName in ('core_dump_generated') and additionalInfo in ('td_connector','tdc_admin','tdc_upgrade_tool') and cluster_alias in ('AY226Q','AY232S','AY239X','AY239Y','AY242U','AY247O','AY247P','AY247Y','AY248L','AY252Y','AY259U','AY271X','AY271Z','AY272B','AY272F','AY272G','AY275S','AY280A','AY280F','AY281D','AY281I','AY283A','AY283O','AY451A')"], "enable": false, "notifyEnable": true, "name": "cloudOps_live_migration_nc_by_iz", "doc": "热迁移发布变更导致nc异常", "end": 0, "interval": 60, "logstore": "live_migrate_exception_linkage_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>3", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 86400}, {"query": "* and window_size: 030005  and not exceptionName: null  and not biz_status:nc_down and not biz_status:locked", "analyse": "select planWindowStart, logSource, exceptionName, exceptionTime, ncIp, isGamma, instanceId, clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( ncIp) OVER(PARTITION BY clusterAlias,exceptionName))) as ncCount from (select distinct logSource, exceptionName, exception_time as exceptionTime, ncIp, isGamma, clusterAlias, aliUid, changeTime as planWindowStart, iz, instanceId from log  where (exceptionName in ('live_migrate_cause_nc_hang') and warningLevel in ('critical')) or (exceptionName in ('core_dump_generated') and additionalInfo in ('td_connector','tdc_admin','tdc_upgrade_tool') and cluster_alias in ('AY226Q','AY232S','AY239X','AY239Y','AY242U','AY247O','AY247P','AY247Y','AY248L','AY252Y','AY259U','AY271X','AY271Z','AY272B','AY272F','AY272G','AY275S','AY280A','AY280F','AY281D','AY281I','AY283A','AY283O','AY451A')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('live_migrate_cause_nc_hang') and warningLevel in ('critical')", "exceptionName in ('core_dump_generated') and additionalInfo in ('td_connector','tdc_admin','tdc_upgrade_tool') and cluster_alias in ('AY226Q','AY232S','AY239X','AY239Y','AY242U','AY247O','AY247P','AY247Y','AY248L','AY252Y','AY259U','AY271X','AY271Z','AY272B','AY272F','AY272G','AY275S','AY280A','AY280F','AY281D','AY281I','AY283A','AY283O','AY451A')"], "enable": false, "notifyEnable": true, "name": "cloudOps_live_migration_nc_by_cluster", "doc": "热迁移发布变更导致nc异常", "end": 0, "interval": 60, "logstore": "live_migrate_exception_linkage_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>1", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 86400}, {"query": "* and opstype :  0x40007000000002 and workitemname : UpdateSNPackage and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much') and warningLevel in ('fatal','critical','warning') and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) or (exceptionName in ('xdragon_cn_hang_or_down') and warningLevel = 'fatal') limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much') and warningLevel in ('fatal','critical','warning') and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))", "exceptionName in ('xdragon_cn_hang_or_down') and warningLevel = 'fatal'"], "enable": true, "notifyEnable": true, "name": "cloudOps_bmc_deploy_alert", "doc": "cloudops bmc发布导致异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype :  0x400070000004M1 and workitemname : EccCommonCmd and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much') and warningLevel in ('fatal','critical','warning') and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much') and warningLevel in ('fatal','critical','warning') and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_Cacheclean", "doc": "cloudops cache内存清理导致nc异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype :  0x400070000004M1 and workitemname : EccCommonCmd and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_Cacheclean_vm", "doc": "cloudops cache内存清理导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype :  0x3000000000040 and workitemname : VmNetCpuLimitLimit and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_VmNetCpuLimit_vm", "doc": "cloudops 限制VmNetCpu导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype :  0x3000000000049 and workitemname : VmSessionLimit and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, instanceId, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg( instanceId) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as vmCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region,instanceId from log  where (exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('vm_crash_event','vm_panic_event_too_many','vm_crash_event_too_many','vm_iohang_start')"], "enable": true, "notifyEnable": true, "name": "cloudOps_VmSessionLimit_vm", "doc": "cloudops 修改VM Session导致vm异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{vmCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and (opstype : 0x40003000000066) and windowSize: 030005  and not exceptionName: null  ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where (exceptionName in ('start_vm_failed_xml')) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["exceptionName in ('start_vm_failed_xml')"], "enable": true, "notifyEnable": true, "name": "cloudOps_NcOpHandler", "doc": "cloudops nc变配导致异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "ncCount>1", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and opstype : 0x30000000000432 and windowSize: 030005  and not exceptionName: null ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  region,clusterAlias,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much') and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much') and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_CpuOpHandler", "doc": "cloudops 修改实例CPU利用率上限导致异常", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>0", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 610}, {"query": "* and not isGamma: 1 and (opstype : 0x400012 or opstype : 0x40009000003001 or opstype : 0x400016 or opstype : 0x4000100000240 or opstype : 0x4000300000108 or opstype : 0x40003000001002 or opstype : 0x4000300000021 or opstype : 0x40001000008001) and windowSize: 030005  and not exceptionName: null ", "analyse": "select planWindowStart, logSource, opsType, exceptionName, exceptionTime, ncIp, isGamma, region,clusterAlias, aliUid, iz, cardinality(array_distinct(array_agg(ncIp) OVER(PARTITION BY  opsType,region,iz,exceptionName))) as ncCount, region from (select distinct logSource, exceptionName, exceptionTime, opstype as opsType, ncIp, isGamma, clusterAlias, aliUid, planWindowStart as planWindowStart, iz, region from log  where ((exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )) or (exceptionName in ('nc_hang_too_long','nc_hang_too_much') and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))) limit 10000)", "start": 0, "project": "ecs-release-plan", "whereStatement": ["(exceptionName='nc_down_alert' or (exceptionName = 'ag_nc_icmp_latency_increase' and bizStatus='free' and warningLevel = 'warning') )", "exceptionName in ('nc_hang_too_long','nc_hang_too_much') and (bizStatus = 'free' or (bizStatus = 'mlock' and cores>0))"], "enable": true, "notifyEnable": true, "name": "cloudOps_ncDown", "doc": "cloudops 宕机运维超限", "end": 0, "interval": 60, "logstore": "ecs_cloudops_exceptions_tmp", "alertValue": 0, "levels": [{"expression": "{{ncCount}}>2", "sink": ["ecs-release-plan/ecs_cloudops_exceptions_circuit_breaker_detail"], "name": "critical"}], "globalNotifyEnable": false, "timeRange": 920}], "recordsTotal": 127}