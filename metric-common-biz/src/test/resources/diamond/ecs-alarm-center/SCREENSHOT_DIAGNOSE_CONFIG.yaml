windows:
  - configId: 1662001135
    toUser: true
    matchCondition: '"系统恢复" in "{{content}}" and "选择语言" in "{{content}}"'
    reason: Windows系统因异常进入恢复环境
    innerReason: Windows系统因异常进入恢复环境，原因及解决措施参考官方文档：https://help.aliyun.com/document_detail/41049.html
    solution: 采用微软官方技术支持提出的方案进行处理：https://docs.microsoft.com/en-us/previous-versions/windows/it-pro/windows-7/dd744291(v=ws.10)?redirectedfrom=MSDN

  - configId: 1662001136
    toUser: true
    matchCondition: '"Checking file system on".replace(" ", "") in "{{content}}" or "CHKDSK is verifying files".replace(" ", "") in "{{content}}" or "CHKDSK is verifying indexes".replace(" ", "") in "{{content}}"'
    reason: Windows系统的文件系统出现异常
    innerReason: Windows系统的文件系统出现异常。可能是异常关机或者windows内部系统已经损坏，建议等待自检完成；如果自检最终卡死，只能重置系统盘
    solution: 先尝试使用系统盘快照恢复，若无法恢复，建议重置系统盘

  - configId: 1662001137
    toUser: true
    matchCondition: '"出现内部错误" in "{{content}}" or "系统找不到指定的文件" in "{{content}}"'
    reason: Windows系统出现内部错误
    innerReason: Windows系统出现内部错误。客户自身操作系统问题，建议重启实例；如果重启未解决，只能重置系统盘
    solution: 先尝试使用系统盘快照恢复，若无法恢复，建议重置系统盘

  - configId: 1662001138
    toUser: true
    matchCondition: '"Boot configuration data".replace(" ", "") in "{{content}}"'
    reason: Windows系统引导配置数据（BCD）中的文件丢失或损坏
    innerReason: Windows系统引导配置数据（BCD）中的文件丢失或损坏
    solution: 先尝试使用系统盘快照恢复，若无法恢复则需要提交工单解决

  - configId: 1662001139
    toUser: true
    matchCondition: '"INACCESSIBLE BOOT DEVICE".replace(" ", "") in "{{content}}"'
    reason: Windows系统的引导扇区或驱动文件丢失或损坏
    innerReason: Windows系统的引导扇区或驱动文件丢失或损坏
    solution: 先尝试使用系统盘快照恢复，若无法恢复则需要提交工单解决

  - configId: 1662001140
    toUser: true
    matchCondition: '"bootmgr is missing".replace(" ", "") in "{{content}}"'
    reason: Windows系统的Bootmgr配置丢失或损坏
    innerReason: Windows系统的Bootmgr配置丢失或损坏

  - configId: 1662001141
    toUser: true
    matchCondition: '"missing operating system".replace(" ", "") in "{{content}}"'
    reason: Windows系统的系统文件丢失或损坏
    innerReason: Windows系统的系统文件丢失或损坏
    solution: 先尝试使用系统盘快照恢复，若无法恢复则需要提交工单解决

  - configId: 1662001142
    toUser: true
    matchCondition: '"system registry file is missing".replace(" ", "") in "{{content}}" or "BAD_SYSTEM_CONFIG_INFO" in "{{content}}" or "BAD SYSTEM CONFIG INFO".replace(" ", "") in "{{content}}"'
    reason: Windows系统的注册表丢失或者损坏
    innerReason: Windows系统的注册表丢失或者损坏
    solution: 先尝试使用系统盘快照恢复，若无法恢复则需要提交工单解决。参考文档：https://help.aliyun.com/zh/ecs/support/windows-instance-runs-into-preparing-automatic-repair-mode-on-startup#687bf07065vdz

  - configId: 1662001151
    toUser: true
    matchCondition: '"计算机意外地重新启动或遇到错误" in "{{content}}" and "安装" in "{{content}}"  or "Windows" in "{{content}}" and "无法完成安装" in "{{content}}" or "Windows installation can not proceed".replace(" ", "") in "{{content}}"'
    reason: Windows sysprep因实例重启未正常完成，导致操作系统初始化异常
    innerReason: sysprep未完成导致，客户重启Windows机器导致异常，建议用户重新初始化系统
    solution: 重新初始化系统

  - configId: 1671696280
    toUser: true
    matchCondition: '"xc0000001" in "{{content}}"'
    reason: Windows BCD配置异常或者磁盘文件系统故障
    innerReason: Windows BCD配置异常或者磁盘文件系统故障
    solution: 参考文档修复：https://help.aliyun.com/document_detail/450291.html#3032eb90290k5

  - configId: 1671696281
    toUser: true
    matchCondition: '"选择键盘布局" in "{{content}}" or "Choose your keyboard layout".replace(" ","") in "{{content}}"'
    reason: Windows系统文件损坏或者驱动不兼容
    innerReason: Windows系统文件损坏或者驱动不兼容
    solution: 参考文档修复：https://help.aliyun.com/document_detail/450291.html#3032eb90290k5

  - configId: 1671696282
    toUser: true
    matchCondition: '"Windows Error Recovery".replace(" ","") in "{{content}}"'
    reason: Windows磁盘故障或者硬件变动
    innerReason: Windows磁盘故障或者硬件变动
    solution: 参考文档修复：https://help.aliyun.com/document_detail/450291.html#3032eb90290k5

  - configId: 1671696284
    toUser: true
    matchCondition: '"An operating system wasn''t found".replace(" ","") in "{{content}}"'
    reason: Windows无法找到可用的系统引导启动
    innerReason: Windows无法找到可用的系统引导启动
    solution: 参考文档修复：https://help.aliyun.com/document_detail/450291.html#3032eb90290k5

  - configId: 1684829587
    toUser: false
    matchCondition: '"损坏或者丢失" in "{{content}}" and "无法启动" in "{{content}}" or "FATALERROR" in "{{content}}" and "SLICtablepointer" in "{{content}}"'
    reason: windows系统文件损坏或丢失
    innerReason: windows系统文件损坏或丢失

  - configId: 1684829589
    toUser: false
    matchCondition: '"正在进行更新" in "{{content}}" or "正在配置更新" in "{{content}}"'
    reason: windows系统更新
    innerReason: windows系统更新

  - configId: 1684829590
    toUser: false
    matchCondition: '"If this is the first time you".replace(" ", "") in "{{content}}"'
    reason: windows系统意外关闭或重启
    innerReason: windows系统意外关闭或重启

  - configId: 1686572838
    toUser: false
    matchCondition: '"The selected entry could not be loaded".replace(" ", "") in "{{content}}"'
    reason: windows实例磁盘分区异常导致引导失败
    innerReason: windows实例磁盘分区异常导致引导失败

  - configId: 1686573725
    toUser: false
    matchCondition: '"没有检测到任何网络硬件" in "{{content}}"'
    reason: 未识别到网卡
    innerReason: 未识别到网卡

  - configId: 1713243192
    toUser: false
    matchCondition: '("File" in "{{content}}" and "Status" in "{{content}}" and "Info" in "{{content}}" and "0xc0000428" in "{{content}}") or ("文件" in "{{content}}" and "状态" in "{{content}}" and "信息" in "{{content}}" and "0xc0000428" in "{{content}}")'
    reason: 用户系统未安装*********补丁
    innerReason: 用户系统未安装*********补丁
    solution: 参考文档修复：https://help.aliyun.com/zh/ecs/support/windows-server-2008-r2-cannot-start-due-to-a-driver-signature-verfication-failure

  - configId: 1706506814
    toUser: false
    matchCondition: '("File" in "{{content}}" and "Status" in "{{content}}" and "Info" in "{{content}}") or ("文件" in "{{content}}" and "状态" in "{{content}}" and "信息" in "{{content}}")'
    reason: windows关键文件损坏
    innerReason: windows关键文件损坏
    solution: 参考文档修复：https://kb.alibaba-inc.com/edit?id=G611473

  - configId: 1687169881
    toUser: false
    matchCondition: '("未能启动" in "{{content}}" and "最近更改了硬件或软件" in "{{content}}") or ("hardware or software".replace(" ", "") in "{{content}}" and "fix the problem".replace(" ", "") in "{{content}}")'
    reason: windows未能启动
    innerReason: windows未能启动，原因可能是最近更改了硬件或软件

  - configId: 1687169883
    toUser: true
    matchCondition: '"需要重新启动" in "{{content}}" or "需要重启" in "{{content}}" or "ran into a problem and needs to restart".replace(" ", "") in "{{content}}"'
    reason: windows操作系统异常崩溃
    innerReason: windows操作系统异常崩溃
    solution: 参考文档修复：https://help.aliyun.com/zh/ecs/support/troubleshoot-downtime-issues-of-windows-instances

  - configId: 1687922894
    toUser: false
    matchCondition: '"Windows didn''t load correct".replace(" ", "") in "{{content}}" or "未正确加载" in "{{content}}"'
    reason: windows未正确加载，可能是BCD损坏或文件系统损坏
    innerReason: windows未正确加载，可能是BCD损坏或文件系统损坏

  - configId: 1688005407
    toUser: false
    matchCondition: '"按CTRL+ALT+DELETE" in "{{content}}"'
    reason: windows系统锁屏状态
    innerReason: windows系统锁屏状态

  - configId: 1688525737
    toUser: false
    matchCondition: '"正在关机" in "{{content}}"'
    reason: windows系统卡在关机过程
    innerReason: windows系统卡在关机过程

  - configId: 1688525738
    toUser: false
    matchCondition: '"重置你的电脑或查看高级选项" in "{{content}}" or "Refresh or reset your PC".replace(" ", "") in "{{content}}" or "恢复或初始化电脑" in "{{content}}"'
    reason: windows系统卡在启动过程
    innerReason: windows系统卡在启动过程

  - configId: 1689737280
    toUser: false
    matchCondition: '"未成功关闭" in "{{content}}"'
    reason: windows系统未成功关闭
    innerReason: windows系统未成功关闭

  - configId: 1689737281
    toUser: false
    matchCondition: '"正在检查内存问题" in "{{content}}"'
    reason: windows正在检查内存问题
    innerReason: windows正在检查内存问题

  - configId: 1689905276
    toUser: false
    matchCondition: '"正在准备Windows" in "{{content}}"'
    reason: windows正在开机
    innerReason: windows正在开机

  - configId: 1690275707
    toUser: false
    matchCondition: '"Loki locker".replace(" ", "") in "{{content}}"'
    reason: 系统遭受勒索软件攻击
    innerReason: 系统遭受勒索软件攻击

  - configId: 1693555501
    toUser: false
    matchCondition: '"发现新硬件" in "{{content}}"'
    reason: windows发现新硬件，需安装驱动程序软件
    innerReason: windows发现新硬件，需安装驱动程序软件

  - configId: 1699415390
    toUser: false
    matchCondition: '"此计算机正在使用，并被锁定" in "{{content}}" or "This computer is in use and has been locked".replace(" ", "") in "{{content}}"'
    reason: 设置的默认屏幕保护程序不存在，或者使用损坏的并且受密码保护的屏幕保护程序
    innerReason: 设置的默认屏幕保护程序不存在，或者使用损坏的并且受密码保护的屏幕保护程序

  - configId: 1699415391
    toUser: false
    matchCondition: '"正在扫描和修复驱动器" in "{{content}}" or "Scanning and repairing drive".replace(" ", "") in "{{content}}"'
    reason: windows开机检测修复驱动器
    innerReason: windows开机检测修复驱动器

  - configId: 1699967287
    toUser: false
    matchCondition: '"Booting" in "{{content}}" and "from HardDisk".replace(" ", "") in "{{content}}"'
    reason: windows开机卡在硬盘数据加载阶段
    innerReason: windows开机卡在硬盘数据加载阶段

  - configId: 1699967288
    toUser: false
    matchCondition: '"Call Trace".replace(" ", "") in "{{content}}" or "endtrace" in "{{content}}"'
    reason: windows出现异常CallTrace
    innerReason: windows出现异常CallTrace

  - configId: 1700644567
    toUser: false
    matchCondition: '"BitLocker" in "{{content}}" and "password" in "{{content}}"'
    reason: 使用BitLocker恢复密钥解锁硬盘
    innerReason: 使用BitLocker恢复密钥解锁硬盘

  - configId: 1700644571
    toUser: false
    matchCondition: '"Windows PE".replace(" ", "") in "{{content}}"'
    reason: windows系统挂载PE启动
    innerReason: windows系统挂载PE启动

  - configId: 1701441988
    toUser: false
    matchCondition: '"正在准备安全选项" in "{{content}}" or "Preparing Security Options".replace(" ", "") in "{{content}}"'
    reason: windows开机正在准备安全选项
    innerReason: windows开机正在准备安全选项

  - configId: 1702648244
    toUser: false
    matchCondition: '"正在准备自动修复" in "{{content}}" or "preparing Automatic repair".replace(" ", "") in "{{content}}"'
    reason: windows系统正在准备自动修复
    innerReason: windows系统正在准备自动修复

  - configId: 1702648245
    toUser: false
    matchCondition: '"正在加载文件" in "{{content}}" or "loading files".replace(" ", "") in "{{content}}"'
    reason: windows系统正在加载文件
    innerReason: windows系统正在加载文件

  - configId: 1706506808
    toUser: false
    matchCondition: '"no bootable device".replace(" ", "") in "{{content}}"'
    reason: windows系统开机找不到启动盘
    innerReason: windows系统开机找不到启动盘
    solution: 参考文档修复：https://kb.alibaba-inc.com/repo/467/article?id=G537119

  - configId: 1706506811
    toUser: true
    matchCondition: '"Choose an option".replace(" ", "") in "{{content}}" or "选择一个选项" in "{{content}}"'
    reason: windows系统处于启动模式选择中
    innerReason: windows系统处于启动模式选择中
    solution: 参考文档修复：https://help.aliyun.com/zh/ecs/support/windows-instance-runs-into-preparing-automatic-repair-mode-on-startup

  - configId: 1706506815
    toUser: false
    matchCondition: '("cannot verify".replace(" ", "") in "{{content}}" and "digital signature".replace(" ", "") in "{{content}}") or ("无法验证" in "{{content}}" and "数字签名" in "{{content}}")'
    reason: windows系统无法识别数字签名
    innerReason: windows系统无法识别数字签名
    solution: 参考文档修复：https://kb.alibaba-inc.com/edit?id=G538065

  - configId: 1721748963
    toUser: false
    matchCondition: '"A required device isn''t connected or can''t be accessed" in "{{content}}" or "未连接或无法访问所需设备" in "{{content}}"'
    reason: windows系统未连接或无法访问所需设备
    innerReason: windows系统未连接或无法访问所需设备

  - configId: 1721748964
    toUser: false
    matchCondition: '"The operating system couldn''t be loaded" in "{{content}}"'
    reason: windows系统加载异常
    innerReason: windows系统加载异常

  - configId: 1721748965
    toUser: false
    matchCondition: '"problem has been detected".replace(" ", "") in "{{content}}" or "If this is the first time you".replace(" ", "") in "{{content}}"'
    reason: windows系统意外关闭或重启
    innerReason: windows系统意外关闭或重启

  - configId: 1725003187
    toUser: false
    matchCondition: '"WinPE" in "{{content}}"'
    reason: windows系统挂PE启动成功
    innerReason: windows系统挂PE启动成功

  - configId: 1725462353
    toUser: false
    matchCondition: '"正在重新启动" in "{{content}}"'
    reason: windows正在重新启动
    innerReason: windows正在重新启动

  - configId: 1725462354
    toUser: false
    matchCondition: '"回收站" in "{{content}}"'
    reason: windows系统登录成功
    innerReason: windows系统登录成功

linux:
  - configId: 1662001143
    toUser: true
    matchCondition: '"grub>" in "{{content}}" or "grub rescue>".replace(" ", "") in "{{content}}" or "Entering rescue mode" in "{{content}}" or "GNU GRUB" in "{{content}}"'
    reason: Linux系统GRUB引导失败
    innerReason: Linux系统GRUB引导失败
    solution: 可能原因：grub引导丢失或损坏，系统分区无法识别、异常关机等。建议手动尝试引导启动之后重新grub-install；如果手动引导无法启动，则系统已经损坏，建议重置系统盘

  - configId: 1662001144
    toUser: true
    matchCondition: '"uuid" in "{{content}}" and "does not exist".replace(" ", "") in "{{content}}"'
    reason: Linux系统GRUB配置中root根目录的UUID错误
    innerReason: Linux系统GRUB配置中root根目录的UUID错误

  - configId: 1662001145
    toUser: true
    matchCondition: '"system halt".replace(" ", "") in "{{content}}"'
    reason: Linux系统关机时卡住
    innerReason: Linux系统关机时卡住
    solution: 可以尝试控制台强制重启实例（内存中的缓存数据可能会丢失）

  - configId: 1662001146
    toUser: true
    matchCondition: '"A start job is running".replace(" ", "") in "{{content}}"'
    reason: Linux系统/etc/fstab文件中配置的某个挂载点对应的设备不存在
    innerReason: Linux系统/etc/fstab文件中配置的某个挂载点对应的设备不存在

  - configId: 1662001147
    toUser: true
    matchCondition: '"panic" in "{{content}}"'
    reason: Linux系统出现操作系统崩溃
    innerReason: Linux系统出现操作系统崩溃

  - configId: 1662001148
    toUser: true
    matchCondition: '(".im" in "{{content}}" or ".img" in "{{content}}" or ".ing" in "{{content}}" or "file" in "{{content}}") and ("mot found".replace(" ", "") in "{{content}}" or "not found".replace(" ", "") in "{{content}}") or ("bin/bash" in "{{content}}" or "bin/sh" in "{{content}}") and "No such file or directory".replace(" ", "") in "{{content}}"'
    reason: Linux系统的关键系统文件缺失
    innerReason: Linux系统的关键系统文件缺失

  - configId: 1662001149
    toUser: true
    matchCondition: '"fsck" in "{{content}}"'
    reason: Linux系统启动过程中文件系统fsck检查出异常
    innerReason: Linux系统启动过程中文件系统fsck检查出异常
    solution: 按提示Press F自动修复，如果修复失败，可以尝试按S跳过对应的/data挂载，继续启动OS，在进入OS之后实际再手动执行fsck检测对应挂载点/data所在的文件系统具体异常

  - configId: 1662001150
    toUser: true
    matchCondition: '"Give root password for maintenance".replace(" ", "") in "{{content}}"'
    reason: Linux系统中/etc/fstab文件中配置的某个挂载点对应的设备不存在，或系统启动过程中文件系统fsck检查出异常
    innerReason: fstab异常需要进入修复模式
    solution: 如果是Fstab内配置的挂载点对应的设备不存在，在输入密码进入系统之后，执行mount -a 查看具体报错的行，注释或者删除该行配置之后重新启动；如果是文件系统异常，在输入密码进入系统之后，执行fsck 检测的对应的错误，根据实际情况修复

  - configId: 1671696288
    toUser: true
    matchCondition: '"Failed to Start Switch Root".replace(" ","") in "{{content}}"'
    reason: Linux initrd switch root失败
    innerReason: Linux initrd switch root失败
    solution: 参考文档修复：https://help.aliyun.com/document_detail/450291.html#3032eb90290k5

  - configId: 1684829581
    toUser: true
    matchCondition: '("Outofmemory" in "{{content}}" or "Out3ofmemory" in "{{content}}") and "Memory cgroup".replace(" ", "") in "{{content}}"'
    reason: Linux实例cgroup内存空间不足，导致操作系统出现cgroup内存溢出（Memory cgroup OOM）
    innerReason: Linux系统出现内存cgroup OOM
    solution: 参考文档修复：https://help.aliyun.com/document_detail/450291.html#751806b0505ef

  - configId: 1684829582
    toUser: true
    matchCondition: '"Outofmemory" in "{{content}}" or "Out3ofmemory" in "{{content}}"'
    reason: Linux实例内存空间不足，导致操作系统出现内存溢出（OOM）
    innerReason: Linux出现OOM
    solution: 参考文档修复：https://help.aliyun.com/document_detail/450291.html#751806b0505ef

  - configId: 1684829583
    toUser: false
    matchCondition: '"Failed to remount".replace(" ", "") in "{{content}}"'
    reason: Linux挂载根文件系统失败
    innerReason: Linux挂载根文件系统失败

  - configId: 1684829584
    toUser: false
    matchCondition: '"Failed to start Journal Service".replace(" ", "") in "{{content}}"'
    reason: Linux启动Journal Service失败
    innerReason: Linux启动Journal Service失败

  - configId: 1684829585
    toUser: false
    matchCondition: '"Could not boot".replace(" ", "") in "{{content}}" and "Starting Dracut Emergency Shell".replace(" ", "") in "{{content}}"'
    reason: Linux系统启动失败，进入紧急模式
    innerReason: Linux系统启动失败，进入紧急模式

  - configId: 1684829586
    toUser: false
    matchCondition: '"Failed to establish a new connection".replace(" ", "") in "{{content}}" or "Failed to establish a new commection".replace(" ", "") in "{{content}}"'
    reason: Linux网络异常
    innerReason: Linux网络异常

  - configId: 1684829588
    toUser: false
    matchCondition: '"need to load the kernel first".replace(" ", "") in "{{content}}"'
    reason: Linux系统启动时无法读取内核
    innerReason: Linux系统启动时无法读取内核

  - configId: 1686570955
    toUser: false
    matchCondition: '"Booting" in "{{content}}" and "from HardDisk".replace(" ", "") in "{{content}}"'
    reason: 虚拟机启动卡在硬盘数据加载阶段
    innerReason: 虚拟机启动卡在硬盘数据加载阶段

  - configId: 1687169879
    toUser: false
    matchCondition: '"keys to change the selection".replace(" ", "") in "{{content}}"'
    reason: grub流程进入选内核模式
    innerReason: grub流程进入选内核模式

  - configId: 1687169882
    toUser: false
    matchCondition: '"driver_register" in "{{content}}"'
    reason: Linux系统启动时卡在驱动注册阶段
    innerReason: Linux系统启动时卡在驱动注册阶段

  - configId: 1687772305
    toUser: false
    matchCondition: '("Unable to access opcode".replace(" ", "") in "{{content}}" and "segfault" in "{{content}}") or "Bad RIP value".replace(" ", "") in "{{content}}"'
    reason: Linux系统内存访问异常
    innerReason: Linux系统内存访问异常

  - configId: 1687772306
    toUser: false
    matchCondition: '"login：" in "{{content}}" or ("root@" in "{{content}}" and "# " in "{{content}}")'
    reason: Linux系统登录成功
    innerReason: Linux系统登录成功

  - configId: 1688005405
    toUser: false
    matchCondition: '"can''t find command error".replace(" ", "") in "{{content}}"'
    reason: Linux系统找不到执行命令
    innerReason: Linux系统找不到执行命令

  - configId: 1688005406
    toUser: false
    matchCondition: '"softlockup" in "{{content}}"'
    reason: Linux内核出现softlockup
    innerReason: Linux内核出现softlockup

  - configId: 1688005408
    toUser: false
    matchCondition: '"Fixing recursive fault but reboot is needed".replace(" ", "") in "{{content}}" or ("Fixing" in "{{content}}" and "reboot is needed".replace(" ", "") in "{{content}}")'
    reason: 修复recursive故障需要重新启动
    innerReason: 修复recursive故障需要重新启动

  - configId: 1688525740
    toUser: false
    matchCondition: '"virtnet_poll" in "{{content}}" and "interrupt" in "{{content}}"'
    reason: Linux系统中断处理过程异常
    innerReason: Linux系统中断处理过程异常

  - configId: 1688525741
    toUser: false
    matchCondition: '"new mount options do not match the existing superblock".replace(" ", "") in "{{content}}"'
    reason: Linux实例挂载项与host不一致
    innerReason: Linux实例挂载项与host不一致

  - configId: 1688525742
    toUser: false
    matchCondition: '"Failed unmounting".replace(" ", "") in "{{content}}"'
    reason: Linux解除挂载失败
    innerReason: Linux解除挂载失败

  - configId: 1688525743
    toUser: false
    matchCondition: '"Failed to start Login Service".replace(" ", "") in "{{content}}"'
    reason: Linux开机启动失败
    innerReason: Linux开机启动失败

  - configId: 1689306227
    toUser: false
    matchCondition: '"StartPXEoverIPv6onMAC" in "{{content}}" or "StartPXEoverIPv4onMAC" in "{{content}}" or ("BIOS" in "{{content}}" and ("no such file or directory".replace(" ", "") in "{{content}}" or "Press <DEL> to SETUP or <F11> to Boot Menu".replace(" ", "") in "{{content}}"))'
    reason: 开机卡在BIOS阶段
    innerReason: 开机卡在BIOS阶段

  - configId: 1689306228
    toUser: false
    matchCondition: '"No bootable".replace(" ", "") in "{{content}}"'
    reason: BIOS阶段找不到启动设备
    innerReason: BIOS阶段找不到启动设备

  - configId: 1689306229
    toUser: false
    matchCondition: '"Module scsi_wait_scan not found".replace(" ", "") in "{{content}}" or "Module scsi wait scan not found".replace(" ", "") in "{{content}}"'
    reason: 内核异常导致系统启动失败
    innerReason: 内核异常导致系统启动失败

  - configId: 1689306230
    toUser: false
    matchCondition: '"Unable to find any devices of the type needed".replace(" ", "") in "{{content}}"'
    reason: 系统安装后找不到可用设备
    innerReason: 系统安装后找不到可用设备

  - configId: 1689905277
    toUser: false
    matchCondition: '"Can not open access to console".replace(" ", "") in "{{content}}" or "Cam not open access to console".replace(" ", "") in "{{content}}"'
    reason: linux系统fstab挂载错误
    innerReason: linux系统fstab挂载错误

  - configId: 1690357839
    toUser: false
    matchCondition: '"hung_task_timeout_secs" in "{{content}}"'
    reason: 虚拟机内核出现非预期HungTask
    innerReason: 虚拟机内核出现非预期HungTask

  - configId: 1690357840
    toUser: false
    matchCondition: '("Failed to".replace(" ", "") in "{{content}}" and "grub" in "{{content}}") or "ZoomSize" in "{{content}}"'
    reason: 系统开机卡在grub引导阶段
    innerReason: 系统开机卡在grub引导阶段

  - configId: 1690357841
    toUser: false
    matchCondition: '"Starting dracut".replace(" ", "") in "{{content}}"'
    reason: 系统开机卡在初始化阶段
    innerReason: 系统开机卡在初始化阶段

  - configId: 1690357842
    toUser: false
    matchCondition: '"Dependency failed".replace(" ", "") in "{{content}}"'
    reason: 系统开机找不到依赖文件
    innerReason: 系统开机找不到依赖文件

  - configId: 1690357843
    toUser: false
    matchCondition: '"do_trap" in "{{content}}" and "divide error".replace(" ", "") in "{{content}}" or "do_divide_error".replace(" ", "") in "{{content}}"'
    reason: 除零异常引发内核panic
    innerReason: 除零异常引发内核panic

  - configId: 1690805590
    toUser: false
    matchCondition: '"lowmem reserve".replace(" ", "") in "{{content}}"'
    reason: Linux系统内存分配异常
    innerReason: Linux系统内存分配异常

  - configId: 1690805591
    toUser: false
    matchCondition: '"system d-shutdown".replace(" ", "") in "{{content}}"'
    reason: Linux系统卡在关机阶段
    innerReason: Linux系统卡在关机阶段

  - configId: 1691117315
    toUser: false
    matchCondition: '"alize" in "{{content}}"'
    reason: 虚拟机黑屏无法操作
    innerReason: 虚拟机黑屏无法操作

  - configId: 1697180908
    toUser: false
    matchCondition: '"failed to allocate default IOMMU domain".replace(" ", "") in "{{content}}"'
    reason: Linux启动过程中申请IOMMU domain失败
    innerReason: Linux启动过程中申请IOMMU domain失败

  - configId: 1697180909
    toUser: false
    matchCondition: '"Hardware Error".replace(" ", "") in "{{content}}"'
    reason: Linux出现硬件故障
    innerReason: Linux出现硬件故障

  - configId: 1697180910
    toUser: false
    matchCondition: '"Failed to start numa".replace(" ", "") in "{{content}}"'
    reason: Linux启动numa sysctl失败
    innerReason: Linux启动numa sysctl失败

  - configId: 1699415392
    toUser: false
    matchCondition: '"Call Trace".replace(" ", "") in "{{content}}" or "endtrace" in "{{content}}"'
    reason: Linux内核出现异常CallTrace
    innerReason: Linux内核出现异常CallTrace

  - configId: 1699967285
    toUser: false
    matchCondition: '"Unknown symbol".replace(" ", "") in "{{content}}"'
    reason: Linux内核模块加载失败
    innerReason: Linux内核模块加载失败

  - configId: 1699967286
    toUser: false
    matchCondition: '"insmod in".replace(" ", "") in "{{content}}"'
    reason: Linux卡在载入内核模块阶段
    innerReason: Linux卡在内核载入模块阶段

  - configId: 1700644568
    toUser: false
    matchCondition: '"Failed to start".replace(" ", "") in "{{content}}"'
    reason: Linux启动出现service启动失败
    innerReason: Linux启动出现service启动失败

  - configId: 1700644569
    toUser: false
    matchCondition: '"nvidia" in "{{content}}" and "module verification failed".replace(" ", "") in "{{content}}"'
    reason: gpu驱动加载失败
    innerReason: gpu驱动加载失败

  - configId: 1700644570
    toUser: false
    matchCondition: '"UEFI Interact".replace(" ", "") in "{{content}}" and "Press ESC".replace(" ", "") in "{{content}}"'
    reason: 系统处于UEFI启动模式
    innerReason: 系统处于UEFI启动模式

  - configId: 1701441989
    toUser: false
    matchCondition: '"problem has been detected".replace(" ", "") in "{{content}}" or "If this is the first time you".replace(" ", "") in "{{content}}"'
    reason: Linux系统意外关闭或重启
    innerReason: Linux系统意外关闭或重启

  - configId: 1701441990
    toUser: false
    matchCondition: '"Memory failure".replace(" ", "") in "{{content}}"'
    reason: Linux系统出现内存异常
    innerReason: Linux系统出现内存异常

  - configId: 1702648241
    toUser: false
    matchCondition: '"dracut-initqueue timeout".replace(" ", "") in "{{content}}"'
    reason: 系统安装未找到引导文件
    innerReason: 系统安装未找到引导文件

  - configId: 1702648242
    toUser: false
    matchCondition: '"Unable to render networking".replace(" ", "") in "{{content}}"'
    reason: 未找到可用网络渲染器，网络配置可能已损坏
    innerReason: 未找到可用网络渲染器，网络配置可能已损坏

  - configId: 1702648243
    toUser: false
    matchCondition: '"正在扫描和修复驱动器" in "{{content}}" or "Scanning and repairing drive".replace(" ", "") in "{{content}}"'
    reason: 开机检测修复驱动器
    innerReason: 开机检测修复驱动器

  - configId: 1704553468
    toUser: false
    matchCondition: '"正在准备自动修复" in "{{content}}" or "preparing Automatic repair".replace(" ", "") in "{{content}}"'
    reason: 系统正在准备自动修复
    innerReason: 系统正在准备自动修复

  - configId: 1704553469
    toUser: false
    matchCondition: '"PTE Read access".replace(" ", "") in "{{content}}" and "DMAR" in "{{content}}" and "handling fault status".replace(" ", "") in "{{content}}"'
    reason: 疑似IOMMU开启导致无法开机
    innerReason: 疑似IOMMU开启导致无法开机，可以通过修改内核参数消除：intel_iommu=off

  - configId: 1708502519
    toUser: false
    matchCondition: '"Can''t find".replace(" ", "") in "{{content}}" and "filesystem" in "{{content}}"'
    reason: Linux文件系统异常
    innerReason: Linux文件系统异常

  - configId: 1721705306
    toUser: false
    matchCondition: '"waiting for device".replace(" ", "") in "{{content}}" and "failed with error".replace(" ", "") in "{{content}}" and "mount" in "{{content}}"'
    reason: FreeBSD 11/12在阿里云运行需要内核相关补丁的支持
    innerReason: FreeBSD 11/12在阿里云运行需要内核相关补丁的支持
    solution: 参考文档：https://help.aliyun.com/zh/ecs/user-guide/the-freebsd-operating-system-compatibility?spm=5176.28426678.J_HeJR_wZokYt378dwP-lLl.1.ed7f5181vgy4oL&scm=20140722.S_help@@%E6%96%87%E6%A1%A3@@2358164.S_BB2@bl+RQW@ag0+BB1@ag0+hot+os0.ID_2358164-RL_freebsd-LOC_search~UND~helpdoc~UND~item-OR_ser-V_3-P0_0

  - configId: 1725003198
    toUser: false
    matchCondition: '("new mount options".replace(" ", "") in "{{content}}" or "mew mount options".replace(" ", "") in "{{content}}") and "match the existing superblock".replace(" ", "") in "{{content}}"'
    reason: 文件系统挂载异常，指定的挂载选项与superblock中记录的选项不一致
    innerReason: 文件系统挂载异常，指定的挂载选项与superblock中记录的选项不一致

  - configId: 1725003199
    toUser: false
    matchCondition: '"can''t find command".replace(" ", "") in "{{content}}"'
    reason: 命令未找到，疑似执行的脚本或文件在指定路径中不存在
    innerReason: 命令未找到，疑似执行的脚本或文件在指定路径中不存在

  - configId: 1725462342
    toUser: false
    matchCondition: '"cloud-init".replace(" ", "") in "{{content}}"'
    reason: 开机卡在cloud-init初始化阶段
    innerReason: 开机卡在cloud-init初始化阶段

  - configId: 1725462343
    toUser: false
    matchCondition: '"boot the selected OS".replace(" ", "") in "{{content}}" and "Debian" in "{{content}}"'
    reason: Debian系统启动卡在开机引导流程
    innerReason: Debian系统启动卡在开机引导流程

  - configId: 1725462344
    toUser: false
    matchCondition: '"saving vmcore".replace(" ", "") in "{{content}}" and "kdump" in "{{content}}"'
    reason: 系统正在执行kdump内存转储
    innerReason: 系统正在执行kdump内存转储

  - configId: 1725462345
    toUser: false
    matchCondition: '"this hardware has not undergone upstream testing".replace(" ", "") in "{{content}}"'
    reason: 开机进入紧急模式，疑似文件系统元数据损坏导致
    innerReason: 开机进入紧急模式，疑似文件系统元数据损坏导致

