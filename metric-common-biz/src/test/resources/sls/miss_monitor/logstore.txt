cn_dmar_fault,alisyslog_conman_moc,ecs-xunji<PERSON>,true
vm_under_tlp_ddos_fastpath,,ecs-x<PERSON><PERSON><PERSON>,EMPTY_PROJECT_OR_LOGSTORE
nc_ce_cnt_exception,monitor_exception_sls_alert,ecs-xunjian,true
unlimited_t5_surplus_credit,credit_info_monitor,151,false
create_modify_vm_failed,houyi_access,151,false
tcp_allocate_and_inuse_increased,nc_network,ecs-xunjian,true
cloudbox_xgw_bandwidth_exception,xgw_monitor,cloudbox-wulanchabu,ProjectNotExist
xdragon_cn_ssh_latency_high,check_xdragon_cn_hang_down,ecs-xunjian,true
nc_kernel_fatal_panic,alisyslog_conman,ali_syslog,ProjectNotExist
ack_vm_node_init_success,console_log,ecs-xunjian,true
ant_drill_real_time_alert,dw-store,dw-drill,ProjectNotExist
fpga_network_error,fpga_network_error,ecs-xunjian,false
blkpmd_open_disk_duplicated,iohub_pmd_log,ecs-xunjian,true
cn_nfs_server_error,alisyslog_conman_moc,ecs-xunjian,true
fpga_check_cn_pcie_slow,fpga_io_reg,ecs-xunjian2,true
vm_disk_full_conman,alisyslog_conman_moc,ecs-xunjian,true
hardware_temperature_issue_critical,nc_down_oob_detail_info,ecs-xunjian-zhangbei,ProjectNotExist
suspicious_mining_machine,cmpc_suspicious_mining_machine,ecs-ais-hardware,ProjectNotExist
vm_netcpu_limit_drop_adjust,cloudops_event_ops_log,ecs-xunjian,true
tcp_retry_too_high,nc_network,ecs-xunjian,true
hardware_error_false_positives_week,repair_order_processor,ecs-xunjian,LogStoreNotExist
vm_operation_event,houyi_access,151,false
nc_hang_too_much_drill,exception_reason_detect,ecs-xunjian-zhangbei,ProjectNotExist
live_migrate_perf_degrade_event,live_migration_perf_degrade_alert,ecs-xunjian-zhangbei,ProjectNotExist
runa_kubeagent_http_server_serve_failed,eci_sandbox_kubeagent_log,ecs-xunjian2,false
tdc_cpu_util_high,tdc_monitor,ebs-monitor,ProjectNotExist
vm_arp_increase,nc_stats_arp_ping_1min_agg,ali-aliyun-ecs-sla-net,true
taiji_ept_cnt_error,tj-mem-stat,ali-ecsvirtualization-dataplane,false
nc_memory_less_tdc_precheck_failed,tdc_upgrade_by_tianji,ali-ecs-cpr-monitor,ProjectNotExist
goc_batch_nc_down_p4,monitor_exception_sls_alert,ecs-xunjian,true
gpu_fallen_event,console_log,ecs-xunjian,true
hypercache_hybrid_io8_cache_status_check,hypercache_status_check,ecs-xunjian2,LogStoreNotExist
gpu_migrate_inspect_failure,monitor_exception_sls_alert,ecs-xunjian,true
fpga_chip_invalid_v2,iohub-server,ecs-xunjian2,false
vm_running_event,houyi_event,151,true
gpu_temp_over_limit,gpu_hardware_monitor,ecs-ais-hardware,ProjectNotExist
batch_mock_test_exception,monitor_exception_sls_alert,ecs-xunjian,true
vm_panic_from_guest_asist,linux-crash,guest-os-crash,ProjectNotExist
vm_expect_status_not_matched_long_time,monitor_exception_sls_alert,ecs-xunjian,true
eci_container_agent_health,container_agent_health_check,ecs-xunjian,false
nvme_timeout_too_long,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
cn_power_op,cn_power_op,ecs-xunjian2,true
cpu_error_msr_extraction,alisyslog_conman_moc,ecs-xunjian,true
dragonbox_cn_hardware_error_ipmi,dragonbox_cn_ipmi,ecs-xunjian,false
workitem_event,aone_workitem_classification,ecs-xunjian-zhangbei,ProjectNotExist
fpga_caterr,fpga_caterr_check,ecs-xunjian,true
customer_image_problem,ecs_screenshot_ocr_data,ecs-xunjian-zhangbei,ProjectNotExist
cloudbox_network_error,cloudbox_issue_report,ecs-ais-hardware,ProjectNotExist
predict_socket_power_reach_tdp_by_temp,hardware_thermal_power_sensor_flat,ecs-xunjian,true
qemu_zombie_process_exist,virt_monitor,ali-ecsvirtualization-dataplane,true
cn_down_long_time_no_recovery,xdc_rule_log,ecs-xunjian,true
vmcore_nc_kernel_osissue,ecs_alarm_req_result_log,ecs-xunjian,false
enable_network_queue_missmatch,ecs_net_queues,ecs-xunjian,true
key_kpatch_loaded_by_nc,moc_message,ecs-xunjian,true
control_api_rate_limit_cause_error,ecs_regionmaster_error_log,151,true
ant_drill_expected_alert,dw-store,dw-drill,ProjectNotExist
eed_blkpmd_data_error,iohub_pmd_log,ecs-xunjian,true
runa_kubeagent_http_failed,eci_sandbox_kubeagent_log,ecs-xunjian2,false
vm_vport_rx_droped,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
rusty_nc_proxy_health_exception,check_rusty_nc_proxy,ecs-xunjian2,true
vip_panic_too_many_times,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
pync_config_vport_fail,ecs_pync_log,151,true
nc_down_judge_bajie,bajie_check_nc_hang,ecs-xunjian,false
safeguard_vm_net_retry_increased,tagged_vm_network_stat_1min_agg,ecs-xunjian-zhangbei,ProjectNotExist
physical_machine_hardware_error,alisyslog_conman_moc,ecs-xunjian,true
nc_vport_over_flow,vswitch_log,ecs-xunjian2,true
ptp_latency_failpoint_incr_cross_region,ptp-pingmesh,ptp-pingmesh-zb-aliyun-public,ProjectNotExist
ecs_alarm_agent_import_libvirt_error,ecs_alarm_agent,ecs-xunjian,true
safeguard_vm_session_avg_increased,tagged_vm_network_stat_1min_agg,ecs-xunjian-zhangbei,ProjectNotExist
nc_connection_timeout_too_many,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
cn_cpu_offline,alisyslog_conman_moc,ecs-xunjian,true
vm_network_resource_adjust_exclusion,monitor_exception_sls_alert,ecs-xunjian,true
cloudops_reboot_vm_recovery,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_iohub_check_error,iohub_check_log,ecs-xunjian2,false
vm_panic_aggregate,monitor_exception_sls_alert,ecs-xunjian,true
end_to_end_loss_rate_high_in_nc,zoonet-task,zoonet-cn-shanghai-1,ProjectNotExist
cn_cmci_storm_event,moc_kern,ecs-xunjian,true
cloudops_key_ops_flow_limit,xdc_ratelimit_log,ecs-xunjian,true
key_process_segfault,alisyslog_kernel,ali_syslog,ProjectNotExist
high_risk_human_command,nc_message,ecs-xunjian2,true
nic_error_10min,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nic_control_plane_drop_increased,nc_network,ecs-xunjian,true
mem_bw_exception_yitian,check_yitian_mem_bandwidth_usage,ecs-xunjian2,false
staragent_cn_ping_check_failed,,ecs-xunjian2,EMPTY_PROJECT_OR_LOGSTORE
nc_down_uniform_check_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
nic_online_repair_finished,cloudops_biz_log,ecs-xunjian,true
tdc_startup_check_fail,abnormal_check_result,ecs-xunjian2,LogStoreNotExist
nvidia_vgpu_mgr_process,cn_process_info,ecs-xunjian2,true
nc_cpu_cache_error,mc_log,ecs-xunjian,true
guest_os_root_file_system_error,console_log,ecs-xunjian,true
vm_arp_timeout_recovery,nc_stats_arp_ping_1min_agg,ali-aliyun-ecs-sla-net,true
vm_unexpected_killed_by_other,libvirt_log,ecs-xunjian,true
expansion_and_change_check,ecs_ncinfo_gen_info,ecs-xunjian,true
vpc_command_internal_error,libvirt_log,ecs-xunjian,true
idc_hardware_batch_exception,repair_order_processor,ecs-xunjian,LogStoreNotExist
heartbeat_log_loss,pync_heartbeat_log_new,ecs-xunjian2,false
runa_core_process_cpu_over_usage,cn_process_info,ecs-xunjian2,true
ram_leak_event_aliuid,ramuser-logon,mtee3-aliyun-log,ProjectNotExist
windows_virtio_version_unexpected,console_log,ecs-xunjian,true
vm_network_rx_fifo_error,fpga_network_error,ecs-xunjian,false
nmi_submit_exception,cloudops_task_worker,ecs-xunjian,false
nc_session_too_large,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
nc_storage_device_and_slot_not_consistent,regionmaster_alarm,151,true
ecs_panic_trouble,ecs_screenshot_ocr_data,ecs-xunjian-zhangbei,ProjectNotExist
eci_start_failed_while_vm_running,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vhost_iohub_detect_error,qemu_log,ecs-xunjian,true
process_unrecover_vms,ecs_alarm_center_worker_load,ecs-xunjian,true
exclusive_group_process_unexpected,nc_vm_process_short_info,151,true
rack_mlock_event,rackpower,houyi-ops-log-service,ProjectNotExist
spoold_inspector_volume_zero_uuid_check,spoold_inspector_new,ecs-xunjian2,true
xdragon_cn_hang_or_down,check_xdragon_cn_hang_down,ecs-xunjian,true
hardware_error_from_ipmi_new,machine_ipmi_info,ecs-xunjian,true
cn_system_crash_vmcore_detail,vmcore_dmesg_all,ecs-xunjian2,true
blkpmd_communicate_iohub_ctrl_error,iohub_pmd_log,ecs-xunjian,true
multi_xdragon_cn_hang_too_long,monitor_exception_sls_alert,ecs-xunjian,true
dpdk_avs_util_high_too_long,dpdk_util,ecs-xunjian,true
vm_netcpu_limit_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
vm_single_core_high,,ecs-xunjian-zhangbei,EMPTY_PROJECT_OR_LOGSTORE
livemigrate_iohub_dirty_register,check_livemigrate_iohub_dirty_register,ecs-xunjian2,false
toutiao_rlock_lark_notify,rlock_domain_summary_bytedance,ecs-xunjian-zhangbei,ProjectNotExist
xdragon_monitor_fpga_exception_new,xdragon_monitor_new,ali-ecsvirtualization-dataplane,false
fpga_pr_check_precheck_failed,fpga_caterr_classify_processor,ecs-xunjian,LogStoreNotExist
check_pync_mem,check_pync,ecs-xunjian,true
qemu_not_support_command,libvirt_log,ecs-xunjian,true
local_disk_io_limited,iostat,151,true
asw_exception_alert,monitor_exception_sls_alert,ecs-xunjian,true
blkpmd_type_error,iohub_pmd_log,ecs-xunjian,true
softrepair_not_allowed,cloudops_task_worker,ecs-xunjian,false
tdc_io_thread_busy_wait_io,ebsls_tdc_thread_stats,ebsls-wulanchabu,ProjectNotExist
eci_merge_pool_pod_start_failed,goc-log,ops-eci-container-agent-cn-hangzhou,ProjectNotExist
ht_cahl_end,heartbeat-log,axt-hangzhou-idpt-inner-2,true
dpdk_avs_util_high_too_many,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
qemu_hot_upgrade_event,libvirt_log,ecs-xunjian,true
erdma_packet_fmt_error,erdma_net_transmit,ecs-xunjian2,false
yueyue_llc_contention,global-dataplane-isov-moc-vm,ali-ecsvirtualization-dataplane-global,ProjectNotExist
start_failed_mem_insufficient,ecs_pync_log,151,true
fpga_fault,fpga_caterr_classify_processor,ecs-xunjian,LogStoreNotExist
vm_attack_too_many,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_mce_error,alisyslog_kernel,ali_syslog,ProjectNotExist
vm_netcpu_too_high,nc_net_credit_stats,ali-aliyun-ecs-sla-net,true
cn_mcelog_memory_error_inband,moc_mcelog,ecs-xunjian2,true
virt_suspend_by_max_clients,libvirt_log,ecs-xunjian,true
erdma_rto_timeout,erdma_net_transmit,ecs-xunjian2,false
user_diagnose_polling_event,oss-log-store,oss-log-1014874968054919-cn-hangzhou,ProjectNotExist
fpga_afull_error,iohub_pcie_log,ecs-xunjian,true
vm_power_high_in_rack,nc_power_over_manger_processor,ecs-xunjian-zhangbei,ProjectNotExist
idc_unfinished_repair_order,idc_repair_sls,ecs-xunjian,LogStoreNotExist
ehpc_compute_node_cpu_load_high,ehpc_cpu_usage,ecs-xunjian2,false
live_migrate_on_src_nc_event,live_migrate_log,ecs-xunjian,LogStoreNotExist
guest_os_kernel_panic,console_log,ecs-xunjian,true
failed_to_start_daemon_cn,moc_message,ecs-xunjian,true
ptp_pingmesh_latency_increased,ptp-summary-all,ptp-summary-wulanchabu,ProjectNotExist
kmod_access_failed,libvirt_log,ecs-xunjian,true
nic_downtime_large_impacted,nic_downtime,ecs-xunjian-zhangbei,ProjectNotExist
nc_ntp_issue_try_autofix,alisyslog_system,ali_syslog,ProjectNotExist
schedule_block_offline_for_some_reason,exception_reason_detect,ecs-xunjian-zhangbei,ProjectNotExist
spool_d1x_reconnect_time,spool_log,ecs-xunjian,false
process_oom_exception_in_band,nc_kern,ecs-xunjian2,true
vport_online_failed,libvirt_log,ecs-xunjian,true
vm_vcpu_freq_flapping,dataplane-vm,ali-ecsvirtualization-dataplane,true
gpu_inspection_timeout_not_release,ecs_alarm_instance_operation_log,ecs-xunjian,false
ack_vm_node_init_failed,console_log,ecs-xunjian,true
vm_status_shutted_success,houyi_event,151,true
xen_open_pangu_config_failed,libvirt_log,ecs-xunjian,true
sda_disk_util_exception,iostat_log2,151,true
vm_attack_event,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_network_retry_increased,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
user_session_high_cause_nc_session_high,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
xhs_emr_redis_nc_tag,schedule_trace,151,true
llc_contend_exception,ecs_memory_log,ecs-xunjian,false
go2_tool_operation_on_ag,go2log,ecs-xunjian,true
ak_leak_event_aliuid,leaked-ak,mtee3-aliyun-log,ProjectNotExist
nc_l6_power_too_high,hardware_thermal_power_sensor_flat,ecs-xunjian,true
libvirt_hang,virt_monitor,ali-ecsvirtualization-dataplane,true
xdragon_local_device_lost,xdragon,ali-tianji-cms-transfer,IndexConfigNotExist
open_vm_down_migration,cancel_vm_down_migrate_processor,ecs-xunjian-zhangbei,ProjectNotExist
gshell_check_ready_timeout,ecs_pync_log,151,true
nc_hang_exception,bajie_check_nc_hang,ecs-xunjian,false
nc_core_component_not_working_exception_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
instance_reach_instance_io_limit,ecs_block_storage_event,ecs-xunjian-zhangbei,ProjectNotExist
dpdk_cpu0_100,nc_perf_cpu_cores,151,true
network_pync_status_failed,libvswitch_log,ecs-xunjian2,false
vm_uptime_drop_event_increased,monitor_exception_sls_alert,ecs-xunjian,true
check_drill_close_migration,ecs_alarm_center_worker_load,ecs-xunjian,true
nic_error_vm_end_to_end_loss,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
fpga_bdring_full_error,dpdkavs_log,ecs-xunjian2,true
edac_report_exception_for_nc,alisyslog_kernel,ali_syslog,ProjectNotExist
mce_cpu_error,alisyslog_kernel,ali_syslog,ProjectNotExist
cloudops_ops_flow_limit,xdc_ratelimit_log,ecs-xunjian,true
ipmi_nc_memory_error,alisyslog_hardware,ali_syslog,ProjectNotExist
vm_net_dev_packet_drop,ecs_net_nc_stat_netdev_1min_agg,ali-aliyun-ecs-sla-net,true
nc_hang_uniform_check,nc_hang_down_check,ecs-xunjian,true
user_accept_cloudops_vm_event,cloudops_user_accept_event,ecs-xunjian,LogStoreNotExist
essd_blkpmd_iov_error,ebsls_tdc_netpmd,ebsls-wulanchabu,ProjectNotExist
disk_repair_finished,cloudops_biz_log,ecs-xunjian,true
regionmaster_loss_network,monitor_exception_sls_alert,ecs-xunjian,true
cmdring_parse_err,iohub_pcie_log,ecs-xunjian,true
regionmaster_assign_resource_error,ecs_regionmaster_error_log,151,true
nic_online_repair_break_sla,cloudops_idc_repair,ecs-xunjian,false
vm_lm_fail_triggered_by_nc_traffic_high,maintenance_gray_log,ecs-xunjian,LogStoreNotExist
ag_nc_short_ping_check_failed_fastpath,,ecs-xunjian2,EMPTY_PROJECT_OR_LOGSTORE
vm_mem_bw_occupy_too_large,dataplane-vm,ali-ecsvirtualization-dataplane,true
process_init_memory_failed,alisyslog_system,ali_syslog,ProjectNotExist
xdragon_disk_erase_failed,iohub_boot_error,ecs-xunjian,false
live_migrate_cause_nc_hang,monitor_exception_sls_alert,ecs-xunjian,true
vm_network_retry_high,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
nc_kernel_fatal_panic_analyse_result,vmcore_detail_processor,ecs-xunjian,LogStoreNotExist
edac_report_exception_moc_kern,moc_kern,ecs-xunjian,true
avs_una_sdk_exception,una_error_log,ecs-xunjian2,false
start_vm_load_kernel_failed,qemu_log,ecs-xunjian,true
inspect_gpu_uce_from_ipmi,gpu_hardware_monitor,ecs-ais-hardware,ProjectNotExist
vm_all_core_high,,ecs-xunjian-zhangbei,EMPTY_PROJECT_OR_LOGSTORE
amd_vm_memory_bw_limit,check_amd_mem_bandwidth_usage,ecs-xunjian2,false
tdc_cpu_util_exception,nc_perf_cpu_cores,151,true
process_oom_exception,alisyslog_kernel,ali_syslog,ProjectNotExist
split_lock_exception,split_lock,ecs-xunjian,true
dpdk_avs_util_high,dpdk_util,ecs-xunjian,true
boxrate_pf_value_low,cluster_event_full,cluster-event-center,ProjectNotExist
nic_error_1day,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
check_cn_eth1_connectivity_failed,check_cn_eth1_connectivity,ecs-xunjian2,false
ptp_latency_error_public_cross_region,ptp-pingmesh,ptp-pingmesh-zb-aliyun-public,ProjectNotExist
fm7_net_vgroup_error_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
fpga_link_hardware_error,alisyslog_system,ali_syslog,ProjectNotExist
zombie_process_exception_cn,cn_process_info,ecs-xunjian2,true
idc_rack_power_exception_event,infrastructure-starunion-logstore,starunion-zhangbei-log,ProjectNotExist
goc_failure_update_task,monitor_exception_sls_alert,ecs-xunjian,true
virt_stop_vm_failed,ecs_pync_log,151,true
vnc_agent_start_failed,vnc_agent_log,ecs-xunjian2,true
nc_change_mode_exception,ecs_pync_log,151,true
recover_vm_fail_event,cloudops_http_service,ecs-xunjian,true
oob_operation_internal_error,cloudops_oob_log,ecs-xunjian,true
nic_error_impact_vm_performance,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nic_error_30min,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_attack_event_too_many,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
acl_error_packet_drop,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
vm_virtio_csum_too_much,ecs_net_nc_stat_netdev_1min_agg,ali-aliyun-ecs-sla-net,true
fpga_single_backpressure,iohub-server,ecs-xunjian2,false
vm_vcpu_freq_exception,dataplane-vm,ali-ecsvirtualization-dataplane,true
inspect_vm_perf_impact_event,ecs_ai_vm_perf_degrade_alert,ecs-xunjian-zhangbei,ProjectNotExist
idc_dns_change_event,starunion-change,starunion-zhangbei-log,ProjectNotExist
performance_feature_close,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
pcie2server_fifo_full,iohub_pcie_log,ecs-xunjian,true
blkpmd_journal_create_failed,iohub_pmd_log,ecs-xunjian,true
agentless_vmi_start_task,libvirt_log,ecs-xunjian,true
idc_rack_temperature_incident,starunion-incident,starunion-zhangbei-log,ProjectNotExist
vm_net_queue_error2,fpga_net_queue_check,ecs-xunjian2,false
disk_backplane_error,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
second_pcie_ltssm_status_error,fpga_io_reg,ecs-xunjian2,true
failed_to_start_daemon,alisyslog_system,ali_syslog,ProjectNotExist
cluster_nic_error_1day,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
block_storage_backend_upgrade,tianji_controller_deploy_log_central,ecs-xunjian,LogStoreNotExist
key_process_upgrade,check_config_release,ecs-xunjian,true
nc_iohub_check_error,iohub_inspector,ecs-xunjian2,LogStoreNotExist
runa_containerd_error_too_many,eci_containerd_log,ecs-xunjian2,false
nc_taiji_error_from_vmcore,vmcore_dmesg_all,ecs-xunjian2,true
vm_unpin_allocate_cpus_fail,abnormal_check_result,ecs-xunjian2,LogStoreNotExist
safeguard_vm_cpu_avg_increased,tagged_vm_cpu_stat_1min_agg,ecs-xunjian-zhangbei,ProjectNotExist
inspect_cpu_power_tdp_8269cy,check_cn_cpu_turbo,ecs-xunjian2,false
throttle_cause_disk_slowio,ecs_block_storage_event,ecs-xunjian-zhangbei,ProjectNotExist
nc_ssh_latency_high,nc_hang_down_check,ecs-xunjian,true
frequency_reduction_potential,general_key_metric,ecs-predict-result,ProjectNotExist
moc_dmar_fault,moc_kern,ecs-xunjian,true
vmcore_os_issue_result,kernel_diagnose_result,ecs-ais-hardware,ProjectNotExist
teamd_refresh_port_linkup_event_too_many,alisyslog_kernel,ali_syslog,ProjectNotExist
nic_dual_port_down,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
avs_upgrade_failure,nc_upgrade_stats,achelous-centre,ProjectNotExist
nfv_vm_cause_dpdkcpu_high,ecs_cloudops_log,ecs-xunjian,true
blkpmd_reopen_disk_failed,iohub_pmd_log,ecs-xunjian,true
cpu_cgroup_config_error,nc_vm_process_short_info,151,true
hugepage_surp_too_many,nc_hugepage_overall,ecs-xunjian,true
memory_hardware_prediction_samsung,ecs_nc_fault_predict_4_cloudops,ecs-xunjian,LogStoreNotExist
vm_network_flow_metric_adjust,cloudops_event_ops_log,ecs-xunjian,true
cn_available_mem_drop_too_fast,mem_info_cn,ecs-xunjian2,true
nic_downtime_event,alisyslog_kernel,ali_syslog,ProjectNotExist
monitor_instruction_high_error,dataplane-vmexit,ali-ecsvirtualization-dataplane,true
nic_doorbell_error,nc_kern,ecs-xunjian2,true
hang_process_exception,nc_vm_process_short_info,151,true
eci_qemu_port_error,qemu_log,ecs-xunjian,true
cloudbox_idc_lock_status_close,idc_monitor,cloudbox-wulanchabu,ProjectNotExist
spool_i2_downtime,spool_log,ecs-xunjian,false
fstab_error_password_need,ecs_screenshot_ocr_data,ecs-xunjian-zhangbei,ProjectNotExist
batch_vm_start_failed_in_nc,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
batch_vm_start_failed,exception_reason_detect,ecs-xunjian-zhangbei,ProjectNotExist
cn_pcie_slow_datahub,monitor_exception_sls_alert,ecs-xunjian,true
high_risk_ag_ssh_cmd,nc_message,ecs-xunjian2,true
nc_dpdk_rx_drop_increased,nc_stats_phyif_1min_agg,ali-aliyun-ecs-sla-net,true
system_readonly,ecs_regionmaster_info_log,151,true
vmoc_vm_disk_full,check_vmoc_vm_health,ecs-xunjian2,false
nc_mce_error_conman,alisyslog_conman,ali_syslog,ProjectNotExist
vm_network_session_increased_high,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
disk_iops_except_by_arrearage,houyi_access,151,false
avs_check_error,dpdkavs_post_check,ecs-xunjian2,true
vm_erdma_engine_error,erdma_engine_check,ecs-xunjian2,false
check_pync_reload_status,check_pync,ecs-xunjian,true
vm_network_traffic_dropped_to_zero,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
kuafu_tdc_ddm_error,ebsls_tdc_anet,ebsls-wulanchabu,ProjectNotExist
nc_status_changed_event,houyi_event,151,true
spoold_inspector_io_error_check,spoold_inspector_new,ecs-xunjian2,true
gpu_device_xid_error,moc_kern,ecs-xunjian,true
high_risk_split_lock_customer_event,split_lock_new,ecs-xunjian,true
create_order_out_guarantee,cloudops_biz_log,ecs-xunjian,true
nic_fec_corrected_error_increased,check_mac_port_status,ecs-xunjian2,false
cn_message_log_loss_too_long,moc_message,ecs-xunjian,true
vm_arp_rtt_increase,nc_stats_arp_ping_1min_agg,ali-aliyun-ecs-sla-net,true
runa_imageservice_error_too_many,eci_sandbox_imageservice_log,ecs-xunjian2,false
mcelog_memory_error,alisyslog_system,ali_syslog,ProjectNotExist
spool_d3c_downtime,spool_log,ecs-xunjian,false
nc_time_diff_with_controller,ecs_regionmaster_info_log,151,true
spoold_spooladm_connect_check,spoold_inspector_new,ecs-xunjian2,true
vm_lost_or_remained_error,global_virt_upgrade_full_log,ali-ecsvirtualization-dataplane-global,ProjectNotExist
eci_disk_full,eci-guest-xunjian,eci-container-agent-hangzhou,ProjectNotExist
vm_down_not_recovery_too_long,monitor_exception_sls_alert,ecs-xunjian,true
nc_online_repair_finished,cloudops_idc_repair,ecs-xunjian,false
nc_dpdk_driver_flow_drop,nc_stats_phyif_1min_agg,ali-aliyun-ecs-sla-net,true
spoold_inspector_spoold_thread_cnt_check,spoold_inspector_new,ecs-xunjian2,true
nc_systemd_session_too_much,nc_systemd_session,ecs-xunjian2,true
goc_ag_icmp_loss,monitor_exception_sls_alert,ecs-xunjian,true
xhp_monitor_data_loss_too_long,xhp_framework_heartbeat,ecs-xunjian2,true
cn_mem_error_page_too_many,moc_kern,ecs-xunjian,true
null_device_broken,,151,EMPTY_PROJECT_OR_LOGSTORE
cn_for_vm_gpu_error,moc_kern,ecs-xunjian,true
batch_ops_or_upgrade_event,cloudops_nginx_log,ecs-xunjian,false
vm_paused_exception,check_vm_kvm_detail,ecs-xunjian,true
vm_libvirt_status_change,libvirt_realtime_stat,ecs-xunjian2,true
AMD_IOMMU,alisyslog_conman_moc,ecs-xunjian,true
vm_session_limited,ecs_storage_network_high_util,ecs-xunjian2,LogStoreNotExist
nc_hang_too_long,bajie_check_nc_hang,ecs-xunjian,false
fpga_check_io_pcie_slow,fpga_io_reg,ecs-xunjian2,true
pcie_port_link_down,iohubtool_check,ecs-xunjian2,LogStoreNotExist
ht_arp_ping_rtt_1min,nc_stats_arp_ping_1min_agg,ali-aliyun-ecs-sla-net,true
vm_iohang_too_many_in_nc,monitor_exception_sls_alert,ecs-xunjian,true
ht_pedestal_end,monitor_exception_sls_alert,ecs-xunjian,true
attach_vport_failed,libvirt_log,ecs-xunjian,true
tdc_thread_polling_slow,ebsls_tdc_anet,ebsls-wulanchabu,ProjectNotExist
spool_d3c_reconnect_time,spool_log,ecs-xunjian,false
nc_nic_error_dpdk_vf_error,nic_register_info,achelous-centre,ProjectNotExist
vm_virtio_limit_drop,ecs_net_nc_stat_netdev_1min_agg,ali-aliyun-ecs-sla-net,true
iohub_seu_detected_v2,iohub-server,ecs-xunjian2,false
vm_slow_io_exception,latency,151,true
vhost_blk_start_failed,qemu_log,ecs-xunjian,true
pync_exit_exception,ecs_pync_log,151,true
physical_machine_gpu_error,alisyslog_conman_moc,ecs-xunjian,true
spool_d3s_reconnect_time,spool_log,ecs-xunjian,false
vm_crash_event_too_many_drill,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_heartbeat_loss_too_many,monitor_exception_sls_alert,ecs-xunjian,true
dpdk_avs_util_high_recovery,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_lm_caused_by_nc_dpdkcpu_high,maintenance_gray_log,ecs-xunjian,LogStoreNotExist
vm_start_in_bios_many_times,console_log,ecs-xunjian,true
eci_gpucard_lost_detect,eci-guest-xunjian,eci-container-agent-hangzhou,ProjectNotExist
gamma_ops_event,cloudops_event_ops_log,ecs-xunjian,true
goc_nc_down_for_local_storage,monitor_exception_sls_alert,ecs-xunjian,true
safeguard_vm_exception_increased,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
resource_rotation_to_bm_fail,ecs_sre_bm_transfer_error,ecs-xunjian2,LogStoreNotExist
nc_dpdk_driver_flow_drop_too_long,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
cn_device_vfio_pci_not_bind,libvirt_log,ecs-xunjian,true
cloudbox_idc_lock_status_open,idc_monitor,cloudbox-wulanchabu,ProjectNotExist
vm_lost,virt_monitor,ali-ecsvirtualization-dataplane,true
vm_release_network_error,ebs_service_log,ali-ecs-hangzhou,ProjectNotExist
vm_down_not_recovery,event_start_end,ecs-xunjian-zhangbei,ProjectNotExist
vm_bps_burst_limited,ecs_storage_network_high_util,ecs-xunjian2,LogStoreNotExist
iohub_pmd_cannot_allocate_thread,iohub_pmd_log,ecs-xunjian,true
gpu_local_inspection_status_physical,alisyslog_conman_moc,ecs-xunjian,true
huge_page_less,nc_huge_page_mem,ecs-xunjian,true
bare_metal_guest_key_service_error,alisyslog_conman_moc,ecs-xunjian,true
local_disk_mctp_process_error,hw_spool_log,ecs-xunjian,false
memory_fragmentation,alisyslog_kernel,ali_syslog,ProjectNotExist
eci_event_failed_for_phone,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vpc_async_task_timeout,request_token_processing,vpc-controller-sla-info,Unauthorized
runa_task_agent_error_too_many,eci_sandbox_task_log,ecs-xunjian2,false
inspect_fpga_error_cause_avs_error,dpdkavs_log,ecs-xunjian2,true
cn_for_vm_kernel_warning_stack,moc_kern,ecs-xunjian,true
guest_os_file_system_error,console_log,ecs-xunjian,true
cloudassitant_heartbeat_loss_after_upgrade,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
iohub_check_failed,iohub_check,ecs-xunjian,false
netpmd_tx_fifo_l1_full,dpdkavs_log,ecs-xunjian2,true
avs_latency_high,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
ack_vm_node_init_ready,console_log,ecs-xunjian,true
vm_network_traffic_decreased,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
fdr_dump_success,changencmode_log,ali-ecsvirtualization-dataplane,false
vm_llc_inhibition,global-dataplane-isov-moc-vm,ali-ecsvirtualization-dataplane-global,ProjectNotExist
vm_cpu_increased,vm_monitor_log,151,true
ptp_pingmesh_latency_public_increased,ptp-pingmesh,ptp-pingmesh-zb-aliyun-public,ProjectNotExist
boxrate_pf_value_diff_small,cluster_event_full,cluster-event-center,ProjectNotExist
nc_life_stage_factor,ecs_stab_label_nc,ecs-xunjian-zhangbei,ProjectNotExist
nc_mem_order_slow,nc_irq_hom,ecs-xunjian,false
nc_connection_timeout,ecs_regionmaster_error_log,151,true
hypercache_service_check,hypercache_alarm,ecs-xunjian2,LogStoreNotExist
vm_packet_error_cause_fpga_error,iohub-fpga-event,ecs-xunjian2,false
live_migrate_slb_session_reset,libvirt_log,ecs-xunjian,true
vm_vcpu_steal,dataplane-vm,ali-ecsvirtualization-dataplane,true
nc_session_too_large_notify,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
user_diagnose_event,pop_rpc_trace_log,ali-pop-log,true
boxrate_rt_value_low,cluster_event_full,cluster-event-center,ProjectNotExist
host_mem_less_qemu_mem_high,nc_vm_process_short_info,151,true
inspect_fpga_bm_full,fpga_io_reg,ecs-xunjian2,true
virtio_vring_err,console_log,ecs-xunjian,true
ehpc_service_not_running_managed_cluster,ehpc_services_health,ecs-xunjian2,false
service_vm_rm_init_adapter_fail,service_vm_log_on_moc,ecs-xunjian2,false
nic_enable_failed_after_repair,cloudops_task_worker,ecs-xunjian,false
alarm_agent_ag_update_nc_list_failed,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
fpga_check_cn_pcie_link_warning,fpga_io_reg_check_processor,ecs-xunjian,LogStoreNotExist
cloud_disk_reach_instance_limit,ecs_block_storage_event,ecs-xunjian-zhangbei,ProjectNotExist
high_order_memory_less,mem_fragmentation_level,151,true
runa_kubeagent_rpc_server_serve_failed,eci_sandbox_kubeagent_log,ecs-xunjian2,false
inspect_cpu_power_tdp_amd,check_cn_cpu_turbo,ecs-xunjian2,false
live_migrate_on_dst_nc_event,live_migrate_log,ecs-xunjian,LogStoreNotExist
local_disk_sic_drop,hw_spool_log,ecs-xunjian,false
ehpc_heartbeat_loss_too_long,ehpc_heartbeat,ecs-xunjian2,false
blkpmd_get_buf_failed,iohub_pmd_log,ecs-xunjian,true
nc_rpm_unhealthy,nc_rpm_health,ecs-xunjian2,true
idc_change_event_server_may_lost_power,ais_power_change_notify,ecs-ais-hardware,ProjectNotExist
nc_down_supplement_task,xdc_rule_log,ecs-xunjian,true
vm_netcpu_need_adjust,ecs_cloudops_log,ecs-xunjian,true
hardware_memory_risk_event,ecs_stab_nc_label,ecs-xunjian,LogStoreNotExist
vm_virsh_process_inconsistent,virt_monitor,ali-ecsvirtualization-dataplane,true
ddh_empty_nc_down_event,xdc_rule_log,ecs-xunjian,true
nc_down_event_fastpath,,151,EMPTY_PROJECT_OR_LOGSTORE
vm_stuck_in_migrating_status,live_migrate_log,ecs-xunjian,LogStoreNotExist
cn_process_oom_exception,moc_kern,ecs-xunjian,true
safeguard_vm_session_avg_decreased,tagged_vm_network_stat_1min_agg,ecs-xunjian-zhangbei,ProjectNotExist
ptp_pingmesh_latency_error_public2,ptp-pingmesh,ptp-pingmesh-nt-aliyun-public,ProjectNotExist
idc_repair_order_unexpected_confirm,repair_order_send,ecs-xunjian-zhangbei,ProjectNotExist
vm_iso_config_unexpected,ecs_pync_log,151,true
nic_reset_event,alisyslog_kernel,ali_syslog,ProjectNotExist
batch_query_nc_down_reason,ecs_alarm_center_worker_load,ecs-xunjian,true
malfunction_reason_detect,xdc_goc_fault,ecs-xunjian,true
spoold_inspector_spooladm_zombie_check,spoold_inspector_new,ecs-xunjian2,true
essd_blkpmd_pointer_mismatch,ebsls_tdc_netpmd,ebsls-wulanchabu,ProjectNotExist
parse_xml_error_detail,libvirt_log,ecs-xunjian,true
packet_dropped_rate_too_long,monitor_exception_sls_alert,ecs-xunjian,true
recover_preemption_disk_event,recover_preempt_log,ecs-xunjian-zhangbei,ProjectNotExist
cloudbox_compute_resource_exception,ecs_inventory,cloudbox-wulanchabu,ProjectNotExist
regionmaster_create_share_disk_failed,ecs_regionmaster_error_log,151,true
offline_repaired_order_produce,ecs_alarm_center_worker_load,ecs-xunjian,true
bare_metal_console_log_too_much,alisyslog_conman_moc,ecs-xunjian,true
pync_startvmex_exception,ecs_pync_log,151,true
vm_virtio_rx_not_limit_drop,ecs_net_nc_stat_netdev_1min_agg,ali-aliyun-ecs-sla-net,true
ant_drill_auto_recover,dw-store,dw-drill,ProjectNotExist
ilogtail_oom_unavailable,,ecs-xunjian2,EMPTY_PROJECT_OR_LOGSTORE
data_disk_util_exception,iostat_log2,151,true
cn_memory_fragmentation_conman,alisyslog_conman_moc,ecs-xunjian,true
mem_hardware_corrupted,nc_huge_page_mem,ecs-xunjian,true
cloudbox_ebs_space_exception,ebs_smarthosting,cloudbox-wulanchabu,ProjectNotExist
vm_bps_base_limited,ecs_storage_network_high_util,ecs-xunjian2,LogStoreNotExist
fpga_ur_tlp_report,iohub_pcie_log,ecs-xunjian,true
dpdk_avs_util_high_single_user,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
erdma_err_log_too_much,erdma_maininfo,ecs-xunjian2,false
ack_baremetal_node_init_failed,alisyslog_conman_moc,ecs-xunjian,true
check_pync_nproc_classic,check_pync,ecs-xunjian,true
gpu_hardware_error,alisyslog_system,ali_syslog,ProjectNotExist
goc_nc_down_4AMD_x_cluster,monitor_exception_sls_alert,ecs-xunjian,true
nc_storage_device_slot_duplicate,regionmaster_alarm,151,true
jinlun_detect_hardware_issue,jinlun_detect_result_log,ecs-xunjian,LogStoreNotExist
virt_cannot_open_devnull,libvirt_log,ecs-xunjian,true
oob_power_off_and_reset_from_cloudops,cloudops_oob_log,ecs-xunjian,true
hypercache_ka_utils_version_check,ka_inspector,ecs-xunjian,false
nc_host_mem_less,nc_memory_overall,ecs-xunjian,true
nc_hang_task_detected,alisyslog_kernel,ali_syslog,ProjectNotExist
active_ops_event_v2,cloudops_xdc_plan_event,ecs-xunjian,true
vm_vport_tx_droped,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
local_disk_drop_need_reset,hw_spool_log,ecs-xunjian,false
ddr_calibration_not_done,fpga_io_reg,ecs-xunjian2,true
channel_monitor_for_dragonfly20,virt_monitor,ali-ecsvirtualization-dataplane,true
local_disk_drop_need_offline,hw_spool_log,ecs-xunjian,false
nc_fake_down,monitor_exception_sls_alert,ecs-xunjian,true
ecs_nc_failure_prediction,prediction-push-result,ecs-feature,ProjectNotExist
ag_nc_icmp_latency_increase_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
vm_arp_drop_after_dpdkavs_restart,monitor_exception_sls_alert,ecs-xunjian,true
cn_new_system_session_increased,moc_message,ecs-xunjian,true
disk_io_limited,iostat,151,true
download_iso_fail,ecs_pync_log,151,true
cloudbox_auto_recovery_too_much,cloudops_biz_log,ecs-xunjian,true
llc_contention_potential,general_key_metric,ecs-predict-result,ProjectNotExist
vm_vcpu_pin_failed_pync,ecs_pync_log,151,true
ecs_vgroup_credit_drop,avs_moc20_vgroup_credit_drop_alert,achelous-centre,ProjectNotExist
fpga_asw_mac_drop,nc_stats_phyif_1min_agg,ali-aliyun-ecs-sla-net,true
virt_job_timeout_too_long,libvirt_log,ecs-xunjian,true
cloud_disk_io_shake,ecs_block_storage_event,ecs-xunjian-zhangbei,ProjectNotExist
nc_error_impact_vm_performance,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
gpu_cn_high_order_memory_less,cn_memory_fragmentation_level,ecs-xunjian2,true
idc_hardware_exception,repair_order_processor,ecs-xunjian,LogStoreNotExist
instance_compute_perf_loss,compute_performance_event,ecs-ai-middleoffice-sls,ProjectNotExist
suspect_wild_vm_from_odps,wild_vm_from_houyi_to_nc,ecs-xunjian-zhangbei,ProjectNotExist
inspect_attack_batch_create,monitor_exception_sls_alert,ecs-xunjian,true
vm_iohang_too_many,monitor_exception_sls_alert,ecs-xunjian,true
cloudbox_idc_monitor_data_resume,idc_monitor,cloudbox-wulanchabu,ProjectNotExist
vm_open_blk_failed_ops,xdc_rule_log,ecs-xunjian,true
live_migrate_perf_log_pull,ecs_alarm_center_worker_load,ecs-xunjian,true
ipmi_nc_cpu_error,alisyslog_hardware,ali_syslog,ProjectNotExist
pingmesh_latency_error_in_cluster,pingmesh,ecs-xunjian2,LogStoreNotExist
fpga_exception_same_reason_increase,fpga_caterr_classify_processor,ecs-xunjian,LogStoreNotExist
nc_moc_kernel_warning_stack,alisyslog_kernel,ali_syslog,ProjectNotExist
live_migrate_cancelled_event,libvirt_log,ecs-xunjian,true
guestos_dump_diagnose_event,dump_analysis_result,ecs-xunjian2,LogStoreNotExist
iohub_seu_detected,iohub_seu_log,ecs-xunjian,true
acs_flex_vm_cpu_use_high,vm_monitor_log,151,true
user_event_short_time_not_migrate,user_accept_event_fail_log,ecs-xunjian-zhangbei,ProjectNotExist
customer_system_performance_event,ecs_event_data,ecs-dashboard,ProjectNotExist
pcie_write_cmd_no_resp,alisyslog_conman_moc,ecs-xunjian,true
wuzong_perf_adjust_blacklist,wuzong_perf_degrade_alarm,ecs-ai-middleoffice-sls,ProjectNotExist
idc_lost_power_event,raptor-ecs,raptor-alarm,ProjectNotExist
nic_link_up_down,get_system_state_log,ecs-xunjian2,true
vm_end_to_end_loss_rate_high_too_long,monitor_exception_sls_alert,ecs-xunjian,true
vm_session_preempting_adjust,cloudops_event_ops_log,ecs-xunjian,true
physical_machine_oom,alisyslog_conman_moc,ecs-xunjian,true
ecs_stab_label_nc,ecs_stab_label_nc,ecs-xunjian-zhangbei,ProjectNotExist
blkpmd_check_fpga_qid_invalid,iohub_pmd_log,ecs-xunjian,true
dpdk_avs_util_part_high,dpdk_util,ecs-xunjian,true
create_local_disk_fail,ecs_pync_log,151,true
iohub_slowqueue_fma_err,iohub-server,ecs-xunjian2,false
vm_slowpath_pps_high,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
gpu_error_from_dmesg,console_log,ecs-xunjian,true
cloudbox_nc_down_12_hour,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_available_memory_low,nc_memory_overall,ecs-xunjian,true
vm_slow_io_too_many,monitor_exception_sls_alert,ecs-xunjian,true
avs_monitor_warning_too_long,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
ecs_horus_exec_err,ecs_sre_horus_log,ecs-xunjian2,true
nic_flapping_accidentally_too_long,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_exception_oob_status_error,nc_down_oob_detail_info,ecs-xunjian-zhangbei,ProjectNotExist
eci_independent_pool_controll_vm_down,event_start_end,ecs-xunjian-zhangbei,ProjectNotExist
idc_feature_close,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
cn_reboot_fail_analyse,reboot_fail_analyse_processor,ecs-xunjian-zhangbei,ProjectNotExist
cloudbox_idc_power_on,cloudbox_idc_report,ecs-ais-hardware,ProjectNotExist
multi_xdragon_cn_hang_or_down,monitor_exception_sls_alert,ecs-xunjian,true
instance_compute_perf_ops_by_others,oversale_operation,ecs-ai-middleoffice-sls,ProjectNotExist
nc_down_uniform_check,nc_hang_down_check,ecs-xunjian,true
vm_vsock_icmp_ping_loss_recovery,vsock_ping_check_processor,ecs-xunjian,LogStoreNotExist
packet_dropped_rate_too_much,ecs_net_nc_stat_netdev_1min_agg,ali-aliyun-ecs-sla-net,true
nc_dns_ping_exception,ptp_dns_ping,ptp-summary-drop-zhangjiakou,ProjectNotExist
cn_available_mem_drop_and_loss_data,mem_info_cn,ecs-xunjian2,true
spoold_inspector_io_hang_check,spoold_inspector_new,ecs-xunjian2,true
eci_agent30_start_failed,container-agent,eci-container-agent-hangzhou,ProjectNotExist
spoold_inspector_fd_limit_check,spoold_inspector_new,ecs-xunjian2,true
check_pync_nofile,check_pync,ecs-xunjian,true
dpdk_avs_util_high_3min_multi_user,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_online_repairing,cloudops_idc_repair,ecs-xunjian,false
ack_baremetal_node_init_args,alisyslog_conman_moc,ecs-xunjian,true
server_two_power_error,idc_repair_sls,ecs-xunjian,LogStoreNotExist
nic_hardware_alarm2,nc_kern,ecs-xunjian2,true
nc_total_traffic_too_high,nc_session_stat,ecs-xunjian-zhangbei,ProjectNotExist
vm_on_multi_ncs,nc_vm_process_short_info,151,true
nic_hardware_alarm3,iohub-server,ecs-xunjian2,false
nic_hardware_alarm4,polaris_warn,infrastructure-starunion,ProjectNotExist
nic_hardware_alarm5,iohub-server,ecs-xunjian2,false
nic_hardware_alarm6,alisyslog_kernel,ali_syslog,ProjectNotExist
nic_hardware_alarm7,nic_realtime_stat,ecs-xunjian2,true
discovery_reset_cn_from_cloudops,cloudops_discovery_log,ecs-xunjian,true
virtio_blk_pci_initial_failed,libvirt_log,ecs-xunjian,true
ptp_pingmesh_latency_public_increased2,ptp-pingmesh,ptp-pingmesh-nt-aliyun-public,ProjectNotExist
local_disk_abnormal_vm,hw_spool_log,ecs-xunjian,false
taiji_mem_assign_slow,collect_tj_mem,ecs-xunjian2,false
cloudbox_sec_gateway_error,cloudbox_sec_gateway_report,ecs-ais-hardware,ProjectNotExist
safeguard_vm_slowio_avg_increased,tagged_vm_io_latency_1min_agg,ecs-xunjian-zhangbei,ProjectNotExist
spoold_inspector_cpu_exclusive_check,spoold_inspector_new,ecs-xunjian2,true
vm_netcpu_recover_failure,maintenance_gray_log,ecs-xunjian,LogStoreNotExist
ri_down_resource_empty,ecs_regionmaster_error_log,151,true
nc_rpm_broken,nc_rpm_health,ecs-xunjian2,true
vm_network_credit_throttle,nc_net_credit_event,ali-aliyun-ecs-sla-net,true
data_disk_util_error_too_much,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
share_cn_host_vcpu_util_high,cn_cpu_cores_stat,ecs-xunjian,true
vm_down_end,monitor_exception_sls_alert,ecs-xunjian,true
share_host_vcpu_util_high,nc_perf_cpu_cores,151,true
hardware_temperature_issue_warning,machine_ipmi_info,ecs-xunjian,true
virt_eni_attach_detach_fail,libvirt_log,ecs-xunjian,true
vm_hw_ratio_drop_after_dpdkavs_restart,monitor_exception_sls_alert,ecs-xunjian,true
ag_vm_perf_exception,monitor_exception_sls_alert,ecs-xunjian,true
tdc_mode_unexpected,tdc_monitor,ebs-monitor,ProjectNotExist
vm_arp_ping_rtt_too_long,nc_stats_arp_ping_1min_agg,ali-aliyun-ecs-sla-net,true
cmci_storm_event,alisyslog_kernel,ali_syslog,ProjectNotExist
vm_public_bps_limited,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
tdc_start_failed,tdc_common_log,ecs-xunjian,true
ehpc_custom_exception_test,ehpc_custom_exception,ecs-xunjian2,false
pcie_slow_cn_fix_result,alisyslog_conman_moc,ecs-xunjian,true
vm_hang_send_vm_failure_event,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_vcpu_pin_failed,dataplane_allocatecpus,ali-ecsvirtualization-dataplane,true
vm_all_vcpu_util_high,dataplane-vm,ali-ecsvirtualization-dataplane,true
vm_under_dev_reset_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
vm_no_resource_migrate_unlock_nc,unlock_nc_search_result,ecs-xunjian-zhangbei,ProjectNotExist
release_blocked_by_exception,ecs_change_plan_exceptions_circuit_breaker_detail,ecs-xunjian,LogStoreNotExist
ack_baremetal_node_init_success,alisyslog_conman_moc,ecs-xunjian,true
recover_underlay_eni_failed,ecs_regionmaster_error_log,151,true
vm_shallow_to_deep_hibernate,cloudops_biz_log,ecs-xunjian,true
vm_arp_timeout_increased_for_vip,monitor_exception_sls_alert,ecs-xunjian,true
oom_analysis_result,abnormal_check_result,ecs-xunjian2,LogStoreNotExist
libvirt_connect_failed,ecs_pync_log,151,true
idc_traffic_bottleneck_by_user,xdragon-metric-anomaly-byuid,ecs-predict-result,ProjectNotExist
safeguard_vm_llc_avg_decreased,tagged_vm_cpu_stat_1min_agg,ecs-xunjian-zhangbei,ProjectNotExist
wild_vm_disk_open_failed_hang,xdc_feature_data,ecs-xunjian,true
cloudbox_wait_user_accept_event_too_long,cloudops_schedule_log,ecs-xunjian,true
oob_power_on_and_reset_from_cloudops,cloudops_oob_log,ecs-xunjian,true
asw_exception_alert_for_cloudbox,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_llc_contention,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
spool_d3s_downtime,spool_log,ecs-xunjian,false
vm_netcpu_limit_with_frag_pps_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
ptp_pingmesh_latency_failpoint_increased,ptp-pingmesh,ptp-pingmesh-zb-aliyun-public,ProjectNotExist
vm_network_retry_high_with_userid,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
vm_vport_lost_too_long,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_netcpu_credit_low,nc_net_credit_stats,ali-aliyun-ecs-sla-net,true
cloudassitant_heartbeat_uptime_drop,heartbeat-log,axt-hangzhou-idpt-inner-2,true
exclusive_cpu_pinned_repeatedly,nc_perf_vm_cpu_relations,151,true
live_migrate_on_src_nc_finish,live_migrate_log,ecs-xunjian,LogStoreNotExist
nc_hang_task_detected_conman,alisyslog_conman,ali_syslog,ProjectNotExist
ag_nc_icmp_latency_increase,nc_hang_down_check,ecs-xunjian,true
wuzong_perf_degrade_spot_operation,wuzong_perf_degrade_spot_instance_operation,ecs-ai-middleoffice-sls,ProjectNotExist
blkpmd_open_disk_unfinished_inflight,iohub_pmd_log,ecs-xunjian,true
inspect_process_memory_leak,nc_vm_process_short_info,151,true
vm_netcpu_reach_to_quota,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
cgroup_mem_limitation,nc_cgroup_memory_usage,ecs-xunjian,true
nic_down_too_long,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
dpdkavs_cpu_high_multi_user,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
cn_kernel_warning_stack,alisyslog_conman_moc,ecs-xunjian,true
inspect_steal_cause_perf_loss_migrate,perf_degrade_nc_node_alarm,ecs-ai-middleoffice-sls,ProjectNotExist
nc_down_normal_x_cluster_drill,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
iohubfwd_hang,nc_vm_process_short_info,151,true
ecs_ebs_create_failed_subnet_not_in_vpc,ebs_service_log,ali-ecs-hangzhou,ProjectNotExist
vm_active_ops_event,cloudops_accept_vm_event,ecs-xunjian,false
inner_batch_nc_down_notify_drill,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
hypercache_io_error_or_timeout,ka_inspector,ecs-xunjian,false
check_pync_virt_conn,check_pync,ecs-xunjian,true
eci_journal_error,console_log,ecs-xunjian,true
eci_vport_error,libvirt_log,ecs-xunjian,true
vm_network_session_decreased,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
vm_vcpu_steal_increased,,ecs-xunjian-zhangbei,EMPTY_PROJECT_OR_LOGSTORE
physical_machine_raid_warn,alisyslog_conman_moc,ecs-xunjian,true
physical_machine_kernel_panic_analyse,vmcore_detail_processor,ecs-xunjian,LogStoreNotExist
pync_heartbeat_error,pync_heartbeat_log_new,ecs-xunjian2,false
nc_ce_cnt_high_short_time,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
resize_fs_fail,ecs_pync_log,151,true
guestos_diagnose_item_not_normal,diagnose_item_log,ecs-xunjian-zhangbei,ProjectNotExist
nc_down_12_hour,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_panic_event,libvirt_log,ecs-xunjian,true
nc_storage_gbps_high,nc_storage_stat,ecs-xunjian-zhangbei,ProjectNotExist
nc_too_much_irq_work,alisyslog_kernel,ali_syslog,ProjectNotExist
ecs_ebs_create_failed_ip_not_in_subnet,ebs_service_log,ali-ecs-hangzhou,ProjectNotExist
empty_nc_down_too_long,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_apsara_disk_full,nc_disk_usage,ecs-xunjian,true
cgroup_config_error,libvirt_log,ecs-xunjian,true
nc_memory_sunreclaim_used_too_much,nc_memory_overall,ecs-xunjian,true
key_customer_local_disk_event_generate,ecs_event_data,ecs-dashboard,ProjectNotExist
toutiao_user_event_not_migrate,cloudops_accept_vm_event,ecs-xunjian,false
vmoc_vm_ping_loss,check_vmoc_vm_health,ecs-xunjian2,false
boxrate_pf_rt_delta_gaap,cluster_event_full,cluster-event-center,ProjectNotExist
gpu_lost_card_check,gpu_lostcard_check_processor,ecs-xunjian2,LogStoreNotExist
monitor_data_loss_too_long,nc_perf_load,151,true
libvirt_avs_init_failed,libvirt_log,ecs-xunjian,true
process_cpu_bind_unexpected,ecs_critial_process_cpu_serial,ecs-xunjian,true
ddos_cause_nc_net_perf_drop,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
bmc_or_fw_upgrade,woodpecker_deployment_log,ecs-xunjian,LogStoreNotExist
fpga_net_queue_too_large,fpga_resource_usage,ecs-xunjian2,true
client_certificate_expired,qemu_log,ecs-xunjian,true
nic_port_out_error_high,polaris_warn,infrastructure-starunion,ProjectNotExist
nc_cpu_socket_tdp_reached,ecs_power_log,ecs-xunjian,false
nic_down,get_system_state_log,ecs-xunjian2,true
user_high_load_vm_too_many,monitor_exception_sls_alert,ecs-xunjian,true
vm_start_lazyload,ecs_regionmaster_info_log,151,true
special_vm_llc_contention,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
fpga_bm_drop_with_emr,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
d2s_kmalloc_64_leak,ecs_nc_config_collect,ecs-xunjian,true
vm_rdma_driver_console_log_err,console_log,ecs-xunjian,true
mlx5_timeout_cause_resource_leak,nc_kern,ecs-xunjian2,true
disk_attach_detach_fail,ecs_pync_log,151,true
nic_fec_error,nic_mlx_stat,ecs-xunjian2,true
dpdkcpu_max_util_high_multi_user,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_down_normal_in_cluster_chengdu_drill,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
taiji_cpu_expand,global-tj-cpu-schedevent,ali-ecsvirtualization-dataplane-global,ProjectNotExist
boxrate_rt_value_count,cluster_event_full,cluster-event-center,ProjectNotExist
vm_end_to_end_loss_too_many_in_nc,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_vmexit_cnt_raise_high,dataplane-vmexit,ali-ecsvirtualization-dataplane,true
goc_nc_down_moc_in_cluster,monitor_exception_sls_alert,ecs-xunjian,true
nc_batch_down_causedby_the_same_uid,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
physical_machine_bmc_ping_error,xdragon_monitor_new,ali-ecsvirtualization-dataplane,false
eci_kill_container_hang,container-agent,eci-container-agent-hangzhou,ProjectNotExist
xdragon_iohub_pcie_process_loss,nc_vm_process_short_info,151,true
spoold_dpdk_free_mem_bytes_check,spoold_inspector_new,ecs-xunjian2,true
cloudbox_bandwidth_full,cloud_monitor_data_collector,ecs-xunjian-zhangbei,ProjectNotExist
vm_netcpu_util_recovery,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
ehpc_auto_create_node_idle,ehpc_cpu_usage,ecs-xunjian2,false
vm_vport_lost_recovery,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
libvirt_check_failed,virt_monitor,ali-ecsvirtualization-dataplane,true
dpdk_avs_util_high_3min,dpdk_util,ecs-xunjian,true
vm_stuck_in_disk_load,ecs_screenshot_ocr_data,ecs-xunjian-zhangbei,ProjectNotExist
live_migrate_on_dst_nc_finish,live_migrate_log,ecs-xunjian,LogStoreNotExist
fpga_afull_check,fpga_afull_check_processor,ecs-xunjian,LogStoreNotExist
boxrate_rt_value_diff_small,cluster_event_full,cluster-event-center,ProjectNotExist
fpga_bm_drop,check_mac_port_status,ecs-xunjian2,false
vm_system_io_high_limited,iostat,151,true
ehpc_controller_node_mem_load_high,ehpc_mem_stats,ecs-xunjian2,false
nc_session_too_large_recovery,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
cloudops_success_f0_monitor,ecs-devopsapi-dubbo,ecs-xunjian,true
nc_down_event,nc_perf_load,151,true
vm_hang_screenshot_ocr_reason,ecs_screenshot_ocr_data,ecs-xunjian-zhangbei,ProjectNotExist
gpu_local_inspection_status,console_log,ecs-xunjian,true
nc_forcibly_shutdown_ops,cloudops_event_ops_log,ecs-xunjian,true
io_reg_result_exception,cloudops_biz_log,ecs-xunjian,true
key_process_restart,nc_vm_process_short_info,151,true
nic_error_cause_vm_slowio,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
cloudops_reboot_vm_event,cloudops_accept_vm_event,ecs-xunjian,false
nc_mce_suppressed,alisyslog_kernel,ali_syslog,ProjectNotExist
vm_tx_limit_cause_netcpu_high_too_much,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_iohub_check_error_too_many_in_nc,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
eci_agent30_restartPod_failed,eci-agent-logs,eci-container-agent-hangzhou,ProjectNotExist
fpga_chip_invalid,iohub_pcie_log,ecs-xunjian,true
toutiao_start_physical_machine_failed,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
escape_live_migrate,escape_live_migrate_attrs,ecs-xunjian,LogStoreNotExist
dpdkcpu_util_high_multi_vip_user,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
regionmaster_create_volume_failed,regionmaster_ocean_log,151,true
vm_iohang_event_supp,monitor_exception_sls_alert,ecs-xunjian,true
nc_request_timed_out,ecs_regionmaster_error_log,151,true
nc_soft_irq_slow,nc_irq_sirq,ecs-xunjian,false
ops_ratelimit_overuse_too_much,alert_notify_history,ecs-xunjian,false
physical_machine_kernel_issue,alisyslog_conman_moc,ecs-xunjian,true
nc_ag_icmp_loss_drill,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_multi_vm_split_lock_exception,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_gshellcheck_not_ok_before_upgrade,virt_vm_update_em,ecs-xunjian2,true
nc_filesystem_error_conman,alisyslog_conman,ali_syslog,ProjectNotExist
xdragon_check_new,xdragon_monitor_new,ali-ecsvirtualization-dataplane,false
cn_find_no_irq_handler,moc_kern,ecs-xunjian,true
gpu_inspection_status,gpu-log,gpu-log,ProjectNotExist
hook_script_internal_error,libvirt_log,ecs-xunjian,true
alarm_agent_lose_heartbeat,,ecs-xunjian2,EMPTY_PROJECT_OR_LOGSTORE
physical_machine_disk_error,alisyslog_conman_moc,ecs-xunjian,true
idc_network_incident,starunion-incident,starunion-zhangbei-log,ProjectNotExist
vm_guest_inner_shutdown_reboot,libvirt_log,ecs-xunjian,true
agentless_vmi_destory,libvirt_log,ecs-xunjian,true
tdc_invalid_ddm_packet,ebsls_tdc_anet,ebsls-wulanchabu,ProjectNotExist
vm_crash_event_too_many,monitor_exception_sls_alert,ecs-xunjian,true
vm_netcpu_credit_use_up,nc_net_credit_stats,ali-aliyun-ecs-sla-net,true
nc_load_exception,nc_perf_load,151,true
cpu_feature_close,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_exec_repin_operation,staragent_task_log,ecs-xunjian,LogStoreNotExist
key_customer_exclusive_steal_anomaly,dataplane-vm,ali-ecsvirtualization-dataplane,true
uid_vm_all_vcpu_util_high,monitor_exception_sls_alert,ecs-xunjian,true
cloudassitant_multi_agent,heartbeat-log,axt-hangzhou-idpt-inner-2,true
physical_machine_nvme_error,alisyslog_conman_moc,ecs-xunjian,true
goc_nc_down_normal_in_cluster,monitor_exception_sls_alert,ecs-xunjian,true
eci_kernel_critical_issue,console_log,ecs-xunjian,true
pync_unexpected_attribute_error,ecs_pync_log,151,true
nc_network_flow_too_high,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
user_event_not_migrate,cloudops_accept_vm_event,ecs-xunjian,false
pync_api_timeout,ecs_pync_log,151,true
cloudops_maintenance_event,maintenance_gray_log,ecs-xunjian,LogStoreNotExist
kvm_mark_vm_sleeping,moc_message,ecs-xunjian,true
offline_process_too_long,maintenance_gray_log,ecs-xunjian,LogStoreNotExist
vm_iohang_too_many_drill,monitor_exception_sls_alert,ecs-xunjian,true
eci_agent30_deletePod_failed,eci-agent-logs,eci-container-agent-hangzhou,ProjectNotExist
cloudops_reserve_instance_failed,cloudops_biz_log,ecs-xunjian,true
vm_iohang_start,vm_iohang_warning,iohang-server,ProjectNotExist
vm_dpdkcpu_preempting_adjust,cloudops_event_ops_log,ecs-xunjian,true
tx_hang_conman,alisyslog_conman,ali_syslog,ProjectNotExist
qemu_hot_upgrade_down_time_event,global-online_live_upgrade_perf,ali-ecsvirtualization-dataplane-global,ProjectNotExist
ack_vm_init_args,console_log,ecs-xunjian,true
network_feature_close,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
dmar_fault,nc_kern,ecs-xunjian2,true
regionmaster_lost_nc_heartbeat,regionmaster_alarm,151,true
nc_down_reason_recall,exception_reason_detect,ecs-xunjian-zhangbei,ProjectNotExist
idc_cancelled_repair_order_exception,repair_order_send,ecs-xunjian-zhangbei,ProjectNotExist
network_jitter_in_idc_no_vm,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_mcelog_memory_error_inband,mc_log,ecs-xunjian,true
vm_arp_timeout_event_increased,monitor_exception_sls_alert,ecs-xunjian,true
hardware_error_from_ipmi,nc_down_oob_detail_info,ecs-xunjian-zhangbei,ProjectNotExist
vm_stuck_in_disk_load_console,console_log,ecs-xunjian,true
controller_cpu_100_repeatedly,monitor_exception_sls_alert,ecs-xunjian,true
cloudbox_idc_monitor_data_loss,idc_monitor,cloudbox-wulanchabu,ProjectNotExist
iohub_create_cnvm_err,qemu_log,ecs-xunjian,true
hardware_fault_event_from_kernel,alisyslog_kernel,ali_syslog,ProjectNotExist
pingmesh_latency_error_in_idc_drill,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
live_migrate_vport_relay_config_failed,libvirt_log,ecs-xunjian,true
idc_order_out_of_warranty,repair_order_send,ecs-xunjian-zhangbei,ProjectNotExist
llc_feature_close,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_split_lock_exception_genoa,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_stop_and_repairing,maintenance_gray_log,ecs-xunjian,LogStoreNotExist
physical_machine_panic_hardware_exception,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
ptp_pingmesh_latency_error_public,ptp-pingmesh,ptp-pingmesh-zb-aliyun-public,ProjectNotExist
vm_arp_ping_timeout_ratio_high,nc_stats_arp_ping_1min_agg,ali-aliyun-ecs-sla-net,true
dpdk_avs_util_fatal_15min_notify,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
cancel_vm_down_migrate,cancel_vm_down_migrate_processor,ecs-xunjian-zhangbei,ProjectNotExist
idc_asw_network_exception_event,infrastructure-starunion-logstore,starunion-zhangbei-log,ProjectNotExist
vpc_flowlog_error,vpc_flowlog_monitor_log,ecs-xunjian2,false
dpdkcpu_max_util_high,nc_stat_cpu_cpu_agg_1m,metric-avs-ops-cn-zhangjiakou,ProjectNotExist
batch_ops_or_upgrade_by_ops_event,cloudops_event_ops_log,ecs-xunjian,true
nc_multi_user_vm_uptime_drop,monitor_exception_sls_alert,ecs-xunjian,true
vm_hang_task_event_too_many,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
iohub_pmd_wait_io_too_long,iohub_pmd_log,ecs-xunjian,true
physical_machine_kernel_panic_for_user,monitor_exception_sls_alert,ecs-xunjian,true
eci_vm_start_timeout,console_log,ecs-xunjian,true
cn_mce_killing_error,alisyslog_conman_moc,ecs-xunjian,true
vm_eni_hang_end,vm_net_queue_check_processor,ecs-xunjian,LogStoreNotExist
inspect_abnormal_long_process,nc_vm_process_short_info,151,true
nc_vf_block,nic_register_info,achelous-centre,ProjectNotExist
boxrate_pf_value_count,cluster_event_full,cluster-event-center,ProjectNotExist
spool_i4_downtime,spool_log,ecs-xunjian,false
nc_disk_exception,alisyslog_kernel,ali_syslog,ProjectNotExist
xdragon_cn_hang_too_long,check_xdragon_cn_hang_down,ecs-xunjian,true
fpga_exception_manual_report,xdragon_monitor_new,ali-ecsvirtualization-dataplane,false
pcie_flash_error,alisyslog_kernel,ali_syslog,ProjectNotExist
cloudassitant_multi_agent_increased,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_heartbeat_loss_by_xhp_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
api_worker_full_judgement,ecs_alarm_api_async_task_log,ecs-xunjian,true
nc_session_drop_to_zero,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
pync_exception_start_stop_failed,ecs_pync_log,151,true
safeguard_vm_io_latency_avg_increased,tagged_vm_io_stat_1min_agg,ecs-xunjian-zhangbei,ProjectNotExist
vm_migrate_cause_nc_down,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_mem_error_page_too_many_15min,alisyslog_kernel,ali_syslog,ProjectNotExist
houyi_update_nc_template,houyi_access,151,false
nc_dpdk_high_live_migrate,cloudops_event_ops_log,ecs-xunjian,true
batch_gpu_vm_start_error,monitor_exception_sls_alert,ecs-xunjian,true
alarm_agent_exceed_max_socket_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
dpdkavs_process_recovery,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
regionmaster_create_flow_slow,schedule_trace,151,true
check_pync_cpu,check_pync,ecs-xunjian,true
idc_network_device_event,idc_network_device_change_event_processor,ecs-xunjian-zhangbei,ProjectNotExist
spool_i2_reconnect_time,spool_log,ecs-xunjian,false
pingmesh_latency_error_in_idc,monitor_exception_sls_alert,ecs-xunjian,true
iohub_server_daemon_unexpected_err,iohub-server,ecs-xunjian2,false
fpga_ur_check,fpga_ur_check_processor,ecs-xunjian,LogStoreNotExist
virtio_driver_load_failure,alisyslog_conman_moc,ecs-xunjian,true
query_idc_unrepaired_machine,ecs_alarm_center_worker_load,ecs-xunjian,true
share_memory_used_too_much,nc_memory_overall,ecs-xunjian,true
physical_machine_kernel_panic,alisyslog_conman_moc,ecs-xunjian,true
safeguard_key_ops_generate,xdc_rule_log,ecs-xunjian,true
goc_nc_down_moc_x_cluster,monitor_exception_sls_alert,ecs-xunjian,true
baichuan_operation_record,tuoluo_operation_record,ecs-xunjian2,LogStoreNotExist
gpu_pci_config_err_lost_card,moc_kern,ecs-xunjian,true
vm_vport_tx_cpu_limit_droped,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
nc_down_alert_old,vm_down,151,true
baremetal_start_kernel_assign_mem_err,alisyslog_conman_moc,ecs-xunjian,true
vm_execute_redeploy_event,pop_rpc_trace_log,ali-pop-log,true
eci_merge_pool_panic_event_too_many,monitor_exception_sls_alert,ecs-xunjian,true
start_tpm_failed,libvirt_log,ecs-xunjian,true
xdragon_cn_mce_error,moc_kern,ecs-xunjian,true
nc_alarm_agent_log_decrease,xdragon-metric-pickup-anomaly,ecs-predict-result,ProjectNotExist
xdragon_cn_ping_loss_too_much,check_xdragon_cn_hang_down,ecs-xunjian,true
check_cn_tmpfs_usage,virt_monitor,ali-ecsvirtualization-dataplane,true
cn_for_vm_llc_contention,dataplane-isov-moc-vm,ali-ecsvirtualization-dataplane,false
mock_test_exception,bajie_process_nc_hang,ecs-xunjian,LogStoreNotExist
user_report_instance_error,cloudops_biz_log,ecs-xunjian,true
nic_asw_mac_drop,nc_stats_phyif_1min_agg,ali-aliyun-ecs-sla-net,true
spoold_communicate_mctp_timeout,hw_spool_log,ecs-xunjian,false
nc_tsc_unstable,moc_kern,ecs-xunjian,true
regionmaster_find_no_nclist,ecs_regionmaster_error_log,151,true
vmem_page_offline_by_vm,moc_kern,ecs-xunjian,true
nc_down_alert,event_start_end,ecs-xunjian-zhangbei,ProjectNotExist
cloudassitant_heartbeat_loss_high,heartbeat-log,axt-hangzhou-idpt-inner-2,true
ipmitool_hang,nc_vm_process_short_info,151,true
alarm_agent_exceed_max_socket_too_many,monitor_exception_sls_alert,ecs-xunjian,true
vm_steal_too_many,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_virtio_nobuf_drop,ecs_net_nc_stat_netdev_1min_agg,ali-aliyun-ecs-sla-net,true
cloudbox_idc_exception_event,idc_monitor,cloudbox-wulanchabu,ProjectNotExist
storage_controller_exception,alisyslog_kernel,ali_syslog,ProjectNotExist
nc_hardware_power_changed,hardware_thermal_power_sensor_flat,ecs-xunjian,true
legacy_image_start_in_uefi_way,console_log,ecs-xunjian,true
hypercache_clouddev_perf_check,hypercache_alarm,ecs-xunjian2,LogStoreNotExist
virt_prepare_vm_failed,ecs_prepare_vm_log,ecs-xunjian,false
vm_rcu_sched_stall_on_cpu_error,console_log,ecs-xunjian,true
eci_multinic_pull_images_error,container-agent,eci-container-agent-hangzhou,ProjectNotExist
monitor_data_loss_for_days,alarm_agent_lost_heartbeat,ecs-xunjian,LogStoreNotExist
ecs_ebs_create_failed_not_available_iz,ecs_dubbo_new,ali-ecs-hangzhou,ProjectNotExist
localdisk_moc_iohang_check,check_moc_iohang,ecs-xunjian2,false
tdc_admin_run_failed,ecs_pync_log,151,true
avs_oss_db_collect,collect_avs_oss_db_log,ecs-xunjian2,true
memory_hardware_prediction,ecs_nc_fault_predict_4_cloudops,ecs-xunjian,LogStoreNotExist
vm_livemigrate_exception,regionmaster_alarm,151,true
safeguard_vm_network_bps_decreased,tagged_vm_network_stat_1min_agg,ecs-xunjian-zhangbei,ProjectNotExist
spoold_inspector_nvme_probe_done_check,spoold_inspector_new,ecs-xunjian2,true
vm_guest_kernel_block_bitmap,console_log,ecs-xunjian,true
inner_batch_nc_down_notify,monitor_exception_sls_alert,ecs-xunjian,true
mlock_too_much_poc,cluster_event,cluster-event-center,ProjectNotExist
mem_bw_contend_exception,dataplane-vm,ali-ecsvirtualization-dataplane,true
nic_hardware_alarm,alisyslog_kernel,ali_syslog,ProjectNotExist
safeguard_vm_batch_loss_heartbeat,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
dragonbox_vm_icmp_ping_loss_increased,monitor_exception_sls_alert,ecs-xunjian,true
idc_network_device_error,user_report_exception,ecs-xunjian,LogStoreNotExist
pync_call_third_cmd_error,ecs_pync_log,151,true
nc_process_segfault_too_many,alisyslog_kernel,ali_syslog,ProjectNotExist
fpga_escape_mode,iohub-server,ecs-xunjian2,false
essd_blkpmd_multiple_copy_error,ebsls_tdc_netpmd,ebsls-wulanchabu,ProjectNotExist
nc_vf_tx_error_increased,nic_register_info,achelous-centre,ProjectNotExist
fpga_backpressure,fpga_network_error,ecs-xunjian,false
memleak_diagnose_event,sysak_check_result,ecs-xunjian,LogStoreNotExist
memory_not_enough_start_failed,qemu_log,ecs-xunjian,true
inspect_fpga_err_iohang_by_pmd,ebs_alert_center_internal,ali-ecs-cpr-monitor,ProjectNotExist
ebs_recover_for_iohang_fail,ebs_ops_event,ecs-xunjian2,LogStoreNotExist
vm_inner_restart_event,libvirt_log,ecs-xunjian,true
eci_microvm_error,libvirt_log,ecs-xunjian,true
dewu_vm_cpu_impact,dataplane-vm,ali-ecsvirtualization-dataplane,true
dpdk_avs_util_high_10min_multi_user,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
virtio_blk_create_device_failed,iohub_pmd_log,ecs-xunjian,true
eed_blkpmd_multiple_copy_error,iohub_pmd_log,ecs-xunjian,true
goc_nc_down_normal_x_cluster,monitor_exception_sls_alert,ecs-xunjian,true
vm_pined_status_exception,vm_pin_status,ecs-xunjian,true
win_vminit_error,console_log,ecs-xunjian,true
erase_nvme_disk_fail,spool_log,ecs-xunjian,false
gpu_check_service_vm_fabric_manager,check_service_vm,ecs-xunjian2,false
windows_boot_error,ecs_screenshot_ocr_data,ecs-xunjian-zhangbei,ProjectNotExist
eed_blkpmd_pointer_mismatch,iohub_pmd_log,ecs-xunjian,true
disk_iops_recovery,houyi_access,151,false
virtGatherMan_tool_inner_exception,virtgatherman_tool_log,ali-ecsvirtualization-dataplane,true
inspect_mining_instance,mining_worker_instance,ecs-xunjian-zhangbei,ProjectNotExist
memory_not_enough_start_failed_libvirt,libvirt_log,ecs-xunjian,true
control_process_cpu_high,nc_vm_process_short_info,151,true
nic_error_after_cable_repair,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_health_check_recovery,check_health_result,ecs-xunjian2,LogStoreNotExist
mem_bw_occur_bottleneck,dataplane-vm,ali-ecsvirtualization-dataplane,true
eed_blkpmd_crc_error,iohub_pmd_log,ecs-xunjian,true
edac_report_exception_moc_message,moc_message,ecs-xunjian,true
vm_detect_root_cause_by_change,pop_rpc_trace_log,ali-pop-log,true
gpu_vfio_init_failed,moc_kern,ecs-xunjian,true
vm_exception_push_dingjie,user_report_exception,ecs-xunjian,LogStoreNotExist
critical_alert_for_1111,monitor_exception_sls_alert,ecs-xunjian,true
vnic_single_rx_mode,rdma_monitor_data_backflow,ecs-xunjian,true
live_migrate_vm_event,ecs_pync_log,151,true
vm_check_health_fail,check_health_result,ecs-xunjian2,LogStoreNotExist
cn_dump_diagnose_event,dump_diagnose_processor,ecs-xunjian,LogStoreNotExist
fpga_crc_mismatch_ebs_backend,ebsls_blockserver,ebs-log-system-3,ProjectNotExist
gpu_device_xid_error_conman,alisyslog_conman_moc,ecs-xunjian,true
cloudassitant_heartbeat_loss,heartbeat-log,axt-hangzhou-idpt-inner-2,true
memory_allocation_failed,alisyslog_system,ali_syslog,ProjectNotExist
runa_ecilet_run_task_timeout,ecilet_log,ecs-xunjian2,false
spoold_inspector_volume_cnt_check,spoold_inspector_new,ecs-xunjian2,true
ak_leak_event_test,leaked-ak,mtee3-aliyun-log,ProjectNotExist
cloudassitant_process_killed_exception,metrics-log,axt-hangzhou-idpt-inner-2,true
vm_cpu_usage_change,,ecs-xunjian-zhangbei,EMPTY_PROJECT_OR_LOGSTORE
nc_down_hang_uniform_data_loss_too_long,nc_hang_down_check,ecs-xunjian,true
vm_start_failed,ecs_regionmaster_error_log,151,true
fpga_cfg_reg_error,iohub_pcie_log,ecs-xunjian,true
split_lock_feature_close,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
special_vm_cpu_use_high,vm_monitor_log,151,true
edac_report_exception,alisyslog_conman_moc,ecs-xunjian,true
spoold_inspector_spoold_monitor_check,spoold_inspector_new,ecs-xunjian2,true
vm_vport_tx_limit_dropped,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
nmi_received_unknown_signal,alisyslog_kernel,ali_syslog,ProjectNotExist
spool_i4_reconnect_time,spool_log,ecs-xunjian,false
network_pync_msg_hang,receivers_log,ecs-xunjian2,true
start_vm_failed_xml,ecs_pync_log,151,true
spoold_inspector_machine_ebm_check,spoold_inspector_new,ecs-xunjian2,true
dpdkcpu_part_high_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
key_customer_performance_event_generate,ecs_event_data,ecs-dashboard,ProjectNotExist
active_vm_ops_event,cloudops_xdc_plan_event,ecs-xunjian,true
idc_drilling_event,sysarch-dr-notification,sysarch-dr-notification,ProjectNotExist
erdma_error_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
vm_net_queue_error,iohub_net_rxq_check,ecs-xunjian2,false
nc_ssh_password_failed_too_many,ali_syslog_sshd_log,ali_syslog,ProjectNotExist
tdc_unexpected_ddm_table,tdc_common_log,ecs-xunjian,true
oceanbase_mce_event,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_ssh_status_recovery,nc_hang_down_check,ecs-xunjian,true
failed_to_connect_dbus,console_log,ecs-xunjian,true
nc_down_for_local_storage_nc_drill,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
guest_os_oom,console_log,ecs-xunjian,true
mem_bw_contend_by_socket,dataplane-vm,ali-ecsvirtualization-dataplane,true
eci_vmexit_exception_vm_hang_too_long,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_virtio_setup_invalid_drop,ecs_net_nc_stat_netdev_1min_agg,ali-aliyun-ecs-sla-net,true
vmcore_analysis_result_event,vmcore_analysis_result,ecs-xunjian2,LogStoreNotExist
fpga_check_cn_pcie_slow_too_many,xdc_rule_log,ecs-xunjian,true
batch_cloudbox_vm_heartbeat_loss_high,monitor_exception_sls_alert,ecs-xunjian,true
vmoc_vm_hang_error,check_vmoc_vm_health,ecs-xunjian2,false
fpga_crc_check_error,iohub_pmd_log,ecs-xunjian,true
fpga_ram_err_fm7_e6,cloudops_biz_log,ecs-xunjian,true
nic_hardware_event,alisyslog_system,ali_syslog,ProjectNotExist
vm_crash_event,libvirt_log,ecs-xunjian,true
active_ops_event,cloudops_nginx_log,ecs-xunjian,false
vm_arp_ping_recovery,monitor_exception_sls_alert,ecs-xunjian,true
inspect_migration_vm_hang_renew,xdc_rule_log,ecs-xunjian,true
pingmesh_latency_failpoint_increased,pingmesh,ecs-xunjian2,LogStoreNotExist
yueyue_cpu_use_high,vm_monitor_log,151,true
avs_hotup_vport_downtime,avs_upgrade_log,ali-aliyun-ecs-sla-net,false
fpga_cmd_ring_full,iohub_pcie_log,ecs-xunjian,true
regionmaster_stop_vm_failed,ecs_regionmaster_error_log,151,true
xenwatch_d,nc_vm_process_short_info,151,true
user_traffic_spike_in_region,xdragon-metric-anomaly-byuid,ecs-predict-result,ProjectNotExist
vm_netcpu_limit_with_detect_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
vm_vport_session_error,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
tdc_block_storage_backend_comm_error,ebsls_tdc_anet,ebsls-wulanchabu,ProjectNotExist
amd_socket_mem_bw_high,check_amd_mem_bandwidth_usage,ecs-xunjian2,false
qemu_error_hardware_entry_failed,qemu_log,ecs-xunjian,true
inspect_vm_ntp_server_error,monitor_exception_sls_alert,ecs-xunjian,true
ht_pedestal_start,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
erdma_io_thread_hang,erdma_utilization,ecs-xunjian2,false
xdragon_local_device_error,xdragon,ali-tianji-cms-transfer,IndexConfigNotExist
vmcore_collect_check_upload_fail,abnormal_check_result,ecs-xunjian2,LogStoreNotExist
cn_cpu_cache_error,moc_mcelog,ecs-xunjian2,true
storage_compute_mix_cpu_high,nc_perf_cpu_cores,151,true
eci_oom_killed_occurd,monitor_exception_sls_alert,ecs-xunjian,true
packet_frag_limit_error_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
cn_memory_fragmentation,moc_kern,ecs-xunjian,true
avs_bypass_event,iohubtool_check,ecs-xunjian2,LogStoreNotExist
ehpc_service_not_running,ehpc_services_health,ecs-xunjian2,false
inspect_ak_leak_uid_batch_create,ecs_pync_log,151,true
virt_nc_data_loss,dataplane-nc-power,ali-ecsvirtualization-dataplane,true
kvm_vm_disk_full_conman,console_log,ecs-xunjian,true
goc_cloudbox_nc_hang,monitor_exception_sls_alert,ecs-xunjian,true
vm_crash_end,monitor_exception_sls_alert,ecs-xunjian,true
vm_network_session_increased,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
vmexit_exception_vm_hang,dataplane-vmexit,ali-ecsvirtualization-dataplane,true
vm_pkt_err_cause_fpga_error_enhance,cloudops_biz_log,ecs-xunjian,true
file_system_problem,ecs_screenshot_ocr_data,ecs-xunjian-zhangbei,ProjectNotExist
qat_device_error,alisyslog_kernel,ali_syslog,ProjectNotExist
softrepair_too_much,cloudops_event_ops_log,ecs-xunjian,true
runa_ecilet_error_too_many,ecilet_log,ecs-xunjian2,false
xdragon_hot_upgrade_precheck_failed,xdragon_hot_upgrade_fpga_cmd,ecs-xunjian2,false
batch_avs_monitor_warning,monitor_exception_sls_alert,ecs-xunjian,true
virt_nc_down_reason_sync,user_report_exception,ecs-xunjian,LogStoreNotExist
physical_machine_cn_net_loss_for_user,xdc_rule_log,ecs-xunjian,true
ramos_in_free_nc,nc_vm_process_short_info,151,true
nc_hard_irq_slow,nc_irq_irq,ecs-xunjian,false
eci_nfs_error,console_log,ecs-xunjian,true
xdragon_cn_fw_secure_error,cn_fw_secure,ecs-xunjian,true
vm_locked_by_security,pop_rpc_trace_log,ali-pop-log,true
xdragon_monitor_fpga_exception,xdragon-monitor,ali-ecsvirtualization-dataplane,false
vm_tagged_key_resources,pop_rpc_trace_log,ali-pop-log,true
iso_vm_retained,nc_vm_process_short_info,151,true
spoold_inspector_volume_miss_check,spoold_inspector_new,ecs-xunjian2,true
physical_machine_user_reboot,qemu_log,ecs-xunjian,true
goc_nc_down_2u4_x_cluster,monitor_exception_sls_alert,ecs-xunjian,true
xdragon_cn_ping_loss_too_much_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
nc_cpu_offline,alisyslog_kernel,ali_syslog,ProjectNotExist
gpu_inspection_fatal_error_report,idc_push_event_processor,ecs-xunjian-zhangbei,ProjectNotExist
update_vm_feature_event,houyi_access,151,false
nc_nic_mtu_error,nic_register_info,achelous-centre,ProjectNotExist
nc_capacity_change_error,ecs_regionmaster_error_log,151,true
nc_ssh_status_recovery_v2,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
virt_report_nc_exception,,ecs-xunjian-zhangbei,EMPTY_PROJECT_OR_LOGSTORE
teamd_port_enable_event_too_many,alisyslog_kernel,ali_syslog,ProjectNotExist
vm_retained,virt_monitor,ali-ecsvirtualization-dataplane,true
hardware_error_false_positives,repair_order_processor,ecs-xunjian,LogStoreNotExist
vm_key_parse_error_drop,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
cmdr_inspect_seu_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
vm_status_change_to_paused,libvirt_log,ecs-xunjian,true
nc_root_disk_full,nc_disk_usage,ecs-xunjian,true
vm_guest_network_service_error,console_log,ecs-xunjian,true
cloudops_softrepair_success,cloudops_biz_log,ecs-xunjian,true
vm_perf_disturbed_event,ecs_cloudops_log,ecs-xunjian,true
vm_prepare_host_device_failed,libvirt_log,ecs-xunjian,true
netpmd_tx_packet_paddr_error,dpdkavs_log,ecs-xunjian2,true
nic_fec_corrected_error_too_long,monitor_exception_sls_alert,ecs-xunjian,true
credit_perf_mode_change,credit_spec_change,151,false
vm_resize_disk_failed,ecs_pync_log,151,true
kvm_intercepted_exception_vm,abnormal_check_result,ecs-xunjian2,LogStoreNotExist
inspect_shmem_memory_leak,nc_memory_overall,ecs-xunjian,true
gpu_fabric_manager_process_quit,service_vm_log_on_moc,ecs-xunjian2,false
vfio_device_initial_failed,libvirt_log,ecs-xunjian,true
agent_postcheck_failed,health_check,ecs-xunjian2,false
nc_need_restart_but_not_recover,xdc_rule_log,ecs-xunjian,true
spoold_inspector_nvme_linkdown_check,spoold_inspector_new,ecs-xunjian2,true
vm_vsock_icmp_ping_loss_new,vsock_ping_check_processor,ecs-xunjian,LogStoreNotExist
fpga_pr_detect_live_migrate,maintenance_gray_log,ecs-xunjian,LogStoreNotExist
eci_agent30_createPod_failed,eci-agent-logs,eci-container-agent-hangzhou,ProjectNotExist
membw_contention_potential,general_key_metric,ecs-predict-result,ProjectNotExist
cn_for_vm_cpu_offline,moc_kern,ecs-xunjian,true
vm_retry_after_dpdkavs_restart,monitor_exception_sls_alert,ecs-xunjian,true
nc_exception_push_dingjie,user_report_exception,ecs-xunjian,LogStoreNotExist
vm_guest_process_segment_fault,console_log,ecs-xunjian,true
vm_migrate_nc_event,houyi_event,151,true
alarm_agent_run_exception,ecs_alarm_agent_tianji_stderr,ecs-xunjian,true
crond_update_root_cause,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
discovery_reset_moc_from_cloudops,cloudops_discovery_log,ecs-xunjian,true
memory_less_start_process_failed,alisyslog_system,ali_syslog,ProjectNotExist
hostdev_detach_timeout_too_many,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_vfio_err_notifier,qemu_log,ecs-xunjian,true
xdragon_key_process_upgrade_event,tianji_deploy_log_central,ecs-xunjian,LogStoreNotExist
process_exception,nc_vm_process_short_info,151,true
ptp_pingmesh_latency_error,ptp-summary-all,ptp-summary-wulanchabu,ProjectNotExist
spoold_inspector_cpu_affinity_check,spoold_inspector_new,ecs-xunjian2,true
eci_root_disk_full,eci-guest-xunjian,eci-container-agent-hangzhou,ProjectNotExist
idc_asw_device_change_event,starunion-change,starunion-zhangbei-log,ProjectNotExist
ehpc_vcpu_flapping,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_down_normal_in_cluster_drill,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
spoold_inspector_service_status_check,spoold_inspector_new,ecs-xunjian2,true
nic_flapping_cause_disk_slowio,ecs_block_storage_event,ecs-xunjian-zhangbei,ProjectNotExist
inner_aliuid_vm_down_too_many,monitor_exception_sls_alert,ecs-xunjian,true
standard_t5_performance_restrict,credit_info_monitor,151,false
vm_disk_io_latency_exception,iostat,151,true
guest_os_pci_slot_not_enough,libvirt_log,ecs-xunjian,true
vm_retry_high_unexpected_gather,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
essd_blkpmd_address_error,ebsls_tdc_netpmd,ebsls-wulanchabu,ProjectNotExist
vm_retry_high_caused_by_dpdkcpu_high,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_operation_invalid_argument,libvirt_log,ecs-xunjian,true
query_power_on_and_reset_record,nc_down_oob_operate_record,ecs-xunjian-zhangbei,ProjectNotExist
open_pangu_config_failed,libvirt_log,ecs-xunjian,true
fpga_hotup_fifo_full,iohub_pcie_log,ecs-xunjian,true
asw_critical_alert,paoding_network_error,ecs-xunjian,LogStoreNotExist
dpdkcpu_part_high_io_balance,cloudops_event_ops_log,ecs-xunjian,true
essd_blkpmd_data_error,ebsls_tdc_netpmd,ebsls-wulanchabu,ProjectNotExist
teamd_device_event_4_too_many,alisyslog_kernel,ali_syslog,ProjectNotExist
cn_pcie_link_down_kern,moc_kern,ecs-xunjian,true
pcie_backpressure_event,pcie_backpressure_check_processor,ecs-xunjian-zhangbei,ProjectNotExist
rm_start_check_unfinished_task_error,ecs_regionmaster_error_log,151,true
iohub_fd_cause_msg_fail,iohub_pcie_log,ecs-xunjian,true
query_power_off_and_reset_record,nc_down_oob_operate_record,ecs-xunjian-zhangbei,ProjectNotExist
runa_kubeagent_proxyserver_error,eci_sandbox_kubeagent_log,ecs-xunjian2,false
idc_asw_device_change_detail_event,starunion-change-detail,starunion-zhangbei-log,ProjectNotExist
vm_eni_hang_start,iohub_net_rxq_check,ecs-xunjian2,false
tdc_offline_page,tdc_mcelog_trigger_audit,ecs-xunjian2,false
runa_ecilet_run_task_failed,ecilet_log,ecs-xunjian2,false
nvme_reset_with_iohang,tdc_monitor,ebs-monitor,ProjectNotExist
isovm_retained,virt_monitor,ali-ecsvirtualization-dataplane,true
controller_cpu_100,nc_perf_cpu_cores,151,true
nc_available_mem_drop_too_fast,nc_memory_overall,ecs-xunjian,true
process_d_state_too_long,d_process,ecs-xunjian2,false
eci_vm_start_failed_recover,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
ag_nc_short_ping_check_failed,nc_ping_fail_by_ag,ecs-xunjian2,false
uid_batch_create_instance,monitor_exception_sls_alert,ecs-xunjian,true
vm_iohang_end,vm_iohang_warning,iohang-server,ProjectNotExist
nc_dpdkcpu_expansion,cloudops_event_ops_log,ecs-xunjian,true
vm_session_too_large,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
process_exception_too_much,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vm_vport_lost,check_netdev_lost,ecs-xunjian2,true
nic_flapping_exception,ecs_block_storage_event,ecs-xunjian-zhangbei,ProjectNotExist
batch_cloudbox_pync_heartbeat_log_loss,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
windows_os_problem,ecs_screenshot_ocr_data,ecs-xunjian-zhangbei,ProjectNotExist
gpu_vm_migrate_event,monitor_exception_sls_alert,ecs-xunjian,true
zombie_process_exception,nc_vm_process_short_info,151,true
vm_hang_event_not_avoid_too_long,monitor_exception_sls_alert,ecs-xunjian,true
rlock_resource_used_exception,houyi_event,151,true
cn_pcie_link_down,alisyslog_conman_moc,ecs-xunjian,true
nc_black_hole_memory_large,nc_memory_overall,ecs-xunjian,true
vm_operation_failed,regionmaster_alarm,151,true
cn_mem_err_col_row_too_much,moc_kern,ecs-xunjian,true
fpga_check_cn_pcie_lane_slow,fpga_io_reg,ecs-xunjian2,true
xdragon_cn_mce_suppressed,moc_kern,ecs-xunjian,true
asw_exception_alert_for_dedicated_region,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
spoold_inspector_spooladm_cnt_check,spoold_inspector_new,ecs-xunjian2,true
hostdev_detach_timeout,libvirt_log,ecs-xunjian,true
abnormal_long_process_increase,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
vmem_page_offline,moc_kern,ecs-xunjian,true
runa_sandboxer_error_too_many,eci_sandboxer_log,ecs-xunjian2,false
nc_taiji_error,alisyslog_kernel,ali_syslog,ProjectNotExist
ops_nc_too_much,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
file_system_error_conman,alisyslog_conman,ali_syslog,ProjectNotExist
storage_nc_cause_vm_iohang,vm_iohang_detect_processor,ecs-xunjian,LogStoreNotExist
cgpu_evm_key_console_log,console_log,ecs-xunjian,true
ht_cahl_start,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
ht_cahl_lost,heartbeat-log,axt-hangzhou-idpt-inner-2,true
xdragon_hot_upgrade_fpga_down_event,xdragon_hot_upgrade_fpga_cmd,ecs-xunjian2,false
idc_order_category_mistake_exception,repair_order_processor,ecs-xunjian,LogStoreNotExist
nc_mem_err_col_row_too_much,alisyslog_kernel,ali_syslog,ProjectNotExist
create_order_out_guarantee_v2,idc_repair_sls,ecs-xunjian,LogStoreNotExist
virt_dbus_timeout,libvirt_log,ecs-xunjian,true
ops_restart_vm_too_much,third_part_service,ecs-xunjian,true
vm_vport_rx_limit_dropped,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
cn_kernel_event_logged,moc_kern,ecs-xunjian,true
vm_panic_event_too_many,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
pync_prepare_vm_exception,ecs_pync_log,151,true
file_system_error,alisyslog_kernel,ali_syslog,ProjectNotExist
key_system_file_problem,ecs_screenshot_ocr_data,ecs-xunjian-zhangbei,ProjectNotExist
gpu_vm_release_event,ecs_metaq_send,ali-ecs-hangzhou,ProjectNotExist
wild_vm_disk_retension,wild_disk_meta_info,ecs-xunjian-zhangbei,ProjectNotExist
eci_agent30_updatePod_failed,eci-agent-logs,eci-container-agent-hangzhou,ProjectNotExist
vm_virtio_mtu_drop,ecs_net_nc_stat_netdev_1min_agg,ali-aliyun-ecs-sla-net,true
ptp_pingmesh_latency_failpoint_increased2,ptp-pingmesh,ptp-pingmesh-nt-aliyun-public,ProjectNotExist
nc_hang_uniform_check_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
xdragon_disk_lost_exception,cloudops_task_worker,ecs-xunjian,false
bm_to_io_pcie_block,iohub-fpga-event,ecs-xunjian2,false
network_pync_exception,receivers_log,ecs-xunjian2,true
cloudops_aep_clear_success,cloudops_biz_log,ecs-xunjian,true
spool_d1x_downtime,spool_log,ecs-xunjian,false
mce_killing_error,alisyslog_kernel,ali_syslog,ProjectNotExist
cloudbox_sec_gateway_vm_error,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
xdragon_cn_hang_or_down_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
idc_power_device_change_event,starunion-change,starunion-zhangbei-log,ProjectNotExist
node_pressure_high,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
fail_to_create_vport,ecs_pync_log,151,true
vm_guest_kernel_double_fault,console_log,ecs-xunjian,true
vm_system_io_burst_limited,iostat,151,true
nc_disk_too_much,iostat,151,true
inspect_mining_uid,miningmachine_create_event,ecs-ais-hardware,ProjectNotExist
nc_down_for_local_storage_nc,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
tdc_upgrade_disk_downtime,ebsls_tdc_update,ebsls-wulanchabu,ProjectNotExist
batch_nc_dns_ping_error,monitor_exception_sls_alert,ecs-xunjian,true
vm_ept_metric_exception,dataplane-vmexit,ali-ecsvirtualization-dataplane,true
idc_disk_hardware_exception,repair_order_processor,ecs-xunjian,LogStoreNotExist
eci_merge_pool_create_pod_failed_too_many,monitor_exception_sls_alert,ecs-xunjian,true
ipmi_reset_cn_fail,qemu_log,ecs-xunjian,true
runa_kubeagent_rpc_failed,eci_sandbox_kubeagent_log,ecs-xunjian2,false
ecs_local_storage_health_exception,check_ecs_local_storage,ecs-xunjian2,false
vm_vport_rx_cpu_limit_droped,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
nc_mce_suppressed_conman,alisyslog_conman,ali_syslog,ProjectNotExist
user_accept_event_to_avoid_issue,cloudops_accept_vm_event,ecs-xunjian,false
ecs_alarm_agent_cache_increased,ecs_alarm_agent_monitor_resource,ecs-xunjian,false
hardware_err_or_nc_down_too_many_times,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
pingmesh_latency_error_public,pingmesh,ecs-xunjian2,LogStoreNotExist
vm_end_to_end_loss_rate_high,zoonet-task,zoonet-cn-shanghai-1,ProjectNotExist
blkpmd_check_failed_stop_disk_queue,iohub_pmd_log,ecs-xunjian,true
eed_blkpmd_fpga_abnormal,iohub_pmd_log,ecs-xunjian,true
vm_cpu_increased_too_many,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
nc_storage_iops_high,nc_storage_stat,ecs-xunjian-zhangbei,ProjectNotExist
staragent_cn_ssh_check_failed,,ecs-xunjian2,EMPTY_PROJECT_OR_LOGSTORE
cloud_disk_reach_io_limit,ecs_block_storage_event,ecs-xunjian-zhangbei,ProjectNotExist
pmd_buf_wrong_addr_buf_error,iohub_pmd_log,ecs-xunjian,true
vm_network_flooding,console_log,ecs-xunjian,true
eci_agent30_kubeproxy_exit_error,eci-agent-logs,eci-container-agent-hangzhou,ProjectNotExist
spoold_inspector_nvme_kernel_check,spoold_inspector_new,ecs-xunjian2,true
vm_down_but_not_recover,event_start_end,ecs-xunjian-zhangbei,ProjectNotExist
goc_nc_down_4AMD_in_cluster,monitor_exception_sls_alert,ecs-xunjian,true
ops_power_reset_nc_too_much,third_part_service,ecs-xunjian,true
process_cpu_runq_delay_too_much,runqslower,ecs-xunjian2,false
vm_lm_fail_triggered_by_nc_dpdkcpu_high,maintenance_gray_log,ecs-xunjian,LogStoreNotExist
rack_power_warn_by_sell_rate,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
staragent_uptime_check_failed,,ecs-xunjian2,EMPTY_PROJECT_OR_LOGSTORE
dpdkcpu_max_util_high2,nc_stat_cpu_cpu_agg_1m,metric-avs-ops-cn-shanghai-et2-b01,ProjectNotExist
bare_metal_key_process_segfault,alisyslog_conman_moc,ecs-xunjian,true
gpu_guestos_diagnose_status,diagnose_item_log,ecs-xunjian-zhangbei,ProjectNotExist
xdragon_resize_disk_error_by_yaochi,houyi_proxy,ali-ecs-hangzhou,ProjectNotExist
netpmd_tx_fifo_l2_full,dpdkavs_log,ecs-xunjian2,true
ehpc_controller_node_cpu_load_high,ehpc_cpu_usage,ecs-xunjian2,false
cn_host_mem_less,mem_info_cn,ecs-xunjian2,true
nic_aoc_status_abnormal,proactive_rma_list_aoc_ecs,ecs-xunjian-zhangbei,ProjectNotExist
netpmd_rx_tail_ptr_err,dpdkavs_log,ecs-xunjian2,true
alarm_agent_exec_monitor_failed,ecs_alarm_agent_monitor_failed_all,ecs-xunjian,LogStoreNotExist
nc_hang_too_much,exception_reason_detect,ecs-xunjian-zhangbei,ProjectNotExist
rack_power_error_empty_nc_shutdown,cloudops_xdc_plan_event,ecs-xunjian,true
check_mute_nc_exceptions,ecs_alarm_center_worker_load,ecs-xunjian,true
nc_disk_exception_conman,alisyslog_conman,ali_syslog,ProjectNotExist
dpdk_memseg_error,check_dpdk_memseg,ecs-xunjian2,false
local_storage_vm_disk_error_conman,console_log,ecs-xunjian,true
ecs_control_exception,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
idc_network_incident2,infrastructure-starunion-logstore,starunion-zhangbei-log,ProjectNotExist
pingmesh_latency_public_increased,pingmesh,ecs-xunjian2,LogStoreNotExist
key_customer_event_generate,ecs_event_data,ecs-dashboard,ProjectNotExist
spoold_spoold_alive_check,spoold_inspector_new,ecs-xunjian2,true
nc_factor_affected_timeout,nc_performance_affected_timeout,ecs-xunjian-zhangbei,ProjectNotExist
iohub_seu_detected_fastpath,,ecs-xunjian,EMPTY_PROJECT_OR_LOGSTORE
nc_cloudassitant_heartbeat_loss,monitor_exception_sls_alert,ecs-xunjian,true
cn_dump_exception,cn_dump_log,ecs-xunjian2,false
eci_vmm_dragonball_panicked,fc_log,ecs-xunjian,false
agent_hook_check_failed,virt_monitor,ali-ecsvirtualization-dataplane,true
credit_vm_vcpu_steal,dataplane-vm,ali-ecsvirtualization-dataplane,true
cn_hang_task_detected,moc_kern,ecs-xunjian,true
eci_vm_retained,virt_monitor,ali-ecsvirtualization-dataplane,true
vm_external_interrupt_exception,dataplane-vmexit,ali-ecsvirtualization-dataplane,true
ecs_ebs_failed_resource_is_empty,workflow_metrics,ali-ecs-hangzhou,ProjectNotExist
ptp_latency_public_increased_cross_region,ptp-pingmesh,ptp-pingmesh-zb-aliyun-public,ProjectNotExist
vm_network_resource_adjust_limited,cloudops_biz_log,ecs-xunjian,true
ecs_control_repair_order,idc_repair_sls,ecs-xunjian,LogStoreNotExist
scc_rdma_exception,rdma,ali-tianji-cms-transfer,IndexConfigNotExist
dpdk_avs_util_part_high2,dpdk_util,ecs-xunjian,true
vhost_blk_start_failed_too_many,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
process_status_exception,nc_vm_process_short_info,151,true
essd_blkpmd_fpga_abnormal,ebsls_tdc_netpmd,ebsls-wulanchabu,ProjectNotExist
vm_performance_metric_anomaly,ops-anomaly-feature,ecs-predict-result,ProjectNotExist
nc_mem_error_page_too_many,alisyslog_kernel,ali_syslog,ProjectNotExist
idc_rack_power_high_resume,infrastructure-starunion-logstore,starunion-zhangbei-log,ProjectNotExist
vm_metric_recovery,xhp_vm_heartbeat,ecs-xunjian2,false
inspect_power_error_cause_start_failed,nc_down_oob_detail_info,ecs-xunjian-zhangbei,ProjectNotExist
nc_performance_metric_anomaly,ops-anomaly-feature,ecs-predict-result,ProjectNotExist
split_lock_unrestrain,split_lock,ecs-xunjian,true
nc_memory_flapping,nc_memory_overall,ecs-xunjian,true
trigger_sysrq_exception,cloudops_task_worker,ecs-xunjian,false
taiji_cpu_schedevent,tj-cpu-schedevent,ali-ecsvirtualization-dataplane,false
runa_eci_proxy_error_too_many,eci-proxy-mixed-pool,151,true
safeguard_vm_new_session_increased,tagged_vm_network_stat_1min_agg,ecs-xunjian-zhangbei,ProjectNotExist
eci_kernel_critical_issue_on_same_nc,monitor_exception_sls_alert,ecs-xunjian,true
check_cn_trusted_status,check_nc_trusted_status,ecs-xunjian2,false
migrating_vm_down,ecs_alarm_error_log,ecs-xunjian,true
vm_batch_panic_or_hang_in_nc,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
system_util_high,nc_perf_cpu_cores,151,true
cn_memory_corrupted,mem_info_cn,ecs-xunjian2,true
idc_rack_temperature_high_event,infrastructure-starunion-logstore,starunion-zhangbei-log,ProjectNotExist
cn_split_lock_exception,split_lock_new,ecs-xunjian,true
key_process_missed,collect_all_process,ecs-xunjian2,true
cloudops_offline_success,cloudops_biz_log,ecs-xunjian,true
cpu_credit_drop,credit_info_monitor,151,false
iohub_open_backend_failed,qemu_log,ecs-xunjian,true
cloudops_maintenance_event_v2,cloudops_schedule_info_log,ecs-xunjian,true
physical_machine_filesystem_error,alisyslog_conman_moc,ecs-xunjian,true
avs_monitor_warning,nc_avs_monitor_warnings,ali-aliyun-ecs-sla-net,true
xdragon_hardware_pcie_failure,iohub_pcie_log,ecs-xunjian,true
vm_network_new_session_request_increased,nc_stats_vport_1min_agg,ali-aliyun-ecs-sla-net,false
seu_report_unknown_pattern,iohub_pcie_log,ecs-xunjian,true
gshell_exec_error,ecs_pync_log,151,true
system_diagnose_event,sysak_check_result,ecs-xunjian,LogStoreNotExist
fpga_ur_error,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
ecs_ebs_create_failed_vrc_route_conflict,ebs_service_log,ali-ecs-hangzhou,ProjectNotExist
vm_pined_map_exception,vm_pin_status,ecs-xunjian,true
goc_nc_down_2u4_in_cluster,monitor_exception_sls_alert,ecs-xunjian,true
nic_crc_error,nic_mlx_stat,ecs-xunjian2,true
fpga_netqueue_create_failed,iohub-server,ecs-xunjian2,false
runq_exception,get_system_state_log,ecs-xunjian2,true
virt_report_exception_ping_failed,,ecs-xunjian-zhangbei,EMPTY_PROJECT_OR_LOGSTORE
vm_vport_lost_too_long2,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
eed_blkpmd_address_error,iohub_pmd_log,ecs-xunjian,true
nvme_flash_error,alisyslog_kernel,ali_syslog,ProjectNotExist
nc_check_health_fail,check_health_result,ecs-xunjian2,LogStoreNotExist
core_dump_generated,check_config_release,ecs-xunjian,true
vm_performance_multi_metric_anomaly,ops-anomaly-feature,ecs-predict-result,ProjectNotExist
eci_release_but_vm_still_remaining,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
tdc_operation_failed_detail,tdc_service,ali-ecs-cpr-monitor,ProjectNotExist
controller_feature_close,monitor_exception_sls_alert,ecs-xunjian-zhangbei,ProjectNotExist
virt_disk_operation_failed,libvirt_log,ecs-xunjian,true