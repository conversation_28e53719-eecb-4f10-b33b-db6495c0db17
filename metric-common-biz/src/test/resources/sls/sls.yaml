# ECS 监控相关sls配置
ecs_monitor:
  ak: ${ecs_monitor_sls_ak}
  sk: ${ecs_monitor_sls_sk}
  defaultEndpoint: 'http://cn-hangzhou-corp.sls.aliyuncs.com'
  regionProjectMap:
    ap-northeast-1: '151-jp'  #东京
    ap-southeast-1: 'hy-151-ap'  # 新加波
    ap-southeast-3: 'hy-151-my'  #吉隆坡
    eu-central-1:  'hy-151-ger' #法兰克
    me-east-1:  'hy-151-dubai' #迪拜
    cn-huhehaote: 'hy-151-huhehaote' # 内蒙
    default: '151'   # 默认走弹内
    ap-southeast-5: 'hy-151-indonesia' # 印尼
    cn-chengdu: 'hy-151-chengdu' # 成都
    eu-west-1: 'hy-151-england'   # 英国
    cn-heyuan: 'hy-151-heyuan'   # 河源
    cn-wulanchabu: 'hy-151-wulan'   # 国内乌兰查
    rus-west-1: 'hy-151-russia'   # 俄罗斯
    cn-huhehaote-nebula-1: 'hy-151-huhehaote-nebula' # 呼和浩特星云
    cn-guangzhou: 'hy-151-guangzhou' # 广州
    cn-zhangjiakou-spe: 'hy-151-zhangjiakou-spe' # zhangjiakou spe
    cn-nanjing: 'hy-151-nanjing' # nanjing
    ap-southeast-6: 'hy-151-ph' # 菲律宾
    cn-shanghai-mybk: 'hy-151-sh-mybk' # 网商银行
    cn-beijing-finance-1: 'hy-151-beijing-finance' # 北京金融云
    ap-hochiminh-ant: 'hy-151-hochiminh-ant' # 越南region
    ap-northeast-2: 'hy-151-seoul' # 韩国region
    ap-southeast-7: 'hy-151-bankok' # 曼谷
    me-central-1: 'hy-151-riyadh' # 沙特
    cn-fuzhou: 'hy-151-fuzhou' # fuzhou
    cn-zhengzhou-jva: 'hy-151-zhengzhou-jva' # jva
    cn-nantong: 'hy-151-cn-nantong' # nantong
    cn-hangzhou-mybk: 'hy-151-cn-hangzhou-mybk' # 杭州网商
    cn-shanghai-ant: 'hy-151-cn-shanghai-ant' # 蚂蚁上海云
    cn-wuhan-lr: 'hy-151-cn-wuhan-lr' # 武汉
    cn-qingdao: 'hy-151-qingdao' # 青岛的专属云
    cn-chengdu-ant: 'hy-151-cn-chengdu-ant' # chengdu 蚂蚁专属云
#    cn-chengdu-acdr-ut-1: 'hy-151-cn-chengdu-acdr-ut-1' # 非授信region

# ECS sla 相关
ecs_sla_alarm:
  ak: ${ecs_sla_alarm_sls_ak}
  sk: ${ecs_sla_alarm_sls_sk}
  defaultEndpoint: 'http://cn-hangzhou-corp.sls.aliyuncs.com'
  regionProjectMap:
    default: 'ecs-dashboard'

# ecs巡检
ecs_inspector:
  ak: ${ecs_inspector_sls_ak}
  sk: ${ecs_inspector_sls_sk}
  defaultEndpoint: 'http://cn-hangzhou-corp.sls.aliyuncs.com'
  regionProjectMap:
    ap-northeast-1: 'ecs-xunjian-jp'  #东京
    ap-southeast-1: 'ecs-xunjian-sin'  #新加坡
    ap-southeast-3: 'ecs-xunjian-my'  #吉隆坡
    eu-central-1:  'ecs-xunjian-ger' #法兰克
    me-east-1:  'ecs-xunjian-me' #迪拜
    cn-huhehaote: 'ecs-xunjian-huhehaote' # 内蒙
    default: 'ecs-xunjian'   # 默认走弹内
    ap-southeast-5: 'ecs-xunjian-indonesia'   # 印尼
    cn-chengdu: 'ecs-xunjian-chengdu'   # 成都
    eu-west-1: 'ecs-xunjian-england'   # 英国
    cn-heyuan: 'ecs-xunjian-heyuan'   # 河源
    cn-wulanchabu: 'ecs-xunjian-wulanchabu'   # 国内乌兰查
    rus-west-1: 'ecs-xunjian-russia'   # 俄罗斯
    cn-huhehaote-nebula-1: 'ecs-xunjian-huhehaote-nebula'
    cn-guangzhou: 'ecs-xunjian-guangzhou'
    cn-zhangjiakou-spe: 'ecs-xunjian-zhangjiakou-spe' # zhangjiakou spe
    cn-nanjing: ecs-xunjian-nanjing
    ap-southeast-6: ecs-xunjian-ph
    cn-shanghai-mybk: 'ecs-xunjian-sh-mybk' # 网商银行
    cn-beijing-finance-1: 'ecs-xunjian-beijing-finance' # 北京金融云
    ap-hochiminh-ant: 'ecs-xunjian-hochiminh-ant' # 越南region
    ap-northeast-2: 'ecs-xunjian-seoul' # 韩国region
    ap-southeast-7: 'ecs-xunjian-bankok' # 曼谷
    me-central-1: 'ecs-xunjian-riyadh' # 沙特
    cn-fuzhou: 'ecs-xunjian-fuzhou' # 福州
    cn-zhengzhou-jva: 'ecs-xunjian-zhengzhou-jva' # 郑州
    cn-nantong: 'ecs-xunjian-cn-nantong' # 南通
    cn-hangzhou-mybk: 'ecs-xunjian-cn-hangzhou-mybk'  # 杭州网商
    cn-shanghai-ant: 'ecs-xunjian-cn-shanghai-ant' # 蚂蚁上海云
    cn-wuhan-lr: 'ecs-xunjian-cn-wuhan-lr' # cn-wuhan-lr
    cn-qingdao: 'ecs-xunjian-qingdao' # cn-qingdao
    cn-chengdu-ant: 'ecs-xunjian-cn-chengdu-ant' # cn-chengdu-ant
#    cn-chengdu-acdr-ut-1: 'ecs-xunjian-cn-chengdu-acdr-ut-1' # 非授信

ecs_xunjian2:
  ak: ${ecs_inspector_sls_ak}
  sk: ${ecs_inspector_sls_sk}
  defaultEndpoint: 'http://cn-wulanchabu-share.log.aliyuncs.com'
  regionProjectMap:
    ap-northeast-1: 'ecs-xunjian2-jp'  #东京
    ap-southeast-1: 'ecs-xunjian2-sin'  #新加坡
    ap-southeast-3: 'ecs-xunjian2-my'  #吉隆坡
    eu-central-1:  'ecs-xunjian2-ger' #法兰克
    me-east-1:  'ecs-xunjian2-me' #迪拜
    cn-huhehaote: 'ecs-xunjian2-huhehaote' # 内蒙
    default: 'ecs-xunjian2'   # 默认走弹内
    ap-southeast-5: 'ecs-xunjian2-indonesia'   # 印尼
    cn-chengdu: 'ecs-xunjian2-chengdu'   # 成都
    eu-west-1: 'ecs-xunjian2-england'   # 英国
    cn-heyuan: 'ecs-xunjian2-heyuan'   # 河源
    rus-west-1: 'ecs-xunjian2-russia'   # 俄罗斯
    cn-huhehaote-nebula-1: 'ecs-xunjian2-huhehaote-nebula'
    cn-guangzhou: 'ecs-xunjian2-guangzhou'
    cn-zhangjiakou-spe: 'ecs-xunjian2-zhangjiakou-spe' # zhangjiakou spe
    cn-hongkong: ecs-xunjian2-hk
    cn-shenzhen: ecs-xunjian2-shenzhen
    cn-qingdao: ecs-xunjian2-qingdao
    cn-shanghai: ecs-xunjian2-shanghai
    cn-beijing: ecs-xunjian2-beijing
    cn-hangzhou: ecs-xunjian2-hangzhou
    cn-zhangjiakou: ecs-xunjian2-zhangjiakou
    cn-nanjing: ecs-xunjian2-nanjing
    ap-southeast-6: ecs-xunjian2-ph
    cn-shanghai-mybk: 'ecs-xunjian2-sh-mybk' # 网商银行
    cn-beijing-finance-1: 'ecs-xunjian2-beijing-finance' # 北京金融云
    ap-hochiminh-ant: 'ecs-xunjian2-hochiminh-ant' # 越南region
    ap-northeast-2: 'ecs-xunjian2-seoul' # 韩国region
    ap-southeast-7: 'ecs-xunjian2-bankok' # 泰国
    me-central-1: 'ecs-xunjian2-riyadh' # 沙特
    cn-fuzhou: 'ecs-xunjian2-fuzhou' # 福州
    cn-zhengzhou-jva: 'ecs-xunjian2-zhengzhou-jva' # 郑州
    cn-nantong: 'ecs-xunjian2-cn-nantong' # 南通
    cn-hangzhou-mybk: 'ecs-xunjian2-cn-hangzhou-mybk' # 杭州网商
    cn-shanghai-ant: 'ecs-xunjian2-cn-shanghai-ant' # 蚂蚁上海云
    cn-wuhan-lr: 'ecs-xunjian2-cn-wuhan-lr' # cn-wuhan-lr
    cn-chengdu-ant: 'ecs-xunjian2-cn-chengdu-ant' # cn-chengdu-ant
#    cn-chengdu-acdr-ut-1: 'ecs-xunjian2-cn-chengdu-acdr-ut-1' # 非授信

ecs_cloudras:
  ak: ${ecs_inspector_sls_ak}
  sk: ${ecs_inspector_sls_sk}
  defaultEndpoint: 'http://cn-zhangjiakou-2-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ecs-cloudras'

# ecs巡检
ecs_network:
  ak: ${ecs_monitor_sls_ak}
  sk: ${ecs_monitor_sls_sk}
  defaultEndpoint: 'http://cn-shanghai-corp.sls.aliyuncs.com'
  regionProjectMap:
    ap-northeast-1: 'ali-aliyun-ecs-sla-net-jp'  #东京
    ap-southeast-1: 'ali-aliyun-ecs-sla-net-ap'  #新加坡
    ap-southeast-3: 'ali-aliyun-ecs-sla-net-my'  #吉隆坡
    eu-central-1:  'ali-aliyun-ecs-sla-net-ger' #法兰克
    me-east-1:  'ali-aliyun-ecs-sla-net-dubai' #迪拜
    cn-huhehaote: 'ali-aliyun-ecs-sla-net-huhehaote' # 内蒙
    default: 'ali-aliyun-ecs-sla-net'   # 默认走弹内
    ap-southeast-5: 'ali-aliyun-ecs-sla-net-indonesia'   # 印尼
    cn-chengdu: 'ali-aliyun-ecs-sla-net-chengdu'   # 印尼
    eu-west-1: 'ali-aliyun-ecs-sla-net-england'   # 英国
    cn-heyuan: 'ali-aliyun-ecs-sla-net-heyuan'   # 河源
    cn-wulanchabu: 'ali-aliyun-ecs-sla-net-wulanchabu'   # 国内乌兰查
    rus-west-1: 'ali-aliyun-ecs-sla-net-russia'   # 俄罗斯
    cn-guangzhou: 'ali-aliyun-ecs-sla-net-guangzhou'   # 广州
    cn-zhangjiakou-spe: 'ali-aliyun-ecs-sla-net-zhangjiakou-spe'   # cn-zhangjiakou-spe
    cn-nanjing: 'ali-aliyun-ecs-sla-net-nanjing'   # cn-nanjing
    ap-southeast-6: 'ali-aliyun-ecs-sla-net-ph'   # 菲律宾
    cn-shanghai-mybk: 'ali-aliyun-ecs-sla-net-sh-mybk' # 网商银行
    cn-beijing-finance-1: 'ali-aliyun-ecs-sla-net-beijing-finance' # 北京金融云
    ap-hochiminh-ant: 'ali-aliyun-ecs-sla-net-hochiminh-ant' # 越南region
    cn-hangzhou: 'ali-aliyun-ecs-sla-net-hangzhou'
    cn-shanghai: 'ali-aliyun-ecs-sla-net-shanghai'   # shanghai
    cn-beijing: 'ali-aliyun-ecs-sla-net-beijing'   # 默认走beijing单元
    cn-hongkong: 'ali-aliyun-ecs-sla-net-hongkong'   # 香港单元化
    cn-huhehaote-nebula-1: 'ali-aliyun-ecs-sla-net-huhehaote-nebula' # 呼和浩特星云
    ap-northeast-2: 'ali-aliyun-ecs-sla-net-seoul' # 韩国region
    ap-southeast-7: 'ali-aliyun-ecs-sla-net-bankok' # 泰国
    cn-shenzhen: 'ali-aliyun-ecs-sla-net-shenzhen' # 深圳
    me-central-1: 'ali-aliyun-ecs-sla-net-riyadh' # 沙特
    cn-qingdao: 'ali-aliyun-ecs-sla-net-qingdao' # qingdao
    cn-zhangjiakou: 'ali-aliyun-ecs-sla-net-zhangjiakou' # zhangjiakou
    cn-fuzhou: ali-aliyun-ecs-sla-net-fuzhou # fuzhou
    cn-zhengzhou-jva: ali-aliyun-ecs-sla-net-zhengzhou-jva # zhengzhou-jva
    cn-nantong: ali-aliyun-ecs-sla-net-cn-nantong # nantong
    cn-hangzhou-mybk: 'ali-aliyun-ecs-sla-net-cn-hangzhou-mybk' # 杭州网商云
    cn-shanghai-ant: 'ali-aliyun-ecs-sla-net-cn-shanghai-ant' # 蚂蚁上海云
    cn-wuhan-lr: 'ali-aliyun-ecs-sla-net-cn-wuhan-lr' # wuhan
    cn-chengdu-ant: 'ali-aliyun-ecs-sla-net-cn-chengdu-ant' # chengdu 蚂蚁专属云
# cn-chengdu-acdr-ut-1: ali-aliyun-ecs-sla-net-cn-chengdu-acdr-ut-1 # 非授信


cloud_monitor:
  ak: ${cloud_monitor_ak}
  sk: ${cloud_monitor_sk}
  defaultEndpoint: 'http://cn-shanghai-corp.sls.aliyuncs.com'
  regionProjectMap:
    ap-northeast-1: 'ali-tianji-cms-transfer-jp'  #东京
    ap-southeast-3: 'ali-tianji-cms-transfer-my'  #吉隆坡
    eu-central-1:  'ali-tianji-cms-transfer-eu-germany' #法兰克
    me-east-1:  'ali-tianji-cms-transfer-dubai' #迪拜
    cn-huhehaote: 'ali-tianji-cms-transfer-nt' # 内蒙
    default: 'ali-tianji-cms-transfer'   # 默认走弹内
    ap-southeast-5: 'ali-tianji-cms-transfer-id'   # 印尼

# ECS 存量相关报警
ecs_storage_monitor:
  ak: ${ecs_disk_ak}
  sk: ${ecs_disk_sk}
  defaultEndpoint: 'http://cn-hangzhou-corp.sls.aliyuncs.com/'
  regionProjectMap:
    default: 'ali-ecs-cpr-monitor'
    default_1: 'ali-ecs-disk'
    ap-northeast-1: 'ali-ecs-disk-jp'  #东京
    cn-hangzhou-corp: 'ali-ecs-disk'  #杭州
    ap-southeast-1: 'ali-ecs-disk-singapore'  #新加坡
    ap-southeast-3: 'ali-ecs-disk-malaysia'  #吉隆坡
    eu-central-1:  'ali-ecs-disk-ger' #法兰克
    me-east-1:  'ali-ecs-disk-db' #迪拜
    cn-huhehaote: 'ali-ecs-disk-huhehaote' # 内蒙
    ap-southeast-5: 'ali-ecs-disk-indonesia'   # 印尼
    cn-chengdu: 'ali-ecs-disk-chengdu'   # 成都
    eu-west-1: 'ali-ecs-disk-eu'   # 英国
    cn-heyuan: 'ali-ecs-disk-heyuan'   # 河源
    cn-wulanchabu: 'ali-ecs-disk-wulanchabu'   # 国内乌兰查
    rus-west-1: 'ali-ecs-disk-rus'   # 俄罗斯
    cn-guangzhou: 'ali-ecs-disk-guangzhou'   # 广州
    cn-zhangjiakou-spe: 'ali-ecs-disk-zhangjiakou-spe'   # 张北
    cn-nanjing: 'ali-ecs-disk-nanjing'   # 南京
    ap-southeast-6: 'ali-ecs-disk-philippines'   # 菲律宾
    cn-shanghai-mybk: 'ali-ecs-disk-shanghai-mybk'   # 蚂蚁网商
    cn-beijing-finance-1: 'ali-ecs-disk-beijing-finance' # 北京金融云
    ap-hochiminh-ant: 'ali-ecs-disk-hochiminh-ant' # 蚂蚁越南
    ap-northeast-2: 'ali-ecs-disk-korea' # 韩国region
    ap-southeast-7: 'ali-ecs-disk-bangkok' # 曼谷
    me-central-1: 'ali-ecs-disk-alb' # 沙特
    cn-fuzhou: 'ali-ecs-disk-fuzhou' # 福州
    cn-zhengzhou-jva: 'ali-ecs-disk-zhengzhou-jva' # 郑州
    cn-huhehaote-nebula-1: 'ali-ecs-disk-huhehaote-xy' # huhehaote星云
    cn-nantong: 'ali-ecs-disk-nantong' # nantong
    cn-chengdu-acdr-ut-1: 'ali-ecs-disk-chengdu-acdr' # 非授信
    cn-hangzhou-mybk: 'ali-ecs-disk-hangzhou-mybk' # 杭州网商
    cn-shanghai-ant: 'ali-ecs-disk-cn-shanghai-ant' # 上海蚂蚁云
    cn-wuhan-lr: 'ali-ecs-disk-cn-wuhan-lr' # 武汉
    cn-qingdao: 'ali-ecs-disk-cn-qingdao' # qingdao
    cn-chengdu-ant: 'ali-ecs-disk-cn-chengdu-ant' # cn-chengdu-ant

ecs_storage_iohang:
  ak: ${ecs_disk_ak}
  sk: ${ecs_disk_sk}
  defaultEndpoint: 'http://cn-hangzhou.log.aliyuncs.com'
  regionProjectMap:
    default: 'iohang-server'

ebs_monitor:
  ak: ${ecs_disk_ak}
  sk: ${ecs_disk_sk}
  defaultEndpoint: 'http://zhangbei-corp-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ebs-monitor'

ebs_log_system:
  ak: ${ecs_disk_ak}
  sk: ${ecs_disk_sk}
  defaultEndpoint: 'http://cn-hangzhou-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ebs-log-system-3'

ecs_rdma:
  ak: ${disk_error_prediction_ak}
  sk: ${disk_error_prediction_sk}
  defaultEndpoint: 'http://zhangbei-corp-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'rdma-prepub'

virt_dataplane:
  ak: ${virt_dataplane_ak}
  sk: ${virt_dataplane_sk}
  defaultEndpoint: 'http://cn-shanghai-corp.sls.aliyuncs.com'
  regionProjectMap:
    default: 'ali-ecsvirtualization-dataplane'   # 默认走弹内
    ap-northeast-1: 'ali-ecsvirtualization-dataplane-ap-northeast-1'  #东京
    ap-southeast-1: 'ali-ecsvirtualization-dataplane-ap-southeast-1'  #新加坡
    ap-southeast-3: 'ali-ecsvirtualization-dataplane-ap-southeast-3'  #吉隆坡
    eu-central-1:  'ali-ecsvirtualization-dataplane-eu-central-1' #法兰克
    me-east-1:  'ali-ecsvirtualization-dataplane-me-east-1' #迪拜
    cn-huhehaote: 'ali-ecsvirtualization-dataplane-cn-huhehaote' # 内蒙
    ap-southeast-5: 'ali-ecsvirtualization-dataplane-ap-southeast-5'   # 印尼
    cn-chengdu: 'ali-ecsvirtualization-dataplane-chengdu'   # 成都
    eu-west-1: 'ali-ecsvirtualization-dataplane-england'   # 英国
    cn-heyuan: 'ali-ecsvirtualization-dataplane-cn-heyuan'   # 河源
    cn-wulanchabu: 'ali-ecsvirtualization-dataplane-wulanchabu'   # 国内乌兰查
    rus-west-1: 'ali-ecsvirtualization-dataplane-russia'   # 俄罗斯
    cn-guangzhou: 'ali-ecsvirtualization-dataplane-cn-guangzhou'   # 广州
    cn-qingdao: 'ali-ecsvirtualization-dataplane-cn-qingdao'    # 青岛
    cn-zhangjiakou-spe: 'ali-ecsvirtualization-datappane-zhangbei-spe'   # 张北spe
    cn-nanjing: 'ali-ecsvirtualization-dataplane-cn-nanjing'    # 南京
    ap-southeast-6: 'ali-ecsvirtualization-dataplane-ap-southeast-6'   # 菲律宾
    cn-shanghai-mybk: 'ali-ecsvirtualization-dataplane-sh-mybk' # 网商银行
    cn-beijing-finance-1: 'ali-ecsvirtualization-dataplane-beijing-finance' # 北京金融云
    ap-hochiminh-ant: 'ali-ecsvirtualization-dataplane-hochiminh-ant' # 越南region
    ap-northeast-2: 'ali-ecsvirtualization-dataplane-ap-northeast-2' # 韩国
    ap-southeast-7: 'ali-ecsvirtualization-dataplane-ap-southeast-7' # 泰国
    me-central-1: 'ali-ecsvirtualization-dataplane-me-central-1' # 沙特
    cn-fuzhou: 'ali-ecsvirtualization-dataplane-fuzhou' # 福州
    us-east-1: 'ali-ecsvirtualization-dataplane-us-east' # 美东
    us-west-1: 'ali-ecsvirtualization-dataplane-us-west' # 美西
    cn-shenzhen: 'ali-ecsvirtualization-dataplane-cn-shenzhen' # 美西
    cn-hongkong: 'ali-ecsvirtualization-dataplane-cn-hongkong' # cn-hongkong
    cn-hangzhou: 'ali-ecsvirtualization-dataplane-cn-hangzhou' # cn-hangzhou
    cn-zhengzhou-jva: 'ali-ecsvirtualization-dataplane-zhengzhou-jva' # cn-zhengzhou
    cn-shanghai: 'ali-ecsvirtualization-dataplane-cn-shanghai' # cn-shanghai
    cn-beijing: 'ali-ecsvirtualization-dataplane-cn-beijing' # cn-beijing
    cn-zhangjiakou: 'ali-ecsvirtualization-dataplane-cn-zhangbei' # cn-zhangbei
    cn-huhehaote-nebula-1: 'ali-ecsvirtualization-dataplane-cn-huhehaote-nebula' # hehehaote星云
    cn-nantong: 'ali-ecsvirtualization-dataplane-cn-nantong' # 南通
    cn-hangzhou-mybk: 'ali-ecsvirtualization-dataplane-cn-hangzhou-mybk' # cn-hangzhou-mybk
    cn-shanghai-ant: 'ali-ecsvirtualization-dataplane-cn-shanghai-ant' # 蚂蚁上海云
    cn-wuhan-lr: 'ali-ecsvirtualization-dataplane-cn-wuhan-lr' # 武汉
    cn-chengdu-ant: 'ali-ecsvirtualization-dataplane-cn-chengdu-ant' #  chengdu -ant

ecs_yaochi:
  ak: ${ecs_yaochi_sls_ak}
  sk: ${ecs_yaochi_sls_sk}
  defaultEndpoint: 'http://cn-hangzhou-corp.sls.aliyuncs.com/'
  regionProjectMap:
    default: 'ali-ecs-hangzhou'   # 默认走弹内
    ap-southeast-1: 'ecs-log-ap-southeast-1'  #所有海外都到新加坡

ecs_yaochi_event_center:
  ak: ${ecs_yaochi_sls_ak}
  sk: ${ecs_yaochi_sls_sk}
  defaultEndpoint: 'http://cn-zhangjiakou-share.log.aliyuncs.com/'
  regionProjectMap:
    default: 'cluster-event-center'

tianji_pingmesh_public:
  # 公有云生产
  ak: ${tianji_pingmesh_ak}
  sk: ${tianji_pingmesh_sk}
  defaultEndpoint: 'http://cn-zhangjiakou-share.log.aliyuncs.com'
  regionProjectMap:
    cn-qingdao: 'pingmesh-qd-aliyun-public'
    ap-northeast-1: 'pingmesh-jp-aliyun-public'  #东京
    ap-southeast-1: 'pingmesh-sg-aliyun-public'  #新加坡
    ap-southeast-3: 'pingmesh-my-aliyun-public'  #吉隆坡
    eu-central-1:  'pingmesh-de-aliyun-public' #法兰克
    me-east-1:  'pingmesh-db-aliyun-public' #迪拜
    cn-huhehaote: 'pingmesh-nm-aliyun-public' # 内蒙
    ap-southeast-5: 'pingmesh-id-aliyun-public'   # 印尼
    cn-chengdu: 'pingmesh-cd-aliyun-public'   # 成都
    eu-west-1: 'pingmesh-gb-aliyun-public'   # 英国
    us-west-1: 'pingmesh-usw-aliyun-public'   # 美西硅谷
    default: 'pingmesh-zb-aliyun-public'   # 张家口
    us-east-1: 'pingmesh-use-aliyun-public'   # 美国东部
    cn-shenzhen: 'pingmesh-sz-aliyun-public'   # 深圳
    cn-beijing: 'pingmesh-bj-aliyun-public'   # 北京
    cn-shanghai: 'pingmesh-sh-aliyun-public'   # 上海
    cn-hangzhou: 'pingmesh-hz-aliyun-public'   # 杭州
    cn-hongkong: 'pingmesh-hk-aliyun-public'   # 香港
    rus-west-1: 'pingmesh-ru-aliyun-public'   # 俄罗斯
    cn-guangzhou: 'pingmesh-gz-aliyun-public'   # 广州
    cn-heyuan: 'pingmesh-hy-aliyun-public'   # 河源
    cn-wulanchabu: 'pingmesh-wlcb-aliyun-public'   # wulanchabu
    ap-southeast-6: 'pingmesh-ph-aliyun-public'   # 菲律宾
    ap-hochiminh-ant: 'pingmesh-vn-finance-public' # 蚂蚁越南

tianji_pingmesh_public_new:
  # 公有云生产
  ak: ${tianji_pingmesh_ak_new}
  sk: ${tianji_pingmesh_sk_new}
  defaultEndpoint: 'http://ap-northeast-2-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'pingmesh-kr-aliyun-public'   # 韩国
    ap-southeast-7: 'pingmesh-th-aliyun-public'   # 泰国
    me-central-1: 'pingmesh-sau-aliyun-public'   # 沙特
    cn-wuhan-lr: 'pingmesh-lr-aliyun-public'   # wuhan

tianji_pingmesh_aliproduct:
  # 弹内 ali 生产
  ak: ${tianji_pingmesh_ak}
  sk: ${tianji_pingmesh_sk}
  defaultEndpoint: 'http://zhangbei-corp-share.log.aliyuncs.com'
  regionProjectMap:
    default: pingmesh-zb-ali-product
    default_1: pingmesh-sg-ali-product
    default_2: pingmesh-ru-ali-product
    default_3: pingmesh-nm-ali-product
    default_4: pingmesh-sz-ali-product
    default_5: pingmesh-zb-ali-product
    default_6: pingmesh-sh-ali-product
    default_7: pingmesh-hz-ali-product
    default_8: pingmesh-hz-ali-test

tianji_pingmesh_finance:
  # 金融云
  ak: ${tianji_pingmesh_ak}
  sk: ${tianji_pingmesh_sk}
  defaultEndpoint: 'http://cn-beijing-finance-1-share.log.aliyuncs.com'
  regionProjectMap:
    default: pingmesh-bj-finance-public

gpu_inspector:
  ak: ${gpu_inspector_ak}
  sk: ${gpu_inspector_sk}
  defaultEndpoint: 'http://cn-huhehaote.log.aliyuncs.com'
  regionProjectMap:
    default: 'gpu-log' # 内蒙

fpga_inspector:
  ak: ${fpga_inspector_ak}
  sk: ${fpga_inspector_sk}
  defaultEndpoint: 'http://cn-wulanchabu.log.aliyuncs.com'
  regionProjectMap:
    default: 'faas-check' # 内蒙

idc_event:
  ak: ${idc_sls_alert_ak}
  sk: ${idc_sls_alert_sk}
  defaultEndpoint: 'http://zhangbei-corp-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'raptor-alarm'

idc_drilling:
  ak: ${idc_drilling_event_ak}
  sk: ${idc_drilling_event_sk}
  defaultEndpoint: 'http://cn-hangzhou.log.aliyuncs.com'
  regionProjectMap:
    default: 'sysarch-dr-notification'

aliyun_pop_log:
  ak: ${aliyun_pop_sls_ak}
  sk: ${aliyun_pop_sls_sk}
  defaultEndpoint: 'http://cn-shanghai-corp.sls.aliyuncs.com'
  regionProjectMap:
    default: 'ali-pop-log'
    ap-northeast-1: 'ali-pop-log-jp'
    ap-southeast-1: 'ali-pop-log-xjp'

vpc_controller:
  ak: ${vpc_controller_ak}
  sk: ${vpc_controller_sk}
  defaultEndpoint: 'http://zhangbei-corp-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'vpc-controller-sla-info'

goc_push:
  ak: ${goc_event_ak}
  sk: ${goc_event_sk}
  defaultEndpoint: 'http://zhangbei-corp-share.log.aliyuncs.com/'
  regionProjectMap:
    default: 'goc-event'

ali_syslog:
  ak: ${ali_syslog_ak}
  sk: ${ali_syslog_sk}
  defaultEndpoint: 'http://cn-hangzhou-corp.sls.aliyuncs.com'
  regionProjectMap:
    default: 'ali_syslog'

# ECS sla 相关
xunjian_zhangbei:
  ak: ${ecs_inspector_sls_ak}
  sk: ${ecs_inspector_sls_sk}
  defaultEndpoint: 'http://zhangbei-corp-share.log.aliyuncs.com/'
  regionProjectMap:
    default: 'ecs-xunjian-zhangbei'

new_regional_sls_config:
  ak: ${ecs_inspector_sls_ak}
  sk: ${ecs_inspector_sls_sk}
  defaultEndpoint: 'http://zhangbei-corp-share.log.aliyuncs.com/'
  regionProjectMap:
    default: 'ecs-xunjian-zhangbei'
    ap-northeast-1: 'ecs-xunjian2-jp'  #东京
    ap-southeast-1: 'ecs-xunjian2-sin'  #新加坡
    ap-southeast-3: 'ecs-xunjian2-my'  #吉隆坡
    eu-central-1:  'ecs-xunjian2-ger' #法兰克
    me-east-1:  'ecs-xunjian2-me' #迪拜
    cn-huhehaote: 'ecs-xunjian2-huhehaote' # 内蒙
    cn-wulanchabu: 'ecs-xunjian2'   # wulan
    ap-southeast-5: 'ecs-xunjian2-indonesia'   # 印尼
    cn-chengdu: 'ecs-xunjian2-chengdu'   # 成都
    eu-west-1: 'ecs-xunjian2-england'   # 英国
    cn-heyuan: 'ecs-xunjian2-heyuan'   # 河源
    rus-west-1: 'ecs-xunjian2-russia'   # 俄罗斯
    cn-huhehaote-nebula-1: 'ecs-xunjian2-huhehaote-nebula'
    cn-guangzhou: 'ecs-xunjian2-guangzhou'
    cn-zhangjiakou-spe: 'ecs-xunjian2-zhangjiakou-spe' # zhangjiakou spe
    cn-hongkong: ecs-xunjian2-hk
    cn-shenzhen: ecs-xunjian2-shenzhen
    cn-qingdao: ecs-xunjian2-qingdao
    cn-shanghai: ecs-xunjian2-shanghai
    cn-beijing: ecs-xunjian2-beijing
    cn-hangzhou: ecs-xunjian2-hangzhou
    cn-zhangjiakou: ecs-xunjian2-zhangjiakou
    cn-nanjing: ecs-xunjian2-nanjing
    ap-southeast-6: ecs-xunjian2-ph
    cn-shanghai-mybk: 'ecs-xunjian2-sh-mybk' # 网商银行
    cn-beijing-finance-1: 'ecs-xunjian2-beijing-finance' # 北京金融云
    ap-hochiminh-ant: 'ecs-xunjian2-hochiminh-ant' # 越南region
    ap-northeast-2: 'ecs-xunjian2-seoul' # 韩国region
    ap-southeast-7: 'ecs-xunjian2-bankok' # 泰国
    me-central-1: 'ecs-xunjian2-riyadh' # 沙特
    cn-fuzhou: 'ecs-xunjian2-fuzhou' # 福州
    cn-zhengzhou-jva: 'ecs-xunjian2-zhengzhou-jva' # 郑州
    cn-nantong: 'ecs-xunjian2-cn-nantong' # 南通
    cn-hangzhou-mybk: 'ecs-xunjian2-cn-hangzhou-mybk' # 杭州网商
    cn-shanghai-ant: 'ecs-xunjian2-cn-shanghai-ant' # 蚂蚁上海云
    cn-wuhan-lr: 'ecs-xunjian2-cn-wuhan-lr' # cn-wuhan-lr
    cn-chengdu-ant: 'ecs-xunjian2-cn-chengdu-ant' # cn-chengdu-ant

# ECS故障恢复相关
failure_resume:
  ak: ${ecs_inspector_sls_ak}
  sk: ${ecs_inspector_sls_sk}
  defaultEndpoint: 'http://cn-hangzhou-share.log.aliyuncs.com/'
  regionProjectMap:
    default: 'ecs-failure-resume'

# IDC基础设施
idc_infrastructure:
  ak: ${idc_infrastructure_ak}
  sk: ${idc_infrastructure_sk}
  defaultEndpoint: 'http://zhangbei-corp-share.log.aliyuncs.com/'
  regionProjectMap:
    default: 'infrastructure-starunion'

# IDC基础设施
idc_infrastructure_oxs:
  ak: ${idc_infrastructure_ak}
  sk: ${idc_infrastructure_sk}
  defaultEndpoint: 'http://cn-zhangjiakou-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'starunion-zhangbei-log'

#eci container-agent的logstore
container_agent:
  ak: ${container_agent_ak}
  sk: ${container_agent_sk}
  defaultEndpoint: 'http://cn-hangzhou-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'eci-container-agent-hangzhou'
    eu-central-1: 'eci-container-agent-eu-central-1'
    ap-southeast-5: 'eci-container-agent-southeast-test'
    us-east-1: 'eci-container-agent-east'
    cn-huhehaote: 'eci-container-agent-huhehaote'
    eu-west-1: 'eci-container-agent-west'
    cn-chengdu: 'eci-container-agent-chengdu'
    cn-zhangjiakou: 'eci-container-agent-zhangjiakou'
    cn-hongkong: 'eci-container-agent-hongkong'
    ap-southeast-1: 'eci-container-agent-singapore'
    us-west-1: 'eci-container-agent-american-west'
    cn-shenzhen: 'eci-container-agent-shenzhen'
    cn-beijing: 'eci-container-agent-beijing'
    cn-shanghai: 'eci-container-agent-shanghai'
    cn-heyuan: 'eci-container-agent-cn-heyuan'
    cn-wulanchabu: 'eci-container-agent-cn-wulanchabu'   # 国内乌兰查
    cn-guangzhou: 'eci-container-agent-cn-guangzhou'   # 国内广州

#ops-eci-container-agent
ops-eci-container-agent:
  ak: ${container_agent_ak}
  sk: ${container_agent_sk}
  defaultEndpoint: 'http://cn-hangzhou-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ops-eci-container-agent-cn-hangzhou'
    me-east-1: 'ops-eci-container-agent-me-east-1'
    cn-zhengzhou-jva: 'ops-eci-container-agent-cn-zhengzhou-jva'
    cn-nanjing: 'ops-eci-container-agent-cn-nanjing'
    ap-northeast-2: 'ops-eci-container-agent-ap-northeast-2'
    ap-southeast-5: 'ops-eci-container-agent-ap-southeast-5'
    cn-wulanchabu: 'ops-eci-container-agent-cn-wulanchabu'
    cn-guangzhou: 'ops-eci-container-agent-cn-guangzhou'
    rus-west-1: 'ops-eci-container-agent-rus-west-1'
    me-central-1: 'ops-eci-container-agent-me-central-1'
    cn-beijing: 'ops-eci-container-agent-cn-beijing'
    cn-hangzhou: 'ops-eci-container-agent-cn-hangzhou'
    cn-shenzhen: 'ops-eci-container-agent-cn-shenzhen'
    us-west-1: 'ops-eci-container-agent-us-west-1'
    ap-southeast-1: 'ops-eci-container-agent-ap-southeast-1'
    cn-hongkong: 'ops-eci-container-agent-hongkong'
    cn-zhangjiakou: 'ops-eci-container-agent-cn-zhangjiakou'
    cn-chengdu: 'ops-eci-container-agent-cn-chengdu'
    eu-west-1: 'ops-eci-container-agent-eu-west-1'
    cn-huhehaote: 'ops-eci-container-agent-cn-huhehaote'
    us-east-1: 'ops-eci-container-agent-us-east-1'
    eu-central-1: 'ops-eci-container-agent-eu-central-1'
    cn-qingdao: 'ops-eci-container-agent-cn-qingdao'
    cn-heyuan: 'ops-eci-container-agent-cn-heyuan'
    ap-northeast-1: 'ops-eci-container-agent-ap-northeast-1'
    ap-southeast-3: 'ops-eci-container-agent-ap-southeast-3'
    cn-shanghai: 'ops-eci-container-agent-shanghai'
    cn-fuzhou: 'ops-eci-container-agent-cn-fuzhou'

eci_trace_log:
  ak: ${container_agent_ak}
  sk: ${container_agent_sk}
  defaultEndpoint: 'cn-wulanchabu-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'eci-trace-center'

cloudassistant:
  ak: ${cloudassistant_ak}
  sk: ${cloudassistant_sk}
  defaultEndpoint: 'http://cn-hangzhou-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'axt-hangzhou'
    cn-heyuan: 'axt-heyuan'
    cn-north-2-gov-1: 'axt-cn-north-2-gov-1'
    cn-ali-gov: 'axt-cn-ali-gov'
    cn-chengdu: 'axt-cn-chengdu'
    ap-northeast-1: 'axt-ap-northeast-1'
    ap-southeast-5: 'axt-ap-southeast-5'
    ap-southeast-1: 'axt-ap-southeast-1'
    me-east-1: 'axt-me-east-1'
    eu-central-1: 'axt-eu-central-1'
    eu-west-1: 'axt-eu-west-1'
    us-west-1: 'axt-us-west-1'
    us-east-1: 'axt-us-east-1'
    cn-qingdao: 'axt-qingdao'
    cn-hongkong: 'axt-hongkong'
    cn-zhangjiakou: 'axt-zhangjiakou'
    cn-beijing: 'axt-beijing'
    cn-shanghai: 'axt-shanghai'
    cn-shenzhen: 'axt-shenzhen'
    cn-huhehaote: 'axt-huhehaote'
    cn-wulanchabu: 'axt-wulanchabu'   # 国内乌兰查
    rus-west-1: 'axt-rus-west-1'   # 俄罗斯
    cn-shenzhen-finance: 'axt-shenzhen-finance-1'   # 深圳金融云
    cn-guangzhou: 'axt-guangzhou'   # 广州
    cn-nanjing: 'axt-nanjing'   # 国内广州
    cn-zhangjiakou-spe: 'axt-zhangjiakou-spe'   # 国内张家口spe
    ap-southeast-6: 'axt-ap-southeast-6'   # 菲律宾
    cn-beijing-finance-1: 'axt-beijing-finance-1'   # 北京金融
    cn-shanghai-mybk: 'axt-shanghai-mybk'   # 北京金融
    ap-northeast-2: 'axt-ap-northeast-2'   # 韩国
    ap-southeast-7: 'axt-ap-southeast-7'   # 泰国
    me-central-1: 'axt-me-central-1'   # 沙特
    cn-shanghai-finance-1: 'axt-shanghai-finance-1'   # 上海金融云
    cn-zhengzhou-jva: 'axt-zhengzhou-jva'   # 郑州联合云
    cn-fuzhou: 'axt-fuzhou'   # 福州专有域
    cn-huhehaote-nebula-1: 'axt-huhehaote-nebula-1'   # 呼和浩特星云
    cn-nantong: 'axt-nantong'   # cn-nantong
    cn-chengdu-acdr-ut-1: 'axt-chengdu-acdr-ut-1'   # cn-chengdu-acdr-ut-1
    cn-hangzhou-mybk: 'axt-hangzhou-mybk'   # cn-hangzhou-mybk
    cn-shanghai-ant: 'axt-shanghai-ant' # 蚂蚁上海云
#    cn-chengdu-ant: 'axt-chengdu-ant' # 成都蚂蚁

qitian_sls:
  ak: ${qitian_sls_ak}
  sk: ${qitian_sls_sk}
  defaultEndpoint: 'http://cn-hangzhou-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'qt-changeplatform'

qitian_zoonet:
  ak: ${qitian_sls_ak}
  sk: ${qitian_sls_sk}
  defaultEndpoint: 'http://cn-shanghai-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'zoonet-cn-shanghai-1'
    cn-huhehaote: 'zoonet-cn-huhehaote'
    cn-shenzhen: 'zoonet-cn-shenzhen-1'
    cn-zhangjiakou-spe: 'zoonet-cn-zhangjiakou-spe'
    ap-southeast-1: 'zoonet-ap-singapore-1'
    cn-zhangjiakou: 'zoonet-cn-zhangjiakou-1'
    cn-hangzhou: 'zoonet-cn-hangzhou-1'
    cn-beijing: 'zoonet-cn-beijing-1'
    us-west-1: 'zoonet-us-west'
    us-east-1: 'zoonet-us-east'
    rus-west-1: 'zoonet-rus-russia'
    me-east-1: 'zoonet-me-dubai'
    eu-west-1: 'zoonet-eu-england'
    eu-central-1: 'zoonet-eu-germany'
    cn-wulanchabu: 'zoonet-cn-wulanchabu'
    cn-qingdao: 'zoonet-cn-qingdao'
    cn-hongkong: 'zoonet-cn-hongkong'
    cn-heyuan: 'zoonet-cn-heyuan'
    cn-guangzhou: 'zoonet-cn-guangzhou'
    cn-chengdu: 'zoonet-cn-chengdu'
    ap-southeast-3: 'zoonet-ap-malaysia'
    ap-southeast-5: 'zoonet-ap-indonesia'
    ap-northeast-1: 'zoonet-ap-japan'
    ap-northeast-2: 'zoonet-ap-korea'
    ap-southeast-7: 'zoonet-ap-thailand'
    ap-southeast-6: 'zoonet-ap-manila'
    cn-nanjing: 'zoonet-cn-nanjing'
#    me-central-1: 'zoonet-me-riyadh'
#    cn-beijing-finance-1: 'zoonet-cn-beijing-finance'

qitian_zoonet_gov:
  ak: ${qitian_sls_ak}
  sk: ${qitian_sls_sk}
  defaultEndpoint: 'http://cn-shanghai-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'zoonet-cn-shanghai-1'
    cn-beijing: 'zoonet-gov-beijing'

ant_drill_network:
  ak: ${ant_drill_ak}
  sk: ${ant_drill_sk}
  defaultEndpoint: 'http://cn-zhangjiakou-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'dw-drill'

houyi_ops:
  ak: ${ecs_monitor_sls_ak}
  sk: ${ecs_monitor_sls_sk}
  defaultEndpoint: 'http://zhangbei-corp-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'houyi-ops-log-service'

cloudbox_monitor:
  ak: ${ecs_monitor_sls_ak}
  sk: ${ecs_monitor_sls_sk}
  defaultEndpoint: 'http://cn-wulanchabu-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'cloudbox-wulanchabu'

ecs_ops:
  ak: ${ecs_yaochi_sls_ak}
  sk: ${ecs_yaochi_sls_sk}
  defaultEndpoint: 'http://cn-shanghai-corp.sls.aliyuncs.com'
  regionProjectMap:
    default: 'ecs-ops-internal-shanghai'
    ap-southeast-1: 'ecs-ops-ap-southeast'

vpc_e2e:
  ak: ${vpc_e2e_sls_ak}
  sk: ${vpc_e2e_sls_sk}
  defaultEndpoint: 'http://cn-shanghai-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'achelous-centre'

ecs_ai_middle_office:
  ak: ${ecs_inspector_sls_ak}
  sk: ${ecs_inspector_sls_sk}
  defaultEndpoint: 'http://cn-wulanchabu-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ecs-ai-middleoffice-sls'

ali_aegis_log:
  ak: ${ali_aegis_ak}
  sk: ${ali_aegis_sk}
  defaultEndpoint: 'http://cn-hangzhou-corp.sls.aliyuncs.com'
  regionProjectMap:
    default: 'ali-hz-aegis-log'

virt_dataplane_global:
  ak: ${virt_dataplane_ak}
  sk: ${virt_dataplane_sk}
  defaultEndpoint: 'http://cn-shanghai-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ali-ecsvirtualization-dataplane-global'

opsx_change_log:
  ak: ${opsx_ecs_change_ak}
  sk: ${opsx_ecs_change_sk}
  defaultEndpoint: 'http://cn-zhangjiakou-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ali-syslog'

diagnose_oss_operation_log:
  ak: ${ecs_yaochi_sls_ak}
  sk: ${ecs_yaochi_sls_sk}
  defaultEndpoint: 'cn-hangzhou-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'oss-log-1014874968054919-cn-hangzhou'
    us-west-1: oss-log-1014874968054919-us-west-1
    us-east-1: oss-log-1014874968054919-us-east-1
    me-east-1: oss-log-1014874968054919-me-east-1
    eu-west-1: oss-log-1014874968054919-eu-west-1
    eu-central-1: oss-log-1014874968054919-eu-central-1
    ap-southeast-5: oss-log-1014874968054919-ap-southeast-5
    ap-southeast-3: oss-log-1014874968054919-ap-southeast-3
    ap-southeast-1: oss-log-1014874968054919-ap-southeast-1
    ap-northeast-1: oss-log-1014874968054919-ap-northeast-1
    cn-beijing: oss-log-1014874968054919-cn-beijing
    cn-chengdu: oss-log-1014874968054919-cn-chengdu
    cn-zhangjiakou: oss-log-1014874968054919-cn-zhangjiakou
    cn-heyuan: oss-log-1014874968054919-cn-heyuan
    cn-hongkong: oss-log-1014874968054919-cn-hongkong
    cn-huhehaote: oss-log-1014874968054919-cn-huhehaote
    cn-qingdao: oss-log-1014874968054919-cn-qingdao
    cn-shanghai: oss-log-1014874968054919-cn-shanghai
    cn-shenzhen: oss-log-1014874968054919-cn-shenzhen
    cn-wulanchabu: oss-log-1014874968054919-cn-wulanchabu
    cn-guangzhou: oss-log-1014874968054919-cn-guangzhou
    ap-southeast-6: oss-log-1014874968054919-ap-southeast-6

ecs_ais_hardware:
  ak: ${ecs_inspector_sls_ak}
  sk: ${ecs_inspector_sls_sk}
  defaultEndpoint: 'http://cn-wulanchabu-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ecs-ais-hardware'

ecs_predict_result:
  ak: ${ecs_inspector_sls_ak}
  sk: ${ecs_inspector_sls_sk}
  defaultEndpoint: 'http://cn-wulanchabu-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ecs-predict-result'

ptp_pingmesh_data:
  ak: ${ptp_data_sls_ak}
  sk: ${ptp_data_sls_sk}
  defaultEndpoint: 'http://cn-wulanchabu-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ptp-summary-wulanchabu'

ptp_summary_data:
  ak: ${ptp_data_sls_ak}
  sk: ${ptp_data_sls_sk}
  defaultEndpoint: 'http://cn-zhangjiakou-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ptp-summary-drop-zhangjiakou'

ptp_pingmesh_public:
  ak: ${ptp_data_sls_ak}
  sk: ${ptp_data_sls_sk}
  defaultEndpoint: 'http://cn-zhangjiakou-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ptp-pingmesh-zb-aliyun-public'
    cn-qingdao: 'ptp-pingmesh-qd-aliyun-public'
    us-east-1: 'ptp-pingmesh-use-aliyun-public'
    us-west-1: 'ptp-pingmesh-usw-aliyun-public'
    cn-wulanchabu: 'ptp-pingmesh-wlcb-aliyun-public'
    cn-huhehaote: 'ptp-pingmesh-hhht-aliyun-public'
    cn-chengdu: 'ptp-pingmesh-cd-aliyun-public'
    cn-shenzhen: 'ptp-pingmesh-sz-aliyun-public'
    cn-heyuan: 'ptp-pingmesh-hy-aliyun-public'
    cn-beijing: 'ptp-pingmesh-bj-aliyun-public'
    cn-shanghai: 'ptp-pingmesh-sh-aliyun-public'
    cn-hangzhou: 'ptp-pingmesh-hz-aliyun-public'
    cn-nanjing: 'ptp-pingmesh-nj-aliyun-public'
    cn-fuzhou: 'ptp-pingmesh-fz-aliyun-public'
    cn-hongkong: 'ptp-pingmesh-hk-aliyun-public'
    ap-hochiminh-ant: 'ptp-pingmesh-vn-aliyun-public'
    cn-guangzhou: 'ptp-pingmesh-gz-aliyun-public'
    ap-northeast-2: 'ptp-pingmesh-kr-aliyun-public'
    ap-southeast-7: 'ptp-pingmesh-th-aliyun-public'
    ap-southeast-1: 'ptp-pingmesh-sg-aliyun-public'
    ap-northeast-1: 'ptp-pingmesh-jp-aliyun-public'
    me-east-1: 'ptp-pingmesh-db-aliyun-public'
    ap-southeast-3: 'ptp-pingmesh-my-aliyun-public'
    eu-west-1: 'ptp-pingmesh-gb-aliyun-public'
    ap-southeast-6: 'ptp-pingmesh-ph-aliyun-public'
    rus-west-1: 'ptp-pingmesh-ru-aliyun-public'
    ap-southeast-5: 'ptp-pingmesh-id-aliyun-public'
    eu-central-1: 'ptp-pingmesh-de-aliyun-public'
    cn-wuhan-lr: 'ptp-pingmesh-lr-aliyun-public'
    cn-zhengzhou-jva: 'ptp-pingmesh-zz-aliyun-public'

ptp_pingmesh_public2:
  ak: ${ptp_data_sls_ak}
  sk: ${ptp_data_sls_sk}
  defaultEndpoint: 'http://cn-shanghai-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ptp-pingmesh-nt-aliyun-public'

ebs_sls:
  ak: ${ecs_disk_ak}
  sk: ${ecs_disk_sk}
  defaultEndpoint: 'http://cn-wulanchabu-share.log.aliyuncs.com/'
  regionProjectMap:
    default: 'ebsls-wulanchabu'
    ap-northeast-1: 'ali-ecs-disk-jp'  #东京
    ap-southeast-1: 'ali-ecs-disk-singapore'  #新加坡
    ap-southeast-3: 'ali-ecs-disk-malaysia'  #吉隆坡
    eu-central-1:  'ali-ecs-disk-ger' #法兰克
    me-east-1:  'ali-ecs-disk-db' #迪拜
    cn-huhehaote: 'ali-ecs-disk-huhehaote' # 内蒙
    ap-southeast-5: 'ali-ecs-disk-indonesia'   # 印尼
    cn-chengdu: 'ali-ecs-disk-chengdu'   # 成都
    eu-west-1: 'ali-ecs-disk-eu'   # 英国
    cn-heyuan: 'ali-ecs-disk-heyuan'   # 河源
    cn-wulanchabu: 'ali-ecs-disk-wulanchabu'   # 国内乌兰查
    rus-west-1: 'ali-ecs-disk-rus'   # 俄罗斯
    cn-guangzhou: 'ali-ecs-disk-guangzhou'   # 广州
    cn-zhangjiakou-spe: 'ali-ecs-disk-zhangjiakou-spe'   # 张北
    cn-nanjing: 'ali-ecs-disk-nanjing'   # 南京
    ap-southeast-6: 'ali-ecs-disk-philippines'   # 菲律宾
    cn-shanghai-mybk: 'ali-ecs-disk-shanghai-mybk'   # 蚂蚁网商
    cn-beijing-finance-1: 'ali-ecs-disk-beijing-finance' # 北京金融云
    ap-hochiminh-ant: 'ali-ecs-disk-hochiminh-ant' # 蚂蚁越南
    ap-northeast-2: 'ali-ecs-disk-korea' # 韩国region
    ap-southeast-7: 'ali-ecs-disk-bangkok' # 曼谷
    me-central-1: 'ali-ecs-disk-alb' # 沙特
    cn-fuzhou: 'ali-ecs-disk-fuzhou' # 福州
    cn-zhengzhou-jva: 'ali-ecs-disk-zhengzhou-jva' # 郑州
    cn-huhehaote-nebula-1: 'ali-ecs-disk-huhehaote-xy' # huhehaote星云
    cn-nantong: 'ali-ecs-disk-nantong' # nantong
    cn-chengdu-acdr-ut-1: 'ali-ecs-disk-chengdu-acdr' # 非授信
    cn-hangzhou-mybk: 'ali-ecs-disk-hangzhou-mybk' # 杭州网商
    cn-shanghai-ant: 'ali-ecs-disk-cn-shanghai-ant' # 上海蚂蚁云
    cn-wuhan-lr: 'ali-ecs-disk-cn-wuhan-lr' # wuhan
    cn-qingdao: 'ali-ecs-disk-cn-qingdao' # qingdao
    cn-chengdu-ant: 'ali-ecs-disk-cn-chengdu-ant' # chengdu-ant

ecs_service_monitor:
  ak: ${ecs_service_monitor_sls_ak}
  sk: ${ecs_service_monitor_sls_sk}
  defaultEndpoint: 'cn-hangzhou-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ecs-service-monitor'

vpc_metric_avs:
  ak: ${vpc_metric_sls_ak}
  sk: ${vpc_metric_sls_sk}
  defaultEndpoint: 'http://cn-zhangjiakou-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'metric-avs-ops-cn-zhangjiakou'
    cn-shanghai: 'metric-avs-ops-cn-shanghai'
    cn-hangzhou: 'metric-avs-ops-cn-hangzhou'
    cn-huhehaote: 'metric-avs-ops-cn-huhehaote'
    cn-shenzhen: 'metric-avs-ops-cn-shenzhen'
    cn-hongkong: 'metric-avs-ops-cn-hongkong'
    cn-qingdao: 'metric-avs-ops-cn-qingdao'
    cn-beijing: 'metric-avs-ops-cn-beijing'
    cn-guangzhou: 'metric-avs-ops-cn-guangzhou'
    cn-wulanchabu: 'metric-avs-ops-cn-wulanchabu'
    cn-heyuan: 'metric-avs-ops-cn-heyuan'
    cn-chengdu: 'metric-avs-ops-cn-chengdu'
    ap-southeast-5: 'metric-avs-ops-ap-southeast-5'
    ap-southeast-3: 'metric-avs-ops-ap-southeast-3'
    ap-southeast-1: 'metric-avs-ops-ap-southeast-1'
    ap-northeast-2: 'metric-avs-ops-ap-northeast-2'
    ap-northeast-1: 'metric-avs-ops-ap-northeast-1'
    eu-central-1: 'metric-avs-ops-eu-central-1'
    eu-west-1: 'metric-avs-ops-eu-west-1'
    me-east-1: 'metric-avs-ops-me-east-1'
    me-central-1: 'metric-avs-ops-me-central-1'
    rus-west-1: 'metric-avs-ops-rus-west-1'

vpc_metric_avs2:
  ak: ${vpc_metric_sls_ak}
  sk: ${vpc_metric_sls_sk}
  defaultEndpoint: 'http://cn-shanghai-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'metric-avs-ops-cn-shanghai-et2-b01'

vpc_metric_avs3:
  ak: ${vpc_metric_sls_ak}
  sk: ${vpc_metric_sls_sk}
  defaultEndpoint: 'http://cn-shanghai-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'metric-avs-ops-cn-zhangjiakou-spe'

ecs_feature:
  ak: ${ecs_inspector_sls_ak}
  sk: ${ecs_inspector_sls_sk}
  defaultEndpoint: 'http://cn-wulanchabu.log.aliyuncs.com'
  regionProjectMap:
    default: 'ecs-feature'

ecs_guest_os:
  ak: ${ecs_guest_os_ak}
  sk: ${ecs_guest_os_sk}
  defaultEndpoint: 'http://cn-hangzhou.log.aliyuncs.com'
  regionProjectMap:
    default: 'guest-os-crash'

ecs_security:
  ak: ${ecs_security_ak}
  sk: ${ecs_security_sk}
  defaultEndpoint: 'http://zhangbei-corp-share.log.aliyuncs.com/'
  regionProjectMap:
    default: 'mtee3-aliyun-log'
    ap-southeast-1: 'mtee3-aliyun-sg-log'

ehpc_sls:
  ak: ${ehpc_sls_ak}
  sk: ${ehpc_sls_sk}
  defaultEndpoint: 'http://cn-hangzhou.log.aliyuncs.com'
  regionProjectMap:
    default: 'ranyou-test'

acs_plm:
  ak: ${acs_sls_ak}
  sk: ${acs_sls_sk}
  defaultEndpoint: 'http://cn-hangzhou.log.aliyuncs.com'
  regionProjectMap:
    default: 'acs-plm-trace'

event_center:
  ak: ${ecs_inspector_sls_ak}
  sk: ${ecs_inspector_sls_sk}
  defaultEndpoint: 'http://cn-wulanchabu-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'event-center'

acs_trace:
  ak: ${acs_trace_sls_ak}
  sk: ${acs_trace_sls_sk}
  defaultEndpoint: 'http://cn-hangzhou.log.aliyuncs.com'
  regionProjectMap:
    default: 'acs-trace'

pop_ecs:
  ak: ${pop_ecs_ak}
  sk: ${pop_ecs_sk}
  defaultEndpoint: 'http://zhangbei-corp-share.log.aliyuncs.com'
  regionProjectMap:
    default: 'ecs-pop-aliyun-cn'
    ap-northeast-1: 'ecs-pop-aliyun-jp'
    ap-southeast-1: 'ecs-pop-aliyun-intl'

ehpc_ops_sls:
  ak: ${ehpc_ops_sls_ak}
  sk: ${ehpc_ops_sls_sk}
  defaultEndpoint: 'http://cn-beijing.log.aliyuncs.com'
  regionProjectMap:
    default: 'ehpc-ops-cn-beijing'
    cn-beijing: 'ehpc-ops-cn-beijing'