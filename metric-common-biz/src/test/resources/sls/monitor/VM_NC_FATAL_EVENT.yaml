nc_fake_down:
  interval: 57
  timeRange: 950
  enable: true
  doc: '物理机假宕机,可能是网络抖动导致'
  type: nc_cpu_event
  query: 'exceptionName: ag_nc_icmp_latency_increase or exceptionName: nc_down_judge_bajie or (exceptionName: regionmaster_lost_nc_heartbeat and warningLevel: fatal)'
  analyse: select *,0 as submit_ops,nc_ip as report_nc_ip from (select count(nc_ip) OVER(PARTITION BY cluster_name) as down_cnt_cluster,cluster_name,nc_ip,exception_time,down_time,cluster_name from (select cluster_alias as cluster_name,ncIp as nc_ip,min(exceptionTime) as exception_time,to_unixtime(now()) - to_unixtime(min(exceptionTime)) as down_time from log group by nc_ip,cluster_alias) ) HAVING down_cnt_cluster <= 3 limit 2000
  logstore: 'monitor_exception_sls_alert'
  slsConfigName: ecs_inspector
  silent: 180
  linkedQueryList: [queryLastUptime(nc_ip@or),queryNcHitCnDownMigrateAction()]
  detailMetric: queryPingmeshLatencyPublic
  region: [default]
  retentionTime: 100
  powerSQL: true
  tag: PerformanceImpact
  linkedDimensions: ['NC_INFO','HOUYI_NC_INFO','DRAGONBOX_CN_INFO']
  exclusion:
    ext: '"{{NC_INFO.product_name}}" in ("Dragonbox","Dragonbox_std")'
  levels:
    - name: fatal
      expression: '{{down_cnt_cluster}} <= 3 and {{submit_ops}} == 0 and {{NC_INFO.isXDragon}} and {{last_uptime}} >= 3600 and {{down_time}} >= 250'
      action: [batchLinkedActiveOps]
      notify: [SlsAlertHandler]
    - name: critical
      expression: '{{down_cnt_cluster}} <= 3 and {{submit_ops}} == 0 and "{{NC_INFO}}" != "None" and {{last_uptime}} >= 3600 and {{down_time}} >= 250'
      action: [batchLinkedActiveOps]
      notify: [SlsAlertHandler]

nc_down_uniform_check:
  interval: 25
  timeRange: 130
  enable: true
  doc: 'AG上ICMP_PING 物理机不通'
  type: nc_cpu_event
  query: 'not AY211H and not AY03A and not AY03B and not AY03C and not AY03D and not AY03E and not AY03G and not AY03H and not AY03I and state: NC_DOWN'
  analyse: select 0 as network_error,__source__ as intranet_ip,nc_ip,count(DISTINCT timestamp) as down_count,'NC_DOWN' as ext3,from_unixtime(max(timestamp)) as exception_time  GROUP BY intranet_ip,nc_ip having to_unixtime(now()) - max(timestamp) <= 210  limit 20000
  logstore: 'nc_hang_down_check'
  slsConfigName: ecs_inspector
  silent: 180
  linkedQueryList: [queryVmNetworkEventByIntranetIp(intranet_ip@or)]
  detailMetric: queryNcHangDownStatus
  retentionTime: 40
  powerSQL: true
  tag: PerformanceImpact
  linkedDimensions: ['NC_INFO','DRAGONBOX_CN_INFO']
  exclusion:
    # 排除ag network_error
    ext: '"{{NC_INFO}}" == "None" or {{NC_INFO.security_level}} == 2 or {{network_error}} == 1'
  levels:
    # 本地盘宕机
    - name: fatal
      expression: '{{down_count}} >= 1 and {{NC_INFO.isLocalStorage}}'
      action: [batchLinkedActiveOps]
      notify: [SlsAlertHandler]
      # 云盘NC宕机
    - name: critical
      expression: '{{down_count}} >= 1 and not {{NC_INFO.isLocalStorage}}'
      action: [batchLinkedActiveOps]
      notify: [SlsAlertHandler]

nc_down_uniform_check_fastpath:
  interval: 55
  timeRange: 60
  enable: true
  doc: 'AG上ICMP_PING 物理机不通，通过快速路径发现'
  type: nc_cpu_event
  query: ''
  analyse: ''
  logstore: ''
  slsConfigName: ecs_inspector
  silent: 180
  retentionTime: 40
  tag: PerformanceImpact
  detailMetric: queryNcHangDownStatus
  linkedDimensions: ['NC_INFO','DRAGONBOX_CN_INFO']
  exclusion:
    ext: '{{NC_INFO.security_level}} == 2 or {{NC_INFO.cluster_alias}} in ("AY03A" ,"AY03B","AY03C","AY03D","AY03E" ,"AY03G" ,"AY03H","AY03I")'
  levels:
    # 本地盘宕机
    - name: fatal
      expression: '{{down_count}} >= 1 and {{NC_INFO.isLocalStorage}}'
      action: [batchLinkedActiveOps]
      notify: [SlsAlertHandler]
      # 云盘NC宕机
    - name: critical
      expression: '{{down_count}} >= 1 and not {{NC_INFO.isLocalStorage}}'
      action: [batchLinkedActiveOps]
      notify: [SlsAlertHandler]

nc_hang_uniform_check:
  interval: 60
  timeRange: 650
  enable: true
  doc: '物理机间歇夯机'
  type: nc_cpu_event
  query: 'not AY04A and not AY117B and not state: NC_DOWN and not AY71B or (AY566B or AY664B or AY650C or AY637C or AY637D or AY637B or AY649B or AY649C or AY668B or AY892B or AY881B or AY666C or AY666B or AY695B or AY673B or AY670C or AY670B or AY696B or AY682C or AY682B or AY682F or AY881C)'
  analyse: select nc_ip,sum(is_hang) as exception_count,max(ssh_result) as ext3,avg(ssh_latency) as avg_ssh_latency,from_unixtime(max_by(timestamp,is_hang)) as exception_time,to_unixtime(now()) - max_by(timestamp,is_hang) as delta_time  from (select case when ssh_result in ('SSH_CLOSE','SSH_PERMISSION','SSH_OTHER_ERROR','SSH_TIMEOUT', 'SSH_NOT_ROUTE','SSH_REFUSED') then 1 else 0 end as is_hang,ssh_latency,ssh_result,timestamp,nc_ip from log ) group by nc_ip HAVING avg_ssh_latency >= 3 or exception_count >= 1 limit 20000
  logstore: 'nc_hang_down_check'
  slsConfigName: ecs_inspector
  silent: 480
  detailMetric: queryNcHangDownStatus
  retentionTime: 180
  powerSQL: true
  tag: PerformanceImpact
  linkedDimensions: [NC_INFO,CLUSTER_INFO,DRAGONBOX_CN_INFO]
  exclusion:
    ext: 'not ({{NC_INFO.isOnlineComputeNc}} or {{NC_INFO.isOnlineStorageNc}} )'
  levels:
    - name: fatal
      expression: '{{exception_count}} >= 5 and {{CLUSTER_INFO.isComputeCluster}}'
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=OOBStatusCheckProcessor)','batchLinkDrillEvent','batchRunTask(taskName=NcScheduleFactorProcessor)','batchRunTask(taskName=VsockWrapperCheckProcessor)']
    - name: critical
      expression: '{{exception_count}} >= 1 and {{CLUSTER_INFO.isComputeCluster}} and {{delta_time}} <= 135'
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=nc_hang,silent=1500,slsPush=True)','batchLinkDrillEvent','batchRunTask(taskName=NcScheduleFactorProcessor)']
      notify: [SlsAlertHandler]
    - name: warning
      expression: '{{avg_ssh_latency}} >= 3'
      action: ['batchLinkDrillEvent','batchRunTask(taskName=NcScheduleFactorProcessor)']
      notify: [SlsAlertHandler]
    - name: low_warning
      expression: '{{exception_count}} >= 1 and {{CLUSTER_INFO.isStorageCluster}} and {{delta_time}} <= 135'
      action: ['batchLinkDrillEvent']
      notify: [SlsAlertHandler]

nc_hang_uniform_check_fastpath:
  interval: 60
  timeRange: 650
  enable: true
  doc: '物理机间歇夯机，通过快速路径发现'
  type: nc_cpu_event
  query: ''
  analyse: ''
  logstore: ''
  slsConfigName: ecs_inspector
  silent: 180
  retentionTime: 12
  tag: PerformanceImpact
  detailMetric: queryNcHangDownStatus
  linkedDimensions: [NC_INFO,CLUSTER_INFO,DRAGONBOX_CN_INFO]
  exclusion:
    ext: 'not ({{NC_INFO.isOnlineComputeNc}} or {{NC_INFO.isOnlineStorageNc}} ) '
  levels:
    - name: fatal
      expression: '{{exception_count}} >= 5 and {{CLUSTER_INFO.isComputeCluster}}'
      notify: [SlsAlertHandler]
      action: ['batchLinkDrillEvent','batchRunTask(taskName=VsockWrapperCheckProcessor)']
    - name: critical
      expression: '{{exception_count}} >= 1 and {{CLUSTER_INFO.isComputeCluster}}'
      action: ['batchLinkDrillEvent']
      notify: [SlsAlertHandler]

pingmesh_latency_error_public:
  interval: 13
  timeRange: 57
  enable: true
  doc: 'TCP_PINGMESH异常超时'
  type: nc_network_sys
  query: 'not NG185-VM-JR-G1 and not NU150-VM-JR-G1 and not NO190-VM-JR-G1 and not ecs_move and latency<=5000000 and not offline_buffer and not ecs-project-pool and not dst_group: aliyun_ecs_recycle and not dst_group: aliyun_ecs_expire'
  analyse: select 0 as black_silent,'pingmesh_latency_error_public' as exception_name,avg(latency) as avg_latency,nc_ip,cluster_name,sum(timeout) * 1.0 / count(DISTINCT src_ip) as rate ,count(DISTINCT src_ip) as total_nc,from_unixtime(min(my_timestamp)) as exception_time,max_by(latency,my_timestamp) as lastest_lat from (select max(timestamp) as my_timestamp,avg(latency) as latency,dst_ip as nc_ip,src_ip,upper(regexp_extract(dst_group,'^aliyun_([\w]+)_',1)) as cluster_name,CASE when avg(latency) = 5000000 then 1 else 0 end as timeout, max_by(latency,timestamp) as latency_max_time from log where src_group like 'aliyun_%_server' group by dst_ip,dst_group,src_ip  HAVING cluster_name is not null and not regexp_like(cluster_name, '(AY124)|(AY129)|(AY230)|(AY32D)|(AY32E)|(AY32F)|(AY32G)|(AY32I)|(AY32J)|(AY32K)|(AY32L)|(AY32M)|(AY32N)|(AY32O)|(AY32P)|(AY32Q)|(AY32R)|(AY32S)|(AY32T)|(AY32U)|(AY32V)|(AY32W)|(AY32X)|(AY32Y)|(AY32Z)|(AY95C)|(AY95Z)|(AY249)|(AY03)|(OSS)|(AY375Y)|(AY375L)' ) or dst_group like 'ecs%' and dst_group not like '%buffer%' ) group by nc_ip,cluster_name HAVING total_nc>= 8 and ( rate >= 0.48 or avg_latency >= 1000000)  limit 20000
  logstore: pingmesh
  slsConfigName: ecs_xunjian2
  silent: 180
  retentionTime: 6
  tag: PerformanceImpact
  detailMetric: queryPingmeshLatencyPublic
  linkedDimensions: [NC_INFO]
  linkedQueryList: [querySilentOrBlackNc(exception_name@or)]
  powerSQL: true
  maxTraceLog: 800
  exclusion:
    ext: '"{{NC_INFO}}" == "None" or "online_storage" in "{{NC_INFO.tag_name}}" or "{{NC_INFO.river_type}}" == "存储" or not ({{NC_INFO.isOnlineNc}} or {{NC_INFO.isApsaraNc}}) '
  levels:
    - name: fatal
      expression: '{{rate}} == 1 and {{black_silent}} == 0 and {{lastest_lat}} == 5000000'
      notify: [SlsAlertHandler]
    - name: critical
      expression: '{{rate}} >= 0.5 and {{black_silent}} == 0 and {{lastest_lat}} == 5000000'
      action: ['batchLinkedActiveOps','batchRunTask(taskName=OOBStatusCheckProcessor)','batchRunTask(taskName=OOBOpRecordProcessor)','batchRunTask(taskName=StaragentCheckProcessor,callback=False)']
      notify: [SlsAlertHandler]
    - name: warning
      expression: '{{avg_latency}} >= 800000 and {{black_silent}} == 0 and {{lastest_lat}} == 5000000'
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=OOBStatusCheckProcessor)','batchRunTask(taskName=OOBOpRecordProcessor)','batchRunTask(taskName=StaragentCheckProcessor,callback=False)']
    - name: low_warning
      expression: '{{black_silent}} == 0 and ({{avg_latency}} >= 800000 or {{rate}} >= 0.48) and {{lastest_lat}} <= 2550000'
      notify: [SlsAlertHandler]

ptp_pingmesh_latency_error_public:
  interval: 13
  timeRange: 57
  enable: true
  doc: 'TCP_PINGMESH异常超时(PTP同region)'
  type: nc_network_sys
  query: 'latency<=5000000 and not NG185-VM-JR-G1 and not NU150-VM-JR-G1 and not NO190-VM-JR-G1 and not ecs_move and not offline_buffer and not ecs-project-pool and not dst_group: aliyun_ecs_recycle and not dst_group: aliyun_ecs_expire and not dst_group: aliyun_supply_offline'
  analyse: select 0 as black_silent,0 as fpga_upgrade,'ptp_pingmesh_latency_error_public' as exception_name,avg(latency) as avg_latency,nc_ip,cluster_name,sum(timeout) * 1.0 / count(DISTINCT src_ip) as rate,count(DISTINCT src_ip) as total_nc,from_unixtime(max_by(my_timestamp,latency)) as exception_time,max_by(latency,my_timestamp) as lastest_lat from (select max_by(timestamp,latency) as my_timestamp,avg(latency) as latency,dst_ip as nc_ip,src_ip,upper(regexp_extract(dst_group,'^aliyun_([\w]+)_',1)) as cluster_name,CASE when avg(latency) = 5000000 then 1 else 0 end as timeout from log where src_region_name=dst_region_name and src_group like 'aliyun_%_server' group by dst_ip,dst_group,src_ip HAVING cluster_name is not null and not regexp_like(cluster_name, '(AY124)|(AY129)|(AY230)|(AY32D)|(AY32E)|(AY32F)|(AY32G)|(AY32I)|(AY32J)|(AY32K)|(AY32L)|(AY32M)|(AY32N)|(AY32O)|(AY32P)|(AY32Q)|(AY32R)|(AY32S)|(AY32T)|(AY32U)|(AY32V)|(AY32W)|(AY32X)|(AY32Y)|(AY32Z)|(AY95C)|(AY95Z)|(AY249)|(AY03)|(OSS)') and not dst_group like 'ecs%' and dst_group not like '%buffer%') group by nc_ip,cluster_name HAVING total_nc>= 8 and (rate >= 0.48 or avg_latency >= 1000000) limit 20000
  logstore: ptp-pingmesh
  slsConfigName: ptp_pingmesh_public
  silent: 180
  retentionTime: 6
  tag: PerformanceImpact
  detailMetric: queryPtpPingmeshLatencyPublic
  linkedDimensions: [NC_INFO]
  linkedQueryList: [querySilentOrBlackNc(exception_name@or),queryFpgaUpgradeEvent(),queryAswChangeDetailEventByNcIp(nc_ip@or)]
  powerSQL: true
  maxTraceLog: 800
  exclusion:
    ext: '"{{NC_INFO}}" == "None" or {{fpga_upgrade}} == 1 or "online_storage" in "{{NC_INFO.tag_name}}" or "{{NC_INFO.river_type}}" == "存储" or not ({{NC_INFO.isOnlineNc}} or {{NC_INFO.isApsaraNc}}) '
  levels:
    - name: fatal
      expression: '{{rate}} == 1 and {{black_silent}} == 0 and {{lastest_lat}} == 5000000'
      notify: [SlsAlertHandler]
    - name: critical
      expression: '{{rate}} >= 0.5 and {{black_silent}} == 0 and {{lastest_lat}} == 5000000'
      action: ['batchLinkedActiveOps','batchRunTask(taskName=OOBStatusCheckProcessor)','batchRunTask(taskName=OOBOpRecordProcessor)','batchRunTask(taskName=StaragentCheckProcessor,callback=False)']
      notify: [SlsAlertHandler]
    - name: warning
      expression: '{{avg_latency}} >= 800000 and {{black_silent}} == 0 and {{lastest_lat}} == 5000000'
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=OOBStatusCheckProcessor)','batchRunTask(taskName=OOBOpRecordProcessor)','batchRunTask(taskName=StaragentCheckProcessor,callback=False)']
    - name: low_warning
      expression: '{{black_silent}} == 0 and ({{avg_latency}} >= 800000 or {{rate}} >= 0.48) and {{lastest_lat}} <= 2550000'
      notify: [SlsAlertHandler]

ptp_pingmesh_latency_error_public2:
  interval: 13
  timeRange: 57
  enable: true
  doc: 'TCP_PINGMESH异常超时(PTP同region)'
  type: nc_network_sys
  query: 'latency<=5000000 and not NG185-VM-JR-G1 and not NU150-VM-JR-G1 and not NO190-VM-JR-G1 and not ecs_move and not offline_buffer and not ecs-project-pool and not dst_group: aliyun_ecs_recycle and not dst_group: aliyun_ecs_expire and not dst_group: aliyun_supply_offline'
  analyse: select 0 as black_silent,0 as fpga_upgrade,'ptp_pingmesh_latency_error_public' as exception_name,avg(latency) as avg_latency,nc_ip,cluster_name,sum(timeout) * 1.0 / count(DISTINCT src_ip) as rate,count(DISTINCT src_ip) as total_nc,from_unixtime(max_by(my_timestamp,latency)) as exception_time,max_by(latency,my_timestamp) as lastest_lat from (select max_by(timestamp,latency) as my_timestamp,avg(latency) as latency,dst_ip as nc_ip,src_ip,upper(regexp_extract(dst_group,'^aliyun_([\w]+)_',1)) as cluster_name,CASE when avg(latency) = 5000000 then 1 else 0 end as timeout from log where src_region_name=dst_region_name and src_group like 'aliyun_%_server' group by dst_ip,dst_group,src_ip HAVING cluster_name is not null and not regexp_like(cluster_name, '(AY124)|(AY129)|(AY230)|(AY32D)|(AY32E)|(AY32F)|(AY32G)|(AY32I)|(AY32J)|(AY32K)|(AY32L)|(AY32M)|(AY32N)|(AY32O)|(AY32P)|(AY32Q)|(AY32R)|(AY32S)|(AY32T)|(AY32U)|(AY32V)|(AY32W)|(AY32X)|(AY32Y)|(AY32Z)|(AY95C)|(AY95Z)|(AY249)|(AY03)|(OSS)') and not dst_group like 'ecs%' and dst_group not like '%buffer%') group by nc_ip,cluster_name HAVING total_nc>= 8 and (rate >= 0.48 or avg_latency >= 1000000) limit 20000
  logstore: ptp-pingmesh
  slsConfigName: ptp_pingmesh_public2
  silent: 180
  retentionTime: 6
  tag: PerformanceImpact
  detailMetric: queryPtpPingmeshLatencyPublic2
  linkedDimensions: [NC_INFO]
  linkedQueryList: [querySilentOrBlackNc(exception_name@or),queryFpgaUpgradeEvent(),queryAswChangeDetailEventByNcIp(nc_ip@or)]
  powerSQL: true
  maxTraceLog: 800
  exclusion:
    ext: '"{{NC_INFO}}" == "None" or {{fpga_upgrade}} == 1 or "online_storage" in "{{NC_INFO.tag_name}}" or "{{NC_INFO.river_type}}" == "存储" or not ({{NC_INFO.isOnlineNc}} or {{NC_INFO.isApsaraNc}}) '
  levels:
    - name: fatal
      expression: '{{rate}} == 1 and {{black_silent}} == 0 and {{lastest_lat}} == 5000000'
      notify: [SlsAlertHandler]
    - name: critical
      expression: '{{rate}} >= 0.5 and {{black_silent}} == 0 and {{lastest_lat}} == 5000000'
      action: ['batchLinkedActiveOps','batchRunTask(taskName=OOBStatusCheckProcessor)','batchRunTask(taskName=OOBOpRecordProcessor)','batchRunTask(taskName=StaragentCheckProcessor,callback=False)']
      notify: [SlsAlertHandler]
    - name: warning
      expression: '{{avg_latency}} >= 800000 and {{black_silent}} == 0 and {{lastest_lat}} == 5000000'
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=OOBStatusCheckProcessor)','batchRunTask(taskName=OOBOpRecordProcessor)','batchRunTask(taskName=StaragentCheckProcessor,callback=False)']
    - name: low_warning
      expression: '{{black_silent}} == 0 and ({{avg_latency}} >= 800000 or {{rate}} >= 0.48) and {{lastest_lat}} <= 2550000'
      notify: [SlsAlertHandler]

ptp_latency_error_public_cross_region:
  interval: 13
  timeRange: 57
  enable: true
  doc: 'TCP_PINGMESH异常超时(PTP跨region)'
  type: nc_network_sys
  query: 'latency<=5000000 and not NG185-VM-JR-G1 and not NU150-VM-JR-G1 and not NO190-VM-JR-G1 and not ecs_move and not offline_buffer and not ecs-project-pool and not dst_group: aliyun_ecs_recycle and not dst_group: aliyun_ecs_expire and not dst_group: aliyun_supply_offline'
  analyse: select 0 as black_silent,'ptp_pingmesh_latency_error_public' as exception_name,avg(latency) as avg_latency,nc_ip,cluster_name,sum(timeout) * 1.0 / count(DISTINCT src_ip) as rate,count(DISTINCT src_ip) as total_nc,from_unixtime(min(my_timestamp)) as exception_time,max_by(latency,my_timestamp) as lastest_lat from (select max(timestamp) as my_timestamp,avg(latency) as latency,dst_ip as nc_ip,src_ip,upper(regexp_extract(dst_group,'^aliyun_([\w]+)_',1)) as cluster_name,CASE when avg(latency) = 5000000 then 1 else 0 end as timeout from log where src_region_name!=dst_region_name and src_group like 'aliyun_%_server' group by dst_ip,dst_group,src_ip HAVING cluster_name is not null and not regexp_like(cluster_name, '(AY124)|(AY129)|(AY230)|(AY32D)|(AY32E)|(AY32F)|(AY32G)|(AY32I)|(AY32J)|(AY32K)|(AY32L)|(AY32M)|(AY32N)|(AY32O)|(AY32P)|(AY32Q)|(AY32R)|(AY32S)|(AY32T)|(AY32U)|(AY32V)|(AY32W)|(AY32X)|(AY32Y)|(AY32Z)|(AY95C)|(AY95Z)|(AY249)|(AY03)|(OSS)') and not dst_group like 'ecs%' and dst_group not like '%buffer%') group by nc_ip,cluster_name HAVING total_nc>= 8 and (rate >= 0.48 or avg_latency >= 1000000) limit 20000
  logstore: ptp-pingmesh
  slsConfigName: ptp_pingmesh_public
  silent: 180
  retentionTime: 6
  tag: PerformanceImpact
  detailMetric: queryPtpPingmeshLatencyPublicCrossRegion
  linkedDimensions: [NC_INFO]
  linkedQueryList: [querySilentOrBlackNc(exception_name@or)]
  powerSQL: true
  maxTraceLog: 800
  exclusion:
    ext: '"{{NC_INFO}}" == "None" or "online_storage" in "{{NC_INFO.tag_name}}" or "{{NC_INFO.river_type}}" == "存储" or not ({{NC_INFO.isOnlineNc}} or {{NC_INFO.isApsaraNc}}) '
  levels:
    - name: fatal
      expression: '{{rate}} == 1 and {{black_silent}} == 0 and {{lastest_lat}} == 5000000'
      notify: [SlsAlertHandler]
    - name: critical
      expression: '{{rate}} >= 0.5 and {{black_silent}} == 0 and {{lastest_lat}} == 5000000'
      action: ['batchLinkedActiveOps','batchRunTask(taskName=OOBStatusCheckProcessor)','batchRunTask(taskName=OOBOpRecordProcessor)','batchRunTask(taskName=StaragentCheckProcessor,callback=False)']
      notify: [SlsAlertHandler]
    - name: warning
      expression: '{{avg_latency}} >= 800000 and {{black_silent}} == 0 and {{lastest_lat}} == 5000000'
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=OOBStatusCheckProcessor)','batchRunTask(taskName=OOBOpRecordProcessor)','batchRunTask(taskName=StaragentCheckProcessor,callback=False)']
    - name: low_warning
      expression: '{{black_silent}} == 0 and ({{avg_latency}} >= 800000 or {{rate}} >= 0.48) and {{lastest_lat}} <= 2550000'
      notify: [SlsAlertHandler]

nc_ssh_latency_high:
  interval: 61
  timeRange: 90
  enable: true
  doc: '物理机SSH延迟陡增'
  type: nc_cpu_event
  query: 'ssh_latency >= 5 and not state: NC_HANG and not state: NC_DOWN and not AY03F and not AY03C and not AY04A and not AY03H and not AY03E'
  analyse: select max(ssh_latency) as ssh_latency, nc_ip, from_unixtime(min(timestamp)) as exception_time,cluster_name group by nc_ip,cluster_name HAVING ssh_latency >= 8 limit 2000
  logstore: nc_hang_down_check
  slsConfigName: ecs_inspector
  silent: 580
  detailMetric: queryNcHangDownStatus
  linkedDimensions: [NC_INFO]
  retentionTime: 7
  tag: PerformanceImpact
  levels:
    - name: fatal
      expression: '{{ssh_latency}} >= 20'
      action: ['batchRunTask(taskName=NcScheduleFactorProcessor)']
      notify: [SlsAlertHandler]
    - name: critical
      expression: '{{ssh_latency}} >= 10'
      action: ['batchRunTask(taskName=NcScheduleFactorProcessor)']
      notify: [SlsAlertHandler]
    - name: warning
      expression: '{{ssh_latency}} >= 8'
      action: ['batchRunTask(taskName=NcScheduleFactorProcessor)']
      notify: [SlsAlertHandler]

ptp_pingmesh_latency_error:
  interval: 58
  timeRange: 65
  enable: true
  doc: '网络延迟较大(ptp探测,同网络集群)'
  type: nc_network_sys
  query: 'dst_level1_product_line:阿里云-弹性计算 and delay >= 300000'
  analyse: select dest_ip as nc_ip,from_unixtime(cast(max(pkg_start_time) as bigint) / 1000) as exception_time,avg(delay) as delay_avg,dst_dsw_cluster_name as extension where src_dsw_cluster_name=dst_dsw_cluster_name group by nc_ip,extension limit 10000
  logstore: 'ptp-summary-all'
  slsConfigName: ptp_pingmesh_data
  silent: 1800
  retentionTime: 15
  linkedDimensions: [NC_INFO]
  tag: PerformanceImpact
  detailMetric: queryPtpPingmeshLatency
  exclusion:
    ext: '"{{NC_INFO}}" == "None"'
  levels:
    - name: warning
      expression: '{{delay_avg}} >= 300000'
      notify: [SlsAlertHandler]

ptp_pingmesh_latency_increased:
  interval: 58
  timeRange: 125
  enable: true
  doc: '网络延迟徒增(ptp探测,同网络集群)'
  type: nc_network_sys
  query: 'dst_level1_product_line:阿里云-弹性计算'
  analyse: select * from (select nc_ip,extension,from_unixtime(timestamp) as exception_time,delay_avg,delay_avg / (lag(delay_avg,1,delay_avg) OVER(PARTITION BY nc_ip ORDER BY timestamp ASC) + 0.000000001) AS delay_mul from (select dest_ip as nc_ip,dst_dsw_cluster_name as extension,avg(delay) as delay_avg,__time__ - __time__ % 60 as timestamp from log where src_dsw_cluster_name=dst_dsw_cluster_name group by nc_ip,extension,timestamp)) having delay_mul >= 9 and delay_avg >= 1000000 limit 10000
  logstore: 'ptp-summary-all'
  slsConfigName: ptp_pingmesh_data
  silent: 1800
  retentionTime: 15
  linkedDimensions: [NC_INFO]
  tag: PerformanceImpact
  detailMetric: queryPtpPingmeshLatency
  exclusion:
    ext: '"{{NC_INFO}}" == "None"'
  levels:
    - name: warning
      expression: '{{delay_mul}} >= 9'
      notify: [SlsAlertHandler]

ag_nc_icmp_latency_increase:
  interval: 30
  timeRange: 170
  enable: true
  doc: '诊断发现物理机有宕机信号'
  type: nc_cpu_event
  query: '*'
  analyse: select max_by(ping_latency,time) as ping_latency ,max(latency_delta) as latency_delta,max(time) as exception_time,nc_ip,cluster_name from (select nc_ip,time,cluster_name,ping_latency,1.0 * ping_latency - (lag(ping_latency, 1, 100000) over(partition BY nc_ip ORDER by time) ) as latency_delta from (select case when ping_result is null then 0 else ping_result end as ping_latency, nc_ip,cluster_name,from_unixtime(timestamp - timestamp % 60) as time from log )) group by nc_ip,cluster_name HAVING latency_delta >0 and ping_latency = 100 limit 5000
  logstore: 'nc_hang_down_check'
  slsConfigName: ecs_inspector
  silent: 14400
  detailMetric: queryNcHangDownStatus
  retentionTime: 10
  tag: PerformanceImpact
  linkedDimensions: [NC_INFO,MULTI_NC_INFO,HOUYI_NC_INFO]
  levels:
    - name: critical
      expression: '{{latency_delta}} > 0 and not {{HOUYI_NC_INFO.isEmptyNc}}'
      action: [batchLinkedActiveOps,'batchRunTask(taskName=OOBOpRecordProcessor)','batchRunTask(taskName=OOBStatusCheckProcessor)']
      notify: [SlsAlertHandler]
    - name: warning
      expression: '{{latency_delta}} > 0 and {{HOUYI_NC_INFO.isEmptyNc}} and not {{HOUYI_NC_INFO.isNcDown}}'
      action: [batchLinkedActiveOps,'batchRunTask(taskName=OOBOpRecordProcessor)']
      notify: [SlsAlertHandler]

ag_nc_icmp_latency_increase_fastpath:
  interval: 55
  timeRange: 60
  enable: true
  doc: '诊断发现物理机有宕机信号，通过快速路径发现'
  type: nc_cpu_event
  query: ''
  analyse: ''
  logstore: ''
  slsConfigName: ecs_inspector
  silent: 14400
  retentionTime: 40
  tag: PerformanceImpact
  detailMetric: queryNcHangDownStatus
  linkedDimensions: [NC_INFO,MULTI_NC_INFO,HOUYI_NC_INFO]
  levels:
    - name: critical
      expression: '{{latency_delta}} > 0 and not {{HOUYI_NC_INFO.isEmptyNc}}'
      action: [batchLinkedActiveOps,'batchRunTask(taskName=OOBOpRecordProcessor)','batchRunTask(taskName=OOBStatusCheckProcessor)']
      notify: [SlsAlertHandler]
    - name: warning
      expression: '{{latency_delta}} > 0 and {{HOUYI_NC_INFO.isEmptyNc}} and not {{HOUYI_NC_INFO.isNcDown}}'
      action: [batchLinkedActiveOps,'batchRunTask(taskName=OOBOpRecordProcessor)']
      notify: [SlsAlertHandler]

vm_vsock_icmp_ping_loss_new:
  interval: 18
  timeRange: 50
  enable: true
  doc: 虚拟机VSOCK_ICMP_PING全丢包，来自实时诊断的数据源
  type: vm_network_sys
  query: '* AND (Status: ok OR Status: failed)'
  analyse: SELECT ExceptionName as ext3,ExceptionName AS fromException, MachineId AS machine_id, NcIp as nc_ip, from_unixtime(__time__) AS exception_time, ROUND(CAST(FailureIpCnt AS DOUBLE)/CAST(TotalIpCnt AS DOUBLE), 3) AS loss_ratio WHERE TotalIpCnt > 0 GROUP BY fromException, machine_id, nc_ip, exception_time, loss_ratio HAVING loss_ratio >= 1 LIMIT 5000
  logstore: vsock_ping_check_processor
  slsConfigName: ecs_inspector
  silent: 180
  region: [default]
  retentionTime: 5
  detailMetric: queryVmArpPingMetric
  tag: PerformanceImpact
  linkedDimensions:
    - NC_INFO
    - VM_INFO
    - USER_INFO
    - DRAGONBOX_CN_INFO
  levels:
    - name: fatal
      expression: '{{loss_ratio}} >= 1 and "{{fromException}}" == "vm_down_end" and not {{NC_INFO.isPhysicalMachine}}'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      action: ['batchRunTask(taskName=VsockPingCheckProcessor,delay=60)']
    - name: critical
      expression: '{{loss_ratio}} >= 1'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
    - name: warning
      expression: '{{loss_ratio}} >= 1 and "{{NC_INFO.cluster_usage}}" == "测试"'
      notify: [SlsAlertHandler]
  eventCenter:
    push: true
    opsCode: InstanceFailure
    impact: Alert
    resourceStatus: NotApplicable
    category: VM.Availability

vm_vsock_icmp_ping_loss_recovery:
  interval: 20
  timeRange: 600
  enable: true
  doc: 虚拟机VSOCK_ICMP_PING全丢包恢复，来自实时诊断的数据源
  type: vm_network_event
  query: '* AND (Status: ok OR Status: failed)'
  analyse: SELECT 1 as is_recover, machine_id, nc_ip, exception_time, fromException, ext3, loss_ratio, last_loss_ratio, max_exception_time FROM (SELECT machine_id, nc_ip, exception_time, fromException, ext3, loss_ratio, lag(loss_ratio, 1, loss_ratio) over(PARTITION BY machine_id ORDER BY exception_time ASC) as last_loss_ratio, first_value(exception_time) over(PARTITION BY machine_id ORDER BY exception_time DESC) as max_exception_time FROM (SELECT ExceptionName as ext3, ExceptionName AS fromException, MachineId AS machine_id, NcIp as nc_ip, from_unixtime(__time__) AS exception_time, ROUND(CAST(FailureIpCnt AS DOUBLE)/CAST(TotalIpCnt AS DOUBLE), 3) AS loss_ratio FROM log WHERE TotalIpCnt > 0)) WHERE (loss_ratio < 1 AND last_loss_ratio >= 1) OR (loss_ratio >= 1 AND exception_time = max_exception_time AND to_unixtime(now()) - to_unixtime(max_exception_time) > 220) LIMIT 5000
  logstore: vsock_ping_check_processor
  slsConfigName: ecs_inspector
  silent: 0
  region: [default]
  retentionTime: 5
  tag: Event
  detailMetric: queryVmArpPingMetric
  linkedDimensions:
    - NC_INFO
    - VM_INFO
    - USER_INFO
    - DRAGONBOX_CN_INFO
  levels:
    - name: critical
      expression: '{{is_recover}} == 1'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      action: ['batchRunTask(taskName=EndTimerProcessor,eventName=arpping_timeout)']
      persistent: false

xdragon_cn_hang_too_long:
  # 查询的时间间隔
  interval: 36
  # 查询的sql
  timeRange: 320
  type: nc_cpu_available
  enable: true
  doc: '神龙CN长时间夯机'
  query: '(CN_HANG or CN_BUSY)'
  analyse: select nc_ip, concat(cn_ip,'|') as extension,state as ext3, state as reason,from_unixtime(min(timestamp)) as exception_time,count(*) as exception_cnt from (select __source__ as nc_ip, state, __time__ - timestamp as delta_time,timestamp,cn_ip from log) where delta_time <= 900 group by nc_ip,cn_ip,state limit 10000
  logstore: check_xdragon_cn_hang_down
  slsConfigName: ecs_inspector
  silent: 120
  detailMetric: queryCnSSHLatencyDetail
  linkedDimensions: [NC_INFO,HOUYI_NC_INFO]
  retentionTime: 45
  tag: PerformanceImpact
  exclusion:
    ext: '"{{HOUYI_NC_INFO}}" == "None" or {{HOUYI_NC_INFO.isDeploying}}'
  levels:
    - name: fatal
      # 相当于连续5分钟夯
      expression: '{{exception_cnt}} >= 3 and "{{reason}}" == "CN_HANG"'
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=cn_hang,gocFailurePush=True,slsPush=True)', 'batchRunTask(taskName=SyshangSysakCollectProcessor,isCn=True)']
      atDuty: [xdragon,virtualization]
      hasVmThenAlert: true
    - name: critical
      expression: '{{exception_cnt}} >= 5'
      notify: [SlsAlertHandler]
      atDuty: [xdragon,virtualization]
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=cn_hang,gocFailurePush=True,slsPush=True)']
      hasVmThenAlert: true
    - name: warning
      expression: '{{exception_cnt}} >= 2'
      notify: [SlsAlertHandler]
      atDuty: [xdragon,virtualization]
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=cn_hang,slsPush=True)']
      hasVmThenAlert: true

multi_xdragon_cn_hang_too_long:
  interval: 60
  timeRange: 240
  type: nc_cpu_available
  enable: true
  doc: '检测到多个神龙CN长时间夯机'
  query: 'exceptionName: physical_machine_kernel_issue and warningLevel: fatal and isPhysicalMachine: False  or exceptionName: xdragon_cn_hang_too_long and warningLevel: fatal'
  analyse: select ncIp as nc_ip,ncIp as machine_id, warningLevel, count(DISTINCT extension) as cnt GROUP BY machine_id,warningLevel having cnt = 2 limit 10000
  logstore: monitor_exception_sls_alert
  slsConfigName: ecs_inspector
  silent: 120
  detailMetric: queryCnSSHLatencyDetail
  retentionTime: 100
  powerSQL: true
  region: [default]
  tag: Unavailable
  linkedDimensions: [ NC_INFO,MULTI_NC_INFO,HOUYI_NC_INFO ]
  levels:
    - name: fatal
      expression: '"{{warningLevel}}" == "fatal" and {{cnt}} == 2'
      notify: [ SlsAlertHandler ]
      atDuty: [ xdragon ]
    - name: critical
      expression: '"{{warningLevel}}" == "critical" and {{cnt}} == 2'
      notify: [ SlsAlertHandler ]
      atDuty: [ xdragon ]

xdragon_cn_ssh_latency_high:
  # 查询的时间间隔
  interval: 43
  # 查询的sql
  timeRange: 120
  type: nc_cpu_available
  enable: true
  doc: '神龙CN_SSH延迟飙高'
  query: 'ssh_latency >= 5 and not CN_HANG'
  analyse: select avg(ssh_latency) as ssh_latency, __source__ as nc_ip, from_unixtime(min(timestamp)) as exception_time, concat(cn_ip,'|') as extension group by nc_ip,cn_ip limit 1000
  logstore: check_xdragon_cn_hang_down
  slsConfigName: ecs_inspector
  silent: 120
  detailMetric: queryCnSSHLatencyDetail
  linkedDimensions: [NC_INFO,HOUYI_NC_INFO]
  retentionTime: 21
  tag: PerformanceImpact
  exclusion:
    ext: '"{{HOUYI_NC_INFO}}" == "None"'
  levels:
    - name: critical
      expression: '{{ssh_latency}} >= 10'
      notify: [SlsAlertHandler]
      atDuty: [xdragon,virtualization]
    - name: warning
      expression: '{{ssh_latency}} >= 5'
      notify: [SlsAlertHandler]
      atDuty: [xdragon,virtualization]

xdragon_cn_ping_loss_too_much:
  interval: 20
  timeRange: 120
  type: nc_cpu_available
  enable: true
  doc: '神龙CN_ICMP PING丢包异常'
  query: '*'
  analyse: select __source__ as nc_ip,cluster_name,avg(case when ping_result is null or cast(ping_result as varchar) = '' then 0 else cast(ping_result as bigint) end ) as loss_rate,max(state) as state,min(from_unixtime(timestamp)) as exception_time,concat(cn_ip,'|') as extension group by nc_ip,extension,cluster_name HAVING loss_rate > 0 and to_unixtime(now()) - min(timestamp) <= 250 limit 3000
  logstore: check_xdragon_cn_hang_down
  slsConfigName: ecs_inspector
  silent: 120
  detailMetric: queryCnSSHLatencyDetail
  tag: PerformanceImpact
  linkedDimensions: [NC_INFO,HOUYI_NC_INFO]
  exclusion:
    ext: '"{{HOUYI_NC_INFO}}" == "None"'
  levels:
    - name: fatal
      expression: '{{loss_rate}} == 100 and "{{state}}" != "CN_DOWN"'
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=VsockPingCheckProcessor)','batchRunTask(taskName=StaragentCheckProcessor,callback=False)','batchRunTask(taskName=NcScheduleFactorProcessor)']
      atDuty: [xdragon]
      hasVmThenAlert: true
    - name: critical
      expression: '{{loss_rate}} >= 10 and "{{state}}" != "CN_DOWN"'
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=VsockPingCheckProcessor)','batchRunTask(taskName=StaragentCheckProcessor,callback=False)','batchRunTask(taskName=NcScheduleFactorProcessor)']
      atDuty: [xdragon]
      hasVmThenAlert: true

xdragon_cn_ping_loss_too_much_fastpath:
  interval: 20
  timeRange: 120
  type: nc_cpu_available
  enable: true
  doc: '神龙CN_ICMP PING丢包异常，通过快速路径上报'
  query: ''
  analyse: ''
  logstore: ''
  slsConfigName: ecs_inspector
  silent: 120
  detailMetric: queryCnSSHLatencyDetail
  tag: PerformanceImpact
  linkedDimensions: [NC_INFO,HOUYI_NC_INFO]
  exclusion:
    ext: '"{{HOUYI_NC_INFO}}" == "None"'
  levels:
    - name: fatal
      expression: '{{loss_rate}} == 100 and "{{reason}}" != "CN_DOWN" '
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=VsockPingCheckProcessor)','batchRunTask(taskName=StaragentCheckProcessor,callback=False)','batchRunTask(taskName=NcScheduleFactorProcessor)']
      atDuty: [xdragon]
      hasVmThenAlert: true
    - name: critical
      expression: '{{loss_rate}} >= 10 and "{{reason}}" != "CN_DOWN" '
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=VsockPingCheckProcessor)','batchRunTask(taskName=StaragentCheckProcessor,callback=False)','batchRunTask(taskName=NcScheduleFactorProcessor)']
      atDuty: [xdragon]
      hasVmThenAlert: true

vm_iohang_start:
 interval: 27
 timeRange: 95
 enable: true
 doc: 'vm存储IO夯出现告警'
 type: vm_virtStorage_event
 query: 'type:warn and not Unknown_VM'
 analyse: select 0 as desc_len_error,replace(vm, 'iso-', '') as machine_id,case when position('iso-' IN vm) != 0 then 'iso_hang' else 'disk_hang' end as reason, max_by(nc, timestamp) as report_nc_ip, date_format(max(timestamp-60), '%Y-%m-%d %H:%i:%S') as exception_time, arbitrary(type) as hang_status, device as EXTRA_DATA, device as extension, device as ext3 group by vm, device limit 200000
 logstore: vm_iohang_warning
 slsConfigName: ecs_storage_iohang
 transformParams: [transformExt3ToDiskId]
 linkedQueryList: [queryPmdIoqDescLenError(),queryTdcInvalidIO()]
 silent: 0
 retentionTime: 60
 detailMetric: queryVmStorageIOLatency
 tag: Unavailable
 linkedDimensions: [NC_INFO,USER_INFO,VM_INFO]
 exclusion:
   ext: ' "ecs.i1" in "{{VM_INFO.instanceType}}" or "ecs.i2" in "{{VM_INFO.instanceType}}" or "ecs.si2" in "{{VM_INFO.instanceType}}" '
 levels:
   - name: fatal
     expression: '"{{hang_status}}" == "warn" and "{{reason}}" != "iso_hang" and {{desc_len_error}} == 0 and ("{{VM_INFO}}" != "None" and "{{VM_INFO.ncIp}}" == "{{report_nc_ip}}" or "{{VM_INFO.ncIp}}" in ("None","","null"))'
     action: ['batchRunTask(taskName=StartTimerProcessor,eventName=vm_iohang,deltaTime=0,silent=600,slsPush=True)','batchRunTask(taskName=FpgaAfullCheckProcessor)']
     notify: [EventCenterAlertHandler,SlsAlertHandler]
   - name: critical
     expression: '"{{hang_status}}" == "warn" and "{{reason}}" != "iso_hang" and {{desc_len_error}} == 1 and ("{{VM_INFO}}" != "None" and "{{VM_INFO.ncIp}}" == "{{report_nc_ip}}" or "{{VM_INFO.ncIp}}" in ("None","","null"))'
     action: ['batchRunTask(taskName=StartTimerProcessor,eventName=vm_iohang,deltaTime=0,silent=600,slsPush=True)',batchRunTask(taskName=DisableLiveMigrateProcessor)]
     notify: [EventCenterAlertHandler,SlsAlertHandler]
   - name: warning
     # iso启动阶段iohang不需要通知客户
     expression: '"{{hang_status}}" == "warn" and "{{reason}}" == "iso_hang"'
     notify: [SlsAlertHandler]
   - name: low_warning
     expression: '"{{hang_status}}" == "warn" and "{{machine_id}}" == "CNforVM"'
     notify: [SlsAlertHandler]
     # 补偿数据
   - name: normal
     expression: '{{supplement}} == 1'
     notify: [SlsAlertHandler,EventCenterAlertHandler]
 eventCenter:
   push: true
   opsCode: SystemFailure
   impact: Alert
   resourceStatus: NotApplicable
   category: VM.Availability
   closureFlag: start
   closureEvent: vm_iohang

vm_iohang_end:
  interval: 33
  timeRange: 165
  enable: true
  doc: 'vm存储IO夯解除'
  type: vm_virtStorage_event
  query: '*'
  analyse: select * ,'null' as end_time,0 as iohang_start,device as extension, device as ext3 from (select max(time) as exception_time, (to_unixtime(localtimestamp)- to_unixtime(max(time)) ) as last_event_time , max_by(type, __time__) as hang_status, replace(vm,'iso-') as machine_id , max_by(nc, __time__) as nc_ip, device from log group by vm, device) where hang_status = 'resume' and last_event_time >= 295 limit 500000
  logstore: vm_iohang_warning
  slsConfigName: ecs_storage_iohang
  transformParams: [transformExt3ToDiskId]
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO,DRAGONBOX_CN_INFO]
  linkedQueryList: [queryVmIOStartEvent()]
  tag: Event
  silent: 0
  retentionTime: 60
  detailMetric: queryVmStorageIOLatency
  exclusion:
    ext: ' "ecs.i1" in "{{VM_INFO.instanceType}}" or "ecs.i2" in "{{VM_INFO.instanceType}}" or "ecs.si2" in "{{VM_INFO.instanceType}}" '
  levels:
    - name: normal
      expression: '"{{hang_status}}" == "resume" and {{iohang_start}} == 1'
      action: ['batchRunTask(taskName=EndTimerProcessor,eventName=vm_iohang,gocFailurePush=False,slsPush=True)']
      notify: [EventCenterAlertHandler,SlsAlertHandler]
    - name: low_warning
      expression: '{{supplement}} == 1'
      action: ['batchRunTask(taskName=EndTimerProcessor,eventName=vm_iohang,gocFailurePush=False,slsPush=True)']
      notify: [EventCenterAlertHandler,SlsAlertHandler]
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Ok
    category: VM.Availability
    closureFlag: end
    closureEvent: vm_iohang

nc_kernel_fatal_panic:
  interval: 33
  timeRange: 70
  enable: true
  doc: '宿主机OS内部Kernel致命故障'
  type: nc_cpu_event
  query: '"unable to handle" or "Kernel panic -" or Oops and not msg: fake and not DMAR and not msg: head'
  analyse: select 1 as panic,__topic__ as hostname, min(time) as exception_time,lower(trim(regexp_replace(msg,'\d',''))) as ext3  group by hostname,ext3 HAVING hostname like '%cloud%' limit 10000
  logstore: alisyslog_conman
  slsConfigName: ali_syslog
  detailMetric: queryConmanLog
  retentionTime: 70
  tag: Unavailable
  silent: 280
  linkedDimensions: [NC_INFO,DRAGONBOX_CN_INFO]
  levels:
    - name: fatal
      expression: '{{panic}} == 1 and ( "fatal hardware error" in "{{ext3}}" or "fatal machine check" in "{{ext3}}" or "uncorrected hardware" in "{{ext3}}") and "{{NC_INFO}}" != "None"'
      # panic之后10分钟进行vmcore详细信息拉取
      action: ['batchRunTask(taskName=StaragentCheckProcessor,callback=False)','batchRunTask(taskName=VmcoreDetailInfoPullProcessor,delay=600,timeout=900)']
      notify: [SlsAlertHandler,EventCenterAlertHandler]
    - name: critical
      expression: '{{panic}} == 1 and "{{NC_INFO}}" != "None"'
      # panic之后10分钟进行vmcore详细信息拉取
      action: ['batchRunTask(taskName=StaragentCheckProcessor,callback=False)','batchRunTask(taskName=VmcoreDetailInfoPullProcessor,delay=600,timeout=900)']
      notify: [SlsAlertHandler,EventCenterAlertHandler]

nc_kernel_fatal_panic_analyse_result:
  interval: 58
  timeRange: 70
  enable: true
  doc: '宿主机OS内部Kernel致命故障，内核分析结论展示'
  type: nc_cpu_event
  query: 'IsCn: 0'
  analyse: SELECT 1 as result, NcIp as nc_ip, CoreTime as exception_time, PanicClass as reason, concat('[PanicClass]:',PanicClass,'; [FuncName]:',FuncName,'; [CrashKey]:',CrashKey,'; [VmcoreLink]:', VmcoreLink) as ext3 LIMIT 1000
  logstore: vmcore_detail_processor
  region: [default]
  slsConfigName: ecs_inspector
  detailMetric: queryConmanLog
  retentionTime: 70
  silent: 330
  tag: Unavailable
  linkedDimensions: [NC_INFO]
  levels:
    - name: low_warning
      expression: '{{result}} == 1 and "{{NC_INFO}}" != "None"'
      notify: [SlsAlertHandler]

physical_machine_kernel_panic:
  interval: 21
  timeRange: 32
  enable: true
  doc: '神龙CN_OS内部Kernel致命故障'
  type: nc_cpu_event
  query: '("unable to handle" or "Kernel panic -" or Oops and SMP and not content: vm and not Modules and not content: vmcs_die_notify or "Kernel BUG at") and not DMAR or (general protection and content: fault and content: SMP)'
  analyse: select '' as reason,lower(trim(regexp_replace(content,'\d',''))) as ext3,__source__ as nc_ip,count(*) as exceptionCnt,case when min(regexp_extract(content, '(\d+\-\d+\-\d+ \d+:\d+:\d+)', 1)) is null then from_unixtime(min(cast(__time__ as bigint))) else min(regexp_extract(content, '(\d+\-\d+\-\d+ \d+:\d+:\d+)', 1)) end as exception_time,__topic__ as cnSn,case when split_part(sn,'-',2) = '02' then '*************|' else '*************|' end as extension group by nc_ip,ext3,cnSn,extension limit 10000
  logstore: alisyslog_conman_moc
  slsConfigName: ecs_inspector
  silent: 380
  detailMetric: queryPhysicalMachineConsoleLog
  linkedQueryList: [queryPanicRIP(nc_ip@or)]
  tag: Unavailable
  retentionTime: 50
  linkedDimensions: [NC_INFO]
  levels:
    - name: fatal
      expression: '{{exceptionCnt}} >= 1 and ( "fatal hardware error" in "{{ext3}}" or "fatal machine check" in "{{ext3}}" or "fatal local machine check" in "{{ext3}}" or "mce memory error" in "{{ext3}}" or "machine_check" in "{{reason}}") '
      # panic之后10分钟进行vmcore详细信息拉取
      action: ['batchRunTask(taskName=VsockPingCheckProcessor)']
      notify: [SlsAlertHandler,EventCenterAlertHandler]
    - name: critical
      expression: '{{exceptionCnt}} >= 1'
      # panic之后10分钟进行vmcore详细信息拉取
      action: ['batchRunTask(taskName=VsockPingCheckProcessor)','batchRunTask(taskName=VmcoreDetailInfoPullProcessor,delay=600,timeout=900)']
      notify: [SlsAlertHandler,EventCenterAlertHandler]
  eventCenter:
    push: true
    opsCode: InstanceFailure
    impact: Alert
    resourceStatus: NotApplicable
    category: VM.Availability
    eventSilent: 600

physical_machine_kernel_panic_analyse:
  interval: 63
  timeRange: 99
  enable: true
  doc: '神龙物理机OS内部Kernel致命故障，内核分析结论展示'
  type: nc_cpu_event
  query: 'IsCn: 1'
  analyse: SELECT 1 as result, NcIp as nc_ip, CoreTime as exception_time, PanicClass as reason, concat('[PanicClass]:',PanicClass,'; [FuncName]:',FuncName,'; [CrashKey]:',CrashKey,'; [VmcoreLink]:', VmcoreLink) as ext3 LIMIT 1000
  logstore: vmcore_detail_processor
  region: [default]
  slsConfigName: ecs_inspector
  detailMetric: queryPhysicalMachineConsoleLog
  tag: Unavailable
  retentionTime: 70
  silent: 330
  linkedDimensions: [NC_INFO]
  levels:
    - name: low_warning
      expression: '{{result}} == 1 and "{{NC_INFO}}" != "None"'
      notify: [SlsAlertHandler]

physical_machine_kernel_issue:
  interval: 53
  timeRange: 68
  enable: true
  doc: '神龙物理机CN内核异常'
  type: nc_cpu_event
  query: 'not content: insert_kthread_work* and (blocked or lockup or softlockup or (Tx timeout not time) or self-detected and stall ) and not "until released"'
  analyse: select '' as reason,trim(regexp_replace(content,'\d','')) as ext3,__source__ as nc_ip,count(*) as exceptionCnt,case when min(regexp_extract(content, '(\d+\-\d+\-\d+ \d+:\d+:\d+)', 1)) is null then from_unixtime(min(cast(__time__ as bigint))) else min(regexp_extract(content, '(\d+\-\d+\-\d+ \d+:\d+:\d+)', 1)) end as exception_time,__topic__ as cnSn,case when split_part(sn,'-',2) = '02' then '*************|' else '*************|' end as extension group by nc_ip,ext3,cnSn,extension limit 10000
  logstore: alisyslog_conman_moc
  slsConfigName: ecs_inspector
  silent: 430
  detailMetric: queryPhysicalMachineConsoleLog
  linkedQueryList: [queryPanicRIP(nc_ip@or)]
  tag: PerformanceImpact
  linkedDimensions: [NC_INFO]
  retentionTime: 13
  levels:
    - name: fatal
      expression: '{{exceptionCnt}} >= 1 and "vda-: block" in "{{ext3}}"'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      hasVmThenAlert: true
    - name: critical
      expression: '{{exceptionCnt}} >= 1 and not "stall" in "{{ext3}}"'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      hasVmThenAlert: true
    - name: low_warning
      expression: '{{exceptionCnt}} >= 1 and "stall" in "{{ext3}}"'
      notify: [SlsAlertHandler]
      hasVmThenAlert: true
  eventCenter:
    push: true
    opsCode: InstanceFailure
    impact: Alert
    resourceStatus: Warning
    category: VM.Availability
    vmEventName: physical_machine_kernel_issue_for_user
    eventSilent: 1800

vm_operation_failed:
  interval: 60
  timeRange: 100
  enable: true
  doc: '虚拟机核心操作接口失败'
  type: vm_resource_event
  query: 'redeploy_instance_fail or stop_vm_fail and not other_info: wild'
  analyse: select regexp_extract(other_info, '([\w\-]+)\s*fail', 1) as machine_id,min(time) as exception_time,count(DISTINCT time) as exception_cnt, regexp_extract(alarm_item, '\((.*)\)', 1) as reason,regexp_extract(alarm_item, '\((.*)\)', 1) as extension  group by machine_id,reason limit 800
  logstore: regionmaster_alarm
  slsConfigName: ecs_monitor
  region: [default]
  silent: 780
  tag: ControlFailed
  retentionTime: 40
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO,DRAGONBOX_CN_INFO]
  levels:
    - name: critical
      expression: '{{exception_cnt}} >= 1 and "{{reason}}" == "redeploy_instance_fail" '
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      action: ['batchRunTask(taskName=ReasonDetectProcessor,exceptionNameField=exceptionName,detectType=REASON_START_STOP,timeRange=106400)']
      atDuty: [inventory,inventory_stable,inventory_schedule,nc]
    - name: warning
      expression: '{{exception_cnt}} >= 1 and "{{reason}}" == "stop_vm_fail" '
      notify: [SlsAlertHandler,EventCenterAlertHandler]
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Maintaining
    category: VM.Availability

storage_nc_cause_vm_iohang:
  interval: 156
  timeRange: 200
  enable: true
  doc: '存储后端NC异常可能导致vm io异常'
  type: vm_virtStorage_event
  query: 'not vm_slow_io_exception and not vm_disk_io_latency_exception	and not vm_iohang_start'
  analyse: select case when instanceId is null then 0 else 1 end as is_iohang ,ncIp as nc_ip,case when instanceId is null then machineId else instanceId end as machine_id,min(exceptionTime) as exception_time, case when reason = '' then exceptionName else reason end as reason,warningLevel as warning_level, case when ext3 is null then additionalInfo else ext3 end as ext3,exceptionType as type,count(*) as exception_cnt group by ncIp,instanceId,machineId,reason,additionalInfo,ext3,warningLevel,exceptionType,exceptionName limit 10000
  logstore: vm_iohang_detect_processor
  slsConfigName: ecs_inspector
  silent: 280
  region: [default]
  retentionTime: 8
  tag: Unavailable
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO,DRAGONBOX_CN_INFO]
  detailMetric: queryVmStorageIOLatency
  levels:
    - name: critical
      expression: '{{exception_cnt}} > 0 and {{is_iohang}} == 1 '
      notify: [SlsAlertHandler]
      atDuty: [nc,block_storage]
    - name: warning
      expression: '{{exception_cnt}} > 0 and {{is_iohang}} == 0'
      notify: [SlsAlertHandler]

gshell_exec_error:
  interval: 246
  timeRange: 320
  enable: true
  doc: 'ISO状态执行gshell异常'
  type: vm_virt_event
  query: 'open channel fail Instance'
  analyse: select 1 as gshell_timeout,machine_id,nc_ip,ext3,min(time) as exception_time,case when position('112:' in ext3) != 0 then 'disk_full' when position('5:' in ext3) != 0 then 'file_access_denied' else 'other_reason' end as reason from (select regexp_extract(content, 'iso-(i-[\w\d]+)',1 ) as machine_id,__source__ as nc_ip,regexp_extract(content,'gshell.*',0) as ext3, time from log) group by machine_id,nc_ip,ext3 limit 1000
  logstore: ecs_pync_log
  slsConfigName: ecs_monitor
  silent: 430
  retentionTime: 40
  tag: ControlFailed
  detailMetric: queryGShellErrorStack
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO,DRAGONBOX_CN_INFO]
  exclusion:
    ext: '"tmpvm" in "{{machine_id}}" or "BVT" in "{{machine_id}}"'
  levels:
    - name: critical
      expression: '{{gshell_timeout}} == 1 and "{{reason}}" == "disk_full"'
      notify: [SlsAlertHandler]
    - name: warning
      expression: '{{gshell_timeout}} == 1 and {{VM_INFO.isWin}}'
      notify: [SlsAlertHandler]
    - name: low_warning
      expression: '{{gshell_timeout}} == 1'
      notify: [SlsAlertHandler]

xdragon_cn_hang_or_down:
  # 查询的时间间隔
  interval: 15
  # 查询的sql
  timeRange: 125
  type: nc_cpu_available
  enable: true
  doc: '检测到神龙CN出现夯机或宕机异常'
  query: '(CN_DOWN or CN_HANG or CN_BUSY)'
  analyse: select 1800 as last_uptime,0 as cpu_util_high,cluster_name,nc_ip,concat(cn_ip,'|') as extension,state as ext3, state as reason,from_unixtime(min(timestamp)) as exception_time,count(*) as exception_cnt from (select __source__ as nc_ip, state, __time__ - timestamp as delta_time,timestamp,cluster_name,cn_ip from log) where delta_time <= 200 group by nc_ip,state,cluster_name,cn_ip limit 100000
  logstore: check_xdragon_cn_hang_down
  slsConfigName: ecs_inspector
  silent: 330
  # 刚开机半小时有预期cn不通
  linkedQueryList: [queryMinUptime(),queryNcOpsReason()]
  detailMetric: queryCnSSHLatencyDetail
  retentionTime: 100
  tag: PerformanceImpact
  powerSQL: true
  linkedDimensions: [NC_INFO,CLUSTER_INFO,HOUYI_NC_INFO]
  exclusion:
    # 神龙轮转不做CN宕机迁移，last_uptime 物理机刚开机半小时不做CN宕机迁移。
    ext: '"{{HOUYI_NC_INFO}}" == "None" or {{HOUYI_NC_INFO.isOffline}} or {{HOUYI_NC_INFO.isDeploying}} or "轮转升级" in "{{ext3}}" or {{last_uptime}} < 1800 '
  levels:
    - name: fatal
      expression: '{{exception_cnt}} >= 1 and "{{reason}}" == "CN_DOWN"'
      action: ['batchRunTask(taskName=OOBStatusCheckProcessor,CN_OOB=True)','batchRunTask(taskName=IohubCheckProcessor)','batchLinkedActiveOps']
      notify: [SlsAlertHandler]
      atDuty: [xdragon]
      hasVmThenAlert: true
    - name: critical
      expression: '{{exception_cnt}} >= 2 and "{{reason}}" == "CN_HANG"'
      action: ['batchRunTask(taskName=OOBStatusCheckProcessor,CN_OOB=True)','batchRunTask(taskName=IohubCheckProcessor)','batchLinkedActiveOps']
      notify: [SlsAlertHandler]
      atDuty: [xdragon]
    - name: warning
      expression: '{{exception_cnt}} >= 1 and {{cpu_util_high}} == 0'
      action: [ 'batchRunTask(taskName=VsockPingCheckProcessor)','batchRunTask(taskName=OOBStatusCheckProcessor,CN_OOB=True)','batchLinkedActiveOps']
      notify: [SlsAlertHandler]
    - name: low_warning
      expression: '{{exception_cnt}} >= 1 and {{cpu_util_high}} == 1'
      action: [ 'batchRunTask(taskName=VsockPingCheckProcessor)','batchRunTask(taskName=OOBStatusCheckProcessor,CN_OOB=True)','batchLinkedActiveOps']
      notify: [SlsAlertHandler]

xdragon_cn_hang_or_down_fastpath:
  # 查询的时间间隔
  interval: 15
  # 查询的sql
  timeRange: 125
  type: nc_cpu_available
  enable: true
  doc: '检测到神龙CN出现夯机或宕机异常，通过快速路径上报'
  query: ''
  analyse: ''
  logstore: ''
  slsConfigName: ecs_inspector
  silent: 330
  # 刚开机半小时有预期cn不通
  linkedQueryList: [queryMinUptime(),queryNcOpsReason()]
  detailMetric: queryCnSSHLatencyDetail
  retentionTime: 100
  tag: PerformanceImpact
  powerSQL: true
  linkedDimensions: [NC_INFO,CLUSTER_INFO,HOUYI_NC_INFO]
  exclusion:
    # 神龙轮转不做CN宕机迁移，last_uptime 物理机刚开机半小时不做CN宕机迁移。
    ext: '"{{HOUYI_NC_INFO}}" == "None" or {{HOUYI_NC_INFO.isOffline}} or {{HOUYI_NC_INFO.isDeploying}} or "轮转升级" in "{{ext3}}" or {{last_uptime}} < 1800 '
  levels:
    - name: fatal
      expression: '{{exception_cnt}} >= 1 and "{{reason}}" == "CN_DOWN"'
      action: ['batchRunTask(taskName=OOBStatusCheckProcessor,CN_OOB=True)','batchLinkedActiveOps']
      notify: [SlsAlertHandler]
      atDuty: [xdragon]
      hasVmThenAlert: true
    - name: critical
      expression: '{{exception_cnt}} >= 2 and "{{reason}}" == "CN_HANG"'
      action: ['batchRunTask(taskName=OOBStatusCheckProcessor,CN_OOB=True)','batchLinkedActiveOps']
      notify: [SlsAlertHandler]
      atDuty: [xdragon]

multi_xdragon_cn_hang_or_down:
  interval: 60
  timeRange: 240
  type: nc_cpu_available
  enable: true
  doc: '检测到神龙多个CN出现夯机或宕机异常'
  query: 'exceptionName: xdragon_cn_hang_or_down and (warningLevel: fatal or warningLevel: critical)'
  analyse: select ncIp as machine_id, warningLevel, count(DISTINCT extension) as cnt GROUP BY machine_id,warningLevel limit 10000
  logstore: monitor_exception_sls_alert
  slsConfigName: ecs_inspector
  silent: 330
  region: [default]
  powerSQL: true
  detailMetric: queryCnSSHLatencyDetail
  retentionTime: 100
  tag: PerformanceImpact
  linkedDimensions: [ NC_INFO,MULTI_NC_INFO,HOUYI_NC_INFO ]
  levels:
    - name: fatal
      expression: '"{{warningLevel}}" == "fatal" and {{cnt}} == 2'
      notify: [ SlsAlertHandler ]
      atDuty: [ xdragon ]
    - name: critical
      expression: '"{{warningLevel}}" >= "critical" and {{cnt}} == 2'
      notify: [ SlsAlertHandler ]
      atDuty: [ xdragon ]
    
vmexit_exception_vm_hang:
  interval: 85
  timeRange: 310
  enable: true
  doc: 'VmExit指标异常疑似虚拟机内部夯死或者蓝屏'
  type: vm_cpu_available
  query: 'not (AY707F or AY713H or AY713P or AY707H or AY684H or AY718D or AY712O or AY712N or AY718E or AY714D or AY714Q or AY704Z or AY726C or AY718T or AY705H or AY704P or AY684K or AY725V or AY735F or AY379M or AY414I or AY330Z or AY441M)'
  analyse: SELECT 0 as miss_report,sum(APIC_IPI+APIC_WRITE+MSR_WRITE+MSR_READ+HLT) AS nrvmexit,avg(apiC_WRITE) as avg_apic_write,domainName as machine_id,__source__ as nc_ip,cluster_id as cluster_name,concat(max(date),' ',max(time)) as exception_time,count(distinct time) as cnt GROUP BY domainName,__source__ ,cluster_id  HAVING nrvmexit < 1 and nrvmexit >= 0 and count(*) >= 3 and avg_apic_write <= 0.1 and cnt >= 4 limit 15000
  logstore: dataplane-vmexit
  slsConfigName: virt_dataplane
  silent: 430
  detailMetric: queryVmExitMetric
  retentionTime: 3
  tag: Unavailable
  maxTraceLog: 300
  linkedQueryList: [queryVMExitWindowsUnknownIssue()]
  linkedDimensions: [NC_INFO,VM_INFO,USER_INFO,VM_TAGS]
  exclusion:
    ext: '"{{VM_INFO}}" == "None" or "{{NC_INFO.cpu_generation}}" in ("ROME","Ampere","Kunpeng","Yitian710","Denverton(Atom)","atom","NA","Genoa","ICX-D") or {{VM_INFO.getGmtStartedUnixTime}} < 150 or "{{VM_TAGS.confidential_computing_mode}}" == "TDX" or "{{VM_INFO.aliUid}}" in ("1069799516703727","1160100279132283","1215899467912565","1222704190984900","1260199340242001","1353100377890690","1392299526450536","1497199414904744","1498090572035497","1636300280371182","1780100280388378","1867302109790069","1904435775535168","1934099339377547","1262359858783294","1550203943326350","1626999119920274")'
  levels:
    - name: fatal
      expression: '{{nrvmexit}} == 0 and {{miss_report}} == 0 and not "{{machine_id}}".startswith("BVT") and "{{NC_INFO.vcpu_mod}}" != "cpu_credit"'
      action: [batchRunTask(taskName=ScreenshotDiagnoseProcessor)]
      notify: [EventCenterAlertHandler,SlsAlertHandler]
    - name: critical
      expression: '{{nrvmexit}} == 0 and {{miss_report}} == 0 and not "{{machine_id}}".startswith("BVT")'
      notify: [EventCenterAlertHandler,SlsAlertHandler]
    - name: warning
      expression: '{{avg_apic_write}} <= 0.1 and {{nrvmexit}} < 1'
      notify: [SlsAlertHandler]
      persistent: false
  eventCenter:
    push: true
    opsCode: InstanceFailure
    impact: Alert
    resourceStatus: Warning
    category: VM.Availability
    eventSilent: 10800

vm_panic_event:
  interval: 42
  timeRange: 80
  enable: true
  doc: 'VM异常PANIC重启可能是虚拟机内部异常'
  type: vm_cpu_available
  query: 'paniced unexcepted event or ("paniced kdump event")'
  analyse: select '' as ext3,1 as panic ,regexp_extract(detail,'domain ([\w\-]*)\.',1) as machine_id ,__source__ as nc_ip,min(time) as exception_time,case when position('kdump' in detail) != 0 then 1 else 0 end as kdump_panic  group by detail,nc_ip limit 10000
  logstore: 'libvirt_log'
  slsConfigName: ecs_inspector
  silent: 0
  retentionTime: 13
  detailMetric: queryVmPanicLog
  tag: Unavailable
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO,DRAGONBOX_CN_INFO]
  linkedQueryList: [queryVmFeatures(machine_id@or),queryEventFDIssue(machine_id@or)]
  exclusion:
    ext: '"{{VM_INFO}}" == "None" or "{{VM_INFO.status}}" in ("Destroyed","destroying","Released","Releasing")'
  levels:
    - name: fatal
      expression: '{{panic}} == 1 and ("{{machine_id}}".startswith("eci") or "{{machine_id}}".startswith("acs") )'
      notify: [SlsAlertHandler]
      atDuty: [eci]
    - name: critical
      expression: '{{panic}} == 1 and {{kdump_panic}} == 1'
      notify: [SlsAlertHandler]
    - name: warning
      expression: '{{panic}} == 1'
      notify: [SlsAlertHandler]

vm_arp_ping_recovery:
  interval: 65
  timeRange: 500
  enable: true
  doc: 'VM_ARP_PING延迟时间超时，疑似VM内部故障'
  type: vm_network_sys
  query: 'exceptionName: vm_arp_ping_rtt_too_long and not warningLevel: normal'
  analyse: select count(*) as cnt,instanceId as machine_id,max_by(exceptionTime,__time__) as time group by machine_id HAVING cnt <= 3 and to_unixtime(now()) - to_unixtime(time) >= 320 limit 10000
  logstore: 'monitor_exception_sls_alert'
  detailMetric: queryVmArpPingMetric
  slsConfigName: ecs_inspector
  linkedDimensions: [NC_INFO,VM_INFO,USER_INFO]
  silent: 330
  region: [default]
  powerSQL: true
  retentionTime: 2
  tag: Event
  levels:
    - name: normal
      expression: '{{cnt}} >= 3 or "{{VM_INFO.status}}" in ("Destroyed","destroying","Released","Releasing","Shutted")'
      persistent: false
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=EndTimerProcessor,eventName=arpping_timeout,silent=900)']

vm_arp_ping_rtt_too_long:
  interval: 59
  timeRange: 190
  enable: true
  doc: 'VM_ARP_PING延迟时间超时，疑似VM内部故障'
  type: vm_network_sys
  query: 'not vm_name: "" and not vm_name: None and arp_tx_packets > 0'
  analyse: select timeout_vm.nc_ip,timeout_vm.machine_id,timeout_vm.arp_ping_rtt_avg,timeout_vm.timout_ratio,timeout_vm.cnt,timeout_vm.exception_time,case when vm_pps.network_normal is not null then vm_pps.network_normal else 1 end as network_normal from ( SELECT NC_IP as nc_ip,split_part(vm_name, '.', 1) as machine_id,avg(rtt_avg) as arp_ping_rtt_avg,sum(arp_timeout_num) * 1.0 / sum(arp_tx_packets) as timout_ratio,count(*) as cnt,max(begin_time) as exception_time from log GROUP BY nc_ip,vm_name HAVING timout_ratio >= 0.97 and cnt > 1 and arp_ping_rtt_avg = 0 and machine_id not like 'BVT%' and machine_id not like 'eno%' ) as timeout_vm left join ( select DISTINCT 0 as network_normal,split_part(vm_name,'.',1) as machine_id from nc_stats_vport_1min_agg having session_count < 110 and tx_packets_avg < 40 and rx_packets_avg < 40 ) as vm_pps on vm_pps.machine_id = timeout_vm.machine_id limit 20000
  logstore: 'nc_stats_arp_ping_1min_agg'
  detailMetric: queryVmArpPingMetric
  slsConfigName: ecs_network
  linkedDimensions: [NC_INFO,VM_INFO,USER_INFO]
  silent: 350
  retentionTime: 2
  maxTraceLog: 500
  powerSQL: true
  tag: PerformanceImpact
  exclusion:
    # 已释放，创建在50分钟以内的实例都需要排除,原因是会有各种存储的噪声
    ext: '"{{VM_INFO}}" == "None" or "{{VM_INFO.status}}" in ("Destroyed","Shutted","Shutting","destroying") or {{VM_INFO.getDeltaTimeForStart}} <= 800 or "{{VM_INFO.aliUid}}" == "1626999119920274"'
  levels:
    - name: critical
      expression: '{{timout_ratio}} >= 0.97 and {{arp_ping_rtt_avg}} == 0 and {{NC_INFO.isPhysicalMachine}} and {{network_normal}} == 0'
      action: ['batchRunTask(taskName=VsockPingCheckProcessor)','batchRunTask(taskName=ScreenshotDiagnoseProcessor)']
      persistent: false
      notify: [SlsAlertHandler]
    - name: warning
      expression: '{{timout_ratio}} >= 0.97 and {{arp_ping_rtt_avg}} == 0 and "{{machine_id}}".startswith("eci") and {{network_normal}} == 0'
      action: ['batchRunTask(taskName=VsockPingCheckProcessor)','batchRunTask(taskName=ScreenshotDiagnoseProcessor)']
      persistent: false
      notify: [SlsAlertHandler]
    - name: low_warning
      expression: '{{timout_ratio}} >= 0.97 and {{arp_ping_rtt_avg}} == 0 and {{network_normal}} == 0'
      persistent: false
      notify: [SlsAlertHandler]

vm_arp_ping_timeout_ratio_high:
  interval: 63
  timeRange: 600
  enable: true
  doc: 'VM_ARP_PING timeout过多'
  type: vm_network_sys
  query: 'not vm_name: "" and not vm_name: None and rtt_avg > 0 and arp_timeout_num > 0'
  analyse: select count_if(arp_timeout_ratio >= 0.1) as timeout_ratio_high_cnt,NC_IP as nc_ip,split_part(vm_name, '.', 1) as machine_id,max(arp_timeout_ratio)  as max_ratio from (select sum(arp_timeout_num) * 1.0 / sum(arp_tx_packets) as arp_timeout_ratio,sum(arp_tx_packets) as arp_tx_packets,NC_IP,vm_name,to_unixtime(begin_time) - to_unixtime(begin_time) % 60 as time_minute from log where regexp_like(begin_time, '\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}') group by NC_IP,vm_name,time_minute) group by nc_ip,machine_id having timeout_ratio_high_cnt >= 4 limit 10000
  logstore: 'nc_stats_arp_ping_1min_agg'
  detailMetric: queryVmArpPingMetric
  slsConfigName: ecs_network
  linkedDimensions: [NC_INFO,VM_INFO,USER_INFO]
  silent: 680
  retentionTime: 2
  maxTraceLog: 500
  tag: PerformanceImpact
  powerSQL: true
  exclusion:
    # 已释放，创建在50分钟以内的实例都需要排除,原因是会有各种存储的噪声
    ext: '"{{VM_INFO}}" == "None" or "{{VM_INFO.status}}" in ("Destroyed","Shutted","Shutting") or "{{VM_INFO.instanceTypeFamily}}" == "ecs.poc-test" or {{VM_INFO.getDeltaTimeForStart}} <= 3000 or "{{VM_INFO.aliUid}}" in ["1111797051161186", "1647796581073291", "1212121042863399", "1108921452760297", "1008276465906671", "1158592593815165", "5924158341670144", "5311690394279550", "5704391078516231", "5370229363125969", "1044774937380389","1414201222240683","1998516749355656","1900050632443592","1098265594793708","1118383084773269","1009057614996545","1699509817074545","1306275754943458","1637521838344392","1819217489945997","1020953122742015","1242424451954755","1889198963850127","1426975756951578","1295175756943409","1232711574730627","1779896401038998","1969844741194574","1009057614996545","1967135963103465","1789865587421410","1386324885355401","1386844741194306","1285827532264027","1275153877397226"]'
  levels:
    - name: fatal
      expression: '{{timeout_ratio_high_cnt}} >= 9 and {{max_ratio}} >= 0.45'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
    - name: critical
      expression: '{{timeout_ratio_high_cnt}} >= 4 and {{max_ratio}} >= 0.45'
      persistent: false
      notify: [SlsAlertHandler,EventCenterAlertHandler]
    - name: warning
      expression: '{{timeout_ratio_high_cnt}} >= 9 and {{max_ratio}} < 0.45'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
    - name: low_warning
      expression: '{{timeout_ratio_high_cnt}} >= 4 and {{max_ratio}} < 0.45'
      persistent: false
      notify: [SlsAlertHandler,EventCenterAlertHandler]
  eventCenter:
    push: true
    opsCode: InstanceFailure
    impact: Alert
    resourceStatus: Warning
    category: VM.Availability

nc_hang_too_long:
  interval: 60
  timeRange: 620
  enable: true
  doc: '物理机严重夯机超时'
  type: nc_cpu_event
  query: 'not busy and not readonly and not down and not INFO and not iowait and not server_down and not get_error_server_more_then_limit and not ssh_stop'
  analyse: select max(date_parse(cast(time_int as varchar),'%Y%m%d%H%i%S')) as exception_time,regexp_split(nc_ip,',') as nc_ip, regexp_split(other_info, '\s[ssh_err|busy|iowait|readonly|down|ssh_tmo|ssh_stop]*\|') as nc_ips,count(*) as hang_count GROUP BY nc_ip,other_info HAVING exception_time is not null limit 1000
  logstore: 'bajie_check_nc_hang'
  slsConfigName: ecs_inspector
  silent: 480
  detailMetric: queryNcHangDownStatus
  retentionTime: 100
  tag: PerformanceImpact
  linkedDimensions: [NC_INFO,HOUYI_NC_INFO]
  exclusion:
    ext: '"{{NC_INFO}}" == "None"'
  levels:
    - name: fatal
      expression: '{{hang_count}} >= 6'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      action: ['batchRunTask(taskName=OOBStatusCheckProcessor)','batchRunTask(taskName=VsockWrapperCheckProcessor)']
      hasVmThenAlert: true
    - name: critical
      expression: '{{hang_count}} >= 4'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      action: ['batchRunTask(taskName=OOBStatusCheckProcessor)','batchRunTask(taskName=VsockWrapperCheckProcessor)']
      hasVmThenAlert: true
    - name: warning
      # 连续三分钟持续夯机
      expression: '{{hang_count}} >= 3'
      notify: [SlsAlertHandler]
      hasVmThenAlert: true
      action: ['batchRunTask(taskName=OOBStatusCheckProcessor)','batchRunTask(taskName=VsockWrapperCheckProcessor)']
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Hang
    resourceStatus: NotApplicable
    category: VM.Availability

vm_down_end:
  interval: 28
  timeRange: 54000
  enable: true
  doc: 'VM宕机恢复事件'
  type: vm_cpu_available
  query: '(exceptionName: nc_down_alert and not additionalInfo: analyse or exceptionName: vm_down_end) and is_location_public_in_sale: 1 and not instanceId: None'
  analyse: select *,1 as down_end,0 as outer_shutdown,-1 as etime_seconds from (select exceptionTime as extension,exceptionTime as down_start_time,to_unixtime(exceptionTime) as down_timestamp,to_unixtime(exceptionTime) as ready_timestamp,machine_id,nc_ip,cluster_name,lead(exceptionName, 1, null) over(partition BY machine_id, cluster_name,nc_ip ORDER by exceptionTime) as event_type,exceptionName as sourceErrorName,actionOnMaintenance as ext3,vcpu_mod from (select actionOnMaintenance,instanceId as machine_id, cluster_alias as cluster_name,ncIp as nc_ip,exceptionName,exceptionTime,vcpu_mod from log) ) where event_type is null and sourceErrorName in ('nc_down_alert','recover_preemption_disk_event') and to_unixtime(now()) - down_timestamp <= 53280 limit 50000
  logstore: 'monitor_exception_sls_alert'
  slsConfigName: ecs_inspector
  silent: 0
  region: [default]
  linkedQueryList: [queryVmQemuETime(nc_ip@or),queryUserStopRestartVmOpLog()]
  retentionTime: 150
  powerSQL: true
  detailMetric: queryVmRunStatusChanged
  tag: Event
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO,DRAGONBOX_CN_INFO]
  levels:
    - name: critical
      # 脱网原地恢复的情况根据etime_seconds超过1000s来判断
      expression: '{{etime_seconds}} >= 1000 and ({{ready_timestamp}} - {{down_timestamp}} >= 100)'
      action: ['batchRunTask(taskName=EndTimerProcessor,eventName=nc_down|vm_down_but_not_recover|nc_hang|cn_hang,slsPush=True,gocFailurePush=True)', 'batchRunTask(taskName=VsockPingCheckProcessor,delay=30)']
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      persistent: false
    - name: warning
      expression: '1000 >= {{etime_seconds}} >= 0 and ({{ready_timestamp}} - {{down_timestamp}} >= 100) or ("{{ext3}}" == "Stop" or {{outer_shutdown}} == 1)'
      action: ['batchRunTask(taskName=EndTimerProcessor,eventName=nc_down|vm_down_but_not_recover|nc_hang|cn_hang,slsPush=True,gocFailurePush=True)', 'batchRunTask(taskName=VsockPingCheckProcessor,delay=30)']
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      persistent: false
    - name: low_warning
      # 过期/运行中都可以认为是vm down end 结束
      expression: '{{down_end}} == 1 and "{{VM_INFO.gmtSync}}" > "{{down_start_time}}" and ("{{VM_INFO.status}}" == "Running" or "{{VM_INFO.ecsBusinessStatus}}" == "Expired")'
      action: ['batchRunTask(taskName=EndTimerProcessor,eventName=nc_down|vm_down_but_not_recover|nc_hang|cn_hang,timeField=gmtSync,slsPush=True,gocFailurePush=True)','batchRunTask(taskName=VsockPingCheckProcessor,delay=30)']
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      persistent: false
    - name: normal
      # 释放的VM算down end
      expression: '{{down_end}} == 1 and ("{{VM_INFO}}" == "None" or "{{VM_INFO.status}}" in ("Destroyed","destroying","Released","Releasing"))'
      action: ['batchRunTask(taskName=EndTimerProcessor,eventName=nc_down|vm_down_but_not_recover|nc_hang|cn_hang,slsPush=True,gocFailurePush=True)']
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      persistent: false
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Ok
    category: VM.Availability
    closureFlag: end
    closureEvent: vm_down

vm_crash_end:
  interval: 23
  timeRange: 7200
  enable: true
  doc: 'VM_CRASH恢复事件'
  type: vm_cpu_available
  query: '(exceptionName: vm_crash_event and not warningLevel: low_warning or exceptionName: vm_crash_end and not warningLevel: fatal or exceptionName: vm_livemigrate_exception and warningLevel: fatal or exceptionName: vm_unexpected_killed_by_other) and is_location_public_in_sale: 1 and not instanceId: None'
  analyse: select *,1 as down_end,to_unixtime(now()) - to_unixtime(down_start_time) as delta_time from (select exceptionTime as down_start_time,machine_id,nc_ip,cluster_name,lead(exceptionName, 1, null) over(partition BY machine_id, cluster_name,nc_ip ORDER by exceptionTime) as event_type,exceptionName as sourceErrorName from (select instanceId as machine_id, cluster_alias as cluster_name,ncIp as nc_ip,exceptionName,exceptionTime from log) ) where event_type is null and sourceErrorName in ('vm_crash_event','vm_livemigrate_exception') limit 50000
  logstore: 'monitor_exception_sls_alert'
  slsConfigName: ecs_inspector
  silent: 460
  region: [default]
  powerSQL: true
  retentionTime: 100
  detailMetric: queryVmRunStatusChanged
  tag: Event
  linkedDimensions: [NC_INFO,HOUYI_NC_INFO,VM_INFO,USER_INFO,DRAGONBOX_CN_INFO,VM_MAINTENANCE_INFO]
  levels:
    - name: fatal
      expression: ' {{down_end}} == 1 and "{{VM_INFO.status}}" != "Running" and {{delta_time}} >= 280 and "{{VM_MAINTENANCE_INFO.Value}}" != "Stop" and "ecs.eci" not in "{{VM_INFO.instanceType}}"'
      notify: [SlsAlertHandler]
      filterChains: ['GammaEnvFilter()','OpsAttributeFilter(needLeaveNc=0)']
    - name: warning
      # 用户设置的运维策略就是要离开
      expression: '{{down_end}} == 1 and "{{VM_INFO.status}}" != "Running" and ("{{VM_MAINTENANCE_INFO.Value}}" == "Stop" or "ecs.eci" in "{{VM_INFO.instanceType}}")'
      action: ['batchRunTask(taskName=EndTimerProcessor,eventName=vm_crash,timeField=gmtSync,gocFailurePush=True,slsPush=True)']
      notify: [SlsAlertHandler]
      persistent: false
    - name: low_warning
      # 过期/运行中都可以认为是vm down end 结束
      expression: '{{down_end}} == 1 and "{{VM_INFO.gmtSync}}" >= "{{down_start_time}}" and ("{{VM_INFO.status}}" == "Running" or "{{VM_INFO.ecsBusinessStatus}}" == "Expired")'
      action: ['batchRunTask(taskName=EndTimerProcessor,eventName=vm_crash,timeField=gmtSync,gocFailurePush=True,slsPush=True)']
      notify: [SlsAlertHandler]
      persistent: false
    - name: normal
      # 释放的VM算down end
      expression: '{{down_end}} == 1 and ("{{VM_INFO}}" == "None" or "{{VM_INFO.status}}" in ("Destroyed","destroying","Released","Releasing"))'
      action: ['batchRunTask(taskName=EndTimerProcessor,eventName=vm_crash,timeField=gmtSync,gocFailurePush=True,slsPush=True)']
      notify: [SlsAlertHandler]
      persistent: false

vm_crash_event:
  interval: 13
  timeRange: 110
  enable: true
  doc: 'VM异常crash事件'
  type: vm_cpu_available
  query: domain crashed or 'action=crash'
  analyse: select 0 as op_ret,0 as migrate_finished,'[]' as killed_vm_list,-1 as lastStartingTime,to_unixtime(time) as exception_timestamp ,0 as submit_ops,'["other"]' as statuses,split_part(machine_id, '.', 1) as machine_id,time as exception_time,1 as crashed,report_nc_ip,report_nc_ip as extension  from (select case when position('Xend' IN class) != 0 then regexp_extract(detail,'vm_name=([\w\-]+)',1) else regexp_extract(detail, '.([\.\w\d\-]+). closed',1) end as machine_id,time,__source__ as report_nc_ip from log ) where machine_id not like 'iso-%' and machine_id not like 'tmp%' and machine_id not like 'con%' and machine_id not like 'dpdk%' and machine_id not like 'erase%' and machine_id not like 'BVT%' limit 50000
  logstore: libvirt_log
  slsConfigName: ecs_inspector
  silent: 0
  linkedQueryList: [queryVmHasMigrated(),queryNcHitCnDownMigrateAction(),queryVmHasKilled(),queryOpenDiskFailedCode()]
  detailMetric: queryVmRunStatusChanged
  retentionTime: 60
  powerSQL: true
  alarmId: test_vm_crash
  tag: Unavailable
  linkedDimensions: [NC_INFO,HOUYI_NC_INFO,USER_INFO,VM_INFO]
  exclusion:
    ext: '"{{VM_INFO}}" == "None" or "{{VM_INFO.status}}" in ("Pending","Starting","Destroyed") or "{{VM_INFO.ecsBusinessStatus}}" == "Starting" or {{VM_INFO.getDeltaTimeForStart}} < 200 or "{{machine_id}}" in {{killed_vm_list}} or "{{machine_id}}".startswith("cn-ramos") or "{{VM_INFO.aliUid}}" == "5279925069529723" '
  levels:
      # 过滤掉两种情况，1热迁移失败的时候，会回迁的时候，会主动crash掉，2.cn宕机迁移的时候，会先kill iohub，导致vmcrash，这两种情况都属于vmcrash
      # 目前发现启停的时候也会有vmcrash的误报，排除掉
    - name: warning
      expression: '{{crashed}} == 1 and {{submit_ops}} == 0 and (({{exception_timestamp}} - {{lastStartingTime}} >= 150)) and "{{VM_INFO.ncIp}}" == "{{report_nc_ip}}" and {{op_ret}} != -44000'
      action: [batchLinkedActiveOps,'batchRunTask(taskName=StartTimerProcessor,eventName=vm_crash,deltaTime=0,silent=900,slsPush=True)','batchRunTask(taskName=ReasonDetectProcessor,exceptionNameField=exceptionName,detectType=REASON_VM_APPLICABILITY,delay=45,timeRange=7200)']
      notify: [EventCenterAlertHandler,SlsAlertHandler,FailureRecoverAlertHandler]
    - name: low_warning
      expression: '{{crashed}} == 1 and {{submit_ops}} == 0 and (({{exception_timestamp}} - {{lastStartingTime}} >= 150)) and {{op_ret}} != -44000'
      action: [batchLinkedActiveOps]
      persistent: false
      notify: [SlsAlertHandler,FailureRecoverAlertHandler]
    - name: normal
      expression: '{{crashed}} == 1 and {{submit_ops}} == 0 and (({{exception_timestamp}} - {{lastStartingTime}} >= 150)) and {{op_ret}} == -44000'
      persistent: false
      notify: [ SlsAlertHandler ]
  eventCenter:
    push: true
    opsCode: InstanceFailure
    impact: Reboot
    resourceStatus: NotApplicable
    category: VM.Availability
    eventSilent: 3558

regionmaster_lost_nc_heartbeat:
  interval: 28
  timeRange: 60
  enable: true
  doc: '管控丢失PYNC_UDP心跳超1分钟'
  type: nc_network_sys
  query: 'nc_network_jitter or nc_real_down'
  analyse: select min(time) as exception_time,machine_id as nc_ip,count(*) as exception_count,regexp_extract(alarm_item, '\((.*)\)', 1) as reason  group by machine_id,other_info,alarm_item limit 20000
  logstore: regionmaster_alarm
  slsConfigName: ecs_monitor
  detailMetric: queryRegionmasterJudgeNcInfo
  # 需要监控的region 不写全部监控
  region: [default]
  silent: 300
  retentionTime: 13
  powerSQL: true
  tag: PerformanceImpact
  linkedDimensions: [NC_INFO,DRAGONBOX_CN_INFO]
  levels:
    - name: fatal
      expression: '{{exception_count}} >= 1 and "{{reason}}" == "nc_real_down"'
      notify: [SlsAlertHandler]
    - name: critical
      expression: '{{exception_count}} >= 1'
      action: ['batchRunTask(taskName=StaragentCheckProcessor,callback=False,excludeAZones=in-west-antgroup-1)']
      notify: [SlsAlertHandler]
      needRelatedVm: false

nc_down_alert_old:
  interval: 45
  timeRange: 100
  enable: true
  doc: 'nc宕机导致VM宕'
  type: vm_cpu_event
  query: 'not Zone: test-zone and (EventType = -2 or EventType = -3 or EventType = -5 or EventType = -7 or EventType = -9) and not Region: cn-hangzhou-tf52ac3-2'
  analyse: SELECT DISTINCT  InstanceId as machine_id,NCIP as nc_ip,time as exception_time, 'down_start' as down_type limit 100000
  logstore: vm_down
  slsConfigName: ecs_monitor
  region: [default]
  silent: 980
  powerSQL: true
  detailMetric: queryVmRunStatusChanged
  tag: Unavailable
  linkedDimensions: [NC_INFO,VM_INFO,USER_INFO,DRAGONBOX_CN_INFO]
  retentionTime: 13
  exclusion:
    ext: '"{{VM_INFO}}" == "None" or "{{VM_INFO.instanceType}}".startswith("ecs.lbm") or "{{VM_INFO.instanceType}}".startswith("ecs.sbm")'
  levels:
    - name: critical
      expression: '"{{down_type}}" == "down_start" and "BVT" not in "{{machine_id}}"'
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=nc_down,deltaTime=15,silent=10800,slsPush=True,skipKwArgs=1,sync=True)']
      notify: [SlsAlertHandler]
      persistent: false

nc_down_event:
  #假设一个nc从宕机到重启上电最多耗时1800s
  interval: 62
  #3600s保证无论每次间隔查询的起始点在哪里，当宕机重启发生后，总能保证有一次查询可以囊括一次完整的宕机结束点和重启发生时间点
  timeRange: 1000
  enable: true
  doc: 'NC真宕机非网络问题'
  type: nc_cpu_event
  query: '*'
  analyse: SELECT new_uptime,'uptime 0' as ext3,nc_ip, exception_time, 1 as nc_down,delta_uptime from (select nc_ip,cluster_name,from_unixtime(to_unixtime(collect_time)+60) as exception_time, uptime-(lead(uptime, 1, 2147483647) over(partition BY nc_ip, cluster_name ORDER by collect_time) ) as delta_uptime,lead(uptime, 1, 2147483647) over(partition BY nc_ip, cluster_name ORDER by collect_time) as new_uptime  from (select date_trunc('minute',time) as collect_time,__source__ as nc_ip,max(uptime) as uptime,cluster_name from log group by collect_time,nc_ip,cluster_name)) WHERE delta_uptime >= 59 and new_uptime <= 360 and to_unixtime(now())-to_unixtime(exception_time) <= 3800 limit 10000
  logstore: nc_perf_load
  slsConfigName: ecs_monitor
  silent: 380
  detailMetric: queryNcUptimeMetric
  linkedDimensions: ['NC_INFO']
  retentionTime: 40
  tag: Unavailable
  exclusion:
    ext: '"轮转升级" in "{{ext3}}"'
  levels:
    - name: fatal
      expression: '{{new_uptime}} <= 360 and {{NC_INFO.isXDragon}}'
      action: [batchLinkedActiveOps,'batchRunTask(taskName=ResidentRebootRecordProcessor)','batchRunTask(taskName=TDCCheckProcessor)']
      notify: [SlsAlertHandler]
    - name: critical
      expression: '{{new_uptime}} <= 360'
      action: [batchLinkedActiveOps,'batchRunTask(taskName=ResidentRebootRecordProcessor)','batchRunTask(taskName=TDCCheckProcessor)']
      notify: [SlsAlertHandler]
    - name: warning
      expression: '360 < {{new_uptime}} <= 3800'
      action: [batchLinkedActiveOps,'batchRunTask(taskName=TDCCheckProcessor)']
      notify: [SlsAlertHandler]
      filterChains: [GammaEnvFilter(),GlobalAlarmFilter()]

nc_down_event_fastpath:
  interval: 62
  timeRange: 1000
  enable: true
  doc: 'NC真宕机非网络问题，通过快速路径发现'
  type: nc_cpu_event
  query: ''
  analyse: ''
  logstore: ''
  slsConfigName: ecs_monitor
  silent: 380
  detailMetric: queryNcUptimeMetric
  linkedDimensions: ['NC_INFO']
  retentionTime: 40
  tag: Unavailable
  exclusion:
    ext: '"轮转升级" in "{{ext3}}"'
  levels:
    - name: fatal
      expression: '{{new_uptime}} <= 360 and {{NC_INFO.isXDragon}}'
      action: [batchLinkedActiveOps,'batchRunTask(taskName=ResidentRebootRecordProcessor)','batchRunTask(taskName=TDCCheckProcessor)']
      notify: [SlsAlertHandler]
    - name: critical
      expression: '{{new_uptime}} <= 360'
      action: [batchLinkedActiveOps,'batchRunTask(taskName=ResidentRebootRecordProcessor)','batchRunTask(taskName=TDCCheckProcessor)']
      notify: [SlsAlertHandler]
    - name: warning
      expression: '360 < {{new_uptime}} <= 3800'
      action: [batchLinkedActiveOps,'batchRunTask(taskName=TDCCheckProcessor)']
      notify: [SlsAlertHandler]

nc_down_judge_bajie:
  interval: 30
  timeRange: 60
  enable: true
  doc: 'AG上ping NC不通'
  type: nc_cpu_event
  query: 'down'
  analyse: select 'normal' as nc_biz_status,source,nc_ip,min(time) as exception_time,count(*) as down_count,to_unixtime(now()) - to_unixtime(min(time)) as down_time from (select array_union(CASE when error_type = 'down' then regexp_split(nc_ip,',') else regexp_split(',',',') end, regexp_extract_all(other_info, '(\d+\.\d+\.\d+\.\d+) down',1))  as nc_list,error_type,error_type as ext3,date_parse(cast(time_int as varchar), '%Y%m%d%H%i%S') as time,__source__ as source from log GROUP BY nc_ip,other_info,error_type,time_int,source),unnest( cast( nc_list as array(varchar) ) ) as t(nc_ip) group by nc_ip,source,error_type HAVING  nc_ip != '' limit 10000
  logstore: 'bajie_check_nc_hang'
  slsConfigName: ecs_inspector
  silent: 43200
  detailMetric: queryNcHangDownStatus
  linkedQueryList: [queryLastNcStatus(nc_ip@or)]
  retentionTime: 120
  tag: PerformanceImpact
  linkedDimensions: ['NC_INFO','HOUYI_NC_INFO']
  exclusion:
    ext: '"{{NC_INFO}}" == "None" or {{NC_INFO.security_level}} == 2'
  levels:
    - name: fatal
      expression: '{{down_count}} >= 1 and {{NC_INFO.isXDragon}} and "{{nc_biz_status}}" != "nc_down" and {{NC_INFO.isOnlineNc}}'
      action: [batchLinkedActiveOps]
      notify: [SlsAlertHandler]
      hasVmThenAlert: true
    - name: critical
      expression: '{{down_count}} >= 1 and {{NC_INFO.isOnlineNc}} and "{{nc_biz_status}}" != "nc_down"'
      action: [batchLinkedActiveOps]
      notify: [SlsAlertHandler]
      hasVmThenAlert: true

vm_down_but_not_recover:
  interval: 102
  timeRange: 21600
  enable: true
  doc: 'NC宕机但虚拟机未主动迁移'
  type: nc_cpu_event
  query: 'not AY374Z and not AY364X and not AY374B and not AY374G and not AY374N and not AY364Z and not AY364P and not AY374D and not AY340E and not AY240L and not AY364Y and not AY240Y and not AY287W and exception_name: nc_down and is_local_disk: 0 and not ext1: cn-hangzhou-onebox-nebula'
  analyse: select '' as reason,'' as ext3,nc_ip as machine_id,to_unixtime(now()) - to_unixtime(max(start_time)) as delta_time,sum(unrecovered_cnt) as unrecover_cnt,nc_ip,max(start_time) as lastExceptionTime from ( select DISTINCT case when end_time is not null and length(trim(end_time)) != 0 then -1 else 1 end as unrecovered_cnt,instance_id,nc_ip,start_time from log WHERE  instance_id not like 'netvm%' and instance_id not like 'storage%' and instance_id not like 'pan%' and instance_id not like 'E2E%' and instance_id not like '%houyiecsay%' and instance_id not like 'ebs%' ) group by nc_ip HAVING unrecover_cnt > 0 and delta_time >= 500 limit 3000
  logstore: event_start_end
  slsConfigName: xunjian_zhangbei
  region: [default]
  linkedQueryList: [queryNotRecoveryReason(nc_ip@or)]
  silent: 0
  transformParams: [relateNcRlockInfoToReason]
  detailMetric: queryVmRunStatusChanged
  tag: ControlFailed
  linkedDimensions: [NC_INFO,HOUYI_NC_INFO,VM_MAINTENANCE_INFO]
  retentionTime: 130
  exclusion:
    # ddh / 618 / 都不迁移
    ext: '"{{NC_INFO}}" == "None" or not {{HOUYI_NC_INFO.supportDDHMigration}} or {{NC_INFO.notSupportAutoRecovery}} or "618" in "{{NC_INFO.supported_family}}" or {{NC_INFO.isMuteDownMigration}} or "re6p" in "{{NC_INFO.supported_family}}" or "{{NC_INFO.supported_family}}" in ("ecs.ebmgn6-inc","ecs.f3e","ecs.sccgn6ne-inc","ecs.ebmhfg5") or "undelay" in "{{NC_INFO.supported_family}}"'
  levels:
    - name: fatal
      expression: '{{delta_time}} >= 440 and {{unrecover_cnt}} > 0 and ( "RECOVER_ALIVE_ERROR" in "{{ext3}}" or "{{reason}}" in ("RES_NO_MIGRATION_QUOTA","RES_RLOCK_OVERUSE") ) and "RES_TMP_NOT_SLA" not in "{{ext3}}" and "RES_STATUS_NOT_REQUIRED" not in "{{ext3}}"'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=vm_down_but_not_recover,silent=2140,slsPush=True)']
      atDuty: [nc,hitch_control]
      filterChains: [GammaEnvFilter(),OpsAttributeFilter(),GlobalAlarmFilter()]
      restrainChains: ['TimeRangeRestrain(startTime=22:00:00;endTime=08:00:00;checkResume=True)','AliuidRestrain(aliuidList=[1647796581073291,1108921452760297];startTime=22:00:00;endTime=10:00:00)']
    - name: critical
      expression: '{{delta_time}} >= 440 and {{unrecover_cnt}} > 0 and "START_VM_ERROR" in "{{ext3}}" and "RES_TMP_NOT_SLA" not in "{{ext3}}" '
      atDuty: [instance_control,hitch_control]
      filterChains: [GammaEnvFilter(),OpsAttributeFilter(),GlobalAlarmFilter()]
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=vm_down_but_not_recover,silent=2140,slsPush=True)']
      restrainChains: ['TimeRangeRestrain(startTime=22:00:00;endTime=08:00:00;checkResume=True)','AliuidRestrain(aliuidList=[1647796581073291,1108921452760297];startTime=22:00:00;endTime=10:00:00)']
    - name: warning
      expression: '{{delta_time}} >= 440 and {{unrecover_cnt}} > 0 and ("{{reason}}" in ("RES_SUPPLY_OVER_SLA","RES_LARK_OTHER","RLOCK_NC_EXISTED") or "SYSTEM_ERROR" in "{{ext3}}" or "RESOURCE_ERROR" in "{{ext3}}" and ("RES_STATUS_STOP_SUPPLY_MODEL" not in "{{ext3}}" or "RES_TMP_NOT_SLA" not in "{{ext3}}" or "RES_STATUS_CLASSIC" not in "{{ext3}}" or "RES_STATUS_STOP_SUPPLY_DEFEND" not in "{{ext3}}" or "RES_STATUS_NOT_REQUIRED" not in "{{ext3}}") )'
      atDuty: [inventory,inventory_stable,inventory_schedule,巴赫,马信]
      filterChains: [GammaEnvFilter(),OpsAttributeFilter(),GlobalAlarmFilter()]
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=vm_down_but_not_recover,silent=2140,slsPush=True)']
      restrainChains: ['TimeRangeRestrain(startTime=22:00:00;endTime=08:00:00;checkResume=True)','AliuidRestrain(aliuidList=[1647796581073291,1108921452760297];startTime=22:00:00;endTime=10:00:00)']
    - name: low_warning
      expression: '{{delta_time}} >= 440 and {{unrecover_cnt}} > 0 and "EXPECTED_ERROR" not in "{{ext3}}" and ("RES_STATUS_STOP_SUPPLY_MODEL" not in "{{ext3}}" or "RES_STATUS_CLASSIC" not in "{{ext3}}" or "RES_STATUS_STOP_SUPPLY_DEFEND" not in "{{ext3}}" ) and "RES_TMP_NOT_SLA" not in "{{ext3}}" and "RES_STATUS_NOT_REQUIRED" not in "{{ext3}}"'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=vm_down_but_not_recover,silent=2140,slsPush=True)']
      filterChains: [GammaEnvFilter(),OpsAttributeFilter(),GlobalAlarmFilter()]
      atDuty: [inventory,inventory_stable,inventory_schedule,nc,hitch_control,instance_control]
      restrainChains: ['TimeRangeRestrain(startTime=22:00:00;endTime=08:00:00;checkResume=True)','AliuidRestrain(aliuidList=[1647796581073291,1108921452760297];startTime=22:00:00;endTime=10:00:00)']
    - name: normal
      expression: '{{delta_time}} >= 440 and {{unrecover_cnt}} > 0 and ("RES_STATUS_STOP_SUPPLY_MODEL" not in "{{ext3}}" or "RES_STATUS_CLASSIC" not in "{{ext3}}" or "RES_STATUS_STOP_SUPPLY_DEFEND" not in "{{ext3}}" ) and "RES_TMP_NOT_SLA" not in "{{ext3}}"'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=vm_down_but_not_recover,silent=2140,slsPush=True)']
      atDuty: [巴赫]
      filterChains: [GammaEnvFilter(),OpsAttributeFilter(),GlobalAlarmFilter()]
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Warning
    category: NC.Sys_Hidden_Danger&VM.Availability

vm_down_not_recovery:
  interval: 105
  timeRange: 21600
  enable: true
  doc: 'vm宕机但虚拟机未主动迁移启动'
  type: vm_cpu_event
  query: 'exception_name: nc_down and is_local_disk: 0'
  analyse: select * from (select count(*) over (partition BY region) as region_cnt,sum(unrecovered_cnt) as unrecover_cnt,instance_id as machine_id,to_unixtime(now()) - to_unixtime(start_time) as delta_time,nc_ip,region from (select DISTINCT case when end_time is not null and length(trim(end_time)) != 0 then -1 else 1 end as unrecovered_cnt,instance_id,nc_ip,start_time,ext1 as region from log WHERE  instance_id not like 'netvm%') group by instance_id,start_time,nc_ip,region HAVING unrecover_cnt > 0 and delta_time >= 440) HAVING region_cnt <= 1024 and machine_id not like 'storagevm%' and machine_id not like 'network%' limit 10000
  logstore: event_start_end
  slsConfigName: xunjian_zhangbei
  region: [default]
  silent: 0
  detailMetric: queryVmRunStatusChanged
  tag: ControlFailed
  linkedDimensions: [VM_INFO,USER_INFO,NC_INFO,HOUYI_NC_INFO]
  retentionTime: 130
  exclusion:
    ext: '"{{HOUYI_NC_INFO}}" == "None" or "{{VM_INFO}}" == "None" or {{HOUYI_NC_INFO.isGammaMachine}} or not {{HOUYI_NC_INFO.supportDDHMigration}} or "{{NC_INFO}}" == "None" or {{NC_INFO.notSupportAutoRecovery}} or "618" in "{{NC_INFO.supported_family}}" or {{NC_INFO.isMuteDownMigration}} or "re6p" in "{{NC_INFO.supported_family}}" or "{{NC_INFO.supported_family}}" in ("ecs.ebmgn6-inc","ecs.f3e","ecs.sccgn6ne-inc","ecs.ebmhfg5") or "undelay" in "{{NC_INFO.supported_family}}"'
  levels:
    - name: critical
      expression: '{{delta_time}} >= 440 and {{HOUYI_NC_INFO.supportDDHMigration}} and {{HOUYI_NC_INFO.isDedicatedHost}}'
      atDuty: [instance_control]
      filterChains: [OpsAttributeFilter()]
      notify: [SlsAlertHandler]
      action: [batchRunTask(taskName=UnLockNcSearchProcessor)]
    - name: warning
      expression: '{{delta_time}} >= 440'
      atDuty: [instance_control]
      filterChains: [OpsAttributeFilter()]
      notify: [SlsAlertHandler]
      action: [batchRunTask(taskName=UnLockNcSearchProcessor)]

vm_paused_exception:
  interval: 99
  timeRange: 2700
  enable: true
  doc: '虚拟机处于暂停状态需要虚拟化调查'
  type: vm_cpu_event
  query: 'not hardware_paused_vms: "" or not paused_vms: ""'
  analyse: select split(hardware_paused_vms,',',30) as machine_list,split(paused_vms,',',300) as machine_id, cluster_name,count(*) as exception_cnt,__source__ as nc_ip,paused_vms,from_unixtime(max(timestamp)) as exception_time GROUP by hardware_paused_vms,paused_vms,cluster_name,__source__ limit 12000
  logstore: 'check_vm_kvm_detail'
  slsConfigName: ecs_inspector
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO,DRAGONBOX_CN_INFO]
  detailMetric: queryLiveMigrateNetworkDetail
  retentionTime: 40
  powerSQL: true
  tag: PerformanceImpact
  exclusion:
    ext: '"{{VM_INFO}}" == "None" or "{{VM_INFO.aliUid}}" == "1626999119920274"'
  silent: 3580
  levels:
    - name: fatal
      # 硬件故障导致paused
      expression: '{{exception_cnt}} >= 1 and {{machine_list}} != [""]'
      notify: [EventCenterAlertHandler,SlsAlertHandler]
      atDuty: [virtualization]
    - name: critical
      expression: '{{exception_cnt}} >= 3 and "{{paused_vms}}" not in ("","None","null")'
      notify: [EventCenterAlertHandler,SlsAlertHandler]
      action: ['batchRunTask(taskName=CheckHealthProcessor,checkItem="Vm Service Check")']
      atDuty: [virtualization]
    - name: warning
      expression: '{{exception_cnt}} >= 2 and "{{paused_vms}}" not in ("","None","null")'
      notify: [ SlsAlertHandler ]
      atDuty: [ virtualization ]
      action: ['batchRunTask(taskName=CheckHealthProcessor,checkItem="Vm Service Check")']
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Hang
    resourceStatus: Maintaining
    category: VM.Availability

vm_status_change_to_paused:
  interval: 57
  timeRange: 75
  enable: true
  doc: '虚拟机变成paused状态'
  type: vm_virt_event
  query: 'detail: paused and class: qemuProcessHandleStop and detail: Transitioned and detail: state'
  analyse: select 0 as user_op,'[]' as statuses,0 as vm_panic_too_many,0 as iohubctrl_upgrade,0 as is_qemu_hot_upgrade,regexp_extract(detail,'(\w+\-\w+)\.',1) as machine_id,__source__ as report_nc_ip,count(*) as cnt,max(time) as exception_time,count(DISTINCT class) as class_cnt,max(regexp_extract(detail,'\.\d+\.\d+ (.*)',1)) as ext3 group by machine_id,report_nc_ip limit 5000
  logstore: libvirt_log
  slsConfigName: ecs_inspector
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO,HOUYI_NC_INFO]
  detailMetric: queryLibvirtErrorLog
  retentionTime: 20
  tag: PerformanceImpact
  linkedQueryList: [queryVmHasMigrated(),queryVmPanicTooMany(),queryUserPausedInstanceOpLog(),queryIohubCtrlUpgradeEvent(),queryQemuHotUpgrade()]
  silent: 200
  exclusion:
    ext: '"{{VM_INFO}}" == "None"'
  levels:
    - name: warning
      expression: '{{class_cnt}} == 1 and "paused" in "{{ext3}}" and "Migrating" not in {{statuses}} and "{{HOUYI_NC_INFO.NcIp}}" == "{{report_nc_ip}}" and {{vm_panic_too_many}} == 0 and {{user_op}} == 0 and {{iohubctrl_upgrade}} == 0 and {{is_qemu_hot_upgrade}} == 0'
      notify: [ SlsAlertHandler ]
      action:
        - 'batchRunTask(taskName=CheckHealthProcessor,delay=45,checkItem="Vm Service Check")'
        - 'batchRunTask(taskName=VsockPingCheckProcessor,delay=45)'
    - name: normal
      expression: '{{class_cnt}} == 1 and "paused" in "{{ext3}}" and {{user_op}} == 1'
      notify: [ SlsAlertHandler ]

nc_hang_exception:
  interval: 45
  timeRange: 85
  enable: true
  doc: '物理机夯机异常'
  type: nc_cpu_event
  query: 'not source: "**********" and not source: "*************" and not down and not INFO and not iowait and not server_down and not __source__: "************"  and not __source__: "*************"'
  analyse: select regexp_split(nc_ip,',') as nc_ip, regexp_split(other_info, '\s[ssh_err|busy|iowait|readonly|down|ssh_tmo|ssh_stop]*\|') as nc_ips,error_type as ext3,error_type as extension,count(*) as hang_count, case when time_int is null then from_unixtime(timestamp) else date_parse(cast(time_int as varchar), '%Y%m%d%H%i%S') end as exception_time GROUP BY nc_ip,other_info,error_type,exception_time limit 300
  logstore: 'bajie_check_nc_hang'
  slsConfigName: ecs_inspector
  detailMetric: queryNcHangDownStatus
  silent: 580
  retentionTime: 120
  tag: PerformanceImpact
  linkedDimensions: [NC_INFO,HOUYI_NC_INFO,DRAGONBOX_CN_INFO]
  exclusion:
    ext: '"{{NC_INFO.supported_family}}" == "None"'
  levels:
    - name: fatal
      expression: '{{hang_count}} >= 1 and "{{ext3}}" == "get_error_server_more_then_limit" and {{NC_INFO.isOnSale}}'
      notify: [EventCenterAlertHandler,SlsAlertHandler]
      hasVmThenAlert: true
      persistent: false
    - name: critical
      expression: '{{hang_count}} >= 1 and "{{ext3}}" not in ("get_error_server_more_then_limit","ssh_stop")'
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=nc_hang,silent=1500,slsPush=True)']
      notify: [EventCenterAlertHandler,SlsAlertHandler]
      hasVmThenAlert: true
    - name: warning
      expression: '{{hang_count}} >= 1 and "{{ext3}}" == "readonly" and "{{NC_INFO}}" != "None" and "{{NC_INFO.getInstanceFamily}}" != "None"'
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=nc_hang,silent=1500,slsPush=True)']
      notify: [EventCenterAlertHandler,SlsAlertHandler]
    - name: low_warning
      expression: '{{hang_count}} >= 1 and "{{ext3}}" == "ssh_stop"'
      notify: [SlsAlertHandler]
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: NotApplicable
    category: VM.Availability
    eventSilent: 100

nc_ssh_status_recovery:
  interval: 58
  timeRange: 330
  enable: true
  doc: '物理机SSH状态恢复'
  type: nc_cpu_event
  query: 'not AY03G and not AY03H and not AY03F and not AY03B and not AY03E and not AY03C and not AY03D and not AY02A and not AY04A and not AY32I and not AY32G and not AY32J and not cluster_name: ""'
  analyse: select * from (select ssh_fail_cnt-(lead(ssh_fail_cnt, 1, 1000) over(partition BY nc_ip ORDER by timestamp) ) as is_ssh_ok,from_unixtime(timestamp + 140) as exception_time,nc_ip,ssh_result as ext3 from (select case when (ssh_result = 'SSH_CLOSE' or ssh_result = 'SSH_PERMISSION' or ssh_result = 'SSH_OTHER_ERROR' or ssh_result = 'SSH_TIMEOUT' or ssh_result = 'SSH_NOT_ROUTE' or ssh_result = 'SSH_REFUSED' or ssh_latency >= 20) and state != 'NC_DOWN' then 1 else 0 end as ssh_fail_cnt,timestamp,nc_ip,ssh_result from log order by timestamp)) where is_ssh_ok = 1 limit 10000
  logstore: 'nc_hang_down_check'
  slsConfigName: ecs_inspector
  detailMetric: queryNcHangDownStatus
  silent: 0
  tag: Event
  retentionTime: 10
  internal: true
  linkedDimensions: [NC_INFO]
  exclusion:
    ext: '"{{NC_INFO}}" == "None"'
  levels:
    - name: warning
      alertOnCall: 10
      expression: '{{is_ssh_ok}} == 1'
      action: ['batchRunTask(taskName=EndTimerProcessor,eventName=nc_hang,sync=True,gocFailurePush=True)']
      notify: [SlsAlertHandler]

nc_ssh_status_recovery_v2:
  interval: 64
  timeRange: 1300
  enable: true
  doc: '物理机SSH状态恢复'
  type: nc_cpu_event
  query: 'exceptionName: nc_hang_exception or exceptionName: nc_hang_uniform_check or exceptionName: nc_hang_too_long or exceptionName: xdragon_cn_hang_too_long and not instanceId: ""'
  analyse: select count(*) as error_cnt,ncIp as nc_ip,to_unixtime(now()) - to_unixtime(max(exceptionTime)) as delta_time,cluster_alias as cluster_name,max_by(exceptionName,__time__) as ext3 group by nc_ip,cluster_name HAVING delta_time >= 900 limit 2000
  logstore: 'monitor_exception_sls_alert'
  slsConfigName: xunjian_zhangbei
  powerSQL: true
  detailMetric: queryNcHangDownStatus
  silent: 0
  tag: Event
  retentionTime: 10
  internal: true
  linkedDimensions: [NC_INFO]
  exclusion:
    ext: '"{{NC_INFO}}" == "None"'
  levels:
    - name: warning
      alertOnCall: 10
      expression: '{{delta_time}} >= 900 and "{{ext3}}" != "xdragon_cn_hang_too_long"'
      action: ['batchRunTask(taskName=EndTimerProcessor,eventName=nc_hang,sync=True,gocFailurePush=True)']
      notify: [SlsAlertHandler]
      needRelatedVm: false
      persistent: false
    - name: low_warning
      alertOnCall: 10
      expression: '{{delta_time}} >= 900 and "{{ext3}}" == "xdragon_cn_hang_too_long"'
      action: ['batchRunTask(taskName=EndTimerProcessor,eventName=cn_hang,sync=True,gocFailurePush=True)']
      notify: [SlsAlertHandler]
      needRelatedVm: false
      persistent: false

vm_livemigrate_exception:
  interval: 65
  timeRange: 180
  enable: true
  doc: '热迁移后VM状态异常'
  type: vm_virt_event
  query: 'live_migrate_vm_cause_problem and not other_info: timeout and not other_info: tooLongTime and not other_info: vmNoFlow'
  analyse: select min(time) as exception_time,machine_id,count(*) as exception_count,regexp_extract(other_info, 'problem:(\w+),',1) as reason,regexp_extract(other_info, 'problem:(\w+),',1) as extension,regexp_extract(other_info, 'from nc:([\d\-]+)',1) as nc_id,regexp_extract(other_info, 'from nc:([\d-]+), to nc:([\d-]+)') as ext3 group by machine_id,other_info
  logstore: 'regionmaster_alarm'
  slsConfigName: ecs_monitor
  region: ['default']
  silent: 1000
  detailMetric: queryVmRunStatusChanged
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO,DRAGONBOX_CN_INFO,HOUYI_NC_INFO]
  retentionTime: 90
  tag: PerformanceImpact
  exclusion:
    ext: '"{{VM_INFO}}" == "None" or "{{VM_INFO.status}}" in ("Destroyed","destroying","Released","Releasing")'
  levels:
    - name: fatal
      expression: '{{exception_count}} >= 1 and "{{reason}}" in ("vmDead","vmDeadMayNotAutoProcess","VM_DEAD","VM_POST_DEAD") and not "{{VM_INFO.status}}" in ("Destroyed","destroying","Released","Releasing")'
      notify: [EventCenterAlertHandler,SlsAlertHandler]
      atDuty: [virtualization]
      action: [batchLinkedActiveOps,'batchRunTask(taskName=StartTimerProcessor,eventName=vm_crash,deltaTime=0,silent=900,slsPush=Ture)']
    - name: critical
      expression: '{{exception_count}} >= 1 and not "{{VM_INFO.status}}" in ("Destroyed","destroying","Released","Releasing")'
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=VsockPingCheckProcessor)']
  eventCenter:
    push: true
    opsCode: InstanceFailure
    impact: Reboot
    resourceStatus: NotApplicable
    category: VM.Availability
    eventSilent: 3558

critical_alert_for_1111:
  interval: 60
  timeRange: 80
  enable: false
  doc: '双十一集团关键监控通知'
  type: vm_cpu_event
  query: '(aliUid: 1647796581073291 or aliUid: 1108921452760297 or aliUid: 1008276465906671 or aliUid: 1317334647812936) and (exceptionName: nc_hang_exception  or exceptionName: vm_crash_event and warningLevel: warning or exceptionName: ag_nc_icmp_latency_increase or exceptionName: physical_machine_kernel_panic and fatal or exceptionName: vm_iohang_start or xdragon_cn_hang_too_long or xdragon_cn_hang_or_down or xdragon_cn_ping_loss_too_much or exceptionName: core_dump_generated and not low_warning or process_oom_exception and not low_warning and not normal or exceptionName: fpga_caterr or exceptionName: nc_down_alert) and not additionalInfo: active and not biz_status: nc_down and not biz_status: mlock'
  analyse: select 1 as exception,ncIp as nc_ip,cluster_alias as cluster_name,region as ext1,concat('info=',aoneProductName,':',buName) as ext2,additionalInfo as ext3,additionalInfo as extension,exceptionName as reason,instanceId as machine_id,min(exceptionTime) as exception_time group by nc_ip,cluster_name,ext1,ext2,ext3,extension,reason,machine_id HAVING cluster_name != 'AY10X' and ext3 != 'get_error_server_more_then_limit' limit 20000
  logstore: 'monitor_exception_sls_alert'
  slsConfigName: ecs_inspector
  powerSQL: true
  region: ['default']
  silent: 980
  tag: Event
  retentionTime: 1
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO,DRAGONBOX_CN_INFO]
  levels:
    - name: critical
      expression: '{{exception}} == 1 and "{{extension}}" != "sshd"'
      notify: [DingAlertHandler]
      persistent: false

nc_down_alert:
  interval: 20
  timeRange: 61
  enable: true
  doc: '对客VM宕机事件'
  type: vm_cpu_event
  query: 'exception_name: nc_down and end_time: ""'
  analyse: select case when max(ali_uid) is null then '' else cast(max(ali_uid) as varchar) end  as ali_uid,0 as down_end,************ as down_end_time,instance_id as machine_id,start_time as exception_time,nc_ip,cluster_name,to_unixtime(start_time) as down_timestamp group by machine_id,start_time,nc_ip,cluster_name HAVING machine_id not like 'BVT%' limit 100000
  logstore: event_start_end
  slsConfigName: new_regional_sls_config
  region: [default,eu-central-1]
  silent: 70
  detailMetric: queryVmRunStatusChanged
  linkedDimensions: [NC_INFO,VM_INFO,USER_INFO,VM_MAINTENANCE_INFO]
  retentionTime: 100
  tag: Unavailable
  linkedQueryList: [queryAllVmDownEndEvent()]
  transformParams: [reduceDuplicateMonitors]
  alarmId: test_all_down_get_in
  exclusion:
    ext: '("{{VM_INFO}}" == "None" and "AY" not in "{{machine_id}}" and "eci" not in "{{machine_id}}" and "i-" not in "{{machine_id}}" and "cp-" not in "{{machine_id}}" and "hbm-" not in "{{machine_id}}" and "acs-" not in "{{machine_id}}") or ("storagevm-" in "{{machine_id}}" or "netvm-" in "{{machine_id}}")'
  levels:
    - name: critical
      expression: '{{down_end}} == 0 and {{down_end_time}} > {{down_timestamp}} and "BVT" not in "{{machine_id}}"'
      action: ['batchLinkedActiveOps','batchLinkDrillEvent']
      notify: [EventCenterAlertHandler,SlsAlertHandler,FailureRecoverAlertHandler,FailureAlertHandler]
    - name: warning
      expression: '{{supplement}} == 1 and "BVT" not in "{{machine_id}}"'
      action: ['batchLinkedActiveOps','batchLinkDrillEvent']
      notify: [EventCenterAlertHandler,SlsAlertHandler,FailureRecoverAlertHandler,FailureAlertHandler]
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: NotApplicable
    category: VM.Availability
    closureFlag: start
    closureEvent: vm_down

gpu_fallen_event:
  interval: 279
  timeRange: 350
  enable: true
  doc: 'vm内部gpu出现掉卡故障'
  type: vm_gpu_available
  query: 'content: "GPU has fallen"'
  analyse: select 1 as gpu_unavailable, regexp_extract(vmname, '([ecihbmcpasvo]+-[\w]+)\.', 1) as machine_id, __source__ as nc_ip,  min(from_unixtime(__time__)) as exception_time, regexp_extract(content, '\(PCI:([\w\:\.]+)\)', 1) as ext3 group by machine_id, nc_ip, ext3 limit 20000
  logstore: console_log
  slsConfigName: ecs_inspector
  silent: 3580
  detailMetric: queryVmGpuUnAvailableLog
  linkedDimensions: [NC_INFO,VM_INFO,USER_INFO]
  retentionTime: 100
  tag: Unavailable
  levels:
    - name: fatal
      expression: '{{gpu_unavailable}} == 1'
      notify: [SlsAlertHandler]
      atDuty: [谢峰]

vm_operation_invalid_argument:
  interval: 150
  timeRange: 170
  enable: true
  doc: '虚拟机操作异常libvirt遇到非法参数'
  type: vm_virt_event
  query: 'Invalid argument and not class: qemuMonitorQueryVcpuThreadInfo and not detail: affinity and not class: qemuDomainGetBlockInfo and not class: httpserver and not class: qemuMonitorIO and not qemuMonitorSystemPowerdown'
  analyse: select 1 as error,machine_id,nc_ip,reason,min(time) as exception_time from (select class as reason,replace(regexp_extract(detail,'qemu\\(.*)\.scope',1),'x2di\x2d','i-') as machine_id,__source__ as nc_ip,time from log  HAVING machine_id is not null ) group by machine_id,nc_ip,reason limit 1000
  logstore: libvirt_log
  slsConfigName: ecs_inspector
  silent: 580
  detailMetric: queryLibvirtErrorLog
  linkedDimensions: [NC_INFO,VM_INFO,USER_INFO,DRAGONBOX_CN_INFO]
  tag: ControlFailed
  retentionTime: 13
  levels:
    - name: fatal
      expression: '{{error}} == 1'
      notify: [SlsAlertHandler]

physical_machine_cn_net_loss_for_user:
  interval: 50
  timeRange: 60
  enable: true
  doc: 神龙物理机CN非预期端到端不通（非5U)
  type: vm_network_sys
  query: 'ruleName: physical_machine_cn_net_loss'
  analyse: select 1 as net_loss,targetId as machine_id,"dimensions.nc" as nc_ip,from_unixtime(min(matchedTimestamp/1000)) as exception_time,case when position('fpga_error' IN max_by(features,__time__)) != 0 or position('xdragon_cn_kernel_panic' IN max_by(features,__time__)) != 0  then 1 else 0 end as panic group by machine_id,nc_ip HAVING exception_time is not null limit 1000
  logstore: xdc_rule_log
  slsConfigName: xunjian_zhangbei
  silent: 200
  region: [default]
  retentionTime: 3
  tag: Unavailable
  detailMetric: queryVmArpPingMetric
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO,DRAGONBOX_CN_INFO]
  levels:
    - name: critical
      expression: '{{net_loss}} == 1 and {{panic}} == 1'
      notify: [EventCenterAlertHandler]
      action: ['batchRunTask(taskName=FpgaIORegCheckProcessor)']
    - name: warning
      expression: '{{net_loss}} == 1 and {{panic}} == 0'
      notify: [EventCenterAlertHandler]
      action: ['batchRunTask(taskName=ScreenshotDiagnoseProcessor)','batchRunTask(taskName=OOBStatusCheckProcessor,CN_OOB=True)','batchRunTask(taskName=FpgaIORegCheckProcessor)']
  eventCenter:
    push: true
    opsCode: InstanceFailure
    impact: Alert
    resourceStatus: Warning
    category: VM.Availability
    eventSilent: 1800

live_migrate_cause_nc_hang:
  interval: 55
  timeRange: 900
  enable: true
  doc: VM热迁移导致NC夯机异常
  type: nc_cpu_event
  # 热迁移包括源NC和目标NC，夯机包括NC夯机和CN夯机，去除已知原因导致的热迁移
  query: '(exceptionName: live_migrate_vm_event or exceptionName: live_migrate_on_src_nc_event or exceptionName: live_migrate_on_dst_nc_event) or ((exceptionName: xdragon_cn_hang_too_long or exceptionName: nc_hang_too_long or exceptionName: nc_hang_exception or exceptionName: nc_hang_uniform_check or exceptionName: nc_hang_too_much or exceptionName: xdragon_cn_hang_or_down) and (fatal or critical or warning) and not extension: get_error_server_more_then_limit and not additionalInfo: readonly and not instanceId: "")'
  analyse: select 1 as cause_hang, 'unknown' as offline_reason, migrate.nc_ip, migrate.cluster_name,hang.exception_time as exception_time, migrate.ext3, array_union(migrate.exceptionList, hang.exceptionList) as exceptionList,to_unixtime(now()) as nowtimestamp,to_unixtime(migrate.exception_time) as migrate_timestamp from ( SELECT ncIp as nc_ip, cluster_alias as cluster_name, min(exceptionTime) as exception_time, array_agg(DISTINCT instanceId) as ext3, array_agg(DISTINCT exceptionName) as exceptionList FROM log where exceptionName in ('live_migrate_vm_event','live_migrate_on_src_nc_event','live_migrate_on_dst_nc_event') GROUP BY ncIp, cluster_alias limit 10000 ) as migrate inner join ( SELECT ncIp as nc_ip, cluster_alias as cluster_name, min(exceptionTime) as exception_time,  array_agg(DISTINCT instanceId) as ext3, array_agg(DISTINCT exceptionName) as exceptionList FROM log where exceptionName in ('xdragon_cn_hang_too_long','nc_hang_too_long','nc_hang_exception','nc_hang_uniform_check','nc_hang_too_much','xdragon_cn_hang_or_down') GROUP BY ncIp, cluster_alias limit 10000) as hang on migrate.nc_ip = hang.nc_ip and migrate.cluster_name = hang.cluster_name where migrate.exception_time < hang.exception_time having nowtimestamp - migrate_timestamp <= 300 limit 1000
  logstore: monitor_exception_sls_alert
  powerSQL: true
  slsConfigName: ecs_inspector
  linkedQueryList: [queryNcOfflineReason(nc_ip@or)]
  silent: 200
  region: [default]
  tag: PerformanceImpact
  retentionTime: 15
  linkedDimensions: [NC_INFO,HOUYI_NC_INFO]
  levels:
    - name: critical
      # 排除掉宕机和下线的节点, cause_hang 要写上，写对应 warningValue
      expression: '{{cause_hang}} == 1 and "{{HOUYI_NC_INFO.BizStatus}}" not in ("nc_down","offline")'
      notify: [SlsAlertHandler]

wild_vm_disk_retension:
  interval: 52
  timeRange: 100
  enable: true
  doc: 野VM问题导致磁盘残留
  type: vm_disk_event
  query: 'not instanceId: "" and not diskStatus: deleted and not computeNcIp: "" and not storageNcIp: ""'
  analyse: select 1 as error,instanceId as machine_id,fromException as reason,concat(computeNcIp,'|',storageNcIp) as ext3,concat(computeNcIp,'|',storageNcIp) as extension,computeNcIp as nc_ip group by machine_id,reason,ext3,nc_ip,storageNcIp HAVING nc_ip != storageNcIp limit 1000
  logstore: wild_disk_meta_info
  slsConfigName: xunjian_zhangbei
  silent: 100
  region: [default]
  retentionTime: 12
  tag: ControlFailed
  linkedDimensions: [NC_INFO,VM_INFO,USER_INFO,DRAGONBOX_CN_INFO]
  levels:
    - name: critical
      expression: '{{error}} == 1'
      notify: [SlsAlertHandler]

recover_preemption_disk_event:
  interval: 12
  timeRange: 50
  enable: true
  doc: 抢占盘迁移VM事件
  type: vm_cpu_event
  query: '*'
  analyse: SELECT DISTINCT vmName as machine_id, min(recoverTime) as exception_time, ncIp as nc_ip,1 as error_cnt group by machine_id,nc_ip limit 10000
  logstore: recover_preempt_log
  slsConfigName: xunjian_zhangbei
  silent: 0
  retentionTime: 13
  region: [ default ]
  tag: Event
  detailMetric: queryVmRunStatusChanged
  linkedDimensions:
    - NC_INFO
    - HOUYI_NC_INFO
    - VM_INFO
    - USER_INFO
  levels:
    - name: critical
      expression: '{{error_cnt}} > 0 and "{{VM_INFO.status}}" in ("Running","Starting","Migrating")'
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=nc_down,deltaTime=0,silent=900,slsPush=True,skipKwArgs=1,sync=Ture)']
      atDuty: [ nc ]
    - name: low_warning
      expression: '{{error_cnt}} > 0 and "{{VM_INFO.status}}" != "Running"'
      notify: [SlsAlertHandler]
      atDuty: [ nc ]

recover_vm_fail_event:
  interval: 200
  timeRange: 300
  enable: true
  doc: 实例迁移失败
  type: vm_cpu_event
  query: 'recover_vm and -1201027 and __raw_log__: recover_vm'
  analyse: select regexp_extract(__raw_log__, 'vm_name=([^"]+)',1) as machine_id,__raw_log__, 1 as cnt from log where regexp_extract(__raw_log__, 'vm_name=([^"]+)',1) is not null
  logstore: cloudops_http_service
  slsConfigName: ecs_inspector
  silent: 0
  retentionTime: 13
  region: [ default ]
  tag: Event
  detailMetric: queryVmRunStatusChanged
  linkedDimensions:
    - VM_INFO
    - USER_INFO
  levels:
    - name: critical
      expression: '{{cnt}} > 0 '
      notify: [SlsAlertHandler]
      atDuty: [ nc ]

vm_unexpected_killed_by_other:
  interval: 50
  timeRange: 57
  enable: true
  doc: 虚拟机被Kill
  type: vm_cpu_event
  query: qemuProcessKill and ("flags=2" or CNforVM0 or CNforVM1 or CNforVM)
  analyse: select 0 as clean_cnforvm,machine_id,pid,exception_time,nc_ip,case when machine_id = 'CNforVM1' then '*************|' when machine_id in ('CNforVM0','CNforVM') then '*************|' else '' end as extension,case when machine_id like 'CNforVM%' then machine_id else 'MOCK' end as ext3 from (select DISTINCT 'MOCK' as ext3,regexp_extract(detail,'vm=([\w\-]+)',1) as machine_id,regexp_extract(detail,'pid=(\d+)',1) as pid,time as exception_time,__source__ as nc_ip from log HAVING  machine_id not like 'iso-%') group by machine_id,pid,exception_time,nc_ip,ext3,extension limit 10000
  logstore: libvirt_log
  slsConfigName: ecs_inspector
  silent: 0
  linkedQueryList: [queryProcessKillLog(),queryNcTransformKillEvent()]
  retentionTime: 20
  tag: Unavailable
  detailMetric: queryVmRunStatusChanged
  linkedDimensions: [NC_INFO,VM_INFO,USER_INFO,DRAGONBOX_CN_INFO,HOUYI_NC_INFO]
  exclusion:
    ext: '"{{HOUYI_NC_INFO}}" == "None" or {{HOUYI_NC_INFO.isOffline}} or {{HOUYI_NC_INFO.isEmptyNc}}'
  levels:
    - name: critical
      expression: '"{{pid}}" != "0" and "{{ext3}}" not in ("libvirtd","MOCK") and "{{machine_id}}" not in ("CNforVM","CNforVM1","CNforVM2")'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=vm_crash,deltaTime=0,silent=900,slsPush=True)']
    - name: low_warning
      expression: '"{{pid}}" != "0" and "{{machine_id}}" in ("CNforVM","CNforVM1","CNforVM2") and {{clean_cnforvm}} == 0'
      notify: [SlsAlertHandler]
    - name: normal
      expression: '"{{pid}}" != "0" and "{{machine_id}}" in ("CNforVM","CNforVM1","CNforVM2") and {{clean_cnforvm}} == 1'
      notify: [SlsAlertHandler]
  eventCenter:
    push: true
    opsCode: InstanceFailure
    impact: Reboot
    resourceStatus: NotApplicable
    category: VM.Availability
    eventSilent: 3558

dpdk_avs_util_fatal_15min_notify:
  interval: 60
  timeRange: 930
  enable: true
  doc: 'DPDK利用率过高15min告警'
  type: nc_vpc_sys
  query: 'exceptionName:dpdk_avs_util_high and warningLevel:fatal and isPhysicalMachine:False and isLocalDisk:False and warningValue>=0.9'
  analyse: select count(DISTINCT exception_time) as alertTimes,machine_id,max(exception_time) as exception_time from (select count(DISTINCT aliUid) as userCount,ncIp as machine_id,exceptionTime as exception_time from log group by ncIp,exceptionTime having userCount >=2) group by machine_id having alertTimes>=10 limit 1000
  logstore: monitor_exception_sls_alert
  slsConfigName: xunjian_zhangbei
  powerSQL: true
  region: [default]
  retentionTime: 10
  tag: PerformanceImpact
  linkedDimensions: [NC_INFO]
  internal: true
  silent: 0
  levels:
    - name: fatal
      expression: '{{alertTimes}} >= 15'
      notify: [PhoneCallAlertHandler]
      atDuty: [至成]
      persistent: false

nic_downtime_large_impacted:
  interval: 58
  timeRange: 65
  enable: true
  doc: '单网卡down时间长, nc流量达到极限'
  type: nc_network_event
  query: 'downtime_max >= 2'
  analyse: select 0 as traffic_too_high,nc_ip,exception_time,net_dev as extension,downtime_max,downtime_max as ext3 from log limit 10000
  logstore: nic_downtime
  slsConfigName: xunjian_zhangbei
  region: [default]
  retentionTime: 7
  detailMetric: queryNcFlow
  tag: PerformanceImpact
  linkedQueryList: [queryNcTrafficTooHigh(nc_ip@or)]
  linkedDimensions: [NC_INFO]
  internal: false
  levels:
    - name: warning
      expression: '{{downtime_max}} >= 3 and {{traffic_too_high}} == 1'
      notify: [SlsAlertHandler]

virt_report_nc_exception:
  interval: 360
  timeRange: 360
  enable: true
  doc: '虚拟化主动上报NC异常'
  type: nc_cpu_event
  query: ''
  analyse: ''
  logstore: ''
  slsConfigName: xunjian_zhangbei
  silent: 580
  retentionTime: 13
  tag: Unavailable
  linkedDimensions: [ NC_INFO,VM_INFO ]
  detailMetric: queryReportInfo
  transformParams: [ transformMachineIdToInstance ]
  levels:
    - name: fatal
      expression: '"{{nc_ip}}" != "" and 1 == 1'
      action: ['batchRunTask(taskName=StaragentCheckProcessor,callback=False)']
      notify: [SlsAlertHandler]
    - name: critical
      expression: '"{{nc_ip}}" != "" and "{{extension}}" != "" and "{{extension}}" == "1"'
      notify: [ SlsAlertHandler ]

virt_report_exception_ping_failed:
  interval: 360
  timeRange: 360
  enable: true
  doc: '虚拟化主动上报CN异常后ping不通'
  type: nc_cpu_event
  query: ''
  analyse: ''
  logstore: ''
  slsConfigName: xunjian_zhangbei
  silent: 580
  retentionTime: 13
  tag: Unavailable
  linkedDimensions: [ NC_INFO,HOUYI_NC_INFO ]
  exclusion:
    ext: '"{{NC_INFO}}" == "None" or "{{NC_INFO.state}}" in ("wait_offline","wait_online") or {{NC_INFO.security_level}} == 2 or {{NC_INFO.cluster_alias}} in ("AY03A" ,"AY03B","AY03C","AY03D","AY03E" ,"AY03G" ,"AY03H","AY03I")'
  levels:
    - name: fatal
      expression: '"{{extension}}" != "" and "{{value}}" > 90'
      notify: [ SlsAlertHandler ]

vmcore_nc_kernel_osissue:
  interval: 57
  timeRange: 60
  enable: true
  internal: true
  doc: 'change诊断根因为nc_kernel'
  type: nc_controller_event
  query: 'rootCause and nc_kernel and not result: split_lock_exception and not result: system_crash and not result: cn_split_lock_exception and not kernel_fault_with_detail and 1v8fM7RF7ICLPcya'
  analyse: select nc_ip,machine_id,reason,ext3,EXTRA_DATA as extension,EXTRA_DATA,substr(max(exception_time),1,19) as exception_time,count(*) as cnt from( select json_extract_scalar(result, '$.data.overviewInfo.ncIp') as nc_ip, json_extract_scalar(result, '$.data.overviewInfo.machineId') as machine_id, json_extract_scalar(result, '$.data.rootCause.message') as reason, json_extract_scalar(result, '$.data.rootCause.additionalInfo') as ext3, time as exception_time, concat('featureName=',json_extract_scalar(result, '$.data.rootCause.featureName'),';exceptionName=',json_extract_scalar(result, '$.data.rootCause.exceptionName'),';exceptionTime=',json_extract_scalar(result, '$.data.rootCause.exceptionTime'),';auditParamStr=',json_extract_scalar(request, '$.auditParamStr')) as EXTRA_DATA from log) group by nc_ip,machine_id,reason,ext3,EXTRA_DATA having nc_ip is not null limit 1000
  logstore: ecs_alarm_req_result_log
  slsConfigName: ecs_inspector
  region: [default]
  tag: PerformanceImpact
  silent: 40
  retentionTime: 7
  linkedDimensions: [NC_INFO,VM_INFO,DRAGONBOX_CN_INFO]
  levels:
    - name: warning
      expression: '{{cnt}} >= 1'
      notify: [SlsAlertHandler]
      action: [ 'batchRunTask(taskName=VmcoreOsIssueReportProcessor)' ]
      persistent: false

inspect_migration_vm_hang_renew:
  interval: 60
  timeRange: 600
  enable: true
  doc: '疑似热迁移完成后VM夯机恢复'
  type: vm_cpu_event
  query: 'ruleName: inspect_live_migration_cause_vm_hang and actions: 0x30000000001004'
  analyse: select DISTINCT 1 as exception_count, targetId as machine_id, 0 as nrvmexit
  logstore: xdc_rule_log
  slsConfigName: xunjian_zhangbei
  linkedDimensions: [VM_INFO,NC_INFO,USER_INFO]
  silent: 3580
  tag: PerformanceImpact
  linkedQueryList: [queryVmExitInfo(machine_id@or)]
  levels:
    - name: critical
      expression: '{{exception_count}} >= 1 and {{nrvmexit}} > 0'
      action: [ 'batchRunTask(taskName=CancelOpsProcessor,force=True,cancelVmOps=True,opsCodes="0x30000000001004")' ]
      notify: [ SlsAlertHandler ]
      atDuty: [ 月悬 ]
      hasVmThenAlert: true
      filterChains: [GammaEnvFilter()]

vip_panic_too_many_times:
  interval: 61
  timeRange: 1000
  enable: true
  doc: '重点客户虚拟机反复panic'
  type: vm_cpu_available
  query: 'isLocalDisk: False and supportAutoRecovery: True and (exceptionName: guest_os_kernel_panic and (fatal or warning) or exceptionName: vm_panic_event) and (gcLevel: gc6 or gcLevel: gc7 or aliUid: 1950532694534906 or aliUid: 1075639319543162 or aliUid: 1599381103179735)'
  analyse: select instanceId as machine_id,count(DISTINCT exceptionTime) as cnt,ncIp as nc_ip,cluster_alias group by machine_id,nc_ip,cluster_alias HAVING cnt >= 2
  logstore: monitor_exception_sls_alert
  slsConfigName: xunjian_zhangbei
  linkedDimensions: [VM_INFO,NC_INFO,USER_INFO]
  silent: 260
  tag: Unavailable
  exclusion:
    ext: '"{{VM_INFO}}" == "None" or "{{VM_INFO.status}}" in ("Destroyed","destroying","Released","Releasing")'
  levels:
    - name: critical
      expression: '{{cnt}} >= 2'
      notify: [ SlsAlertHandler ]

vm_on_multi_ncs:
  interval: 66
  timeRange: 310
  enable: true
  doc: VM同时存在于多个NC上
  type: vm_virt_event
  query: '(process: qemu-kvm or process: dragonfly or process: qemu-dm or process: iohub-ctrl) and not CNforVM and not CNforVM0 and not CNforVM1 and not ZOCforVM and not sysvm and not vmoc_for_vswitch'
  analyse: select 0 as hang_down_error,machine_id,count(DISTINCT nc_ip) as nc_cnt,max(nc_ip) as nc_ip,min(nc_ip) as ext3,concat(max(nc_ip),'|',min(nc_ip)) as extension, count(*) * 1.0 / count(DISTINCT process) as data_cnt from (select split_part(replace(whole_process,'iso-',''),'.',1) as machine_id,__source__ as nc_ip,timestamp,process from log) group by machine_id HAVING regexp_like(machine_id,'([eciashbmp]{1,3}\-\w{9,25})') and nc_cnt >= 2 and data_cnt >= 8  limit 10000
  logstore: nc_vm_process_short_info
  slsConfigName: ecs_monitor
  silent: 460
  retentionTime: 18
  linkedQueryList:
    - queryIOHangVmDownLog()
  detailMetric: querySpecificWildVm
  linkedDimensions:
    - NC_INFO
    - VM_INFO
    - USER_INFO
    - DRAGONBOX_CN_INFO
  exclusion:
    ext: '"{{VM_INFO}}" == "None" or "{{VM_INFO.status}}" in ("Destroyed","destroying","Released","Releasing")'
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Maintaining
    category: VM.Availability
  levels:
    - name: critical
      expression: '{{nc_cnt}} >= 2 and {{data_cnt}} >= 8 and not "{{machine_id}}".startswith("BVT") and {{hang_down_error}} == 1'
      notify: ["EventCenterAlertHandler", "SlsAlertHandler"]
      hasVmThenAlert: false
      needRelatedVm: true
    - name: low_warning
      expression: '{{nc_cnt}} >= 2 and {{data_cnt}} >= 8 and not "{{machine_id}}".startswith("BVT") '
      notify: ["EventCenterAlertHandler", "SlsAlertHandler"]
      hasVmThenAlert: false
      needRelatedVm: true
  tag: ControlFailed
  splitTask: true
  maxTraceLog: 10000
  powerSQL: false

xhs_emr_redis_nc_tag:
  interval: 20
  timeRange: 65
  enable: true
  doc: 小红书redis/EMR实例标记
  type: nc_controller_event
  query: '* and aliyunIdkp: 1781574661016173 and dryRun: false and (instanceType: "ecs.r8a.16xlarge" or instanceType: "ecs.r8a.32xlarge" or instanceType: "ecs.r8a.2xlarge") and locationNo: cn-shanghai-m-AY and resultCode: 200'
  analyse: select ncId as nc_list, instanceType as ext3 limit 10000
  logstore: schedule_trace
  slsConfigName: ecs_monitor
  region: [default]
  silent: 460
  retentionTime: 18
  linkedDimensions: [ NC_INFO,HOUYI_NC_INFO ]
  levels:
    - name: critical
      expression: '"{{ext3}}" == "ecs.r8a.2xlarge"'
      action: [ 'batchRunTask(taskName=NcScheduleFactorProcessor)' ]
      notify: [ "SlsAlertHandler" ]
    - name: warning
      expression: '"{{ext3}}" in ("ecs.r8a.16xlarge","ecs.r8a.32xlarge")'
      action: [ 'batchRunTask(taskName=NcScheduleFactorProcessor)' ]
      notify: ["SlsAlertHandler"]
  tag: Event
  splitTask: true
  maxTraceLog: 10000
  powerSQL: false

ddh_empty_nc_down_event:
  interval: 58
  timeRange: 90
  enable: true
  doc: 'DDH空NC发生宕机'
  type: nc_cpu_event
  query: '(0x400018 or 0x40001000008001 or 0x4000100000240 or actions: 0x4000300000021) and dimensions.vcpuCount: 0 and not dimensions.ddhId: "" and not local_disk_nc_down_active'
  analyse: select 1 as error,targetId as nc_ip,min(from_unixtime(matchedTimestamp/1000)) as exception_time,ruleName as reason,"dimensions.ddhId" as ext3,"dimensions.cn" as cnSn group by nc_ip,reason,ext3,cnSn limit 10
  logstore: 'xdc_rule_log'
  slsConfigName: xunjian_zhangbei
  silent: 480
  retentionTime: 20
  linkedDimensions: [NC_INFO,DRAGONBOX_CN_INFO,HOUYI_NC_INFO]
  tag: Unavailable
  exclusion:
    ext: '{{HOUYI_NC_INFO.supportDDHMigration}}'
  levels:
    - name: critical
      expression: '{{error}} == 1 and {{HOUYI_NC_INFO.isDedicatedHost}}'
      notify: [SlsAlertHandler]

vm_vport_lost:
  interval: 29
  timeRange: 60
  enable: true
  doc: '虚拟机vport丢失'
  type: vm_virt_event
  query: 'not lost_netdevs:""'
  analyse: select '["other"]' as statuses,0 as libvirt_upgrade,1 as vport_lost,ip as report_nc_ip,time as exception_time,instance_id as machine_id,lost_netdevs as ext3,if(netdevs is null or netdevs='',1,0) as all_lost,concat(ip,'|') as extension from log where not regexp_like(lost_netdevs,'net_vhost.*|sockcn|test_vif.*|lacp.*') and lower(lost_netdevs)=lost_netdevs limit 3000
  logstore: check_netdev_lost
  slsConfigName: ecs_xunjian2
  silent: 120
  retentionTime: 13
  powerSQL: true
  linkedQueryList: [queryVmHasMigrated(),queryLibvirtUpgradeEvent()]
  linkedDimensions: [NC_INFO,HOUYI_NC_INFO,VM_INFO,USER_INFO]
  tag: Unavailable
  exclusion:
    ext: '"{{VM_INFO}}" == "None" or "{{VM_INFO.status}}" in ("Destroyed","destroying","Released","Releasing","Shutted","Shutting") or {{VM_INFO.getDeltaTimeForStart}} < 300 or {{libvirt_upgrade}} == 1'
  levels:
    - name: fatal
      expression: '{{vport_lost}} == 1 and {{all_lost}} == 1 and "Migrating" not in {{statuses}} and "{{HOUYI_NC_INFO.NcIp}}" == "{{report_nc_ip}}"'
      notify: [SlsAlertHandler]
    - name: warning
      expression: '{{vport_lost}} == 1 and "{{HOUYI_NC_INFO.NcIp}}" != "{{report_nc_ip}}"'
      notify: [ SlsAlertHandler ]
    - name: low_warning
      expression: '{{vport_lost}} == 1 and "{{HOUYI_NC_INFO.NcIp}}" == "{{report_nc_ip}}"'
      notify: [SlsAlertHandler]

vm_vport_lost_too_long:
  interval: 57
  timeRange: 700
  enable: true
  doc: '虚拟机vport长时间丢失'
  type: vm_virt_event
  query: 'exceptionName:vm_vport_lost and warningLevel:fatal'
  analyse: select ncIp as nc_ip,instanceId as machine_id,max(exceptionTime) as exception_time,min(exceptionTime) as exception_time_min,date_diff('second', date_parse(min(exceptionTime), '%Y-%m-%d %H:%i:%S'), date_parse(max(exceptionTime), '%Y-%m-%d %H:%i:%S')) as delta_time group by nc_ip,machine_id having date_diff('second', date_parse(exception_time, '%Y-%m-%d %H:%i:%S'), current_timestamp) < 100 and delta_time >= 600 limit 3000
  logstore: monitor_exception_sls_alert
  slsConfigName: xunjian_zhangbei
  region: [default]
  silent: 780
  retentionTime: 13
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO]
  tag: Unavailable
  levels:
    - name: fatal
      expression: '{{delta_time}} >= 600'
      notify: [SlsAlertHandler]

vm_vport_lost_too_long2:
  interval: 57
  timeRange: 1900
  enable: true
  doc: '虚拟机vport丢失30min'
  type: vm_virt_event
  query: 'exceptionName:vm_vport_lost and warningLevel:fatal'
  analyse: select ncIp as nc_ip,instanceId as machine_id,max(exceptionTime) as exception_time,min(exceptionTime) as exception_time_min,date_diff('second', date_parse(min(exceptionTime), '%Y-%m-%d %H:%i:%S'), date_parse(max(exceptionTime), '%Y-%m-%d %H:%i:%S')) as delta_time group by nc_ip,machine_id having date_diff('second', date_parse(exception_time, '%Y-%m-%d %H:%i:%S'), current_timestamp) < 100 and delta_time >= 1800 limit 3000
  logstore: monitor_exception_sls_alert
  slsConfigName: xunjian_zhangbei
  region: [default]
  silent: 780
  retentionTime: 13
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO]
  tag: Unavailable
  levels:
    - name: fatal
      expression: '{{delta_time}} >= 1800'
      notify: [SlsAlertHandler]
      persistent: false

vm_vport_lost_recovery:
  interval: 57
  timeRange: 1000
  enable: true
  doc: '虚拟机vport丢失recovery'
  type: vm_virt_event
  query: 'exceptionName:vm_vport_lost'
  analyse: select 1 as recovered,instanceId as machine_id,ncIp as nc_ip,warningLevel as exception_level,max(exceptionTime) as last_time, from_unixtime(max(__time__) + 60) as exception_time group by machine_id,nc_ip,exception_level HAVING to_unixtime(now()) - to_unixtime(last_time) >= 900 limit 10000
  logstore: monitor_exception_sls_alert
  slsConfigName: xunjian_zhangbei
  region: [default]
  internal: true
  powerSQL: true
  tag: Event
  linkedDimensions: [NC_INFO,VM_INFO,USER_INFO]
  silent: 780
  retentionTime: 2
  levels:
    - name: low_warning
      expression: '{{recovered}} == 1 and "{{exception_level}}" == "fatal"'
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=CancelOpsProcessor,force=True,cancelVmOps=True,opsCodes="0x30000000001004")']
      persistent: false
    - name: normal
      expression: '{{recovered}} == 1'
      notify: [SlsAlertHandler]
      persistent: false

inspect_fpga_err_iohang_by_pmd:
  interval: 57
  timeRange: 130
  enable: true
  doc: '疑似FPGA问题导致iohang'
  type: vm_virtStorage_event
  query: 'stage: NOTIFY and alert.alert_name: FPGA_IOHANG'
  analyse: select 1 as fpga_iohang,regexp_extract("notify_history.content",'(\d+\.\d+\.\d+\.\d+)',1) as nc_ip,regexp_extract("notify_history.content",'devid\((\d+\-\d+)\)',1) as ext3,case when min(regexp_extract("notify_history.content",'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) is not null then min(regexp_extract("notify_history.content",'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) else min(time_str) end  as exception_time group by nc_ip,ext3 HAVING  nc_ip is not null limit 1000
  logstore: ebs_alert_center_internal
  slsConfigName: ecs_storage_monitor
  region: [default_1]
  silent: 850
  retentionTime: 20
  detailMetric: queryStorageIOLatency
  transformParams: [transformExt3ToDiskId]
  linkedDimensions: [NC_INFO,VM_INFO,USER_INFO]
  tag: Unavailable
  levels:
    - name: fatal
      expression: '{{fpga_iohang}} == 1'
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=CheckHealthProcessor,checkItem="Vm Service Check")']

inspect_vm_ntp_server_error:
  interval: 81
  timeRange: 157
  enable: true
  doc: '虚拟机ntp server异常'
  type: vm_resource_event
  query: 'exceptionName:vm_detect_root_cause_by_change'
  analyse: select 0 as is_ntp_error, ncIp as nc_ip, machine_id as machine_id, region as region_id, max(exceptionTime) as exception_time from log group by nc_ip, machine_id, region_id limit 1000
  logstore: 'monitor_exception_sls_alert'
  slsConfigName: ecs_inspector
  region: [default]
  silent: 780
  linkedQueryList: [queryNtpError(region_id@or)]
  tag: Unavailable
  retentionTime: 13
  linkedDimensions: [NC_INFO,VM_INFO]
  levels:
    - name: fatal
      expression: '{{is_ntp_error}} == 1'
      notify: [SlsAlertHandler]

vm_hang_event_not_avoid_too_long:
  interval: 75
  timeRange: 45200
  enable: true
  doc: 虚拟机夯机事件长时间未规避
  type: vm_cpu_event
  query: 'exceptionName:key_customer_event_generate and additionalInfo: "InstanceFailure.Reboot" and (additionalInfo: Avoided or additionalInfo: Scheduled or additionalInfo: Executed or additionalInfo:Canceled) and "cloudops.vm.maintenance"'
  analyse: select 0 as heartbeat,instanceId as machine_id,sum(tag) as sum_tag,ncIp as nc_ip,max(err_time) as exception_time from (select instanceId,ncIp,max(exceptionTime) as err_time ,case when additionalInfo = 'InstanceFailure.Reboot:Scheduled' then 1 when additionalInfo in ('InstanceFailure.Reboot:Avoided','InstanceFailure.Reboot:Executed','InstanceFailure.Reboot:Canceled') then -1 else 0 end as tag from log group by instanceId,ncIp ,additionalInfo) group by instanceId,ncIp HAVING sum_tag > 0 limit 10000
  logstore: monitor_exception_sls_alert
  slsConfigName: ecs_inspector
  silent: 780
  region: [default]
  retentionTime: 10
  linkedQueryList:
    - queryLastVMHeartbeat(machine_id@or)
  detailMetric: ''
  linkedDimensions:
    - NC_INFO
    - VM_INFO
    - USER_INFO
  eventCenter:
    push: true
    opsCode: InstanceFailure
    impact: Alert
    resourceStatus: Warning
    category: VM.Availability
  levels:
    - name: critical
      expression: '{{sum_tag}} > 0 and "{{VM_INFO.status}}" != "Shutted" and {{heartbeat}} == 0'
      action:
        - batchRunTask(taskName=CheckHealthProcessor,checkItem="Vm Service Check")
      notify: ["SlsAlertHandler"]
      hasVmThenAlert: false
      needRelatedVm: true
    - name: normal
      expression: '{{sum_tag}} > 0 and ("{{VM_INFO.status}}" in ("Shutted","Destroyed","destroying","Released","Releasing") or {{heartbeat}} == 1)'
      notify: ["SlsAlertHandler", "EventCenterAlertHandler"]
      hasVmThenAlert: false
      needRelatedVm: true
  tag: Unavailable
  splitTask: true
  maxTraceLog: 10000
  powerSQL: true

spoold_inspector_io_hang_check:
  interval: 45
  timeRange: 80
  enable: true
  doc: "spool出现hang-io"
  type: nc_disk_event
  query: "(arch: i2 or arch: i4 or arch: d3c or arch: d3s) and io_hang_cnt > 0"
  analyse: SELECT COUNT(1) as exception_cnt, from_unixtime(min(__time__)) as exception_time, ip as nc_ip, max(io_hang_cnt) as io_hang_cnt, arch GROUP by nc_ip, arch limit 1000
  logstore: spoold_inspector_new
  slsConfigName: ecs_xunjian2
  silent: 43180
  retentionTime: 13
  linkedDimensions: [NC_INFO]
  tag: PerformanceImpact
  levels:
    - name: warning
      expression: "{{io_hang_cnt}} >= 1"
      notify: [SlsAlertHandler]

spoold_inspector_io_error_check:
  interval: 45
  timeRange: 80
  enable: true
  doc: "spool出现error-io"
  type: nc_disk_event
  query: "(arch: i2 or arch: i4 or arch: d3c or arch: d3s) and io_error_cnt > 0"
  analyse: SELECT COUNT(1) as exception_cnt, from_unixtime(min(__time__)) as exception_time, ip as nc_ip, max(io_error_cnt) as io_error_cnt, arch GROUP by nc_ip, arch limit 1000
  logstore: spoold_inspector_new
  slsConfigName: ecs_xunjian2
  silent: 43180
  retentionTime: 13
  linkedDimensions: [NC_INFO]
  tag: PerformanceImpact
  levels:
    - name: warning
      expression: "{{io_error_cnt}} >= 1"
      notify: [SlsAlertHandler]

spoold_inspector_nvme_linkdown_check:
  interval: 10
  timeRange: 80
  enable: true
  doc: "spool检测到CN nvme掉盘"
  type: nc_disk_event
  query: "(arch: i4 or arch: d3c) and nvme_linkdown_cnt > 0"
  analyse: SELECT COUNT(1) as exception_cnt, from_unixtime(min(__time__)) as exception_time, ip as nc_ip, max(nvme_linkdown_cnt) as nvme_linkdown_cnt, arch GROUP by nc_ip, arch limit 1000
  logstore: spoold_inspector_new
  slsConfigName: ecs_xunjian2
  silent: 43180
  retentionTime: 13
  linkedDimensions: [NC_INFO]
  tag: PerformanceImpact
  levels:
    - name: critical
      expression: "{{nvme_linkdown_cnt}} >= 1"
      notify: [SlsAlertHandler]

vm_retry_after_dpdkavs_restart:
  interval: 600
  timeRange: 1800
  enable: true
  doc: DPDKAVS重启后VM重传指标趋势异常
  type: vm_network_sys
  query: '(exceptionName: avs_hotup_vport_downtime or exceptionName: key_process_restart and additionalInfo : dpdkavs) or (exceptionName: vm_performance_metric_anomaly and (additionalInfo: "VmNetworkRetryMetric" or additionalInfo: "VmPpsBps/tx_pps" or additionalInfo: "VmPpsBps/rx_pps"))'
  analyse: select t4.ncIp, machine_id , t4.exceptionTime as exception_time, additionalInfo as ext3, case when retrans is null then 100 else retrans end as retrans, case when hw_retrans is null or retrans is null or retrans <= 0 then 0 else hw_retrans * 1.0 / retrans end as hw_retrans_ratio, case when ppsReason is not null then 1 else 0 end as hasPpsAnomaly, ppsReason from (select distinct exceptionTime, ncIp from log where exceptionName = 'key_process_restart' or exceptionName = 'avs_hotup_vport_downtime') as t1 join (select distinct t2.exceptionTime, t2.ncIp, t2.machine_id, t2.additionalInfo, t2.retrans, t2.hw_retrans, ppsReason from (select exceptionTime, ncIp, machine_id, additionalInfo, try_cast(regexp_extract(additionalInfo, 'x_retrans=(\d+(\.\d+)?)', 1) as double) as retrans, try_cast(regexp_extract(additionalInfo, 'hw_retrans=(\d+(\.\d+)?)', 1) as double) as hw_retrans, case when additionalInfo like 'metric=VmNetworkRetryMetric/tx_retry%' then 'tx' else 'rx' end as retryWay from log where additionalInfo like 'metric=VmNetworkRetryMetric%') as t2 left join (select machine_id, case when additionalInfo like 'metric=VmPpsBps/tx_pps%' then 'tx' else 'rx' end as ppsWay, reason as ppsReason, exceptionTime from log where additionalInfo like 'metric=VmPpsBps%') as t3 on t2.machine_id = t3.machine_id and t2.retryWay = t3.ppsWay and date_trunc('minute', t3.exceptionTime) between date_add('minute', -1, t2.exceptionTime) and t2.exceptionTime) as t4 on t1.ncIp = t4.ncIp and date_trunc('minute', t1.exceptionTime) between date_add('minute', -10, t4.exceptionTime) and t4.exceptionTime limit 10000
  logstore: monitor_exception_sls_alert
  slsConfigName: ecs_inspector
  silent: 300
  region: [default]
  retentionTime: 8
  linkedDimensions:
    - VM_INFO
    - NC_INFO
    - USER_INFO
  levels:
    - name: fatal
      expression: '{{retrans}} >= 10 and {{hw_retrans_ratio}} < 0.9 and {{hasPpsAnomaly}} == 1 and "{{ppsReason}}" == "dip"'
      notify: [ "SlsAlertHandler" ]
    - name: critical
      expression: '{{retrans}} >= 10 and {{hw_retrans_ratio}} < 0.9 and {{hasPpsAnomaly}} == 0'
      notify: [ "SlsAlertHandler" ]
    - name: warning
      expression: '{{retrans}} >= 0'
      notify: [ "SlsAlertHandler" ]
  tag: PerformanceImpact
  splitTask: true
  detailMetric: queryXdragonMetricOps

vm_arp_drop_after_dpdkavs_restart:
  interval: 600
  timeRange: 1800
  enable: true
  doc: 'DPDKAVS重启后VM ARP丢包率趋势异常'
  type: vm_network_sys
  query: '(exceptionName: avs_hotup_vport_downtime or exceptionName: key_process_restart and additionalInfo : dpdkavs) or (exceptionName: vm_performance_metric_anomaly and additionalInfo : "VmArpPingMetric/arp_drop_ratio")'
  analyse: select 1 as metric_anomaly, t2.ncIp, machine_id , t2.exceptionTime as exception_time, additionalInfo as ext3 from (select exceptionTime, ncIp from log where exceptionName = 'key_process_restart' or exceptionName = 'avs_hotup_vport_downtime') as t1 join (select exceptionTime, ncIp, machine_id, additionalInfo, try_cast(regexp_extract(additionalInfo, 'value=(\d+(\.\d+)?)', 1) as double) as arp_drop_ratio from log where exceptionName = 'vm_performance_metric_anomaly' having arp_drop_ratio is null or (arp_drop_ratio < 1 and arp_drop_ratio >= 0.3)) as t2 on t1.ncIp = t2.ncIp and date_trunc('minute', t1.exceptionTime) between date_add('minute', -10, t2.exceptionTime) and t2.exceptionTime limit 10000
  logstore: monitor_exception_sls_alert
  slsConfigName: ecs_inspector
  silent: 300
  region: [default]
  retentionTime: 13
  linkedDimensions:
    - VM_INFO
    - NC_INFO
    - USER_INFO
  levels:
    - name: critical
      expression: '{{metric_anomaly}} == 1'
      notify: [ "SlsAlertHandler" ]
  tag: PerformanceImpact
  splitTask: false
  detailMetric: queryXdragonMetricOps

vm_hw_ratio_drop_after_dpdkavs_restart:
  interval: 600
  timeRange: 1800
  enable: true
  doc: 'DPDKAVS重启后VM硬转比下跌异常'
  type: vm_network_sys
  query: '(exceptionName: avs_hotup_vport_downtime or exceptionName: key_process_restart and additionalInfo : dpdkavs) or (exceptionName: vm_performance_metric_anomaly and additionalInfo: "VmPpsBps/hw_pps_ratio")'
  analyse: select 1 as metric_anomaly, t2.ncIp, machine_id , t2.exceptionTime as exception_time, additionalInfo as ext3, pps from (select exceptionTime, ncIp from log where exceptionName = 'key_process_restart' or exceptionName = 'avs_hotup_vport_downtime') as t1 join (select exceptionTime, ncIp, machine_id, additionalInfo, try_cast(regexp_extract(additionalInfo, 'pps=(\d+(\.\d+)?)', 1) as double) as pps from log where exceptionName = 'vm_performance_metric_anomaly') as t2 on t1.ncIp = t2.ncIp and date_trunc('minute', t1.exceptionTime) between date_add('minute', -10, t2.exceptionTime) and t2.exceptionTime limit 10000
  logstore: monitor_exception_sls_alert
  slsConfigName: ecs_inspector
  silent: 300
  region: [default]
  retentionTime: 13
  linkedDimensions:
    - VM_INFO
    - NC_INFO
    - USER_INFO
  levels:
    - name: critical
      expression: '{{pps}} >= 5000'
      notify: [ "SlsAlertHandler" ]
    - name: warning
      expression: '{{pps}} >= 0'
      notify: [ "SlsAlertHandler" ]
  tag: PerformanceImpact
  splitTask: false
  detailMetric: queryXdragonMetricOps

nc_down_supplement_task:
  interval: 98
  timeRange: 2000
  enable: true
  doc: 'nc_down发送宕机事件周期补偿任务（同步）'
  type: nc_cpu_event
  query: '(actions: StartTimerProcessor and nc_down or actions: 0x4000300000108 or actions: 0x3000000000301 or actions: 0x4000300000021 or actions: 0x30000000001010 or actions: 0x30000000001005 and features: vm_crash or actions: 0x30000000000a0) and dimensions.isGammaNc: false and not dimensions.zoneGroupFlag: test_before_online and not dimensions.isNcOffline: true'
  analyse: select 0 as down_event_send,0 as sls_event,targetId as machine_id,"dimensions.nc" as nc_ip,from_unixtime(min(matchedTimestamp/1000)) as exception_time,max("dimensions.vcpuCount") as max_vcpu,max("dimensions.vmCount") as max_vm_cnt,to_unixtime(now()) - min(matchedTimestamp/1000) as down_delta_time,max(case when actions like '%0x3000000000301%' or actions like '%0x30000000001010%' or actions like '%0x30000000001005%' or actions like '%0x4000300000021%' or actions like '%0x30000000000a0%' then 1 else 0 end) as no_need_supp  group by nc_ip,machine_id HAVING exception_time is not null limit 3000
  logstore: 'xdc_rule_log'
  slsConfigName: xunjian_zhangbei
  silent: 0
  retentionTime: 3
  internal: true
  splitTask: false
  linkedQueryList: [queryEventStart(),queryNcDownSlsEvent()]
  linkedDimensions: [NC_INFO,HOUYI_NC_INFO]
  tag: Event
  levels:
    - name: critical
      expression: '{{down_event_send}} == 0 and ({{max_vcpu}} > 0 or {{max_vm_cnt}} > 0) and {{no_need_supp}} == 0 and {{HOUYI_NC_INFO.isNcDown}} and {{down_delta_time}} <= 1000'
      notify: []
      action: ['batchRunTask(taskName=StartTimerProcessor,eventName=nc_down,silent=2000,sync=True,slsPush=True)']
      persistent: false
      needRelatedVm: false
    - name: warning
      expression: '{{down_event_send}} == 1 and {{sls_event}} == 0 and ({{max_vcpu}} > 0 or {{max_vm_cnt}} > 0) and {{down_delta_time}} >= 240 and {{down_delta_time}} <= 1600 and {{no_need_supp}} == 0 '
      notify: []
      action: ['batchRunTask(taskName=SupplementMonitorProcessor,exceptionName=nc_down_alert,sync=True,warningKey=supplement,warningValue=1)']
      needRelatedVm: false
    - name: normal
      expression: '{{down_delta_time}} >= 470'
      notify: []
      action: ['batchRunTask(taskName=ReasonDetectProcessor,exceptionNameField=exceptionName,detectType=REASON_NC_DOWN_RATIO,excludeTestAccount=1,timeRange=43200,endTimeBuffer=7200,sync=True)']
      persistent: false
      needRelatedVm: false

nc_down_reason_recall:
  interval: 86452
  timeRange: 259200
  enable: true
  doc: 'NC宕机原因定期回刷'
  type: nc_cpu_event
  query: 'exceptionName: nc_down_supplement_task and not (ap-southeast-1c-alipay or ap-southeast-1c or ap-southeast-x3 or ap-southeast-c)'
  analyse: select 1 as need_recall,ncIp as nc_ip ,min(exceptionTime) as exception_time ,max_by(reason,inspectTime) as reason,max_by(additionalInfo,inspectTime) as ext3,max_by(extraData,inspectTime) as srcFeature group by ncIp having (reason in ('unknown','null','None','') or reason like 'hardware:other%' or reason like '%panic%'  or reason like '%virt:process%' or reason like '%nc_hang%' or reason like 'hardware:network_card%' or reason like 'physical_network%' or reason like 'hardware:power%') and ext3 not like '[ active ]%' or reason like 'hardware:cpu%' limit 10000
  logstore: 'exception_reason_detect'
  slsConfigName: xunjian_zhangbei
  silent: 0
  retentionTime: 3
  linkedDimensions: [NC_INFO]
  tag: innerFailure
  levels:
    - name: normal
      expression: '{{need_recall}} == 1'
      notify: []
      action: ['batchRunTask(taskName=ReasonDetectProcessor,exceptionNameField=exceptionName,detectType=REASON_NC_DOWN_RATIO,excludeTestAccount=1,timeRange=63200,endTimeBuffer=15000,sync=True)']
      persistent: false
      needRelatedVm: false

vm_iohang_event_supp:
  interval: 254
  timeRange: 600
  enable: true
  doc: 'VM iohang任务补偿（磁盘ID缺失）'
  type: vm_virtStorage_event
  query: 'exceptionName: vm_iohang_start or exceptionName: vm_iohang_end'
  analyse: select instanceId as machine_id,ncIp as nc_ip,max_by(additionalInfo,__time__) as ext3,exceptionName as reason,max_by(additionalInfo,__time__) as extension group by nc_ip,machine_id,reason having ext3 like '%-%' and ext3 not like 'd-%' and ext3 not like 'v-%' and ext3 not like 'ww%' and ext3 not like 'cn-sys-disk%' and ext3 not in ('null','') limit 40000
  logstore: 'monitor_exception_sls_alert'
  slsConfigName: ecs_inspector
  silent: 0
  region: [default]
  retentionTime: 5
  linkedDimensions: [NC_INFO]
  tag: Event
  levels:
    - name: normal
      expression: '"{{reason}}" == "vm_iohang_start"'
      notify: []
      action: ['batchRunTask(taskName=SupplementMonitorProcessor,exceptionName=vm_iohang_start,sync=True,warningKey=supplement,warningValue=1)']
      persistent: false
    - name: low_warning
      expression: '"{{reason}}" == "vm_iohang_end"'
      notify: []
      action: ['batchRunTask(taskName=SupplementMonitorProcessor,exceptionName=vm_iohang_end,sync=True,warningKey=supplement,warningValue=1)']
      persistent: false

nc_batch_down_causedby_the_same_uid:
  interval: 127
  timeRange: 86700
  enable: true
  doc: 疑似同一用户导致批量nc宕机
  type: vm_cpu_event
  query: 'exceptionName: goc_batch_nc_down_p4'
  analyse: select t_nc_uid.nc_ip as nc_ip,t_nc_uid.nc_ip as machine_id,t_nccnt.exception_time as exception_time,t_nc_uid.ali_uid as reason, t_nccnt.ncip_cnt as ext3 from(SELECT ncIp as nc_ip,aliUid AS ali_uid FROM log GROUP BY nc_ip, ali_uid) t_nc_uid left join (SELECT aliUid AS ali_uid,count(distinct ncIp) AS ncip_cnt FROM log where aliUid is not null GROUP BY ali_uid) t_uid_nccnt on t_nc_uid.ali_uid = t_uid_nccnt.ali_uid left join (SELECT count(distinct ncIp) AS ncip_cnt,max(exceptionTime) AS exception_time FROM log) t_nccnt on t_uid_nccnt.ncip_cnt = t_nccnt.ncip_cnt where t_nccnt.ncip_cnt is not null and t_nccnt.ncip_cnt > 2 limit 1000
  logstore: monitor_exception_sls_alert
  slsConfigName: xunjian_zhangbei
  silent: 630
  region: [default]
  retentionTime: 15
  linkedDimensions:
    - NC_INFO
  levels:
    - name: warning
      expression: '{{cnt}} >= 1'
      notify: ["SlsAlertHandler"]
      phoneCall: false
      needRelatedVm: false
  tag: ControlFailed
  splitTask: true
  maxTraceLog: 10000
  powerSQL: true

vm_expect_status_not_matched_long_time:
  interval: 312
  timeRange: 14400
  enable: true
  doc: 虚拟机长时间不达预期状态
  type: vm_cpu_event
  query: 'not instanceId: "" and not status: "" and expect_status: Running and isTestAccount: False and is_gamma_machine: 0 and alarmMute: False'
  analyse: select instanceId as machine_id ,status ,expect_status,ncIp as nc_ip,isLocalDisk as is_local_disk,count(*) as cnt,max(exceptionTime ) as exception_time group by isLocalDisk,instanceId,status,expect_status,ncIp having status != expect_status and cnt >= 10 and to_unixtime(now()) - to_unixtime(exception_time) <= 1000  limit 20000
  logstore: monitor_exception_sls_alert
  slsConfigName: ecs_inspector
  silent: 630
  region: [default]
  retentionTime: 10
  exclusion:
    ext: '"{{NC_INFO}}" == "None" or "{{VM_INFO}}" == "None" or "{{VM_INFO.status}}" == "Running" or "{{NC_INFO.cluster_alias}}".startswith("AT") '
  linkedDimensions:
    - NC_INFO
    - VM_INFO
    - USER_INFO
  levels:
    - name: warning
      expression: '{{cnt}} >= 10 and not {{is_local_disk}}'
      notify: ["SlsAlertHandler"]
    - name: low_warning
      expression: '{{cnt}} >= 10 and {{is_local_disk}}'
      notify: ["SlsAlertHandler"]
  tag: ControlFailed
  splitTask: true
  maxTraceLog: 10000
  powerSQL: true

packet_frag_limit_error_fastpath:
  interval: 55
  timeRange: 60
  enable: false
  doc: '报文因分片限制被裁剪，通过快速路径发现'
  type: nc_network_event
  query: ''
  analyse: ''
  logstore: ''
  slsConfigName: ecs_inspector
  silent: 180
  retentionTime: 7
  tag: PerformanceImpact
  linkedDimensions: [NC_INFO]
  levels:
    - name: warning
      expression: '{{checked}} == 1'
      notify: [SlsAlertHandler]
      persistent: false

fm7_net_vgroup_error_fastpath:
  interval: 55
  timeRange: 60
  enable: true
  doc: 'vgroup规格超过fm7规格限制，通过快速路径发现'
  type: nc_network_event
  query: ''
  analyse: ''
  logstore: ''
  slsConfigName: ecs_inspector
  silent: 180
  retentionTime: 7
  tag: Event
  linkedDimensions: [NC_INFO]
  levels:
    - name: critical
      expression: '{{ret_code}} == 254'
      notify: [SlsAlertHandler]
    - name: warning
      expression: '{{ret_code}} > 0'
      notify: [ SlsAlertHandler ]

vm_net_queue_error:
  interval: 57
  timeRange: 63
  enable: true
  doc: '虚拟机网络队列不通'
  type: vm_network_event
  query: '*'
  analyse: select 1 as checked, regexp_extract(content, '([AYehbmcpi]*\-\w+)\.', 1) as machine_id, __source__ as nc_ip, regexp_replace(split_part(content, 'vm ', 2), 'don.*?t', 'dont') as ext3 from log where (content like 'vm %, port %, qid %:%' or content like 'vm %, port %, whole % hang%') and content not like 'vm %, port phynic, whole tx hang' limit 10000
  logstore: iohub_net_rxq_check
  slsConfigName: ecs_xunjian2
  silent: 280
  retentionTime: 7
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO]
  tag: PerformanceImpact
  levels:
    - name: critical
      expression: '{{checked}} == 1 and "recovered at present, need to check later" not in "{{ext3}}"'
      notify: [SlsAlertHandler]
    - name: low_warning
      expression: '{{checked}} == 1'
      notify: [SlsAlertHandler]

vm_eni_hang_start:
  interval: 57
  timeRange: 63
  enable: true
  doc: '虚拟机网卡夯'
  type: vm_network_event
  query: '*'
  analyse: select 1 as checked, 0 as arp_ping_status, regexp_extract(content, '([AYehbmcpias]*\-\w+)\.', 1) as machine_id, __source__ as nc_ip, regexp_extract(content, 'vm .*, port (.*?),', 1) as vport_id, regexp_replace(split_part(content, 'vm ', 2), 'don.*?t', 'dont') as ext3, 'None' as extension from log where (content like 'vm %, port %, qid %:%' or content like 'vm %, port %, whole % hang%') and content not like 'vm %, port phynic, whole tx hang' limit 10000
  logstore: iohub_net_rxq_check
  slsConfigName: ecs_xunjian2
  silent: 280
  retentionTime: 7
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO]
  linkedQueryList: [queryVmVportArppingHang(machine_id@or)]
  transformParams: [transformVportIdToEniName]
  tag: PerformanceImpact
  levels:
    - name: fatal
      expression: '{{checked}} == 1 and "{{extension}}" != "None" and {{arp_ping_status}} == 1'
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=VmNetQueueCheckProcessor,delay=600)']

vm_eni_hang_end:
  interval: 57
  timeRange: 63
  enable: true
  doc: '虚拟机网卡夯解除'
  type: vm_network_event
  query: 'Status: ok'
  analyse: select 1 as checked, MachineId as machine_id, NcIp as nc_ip, from_unixtime(__time__) as exception_time, 'None' as ext3, 'None' as extension from log limit 1000
  logstore: vm_net_queue_check_processor
  slsConfigName: ecs_inspector
  linkedQueryList: [queryVmEniHangEvent(machine_id@or)]
  region: [default]
  silent: 280
  retentionTime: 7
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO]
  tag: PerformanceImpact
  exclusion:
    ext: '"{{extension}}" == "None"'
  levels:
    - name: normal
      expression: '{{checked}} == 1'
      notify: [SlsAlertHandler]

vm_net_queue_error2:
  interval: 57
  timeRange: 63
  enable: false
  doc: '虚拟机网络队列不通'
  type: vm_network_event
  query: '*'
  analyse: select 1 as checked, vm_id as machine_id, nc_ip, regexp_replace(message, 'don.*?t', 'dont') as ext3 from log limit 10000
  logstore: fpga_net_queue_check
  slsConfigName: ecs_xunjian2
  silent: 280
  retentionTime: 7
  linkedDimensions: [NC_INFO,USER_INFO,VM_INFO]
  tag: PerformanceImpact
  levels:
    - name: fatal
      expression: '{{checked}} == 1 and ("whole rx hang" in "{{ext3}}" or "whole tx hang" in "{{ext3}}")'
      notify: [SlsAlertHandler]
    - name: critical
      expression: '{{checked}} == 1 and "recovered at present, need to check later" not in "{{ext3}}"'
      notify: [SlsAlertHandler]
    - name: low_warning
      expression: '{{checked}} == 1'
      notify: [SlsAlertHandler]

eci_vm_retained:
  interval: 133
  timeRange: 7300
  enable: true
  doc: 释放实例存在残留
  type: vm_virt_event
  query: 'item: remained_or_lost and not instance: managed-vm'
  analyse: select ncip as nc_ip,regexp_extract(instance, '([phbmeci]+\-\w+)', 1) as machine_id,concat(regexp_extract(instance, '([phbmeci]+\-\w+)', 1),'|') as extension,cluster as cluster_name,result as ext3,count(*) as cnt,max(time) as exception_time where item like 'remained_or_lost' group by instance, ncip, cluster, result HAVING machine_id not like 'null' limit 20000
  logstore: virt_monitor
  slsConfigName: virt_dataplane
  silent: 43200
  transformParams: []
  retentionTime: 50
  detailMetric: ''
  linkedDimensions:
    - NC_INFO
    - HOUYI_NC_INFO
    - VM_INFO
    - USER_INFO
    - DRAGONBOX_CN_INFO
  levels:
    - name: low_warning
      expression: '{{cnt}} >= 2  and "{{VM_INFO.status}}" == "Destroyed"'
      notify: ["SlsAlertHandler"]
      phoneCall: false
      needRelatedVm: true
    - name: warning
      expression: '{{cnt}} >= 2  and "{{VM_INFO}}" == "None"'
      notify: ["SlsAlertHandler"]
      phoneCall: false
      needRelatedVm: true
  tag: ControlFailed
  splitTask: true
  maxTraceLog: 10000
  powerSQL: false

fpga_pr_detect_live_migrate:
  interval: 58
  timeRange: 63
  enable: true
  doc: 'pr检测前热迁移受影响ecs'
  type: nc_controller_event
  query: 'opstype:0x40005000000010 and reasontype:hardware_fpga_error and submituser:alarm_xdc and workitemname:Finish and taskstatus:Pass'
  analyse: select 1 as checked,ip as nc_ip,from_unixtime(cast(max(tasktimestring) as bigint) / 1000) as exception_time group by ip limit 1000
  logstore: maintenance_gray_log
  slsConfigName: ecs_inspector
  region: [default]
  retentionTime: 7
  linkedDimensions: [NC_INFO]
  internal: true
  tag: PerformanceImpact
  silent: 780
  levels:
    - name: warning
      expression: '{{checked}} == 1'
      notify: [SlsAlertHandler]
      persistent: false
      needRelatedVm: false

idc_repair_order_unexpected_confirm:
  interval: 62
  timeRange: 70
  enable: true
  doc: '服务器维修单被非预期确认维修'
  type: nc_hardware_hardware
  query: 'state: 驻场待维修 and authorize: true and not 电源线故障 and not 网线故障 and not 硬盘故障 and not peConfirmTime: "" and not type: MOC-CN带外网线故障 and shutdownType: 1'
  analyse: select 1 as confirm,concat(min(peConfirmTime),':00') as exception_time,type as reason,concat('idc:',orderId) as ext3,ip as nc_ip group by reason,ext3,nc_ip,nodeGroup  having nodeGroup  like 'aliyun_ay%' and nodeGroup like '%server' limit 2000
  logstore: repair_order_send
  slsConfigName: xunjian_zhangbei
  silent: 3580
  region: [default]
  linkedDimensions: [NC_INFO]
  detailMetric: queryIdcOrderLog
  tag: ControlFailed
  exclusion:
    ext: '"{{NC_INFO}}" == "None"'
  levels:
    - name: warning
      expression: '{{confirm}} == 1'
      notify: [SlsAlertHandler]

idc_order_out_of_warranty:
  interval: 62
  timeRange: 70
  enable: true
  doc: '服务器维修单过保不修(实时)'
  type: nc_hardware_hardware
  query: 'state: 过保不修'
  analyse: select 1 as confirm,min(time) as exception_time,type as reason,concat('idc:',orderId) as ext3,ip as nc_ip group by reason,ext3,nc_ip,nodeGroup  having nodeGroup  like 'aliyun_ay%' and nodeGroup like '%server' limit 2000
  logstore: repair_order_send
  slsConfigName: xunjian_zhangbei
  silent: 3580
  region: [default]
  linkedDimensions: [NC_INFO]
  detailMetric: queryIdcOrderLog
  tag: ControlFailed
  exclusion:
    ext: '"{{NC_INFO}}" == "None"'
  levels:
    - name: warning
      expression: '{{confirm}} == 1'
      notify: [SlsAlertHandler]

# 注意长度限制（40）
vm_pkt_err_cause_fpga_error_enhance:
  interval: 115
  timeRange: 250
  enable: true
  doc: vm 错包引起 fpga 错误(疑似攻击行为, cloudops兜底探测)
  type: nc_fpga_hardware
  query: 'type: ExecuteSA and ex3: "iohub-fpga-event.log"'
  analyse: select cnt, nc_ip, ext3, case when cardinality(exception_times) > 0 then replace(replace(element_at(exception_times, cardinality(exception_times)), '-', ' '), '.', '-') else logTime end as exception_time from (select 1 as cnt, bizId as nc_ip, remark as ext3, regexp_extract_all(remark, '\-+(\d+\.\d+\.\d+\-\d+:\d+:\d+)\-+', 1) as exception_times, logTime from log where strpos(remark, 'addr 0x04000002 value 0x00200000') > 0 )
  logstore: cloudops_biz_log
  slsConfigName: ecs_inspector
  region: [default]
  tag: PerformanceImpact
  silent: 1980
  linkedDimensions: [NC_INFO,DRAGONBOX_CN_INFO]
  retentionTime: 18
  levels:
    - name: fatal
      expression: '{{cnt}} > 0'
      notify: [SlsAlertHandler]

vm_packet_error_cause_fpga_error:
  interval: 115
  timeRange: 250
  enable: true
  doc: vm 错包引起 fpga 错误(疑似攻击行为)
  type: nc_fpga_hardware
  query: 'addr 0x04000002 value 0x00200000'
  analyse: select 1 as cnt, __source__ as nc_ip, max(__raw__) as ext3, replace(replace(max(time), '-', ' '), '.', '-') as exception_time where strpos(__raw__, 'addr 0x04000002 value 0x00200000') > 0 group by nc_ip having to_unixtime(now()) - to_unixtime(exception_time) < 86400 limit 1000
  logstore: iohub-fpga-event
  slsConfigName: ecs_xunjian2
  tag: PerformanceImpact
  silent: 1980
  linkedDimensions: [NC_INFO,DRAGONBOX_CN_INFO]
  retentionTime: 18
  levels:
    - name: fatal
      expression: '{{cnt}} > 0'
      notify: [SlsAlertHandler]

vmcore_analysis_result_event:
  interval: 67
  timeRange: 111
  enable: true
  doc: vmcore分析结果
  type: nc_cpu_available
  query: '*'
  analyse: select ip as nc_ip,ip as machine_id,max(crashTime) as exception_time,category as reason,if("desc" is not null,"desc",reason) as ext3,isCorrect,count(*) as cnt from log group by nc_ip,reason,ext3,isCorrect LIMIT 1000
  logstore: vmcore_analysis_result
  slsConfigName: ecs_xunjian2
  silent: 630
  region: [default]
  retentionTime: 15
  linkedDimensions:
    - NC_INFO
  eventCenter:
    push: true
    opsCode: InstanceFailure
    impact: Alert
    resourceStatus: Warning
    category: VM.Availability
  levels:
    - name: fatal
      expression: '{{cnt}} >= 1 and "{{isCorrect}}" == "true"'
      notify: ["SlsAlertHandler", "EventCenterAlertHandler"]
      phoneCall: false
      needRelatedVm: false
    - name: critical
      expression: '{{cnt}} >= 1'
      notify: [ "SlsAlertHandler", "EventCenterAlertHandler" ]
      phoneCall: false
      needRelatedVm: false
  tag: PerformanceImpact
  splitTask: true
  maxTraceLog: 10000
  powerSQL: false

eed_blkpmd_pointer_mismatch:
  interval: 65
  timeRange: 71
  enable: true
  doc: eed队列错乱rx_fifo和free_fifo中指针不对，软硬件地址不协同
  type: nc_virtStorage_event
  query: '* and ("is not equal to mbuf phys")'
  analyse: select min(regexp_extract(content,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,count(*) as error_cnt, regexp_extract(content, '.+(is not equal to mbuf phys).*', 1) as ext3,__source__ as nc_ip group by nc_ip,ext3 limit 10000
  logstore: iohub_pmd_log
  slsConfigName: ecs_inspector
  silent: 600
  retentionTime: 15
  linkedDimensions:
    - NC_INFO
  levels:
    - name: critical
      expression: '{{error_cnt}} >= 1'
      notify: [ "SlsAlertHandler" ]
      needRelatedVm: false
  tag: innerFailure
  powerSQL: true

eed_blkpmd_data_error:
  interval: 63
  timeRange: 67
  enable: true
  doc: eed数据异常
  type: nc_virtStorage_event
  query: '* and (("Rx check dma addr invalid") or ("detect zero len") or ("invalid param for ddm read"))'
  analyse: select min(regexp_extract(content,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,count(*) as error_cnt, regexp_extract(content, '.+(check dma addr invalid|detect zero len|invalid param for ddm read).*', 1) as ext3, __source__ as nc_ip group by nc_ip, ext3 limit 10000
  logstore: iohub_pmd_log
  slsConfigName: ecs_inspector
  silent: 600
  retentionTime: 15
  linkedDimensions:
    - NC_INFO
  levels:
    - name: critical
      expression: '{{error_cnt}} >= 1'
      notify: [ "SlsAlertHandler" ]
      needRelatedVm: false
  tag: innerFailure
  powerSQL: true

eed_blkpmd_address_error:
  interval: 66
  timeRange: 72
  enable: true
  doc: eed地址错误
  type: nc_virtStorage_event
  query: '* and (("detect abnormal address") or ("detect NULL pointer"))'
  analyse: select min(regexp_extract(content,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,count(*) as error_cnt, regexp_extract(content, '.+(detect abnormal address|detect NULL pointer).*', 1) as ext3,__source__ as nc_ip group by nc_ip,ext3 limit 10000
  logstore: iohub_pmd_log
  slsConfigName: ecs_inspector
  silent: 600
  retentionTime: 15
  linkedDimensions:
    - NC_INFO
  levels:
    - name: critical
      expression: '{{error_cnt}} >= 1'
      notify: [ "SlsAlertHandler" ]
      needRelatedVm: false
  tag: innerFailure
  powerSQL: true

eed_blkpmd_fpga_abnormal:
  interval: 62
  timeRange: 72
  enable: true
  doc: eed场景fpga异常
  type: nc_virtStorage_event
  query: '* and (("receive fpga exception") or ("no clear bdf param"))'
  analyse: select min(regexp_extract(content,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,count(*) as error_cnt, regexp_extract(content, '.+(receive fpga exception|no clear bdf param).*', 1) as ext3,__source__ as nc_ip group by nc_ip,ext3 limit 10000
  logstore: iohub_pmd_log
  slsConfigName: ecs_inspector
  silent: 600
  retentionTime: 15
  linkedDimensions:
    - NC_INFO
  levels:
    - name: critical
      expression: '{{error_cnt}} >= 1'
      notify: [ "SlsAlertHandler" ]
      needRelatedVm: false
  tag: innerFailure
  powerSQL: true

eed_blkpmd_multiple_copy_error:
  interval: 66
  timeRange: 76
  enable: true
  doc: eed三副本异常
  type: nc_virtStorage_event
  query: '* and (("ddm read request is switched") or ("invalid param for" and "transmission mode"))'
  analyse: select min(regexp_extract(content,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,count(*) as error_cnt, regexp_extract(content, '.+(ddm read request is switched|invalid param for multiple-copy transmission mode).*', 1) as ext3,__source__ as nc_ip group by nc_ip,ext3 limit 10000
  logstore: iohub_pmd_log
  slsConfigName: ecs_inspector
  silent: 600
  transformParams: []
  retentionTime: 15
  detailMetric: ''
  linkedDimensions:
    - NC_INFO
  levels:
    - name: critical
      expression: '{{error_cnt}} >= 1'
      notify: ["SlsAlertHandler"]
      needRelatedVm: false
  tag: innerFailure
  powerSQL: true

eed_blkpmd_crc_error:
  interval: 69
  timeRange: 70
  enable: true
  doc: eed队列错乱rx_fifo和free_fifo中指针不对，软硬件地址不协同
  type: nc_virtStorage_event
  query: '* and (("crc offloading total len") or ("no data for crc offloading"))'
  analyse: select min(regexp_extract(content,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,count(*) as error_cnt, regexp_extract(content, '.+(crc offloading total len non-64-aligned|no data for crc offloading).*', 1) as ext3,__source__ as nc_ip group by nc_ip,ext3 limit 10000
  logstore: iohub_pmd_log
  slsConfigName: ecs_inspector
  silent: 600
  retentionTime: 15
  linkedDimensions:
    - NC_INFO
  levels:
    - name: critical
      expression: '{{error_cnt}} >= 1'
      notify: [ "SlsAlertHandler" ]
      needRelatedVm: false
  tag: innerFailure
  powerSQL: true

essd_blkpmd_pointer_mismatch:
  interval: 55
  timeRange: 62
  enable: true
  doc: essd队列错乱
  type: nc_virtStorage_event
  query: '* and ("is not equal to mbuf phys")'
  analyse: select count(*) as error_cnt,__source__ as nc_ip,min(regexp_extract(time,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,regexp_extract(__raw__,'.+(is not equal to mbuf phys).*',1) as ext3 group by nc_ip,ext3 limit 10000
  logstore: ebsls_tdc_netpmd
  slsConfigName: ebs_sls
  silent: 600
  linkedDimensions: [NC_INFO]
  retentionTime: 15
  tag: innerFailure
  powerSQL: true
  levels:
    - name: critical
      expression: '{{error_cnt}} > 0'
      notify: [ "SlsAlertHandler" ]
      needRelatedVm: false

essd_blkpmd_multiple_copy_error:
  interval: 59
  timeRange: 61
  enable: true
  doc: essd三副本错误
  type: nc_virtStorage_event
  query: '* and ("read request is switched from the ddm channel to the normal read request")'
  analyse: select count(*) as error_cnt,__source__ as nc_ip,min(regexp_extract(time,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,regexp_extract(__raw__,'.+(read request is switched from the ddm channel to the normal read request).*',1) as ext3 group by nc_ip,ext3 limit 10000
  logstore: ebsls_tdc_netpmd
  slsConfigName: ebs_sls
  silent: 600
  linkedDimensions: [NC_INFO]
  retentionTime: 15
  tag: innerFailure
  powerSQL: true
  levels:
    - name: critical
      expression: '{{error_cnt}} > 0'
      notify: [ "SlsAlertHandler" ]
      needRelatedVm: false

essd_blkpmd_fpga_abnormal:
  interval: 65
  timeRange: 69
  enable: true
  doc: essd场景fpga异常
  type: nc_virtStorage_event
  query: '* and ("receive fpga exception")'
  analyse: select count(*) as error_cnt,__source__ as nc_ip,min(regexp_extract(time,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time, 'receive fpga exception' as ext3 group by nc_ip,ext3 limit 10000
  logstore: ebsls_tdc_netpmd
  slsConfigName: ebs_sls
  silent: 600
  linkedDimensions: [NC_INFO]
  retentionTime: 15
  tag: innerFailure
  powerSQL: true
  levels:
    - name: critical
      expression: '{{error_cnt}} > 0'
      notify: [ "SlsAlertHandler" ]
      needRelatedVm: false

essd_blkpmd_data_error:
  interval: 59
  timeRange: 66
  enable: true
  doc: essd场景taiji将MOC内存切换出去可能错包老包
  type: nc_virtStorage_event
  query: '* and ("taichi memory compression")'
  analyse: select count(*) as error_cnt,__source__ as nc_ip,min(regexp_extract(time,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,regexp_extract(__raw__,'.+(taichi memory compression may lead to data corruption risks).*',1) as ext3 group by nc_ip,ext3 limit 10000
  logstore: ebsls_tdc_netpmd
  slsConfigName: ebs_sls
  silent: 600
  linkedDimensions: [NC_INFO]
  retentionTime: 15
  tag: innerFailure
  powerSQL: true
  levels:
    - name: critical
      expression: '{{error_cnt}} > 0'
      notify: [ "SlsAlertHandler" ]
      needRelatedVm: false

essd_blkpmd_iov_error:
  interval: 57
  timeRange: 67
  enable: true
  doc: essd场景iov错误
  type: nc_virtStorage_event
  query: '* and (("ddm write meta csum check failed") or ("solar ddm read req crc check failed") or ("Fusion data len is zero"))'
  analyse: select count(*) as error_cnt,__source__ as nc_ip,min(regexp_extract(time,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,regexp_extract(__raw__,'.+(ddm write meta csum check failed|solar ddm read req crc check failed|data len is zero).*',1) as ext3 group by nc_ip,ext3 limit 10000
  logstore: ebsls_tdc_netpmd
  slsConfigName: ebs_sls
  silent: 600
  linkedDimensions: [NC_INFO]
  retentionTime: 15
  tag: innerFailure
  powerSQL: true
  levels:
    - name: critical
      expression: '{{error_cnt}} > 0'
      notify: [ "SlsAlertHandler" ]
      needRelatedVm: false

essd_blkpmd_address_error:
  interval: 62
  timeRange: 63
  enable: true
  doc: essd地址错误
  type: nc_virtStorage_event
  query: '* and (("Fusion error_addr") or ("Fusion NULL addr"))'
  analyse: select count(*) as error_cnt,__source__ as nc_ip,min(regexp_extract(time,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,regexp_extract(__raw__,'.+(error_addr|NULL addr).*',1) as ext3 group by nc_ip,ext3 limit 10000
  logstore: ebsls_tdc_netpmd
  slsConfigName: ebs_sls
  silent: 600
  linkedDimensions: [NC_INFO]
  retentionTime: 15
  tag: innerFailure
  powerSQL: true
  levels:
    - name: critical
      expression: '{{error_cnt}} > 0'
      notify: [ "SlsAlertHandler" ]
      needRelatedVm: false

kuafu_tdc_ddm_error:
  interval: 63
  timeRange: 64
  enable: true
  doc: 夸父ddm错误
  type: nc_virtStorage_event
  query: '* and (("fatal error for ddm") or ("ddm meta checksum mismatch"))'
  analyse: select count(*) as error_cnt,__source__ as nc_ip,min(regexp_extract(time,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,regexp_extract(__raw__,'.+(fatal error for ddm|ddm meta checksum mismatch).*',1) as ext3 group by nc_ip,ext3 limit 10000
  logstore: ebsls_tdc_anet
  slsConfigName: ebs_sls
  silent: 600
  linkedDimensions: [NC_INFO]
  retentionTime: 15
  tag: innerFailure
  powerSQL: true
  levels:
    - name: critical
      expression: '{{error_cnt}} > 0'
      notify: [ "SlsAlertHandler" ]
      needRelatedVm: false

tdc_invalid_ddm_packet:
  interval: 63
  timeRange: 64
  enable: true
  doc: tdc获取无效ddm数据包
  type: nc_virtStorage_event
  query: '* and ("get a invalid ddm packet")'
  analyse: select count(*) as error_cnt,__source__ as nc_ip,min(regexp_extract(time,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,'invalid ddm packet' as ext3 group by nc_ip,ext3 limit 10000
  logstore: ebsls_tdc_anet
  slsConfigName: ebs_sls
  silent: 600
  linkedDimensions: [NC_INFO]
  exclusion:
    ext: '"{{NC_INFO}}" == "None" or "{{NC_INFO.cluster_alias}}" not in ("AY768E","AY768P","AY758L","AY660T","AY754G","AY760J","AY537Y","AY710V","AY581L","AY710T","AY656P","AY747A","AY747N","AY747Z","AY671Q","AY671N","AY412W","AY621H","AY866C","AY836E","AY826L","AY764T","AY790Z","AY764U","AY803Q","AY790S","AY820F","AY767M","AY820J","AY820A","AY789Z","AY789X","AY767E","AY767K","AY746X","AY790X","AY831G","AY800B","AY800V")'
  retentionTime: 15
  tag: innerFailure
  powerSQL: true
  levels:
    - name: critical
      expression: '{{error_cnt}} > 0'
      notify: [ "SlsAlertHandler" ]
      needRelatedVm: false

tdc_unexpected_ddm_table:
  interval: 62
  timeRange: 65
  enable: true
  doc: 读取到非预期ddm表
  type: nc_virtStorage_event
  query: '* and ("fusionpath read unexpected ddm table hit")'
  analyse: select count(*) as error_cnt,__source__ as nc_ip,min(regexp_extract(time,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,'fusionpath read unexpected ddm table hit' as ext3 group by nc_ip,ext3 limit 10000
  logstore: tdc_common_log
  slsConfigName: ecs_inspector
  silent: 600
  linkedDimensions: [NC_INFO]
  retentionTime: 15
  tag: innerFailure
  powerSQL: true
  levels:
    - name: critical
      expression: '{{error_cnt}} > 0'
      notify: [ "SlsAlertHandler" ]
      needRelatedVm: false

