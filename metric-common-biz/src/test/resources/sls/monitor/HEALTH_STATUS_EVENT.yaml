ht_cahl_end:
  interval: 43
  timeRange: 115
  enable: true
  doc: '实例健康状态，云助手心跳恢复'
  type: vm_heartbeat_event
  query: 'uptime > 0'
  analyse: select 'null' as last_ht_cahl, ecsInstanceId as machine_id,count(*) as cnt, count(*) over () as total,max(uptime) as max_uptime, min(uptime) as min_uptime,max(__time__) as timestamp,to_unixtime(now()) - max(__time__) as delta_time group by ecsInstanceId HAVING ((cnt < 2 and delta_time < 44) or (cnt > 1 and min_uptime < 54000)) and  machine_id is not null  order by cnt desc limit 20000
  logstore: heartbeat-log
  slsConfigName: cloudassistant
  silent: 880
  linkedQueryList: ['queryHtCahlStart()']
  retentionTime: 7
  tag: Event
  detailMetric: queryHeartBeatForCloudAssitant
  linkedDimensions: [VM_INFO,NC_INFO]
  maxTraceLog: 0
  exclusion:
    ext: '"{{VM_INFO}}" == "None" or "{{VM_INFO.status}}" in ("Destroyed","destroying","Released","Releasing")'
  levels:
    - name: warning
      expression: '"{{last_ht_cahl}}" == "ht_cahl_start" and {{total}} < 5000'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
      persistent: false
  eventCenter:
    push: true
    opsCode: InstanceStatusChange
    impact: Alert
    resourceStatus: Warning
    category: Other

ht_arp_ping_rtt_1min:
  interval: 20
  timeRange: 35
  enable: true
  doc: 'VM_ARP_PING延迟时间超时'
  type: vm_network_sys
  query: 'not vm_name: "" and not vm_name: None and arp_tx_packets > 0'
  analyse: select compare(rx, 55) as diff, machine_id, nc_ip, now() as exception_time, count(*) over () as total from (SELECT  NC_IP as nc_ip, split_part(vm_name, '.', 1) as machine_id, avg(rtt_avg) as arp_ping_rtt_avg, sum(arp_rx_packets) as rx ,max(from_unixtime(__time__)) as exception_time from nc_stats_arp_ping_1min_agg GROUP BY nc_ip, machine_id having machine_id not like 'bvt%') group by nc_ip, machine_id having diff[1] = 0 and diff[2] !=0 limit 6000
  logstore: 'nc_stats_arp_ping_1min_agg'
  detailMetric: queryVmArpPingMetric
  slsConfigName: ecs_network
  linkedDimensions: [VM_INFO]
  silent: 21580
  retentionTime: 10
  tag: PerformanceImpact
  maxTraceLog: 0
  powerSQL: true
  exclusion:
    ext: '"{{VM_INFO}}" == "None" or {{VM_INFO.getDeltaTimeForStart}} <= 180'
  levels:
    - name: warning
      expression: '{{total}} < 1000'
      persistent: false
      notify: [SlsAlertHandler]

ht_cahl_lost:
  interval: 41
  timeRange: 197
  enable: true
  doc: '实例健康状态，云助手心跳丢失'
  type: vm_heartbeat_event
  query: 'uptime > 0'
  analyse: select 0 as is_paused,ecsInstanceId as machine_id, count(*) as cnt, count(*) over () as total, min(uptime) as min_uptime,max(__time__) as timestamp,to_unixtime(now()) - max(__time__) as delta_time group by ecsInstanceId HAVING min_uptime > 300000 and cnt > 1 and delta_time between 73 and 119 and  machine_id is not null and cnt < 2500 order by cnt desc limit 20000
  logstore: heartbeat-log
  slsConfigName: cloudassistant
  silent: 3580
  retentionTime: 10
  linkedQueryList: [ 'queryVmPaused()' ]
  detailMetric: queryHeartBeatForCloudAssitant
  linkedDimensions: [VM_INFO,NC_INFO]
  tag: PerformanceImpact
  maxTraceLog: 0
  exclusion:
    ext: '"{{VM_INFO}}" == "None" or "{{VM_INFO.status}}" != "Running"'
  levels:
    - name: warning
      expression: '{{total}} < 2500 and {{is_paused}} == 0'
      notify: [SlsAlertHandler]
      persistent: false

ht_pedestal_start:
  interval: 13
  timeRange: 85
  enable: true
  doc: '宿主机不通且VM心跳丢失'
  type: vm_heartbeat_event
  #query: 'not zoneGroupFlag: biz_cloudbox and ((exceptionName: nc_down_alert and not additionalInfo: analyse) or (exceptionName: avs_monitor_warning and (warningLevel: fatal or warningLevel: critical)) or exceptionName: pingmesh_latency_error_public or exceptionName:ag_nc_short_ping_check_failed or exceptionName:nc_hang_too_long or exceptionName: nc_hang_uniform_check and warningLevel: critical or exceptionName: staragent_cn_ping_check_failed or exceptionName: xdragon_cn_hang_or_down or exceptionName:nc_down_uniform_check) and not status: destroyed and not status: destroying and not instanceId: "" and not (biz_status: nc_down and status: Shutted)'
  query: 'not zoneGroupFlag: biz_cloudbox and ((exceptionName: vm_vsock_icmp_ping_loss_new) or (exceptionName: nc_down_alert and not additionalInfo: analyse) or (exceptionName: avs_monitor_warning and (warningLevel: fatal or warningLevel: critical)) or exceptionName: pingmesh_latency_error_public or exceptionName: ptp_pingmesh_latency_error_public or exceptionName: ptp_pingmesh_latency_error_public2 or exceptionName:nc_hang_too_long or exceptionName: nc_hang_uniform_check and warningLevel: critical or exceptionName: staragent_cn_ping_check_failed or exceptionName: xdragon_cn_hang_or_down or exceptionName:nc_down_uniform_check) and not status: destroyed and not status: destroying and not instanceId: "" and not (biz_status: nc_down and status: Shutted)'
  analyse: select 'null' as last_ht_pedestal,0 as data_loss,0 as is_paused,instanceId as machine_id,max(exceptionTime) as exception_time,ncIp as nc_ip group by machine_id,nc_ip limit 10000
  logstore: 'monitor_exception_sls_alert'
  slsConfigName: xunjian_zhangbei
  silent: 880
  powerSQL: true
  linkedQueryList: ['queryVmMonitorLoss(machine_id@or)', 'queryVmPaused()']
  retentionTime: 10
  detailMetric: queryHeartBeatForCloudAssitant
  linkedDimensions: [NC_INFO, VM_INFO]
  tag: PerformanceImpact
  maxTraceLog: 0
  region: [default]
  levels:
    - name: warning
      expression: '{{data_loss}} == 1 and {{is_paused}} == 0'
      notify: [SlsAlertHandler]
      persistent: false

ht_pedestal_end:
  interval: 59
  timeRange: 86400
  enable: true
  doc: '健康状态底座问题恢复'
  type: vm_heartbeat_event
  query: 'exceptionName: ht_pedestal_start or exceptionName: ht_pedestal_end or exceptionName: vm_down_end or exceptionName: vm_vsock_icmp_ping_loss_recovery or exceptionName: vm_migrate_nc_event'
  #analyse: select 0 as data_recovery, now() as exception_time, base_table.machine_id as machine_id, base_table.ncIp as ncIp from (select machine_id, max(__time__) as max_time from monitor_exception_sls_alert group by machine_id ) as max_table join (select __time__ as time, exceptionName, machine_id, ncIp from monitor_exception_sls_alert) as base_table ON base_table.machine_id = max_table.machine_id and base_table.time = max_table.max_time where base_table.exceptionName = 'ht_pedestal_start' limit 50000
  analyse: select 0 as data_recovery, machine_id,sum(score) as is_closed,max_by(ncIp,time) as ncIp,now() as exception_time from (select case when exceptionName = 'ht_pedestal_start' then 1 when exceptionName = 'ht_pedestal_end' then -1 else 0 end as score,instanceId as machine_id,max_by(ncIp,__time__) as ncIp,max(__time__) as time from log group by instanceId,score having instanceId not like 'BVT-%' ) group by machine_id having is_closed >= 1 limit 25000
  logstore: 'monitor_exception_sls_alert'
  slsConfigName: ecs_inspector
  silent: 880
  powerSQL: true
  linkedQueryList: ['queryVmMonitorRecovery(ncIp@or)']
  retentionTime: 10
  detailMetric: queryHeartBeatForCloudAssitant
  linkedDimensions: [NC_INFO, VM_INFO]
  tag: PerformanceImpact
  maxTraceLog: 0
  region: [default]
  levels:
    - name: warning
      expression: '{{data_recovery}} == 1'
      notify: [SlsAlertHandler]
      persistent: false

ht_cahl_start:
  interval: 15
  timeRange: 113
  enable: true
  doc: '云助手心跳丢失且最新的arp_ping也丢包'
  type: vm_heartbeat_event
  query: 'exceptionName: ht_arp_ping_rtt_1min or exceptionName:ht_cahl_lost'
  analyse: select 0 as is_paused, 'null' as last_ht_cahl, 'null' as last_heartbeat, a.machine_id as machine_id, GREATEST(a.max_time, b.max_time) as exception_time from (select max(exceptionTime) as max_time, machine_id, exceptionName from  monitor_exception_sls_alert group by machine_id, exceptionName having exceptionName in ('ht_arp_ping_rtt_1min')) as a join (select max(exceptionTime) as max_time, machine_id, exceptionName from  monitor_exception_sls_alert group by machine_id, exceptionName having exceptionName='ht_cahl_lost') as  b on a.machine_id=b.machine_id limit 5000
  logstore: 'monitor_exception_sls_alert'
  slsConfigName: xunjian_zhangbei
  powerSQL: true
  silent: 21600
  linkedQueryList: ['queryHtCahlStart()', 'queryAxtHeartBeat(machine_id@or)',  'queryVmPaused()']
  retentionTime: 10
  detailMetric: queryHeartBeatForCloudAssitant
  linkedDimensions: [NC_INFO, VM_INFO]
  tag: PerformanceImpact
  maxTraceLog: 0
  region: [default]
  exclusion:
    ext: '"{{NC_INFO}}" == "None"'
  levels:
    - name: warning
      expression: '"{{last_ht_cahl}}" != "ht_cahl_start" and "{{last_heartbeat}}" != "ok" and {{is_paused}} == 0'
      notify: [SlsAlertHandler]
      action: [ 'batchRunTask(taskName=VsockPingCheckProcessor)']
      persistent: true

vm_metric_recovery:
  interval: 34
  timeRange: 155
  enable: true
  doc: '底层周期性健康检查实例恢复'
  type: vm_heartbeat_event
  query: 'not vmid: ""'
  analyse: select vmid as machine_id, max(__time__) as timestamp, max_by(state, __time__) as new, count_if(state=0) as healthCnt, count(*) as cnt group by vmId having new = 0 and  healthCnt < cnt limit 20000
  logstore: 'xhp_vm_heartbeat'
  slsConfigName: ecs_xunjian2
  powerSQL: true
  silent: 21600
  linkedQueryList: [ ]
  retentionTime: 10
  detailMetric: queryHeartBeatForCloudAssitant
  linkedDimensions: [NC_INFO, VM_INFO]
  tag: PerformanceImpact
  maxTraceLog: 0
  region: [default]
  exclusion:
    ext: '"{{NC_INFO}}" == "None"'
  levels:
    - name: normal
      expression: '{{cnt}} > 1'
      notify: [SlsAlertHandler]
      persistent: false

vm_arp_timeout_recovery:
  interval: 35
  timeRange: 185
  enable: true
  doc: 虚拟机启动后ARP连通
  type: vm_network_event
  query: 'arp_rx_packets > 0 and arp_timeout_num:0 and not vm_name: ""'
  analyse: select split_part(vm_name,'.',1) as machine_id,to_unixtime(now()) - to_unixtime(max(begin_time)) as delta_time,count(*) as cnt,NC_IP  as nc_ip group by machine_id,nc_ip HAVING cnt = 1 and delta_time <= 90 limit 20000
  logstore: nc_stats_arp_ping_1min_agg
  slsConfigName: ecs_network
  silent: 300
  retentionTime: 1
  linkedDimensions: [NC_INFO,VM_INFO]
  tag: Event
  splitTask: false
  maxTraceLog: 4000
  powerSQL: true
  levels:
    - name: normal
      expression: '{{cnt}} == 1'
      notify: [EventCenterAlertHandler]
      persistent: false
  eventCenter:
    push: false
    opsCode: InstanceStatusChange
    impact: Alert
    resourceStatus: Warning
    category: Other

vm_health_check_recovery:
  interval: 54
  timeRange: 62
  enable: true
  doc: vm健康检查结果正常
  type: vm_cpu_event
  query: 'checkItem: Vm and resultMsg: ok and "Vm Service Check"'
  analyse: select 'false' as check_arp_err,'false' as check_cloudasist_err,machineId as machine_id, regexp_extract(max(finishTime),'(\d+-\d+-\d+ \d+:\d+:\d+)',1) as exception_time, concat(resultKey,':', resultValue) as reason, checkItem as ext3, count(*) as cnt,concat(resultKey,':', resultValue) as extension,resultKey,regexp_extract(replace(max_by(message,finishTime),'''',''), '\[\d+%\]') as ratio group by machineId,resultKey,resultValue,checkItem limit 10000
  logstore: check_health_result
  slsConfigName: ecs_xunjian2
  silent: 1200
  region: [default]
  retentionTime: 15
  linkedQueryList: [queryVmHangNeedCheckField()]
  linkedDimensions:
    - NC_INFO
    - VM_INFO
    - USER_INFO
  eventCenter:
    push: true
    opsCode: InstanceStatusChange
    impact: Alert
    resourceStatus: Warning
    category: VM.Performance
    eventSilent: 200
  levels:
    - name: warning
      expression: '{{cnt}} >= 1 and "{{reason}}" == "assist-agent:ok"'
      notify: ["SlsAlertHandler", "EventCenterAlertHandler"]
      hasVmThenAlert: false
      needRelatedVm: true
    - name: low_warning
      expression: '{{cnt}} >= 1 and "{{reason}}" == "network:ok" and "{{ratio}}" == "[0%]" and "{{check_cloudasist_err}}" == "false"'
      notify: ["SlsAlertHandler", "EventCenterAlertHandler"]
      hasVmThenAlert: false
      needRelatedVm: true
    - name: critical
      expression: '{{cnt}} >= 1 and "{{reason}}" == "network:ok" and "{{ratio}}" != "[0%]"'
      notify: ["SlsAlertHandler"]
      hasVmThenAlert: false
      persistent: false
      needRelatedVm: true
    - name: normal
      expression: '{{cnt}} >= 1'
      notify: ["SlsAlertHandler"]
      hasVmThenAlert: false
      persistent: false
      needRelatedVm: true
  tag: Event
  splitTask: true
  maxTraceLog: 10000
  powerSQL: true

vm_check_health_fail:
  interval: 31
  timeRange: 64
  enable: true
  doc: vm健康检查结果异常
  type: vm_cpu_event
  query: 'checkItem: Vm and resultMsg: fail'
  analyse: select machineId as machine_id, regexp_extract(max(finishTime),'(\d+-\d+-\d+ \d+:\d+:\d+)',1) as exception_time, concat(resultKey,':', resultValue) as reason, checkItem as ext3, count(*) as cnt,concat(resultKey,':', resultValue) as extension group by machineId,resultKey,resultValue,checkItem limit 10000
  logstore: check_health_result
  slsConfigName: ecs_xunjian2
  silent: 360
  retentionTime: 13
  linkedDimensions:
    - NC_INFO
    - VM_INFO
    - USER_INFO
  region: [default]
  levels:
    - name: warning
      expression: '{{cnt}} >= 1 and not ("{{machine_id}}".startswith("cp") and "{{reason}}" == "assist-agent:fail" )'
      notify: ["SlsAlertHandler"]
      needRelatedVm: true
  tag: PerformanceImpact
  splitTask: 'true'
  maxTraceLog: 10000
  powerSQL: true

cloudassitant_heartbeat_loss_after_upgrade:
  interval: 57
  timeRange: 875
  enable: true
  doc: 疑似实例热升级后发生云助手心跳异常
  type: vm_heartbeat_event
  query: 'exceptionName: cloudassitant_heartbeat_loss_high or exceptionName: qemu_hot_upgrade_event'
  analyse: select ncIp as nc_ip,machine_id,min(delta_time) as min_deltatime, 1 as cnt from (select to_unixtime(t_a.exceptionTime) - to_unixtime(t_b.exceptionTime) as delta_time,t_a.machine_id as machine_id,ncIp from(select exceptionTime,machine_id,ncIp from log where exceptionName = 'cloudassitant_heartbeat_loss_high' ) t_a left join (select exceptionTime,machine_id from log where exceptionName = 'qemu_hot_upgrade_event' ) t_b on t_a.machine_id = t_b.machine_id where t_b.machine_id is not null) group by nc_ip,machine_id having min_deltatime > 0 and min_deltatime < 600
  logstore: monitor_exception_sls_alert
  slsConfigName: xunjian_zhangbei
  silent: 580
  region: [default]
  retentionTime: 15
  detailMetric: queryHeartBeatForCloudAssitant
  linkedDimensions:
    - NC_INFO
    - USER_INFO
    - VM_INFO
  levels:
    - name: warning
      expression: '{{cnt}} >= 1'
      notify: ["SlsAlertHandler"]
      phoneCall: false
      needRelatedVm: true
  tag: PerformanceImpact
  splitTask: true
  maxTraceLog: 10000
  powerSQL: false
