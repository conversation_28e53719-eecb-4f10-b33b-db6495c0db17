address.server.domain=pre-diamond.cn-hangzhou.aliyun-inc.com
rocketmq.namesrv.server=pre-diamond.cn-hangzhou.aliyun-inc.com
rocketmq.namesrv.group=nsaddr

service.dubbo.provider.version=1.0.0
service.dubbo.timeout=30000
dubbo.registry.address=aliyun-nacos-ns://pre-diamond.cn-hangzhou.aliyun-inc.com
dubbo.aliyun.nacos.enable=false
dubbo.aliyun.jmenv.check=false
dubbo.aliyun.report.enable=false

# global
aliyun.rass.enabled=false

## operation or observation
aliyun.rass.mode=observation

project.name=xdragon-metric

management.security.enabled = false

spring.datasource.jdbc-url=************************************************************************************************************************************************************************************************************************************
spring.datasource.username=metric_prod
spring.datasource.password=ENC(9mC+OYlOAjyxPV9RnyA7NA==)
spring.datasource.driver-class-name=org.mariadb.jdbc.Driver
spring.datasource.test-while-idle=true
spring.datasource.test-on-borrow=true
spring.datasource.validation-query=SELECT 1
spring.datasource.log-validation-errors=true
spring.datasource.pool-name=metric_db
spring.datasource.maximum-pool-size=20
spring.datasource.minimum-idle=2
spring.datasource.connection-timeout=60000
spring.datasource.connection-test-query=select 1

spring.adb.datasource.jdbc-url=****************************************************************************************************************************************************************************************************************************************
spring.adb.datasource.jdbc-url-short=************************************************************************************
spring.adb.datasource.username=metric_admin
spring.adb.datasource.password=ENC(niVo49unNeYSdgmVMiUhZQ==)
spring.adb.datasource.driver-class-name=org.mariadb.jdbc.Driver
spring.adb.datasource.test-while-idle=true
spring.adb.datasource.test-on-borrow=true
spring.adb.datasource.validation-query=SELECT 1
spring.adb.datasource.log-validation-errors=true
spring.adb.datasource.pool-name=metric_log_db
spring.adb.datasource.maximum-pool-size=50
spring.adb.datasource.minimum-idle=5
spring.adb.datasource.connection-timeout=60000
spring.adb.datasource.connection-test-query=select 1
spring.adb.datasource.pattern-table=log_cluster_pattern
spring.adb.datasource.detail-table=cluster_distribution_detail

spring.filter.url=********************************************************************************
spring.filter.table.name=nc_filter
spring.filter.user.name=metric_prod
spring.gamma.url=**********************************************************************
spring.gamma.table.name=ecs_release_plan_gamma_host_v2
spring.gamma.user.name=ecs_plan
spring.gamma.password=ENC(UgtIDi2wvzhs4mLaoxmwuQ==)
spring.remain.endpoint=cn-zhangjiakou-2-share.log.aliyuncs.com
spring.remain.id.project=log-cluster
spring.remain.project=xdragon-metric-ecs-log
spring.remain.logstore=remain_log
spring.library.endpoint=http://service.odps.aliyun-inc.com/api
spring.library.id=LTAI5tHSnP1rfc5Nne9TtHPL
spring.library.key=ENC(8h2z52H8oOa2HDFWifcrIRNnfdvo6LJWtFB8+Hf4JZo=)
spring.library.project=ecs_dw

spring.datasource.plan.jdbc-url=*******************************************************************************
spring.datasource.plan.username=ecs_plan
spring.datasource.plan.password=ENC(UgtIDi2wvzhs4mLaoxmwuQ==)
spring.datasource.plan.timeBetweenEvictionRunsMillis=60000
spring.datasource.plan.minEvictableIdleTimeMillis=300000
spring.datasource.plan.validationQuery=select 1
spring.datasource.plan.testWhileIdle=true
spring.datasource.plan.testOnBorrow=true
spring.datasource.plan.testOnReturn=false
spring.datasource.plan.driver-class-name=org.mariadb.jdbc.Driver
spring.datasource.plan.pool-name=plan_db
spring.datasource.plan.log-validation-errors=true
spring.datasource.plan.maximum-pool-size=20
spring.datasource.plan.minimum-idle=2
spring.datasource.plan.connection-timeout=60000
spring.datasource.plan.connection-test-query=select 1

odps.client.access_id=LTAI5tHSnP1rfc5Nne9TtHPL
odps.client.access_key=ENC(8h2z52H8oOa2HDFWifcrIRNnfdvo6LJWtFB8+Hf4JZo=)
odps.client.endpoint=http://service-cross.odps.aliyun-inc.com/api
odps.client.project=ecs_dw
odps.client.tunnel=http://dt.share.odps.aliyun-inc.com

algo.python.path=/home/<USER>/bin/python
algo.eval.script=/home/<USER>/xdragon-metric/python/EvaluationAlgorithmService.py

spring.schedulerx2.domainName=pre-schedulerx2-oxs-cn-hangzhou.aliyun-inc.com
spring.schedulerx2.groupId=xdragon-metric-default
spring.schedulerx2.appKey=Lut274vmFxMdNWaBTQB6wA==
spring.schedulerx2.enabled=true
spring.schedulerx2.shareContainerPool=false
spring.schedulerx2.namespace=system_namespace


#????????logstore
user.report.exception.logstore = user_report_exception_pre
user.report.exception.project = ecs-xunjian
user.report.exception.slsuser = newbie_ecs
user.report.exception.region = cn-hangzhou-corp

# cache redis ??
spring.redis.host = r-xdragon-metric-pre.redis.rds.aliyuncs.com
spring.redis.port = 6379
spring.redis.password = ENC(qX2QRTJKfezoVWKRhMzau2STIhw4ry9IPcW9TbaMVkE=)
spring.redis.database = 1
spring.redis.timeout=10000
spring.redis.lettuce.pool.max-active = 100
spring.redis.lettuce.pool.max-wait = 10000
spring.redis.lettuce.pool.max-idle = 100
spring.redis.lettuce.pool.min-idle = 5

#predict tsdb
predict.tsdb.sls.user = newbie_ecs
predict.tsdb.sls.region = cn-wulanchabu
predict.tsdb.sls.project = ecs-predict-result
predict.tsdb.sls.logStore = xdragon-metric
predict.tsdb.sls.logStorePickUp = xdragon-metric-pickup-anomaly

log.monitor.sls.user = newbie_ecs
log.monitor.sls.region = cn-wulanchabu
log.monitor.sls.project = ecs-predict-result
log.monitor.sls.logStore = log_monitor_dev

log.index.monitor.sls.user = newbie_ecs
log.index.monitor.sls.region = cn-wulanchabu
log.index.monitor.sls.project = ecs-predict-result
log.index.monitor.sls.logStore = log_index_monitor_dev

log.sla.sls.user = newbie_ecs
log.sla.sls.region = cn-wulanchabu
log.sla.sls.project = ecs-predict-result
log.sla.sls.logStore = third_part_service

log.eventcenter.user = newbie_ecs
log.eventcenter.region = cn-wulanchabu
log.eventcenter.project = event-center
log.eventcenter.logStore = full_event
log.eventcenter.userCpuLogStore = user_perfromance_cpu_info
log.eventcenter.userNetLogStore = user_perfromance_net_info
log.eventcenter.userVmCountLogStore = user_vm_count
log.eventcenter.userRunningVmCountLogStore = user_running_vm_count
log.eventcenter.userRunningVmLogStore = user_running_vms
log.eventcenter.userVmCpuLogStore = vm_perfromance_cpu_info
log.eventcenter.userVmNetLogStore = vm_perfromance_net_info_refine
log.eventcenter.userVmDiskLogStore = vm_disk_io_union_dim
log.eventcenter.userDiskLogStore = user_disk_io
log.eventcenter.ecsInstanceLogStore = ecs_instance_type_define
log.eventcenter.diskIoLimitLogStore=vm_disk_io_with_specification_launch
log.diskinfosta.sls.user=newbie_ecs
log.diskinfosta.sls.region=cn-wulanchabu
log.diskinfosta.sls.project=disk-info-sta
log.diskinfosta.sls.ncDiskIoLimitLogStore=nc_disk_io_with_limit
log.diskinfosta.sls.aliuidDiskIoLimitLogStore=aliuid_disk_io_with_limit

log.anomaly.sls.user = newbie_ecs
log.anomaly.sls.region = cn-wulanchabu
log.anomaly.sls.project = ecs-predict-result
log.anomaly.sls.logStore = xdragon-metric-anomaly
log.anomaly.sls.patternStore = xdragon-metric-log-pattern
log.anomaly.sls.metricStore = xdragon-metric
log.anomaly.aone.project= 1182328
log.anomaly.sls.batchLogStore = xdragon-metric-batch-anomaly
log.cluster.sls.user=newbie_ecs
log.cluster.sls.region=cn-zhangjiakou-2
log.cluster.sls.project=log-cluster
log.cluster.sls.logStore=remain_log
log.cluster.sls.mappingStore = log_mapping
log.cluster.sls.distributionStore = log_distribution
log.cluster.sls.remainStore = log_remain

log.alert.history.sls.user = newbie_ecs
log.alert.history.sls.region = cn-hangzhou-corp
log.alert.history.sls.project = ecs-xunjian
log.alert.history.sls.logStore = alert_notify_history

log.release.sls.user = newbie_ecs
log.release.sls.region = cn-shanghai-corp
log.release.sls.project = ecs-release-plan
log.release.sls.logStore = ecs_change_plan_deploy_log_raw
log.release.sls.byVmLogStore = ecs_change_plan_deploy_log_raw_by_vm
log.release.sls.appDeployLogStore=ecs_app_aone_deploy_log

log.remain.sls.user = newbie_ecs
log.remain.sls.region = cn-zhangjiakou-2
log.remain.sls.project = xdragon-metric-ecs-log
log.remain.sls.logStore = remain_log


log.alert.meta.sls.user = newbie_ecs
log.alert.meta.sls.region = cn-hangzhou-corp
log.alert.meta.sls.project = ecs-xunjian
log.alert.meta.sls.logStore = monitor_exception_sls_alert

log.metric.daily.sls.user = newbie_ecs
log.metric.daily.sls.region = cn-wulanchabu
log.metric.daily.sls.project = ecs-predict-result
log.metric.daily.sls.logStore = xdragon-metric-ops-dev

flink.metric.sls.user = newbie_ecs
flink.metric.sls.region = cn-zhangjiakou-2
flink.metric.sls.project = xdragon-metric-ecs-log
flink.metric.sls.logStore = flink_task_metric
flink.metric.sls.logQualityLogstore = log_task_quality

metric.daily.oss.user = newbie_ecs
metric.daily.oss.endpoint=https://oss-cn-hangzhou-zmf.aliyuncs.com
metric.daily.oss.bucket=xdragon-metric-ops-daily

chat.oss.user = newbie_ecs
chat.oss.endpoint = https://oss-cn-hangzhou-zmf.aliyuncs.com
chat.oss.bucket = xdragon-metric-agent-corpus

chat.test.oss.user = newbie_ecs
chat.test.oss.endpoint = https://oss-cn-zhangjiakou.aliyuncs.com
chat.test.oss.bucket = xdragon-metric-chain-test

xdragon.metric.oss.user=xdragon-metric
xdragon.metric.oss.endpoint=https://oss-cn-hangzhou.aliyuncs.com
xdragon.metric.oss.bucket=xdragon-metric-image

# root cause detect
cloud.ops.regionId=center
cloud.ops.domain=ecsops-share.aliyuncs.com
cloud.ops.region.hz=cn-hangzhou
cloud.ops.domain.hz=ecsops-share.cn-hangzhou.aliyuncs.com
cloud.ops.accessKey=LTAI5tDKDkonWmgUJspaKbeP
cloud.ops.accessSecret=ENC(lS+otyDq5v2UxZAMKKJhJwIghoIUZf/t/w9Ai2kBuSM=)
cloud.ecs.inner.regionId=cn-hangzhou
cloud.ecs.inner.domain=ecs-api-share.cn-hangzhou.aliyuncs.com
cloud.consistent.domain=ecsops-share.cn-zhangjiakou.aliyuncs.com
cloud.consistent.region=cn-zhangjiakou

# eas service
eas.token.firstLabel=NWYwMjBjODIyOWYyNmFlNTAyYTI2YjIyOWMxYWM0ZDNmZjU1ZGJmYQ==
eas.token.secondaryLabel=ZWU1YmY1MTEwYTY2NTA2NzM4ZTQ1MDE4NGZjYTYwNTBhYTk4MDQ0ZQ==
eas.token.sda=ZGIyNzA1OWI3MzAyOTU3MTVhMTMyNzhlOTRhYmE3NTg4YTc2NThmNg==
eas.url.firstLabel=https://1833984915394642.cn-hangzhou.pai-eas.aliyuncs.com
eas.url.secondaryLabel=https://1833984915394642.cn-hangzhou.pai-eas.aliyuncs.com
eas.url.sda=sda-prediction.zhangbei-b.eas.vipserver
eas.model.name=classification
eas.url.type=endpoint


# aone service
aone.appName=ecs-alarm-ceter
aone.secretKey=ENC(7sRKFo4NMcjkEIBrUEPvvCF8hOuBF276lkPuZE0mPag=)
aone.url=http://prepub-aone-api.alibaba-inc.com/issue/openapi
aone.sls.logstore=issue_tracing
aone.sls.consumerGroup=consumerGroupX
aone.sls.enabled=false

#top api
top.appKey=34347971
top.secret=ENC(xtFT18yfPr2s1jcsKcYAhzvHf8fO0VSTSmp1HJwEx/Gjnj0cT2eQfc0KzHoWn1bR)
top.url=http://cross.api.taobao.com/router/rest

# key center
xdragon.keycenter.enabled=true
keycenter.app.publish=083962afcf7141e1922b0d8115b9267c
keycenter.http.service=http://circe-service.alibaba-inc.com/keycenter
keycenter.decrypt.key=xdragon-metric_aone_key

# work item
work.item.aone.project=832307

# auto encrypt/decrypt
jasypt.encryptor.bean=metricEncryptor

cluster.task.fake=false

manager.blink.regionId=center
manager.blink.accessId=LTAI5tDKDkonWmgUJspaKbeP
manager.blink.accessKey=ENC(lS+otyDq5v2UxZAMKKJhJwIghoIUZf/t/w9Ai2kBuSM=)
manager.blink.headerRegionId=cn-hangzhou-internal
manager.blink.employeeId=370713
manager.blink.projectName=ecs_dw
manager.blink.folderName=/LogAnalysis
manager.blink.packageName=log-cluster-2.0.0-jar-with-dependencies.jar
manager.blink.folderId=564556
manager.blink.endpoint=foas.aliyuncs.com

manager.vvp.workspace=default
manager.vvp.namespace=xdragondw
manager.vvp.endpoint=ververica-share.aliyuncs.com
manager.vvp.pilotEndpoint=autopilot-inner-share.aliyuncs.com
manager.vvp.accessId=LTAI5tDKDkonWmgUJspaKbeP
manager.vvp.accessKey=ENC(lS+otyDq5v2UxZAMKKJhJwIghoIUZf/t/w9Ai2kBuSM=)
manager.vvp.engineVersion=vvr-8.0.11-flink-1.17
manager.vvp.deploymentTargetName=asi-zjk-flink-c02-vol1_na63blinkcptssd1_upb-xm-logcluster

server.log.name=externalServerLogger
server.log.source=xdragonMetric

xdragon.fast-access.enabled=false

idns.app.access_id=ecs-alarm-center_api
idns.app.access_key=ENC(3WmMLZwoT/R/Fz9GrxaPId+7CDC5tHdSo1cQUXw5lXw=)

edm.appId=mioyIcQU
edm.appSecret=ENC(+FiBd2sw7D4czAJuVCsOo6SaDT1YaPacnT3OPTKT+mlmHo2eDrlvPbaA3HlllErX)
edm.endpoint=http://ecs-info-center-service.aliyun-inc.com

sls.trend.logstore=log_cluster_count_trend_dev

xdragon.metric.chat.host=pre-xmca-cloudbot.aliyun-inc.com
ding.talk.appKey=ding7kpnyytq6gnjqbup
ding.talk.appSecret=l7sq1_J_vgKpGx_E90naHovcwcRiEKon4YOaiAzBoVyerwvI0HI21CXoymtV4tkk
ding.talk.cardTemplateId=1ece39e1-1e5c-4428-9991-0c137d6dedd2.schema

bpms.authKey=ENC(0N+ISMaOlwt43sYl1CgWMThZUCPQfabU3m58LiJEyICy0djldjmTUzPlMDUM+Q7D1D+7nWV3ijM1OQsm32CnaQ==)
epaas.domainName=s-api.alibaba-inc.com
epaas.accessKey=ecs-cloudops-webv2-ali-IUNneE3
epaas.secretKey=ENC(wuk8/9cm7cbym56TDZGscu0NeHjjgmY16LG5Uzc1AHn9vVd9OxITCaSHKnwZA+Cb)
acl.accessKey=ecs-cloudops-webv2-ali-IUNneE3
acl.api.enabled=false

xdragon.metric.aone.consumer.id = CID_ecs_aone_project_mq
xdragon.metric.aone.deploy.consumer.id=CID_ecs_aone_deploy_mq
xdragon.metric.aone.deploy.gray.consumer.id=CID_ecs_aone_deploy_gray_mq

gts.openapi.enabled=true
gts.openapi.api-env=staging
gts.openapi.app-key=204646002
gts.openapi.app-secret=ENC(f9lcNfSIjDW84BQwKJjoPbiYjqGNKqLdvqAMEDyr8j8mOEeOTMAppRbWFGCRz9Ps)

metric.ots.endpoint=http://ecs-ops-feature.cn-beijing.ots-inner.aliyuncs.com
metric.ots.instance=ecs-ops-feature
metric.ots.accessId=LTAIFZ21yr6Henkj
metric.ots.accessKey=ENC(aioi0QjsrKjtZdd1vXO86Asox+q4nsQxMl9eeZaaA3g=)
metric.ots.table=xdragon_metric_ops

mq.clouddoc.permission.enabled=false
mq.clouddoc.permission.instance.id=rmq-cn-o493t6az601
mq.clouddoc.permission.instance.endpoint=cn-hangzhou.rmq.aliyuncs.com:8080
mq.clouddoc.permission.instance.user=9T004iGwq5ZMbnv2
mq.clouddoc.permission.instance.password=ENC(zpwVluzpE0uk9Hy020GTl7WVXIREPDqZMYU6YqMh4c0=)
mq.clouddoc.permission.instance.topic=t_tam_permission
mq.clouddoc.permission.instance.groupId=g_tam_permission

log.allinonerisk.sls.user = newbie_ecs
log.allinonerisk.sls.region = cn-wulanchabu
log.allinonerisk.sls.project = ecs-predict-result
log.allinonerisk.sls.logStore = allinonerisk
log.allinonerisk.sls.dataLogStore = allinonerisk-datasource
log.allinonerisk.sls.ecsDataLogStore = risk_influence_ecs_detail
log.allinonerisk.sls.batchRiskLogStore = metric_batch_risk

yichangdiaodu.yuque.book = kgythi/cohg6w
yichangdiaodu.yuque.uri = https://yuque.antfin-inc.com/api/v2
yichangdiaodu.yuque.agent = yichangdiaoduzu
yichangdiaodu.yuque.token = ENC(d6nIGCvW2LxLXWggM6/OWP7OfJpVPpqP8YjKmKO1gQideMT9gvFcH1mTsfDnn3US)

yunqi.auth.host = pre-tool-stack-dag.aliyun-inc.com
yunqi.auth.path = /v1/productResearchTool/ecs/authenticate

cloud.auth.regionId = cn-shanghai
cloud.auth.domain = aliyunauth-pre.aliyuncs.com

log.union.sls.user = newbie_ecs
log.union.sls.region = cn-zhangjiakou-2
log.union.sls.project = log-cluster

log.region.odps.endpoint = http://service.odps.aliyun-inc.com/api
log.region.odps.table-name = houyiregiondb_filter_nc
log.region.odps.project = ecs_dw
log.region.odps.user=odps_xdragon_metric

sysom.host = pre-sysom.alibaba-inc.com
sysom.username = clouddoc
sysom.password = SlXwZv179mkbniKMj8CYuQ==

component.tag.init = true

bailian.mns.user = bailian_mns
bailian.mns.ak = LTAI5tDMtqbqMhzKAZBxtkVu
bailian.mns.sk = ENC(chHRAuB62hQv1SC3dTX2CBg9pr/Zc4ASiEL4cf6D9GM=)

domain.cloudbot = cloudbot2.aliyun-inc.com
domain.ticket = ticket.aliyun-inc.com
domain.yanglin = an.aliyun-inc.com
domain.infocenter = ecs-info-center.alibaba-inc.com
domain.aone = aone.alibaba-inc.com
domain.sls.console = sls.console.aliyun.com
domain.vvp = vvp.alibaba-inc.com
domain.skyline = skyline.alibaba-inc.com
domain.fbi = fbi.alibaba-inc.com

bailian.prod = false
bailian.apiKey = CIaGf4FYFMbAo6Dp2RcU8l8MzMUIwO3sIQCmrojgWOKY6AgFgIW35PTngcSL2wMG