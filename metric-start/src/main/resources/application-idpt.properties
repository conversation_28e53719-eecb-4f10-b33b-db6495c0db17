address.server.domain=${env_common_address_server}
rocketmq.namesrv.server=${env_common_address_server}
rocketmq.namesrv.group=nsaddr

service.dubbo.provider.version=1.0.0
service.dubbo.timeout=30000
dubbo.registry.address=aliyun-nacos-ns://${env_common_aliyun_nacos_ns}

dubbo.aliyun.nacos.enable=false
dubbo.aliyun.jmenv.check=false
dubbo.aliyun.report.enable=false

project.name=xdragon-metric

management.security.enabled = false


spring.datasource.jdbc-url=jdbc:mariadb://${env_res_rds_url}:3306/xdragon-metric?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&autoReconnectForPools=true&zeroDateTimeBehavior=convertToNull&trackSchema=false
spring.datasource.username=metric_prod
spring.datasource.password=${env_enc_spring_datasource_password}
spring.datasource.driver-class-name=org.mariadb.jdbc.Driver
spring.datasource.test-while-idle=true
spring.datasource.test-on-borrow=true
spring.datasource.validation-query=SELECT 1
spring.datasource.log-validation-errors=true
spring.datasource.pool-name=metric_db
spring.datasource.maximum-pool-size=20
spring.datasource.minimum-idle=2
spring.datasource.connection-timeout=60000
spring.datasource.connection-test-query=select 1

spring.adb.datasource.jdbc-url=jdbc:mariadb://${env_res_rds_url}:3306/xdragon-metric?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&autoReconnectForPools=true&zeroDateTimeBehavior=convertToNull&trackSchema=false
spring.adb.datasource.jdbc-url-short=jdbc:mysql://${env_res_rds_url}/xdragon-metric
spring.adb.datasource.username=metric_prod
spring.adb.datasource.password=${env_enc_spring_datasource_password}
spring.adb.datasource.driver-class-name=org.mariadb.jdbc.Driver
spring.adb.datasource.test-while-idle=true
spring.adb.datasource.test-on-borrow=true
spring.adb.datasource.validation-query=SELECT 1
spring.adb.datasource.log-validation-errors=true
spring.adb.datasource.pool-name=metric_log_db
spring.adb.datasource.maximum-pool-size=50
spring.adb.datasource.minimum-idle=5
spring.adb.datasource.connection-timeout=60000
spring.adb.datasource.connection-test-query=select 1
spring.adb.datasource.pattern-table=log_cluster_pattern
spring.adb.datasource.detail-table=cluster_distribution_detail

spring.filter.url=jdbc:mysql://${env_res_rds_url}/xdragon-metric
spring.filter.table.name=nc_filter
spring.filter.user.name=metric_prod

spring.gamma.url=jdbc:mysql://${env_res_rds_url}/ecs_plan_meta
spring.gamma.table.name=ecs_release_plan_gamma_host_v2
spring.gamma.user.name=metric_prod
spring.gamma.password=${env_enc_spring_datasource_password}
spring.remain.endpoint=to_be_defined
spring.remain.id.project=log-cluster
spring.remain.project=xdragon-metric-ecs-log
spring.remain.logstore=remain_log
spring.library.endpoint=http://to_be_defined
spring.library.id=to_be_defined
spring.library.key=to_be_defined
spring.library.project=ecs_dw

spring.datasource.plan.jdbc-url=jdbc:mariadb://${env_res_rds_url}:3306/ecs_changeplan_meta
spring.datasource.plan.username=metric_prod
spring.datasource.plan.password=${env_enc_spring_datasource_password}
spring.datasource.plan.timeBetweenEvictionRunsMillis=60000
spring.datasource.plan.minEvictableIdleTimeMillis=300000
spring.datasource.plan.validationQuery=select 1
spring.datasource.plan.testWhileIdle=true
spring.datasource.plan.testOnBorrow=true
spring.datasource.plan.testOnReturn=false
spring.datasource.plan.driver-class-name=org.mariadb.jdbc.Driver
spring.datasource.plan.pool-name=plan_db
spring.datasource.plan.log-validation-errors=true
spring.datasource.plan.maximum-pool-size=20
spring.datasource.plan.minimum-idle=2
spring.datasource.plan.connection-timeout=60000
spring.datasource.plan.connection-test-query=select 1

odps.client.access_id=to_be_defined
odps.client.access_key=to_be_defined
odps.client.endpoint=http://to_be_defined
odps.client.project=ecs_dw
odps.client.tunnel=http://to_be_defined

algo.python.path=/home/<USER>/bin/python
algo.eval.script=/home/<USER>/xdragon-metric/python/EvaluationAlgorithmService.py

spring.schedulerx2.domainName=${env_common_schedulerx_domain}
spring.schedulerx2.groupId=xdragon-metric
spring.schedulerx2.appKey=${env_res_schedulerx_app_key}
spring.schedulerx2.enabled=true
spring.schedulerx2.shareContainerPool=false
#spring.schedulerx2.namespace=system_namespace

user.report.exception.logstore = user_report_exception
user.report.exception.project = ecs-xunjian
user.report.exception.slsuser = newbie_ecs
user.report.exception.region = cn-hangzhou-corp

spring.redis.host =${env_res_redis_url}
spring.redis.port = 6379
spring.redis.password = ${env_enc_spring_redis_password}
spring.redis.database = 1
spring.redis.timeout=10000
spring.redis.lettuce.pool.max-active = 100
spring.redis.lettuce.pool.max-wait = 10000
spring.redis.lettuce.pool.max-idle = 100
spring.redis.lettuce.pool.min-idle = 5

#predict tsdb
predict.tsdb.sls.user = newbie_ecs
predict.tsdb.sls.region = cn-wulanchabu
predict.tsdb.sls.project = ecs-predict-result
predict.tsdb.sls.logStore = xdragon-metric
predict.tsdb.sls.logStorePickUp = xdragon-metric-pickup-anomaly

log.monitor.sls.user = newbie_ecs
log.monitor.sls.region = cn-wulanchabu
log.monitor.sls.project = ecs-predict-result
log.monitor.sls.logStore = log_monitor

log.index.monitor.sls.user = newbie_ecs
log.index.monitor.sls.region = cn-wulanchabu
log.index.monitor.sls.project = ecs-predict-result
log.index.monitor.sls.logStore = log_index_monitor

log.sla.sls.user = newbie_ecs
log.sla.sls.region = cn-wulanchabu
log.sla.sls.project = ecs-predict-result
log.sla.sls.logStore = third_part_service

log.eventcenter.user = newbie_ecs
log.eventcenter.region = cn-wulanchabu
log.eventcenter.project = event-center
log.eventcenter.logStore = full_event
log.eventcenter.userCpuLogStore = user_perfromance_cpu_info
log.eventcenter.userNetLogStore = user_perfromance_net_info
log.eventcenter.userVmCountLogStore = user_vm_count
log.eventcenter.userRunningVmCountLogStore = user_running_vm_count
log.eventcenter.userRunningVmLogStore = user_running_vms
log.eventcenter.userVmCpuLogStore = vm_perfromance_cpu_info
log.eventcenter.userVmNetLogStore = vm_perfromance_net_info_refine
log.eventcenter.userVmDiskLogStore = vm_disk_io_union_dim
log.eventcenter.userDiskLogStore = user_disk_io
log.eventcenter.ecsInstanceLogStore = ecs_instance_type_define
log.eventcenter.diskIoLimitLogStore=vm_disk_io_with_specification_launch
log.diskinfosta.sls.user=newbie_ecs
log.diskinfosta.sls.region=cn-wulanchabu
log.diskinfosta.sls.project=disk-info-sta
log.diskinfosta.sls.ncDiskIoLimitLogStore=nc_disk_io_with_limit
log.diskinfosta.sls.aliuidDiskIoLimitLogStore=aliuid_disk_io_with_limit

log.anomaly.sls.user = newbie_ecs
log.anomaly.sls.region = cn-wulanchabu
log.anomaly.sls.project = ecs-predict-result
log.anomaly.sls.logStore = xdragon-metric-anomaly
log.anomaly.sls.patternStore = xdragon-metric-log-pattern
log.anomaly.sls.metricStore = xdragon-metric
log.anomaly.aone.project= 1190449
log.anomaly.sls.batchLogStore = xdragon-metric-batch-anomaly
log.cluster.sls.user=newbie_ecs
log.cluster.sls.region=cn-zhangjiakou-2
log.cluster.sls.project=log-cluster
log.cluster.sls.logStore=remain_log
log.cluster.sls.mappingStore = log_mapping
log.cluster.sls.distributionStore = log_distribution
log.cluster.sls.remainStore = log_remain

log.alert.history.sls.user = newbie_ecs
log.alert.history.sls.region = cn-hangzhou-idpt-2
log.alert.history.sls.project = ecs-xunjian
log.alert.history.sls.logStore = alert_notify_history

log.release.sls.user = newbie_ecs
log.release.sls.region = cn-hangzhou-idpt-2
log.release.sls.project = ecs-release-plan
log.release.sls.logStore = ecs_change_plan_deploy_log_raw

log.remain.sls.user = newbie_ecs
log.remain.sls.region = cn-hangzhou-idpt-2
log.remain.sls.project = xdragon-metric-ecs-log
log.remain.sls.logStore = remain_log


log.alert.meta.sls.user = newbie_ecs
log.alert.meta.sls.region = cn-hangzhou-idpt-2
log.alert.meta.sls.project = ecs-xunjian
log.alert.meta.sls.logStore = monitor_exception_sls_alert

log.metric.daily.sls.user = newbie_ecs
log.metric.daily.sls.region = cn-hangzhou-idpt-2
log.metric.daily.sls.project = ecs-predict-result
log.metric.daily.sls.logStore = xdragon-metric-ops

flink.metric.sls.user = newbie_ecs
flink.metric.sls.region = cn-hangzhou-idpt-2
flink.metric.sls.project = xdragon-metric-ecs-log
flink.metric.sls.logStore = flink_task_metric
flink.metric.sls.logQualityLogstore = log_task_quality


metric.daily.oss.user = newbie_ecs
metric.daily.oss.endpoint=https://to_be_defined
metric.daily.oss.bucket=xdragon-metric-ops

chat.oss.user = newbie_ecs
chat.oss.endpoint = https://to_be_defined
chat.oss.bucket = xdragon-metric-chain-corpus

chat.test.oss.user = newbie_ecs
chat.test.oss.endpoint = https://to_be_defined
chat.test.oss.bucket = xdragon-metric-chain

xdragon.metric.oss.user=xdragon-metric
xdragon.metric.oss.endpoint=https://to_be_defined
xdragon.metric.oss.bucket=xdragon-metric-image

# root cause detect, use outer cloud ak
cloud.ops.regionId=cn-hangzhou-idpt-2
cloud.ops.domain=${env_common_ecs_ops_domain}
cloud.ops.region.hz=cn-hangzhou-idpt-2
cloud.ops.domain.hz=${env_common_ecs_ops_domain}
cloud.ops.accessKey=${env_common_outer_ak}
cloud.ops.accessSecret=${env_enc_aliyun_ops_accessSecret}
cloud.ecs.inner.regionId=${env_common_outer_region}
cloud.ecs.inner.domain=${env_common_ecs_inner_domain}
cloud.consistent.domain=${env_common_ecs_ops_domain}
cloud.consistent.region=${env_common_outer_region}
# eas service
eas.token.firstLabel=NzVlNWUwM2MwMGVmOWM2MGEzODUyMjgyYzU5ZDJlY2ZjNTZkNGEzMA==
eas.token.secondaryLabel=YWI1MzE4YzgyZDU3MTEzMjU3NTcwMjVjNThkYWFjYzFmMzdlMzUwOQ==
eas.token.sda=ZGIyNzA1OWI3MzAyOTU3MTVhMTMyNzhlOTRhYmE3NTg4YTc2NThmNg==
eas.url.firstLabel=eas-zhangbei.alibaba-inc.com
eas.url.secondaryLabel=eas-zhangbei-b.alibaba-inc.com
eas.url.sda=sda-prediction.zhangbei-b.eas.vipserver
eas.model.name=eas
eas.url.type=vipserver


# aone service
aone.appName=ecs-alarm-ceter
aone.secretKey=to_be_defined
aone.url=http://to_be_defined
aone.sls.logstore=issue_tracing
aone.sls.consumerGroup=aone_create
aone.sls.enabled=false

#top api
top.appKey=34347971
top.secret=to_be_defined
top.url=http://to_be_defined

# key center
xdragon.keycenter.enabled=true
keycenter.app.publish=${env_res_kc_number}
keycenter.http.service=${env_common_keycenter_http_service}
keycenter.decrypt.key=${env_res_kc_key_name}

# work item
work.item.aone.project=1190449

# auto encrypt/decrypt
jasypt.encryptor.bean=metricEncryptor

cluster.task.fake=true

manager.blink.regionId=center
manager.blink.accessId=to_be_defined
manager.blink.accessKey=to_be_defined
manager.blink.headerRegionId=cn-hangzhou-internal
manager.blink.employeeId=370713
manager.blink.projectName=ecs_dw
manager.blink.folderName=/LogAnalysis
manager.blink.packageName=log-cluster-2.0.0-jar-with-dependencies.jar
manager.blink.folderId=564556
manager.blink.endpoint=foas.aliyuncs.com

manager.vvp.workspace=default
manager.vvp.namespace=xdragondw
manager.vvp.endpoint=ververica-share.aliyuncs.com
manager.vvp.pilotEndpoint=autopilot-inner-share.aliyuncs.com
manager.vvp.accessId=to_be_defined
manager.vvp.accessKey=to_be_defined
manager.vvp.engineVersion=vvr-8.0.6-flink-1.17
manager.vvp.deploymentTargetName=asi-zjk-flink-c01_na61blinkcptssd1_upb-aiops

server.log.name=externalServerLogger
server.log.source=xdragonMetric

xdragon.fast-access.enabled=false

idns.app.access_id=to_be_defined
idns.app.access_key=to_be_defined

edm.appId=mioyIcQU
edm.appSecret=to_be_defined
edm.endpoint=http://${env_edm_domain}

sls.trend.logstore=log_cluster_count_trend

xdragon.metric.chat.host=pre-xmca-cloudbot.aliyun-inc.com
ding.talk.appKey=ding7kpnyytq6gnjqbup
ding.talk.appSecret=l7sq1_J_vgKpGx_E90naHovcwcRiEKon4YOaiAzBoVyerwvI0HI21CXoymtV4tkk
ding.talk.cardTemplateId=1ece39e1-1e5c-4428-9991-0c137d6dedd2.schema

bpms.authKey=to_be_defined
epaas.domainName=s-api.alibaba-inc.com
epaas.accessKey=ecs-cloudops-webv2-ali-IUNneE3
epaas.secretKey=to_be_defined
acl.accessKey=ecs-cloudops-webv2-ali-IUNneE3
acl.api.enabled=false

xdragon.metric.aone.consumer.id = CID_ecs_aone_project_mq
xdragon.metric.aone.deploy.consumer.id=CID_ecs_aone_deploy_mq
xdragon.metric.aone.deploy.gray.consumer.id=CID_ecs_aone_deploy_gray_mq

gts.openapi.enabled=true
gts.openapi.api-env=staging
gts.openapi.app-key=204646002
gts.openapi.app-secret=to_be_defined

metric.ots.endpoint=http://ecs-ops-feature.to_be_defined.com
metric.ots.instance=ecs-ops-feature
metric.ots.accessId=ak
metric.ots.accessKey=sk
metric.ots.table=xdragon_metric_ops

mq.clouddoc.permission.enabled=false
mq.clouddoc.permission.instance.id=rmq-cn-zim3syhx601
mq.clouddoc.permission.instance.endpoint=cn-hangzhou-dipt-2.rmq.aliyuncs.com:8080
mq.clouddoc.permission.instance.user=tJdPyhtx4YQpNPtb
mq.clouddoc.permission.instance.password=to_be_defined
mq.clouddoc.permission.instance.topic=t_tam_permission
mq.clouddoc.permission.instance.groupId=g_tam_permission

log.allinonerisk.sls.user = newbie_ecs
log.allinonerisk.sls.region = cn-hangzhou-idpt-2
log.allinonerisk.sls.project = ecs-predict-result
log.allinonerisk.sls.logStore = allinonerisk
log.allinonerisk.sls.dataLogStore = allinonerisk-datasource

yichangdiaodu.yuque.book = kgythi/cohg6w
yichangdiaodu.yuque.uri = https://yuque.antfin-inc.com/api/v2
yichangdiaodu.yuque.agent = yichangdiaoduzu
yichangdiaodu.yuque.token = to_be_defined

yunqi.auth.host = to_be_defined
yunqi.auth.path = /v1/productResearchTool/ecs/authenticate
cloud.auth.regionId=cn-hangzhou-idpt-2
cloud.auth.domain=aliyunauth-pre.cn-hangzhou-idpt-2.com

log.region.odps.endpoint = http://service.odps.aliyun-inc.com/api
log.region.odps.table-name = houyiregiondb_filter_nc
log.region.odps.project = ecs_dw
log.region.odps.user = ecs_dw

sysom.host = to_be_defined
sysom.username = clouddoc
sysom.password = to_be_defined

component.tag.init = false

# global
aliyun.rass.enabled=false

## operation or observation
aliyun.rass.mode=observation

bailian.mns.user = bailian_mns
bailian.mns.ak = ak
bailian.mns.sk = sk

domain.cloudbot = cloudbot2.aliyun-inc.com
domain.ticket = ticket.aliyun-inc.com
domain.yanglin = an.aliyun-inc.com
domain.infocenter = ecs-info-center.alibaba-inc.com
domain.aone = aone.alibaba-inc.com
domain.sls.console = sls.console.aliyun.com
domain.vvp = vvp.alibaba-inc.com
domain.skyline = skyline.alibaba-inc.com
domain.fbi = fbi.alibaba-inc.com

bailian.prod = false
bailian.apiKey = CIaGf4FYFMbAo6Dp2RcU8l8MzMUIwO3sIQCmrojgWOKY6AgFgIW35PTngcSL2wMG