nc_exception_push_dingjie:
  interval: 86400
  timeRange: 1000
  enable: true
  doc: '物理机异常推送定界'
  type: nc_cpu_available
  query: ''
  analyse: ''
  region: [default]
  logstore: user_report_exception
  slsConfigName: ecs_inspector
  linkedDimensions: [VM_INFO,NC_INFO]
  silent: 3580
  tag: Unavailable
  levels:
    - name: normal
      expression: '{{ncCnt}} == 1'
      notify: [ SlsAlertHandler ]

vm_exception_push_dingjie:
  interval: 86400
  timeRange: 1000
  enable: true
  doc: '实例异常推送定界'
  type: vm_cpu_available
  query: ''
  analyse: ''
  logstore: user_report_exception
  region: [default]
  slsConfigName: ecs_inspector
  linkedDimensions: [VM_INFO,NC_INFO]
  silent: 3580
  tag: Unavailable
  levels:
    - name: normal
      expression: '{{vmCnt}} == 1'
      notify: [ SlsAlertHandler ]

virt_nc_down_reason_sync:
  interval: 86400
  timeRange: 1000
  enable: true
  doc: '虚拟化人工定位宕机原因'
  type: nc_virt_event
  query: ''
  analyse: ''
  logstore: user_report_exception
  region: [default]
  slsConfigName: ecs_inspector
  linkedDimensions: [NC_INFO]
  silent: 3580
  tag: Unavailable
  levels:
    - name: normal
      expression: '{{error}} == 1'
      notify: [ SlsAlertHandler ]
      needRelatedVm: false

mock_test_exception:
  interval: 62
  timeRange: 85
  enable: true
  doc: '规模故障测试假异常'
  type: nc_controller_event
  query: 'exceptionName: mock_test_exception'
  analyse: select 1 as error,ncIp as nc_ip,exceptionTime as exception_time,cluster_alias as cluster_name group by nc_ip,exception_time,cluster_alias limit 20000
  logstore: bajie_process_nc_hang
  slsConfigName: ecs_inspector
  silent: 280
  retentionTime: 60
  powerSQL: true
  linkedDimensions: [NC_INFO]
  internal: true
  maxTraceLog: 500
  region: [default]
  tag: Event
  levels:
    - name: warning
      expression: '{{error}} == 1'
      notify: [SlsAlertHandler]
      persistent: false

# test
ehpc_custom_exception_test:
  interval: 59
  timeRange: 60
  enable: true
  doc: 'EHPC 自定义采集项异常测试'
  type: pod_ehpc_event
  query: '* and sub_type: test'
  analyse: select resource_id as machine_id, concat(json_extract_scalar("additional_info", '$.instance_id'), '|') as extension, json_extract_scalar("additional_info", '$.container_name') as ext3, reason, level as event_level limit 1000
  logstore: ehpc_custom_exception
  slsConfigName: ecs_xunjian2
  silent: 300
  retentionTime: 5
  tag: Unavailable
  linkedDimensions: [ NC_INFO,VM_INFO,USER_INFO ]
  levels:
    - name: warning
      expression: '"{{event_level}}" in ["WARNING"]'
      notify: [ SlsAlertHandler ]
      persistent: true

idc_network_device_error:
  interval: 86400
  timeRange: 1000
  enable: true
  doc: '物理机网络设备或光纤出现问题'
  type: nc_network_hardware
  query: ''
  analyse: ''
  region: [default]
  logstore: user_report_exception
  slsConfigName: ecs_inspector
  linkedDimensions: [VM_INFO,NC_INFO]
  silent: 3580
  tag: Unavailable
  levels:
    - name: normal
      expression: '{{error}} == 1'
      notify: [ SlsAlertHandler ]