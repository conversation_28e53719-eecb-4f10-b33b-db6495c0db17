rack_power_warn_by_sell_rate:
  interval: 800
  timeRange: 900
  enable: true
  doc: '整机柜售卖率过高导致超电'
  type: rack_power_hardware
  query: '(exceptionName: idc_rack_power_high_event or exceptionName: idc_rack_power_high_occur) and isTestAccount: False and not cluster_alias: AY478G'
  analyse: select rackId as machine_id,ncIp as nc_ip,instanceTypeFamily group by rackId,ncIp,instanceTypeFamily HAVING not instanceTypeFamily like '%gn%' and not instanceTypeFamily like '%ga%' limit 2000
  logstore: 'monitor_exception_sls_alert'
  slsConfigName: xunjian_zhangbei
  silent: 880
  region: [default]
  linkedQueryList: [queryNcSellRate(nc_ip@or)]
  detailMetric: queryRackPowerDetail
  linkedDimensions: [MULTI_NC_INFO,NC_INFO]
  retentionTime: 8
  tag: PerformanceImpact
  postCalculation:
    groupByFields: [machine_id]
    calculations:
      average__cpu_sell_rate: '1.0 * {{total_used_cpu}} / {{vcpu}}'
  levels:
    - name: critical
      expression: '{{cpu_sell_rate}} >= 0.8'
      notify: [SlsAlertHandler]

# idc拉取未维修工单定时任务
query_idc_unrepaired_machine:
  interval: 30
  timeRange: 100
  enable: true
  doc: '定时拉取idc维修工单'
  type: nc_all_hardware
  query: 'beat_taskid:*'
  analyse: select '127.0.0.1' as nc_ip, 1 as need_query , max(beat_time) as exception_time limit 1
  logstore: ecs_alarm_center_worker_load
  slsConfigName: ecs_inspector
  silent: 0
  region: [default]
  retentionTime: 1
  internal: true
  linkedDimensions: []
  tag: Event
  levels:
    - name: warning
      expression: '{{need_query}} == 1'
      action: [batchRunTask(taskName=RepairOrderProcessor)]
      notify: []
      persistent: false

asw_exception_alert:
  interval: 33
  timeRange: 600
  enable: true
  doc: '疑似物理网络故障导致下挂服务器异常'
  type: nc_network_hardware
  query: 'not phy_network_error_ecs_exception and  not exceptionName: asw_exception_alert and (exceptionName: nc_down_uniform_check or exceptionName: nc_down_uniform_check_fastpath or exceptionName: ag_nc_short_ping_check_failed_fastpath and not warningLevel: low_warning or exceptionName: nic_down or exceptionName: nic_link_up_down or exceptionName: nc_fake_down or exceptionName: pingmesh_latency_failpoint_increased or (exceptionName: pingmesh_latency_error_public or exceptionName: pingmesh_latency_error_finance or exceptionName: ptp_pingmesh_latency_error_public or exceptionName: pingmesh_latency_error_public_new) and not fatal) and not additionalInfo: active and not 轮转升级 and not 经济体上云-神龙FPGA轮转升级 and not lock_reason: 轮转升级* and not (biz_status: nc_down and cluster_usage: 集团上云)'
  analyse: select * from (select 'asw_exception_alert' as exception_name,0 as notify,0 as fpga_upgrade,cardinality(array_distinct(array_agg( nc_ip) OVER(PARTITION BY cluster_name))) as cluster_nc_cnt,cardinality(array_distinct(array_agg( nc_ip) OVER(PARTITION BY ext3))) as nc_count,* from (select cluster_alias as cluster_name,asw_id as ext3, ncIp as nc_ip,ncIp as machine_id,exceptionName as reason,cluster_usage from log where ncIp not in ('************','**************','***********','************','************','************','**************','************','**************','***********','**********','***********','***********','***********','***********','***********','**************','**********','**************','**************','*************','*************','*************','*************','*************','************','**************','*************','***********','*************','*************','************','***********','*************','*************','**************','*************','*************','**************','**************','**************','***********','*************','************','***********','*************','************','************','**************','**************','*************','*************','************','**************','************','************','**************','*************','*************','**************','*************','**************','**************','11.118.60.34','10.151.160.27','11.119.235.108','10.16.163.82','11.218.145.89','11.119.47.209','11.216.97.83','11.119.47.140','10.153.54.43','11.192.60.174','11.200.137.5','11.194.209.82','10.182.224.195','10.3.214.13','10.239.165.169','10.154.126.235','10.3.138.148','11.117.96.238','11.196.86.238','11.117.168.167','10.188.140.153','10.43.36.157','11.192.60.123','10.3.165.9','10.154.167.3','11.201.62.30','11.199.29.203','10.61.184.139','11.192.60.32','11.192.111.153','11.192.73.205','11.117.22.103','11.200.191.133','10.151.161.39','10.37.134.15','100.105.59.202','10.151.108.115','11.115.74.106','11.192.52.235','11.219.84.139','11.219.213.216','10.111.224.238','11.219.139.164','11.197.28.16','11.199.237.20','10.239.165.137','10.151.222.112','11.115.69.17','10.57.19.97','10.223.111.92','10.57.20.169','11.119.55.69','10.111.235.134','10.5.180.132','10.154.96.18','11.119.52.42','10.57.41.26','10.42.3.81','11.116.229.73','11.195.49.107','11.138.14.137','11.201.56.229','10.229.61.3','11.192.94.86','11.117.32.143','10.239.165.168','11.236.89.133','11.200.193.98','10.61.30.92','11.118.181.157','11.117.145.45','11.134.198.86','10.7.169.230','11.192.94.50','11.113.255.193','10.93.142.78','11.114.238.146','10.0.52.156','11.198.217.245','11.113.97.94','10.151.36.26','10.5.52.24','10.127.99.196','10.188.213.1','10.154.109.166','10.42.133.23','11.200.137.37','10.40.225.209','10.42.131.213','10.37.35.83','11.116.162.171','11.218.137.41','11.113.251.51','11.192.244.33','10.42.1.134','11.217.55.172','10.151.145.47','11.199.101.87','10.188.201.3','10.127.99.197','11.195.18.18','10.8.158.67','11.217.34.232','10.10.131.20','10.151.41.52','10.21.76.96','10.151.180.135','10.151.212.45','10.112.123.141','10.21.73.71','10.70.121.210','10.188.208.9','11.119.165.28','10.21.72.154','10.188.165.226','11.198.237.74','11.111.179.214','11.154.10.103','11.118.181.236','11.114.10.23','*************','*************','**********','************','************','*************','***********','**************','**************','**************','*************','*************','*************','*************','************','*************','*************','************','**************','************','************','**************','*************','*************','*************','**************','************','************','**************','*************','*************') group by ncIp,cluster_alias,asw_id,exceptionName,cluster_usage)) where nc_count >= 5 or cluster_nc_cnt >=6 limit 50000
  logstore: 'monitor_exception_sls_alert'
  slsConfigName: ecs_inspector
  silent: 10780
  region: [default]
  tag: PerformanceImpact
  retentionTime: 13
  powerSQL: true
  linkedQueryList: [queryAlertedException(exception_name@or),queryFpgaUpgradeEvent()]
  detailMetric: queryPingmeshLatencyPublic
  linkedDimensions: ['NC_INFO','MULTI_NC_INFO']
  exclusion:
    ext: '"{{NC_INFO}}" == "None" or {{fpga_upgrade}} == 1'
  levels:
    - name: fatal
      expression: '{{notify}} == 0 and ({{cluster_nc_cnt}} >= 6 and "{{cluster_usage}}" not in ("蚂蚁上云","测试") or {{cluster_nc_cnt}} >= 10 and "{{cluster_usage}}" in ("蚂蚁上云","测试")) and "{{reason}}" in ("nc_fake_down","nc_down_uniform_check") and "{{ext3}}" != "None" '
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=IdcNetworkDeviceChangeEventProcessor)']
    - name: critical
      expression: '{{nc_count}} >= 5 and {{notify}} == 0 and "{{reason}}" not in ("nic_down","nic_link_up_down","pingmesh_latency_error_public","pingmesh_latency_failpoint_increased") and "{{ext3}}" != "None" '
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=IdcNetworkDeviceChangeEventProcessor)']
    - name: warning
      expression: '{{cluster_nc_cnt}} >= 6 and {{notify}} == 0 and "{{reason}}" in ("pingmesh_latency_error_public","pingmesh_latency_failpoint_increased") and "{{ext3}}" != "None" '
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=IdcNetworkDeviceChangeEventProcessor)']
    # 服务器单网卡
    - name: low_warning
      expression: '{{nc_count}} >= 5 and {{notify}} == 0 and "{{reason}}" in ("nic_down","nic_link_up_down","pingmesh_latency_error_public","pingmesh_latency_failpoint_increased") and "{{ext3}}" != "None"'
      notify: [SlsAlertHandler]
      action: ['batchRunTask(taskName=IdcNetworkDeviceChangeEventProcessor)']

asw_exception_alert_for_cloudbox:
  interval: 29
  timeRange: 500
  enable: true
  doc: '疑似云盒内部网络故障导致外部无法联通'
  type: nc_network_event
  query: '(exceptionName: nc_hang_uniform_check or exceptionName: nc_hang_too_long) and not additionalInfo: active and not 轮转升级 and not 经济体上云-神龙FPGA轮转升级 and (zoneGroupFlag: biz_cloudbox or cn-chengdu-smarthosting-1)'
  analyse: select *,case when idc = 'CB006' then 'sag-cofcsg91unvfeldlox' when idc = 'CB010' then 'vbr-5fl7qxwla665mp9va3jg9' when idc = 'CB011' then 'sag-9d14t2gtal2r6xoz3q' when idc = 'CB007' then 'vbr-2zexselawbv19e5gktjcq|vbr-2ze2fnr6mophkf8eztxkw' when idc = 'VNCB008' then 'vbr-j6t6yndg27jgrjkzkqhek|vbr-j6tq9qvnswlqt5k9qv6ym|vbr-j6th68yi2qh8sebv5z5rd|vbr-j6tq2wstdtrhbh0ktvv2q|vbr-j6t1a1enkis30wgfbfh6q|vbr-j6tgzduznvceltrz7emef|vbr-j6tezeuh8b8ojwzmtxqxy|vbr-j6tma35m0dkmptkryzokf' when idc = 'CB012' then 'vbr-wz9pybq95n2e6owsuc67m|vbr-wz9weghyhndixwcvsf6vt|vbr-wz9cv089isdak4hamt47f|vbr-wz9s1fjxxlc3z2w3ivaho' when idc = 'CB013' then 'vbr-wz916dkljaq95txw6bhi8|vbr-wz9wol61yn5xzdges3cnp' when idc = 'CB016' then 'sag-ouyozd7h7kz0pfxyn4' when idc = 'CB017' then 'vbr-2ze59u3hue3j2xbqer53f|vbr-2ze2cvk5y9tzcr0m8g1hw' when idc = 'CB018' then 'sag-mrrrlh3xxmnacwttlt' when idc = 'CB019' then 'sag-b9ub90iidg9jekd8be' when idc = 'CB020' then 'vbr-bp1u7ibng4k1nvrz63jj9' when idc = 'CB021' then 'vbr-wz9hz7m5n50a63s7tc6hy|vbr-wz912wucmys8l6ou9wy02' when idc = 'CB022' then 'sag-na5i4m7tw5a14zq573' when idc = 'CB023' then 'sag-7t4slqxf2jmx396guo' when idc = 'CB024' then 'sag-ntwecm0h3cdhnqpq2v' end as ext3 from (select nc_ip,cluster_alias as cluster_name,iz as reason,count(*) OVER(PARTITION BY iz) as ncCntIz,exception_time,idc from (select ncIp as nc_ip,cluster_alias,iz,max(exceptionTime) as exception_time,idc from log group by nc_ip,cluster_alias,iz,idc ) ) HAVING  ncCntIz >= 2 limit 1000
  logstore: 'monitor_exception_sls_alert'
  slsConfigName: xunjian_zhangbei
  silent: 4180
  region: [default]
  tag: PerformanceImpact
  retentionTime: 15
  powerSQL: true
  detailMetric: queryNcHangDownStatus
  linkedDimensions: ['NC_INFO','MULTI_NC_INFO']
  exclusion:
    ext: '"{{NC_INFO}}" == "None"'
  levels:
    - name: warning
      expression: '{{ncCntIz}} >= 2'
      notify: [SlsAlertHandler]
      action:
        - 'batchRunTask(taskName=CloudMonitorMetricProcessor,metric=master.delay|master.drop|net.rxPkgs|net.txPkgs|net_rx.rate|net_tx.rate|drop.pkgs|slave.delay|slave.drop|tx_bandwidth_utilization|rx_bandwidth_utilization,aliUid=1591505147997429,project=acs_smartag)'
        - 'batchRunTask(taskName=CloudMonitorMetricProcessor,metric=BytesInFromIDCToVpc|BytesOutFromVpcToIDC|PkgsDropInFromIDCToVpc|PkgsDropOutFromVpcToIDC|PkgsInFromIDCToVpc|PkgsOutFromVpcToIDC|PkgsRateLimitDropOutFromVpcToVbr|RateInFromIDCToVpc|RateOutFromVpcToIDC|VbrHealthyCheckLatency|VbrHealthyCheckLossRate,aliUid=1591505147997429,project=acs_physical_connection)'

asw_exception_alert_for_dedicated_region:
  interval: 29
  timeRange: 500
  enable: true
  doc: '疑似专属云内部网络故障导致外部无法联通'
  type: nc_network_event
  query: '(exceptionName: nc_hang_uniform_check or exceptionName: nc_hang_too_long) and not 轮转升级 and not 经济体上云-神龙FPGA轮转升级 and zoneGroupFlag: biz_dedicated_region'
  analyse: select * from (select nc_ip,cluster_alias as cluster_name,iz as reason,count(*) OVER(PARTITION BY iz) as ncCntIz,exception_time,idc from (select ncIp as nc_ip,cluster_alias,iz,max(exceptionTime) as exception_time,idc from log group by nc_ip,cluster_alias,iz,idc)) having ncCntIz >= 2 limit 1000
  logstore: monitor_exception_sls_alert
  slsConfigName: xunjian_zhangbei
  silent: 4180
  region: [default]
  tag: PerformanceImpact
  retentionTime: 15
  powerSQL: true
  detailMetric: queryNcHangDownStatus
  linkedDimensions: ['NC_INFO','MULTI_NC_INFO']
  exclusion:
    ext: '"{{NC_INFO}}" == "None"'
  levels:
    - name: warning
      expression: '{{ncCntIz}} >= 6'
      notify: [SlsAlertHandler]

idc_lost_power_event:
  interval: 57
  timeRange: 100
  enable: true
  doc: 'IDC机柜掉电异常'
  type: rack_hardware_event
  query: '*'
  analyse: select 1 as power_lost,concat(idc,'_',room,'_',substr(rackId,1,1),'_',rackId) as machine_id,eventTypeName as reason, min(from_unixtime(__time__)) as exception_time group by machine_id,reason limit 10000
  logstore: 'raptor-ecs'
  slsConfigName: idc_event
  region: [default]
  silent: 180
  retentionTime: 90
  linkedDimensions: [MULTI_NC_INFO,CLUSTER_INFO]
  tag: Unavailable
  levels:
    - name: fatal
      expression: '{{power_lost}} == 1 and {{MULTI_NC_INFO.isValid}}'
      notify: [SlsAlertHandler]
      atDuty: [nc]

idc_rack_power_exception_event:
  interval: 50
  timeRange: 70
  enable: true
  doc: 'IDC报告机柜电力发生异常，来自猎户座告警平台'
  type: rack_hardware_hardware
  query: 'resourceType: RACK and (content.faultType: 0 or content.faultType: 3) and (level: fault or level: warn)'
  analyse: SELECT 0 as notify,'idc_rack_power_exception_event' as exception_name,concat(split_part(location, '_', 3), '_', split_part(location, '_', 2), '_', substr(split_part(location, '_', 1), 1, 1), '_', split_part(location, '_', 1)) AS machine_id, from_unixtime(min(__time__)) AS exception_time, name AS reason, max(description) AS ext3, faultType, level,case when faultType = '0' then 'fatal' else 'critical' end as alert_level group by level,machine_id,reason,faultType LIMIT 2000
  logstore: 'infrastructure-starunion-logstore'
  slsConfigName: idc_infrastructure_oxs
  region: [default]
  silent: 180
  detailMetric: queryRackPowerDetail
  linkedQueryList: ['queryAlertedException(exception_name@or)']
  linkedDimensions: [MULTI_NC_INFO]
  retentionTime: 40
  tag: PerformanceImpact
  levels:
    - name: fatal
      # 双路掉电 ais.double.circuit.power.failure
      expression: '{{notify}} == 0 and {{MULTI_NC_INFO.isValid}} and {{faultType}} == 0'
      notify: [SlsAlertHandler, EventCenterAlertHandler]
      atDuty: [nc]
    - name: critical
      # 单路掉电 ais.cabinet.single.circuit.power.failure
      expression: '{{notify}} == 0 and {{MULTI_NC_INFO.isValid}} and {{faultType}} == 3'
      hasVmThenAlert: true
      notify: [SlsAlertHandler]
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Warning
    category: NC.Sys_Hidden_Danger&Control.Stability
    eventSilent: 600

idc_rack_temperature_high_event:
  interval: 59
  timeRange: 100
  enable: true
  doc: 'IDC报告机柜温度升高异常，来自猎户座告警平台'
  type: rack_hardware_hardware
  query: 'resourceType: RACK and content.faultType: 5 and (level: fault or level: warn)'
  analyse: SELECT 1 AS rack_temperature_high, concat(split_part(location, '_', 3), '_', split_part(location, '_', 2), '_', substr(split_part(location, '_', 1), 1, 1), '_', split_part(location, '_', 1)) AS machine_id, from_unixtime(__time__) AS exception_time, name AS reason, description AS ext3, faultType, level LIMIT 1000
  logstore: 'infrastructure-starunion-logstore'
  slsConfigName: idc_infrastructure_oxs
  region: [default]
  silent: 3580
  detailMetric: queryRackPowerDetail
  linkedDimensions: [MULTI_NC_INFO,CLUSTER_INFO]
  tag: PerformanceImpact
  retentionTime: 40
  levels:
    - name: fatal
      # 致命温升，很可能导致宕机 ais.cabinet.temp.warn
      expression: '{{rack_temperature_high}} == 1 and {{MULTI_NC_INFO.isValid}} and "{{level}}" == "fault"'
      hasVmThenAlert: true
      action: [ 'batchRunTask(taskName=NcScheduleFactorProcessor)' ]
      notify: [SlsAlertHandler, EventCenterAlertHandler]
    - name: critical
      # 温度上升，有可能导致宕机 ais.cabinet.temp.warn
      expression: '{{rack_temperature_high}} == 1 and {{MULTI_NC_INFO.isValid}} and "{{level}}" == "warn"'
      hasVmThenAlert: true
      action: [ 'batchRunTask(taskName=NcScheduleFactorProcessor)' ]
      notify: [SlsAlertHandler, EventCenterAlertHandler]
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Warning
    category: NC.Sys_Hidden_Danger&Control.Stability
    eventSilent: 100

idc_rack_temperature_incident:
  interval: 58
  timeRange: 65
  enable: true
  doc: 'IDC报告机柜暖通异常，来自猎户座告警平台'
  type: rack_hardware_hardware
  query: 'firstCategory:暖通 and secondProductionLine: 阿里云-弹性计算 and influenceInstanceType: RACK'
  analyse: select 1 as rack_temperature_high,concat(split_part(influenceInstanceCodeList, '.', 3), '_', split_part(influenceInstanceCodeList, '.', 2), '.',split_part(influenceInstanceCodeList, '.', 3), '_',substr(split_part(influenceInstanceCodeList, '.', 1), 1, 1), '_', split_part(influenceInstanceCodeList, '.', 1)) AS machine_id,gmtCreate as exception_time,description as ext3 where position(',' in influenceInstanceCodeList)=0 limit 1000
  logstore: starunion-incident
  slsConfigName: idc_infrastructure_oxs
  region: [default]
  silent: 3580
  detailMetric: queryRackPowerDetail
  linkedDimensions: [MULTI_NC_INFO,CLUSTER_INFO]
  tag: PerformanceImpact
  retentionTime: 40
  levels:
    - name: critical
      expression: '{{rack_temperature_high}} == 1 and {{MULTI_NC_INFO.isValid}}'
      hasVmThenAlert: true
      notify: [SlsAlertHandler]

idc_rack_power_high_resume:
  interval: 60
  timeRange: 100
  enable: true
  doc: 'IDC报告机柜功耗升高恢复，来自猎户座告警平台'
  type: rack_hardware_hardware
  # info表示恢复事件，此时的stateName一般为「已恢复」
  query: 'resourceType: RACK and content.faultType: 14 and (level: info)'
  analyse: SELECT 1 AS rack_power_high, concat(split_part(location, '_', 3), '_', split_part(location, '_', 2), '_', substr(split_part(location, '_', 1), 1, 1), '_', split_part(location, '_', 1)) AS machine_id, from_unixtime(__time__) AS exception_time, name AS reason, description AS ext3, faultType, faultLevel, concat('{', '"powerAlarmRate":', powerAlarmRate, ',', '"faultId":', faultId, '}') AS extension, level LIMIT 1000
  logstore: 'infrastructure-starunion-logstore'
  slsConfigName: idc_infrastructure_oxs
  region: [default]
  silent: 3580
  detailMetric: queryRackPowerDetail
  tag: Event
  linkedDimensions: [MULTI_NC_INFO,CLUSTER_INFO]
  retentionTime: 40
  levels:
    - name: low_warning
      expression: '{{rack_power_high}} == 1 and {{MULTI_NC_INFO.isValid}}'
      hasVmThenAlert: true
      notify: [SlsAlertHandler, EventCenterAlertHandler]
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Warning
    category: NC.Sys_Hidden_Danger&Control.Stability
    eventSilent: 100

idc_dns_change_event:
  interval: 29
  timeRange: 58
  enable: true
  doc: 'IDC报告DNS进行变更，来自猎户座告警平台'
  type: asw_network_hardware
  query: 'identityCode: DNS and changeStatus: running'
  analyse: select 1 as changed,influenceInstanceCodeList as machine_id,from_unixtime(cast(planStartTime as bigint)) as exception_time limit 3000
  logstore: starunion-change
  slsConfigName: idc_infrastructure_oxs
  region: [default]
  silent: 3580
  tag: Event
  retentionTime: 40
  linkedDimensions: [NC_INFO]
  levels:
    - name: low_warning
      expression: '{{changed}} == 1'
      notify: [SlsAlertHandler]
      hasVmThenAlert: false
      needRelatedVm: false

idc_asw_network_exception_event:
  interval: 30
  timeRange: 58
  enable: true
  doc: 'IDC报告ASW网络发生异常，来自猎户座告警平台'
  type: asw_network_hardware
  query: 'resourceType: ASW and content.faultType: 2 and (level: fault or level: warn)'
  analyse: SELECT 1 AS asw_network_exception, regexp_extract(description, '.*Host：(.*),发生报警.*', 1) AS machine_id, from_unixtime(__time__) AS exception_time, name AS reason, description AS ext3, faultType, level having machine_id not like '%SQA%' LIMIT 1000
  logstore: 'infrastructure-starunion-logstore'
  slsConfigName: idc_infrastructure_oxs
  region: [default]
  silent: 3580
  tag: PerformanceImpact
  linkedDimensions: ['NC_INFO','MULTI_NC_INFO']
  retentionTime: 40
  levels:
    - name: fatal
      # 堆叠网络设备故障
      expression: '{{asw_network_exception}} == 1 and {{MULTI_NC_INFO.isValid}} and "[Aliping]堆叠网络设备ping不通" in "{{ext3}}"'
      notify: [SlsAlertHandler, EventCenterAlertHandler]
    - name: critical
      # 去堆叠网络设备故障
      expression: '{{asw_network_exception}} == 1 and {{MULTI_NC_INFO.isValid}} and "[Aliping]去堆叠网络设备ping不通" in "{{ext3}}"'
      notify: [SlsAlertHandler, EventCenterAlertHandler]
    - name: warning
      # 网络设备故障
      expression: '{{asw_network_exception}} == 1 and {{MULTI_NC_INFO.isValid}} and "网络设备ping不通" in "{{ext3}}"'
      notify: [SlsAlertHandler, EventCenterAlertHandler]
    - name: low_warning
      # 变更异常/上联网口宕
      expression: '{{asw_network_exception}} == 1 and {{MULTI_NC_INFO.isValid}} and ("变更回退开始" in "{{ext3}}" or ("上联" in "{{ext3}}" and "电路组中断" in "{{ext3}}"))'
      notify: [SlsAlertHandler, EventCenterAlertHandler]
      needRelatedVm: false
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Warning
    category: NC.Sys_Hidden_Danger&Control.Stability
    eventSilent: 100

idc_asw_device_change_event:
  interval: 53
  timeRange: 58
  enable: true
  doc: 'IDC报告ASW设备进行变更，来自猎户座告警平台'
  type: asw_network_hardware
  query: 'secondProductionLine: 阿里云-弹性计算 and influenceInstanceType: ASW and changeStatus: 变更中 and not changeAction:业务/IP地址发布'
  # 一次变更的ASW数量可能很多，单次最多取60个
  analyse: SELECT 1 AS asw_network_change, slice(split(hostNameList, ','), 1, 600) AS machine_list, from_unixtime(__time__) AS exception_time, changeAction AS ext3, riskEstimate having hostNameList not like '%SQA%' LIMIT 1000
  logstore: 'starunion-change'
  slsConfigName: idc_infrastructure_oxs
  region: [default]
  silent: 3580
  tag: Event
  linkedDimensions: ['NC_INFO','MULTI_NC_INFO']
  retentionTime: 40
  levels:
    - name: critical
      # 高风险
      expression: '{{asw_network_change}} == 1 and {{MULTI_NC_INFO.isValid}} and "{{riskEstimate}}" in ("超高","高")'
      notify: [SlsAlertHandler]
      needRelatedVm: true
    - name: warning
      # 中风险
      expression: '{{asw_network_change}} == 1 and {{MULTI_NC_INFO.isValid}} and "{{riskEstimate}}" in ("中高","中","中低")'
      notify: [ SlsAlertHandler]
      needRelatedVm: false
    - name: low_warning
      # 低风险
      expression: '{{asw_network_change}} == 1 and {{MULTI_NC_INFO.isValid}} and "{{riskEstimate}}" in ("低","超低")'
      notify: [ SlsAlertHandler]
      needRelatedVm: false

idc_asw_device_change_detail_event:
  interval: 29
  timeRange: 58
  enable: true
  doc: 'IDC报告ASW设备变更步骤，来自猎户座告警平台'
  type: asw_network_hardware
  query: 'not devices:""'
  # 一次变更的ASW数量可能很多，单次最多取600个
  analyse: select 1 as asw_network_change,json_extract_scalar(operation_json,'$.title') as extension,json_extract_scalar(operation_json,'$.detail') as ext3,machine_list,exception_time from (select json_parse(trim(operation)) as operation_json, slice(split(devices, ','), 1, 600) AS machine_list,start_time as exception_time from log where devices like 'ASW%' and devices not like '%SQA%' and message like 'start%') limit 1000
  logstore: starunion-change-detail
  slsConfigName: idc_infrastructure_oxs
  region: [default]
  silent: 3580
  tag: Event
  linkedDimensions: ['NC_INFO','MULTI_NC_INFO']
  retentionTime: 40
  levels:
    - name: warning
      expression: '{{asw_network_change}} == 1 and {{MULTI_NC_INFO.isValid}}'
      notify: [SlsAlertHandler]
      needRelatedVm: false

idc_network_device_event:
  interval: 40
  timeRange: 60
  enable: true
  doc: '疑似网络设备异常或变更导致物理机异常'
  type: asw_network_hardware
  query: '*'
  analyse: select 1 as checked,NcIp as nc_ip,min(Time) as exception_time,min_by(ExceptionName,Time) as fromException,if(min_by(Reason,Time)='异常',min_by(Description,Time),concat(DeviceId,',',min_by(Description,Time))) as ext3,min_by(Reason,Time) as extension,DeviceId as machine_id from log where Description not like '%业务/IP地址发布%' group by nc_ip,DeviceId limit 10000
  logstore: idc_network_device_change_event_processor
  slsConfigName: xunjian_zhangbei
  region: [ default ]
  silent: 3580
  tag: Event
  linkedDimensions: [ 'NC_INFO' ]
  retentionTime: 40
  levels:
    - name: warning
      expression: '{{checked}} == 1'
      notify: [ SlsAlertHandler ]
      needRelatedVm: false

idc_network_incident:
  interval: 58
  timeRange: 63
  enable: true
  doc: '物理网络异常事件'
  type: asw_network_hardware
  query: 'not informType:RECOVER and (influenceInstanceType: NSW or influenceInstanceType: SSW or influenceInstanceType: BSW or influenceInstanceType: CSR or influenceInstanceType: ISR or influenceInstanceType: CSW or influenceInstanceType: MC or influenceInstanceType: MA or influenceInstanceType: LSW or influenceInstanceType: DSW or influenceInstanceType: PSW or influenceInstanceType: OTHER_NET_DEV)'
  analyse: select 1 as checked, upper(idc_item) as machine_id, gmtCreate as exception_time, influenceInstanceType as extension, description as ext3 from log,unnest(split(idc,',')) as table_alias(idc_item) having machine_id!='ALI' limit 3000
  logstore: starunion-incident
  slsConfigName: idc_infrastructure_oxs
  region: [default]
  tag: Event
  linkedDimensions: [NC_INFO]
  retentionTime: 40
  levels:
    - name: warning
      expression: '{{checked}} == 1'
      notify: [ SlsAlertHandler ]
      needRelatedVm: false

idc_network_incident2:
  interval: 58
  timeRange: 63
  enable: true
  doc: '物理网络异常事件'
  type: asw_network_hardware
  query: 'name:网络设备异常 and not resourceType:ASW'
  analyse: select 1 as checked, upper(idc_item) as machine_id, description as ext3 from log,unnest(split(idc,',')) as table_alias(idc_item) having machine_id!='ALI' limit 3000
  logstore: infrastructure-starunion-logstore
  slsConfigName: idc_infrastructure_oxs
  region: [default]
  tag: Event
  linkedDimensions: [NC_INFO]
  retentionTime: 40
  levels:
    - name: warning
      expression: '{{checked}} == 1'
      notify: [ SlsAlertHandler ]
      needRelatedVm: false

idc_traffic_bottleneck_by_user:
  interval: 58
  timeRange: 63
  enable: true
  doc: '疑似用户流量徒增导致网络设备流量打满'
  type: asw_network_hardware
  query: 'anomaly:spike'
  analyse: select idc,idc as machine_id,'None' as ext3,aliuid as extension,reason,gbps from (select regexp_extract(metric,'UserPerformanceNet/.*/\d+/(.*)',1) as idc,regexp_extract(metric,'UserPerformanceNet/.*/(\d+)/.*',1) as aliuid,regexp_extract(metric,'UserPerformanceNet/(.*)/\d+/.*',1) as reason,from_unixtime(timestamp) as exception_time,value * 8 / 1000 / 1000 / 1000 as gbps from log where metric like 'UserPerformanceNet/tx_bps_total%' or  metric like 'UserPerformanceNet/rx_bps_total%') where position('-' in idc)=0 limit 1000
  logstore: xdragon-metric-anomaly-byuid
  slsConfigName: ecs_predict_result
  region: [default]
  tag: Event
  linkedDimensions: [NC_INFO]
  linkedQueryList: [queryIdcTrafficBottleneckEvent()]
  retentionTime: 40
  exclusion:
    ext: '"{{ext3}}" == "None"'
  levels:
    - name: warning
      expression: '{{gbps}} >= 1'
      notify: [ SlsAlertHandler ]
      needRelatedVm: false

user_traffic_spike_in_region:
  interval: 58
  timeRange: 63
  enable: true
  doc: '用户region内流量徒增'
  type: asw_network_hardware
  query: 'anomaly:spike'
  analyse: select regoin as machine_id,concat(aliUid,'/',ext3) as ext3,aliuid as extension,spike_value from (select regexp_extract(metric,'UserPerformanceNet/(.*)/\d+/.*',1) as ext3,regexp_extract(metric,'UserPerformanceNet/.*/\d+/(.*)',1) as regoin,regexp_extract(metric,'UserPerformanceNet/.*/(\d+)/.*',1) as aliuid,from_unixtime(timestamp) as exception_time,if(metric like 'UserPerformanceNet/tx_bps_total%',value * 8 / 1000 / 1000 / 1000,value) as spike_value from log where metric like 'UserPerformanceNet/tx_bps_total%' or metric like 'UserPerformanceNet/tx_pps_total%' or metric like 'UserPerformanceNet/session_total%') having position('-' in regoin)!=0 limit 1000
  logstore: xdragon-metric-anomaly-byuid
  slsConfigName: ecs_predict_result
  region: [default]
  tag: Event
  linkedDimensions: [NC_INFO]
  retentionTime: 40
  levels:
    - name: warning
      expression: '{{spike_value}} >= 1 and ("tx_bps_total" in "{{ext3}}" or "tx_pps_total" in "{{ext3}}" or "rx_bps_total" in "{{ext3}}" or "rx_pps_total" in "{{ext3}}")'
      notify: [ SlsAlertHandler ]
      needRelatedVm: false
    - name: low_warning
      expression: '{{spike_value}} >= 1'
      notify: [ SlsAlertHandler ]
      needRelatedVm: false

idc_power_device_change_event:
  interval: 60
  timeRange: 70
  enable: true
  doc: 'IDC报告电力等基础设备进行变更，来自猎户座告警平台'
  type: idc_hardware_event
  query: 'secondProductionLine: 阿里云-弹性计算 and identityCode: IDC and changeStatus: 变更中'
  analyse: SELECT 1 AS power_device_change, upper(idc) AS machine_id, from_unixtime(__time__) AS exception_time, changeAction as ext3, riskEstimate LIMIT 1000
  logstore: 'starunion-change'
  slsConfigName: idc_infrastructure_oxs
  region: [default]
  silent: 3580
  tag: Event
  linkedDimensions: [NC_INFO]
  levels:
    - name: critical
      # 高风险
      expression: '{{power_device_change}} == 1 and "{{riskEstimate}}" in ("超高","高")'
      needRelatedVm: false
      notify: [ SlsAlertHandler]
    - name: warning
      # 中风险
      expression: '{{power_device_change}} == 1 and "{{riskEstimate}}" in ("中高","中","中低")'
      needRelatedVm: false
      notify: [ SlsAlertHandler]
    - name: low_warning
      # 低风险
      expression: '{{power_device_change}} == 1'
      needRelatedVm: false
      notify: [ SlsAlertHandler]

idc_drilling_event:
  interval: 57
  timeRange: 200
  enable: true
  doc: '破坏性演练事件'
  type: idc_hardware_event
  query: 'aliyun_*_server'
  analyse: select 1 as drill_event,'127.0.0.1' as nc_ip,upper(cluster_name) as cluster_name,upper(cluster_name) as extension,concat('startTime=',start_time,',','endTime=',end_time) as ext3,operation_type as reason from log,unnest( cast (regexp_extract_all(affected_clusters, 'aliyun_([a-zA-Z\d]+)_server',1) as array(varchar) )) as t(cluster_name) group by cluster_name, start_time, end_time, operation_type limit 1000
  logstore: 'sysarch-dr-notification'
  slsConfigName: idc_drilling
  region: [default]
  silent: 580
  retentionTime: 365
  tag: Event
  linkedDimensions: [NC_INFO,CLUSTER_INFO]
  levels:
    - name: critical
      expression: '{{drill_event}} == 1 and ("net-drill" in "{{reason}}" or "power-off" in "{{reason}}") and "{{CLUSTER_INFO}}" != "None"'
      notify: [SlsAlertHandler]
      atDuty: [control]

asw_critical_alert:
  interval: 38
  timeRange: 60
  enable: true
  doc: '物理网络ASW设备发生严重异常'
  type: asw_network_hardware
  query: '(板卡离线告警 or 网络设备ping不通 or FEX-离线告警 ) and ASW'
  analyse: select 'asw_critical_alert' as exception_name,0 as notify,machine_id,reason,concat(machine_id,' ',reason) as ext3,count(*) as error_cnt,min(from_unixtime(time)) as exception_time from (select __time__ as time,job_name as reason,regexp_extract(event_obj, '([\w\d\-\.]+)\$*', 1) as machine_id from log ) group by machine_id,reason having machine_id not like '%SQA%' limit 1000
  logstore: paoding_network_error
  slsConfigName: ecs_inspector
  region: [default]
  silent: 16180
  linkedQueryList: ['queryAlertedException(machine_id@or)']
  retentionTime: 50
  tag: PerformanceImpact
  linkedDimensions: [MULTI_NC_INFO]
  detailMetric: queryPingmeshLatencyPublic
  levels:
    - name: critical
      expression: '{{notify}} == 0 and {{error_cnt}} >= 0 and "{{MULTI_NC_INFO}}" != "None"'
      action: [ 'batchRunTask(taskName=NcScheduleFactorProcessor)' ]
      notify: [SlsAlertHandler]

rack_mlock_event:
  interval: 63
  timeRange: 72
  enable: true
  doc: '服务器机架锁定或解锁事件'
  type: rack_controller_event
  query: 'content: "LOCK_RACK\\" or LOCK_RACK or content: "UNLOCK_RACK\\"  or UNLOCK_RACK'
  analyse: select count(*) as rack_err,regexp_extract(content,'rack_id.{1,3}([\w\.\-_]+)',1) as machine_id,max(regexp_extract(content,'(\d+\-\d+\-\d+ \d+:\d+:\d+)',1)) as exception_time,regexp_extract(content,'action_name.{1,5}([\w_]+)',1) as ext3 group by machine_id,ext3 limit 300
  logstore: 'rackpower'
  slsConfigName: 'houyi_ops'
  silent: 21580
  tag: Event
  retentionTime: 10
  linkedDimensions: [MULTI_NC_INFO]
  levels:
    - name: warning
      expression: '{{rack_err}} == 1'
      notify: [SlsAlertHandler]

cloudbox_idc_monitor_data_loss:
  interval: 60
  timeRange: 610
  enable: true
  doc: '云盒产品IDC监控数据丢失'
  type: nc_hardware_event
  query: 'not 高温告警'
  analyse: SELECT cb_id as nc_ip, monitorItemName as reason, idc as ext3, COUNT(*) as cnt, min(from_unixtime(__time__)) as exception_time GROUP BY cb_id, monitorItemName, idc HAVING cb_id != 'null' LIMIT 1000
  logstore: idc_monitor
  slsConfigName: cloudbox_monitor
  silent: 7180
  retentionTime: 15
  linkedDimensions: []
  tag: ControlFailed
  levels:
    - name: warning
      needRelatedVm: false
      expression: '{{cnt}} <= 8'
      notify: [SlsAlertHandler]
    
cloudbox_idc_monitor_data_resume:
  interval: 300
  timeRange: 2100
  enable: true
  doc: '云盒产品IDC监控数据丢失恢复'
  type: nc_hardware_event
  query: '*'
  # 每10分钟统计一次，取最近一次完整10分钟的统计数cnt，与最近一次完整10分钟相邻10分钟的统计数last_cnt，进行比较，由少变多即为恢复
  analyse: SELECT from_unixtime(timestamp) as exception_time, cb_id as nc_ip, monitorItemName as reason, idc as ext3, cnt, last_cnt FROM (SELECT timestamp, cb_id, monitorItemName, idc, cnt, lag(cnt, 1, cnt) over(PARTITION BY cb_id, monitorItemName, idc ORDER BY timestamp ASC) as last_cnt FROM (SELECT __time__-__time__%600 as timestamp, cb_id, monitorItemName, idc, COUNT(*) as cnt FROM log GROUP BY timestamp, cb_id, monitorItemName, idc HAVING cb_id != 'null')) WHERE to_unixtime(now()) - timestamp > 600 and to_unixtime(now()) - timestamp < 1200 LIMIT 1000
  logstore: idc_monitor
  slsConfigName: cloudbox_monitor
  silent: 7180
  retentionTime: 15
  tag: Event
  linkedDimensions: []
  levels:
    - name: warning
      needRelatedVm: false
      expression: '{{last_cnt}} <= 8 and {{cnt}} >= 9'
      notify: [SlsAlertHandler]
    
cloudbox_idc_lock_status_open:
  interval: 60
  timeRange: 70
  enable: true
  doc: '云盒产品IDC机柜门开启'
  type: nc_hardware_event
  query: '(dataItem : lock_status and value : 开启) or ( dataItem:lock_event and( value:正常开门 or value:非正常开门)) or (dataItem : door_status and value : 开启)'
  analyse: SELECT cb_id as nc_ip,region as ext1, az as ext2,concat(idc,'-',rack,'-',monitorItemName) as ext3, ali_uid as extension ,monitorItemName as reason, min(from_unixtime(__time__)) as exception_time, COUNT(*) as cnt GROUP BY cb_id, idc,rack,ali_uid,monitorItemName,region,az HAVING cb_id != 'null' LIMIT 1000
  logstore: idc_monitor
  slsConfigName: cloudbox_monitor
  silent: 7180
  tag: Event
  retentionTime: 15
  linkedDimensions: []
  levels:
    - name: warning
      needRelatedVm: false
      expression: '{{cnt}} >= 1'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Warning
    category: NC.Sys_Hidden_Danger&VM.Availability
    closureFlag: start
    closureEvent: cloudbox_lock_open

cloudbox_idc_lock_status_close:
  interval: 57
  timeRange: 130
  enable: true
  doc: '云盒产品IDC机柜门关闭'
  type: nc_hardware_event
  query: '(dataItem : lock_status ) or ( dataItem:lock_event ) or (dataItem : door_status)'
  analyse: select * from (select cb_id as nc_ip,region as ext1,az as ext2,concat(idc,'-',rack,'-',monitorItemName) as ext3,ali_uid as extension,monitorItemName as reason,from_unixtime(min_time) as exception_time,cnt,lag(cnt,1,cnt) over(partition by cb_id,region,idc,az,rack,ali_uid,monitorItemName order by timestamp) as lag_cnt from (select cb_id,region,idc,az,rack,ali_uid,monitorItemName,sum(open_cnt) as cnt,min(timestamp) as min_time,timestamp - timestamp % 60 as timestamp from (select cb_id,region,idc,az,rack,ali_uid,monitorItemName,if(monitorItemName like '%前门%',__time__,__time__ + 1) as timestamp,case when value in ('正常开门','非正常开门','开启')  THEN 1 ELSE 0 end as open_cnt from log where cb_id != 'null') group by cb_id,region,idc,az,rack,ali_uid,monitorItemName,timestamp)) having lag_cnt > 0 and cnt = 0 limit 1000
  logstore: idc_monitor
  slsConfigName: cloudbox_monitor
  silent: 7180
  retentionTime: 15
  linkedDimensions: []
  tag: Event
  levels:
    - name: warning
      needRelatedVm: false
      expression: '{{cnt}} == 0'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Warning
    category: NC.Sys_Hidden_Danger&VM.Availability
    closureFlag: end
    closureEvent: cloudbox_lock_open

cloudbox_idc_exception_event:
  interval: 60
  timeRange: 75
  enable: true
  doc: 云盒产品IDC温度或湿度出现异常
  type: nc_hardware_event
  query: (temperature and 机柜前温度) or (humidity and 机柜前湿度)
  analyse: SELECT idc, nc_ip, ext1, ext2, extension, exception_time, temperature_avg, humidity_avg, IF(temperature_avg < 5 OR temperature_avg > 32, CONCAT('temperature-', CAST(temperature_avg AS VARCHAR)), CONCAT('humidity-', CAST(humidity_avg AS VARCHAR))) AS ext3 FROM (SELECT idc, cb_id AS nc_ip, region AS ext1, az AS ext2, ali_uid AS extension, MIN(time) AS exception_time, ROUND(AVG(CAST(temperature AS DOUBLE)), 2) AS temperature_avg, ROUND(AVG(CAST(humidity AS DOUBLE)), 2) AS humidity_avg FROM (SELECT idc, cb_id, region, az, ali_uid, FROM_UNIXTIME(__time__) AS time, CASE WHEN dataItem = 'temperature' THEN value ELSE NULL END AS temperature, CASE WHEN dataItem = 'humidity' THEN value ELSE NULL END AS humidity FROM log) GROUP BY idc, nc_ip, region, az, ali_uid HAVING nc_ip != 'null' AND (temperature_avg < 5 OR temperature_avg > 32) OR (humidity_avg < 20 OR humidity_avg > 80)) WHERE idc != 'CB004' LIMIT 1000
  logstore: idc_monitor
  slsConfigName: cloudbox_monitor
  silent: 7180
  retentionTime: 15
  detailMetric: ''
  linkedDimensions: []
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Warning
    category: NC.Sys_Hidden_Danger&VM.Availability
  levels:
    - name: critical
      expression: ({{temperature_avg}} < 5 or {{temperature_avg}} > 30) or ({{humidity_avg}} < 20 or {{humidity_avg}} > 80)
      notify: ["SlsAlertHandler", "EventCenterAlertHandler"]
      hasVmThenAlert: false
      phoneCall: false
      needRelatedVm: false
  tag: PerformanceImpact
  splitTask: true
  maxTraceLog: 10000
  powerSQL: false

cloudbox_ebs_space_exception:
  interval: 60
  timeRange: 75
  enable: true
  doc: '云盒产品块存储容量使用过高'
  type: nc_hardware_event
  query: '*'
  analyse: SELECT cb_id as nc_ip, region as ext1, azone as ext2, ali_uid as extension, min(time) as exception_time, avg(used_ratio_v2) as used_ratio_avg GROUP BY nc_ip, region, azone, ali_uid HAVING nc_ip != 'null' LIMIT 1000
  logstore: ebs_smarthosting
  slsConfigName: cloudbox_monitor
  silent: 7180
  retentionTime: 15
  linkedDimensions: []
  tag: ControlFailed
  levels:
    - name: critical
      needRelatedVm: false
      expression: '{{used_ratio_avg}} > 0.85'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Warning
    category: NC.Sys_Hidden_Danger&VM.Availability

cloudbox_compute_resource_exception:
  interval: 60
  timeRange: 75
  enable: true
  doc: '云盒产品计算资源容量使用过高'
  type: nc_hardware_event
  query: '*'
  analyse: SELECT cb_id as nc_ip, region as ext1, azone as ext2, ali_uid as extension, min(from_unixtime(__time__)) as exception_time, avg(usedVcpu/totalVcpu) as used_ratio_avg GROUP BY nc_ip, region, azone, ali_uid HAVING nc_ip != 'null' LIMIT 1000
  logstore: ecs_inventory
  slsConfigName: cloudbox_monitor
  silent: 7180
  retentionTime: 15
  linkedDimensions: []
  tag: ControlFailed
  levels:
    - name: critical
      needRelatedVm: false
      expression: '{{used_ratio_avg}} > 0.9'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Warning
    category: NC.Sys_Hidden_Danger&VM.Availability

cloudbox_xgw_bandwidth_exception:
  interval: 60
  timeRange: 75
  enable: true
  doc: '云盒产品XGW流量水位过高'
  type: nc_network_event
  query: '*'
  analyse: SELECT cb_id as nc_ip, region as ext1, az as ext2, ali_uid as extension, min(begin_time) as exception_time, avg(in_bps) as in_bps_avg, avg(out_bps) as out_bps_avg GROUP BY nc_ip, region, az, ali_uid HAVING nc_ip != 'null' LIMIT 1000
  logstore: xgw_monitor
  slsConfigName: cloudbox_monitor
  silent: 7180
  retentionTime: 15
  linkedDimensions: []
  tag: ControlFailed
  levels:
    - name: critical
      needRelatedVm: false
      expression: '{{in_bps_avg}} > 17600000000 or {{out_bps_avg}} > 17600000000'
      notify: [SlsAlertHandler,EventCenterAlertHandler]
  eventCenter:
    push: true
    opsCode: SystemFailure
    impact: Alert
    resourceStatus: Warning
    category: NC.Sys_Hidden_Danger&VM.Availability

cloudbox_bandwidth_full:
  interval: 60
  timeRange: 75
  enable: true
  doc: '云盒专线带宽打满'
  type: nc_network_event
  query: '(tx_bandwidth_utilization or rx_bandwidth_utilization) and avg_value >= 0.9'
  analyse: 'select ncIp as nc_ip,from_unixtime(cast(max(timestamp) as bigint) / 1000) as exception_time,metricName as reason,instanceId as ext3,avg(avg_value) as util group by nc_ip,ext3,reason limit 1000'
  logstore: cloud_monitor_data_collector
  slsConfigName: xunjian_zhangbei
  silent: 980
  detailMetric: queryCloudBoxBandwidthUtil
  retentionTime: 15
  linkedDimensions: [NC_INFO]
  tag: ControlFailed
  powerSQL: true
  levels:
    - name: critical
      expression: '{{util}} >= 0.9'
      notify: [SlsAlertHandler]

cloudbox_sec_gateway_vm_error:
  interval: 60
  timeRange: 100
  enable: true
  doc: 云盒安全网关VM异常
  type: asw_network_hardware
  query: 'aliUid: 1158703430621731 and not instanceId: "" and not cloudbox_sec_gateway_vm_error and not ecs_alarm_agent_cache_increased and not ht_pedestal_end and not vm_detect_root_cause_by_change and not status: Shutted and not high_risk_ag_ssh_cmd'
  analyse: select count(*) as err_cnt,min(exceptionTime) as exception_time,concat(exceptionName,':',additionalInfo) as ext3,upper(regexp_extract(hostname,'\w+\.[\w\-]+\.([\w]+)',1)) as machine_id,cluster_alias as cluster_name,instanceId as reason,instanceId as extension group by machine_id,reason,cluster_name,ext3 HAVING machine_id is not null limit 10000
  logstore: monitor_exception_sls_alert
  slsConfigName: xunjian_zhangbei
  silent: 980
  retentionTime: 15
  detailMetric: ''
  linkedDimensions: [NC_INFO, MULTI_NC_INFO]
  levels:
    - name: critical
      expression: '{{err_cnt}} >= 1 and {{MULTI_NC_INFO.isValid}}'
      notify: ["SlsAlertHandler"]
      hasVmThenAlert: false
      needRelatedVm: true
  tag: PerformanceImpact
  splitTask: true
  maxTraceLog: 10000
  powerSQL: true

cloudbox_network_error:
  interval: 30
  timeRange: 50
  enable: true
  doc: '分布式云专线网络异常'
  type: asw_network_hardware
  query: 'exceptionLevel:critical'
  analyse: select 1 as checked,if(position('CB' in idc)=1,idc,regionId) as machine_id,regexp_replace(max_by(exceptionMessage,time),'\d','') as ext3,min(time) as exception_time group by regionId,idc limit 1000
  logstore: cloudbox_issue_report
  slsConfigName: ecs_ais_hardware
  region: [default]
  silent: 1200
  retentionTime: 15
  linkedDimensions: [NC_INFO, MULTI_NC_INFO]
  tag: ControlFailed
  levels:
    - name: warning
      expression: '{{checked}} == 1 and {{MULTI_NC_INFO.isValid}}'
      notify: [SlsAlertHandler]
      hasVmThenAlert: false

cloudbox_idc_power_on:
  interval: 58
  timeRange: 63
  enable: true
  doc: '云盒idc上电事件'
  type: asw_network_hardware
  query: 'event:ECS_REBOOT_READY'
  analyse: select 1 as checked,upper(idc) as machine_id,event as ext3 limit 1000
  logstore: cloudbox_idc_report
  slsConfigName: ecs_ais_hardware
  region: [default]
  silent: 1200
  retentionTime: 15
  linkedDimensions: [NC_INFO, MULTI_NC_INFO]
  levels:
    - name: normal
      expression: '{{checked}} == 1 and {{MULTI_NC_INFO.isValid}}'
      notify: [SlsAlertHandler]
      hasVmThenAlert: false

cloudbox_sec_gateway_error:
  interval: 30
  timeRange: 50
  enable: true
  doc: '分布式云安全网关异常'
  type: asw_network_hardware
  query: 'exceptionLevel:critical'
  analyse: select 1 as checked,if(position('CB' in idc)=1,idc,regionId) as machine_id,max(metricName) as ext3,from_unixtime(min(timestamp)) as exception_time group by regionId,idc limit 1000
  logstore: cloudbox_sec_gateway_report
  slsConfigName: ecs_ais_hardware
  region: [default]
  silent: 1200
  retentionTime: 15
  linkedDimensions: [NC_INFO, MULTI_NC_INFO]
  tag: ControlFailed
  levels:
    - name: warning
      expression: '{{checked}} == 1 and {{MULTI_NC_INFO.isValid}}'
      notify: [SlsAlertHandler]
      hasVmThenAlert: false

cloudbox_wait_user_accept_event_too_long:
  interval: 900
  timeRange: 1200
  enable: true
  doc: '云盒等待用户响应事件太久'
  type: nc_hardware_event
  query: 'name:WaitUserAcceptEvent and "workitem.workflowId":378 and status:Retry'
  analyse: select 1 as checked,'None' as nc_ip,scheduleId as scheduleid,from_unixtime(max(__time__)) as exception_time where __time__ - createTime / 1000 > 172800 group by scheduleid limit 1000
  logstore: cloudops_schedule_log
  slsConfigName: ecs_inspector
  region: [default]
  linkedQueryList: [queryCloudboxNcByScheduleid(scheduleid@or)]
  silent: 1800
  retentionTime: 7
  tag: ControlFailed
  linkedDimensions: [NC_INFO]
  levels:
    - name: low_warning
      expression: '{{checked}} == 1 and {{nc_ip}} != "None"'
      notify: [SlsAlertHandler]
      hasVmThenAlert: false
      needRelatedVm: false

batch_nc_dns_ping_error:
  interval: 103
  timeRange: 1800
  enable: true
  doc: '同IDC批量NC ping DNS Server不通'
  type: nc_hardware_event
  query: 'exceptionName:nc_dns_ping_exception and warningValue >= 2000000 and not AM5'
  analyse: select idc as machine_id,count(DISTINCT ncIp ) as nc_cnt,max(exceptionTime ) as exception_time,array_agg(DISTINCT ncIp) as ext3 group by idc  having nc_cnt >= 4 limit 10000
  logstore: monitor_exception_sls_alert
  slsConfigName: ecs_inspector
  region: [default]
  silent: 1800
  retentionTime: 12
  tag: PerformanceImpact
  linkedDimensions: [NC_INFO]
  levels:
    - name: low_warning
      expression: '{{nc_cnt}} >= 4'
      notify: [SlsAlertHandler]
      hasVmThenAlert: false
      needRelatedVm: false

nc_dns_ping_exception:
  interval: 97
  timeRange: 110
  enable: true
  doc: 'NC到DNS server Ping延迟出现异常'
  type: nc_network_sys
  query: 'latency >= 300'
  analyse: select host_ip as nc_ip,dns_server as ext3,min(from_unixtime(timestamp)) as exception_time,max(latency) as max_latency group by nc_ip,ext3 limit 1000
  logstore: ptp_dns_ping
  slsConfigName: ptp_summary_data
  silent: 7180
  retentionTime: 15
  detailMetric: queryDnsPingLatency
  tag: PerformanceImpact
  linkedDimensions: [NC_INFO]
  region: [default]
  levels:
    - name: fatal
      expression: '{{max_latency}} >= 1000000'
      notify: [ SlsAlertHandler ]
    - name: warning
      expression: '{{max_latency}} >= 300'
      notify: [ SlsAlertHandler ]

rack_power_error_empty_nc_shutdown:
  interval: 100
  timeRange: 3600
  enable: true
  doc: '机架功耗超电关空NC'
  type: nc_cpu_available
  query: 'ruleName:rack_power_error_empty_nc_shutdown and (status:Pass or status:Limit)'
  analyse: select *, to_unixtime(now())-to_unixtime(first_event_time) as delta_time from (select resourceId as nc_ip, arbitrary(status) as extension,date_format(min(eventTime)/1000,'%Y-%m-%d %H:%i:%S') as first_event_time from log GROUP BY resourceId)
  logstore: cloudops_xdc_plan_event
  slsConfigName: ecs_inspector
  region: [default]
  alarmId: NC_down
  tag: OpsAction
  silent: 0
  retentionTime: 40
  linkedDimensions: ['NC_INFO']
  levels:
    - name: normal
      expression: '{{delta_time}}<=200'
      notify: [SlsAlertHandler,FailureAlertHandler]

idc_change_event_server_may_lost_power:
  interval: 80
  timeRange: 120
  enable: true
  doc: 基础设施变更可能导致服务器宕机
  type: nc_hardware_event
  query: 'not sn: "" and (outGuarantee: 否 or outGuarantee: 是)'
  analyse: select distinct 1 as error,sn as cnSn,sn as machine_id,sn as ncSn, riskLevel as reason,riskReason as ext3,outGuarantee limit 1000
  logstore: ais_power_change_notify
  slsConfigName: ecs_ais_hardware
  silent: 43200
  region: [default]
  retentionTime: 14
  tag: Event
  exclusion:
    ext: '"{{NC_INFO}}" == "None"'
  linkedDimensions: [NC_INFO,DRAGONBOX_CN_INFO]
  levels:
    - name: fatal
      expression: '{{error}} >= 1 and "{{outGuarantee}}" != "是"'
      notify: ["SlsAlertHandler"]
      phoneCall: false
      needRelatedVm: true
    - name: low_warning
      expression: '{{error}} >= 1 and "{{outGuarantee}}" == "是"'
      notify: ["SlsAlertHandler"]
      phoneCall: false
      needRelatedVm: true
  splitTask: true
  maxTraceLog: 10000
  powerSQL: false

vm_power_high_in_rack:
  interval: 589
  timeRange: 620
  enable: true
  doc: '超电机架中功耗高的VM'
  type: vm_cpu_event
  query: '*'
  tag: Event
  analyse: select ncIp as nc_ip,instanceId as machine_id,min(from_unixtime(__time__)),count(*) as error_cnt,max(cpuUtilAvg) as util group by nc_ip,machine_id order by util desc limit 1000
  logstore: nc_power_over_manger_processor
  slsConfigName: xunjian_zhangbei
  silent: 280
  retentionTime: 10
  detailMetric: queryLiveMigrateNetworkDetail
  linkedDimensions: [NC_INFO,HOUYI_NC_INFO,VM_INFO,USER_INFO]
  levels:
    - name: warning
      expression: '{{error_cnt}} > 0'
      notify: [SlsAlertHandler]