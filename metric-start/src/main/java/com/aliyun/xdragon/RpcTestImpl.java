package com.aliyun.xdragon;

import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.odps.data.Record;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.aliyun.xdragon.api.service.test.RpcTest;
import com.aliyun.xdragon.api.service.workitem.AoneTopService;
import com.aliyun.xdragon.biz.clientops.config.EventCenterLogConfig;
import com.aliyun.xdragon.biz.log.job.LogScoreProcessor;
import com.aliyun.xdragon.biz.log.job.CalculateMetricCheckConfig;
import com.aliyun.xdragon.biz.log.job.LostPointCheckJob;
import com.aliyun.xdragon.biz.log.manager.param.AutoPilotParam;
import com.aliyun.xdragon.biz.log.manager.param.AutoPilotParam.AdvancedParam;
import com.aliyun.xdragon.biz.log.manager.param.AutoPilotParam.LimitParam;
import com.aliyun.xdragon.biz.log.manager.param.AutoPilotParam.ScaleDownParam;
import com.aliyun.xdragon.biz.log.manager.param.AutoPilotParam.ScaleUpParam;
import com.aliyun.xdragon.biz.log.manager.vvp.VvpTaskManager;
import com.aliyun.xdragon.biz.log.repository.ClusterRegionConfigDao;
import com.aliyun.xdragon.biz.log.repository.ClusterTaskDao;
import com.aliyun.xdragon.biz.log.repository.GlobalVersionSourceSlsDao;
import com.aliyun.xdragon.biz.log.repository.LogAnomalyAoneDao;
import com.aliyun.xdragon.biz.log.repository.LogCoverageConfigDao;
import com.aliyun.xdragon.biz.log.repository.LogQualityScoreDao;
import com.aliyun.xdragon.biz.log.repository.LogUnionTaskDao;
import com.aliyun.xdragon.biz.log.repository.PatternDao;
import com.aliyun.xdragon.biz.log.repository.PatternDistributionSlsDao;
import com.aliyun.xdragon.biz.log.repository.PatternMappingDao;
import com.aliyun.xdragon.biz.log.service.LogFlinkTaskService;
import com.aliyun.xdragon.biz.log.service.MetricsPickupService;
import com.aliyun.xdragon.biz.log.service.PatternDistributionService;
import com.aliyun.xdragon.biz.log.service.PickUpConfigService;
import com.aliyun.xdragon.biz.metric.job.MetricImportJob;
import com.aliyun.xdragon.biz.metric.repository.MetricImportDao;
import com.aliyun.xdragon.common.enumeration.log.ClusterTaskStatus;
import com.aliyun.xdragon.common.enumeration.log.StreamingTaskType;
import com.aliyun.xdragon.common.generate.c9.model.ClusterCntDo;
import com.aliyun.xdragon.common.generate.c9.model.NcCntDo;
import com.aliyun.xdragon.common.generate.c9.model.NcDo;
import com.aliyun.xdragon.common.generate.log.model.ClusterDistributionDetail;
import com.aliyun.xdragon.common.generate.log.model.HouyiEvent;
import com.aliyun.xdragon.common.generate.log.model.LogAnomalyAone;
import com.aliyun.xdragon.common.generate.model.LogClusterConfigRegion;
import com.aliyun.xdragon.common.generate.model.LogClusterTask;
import com.aliyun.xdragon.common.generate.model.LogCoverageConfig;
import com.aliyun.xdragon.common.generate.model.LogQualityScore;
import com.aliyun.xdragon.common.generate.model.LogUnionTask;
import com.aliyun.xdragon.common.generate.model.MetricImport;
import com.aliyun.xdragon.common.model.AoneComment;
import com.aliyun.xdragon.common.model.SlsProjectGroup;
import com.aliyun.xdragon.common.model.SlsRegionProject;
import com.aliyun.xdragon.common.model.SlsUserInfo;
import com.aliyun.xdragon.common.model.XdragonMetricResponse;
import com.aliyun.xdragon.common.model.acl.AclBase;
import com.aliyun.xdragon.common.model.buc.BucSimpleUser;
import com.aliyun.xdragon.common.model.gts.UidInfo;
import com.aliyun.xdragon.common.model.log.PlatformParam;
import com.aliyun.xdragon.common.model.log.SlsSourceParam;
import com.aliyun.xdragon.common.model.workItem.WorkItem;
import com.aliyun.xdragon.common.util.MDCUtil;
import com.aliyun.xdragon.service.common.agent.AclClient;
import com.aliyun.xdragon.service.common.agent.BucClient;
import com.aliyun.xdragon.service.common.agent.EdmClient;
import com.aliyun.xdragon.service.common.agent.GtsClient;
import com.aliyun.xdragon.service.common.agent.OdpsClient;
import com.aliyun.xdragon.service.common.service.MetricEncryptor;
import com.aliyun.xdragon.service.common.util.DateUtil;
import com.aliyun.xdragon.service.common.util.FileUtil;
import com.aliyun.xdragon.service.common.util.SlsUtil;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.gson.Gson;
import com.taobao.api.response.KeludeIssueGetResponse.Issue;
import com.taobao.api.response.KeludeIssueSearchResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.SafeConstructor;

import java.io.InputStream;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.aliyun.xdragon.common.enumeration.log.ClusterTaskStatus.DELETED;
import static com.aliyun.xdragon.common.enumeration.log.ClusterTaskStatus.RUNNING;

/**
 * <AUTHOR>
 * @date 2022/06/30
 */
@Service
public class RpcTestImpl implements RpcTest {
    private static final Logger logger = LoggerFactory.getLogger(RpcTestImpl.class);

    private static final Gson gson = new Gson();

    ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("test-pool-%d").build();
    ExecutorService executorService = new ThreadPoolExecutor(32, 32, 0, TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(1024), threadFactory, new ThreadPoolExecutor.AbortPolicy());

    @Autowired
    private MetricEncryptor metricEncryptor;

    @Autowired
    private CalculateMetricCheckConfig calculateMetricCheckConfig;

    @Autowired
    private LostPointCheckJob lostPointCheckJob;

    @Autowired
    private LogCoverageConfigDao logCoverageConfigDao;

    @Autowired
    private PickUpConfigService pickUpConfigService;

    @Autowired
    private OdpsClient odpsClient;

    @Autowired
    private MetricsPickupService metricsPickupService;

    @Autowired
    private EventCenterLogConfig eventCenterLogConfig;

    @Autowired
    private LogFlinkTaskService logFlinkTaskService;

    @Autowired
    private ClusterTaskDao clusterTaskDao;

    @Autowired
    private PatternDistributionSlsDao patternDistributionDao;

    @Autowired
    private MetricImportDao importDao;

    @Autowired
    private MetricImportJob metricImportJob;

    @Autowired
    private GlobalVersionSourceSlsDao globalVersionSourceDao;

    @Autowired
    private AoneTopService aoneTopService;

    @Autowired
    private PatternDistributionService distributionService;

    @Autowired
    private ClusterRegionConfigDao clusterRegionConfigDao;

    @Autowired
    private LogUnionTaskDao logUnionTaskDao;

    @Autowired
    private GtsClient gtsClient;

    @Autowired(required = false)
    private AclClient aclClient;

    @Autowired
    private BucClient bucClient;

    @Autowired
    private EdmClient edmClient;

    @Autowired
    private LogQualityScoreDao logQualityScoreDao;
    @Autowired
    private PatternDao patternDao;
    @Autowired
    private LogAnomalyAoneDao logAnomalyAoneDao;

    @Autowired
    private VvpTaskManager vvpTaskManager;
    @Autowired
    private PatternMappingDao patternMappingDao;

    @Override
    public XdragonMetricResponse<String> getValue(String key) {
        try {
            return XdragonMetricResponse.ofSuccess(metricEncryptor.decrypt(key));
        } catch (Exception e) {
            return XdragonMetricResponse.ofSuccess(key);
        }
    }

    @Override
    public void testLost(Long anomalyConfigId) throws Exception {
        int startTs = 1668459600;
        int endTs = 1670306400;
        while (endTs >= (startTs += 1800)) {
            logger.info("check {}", startTs);
            if (startTs % 86400 == 75600) {
                logger.info("calculate metric threshold for {}", startTs);
                calculateMetricCheckConfig.processSubTask(null, startTs, startTs, anomalyConfigId);
            }

            lostPointCheckJob.processSubTask(null, startTs, startTs, anomalyConfigId);
        }
    }

    /**
     * parse user ak, generate SLS_USER config item
     *
     * @return
     */
    @Override
    public String parse() {
        String kv = "cloud_monitor_ak aaa\n" +
            "cloud_monitor_sk bbb";
        String[] lines = kv.split("\n");
        Map<String, SlsUserInfo> uses = new HashMap<>();
        for (String line : lines) {
            String[] segs = line.split(" ");
            if (segs.length >= 2) {
                String user = parseUser(segs[0]);
                SlsUserInfo info = null;
                if (uses.containsKey(user)) {
                    info = uses.get(user);
                } else {
                    info = new SlsUserInfo();
                    info.setName(user);
                    info.setDescription(user);
                    uses.put(user, info);
                }
                if (segs[0].endsWith("_ak") || segs[0].endsWith("_ak_new")) {
                    info.setAk(metricEncryptor.encrypt(segs[1]));
                } else if (segs[0].endsWith("_sk") || segs[0].endsWith("_sk_new")) {
                    info.setSk(metricEncryptor.encrypt(segs[1]));
                }
            }
        }
        Gson gson = new Gson();
        return gson.toJson(uses.values());
    }

    private String parseUser(String ak) {
        if (ak.startsWith("${")) {
            ak = ak.substring(2);
        }
        if (ak.endsWith("}")) {
            ak = ak.substring(0, ak.length() - 1);
        }
        if (ak.endsWith("_sls_ak") || ak.endsWith("_sls_sk")) {
            ak = ak.substring(0, ak.length() - 7);
        }
        if (ak.endsWith("_ak") || ak.endsWith("_sk")) {
            ak = ak.substring(0, ak.length() - 3);
        }
        if (ak.endsWith("_ak_new") || ak.endsWith("_sk_new")) {
            ak = ak.substring(0, ak.length() - 7) + "_new";
        }
        return ak;
    }

    /**
     * check sls project config
     */
    @Override
    public void checkSls() {
        List<LogCoverageConfig> configList = logCoverageConfigDao.queryConfigByCycle(true);
        configList.addAll(logCoverageConfigDao.queryConfigByCycle(false));

        Map<String, SlsUserInfo> users = pickUpConfigService.loadSlsUsers();
        Map<String, String> regionEndpoint = pickUpConfigService.loadRegionEndpoint();
        List<SlsProjectGroup> projectGroups = pickUpConfigService.loadProjectGroupInfo();
        Map<String, SlsProjectGroup> name2groups = new HashMap<>(64);

        logger.info("check user, endpoint, project group config");
        for (SlsProjectGroup projectGroup : projectGroups) {
            List<SlsRegionProject> regionProjects = projectGroup.getRegions();
            name2groups.put(projectGroup.getProject(), projectGroup);
            if (projectGroup.getAlias() != null && projectGroup.getAlias().size() > 0) {
                for (String alias : projectGroup.getAlias()) {
                    name2groups.put(alias, projectGroup);
                }
            }
            for (SlsRegionProject regionProject : regionProjects) {
                String region = regionProject.getRegion();
                String user = regionProject.getUser();
                String endpoint = regionProject.getEndpoint();
                if (!users.containsKey(user)) {
                    logger.error("no sls user info about user {}", user);
                }
                if (StringUtils.isBlank(endpoint)) {
                    if (!regionEndpoint.containsKey(region)) {
                        logger.error("no endpoint info about region {}", region);
                    }
                }
            }
        }

        logger.info("check log coverage config");
        for (LogCoverageConfig config : configList) {
            String project = config.getProject();
            String region = config.getRegion();
            if (!name2groups.containsKey(project)) {
                logger.error("miss project group in REGION_PROJECT config item, group name is {}", project);
            } else {
                if (StringUtils.isNotBlank(region)) {
                    boolean find = false;
                    List<SlsRegionProject> regionProjects = name2groups.get(project).getRegions();
                    for (SlsRegionProject regionProject : regionProjects) {
                        if (region.equals(regionProject.getRegion())) {
                            find = true;
                            break;
                        }
                    }
                    if (!find) {
                        logger.error(
                            "miss region project in REGION_PROJECT config item, project group is {}, region is {}",
                            project, region);
                    }
                }
            }
        }
    }


    //@Override
    //public void sumDetail(List<Long> tasks, Integer startTs, Integer endTs) {
    //    if (tasks == null || tasks.size() == 0) {
    //        List<LogClusterTask> tt = clusterTaskDao.listTask(null, null, true);
    //        tasks = new LinkedList<>();
    //        for (LogClusterTask t : tt) {
    //            tasks.add(t.getTaskId());
    //        }
    //    }
    //    int step = 1800;
    //    while (startTs < endTs) {
    //        logger.info("start sum detail with ts: {}", startTs);
    //        try {
    //            for (Long taskId : tasks) {
    //                logger.info("start sum detail with ts: {}, task {}", startTs, taskId);
    //                Date start = new Date(startTs * 1000L);
    //                Date end = new Date((startTs + step) * 1000L - 1000L);
    //                patternDistributionDao.deleteDetail(taskId, start, end, true);
    //                patternDistributionDao.sumDetail(taskId, start, end);
    //                Thread.sleep(100L);
    //            }
    //            Thread.sleep(1000L);
    //        } catch (InterruptedException e) {
    //            logger.info("sleep error", e);
    //        } finally {
    //            startTs += step;
    //        }
    //    }
    //}



    @Override
    public void metricImport(List<Long> importIds, Integer startTs, Integer endTs, boolean ignoreLastTs)
        throws Exception {
        List<MetricImport> tasks = importDao.listConfigs();
        if (importIds != null && importIds.size() > 0) {
            tasks.removeIf(t -> !importIds.contains(t.getId()));
        }

        List<MetricImport> expandTasks = metricImportJob.preprocessTask(tasks, !ignoreLastTs, startTs);
        Integer finalStartTs = startTs;
        expandTasks.forEach(t -> t.setLastAggrTime(finalStartTs - 120));
        Map<String, String> logMdc = MDC.getCopyOfContextMap();
        while (startTs < endTs) {
            List<Future<ProcessResult>> fs = new LinkedList<>();
            for (MetricImport subTask : expandTasks) {
                final int subStart = startTs;
                Future<ProcessResult> f = executorService.submit(() -> {
                    MDCUtil.safeSetMdc(logMdc);
                    return metricImportJob.processSubTask(null, subStart, subStart, subTask);
                });
                fs.add(f);
            }
            for (Future<ProcessResult> f : fs) {
                try {
                    ProcessResult r = f.get();
                    logger.info("time: {}, status: {}", startTs, r.getStatus().toString());
                } catch (ExecutionException | InterruptedException e) {
                    logger.error("run fail", e.getCause());
                }
            }
            startTs += 60;
        }
    }



    @Override
    public List<ClusterDistributionDetail> testCache(List<String> ips, long startMs, long endMs) {
        //List<ClusterDistributionDetail> ret = distributionService.getValidNcMd5Map(ips, startMs, endMs);
        //return ret;
        return null;
    }


    @Override
    public List<AoneComment> getAoneComment(long issueId) {
        return aoneTopService.getAoneIssueComment(issueId);
    }

    @Override
    public boolean addAoneComment(Long issueId, String comment, String user) {
        return aoneTopService.addAoneComment(issueId, comment, user);
    }



    //@Override
    //public long delVersionSource(long ms, int size) {
    //    long cnt = 0;
    //    while (true) {
    //        int deled = globalVersionSourceDao.cleanByTime(new Date(ms), size);
    //        cnt += deled;
    //        if (deled < size) {
    //            break;
    //        }
    //    }
    //    return cnt;
    //}

    @Override
    public List<Long> aoneSearch(long startMs, long endMs) {
        WorkItem workItem = new WorkItem();
        workItem.setAkProjectId(1182328L);
        workItem.setModuleIdList(Lists.newArrayList(348650));
        workItem.setUpdatedAtFrom(new Date(startMs));
        workItem.setUpdatedAtTo(new Date(endMs));
        KeludeIssueSearchResponse resp = aoneTopService.searchWorkItem(workItem);
        List<Long> ids = new LinkedList<>();
        resp.getResults().forEach(r -> ids.add(r.getId()));
        logger.info("get {} aone from {} to {}", ids.size(), new Date(startMs), new Date(endMs));
        return ids;
    }

    @Override
    public void fixAoneCloseTime(Long issueId) {
        List<LogAnomalyAone> aones = logAnomalyAoneDao.queryAllAones();
        List<String> openstatus = Lists.newArrayList("New", "Open", "Reopen", "Later", "Worksforme");
        aones.forEach(aone -> {
            Issue issue = aoneTopService.searchWorkItemById(aone.getAoneId());
            if (!openstatus.contains(issue.getStatus())) {
                logger.error("issueid {}, status {}, time {}", aone.getAoneId(), issue.getStatus(), issue.getUpdatedAt());
                logAnomalyAoneDao.updateStatusAndHandler(aone.getAoneId(), aone.getStatus(), aone.getHandler(), issue.getUpdatedAt());
            }
        });
    }

    @Override
    public List<NcDo> getNcDo(String ip) {
        return edmClient.getNcStatusByNcIps(Lists.newArrayList(ip));
    }

    @Override
    public List<NcDo> getNewlyAddedMachines(String start, String end) {
        return edmClient.getNewlyAddedMachines(start, end);
    }

    @Override
    public List<NcDo> queryModifiedMachines(String startTime, String endTime, int startIndex, int size) {
        return edmClient.queryModifiedMachines(startTime, endTime, startIndex, size);
    }

    @Override
    public List<ClusterCntDo> getClusterMachineCnt(List<String> clusters) {
        return edmClient.getClusterMachineCnt(clusters);
    }

    @Override
    public List<NcCntDo> getNcCountWithSurviveVm(List<String> ncIds) {
        return edmClient.getNcCountWithSurviveVm(ncIds);
    }

    @Override
    public List<NcDo> getNcIpsByStatuses(List<String> statuses, Integer start, Integer cnt) {
        return edmClient.getNcIpsByStatuses(statuses, start, cnt);
    }

    @Override
    public int getAllOnlineNc() {
        return edmClient.getAllOnlineNc().size();
    }

    @Override
    public int getAllOnlineNcByPage(int start, int size) {
        return edmClient.getAllOnlineNcByPage(start, size).size();
    }

    @Override
    public List<HouyiEvent> queryHouyiEventDim(List<String> vms) {
        return edmClient.queryHouyiEventDim(vms);
    }

    @Override
    public Long queryCid(String v, boolean vm) {
        if (vm) {
            return edmClient.getVmCid(v);
        } else {
            return edmClient.getUserCid(v);
        }
    }

    @Override
    public long downloadTable(String table, String partition) {
        List<Record> ls = odpsClient.downloadConcurrent(null, table, partition);
        return ls.size();
    }

    @Override
    public void loadUserVmCountToSls(int startTs, int endTs) {
        int step = 1800;
        Client client = metricsPickupService.getSlsClient(eventCenterLogConfig.getRegion(), null,
            eventCenterLogConfig.getUser());
        while (startTs < endTs) {
            String pt = DateUtil.timestamp2str(startTs, "yyyyMMddHHmm");
            logger.info("load data to sls, pt: {}", pt);
            List<Record> ls = odpsClient.downloadConcurrent(null, "user_vm_count", "ds=" + pt);

            List<LogItem> items = new LinkedList<>();
            for (Record r : ls) {
                LogItem item = new LogItem(startTs);
                item.PushBack("ali_uid", r.getString("ali_uid"));
                item.PushBack("az_name", r.getString("az_name"));
                item.PushBack("cid", r.getString("cid"));
                item.PushBack("ds", pt);
                item.PushBack("region_alias", r.getString("region_alias"));
                item.PushBack("timestamp", r.getBigint("ts").toString());
                item.PushBack("vcpu_count", r.getBigint("cpu_count").toString());
                item.PushBack("vm_count", r.getBigint("vm_count").toString());
                items.add(item);
                if (items.size() >= 2000) {
                    try {
                        SlsUtil.putSls(client, eventCenterLogConfig.getProject(),
                            eventCenterLogConfig.getUserVmCountLogStore(), items);
                    } catch (Exception e) {
                        logger.error("put log error", e);
                    }
                    items.clear();
                }
            }

            if (!items.isEmpty()) {
                try {
                    SlsUtil.putSls(client, eventCenterLogConfig.getProject(),
                        eventCenterLogConfig.getUserVmCountLogStore(), items);
                } catch (Exception e) {
                    logger.error("put log error", e);
                }
            }
            startTs += step;
        }
    }

    @Override
    public BucSimpleUser bucTest(String empId) {
        BucSimpleUser ret = bucClient.getSimpleUserByEmpId(empId);
        return ret;
    }

    @Override
    public AclBase aclTest(String type, String empId, String permission) {
        BucSimpleUser ret = bucClient.getSimpleUserByEmpId(empId);
        if (type.equals("grant")) {
            return aclClient.grantPermissionsToUser(ret.getContent().getUserId(), permission, "2024-12-30 12:00:00",
                "test");
        } else if (type.equals("revoke")) {
            return aclClient.revokePermissionsToUser(ret.getContent().getUserId(), permission, "test");
        } else {
            return null;
        }
    }

    @Override
    public UidInfo queryUidInfo(String uid) {
        return gtsClient.queryUidInfo(uid);
    }

    @Override
    public List<Long> queryUidsByCid(Long cid) {
        return gtsClient.queryUidsByCid(cid);
    }

    //@Autowired(required = false)
    //private TamPermissionConsumer tamPermissionConsumer;
    //
    //@Autowired
    //private ConfigService configService;
    //
    //@Autowired
    //private ClientResourceDao clientResourceDao;
    //
    //@Autowired
    //private ClientopsAutoPermissionLogDao clientopsAutoPermissionLogDao;
    //
    //@Override
    //public void loadTamInfo(String userList) {
    //    userList = configService.loadCidTam();
    //    String READ_PERMISSION = "clientops_read";
    //    String WRITE_PERMISSION = "clientops_write";
    //    long PERMISSION_DURATION_MS = 90 * 24 * 3600 * 1000L;
    //    Map<String, BucSimpleUser> userMap = new HashMap<>();
    //    Set<String> permissionGrant = new HashSet<>();
    //    String[] userCid = userList.split("\n");
    //    List<String> userCidList = Arrays.asList(userCid);
    //    userCidList.sort(new Comparator<String>() {
    //        @Override
    //        public int compare(String o1, String o2) {
    //            return o1.compareTo(o2);
    //        }
    //    });
    //    int pos = 0;
    //    for (int i = pos; i < userCidList.size(); i++) {
    //        logger.info("start process line: {} / {}", pos, userCidList.size());
    //        String uc = userCidList.get(i);
    //        try {
    //            String[] userAndCid = uc.split(",");
    //            if (userAndCid.length < 2) {
    //                logger.warn("format error, length < 2, {}", uc);
    //            } else {
    //                String cid = userAndCid[0].trim();
    //                for (int j = 1; j < userAndCid.length; j++) {
    //                    String empid = userAndCid[j].trim();
    //                    try {
    //                        BucSimpleUser userinfo = userMap.getOrDefault(empid, null);
    //                        if (userinfo == null) {
    //                            userinfo = bucClient.getSimpleUserByEmpId(empid);
    //                            userMap.put(empid, userinfo);
    //                        }
    //                        if (userinfo == null) {
    //                            logger.warn("get userinfo by empId={} error", empid);
    //                            continue;
    //                        }
    //
    //                        long expTs = (System.currentTimeMillis() + PERMISSION_DURATION_MS) / 1000;
    //
    //                        if (!permissionGrant.contains(empid)) {
    //                            if (grantPermissionsToUser(empid, userinfo.getContent().getUserId(), READ_PERMISSION,
    //                                (int)expTs) && grantPermissionsToUser(empid, userinfo.getContent().getUserId(),
    //                                WRITE_PERMISSION, (int)expTs)) {
    //                                permissionGrant.add(empid);
    //                            } else {
    //                                continue;
    //                            }
    //                        } else {
    //                            logger.info("emp id {} already granted", empid);
    //                        }
    //
    //                        if (permissionGrant.contains(empid)) {
    //                            if (clientResourceDao.countResource(empid,
    //                                Lists.newArrayList(ClientResourceType.CID.getType()),
    //                                cid,
    //                                Lists.newArrayList(
    //                                    ClientResourceStatus.APPROVE_ACCEPT.getStatus(),
    //                                    ClientResourceStatus.CLOUDDOC_NOTIFY_ACCEPT.getStatus())) == 0L) {
    //                                if (clientResourceDao.addResource(empid, ClientResourceType.CID.getType(), cid,
    //                                    ClientResourceStatus.CLOUDDOC_NOTIFY_ACCEPT.getStatus(), "clouddoc auto add")
    //                                    <= 0) {
    //                                    logger.warn("add client resource fail, emp id {}, resource {}", empid, cid);
    //                                }
    //                            }
    //                        }
    //
    //                        logger.info("add permission success, emp id {}, cid {}", empid, cid);
    //                    } catch (Exception e) {
    //                        logger.warn("process emp id cid fail, emp id {}, cid {}", empid, cid);
    //                    }
    //                }
    //
    //            }
    //        } catch (Exception e) {
    //            logger.info("process record fail, record: {}", uc);
    //        } finally {
    //            pos ++;
    //        }
    //    }
    //
    //}
    //
    //private boolean grantPermissionsToUser(String empId, Integer userId, String permission, int expTs) {
    //    String expiredTime = DateUtil.timestamp2str(expTs);
    //    AclGrantPermission aclGrantPermission = aclClient.grantPermissionsToUser(userId, permission, expiredTime,
    //        "clouddoc add notify");
    //    if (aclGrantPermission == null || !aclGrantPermission.isSuccess()) {
    //        logger.warn("grant {} permission to user {} error", permission, empId);
    //        return false;
    //    }
    //    ClientopsAutoPermissionLog autoAddLog = new ClientopsAutoPermissionLog();
    //    autoAddLog.setEmpid(empId);
    //    autoAddLog.setPermission(permission);
    //    autoAddLog.setGrantTime((int)(System.currentTimeMillis() / 1000));
    //    autoAddLog.setExpireTime(expTs);
    //    autoAddLog.setSource(ClientPermissionSource.CLOUDDOC.getSource());
    //    if (clientopsAutoPermissionLogDao.getEmpPermission(empId, permission) == null) {
    //        clientopsAutoPermissionLogDao.addPermissionLog(autoAddLog);
    //    } else {
    //        clientopsAutoPermissionLogDao.updatePermissionLogWithEmpPermission(autoAddLog, empId, permission);
    //    }
    //
    //    return true;
    //}

    public boolean createUnionTask(Long taskId, String region, String realCenter) {
        LogClusterTask task = clusterTaskDao.getTaskById(taskId);
        PlatformParam platformParam = new PlatformParam();
        platformParam.setCpu(1.0);
        platformParam.setMemory("4Gi");
        platformParam.setParallelism(4L);
        if (StringUtils.isBlank(region)) {
            boolean ret = true;
            List<LogClusterConfigRegion> logClusterConfigRegions = clusterRegionConfigDao.listConfigsWithStatus(taskId,
                ClusterTaskStatus.RUNNING, null,  StreamingTaskType.DEFAULT.getType());
            for (LogClusterConfigRegion logClusterConfigRegion : logClusterConfigRegions) {
                SlsSourceParam sourceParam = gson.fromJson(logClusterConfigRegion.getInputSourceParam(),
                    SlsSourceParam.class);
                String subRegion = logClusterConfigRegion.getRegion();
                if ("center".equals(subRegion) && StringUtils.isNotBlank(realCenter)) {
                    subRegion = realCenter;
                }
                LogUnionTask f = logFlinkTaskService.createUnionTask(task, subRegion, sourceParam, platformParam, true);
                if (f.getPlatformId() == null) {
                    logger.warn("create union task fail, taskId: {}, region: {}", taskId, logClusterConfigRegion.getRegion());
                    ret = false;
                } else {
                    logger.info("create union task success, taskId: {}, region: {}", taskId, logClusterConfigRegion.getRegion());
                }
            }
            return ret;
        } else {
            List<LogClusterConfigRegion> logClusterConfigRegions = clusterRegionConfigDao.listConfigsWithStatus(taskId,
                ClusterTaskStatus.RUNNING, null, StreamingTaskType.DEFAULT.getType());
            SlsSourceParam sourceParam = gson.fromJson(logClusterConfigRegions.get(0).getInputSourceParam(),
                SlsSourceParam.class);
            if (!"center".equals(logClusterConfigRegions.get(0).getRegion())) {
                region = logClusterConfigRegions.get(0).getRegion();
            }
            LogUnionTask t = logFlinkTaskService.createUnionTask(task, region, sourceParam, platformParam, true);
            return t.getPlatformId() != null;
        }
    }

    public boolean startUnionTask(Long taskId, Long unionId) {
        LogClusterTask clusterTask = clusterTaskDao.getTaskById(taskId);
        if (unionId != null) {
            LogUnionTask unionTask = logUnionTaskDao.getUnionTaskById(unionId);
            return logFlinkTaskService.startUnionTask(clusterTask, unionTask, null);
        } else {
            List<LogUnionTask> unionTasks = logUnionTaskDao.getUnionTasksByTaskId(taskId, null);
            boolean ret = true;
            for (LogUnionTask unionTask : unionTasks) {
                boolean f = logFlinkTaskService.startUnionTask(clusterTask, unionTask, null);
                if (!f) {
                    logger.warn("start union task fail, unionId: {}", unionId);
                    ret = false;
                } else {
                    logger.info("start union task success, unionId: {}", unionId);
                }
            }
            return ret;
        }
    }

    public boolean stopUnionTask(Long unionId) {
        LogUnionTask unionTask = logUnionTaskDao.getUnionTaskById(unionId);
        return logFlinkTaskService.stopUnionTask(unionTask);
    }

    public boolean updateUnionTask(Long taskId, Long unionId) {
        if (taskId != null && taskId > 0) {
            LogClusterTask task = clusterTaskDao.getTaskById(taskId);
            List<LogUnionTask> unionTasks = logUnionTaskDao.getUnionTasksByTaskId(taskId, null);
            Map<String, String> mdcContext = MDC.getCopyOfContextMap();
            unionTasks.forEach(unionTask -> {
                executorService.submit(() -> {
                    if (mdcContext != null && !mdcContext.isEmpty()) {
                        MDC.setContextMap(mdcContext);
                    }
                    logger.info("update union task {} start", unionTask.getUnionId());
                    boolean ret = logFlinkTaskService.updateUnionTask(task, unionTask, null);
                    logger.info("update union task {}, result {}", unionTask.getUnionId(), ret);
                });
            });
            return true;

        } else {
            logger.info("update union task {}", unionId);
            LogUnionTask unionTask = logUnionTaskDao.getUnionTaskById(unionId);
            LogClusterTask task = clusterTaskDao.getTaskById(unionTask.getTaskId());
            return logFlinkTaskService.updateUnionTask(task, unionTask, null);
        }
    }

    public boolean deleteUnionTask(Long unionId) {
        LogUnionTask unionTask = logUnionTaskDao.getUnionTaskById(unionId);
        return logFlinkTaskService.deleteUnionTask(unionTask);
    }

    @Autowired
    LogScoreProcessor logScoreProcessor;

    @Override
    public void updateScore(long taskId) {
        List<LogQualityScore> scores = logQualityScoreDao.listAllRecords();

        //Map<Integer, Map<Long, Integer>> newlyCountMap = new HashMap<>();
        //Map<Integer, Map<Long, Integer>> anomalyCountMap = new HashMap<>();
        //scores.forEach(s -> {
        //    Date endDate = s.getEndTime();
        //    int endTs = (int)(endDate.getTime() / 1000);
        //    if (!newlyCountMap.containsKey(endTs)) {
        //        Map<Long, Integer> newlyCount = logScoreProcessor.queryOpeningNewly(endTs);
        //        newlyCountMap.put(endTs, newlyCount);
        //    }
        //    if (!anomalyCountMap.containsKey(endTs)) {
        //        Map<Long, Integer> anomalyCount = logScoreProcessor.queryOpeningAnomaly(endTs);
        //        anomalyCountMap.put(endTs, anomalyCount);
        //    }
        //    int count = newlyCountMap.get(endTs).getOrDefault(s.getTaskId(), 0);
        //    int count2 = anomalyCountMap.get(endTs).getOrDefault(s.getTaskId(), 0);
        //    LogQualityScore logQualityScore = new LogQualityScore();
        //    logQualityScore.setId(s.getId());
        //    logQualityScore.setNewlyCount(count);
        //    logQualityScore.setAnomalyCount(count2);
        //    if (s.getAnomalyScore() > 10.0) {
        //        logQualityScore.setAnomalyScore(10.0);
        //    } else {
        //        logQualityScore.setAnomalyScore(s.getAnomalyScore());
        //    }
        //    double appScore = LogQualityScoreDao.calAppHealthScore(s.getPatternRate(), logQualityScore.getNewlyCount(), logQualityScore.getAnomalyScore(), logQualityScore.getAnomalyCount());
        //    logQualityScore.setAppHealthScore(appScore);
        //    logQualityScoreDao.updateScoreRecord(logQualityScore);
        //});


        //Map<Integer, Map<Long, Double>> anamolyScoreMap = new HashMap<>();
        //scores.forEach(s -> {
        //    Date endDate = s.getEndTime();
        //    int endTs = (int)(endDate.getTime() / 1000);
        //    if (!anamolyScoreMap.containsKey(endTs)) {
        //        Map<Long, Double> taskScore = logScoreProcessor.queryAnomalyScore(endTs);
        //        anamolyScoreMap.put(endTs, taskScore);
        //    }
        //    double appScore = anamolyScoreMap.get(endTs).getOrDefault(s.getTaskId(), 0.0);
        //    LogQualityScore logQualityScore = new LogQualityScore();
        //    logQualityScore.setId(s.getId());
        //    logQualityScore.setAnomalyScore(appScore);
        //    logQualityScoreDao.updateScoreRecord(logQualityScore);
        //});

        //Map<Integer, Map<Long, Pair<Integer, Double>>> patternRateMap = new HashMap<>();
        //scores.forEach(s -> {
        //    Date endDate = s.getEndTime();
        //    int endTs = (int)(endDate.getTime() / 1000);
        //    if (!patternRateMap.containsKey(endTs)) {
        //        Map<Long, Pair<Integer, Double>> taskPatternCount = logScoreProcessor.queryPatternCountScore(endTs);
        //        patternRateMap.put(endTs, taskPatternCount);
        //    }
        //    Pair<Integer, Double> patternPair = patternRateMap.get(endTs).getOrDefault(s.getTaskId(), null);
        //    if (patternPair != null) {
        //        LogQualityScore logQualityScore = new LogQualityScore();
        //        logQualityScore.setId(s.getId());
        //        logQualityScore.setPatternRate(patternPair.getRight());
        //        logQualityScore.setPatternCount(patternPair.getLeft());
        //        logQualityScoreDao.updateScoreRecord(logQualityScore);
        //    }
        //
        //});

        //scores = logQualityScoreDao.listAllRecords();
        //scores.forEach(s -> {
        //    double appScore = LogQualityScoreDao.calAppHealthScore(s.getPatternRate(), s.getNewlyCount(), s.getAnomalyScore(), s.getAnomalyCount());
        //    LogQualityScore logQualityScore = new LogQualityScore();
        //    logQualityScore.setId(s.getId());
        //    logQualityScore.setAppHealthScore(appScore);
        //    logQualityScoreDao.updateScoreRecord(logQualityScore);
        //});

        //scores = logQualityScoreDao.listAllRecords();
        //
        scores.forEach(s -> {
            logger.info("update score for {}, start time {}", s.getId(), s.getStartTime());
            long ts = s.getStartTime().getTime();
            double avgScore = logQualityScoreDao.getAvgScore(s.getTaskId(), new Date(ts - 24 * 3600 * 1000L), s.getStartTime());
            LogQualityScore logQualityScore = new LogQualityScore();
            logQualityScore.setId(s.getId());
            logQualityScore.setDaySmoothScore(avgScore);
            logQualityScoreDao.updateScoreRecord(logQualityScore);
        });
    }

    @Override
    public void openUnionAutoPilot(Long taskId, Long unionId) {
        if (unionId != null && unionId > 0) {
            LogUnionTask task = logUnionTaskDao.getUnionTaskById(unionId);
            openUnionAutoPilot(task);
        } else {
            List<LogUnionTask> unionTasks = logUnionTaskDao.getUnionTasksByStatus(taskId, RUNNING.getStatus(), false);
            unionTasks.forEach(this::openUnionAutoPilot);
        }
    }

    private void openUnionAutoPilot(LogUnionTask task) {
        AutoPilotParam param = new AutoPilotParam();
        param.setDeploymentId(task.getPlatformId());
        param.setMode("AUTOPILOT_MODE_ACTIVE");
        LimitParam limitParam = new LimitParam();
        limitParam.setMaxCpu(250);
        limitParam.setMaxMemory(1000);
        limitParam.setMaxParallelism(128);
        param.setLimitParam(new LimitParam());
        param.setScaleUpParam(new ScaleUpParam());
        param.setScaleDownParam(new ScaleDownParam());
        param.setAdvancedParam(new AdvancedParam());

        boolean ret = vvpTaskManager.updateAutoPilotPolicy(param);
        logger.info("update auto pilot, deployment id {}, result {}", task.getPlatformId(), ret);
        if (ret) {
            ret = vvpTaskManager.updateAutoPilotMode(task.getPlatformId(), "AUTOPILOT_MODE_ACTIVE");
            logger.info("update auto pilot mode, deployment id {}, result {}", task.getPlatformId(), ret);
        }
    }

    @Override
    public void openSubTaskAutoPilot(Long taskId, Long cid) {
        if (cid != null && cid > 0) {
            LogClusterConfigRegion configRegion = clusterRegionConfigDao.getConfigByRegionCid(cid);
            openSubTaskAutoPilot(configRegion);
        } else {
            List<LogClusterConfigRegion> configRegions = clusterRegionConfigDao.listConfigsWithStatus(taskId, RUNNING, null, null);
            configRegions.forEach(this::openSubTaskAutoPilot);
            configRegions = clusterRegionConfigDao.listAllConfigsWithStatus(RUNNING, 2);
            configRegions.forEach(this::openSubTaskAutoPilot);
        }
    }

    private void openSubTaskAutoPilot(LogClusterConfigRegion configRegion) {
        AutoPilotParam param = new AutoPilotParam();
        param.setDeploymentId(configRegion.getPlatformId());
        param.setMode("AUTOPILOT_MODE_ACTIVE");
        LimitParam limitParam = new LimitParam();
        limitParam.setMaxCpu(250);
        limitParam.setMaxMemory(1000);
        limitParam.setMaxParallelism(128);
        if (configRegion.getTaskType().equals(1)) {
            param.setLimitParam(limitParam);
            param.setScaleUpParam(new ScaleUpParam());
            param.setScaleDownParam(new ScaleDownParam());
            param.setAdvancedParam(new AdvancedParam());
        } else if (configRegion.getTaskType().equals(2)) {
            param.setLimitParam(limitParam);
            param.setScaleUpParam(new ScaleUpParam());
            param.setScaleDownParam(new ScaleDownParam());
            AdvancedParam ap = new AdvancedParam();
            ap.setMinTMMemory(8);
            param.setAdvancedParam(ap);
        }
        boolean ret = vvpTaskManager.updateAutoPilotPolicy(param);
        logger.info("update auto pilot, deployment id {}, result {}", configRegion.getPlatformId(), ret);
        if (ret) {
            ret = vvpTaskManager.updateAutoPilotMode(configRegion.getPlatformId(), "AUTOPILOT_MODE_ACTIVE");
            logger.info("update auto pilot mode, deployment id {}, result {}", configRegion.getPlatformId(), ret);
        }
    }

    @Override
    public void openFlinkAutoPilot(List<String> deploymentIds) {
        deploymentIds.forEach(deploymentId -> {
            AutoPilotParam param = new AutoPilotParam();
            param.setDeploymentId(deploymentId);
            param.setMode("AUTOPILOT_MODE_ACTIVE");
            LimitParam limitParam = new LimitParam();
            //limitParam.setMaxCpu(100);
            //limitParam.setMaxMemory(400);
            param.setLimitParam(limitParam);
            param.setScaleUpParam(new ScaleUpParam());
            param.setScaleDownParam(new ScaleDownParam());
            param.setAdvancedParam(new AdvancedParam());
            boolean ret = vvpTaskManager.updateAutoPilotPolicy(param);
            logger.info("update auto pilot, deployment id {}, result {}", deploymentId, ret);
            if (ret) {
                ret = vvpTaskManager.updateAutoPilotMode(deploymentId, "AUTOPILOT_MODE_ACTIVE");
                logger.info("update auto pilot mode, deployment id {}, result {}", deploymentId, ret);
            }
        });
    }

    public void addEndpoint() {
        List<LogClusterConfigRegion> configs = clusterRegionConfigDao.listAllConfigsExcludeStatus(DELETED, Lists.newArrayList(1,2));
        configs.forEach(config -> {
            SlsSourceParam sourceParam = gson.fromJson(config.getInputSourceParam(), SlsSourceParam.class);
            sourceParam.setEndpoint("cn-zhangjiakou-2-intranet.log.aliyuncs.com");
            LogClusterConfigRegion newConfig = new LogClusterConfigRegion();
            newConfig.setRegionCid(config.getRegionCid());
            newConfig.setInputSourceParam(gson.toJson(sourceParam));
            clusterRegionConfigDao.updateConfig(newConfig);
        });

    }

    @Override
    public void updatePatternCount(long taskId) {
        List<LogQualityScore> scores = logQualityScoreDao.listAllRecords();
        scores.forEach(s -> {
            Map<Long, Integer> m = patternDao.getPatternCount(s.getStartTime(), s.getEndTime(), Collections.singletonList(s.getTaskId()));
            if (m.isEmpty() || !m.containsKey(s.getTaskId())) {
                logger.warn("no pattern for task {}, start time {}, end time {}", s.getTaskId(), s.getStartTime(), s.getEndTime());
            } else {
                LogQualityScore logQualityScore = new LogQualityScore();
                logQualityScore.setId(s.getId());
                logQualityScore.setPatternCount(m.get(s.getTaskId()));
                logQualityScoreDao.updateScoreRecord(logQualityScore);
                logger.info("update pattern count, task id {}, start time {}, end time {}, count {}", s.getTaskId(),
                    s.getStartTime(), s.getEndTime(), m.get(s.getTaskId()));
            }
        });
    }

    public void parseLogStore() {
        String host = "http://data.cn-hangzhou-idpt-inner-2.sls-pub.inter.idptcloud01-inner-private.com";
        String ak = "9AjHsCw0Fq4Zc5je";
        String sk = "PERGJYR0rlD4G7tUWJzVyebybnejOQ";
        long to = System.currentTimeMillis() / 1000;
        long from = to - 30 * 86400;
        Client client = new Client(host, ak, sk);
        List<String> monitorFile = Lists.newArrayList("CONTROLLER_EVENT.yaml","GOC_EVENT.yaml","HEALTH_STATUS_EVENT.yaml","VIRT_KEY_EVENT.yaml","CUSTOM_MONITOR_EVENT.yaml","GUESTOS_EVENT.yaml","HOUYI_SCHEDULER_EVENT.yaml","NC_SYSTEM_EVENT.yaml","VM_NC_FATAL_EVENT.yaml","ECI_EVENT.yaml","HARDWARE_EVENT.yaml","IDC_EVENT.yaml","SCHEDULE_FACTOR_CLOSE_EVENT.yaml","VM_PERF_DROP_EVENT.yaml");

        //List<String> monitorFile = Lists.newArrayList("CONTROLLER_EVENT.yaml");

        InputStream is = RpcTestImpl.class.getResourceAsStream("/sls/sls.yaml");
        Yaml yaml = new Yaml(new SafeConstructor());
        Map<String, Object> slsConfigMap = yaml.load(is);
        Map<String, String> config2project = new HashMap<>();
        slsConfigMap.forEach((k, v) -> {
            Object regionProjectMapObj = ((Map<String, Object>)v).get("regionProjectMap");
            Map<String, String> regionProjectMap = (Map<String, String>)regionProjectMapObj;
            String project = regionProjectMap.getOrDefault("default", "not_fount");
            config2project.put(k, project);
        });

        Map<String, String> monitor2logStore = new HashMap<>();
        Map<String, String> monitor2slsConfig = new HashMap<>();
        monitorFile.forEach(file -> {
            String fullPath = "/sls/monitor/" + file;
            InputStream fs = RpcTestImpl.class.getResourceAsStream(fullPath);
            Yaml yaml2 = new Yaml(new SafeConstructor());
            Map<String, Object> monitorConfig = yaml2.load(fs);
            monitorConfig.forEach((name, config) -> {
                Object logStore = ((Map<String, Object>)config).get("logstore");
                Object slsConfigName = ((Map<String, Object>)config).get("slsConfigName");
                monitor2logStore.put(name, (String)logStore);
                monitor2slsConfig.put(name, (String)slsConfigName);
            });
        });

        List<String> monitors = new LinkedList<>();
        monitor2logStore.forEach((monitor, logstore) -> {
            String slsConfigName = monitor2slsConfig.get(monitor);
            String project = config2project.get(slsConfigName);
            String exist = "EMPTY_PROJECT_OR_LOGSTORE";
            if (StringUtils.isNotBlank(logstore) && StringUtils.isNotBlank(project)) {
                exist = isLogExist(client, project, logstore, (int)from, (int)to);
            }
            monitors.add(String.format("%s,%s,%s,%s", monitor, logstore, project, exist));
        });
        System.out.println(monitors.size());

        FileUtil.writeToFile("/tmp/monitor_logstore_project.txt", monitors, false);
    }

    private String isLogExist(Client client, String project, String logStore, int from, int to) {
        String sql = "* | select __time__ as t limit 1";
        try {
            GetLogsResponse response = client.GetLogs(project, logStore, from, to, "", sql);
            Boolean ret = !response.GetLogs().isEmpty();
            return ret.toString();
        } catch (LogException e) {
            return e.GetErrorCode();
        }
    }
}
