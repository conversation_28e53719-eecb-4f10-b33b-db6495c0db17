package com.aliyun.xdragon;

import com.aliyun.xdragon.api.service.CheckHealth;
import com.aliyun.xdragon.biz.log.repository.ClusterTaskDao;
import com.aliyun.xdragon.biz.log.repository.PatternDao;
import com.aliyun.xdragon.service.common.config.diamond.DiamondConfigManager;
import com.aliyun.xdragon.service.common.util.FunctionSwitch;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/13
 */
@Service
public class CheckHealthImpl implements CheckHealth {
    private static final Logger healthLogger = LoggerFactory.getLogger("healthLogger");
    private static final Logger logger = LoggerFactory.getLogger(CheckHealthImpl.class);

    private static final Gson gson = new Gson();

    @Value("${project.name}")
    private String app;

    @Autowired
    private ClusterTaskDao clusterTaskDao;

    @Autowired
    private PatternDao patternDao;

    @Autowired
    private DiamondConfigManager diamondConfigManager;

    @Autowired
    private FunctionSwitch functionSwitch;


    @Scheduled(cron = "0 * * * * *")
    public Map<String, Object> checkAppHealth() {
        Map<String, Object> m = new HashMap<>();
        m.put("timestamp", System.currentTimeMillis() / 1000);
        m.put("app", app);
        boolean isOk = true;
        String message = "app is ok";
        try {
            clusterTaskDao.listTask("log", null, false, 0, 1);
        } catch (Exception e) {
            logger.error("check db health fail", e);
            isOk = false;
            message = "rds error";
        }

        if (isOk && functionSwitch.isAdbEnabled()) {
            try {
                patternDao.getRegionById(9L);
            } catch (Exception e) {
                logger.error("check adb health fail", e);
                isOk = false;
                message = "adb error";
            }
        }

        if (isOk) {
            if (!diamondConfigManager.checkConfigServer()) {
                logger.error("check diamond health fail");
                isOk = false;
                message = "diamond error";
            }
        }

        if (isOk) {
            m.put("status", "ok");
        } else {
            m.put("status", "fail");
        }
        m.put("message", message);

        healthLogger.info(gson.toJson(m));
        return m;
    }

    public String baseCheck() {
        return String.format("{\"timestamp\":%d, \"app\":%s,\"status\":\"ok\"}", System.currentTimeMillis() / 1000, app);
    }
}
