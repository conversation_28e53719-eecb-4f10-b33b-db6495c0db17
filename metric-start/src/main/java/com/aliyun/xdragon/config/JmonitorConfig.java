package com.aliyun.xdragon.config;

import javax.annotation.PostConstruct;

import com.alibaba.alimonitor.jmonitor.common.JmonitorBootstrap;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/02/10
 */
@Configuration
public class JmonitorConfig {

    @PostConstruct
    public void start() {
        JmonitorBootstrap bootstrap = JmonitorBootstrap.getInstance();
        bootstrap.start();
    }
}
