package com.aliyun.xdragon.vvp.udx.log.udtf;

import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.JobID;
import org.apache.flink.api.common.accumulators.Accumulator;
import org.apache.flink.api.common.accumulators.DoubleCounter;
import org.apache.flink.api.common.accumulators.Histogram;
import org.apache.flink.api.common.accumulators.IntCounter;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.cache.DistributedCache;
import org.apache.flink.api.common.externalresource.ExternalResourceInfo;
import org.apache.flink.api.common.functions.BroadcastVariableInitializer;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.api.common.state.AggregatingState;
import org.apache.flink.api.common.state.AggregatingStateDescriptor;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ReducingState;
import org.apache.flink.api.common.state.ReducingStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.metrics.groups.OperatorMetricGroup;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.util.Collector;
import org.junit.Assert;
import org.junit.Test;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/06/07
 */
public class ExtensionFlatMapUdtfTest {
    private ExtensionFlatMapUdtf udtf = new ExtensionFlatMapUdtf();

    class DummyContext implements RuntimeContext {
        @Override
        public JobID getJobId() {
            return null;
        }

        @Override
        public String getTaskName() {
            return null;
        }

        @Override
        public OperatorMetricGroup getMetricGroup() {
            return null;
        }

        @Override
        public int getNumberOfParallelSubtasks() {
            return 0;
        }

        @Override
        public int getMaxNumberOfParallelSubtasks() {
            return 0;
        }

        @Override
        public int getIndexOfThisSubtask() {
            return 0;
        }

        @Override
        public int getAttemptNumber() {
            return 0;
        }

        @Override
        public String getTaskNameWithSubtasks() {
            return null;
        }

        @Override
        public ExecutionConfig getExecutionConfig() {
            return new ExecutionConfig();
        }

        @Override
        public ClassLoader getUserCodeClassLoader() {
            return null;
        }

        @Override
        public void registerUserCodeClassLoaderReleaseHookIfAbsent(String s, Runnable runnable) {

        }

        @Override
        public <V, A extends Serializable> void addAccumulator(String s, Accumulator<V, A> accumulator) {

        }

        @Override
        public <V, A extends Serializable> Accumulator<V, A> getAccumulator(String s) {
            return null;
        }

        @Override
        public IntCounter getIntCounter(String s) {
            return null;
        }

        @Override
        public LongCounter getLongCounter(String s) {
            return null;
        }

        @Override
        public DoubleCounter getDoubleCounter(String s) {
            return null;
        }

        @Override
        public Histogram getHistogram(String s) {
            return null;
        }

        @Override
        public Set<ExternalResourceInfo> getExternalResourceInfos(String s) {
            return null;
        }

        @Override
        public boolean hasBroadcastVariable(String s) {
            return false;
        }

        @Override
        public <RT> List<RT> getBroadcastVariable(String s) {
            return null;
        }

        @Override
        public <T, C> C getBroadcastVariableWithInitializer(String s,
            BroadcastVariableInitializer<T, C> broadcastVariableInitializer) {
            return null;
        }

        @Override
        public DistributedCache getDistributedCache() {
            return null;
        }

        @Override
        public <T> ValueState<T> getState(ValueStateDescriptor<T> valueStateDescriptor) {
            return null;
        }

        @Override
        public <T> ListState<T> getListState(ListStateDescriptor<T> listStateDescriptor) {
            return null;
        }

        @Override
        public <T> ReducingState<T> getReducingState(ReducingStateDescriptor<T> reducingStateDescriptor) {
            return null;
        }

        @Override
        public <IN, ACC, OUT> AggregatingState<IN, OUT> getAggregatingState(
            AggregatingStateDescriptor<IN, ACC, OUT> aggregatingStateDescriptor) {
            return null;
        }

        @Override
        public <UK, UV> MapState<UK, UV> getMapState(MapStateDescriptor<UK, UV> mapStateDescriptor) {
            return null;
        }
    }

    class DummyCollector<T> implements Collector {
        @Override
        public void collect(Object o) {}

        @Override
        public void close() {}
    }

    @Test
    public void testOpen() throws Exception {
        Assert.assertNotNull(udtf);
        udtf.open(new FunctionContext(new DummyContext()));
    }

    @Test
    public void testEval() {
        Assert.assertNotNull(udtf);
        udtf.setCollector(new DummyCollector<Tuple3<Long, String, String>>());
        String extStr = "******* dummy";
        udtf.eval(extStr);
    }
}
