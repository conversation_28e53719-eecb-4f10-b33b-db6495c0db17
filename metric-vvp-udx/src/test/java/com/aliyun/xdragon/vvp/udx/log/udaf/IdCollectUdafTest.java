package com.aliyun.xdragon.vvp.udx.log.udaf;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import com.aliyun.xdragon.common.util.CompressUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2023/07/25
 */
public class IdCollectUdafTest {
    private IdCollectUdaf udaf = new IdCollectUdaf();

    @Test
    public void testIdCollectUdaf() {
        Assert.assertNotNull(udaf);

        // Test createAccumulator
        Set<byte[]> accumulator = udaf.createAccumulator();
        Assert.assertNotNull(accumulator);
        Assert.assertTrue(accumulator.isEmpty());

        // Test accumulate
        udaf.accumulate(accumulator, "1234567890abcdeffedcba0123456789", "*******");
        udaf.accumulate(accumulator, "1234567890abcdeffedcba0123456789", "*******");
        udaf.accumulate(accumulator, "ffffffffffffffffffffffffffffffff", "*******");
        Assert.assertFalse(accumulator.isEmpty());
        Assert.assertEquals(2, accumulator.size());

        // Test merge
        Set<byte[]> other = udaf.createAccumulator();
        Assert.assertTrue(other.isEmpty());
        List<Set<byte[]>> accumulatorArray = new ArrayList<>();
        accumulatorArray.add(accumulator);
        udaf.merge(other, accumulatorArray);
        Assert.assertFalse(other.isEmpty());
        Assert.assertEquals(accumulator.size(), other.size());

        // Test getValue
        byte[] value = udaf.getValue(accumulator);
        Assert.assertNotNull(value);
        List<Pair<String, String>> pairs = CompressUtil.bytesToIpMd5List(value);
        Assert.assertEquals(2, pairs.size());
        Assert.assertEquals("*******", pairs.get(0).getLeft());
        Assert.assertEquals("1234567890abcdeffedcba0123456789", pairs.get(0).getRight());
        Assert.assertEquals("*******", pairs.get(1).getLeft());
        Assert.assertEquals("ffffffffffffffffffffffffffffffff", pairs.get(1).getRight());
    }
}
