package com.aliyun.xdragon.vvp.udx.log.udtf;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.junit.Ignore;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/02/20
 */
public class AlarmItemSplitUdtfTest {

    @Test
    @Ignore
    public void t() {
        Gson gson = new Gson();
//        String str = "[\"[cp-uf674v0gksh4wkn15zqw]\",\"[i-uf608g7svux9iivcizg3]\",\"[cp-uf6ge6fxxmkjvpa8jpgh]\",\"[cp-uf6hgs0qluwrghm515un]\",\"[i-bp154hvd78hlnv6krlwy]\",\"null\",\"[cp-uf674v0gksh4wkn15zqx]\",\"[i-bp1fuc9jlrfp7qkf08m9]\",\"[i-t4nb3g19ohzv314lioqh]\",\"[i-bp17hlj6685qy0zts7zy]\",\"[i-bp17hlj6685qy0zts7zy]\",\"[i-uf6fryfus9epsu5lfq32]\",\"[i-bp13dpr641wj9dqxzmnj]\",\"[i-bp154hvd78hlo13o3s0f]\",\"[i-wz9aptg6nez6wqec2fvz]\",\"[cp-uf69dcgpw9z0862fl1k8]\",\"[i-m5e2nuaub04pkw7ay1i8]\",\"[cp-uf60ctv020jo8jqoi78x]\",\"[i-bp15t26m1nlmyu5459fr]\",\"[i-2ze4439ww8n80ydgco12]\"]";
//        List<String> list = gson.fromJson(str, new TypeToken<ArrayList<String>>() {
//        }.getType());
//        System.out.println(list);
//
//        String str2 = "[\"item_1db3b320b9d64a28a0779e4bc791ad04\",\"item_67bb1327fff048c2ab80c23d1f615bd4\",\"item_a0092ff2316e453baf1d861a1ed1c910\",\"item_72b9d33a8c724a50a2120fa5919f6142\",\"item_ccb23a05d8264ed1979c597b3afe7d37\",\"item_89e303f1a1814c82a08cc150d7adf17c\",\"item_044df96a2f4e4610a2dfdf4e0aa2ff45\",\"item_cfae6f43c12247a2baad52bce562aa09\",\"item_1fc1003b4b614b909caf56dafc588028\",\"item_1bea92f81a7144efb9e7d44c0a3a4a8b\",\"item_a121855d0682429481ed9bd8d5817812\",\"item_5f318d4e87b744968460ca77904cb22a\",\"item_30b48a57c9644dd4b68373f6570ddbe4\",\"item_fc7bf1b179c2462e950e70f032bb8786\",\"item_deaf51c3c7b04b219f1cadb2c63d710a\",\"item_49ea418c088d461db1091f0eb3389dc0\",\"item_fcdc1fda323c43c8b9c511b890455686\",\"item_ef7c416fdeb44652ae21fcd944ad1507\",\"item_3ef752af95f041409e8d0b09031a8a5c\",\"item_fa7dc3c6f9ac4941a3df460d9fe70432\"]";
//        List<String> list2 = gson.fromJson(str2, new TypeToken<ArrayList<String>>() {
//        }.getType());
//        System.out.println(list2);
//
//        String str3 = "[i-wz97a0nc0pdremoju837,i-wz97a0nc0pdremoju838]";
//        List<String> list3 = gson.fromJson(str3, new TypeToken<ArrayList<String>>() {
//        }.getType());
//        System.out.println(list3);

        String str4="[\"[i-2zej55q9x74t9mcxleb7]\",\"[i-2ze2z8pmwri4nq3mkgit]\",\"[i-2ze5dwrv6vy4awukusvv]\",\"[i-2zefrulfxnl8gbstjhxc]\",\"[i-2ze672ze641k7orvwgm1]\",\"[i-8ps3je40gezucb78m36z]\",\"[i-8psde5mdwzq8xx0ndq06]\",\"[i-vr85ri61bgotv63eeu5h]\",\"[i-vr85ri61bgotv63eeu5g]\",\"[eci-2zeh5ir621soo38zrqgn]\",\"[i-rj9gdpbh6b8ead8tdqwu]\",\"[i-rj9aht2cuvwnriglu1m1]\",\"[i-t4nj72dsg57bwvxnvq8l]\"]";
        List<String> vms = gson.fromJson(str4, new TypeToken<ArrayList<String>>() {
        }.getType());
        for (int i = 0; i< vms.size(); i++) {
            List<String> names;
            if (vms.get(i).startsWith("[") && vms.get(i).endsWith("]")) {
                names = gson.fromJson(vms.get(i), new TypeToken<ArrayList<String>>() {
                }.getType());
            } else {
                names = Lists.newArrayList(vms.get(i));
            }
            System.out.println(names);
        }

        String s = "123";
        String[] segs = s.split("\\|");
        System.out.println(segs);
    }
}
