package com.aliyun.xdragon.vvp.udx.log.udaf;

import com.aliyun.xdragon.algorithm.log.config.ClusterConfigV2;
import com.aliyun.xdragon.algorithm.log.model.LogClusterV2;
import com.aliyun.xdragon.algorithm.log.model.entities.CandidateEntity;
import com.aliyun.xdragon.algorithm.log.model.entities.ClusterInfo;
import com.aliyun.xdragon.algorithm.log.model.entities.PretreatLogInfo;
import com.aliyun.xdragon.common.util.BKDRHash;
import com.google.gson.Gson;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.DigestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/09
 */
public class LogClusterUdaf extends AggregateFunction<List<String>, LogClusterV2> {
    private static final Logger logger = LoggerFactory.getLogger(LogClusterUdaf.class);
    private static final Gson GSON = new Gson();

    private int scale;
    private int support;
    private int ratioSupport;
    private double weightThreshold;

    @Override
    public void open(FunctionContext context) {
        scale = Integer.parseInt(context.getJobParameter("cluster.scale", "50000"));
        support = Integer.parseInt(context.getJobParameter("cluster.support", "5"));
        ratioSupport = Integer.parseInt(context.getJobParameter("cluster.rsupport", "3"));
        weightThreshold = Double.parseDouble(context.getJobParameter("cluster.weightThreshold", "0.04"));
    }

    @Override
    public LogClusterV2 createAccumulator() {
        ClusterConfigV2 config = new ClusterConfigV2(scale, ratioSupport, support, weightThreshold, "REGEX");
        return new LogClusterV2(config);
    }

    @Override
    public List<String> getValue(LogClusterV2 accumulator) {
        logger.info("[LogClusterUdaf] Go into getValue logic at {}", System.currentTimeMillis());
        Map<String, CandidateEntity> clusterDict = accumulator.cluster();
        return getClusterInfoStrList(clusterDict);
    }

    public void accumulate(LogClusterV2 accumulator, String tokens, String extInfo, String md5, Integer count) {
        if (tokens != null && !tokens.isEmpty()) {
            PretreatLogInfo log = new PretreatLogInfo(tokens, extInfo, md5, "");
            accumulator.collect(log, count);
        }
    }

    public void merge(LogClusterV2 accumulator, Iterable<LogClusterV2> its) {
        int cnt = 0;
        for (LogClusterV2 logCluster : its) {
            accumulator.merge(logCluster);
            ++cnt;
        }
        logger.info("[LogClusterUdaf] Merge current accumulator with {} more accumulators.", cnt);
    }

    private List<String> getClusterInfoStrList(Map<String, CandidateEntity> clusterDict) {
        if (clusterDict == null || clusterDict.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> clusterInfoStrList = new ArrayList<>(clusterDict.size());
        for (Map.Entry<String, CandidateEntity> entry : clusterDict.entrySet()) {
            ClusterInfo clusterInfo = new ClusterInfo();
            CandidateEntity cluster = entry.getValue();
            clusterInfo.setKey(BKDRHash.hash(cluster.getTokens(), ' '));
            clusterInfo.setSupport(cluster.getSupport());
            clusterInfo.setPattern(String.join(" ", cluster.generatePattern()));
            clusterInfo.setMd5(DigestUtils.md5DigestAsHex(clusterInfo.getPattern().getBytes()));
            clusterInfo.setExt(cluster.getExtensionInfoStr());
            clusterInfo.setNcCnt(safeGetNcInfoFromCandidateEntity(cluster));
            clusterInfo.setRawMd5List(String.join(",", cluster.getMd5Set()));
            clusterInfoStrList.add(GSON.toJson(clusterInfo));
        }
        return clusterInfoStrList;
    }

    private int safeGetNcInfoFromCandidateEntity(CandidateEntity cluster) {
        if (cluster == null || cluster.getExtensionInfo() == null || cluster.getExtensionInfo().isEmpty()) {
            return 0;
        }
        return cluster.getExtensionInfo().get(0).size();
    }
}
