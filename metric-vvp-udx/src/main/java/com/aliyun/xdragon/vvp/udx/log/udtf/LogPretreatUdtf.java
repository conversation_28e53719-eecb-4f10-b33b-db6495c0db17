package com.aliyun.xdragon.vvp.udx.log.udtf;

import com.aliyun.xdragon.algorithm.log.model.entities.LogInfo;
import com.aliyun.xdragon.algorithm.log.model.utils.RegexWithLimitLogSplitter;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.DigestUtils;

import static com.aliyun.xdragon.algorithm.log.util.LogClusterUtil.splitIndicesStr;

/**
 * <AUTHOR>
 * @date 2022/11/08
 */
public class LogPretreatUdtf extends TableFunction<Tuple2<LogInfo, String>> {
    private static Logger logger = LoggerFactory.getLogger(LogPretreatUdtf.class);

    private RegexWithLimitLogSplitter splitter;

    private int cutLines;
    private int cutLength;
    private String maskPrefix;
    private String maskSuffix;
    private String maskStr;
    private String indicesStr;

    @Override
    public void open(FunctionContext context) {
        cutLines = Integer.parseInt(context.getJobParameter("cluster.cutLines", "5"));
        cutLength = Integer.parseInt(context.getJobParameter("cluster.cutLength", "30"));
        maskPrefix = context.getJobParameter("cluster.maskPrefix", "");
        maskSuffix = context.getJobParameter("cluster.maskSuffix", "");
        indicesStr = context.getJobParameter("cluster.indicesStr", "1");
        maskStr = context.getJobParameter("cluster.maskStr", " REGEX ");
        splitter = new RegexWithLimitLogSplitter(cutLines, cutLength, RegexWithLimitLogSplitter.DELIMITERS,
                maskPrefix, maskSuffix, splitIndicesStr(indicesStr));
    }

    public void eval(String raw, Long id, String slsSource, String nc, String exts) {
        String aggExts = String.join("|", exts, slsSource);
        LogInfo logInfo = new LogInfo(raw, id, nc, aggExts, splitter::split);
        String logInfoMd5 = DigestUtils.md5DigestAsHex(logInfo.getReadableTokens().getBytes());
        collect(Tuple2.of(logInfo, logInfoMd5));
    }
}
