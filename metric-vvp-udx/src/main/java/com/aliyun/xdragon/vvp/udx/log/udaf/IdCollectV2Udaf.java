package com.aliyun.xdragon.vvp.udx.log.udaf;

import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/07/21
 */
public class IdCollectV2Udaf extends AggregateFunction<String, List<Long>> {
    private int maxSize;

    @Override
    public void open(FunctionContext context) {
        maxSize = Integer.parseInt(context.getJobParameter("storage_id_cnt", "1000"));
    }

    @Override
    public String getValue(List<Long> accumulator) {
        return accumulator.stream().map(String::valueOf).collect(Collectors.joining(","));
    }

    @Override
    public List<Long> createAccumulator() {
        return new ArrayList<>(maxSize);
    }

    public void accumulate(List<Long> accumulator, Long id) {
        if (accumulator.size() >= maxSize) {
            return;
        }
        accumulator.add(id);
    }

    public void merge(List<Long> accumulator, Iterable<List<Long>> its) {
        for (List<Long> acc : its) {
            if (accumulator.size() >= maxSize) {
                break;
            }
            int maxCnt = maxSize - accumulator.size();
            accumulator.addAll(acc.subList(0, Math.min(acc.size(), maxCnt)));
        }
    }
}
