package com.aliyun.xdragon.vvp.udx.log.udaf;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.aliyun.xdragon.algorithm.log.config.ClusterConfigV2;
import com.aliyun.xdragon.algorithm.log.model.LogClusterNGram;
import com.aliyun.xdragon.algorithm.log.model.entities.CandidateEntity;
import com.aliyun.xdragon.algorithm.log.model.entities.ClusterInfo;
import com.aliyun.xdragon.algorithm.log.model.entities.PretreatLogInfo;
import com.aliyun.xdragon.common.util.BKDRHash;
import com.google.gson.Gson;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.DigestUtils;

/**
 * <AUTHOR>
 * @date 2023/10/20
 */
public class LogCLusterNGramUdaf extends AggregateFunction<List<String>, LogClusterNGram> {
    private static final Logger logger = LoggerFactory.getLogger(LogCLusterNGramUdaf.class);
    private static final Gson GSON = new Gson();

    private double weightThreshold;

    @Override
    public void open(FunctionContext context) {
        weightThreshold = Double.parseDouble(context.getJobParameter("cluster.weightThreshold", "0.04"));
    }

    @Override
    public LogClusterNGram createAccumulator() {
        ClusterConfigV2 config = new ClusterConfigV2(0, 0, 0, weightThreshold, "REGEX");
        return new LogClusterNGram(config);
    }

    @Override
    public List<String> getValue(LogClusterNGram accumulator) {
        logger.info("Go into getValue logic at {}", System.currentTimeMillis());
        Map<String, CandidateEntity> clusterDict = accumulator.cluster();
        return getClusterInfoStrList(clusterDict);
    }

    public void accumulate(LogClusterNGram accumulator, String tokens, String extInfo, String md5, String rawMd5String,
        Integer count) {
        if (tokens != null && !tokens.isEmpty()) {
            PretreatLogInfo log = new PretreatLogInfo(tokens, extInfo, md5, rawMd5String);
            accumulator.collect(log, count);
        }
    }

    public void merge(LogClusterNGram accumulator, Iterable<LogClusterNGram> its) {
        int cnt = 0;
        for (LogClusterNGram logCluster : its) {
            accumulator.merge(logCluster);
            ++cnt;
        }
        logger.info("Merge current accumulator with {} more accumulators.", cnt);
    }

    private List<String> getClusterInfoStrList(Map<String, CandidateEntity> clusterDict) {
        if (clusterDict == null || clusterDict.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> clusterInfoStrList = new ArrayList<>(clusterDict.size());
        for (Map.Entry<String, CandidateEntity> entry : clusterDict.entrySet()) {
            ClusterInfo clusterInfo = new ClusterInfo();
            CandidateEntity cluster = entry.getValue();
            clusterInfo.setKey(BKDRHash.hash(cluster.getTokens(), ' '));
            clusterInfo.setSupport(cluster.getSupport());
            clusterInfo.setPattern(cluster.getPattern());
            clusterInfo.setMd5(DigestUtils.md5DigestAsHex(clusterInfo.getPattern().getBytes()));
            clusterInfo.setExt(cluster.getExtensionInfoStr());
            clusterInfo.setNcCnt(safeGetNcInfoFromCandidateEntity(cluster));
            clusterInfo.setRawMd5List(String.join(",", cluster.getMd5Set()));
            clusterInfoStrList.add(GSON.toJson(clusterInfo));
        }
        return clusterInfoStrList;
    }

    private int safeGetNcInfoFromCandidateEntity(CandidateEntity cluster) {
        if (cluster == null || cluster.getExtensionInfo() == null || cluster.getExtensionInfo().isEmpty()) {
            return 0;
        }
        return cluster.getExtensionInfo().get(0).size();
    }
}
