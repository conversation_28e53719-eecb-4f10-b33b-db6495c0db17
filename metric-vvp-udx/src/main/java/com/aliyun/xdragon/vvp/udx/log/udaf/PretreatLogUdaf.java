package com.aliyun.xdragon.vvp.udx.log.udaf;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.gson.Gson;
import org.apache.flink.table.functions.AggregateFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2022/11/09
 */
public class PretreatLogUdaf extends AggregateFunction<String, List<Map<String, Integer>>> {
    private static final Logger logger = LoggerFactory.getLogger(PretreatLogUdaf.class);
    private static final Gson GSON = new Gson();

    private static final int MAX_EXT_NUM = 10;

    @Override
    public List<Map<String, Integer>> createAccumulator() {
        List<Map<String, Integer>> accumulator = new ArrayList<Map<String, Integer>>(MAX_EXT_NUM);
        for (int i = 0; i < MAX_EXT_NUM; ++i) {
            accumulator.add(new HashMap<>());
        }
        return accumulator;
    }

    @Override
    public String getValue(List<Map<String, Integer>> accumulator) {
        List<Map<String, Integer>> validList = new ArrayList<>(MAX_EXT_NUM);
        for (Map<String, Integer> map : accumulator) {
            if (map == null || map.isEmpty()) {
                break;
            }
            validList.add(map);
        }
        return GSON.toJson(validList);
    }

    public void accumulate(List<Map<String, Integer>> accumulator, String extInfo) {
        String[] extArray = extInfo.split(" ");
        for (int i = 0; i < Math.min(extArray.length, MAX_EXT_NUM); ++i) {
            Map<String, Integer> curMap = accumulator.get(i);
            Integer count = curMap.getOrDefault(extArray[i], 0);
            curMap.put(extArray[i], ++count);
        }
    }

    public void merge(List<Map<String, Integer>> accumulator, Iterable<List<Map<String, Integer>>> its) {
        for (List<Map<String, Integer>> extList : its) {
            int index = 0;
            for (Map<String, Integer> extInfo : extList) {
                Map<String, Integer> extInfoInAcc = accumulator.get(index);
                if (extInfo == null) {
                    break;
                }
                for (Map.Entry<String, Integer> entry : extInfo.entrySet()) {
                    Integer count = entry.getValue() + extInfoInAcc.getOrDefault(entry.getKey(), 0);
                    extInfoInAcc.put(entry.getKey(), count);
                }
                ++index;
            }
        }
    }
}
