package com.aliyun.xdragon.vvp.udx.log.udaf;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.math3.stat.StatUtils;
import org.apache.flink.table.functions.AggregateFunction;


/**
 * <AUTHOR>
 * @date 2023/05/29
 */
public class MedianUdaf extends AggregateFunction<Double, List<Double>> {
    @Override
    public Double getValue(List<Double> acSum) {
        if (acSum.isEmpty()) {
            return null;
        }
        return StatUtils.percentile(acSum.stream().mapToDouble(Double::doubleValue).toArray(), 50);
    }

    @Override
    public List<Double> createAccumulator() {
        return new ArrayList<>();
    }

    public void accumulate(List<Double> acc, Double num) {
        if (num != null) {acc.add(num);}
    }

    /**
     * Support local-global two stage aggregate optimization.
     */
    public void merge(List<Double> acc, Iterable<List<Double>> it) {
        for (List<Double> accSum : it) {
            if (null != accSum) {
                acc.addAll(accSum);
            }
        }
    }
}
