package com.aliyun.xdragon.vvp.udx.log.udtf;

import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2023/06/07
 *
 * Udtf for info collection only
 */
public class InfoCollectionUdtf extends TableFunction<Tuple3<Long, String, String>> {
    private static final Logger logger = LoggerFactory.getLogger(InfoCollectionUdtf.class);

    private long taskId;
    private String region;
    private String logStore;

    @Override
    public void open(FunctionContext context) {
        taskId = Long.parseLong(context.getJobParameter("task_id", "-1"));
        region = context.getJobParameter("input.region", "unknown");
        logStore = context.getJobParameter("sls.input.logStore", "unknown");
    }

    public void eval() {
        collect(Tuple3.of(taskId, region, logStore));
    }
}
