package com.aliyun.xdragon.odps.udx.period.udtf;

import java.util.List;

import com.aliyun.odps.udf.local.runner.BaseRunner;
import com.aliyun.odps.udf.local.runner.UDTFRunner;
import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2024/12/10
 */
public class FeatureGroupExtractTest {

    @Test
    public void test1() throws Exception {
        BaseRunner runner = new UDTFRunner(null, "com.aliyun.xdragon.odps.udx.period.udtf.FeatureGroupExtract");
        runner.feed(new Object[] {"test_rule", "[\"a\",\"c\",\"b\",\"e\",\"d\"]", 1L});
        List<Object[]> out = runner.yield();
        Assert.assertEquals(5, out.size());
    }

    @Test
    public void test2() throws Exception {
        BaseRunner runner = new UDTFRunner(null, "com.aliyun.xdragon.odps.udx.period.udtf.FeatureGroupExtract");
        runner.feed(new Object[] {"test_rule", "[\"a\",\"c\",\"b\",\"e\",\"d\"]", 2L});
        List<Object[]> out = runner.yield();
        Assert.assertEquals(15, out.size());
    }

    @Test
    public void test3() throws Exception {
        BaseRunner runner = new UDTFRunner(null, "com.aliyun.xdragon.odps.udx.period.udtf.FeatureGroupExtract");
        runner.feed(new Object[] {"test_rule", "[\"a\",\"c\",\"b\",\"e\",\"d\"]", 3L});
        List<Object[]> out = runner.yield();
        Assert.assertEquals(25, out.size());
    }

    @Test
    public void test4() throws Exception {
        BaseRunner runner = new UDTFRunner(null, "com.aliyun.xdragon.odps.udx.period.udtf.FeatureGroupExtract");
        runner.feed(new Object[] {"test_rule", "[\"a\",\"c\",\"b\",\"e\",\"d\"]", 10L});
        List<Object[]> out = runner.yield();
        Assert.assertEquals(31, out.size());
    }

}