package com.aliyun.xdragon.odps.udx.period.udtf;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.aliyun.odps.udf.UDFException;
import com.aliyun.odps.udf.UDTF;
import com.aliyun.odps.udf.annotation.Resolve;
import com.google.gson.Gson;

/**
 * <AUTHOR>
 * @date 2024/12/10
 */
@Resolve({"string,string,bigint->string,string,bigint"})
public class FeatureGroupExtract extends UDTF {
    @Override
    public void process(Object[] args) throws UDFException {
        String ruleName = (String)args[0];
        String featureString = (String)args[1];
        long maxSize = (long)args[2];
        Gson gson = new Gson();
        List<String> list = gson.fromJson(featureString, List.class);
        Collections.sort(list);
        if (!list.isEmpty()) {
            backtrack(new ArrayList<>(), list, 0, maxSize, ruleName);
        }
    }

    private void backtrack(List<String> currentSubset, List<String> set, int id, long maxSize, String ruleName)
        throws UDFException {
        //尝试放入该元素
        currentSubset.add(set.get(id));
        String features = String.join(",", currentSubset);
        forward(ruleName, features, (long)currentSubset.size());
        if (currentSubset.size() < maxSize && id + 1 < set.size()) {
            backtrack(currentSubset, set, id + 1, maxSize, ruleName);
        }
        //尝试不放入该元素
        currentSubset.remove(currentSubset.size() - 1);
        if (currentSubset.size() < maxSize && id + 1 < set.size()) {
            backtrack(currentSubset, set, id + 1, maxSize, ruleName);
        }
    }

}
