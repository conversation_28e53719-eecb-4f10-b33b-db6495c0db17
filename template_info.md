# 源码自动生成模板 pandora-boot-archetype-docker

### 概述

* 模板: pandora-boot-archetype-docker
* 模板答疑人: [子观](https://work.alibaba-inc.com/nwpipe/u/64988)
* 模板使用时间: 2021-11-30 17:50:35

### Docker
* Image: reg.docker.alibaba-inc.com/bootstrap/image
* Tag: 0.1
* SHA256: e4b70f4f7d0b60aa3e5666eba441a376b31ec6e0bd550a4efc5af8f057c6d7d8

### 用户输入参数
* repoUrl: "**************************:cloud-ecs-devops/xdragon-metric.git" 
* appName: "xdragon-metric" 
* javaVersion: "1.8" 
* groupId: "com.aliyun.xdragon" 
* artifactId: "metric" 
* style: "hsf,diamond,tair,sentinel,autoconfig" 
* operator: "24194" 

### 上下文参数
* appName: xdragon-metric
* operator: 24194
* gitUrl: **************************:cloud-ecs-devops/xdragon-metric.git
* branch: master


### 命令行
	sudo docker run --rm -v `pwd`:/workspace -e repoUrl="**************************:cloud-ecs-devops/xdragon-metric.git" -e appName="xdragon-metric" -e javaVersion="1.8" -e groupId="com.aliyun.xdragon" -e artifactId="metric" -e style="hsf,diamond,tair,sentinel,autoconfig" -e operator="24194"  reg.docker.alibaba-inc.com/bootstrap/image:0.1

