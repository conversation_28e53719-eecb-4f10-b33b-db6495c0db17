package com.aliyun.xdragon.metric.spark.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/18
 */
public class FeatureSkipList implements Serializable {

    private static final long serialVersionUID = 1936514893814L;

    //正向特征
    private final List<String> featureList;
    //正向特征未命中时的跳转
    private final List<Integer> jumpList;

    private int currentIndex = 0;

    private Boolean match = null;

    public FeatureSkipList(List<String> features) {
        //TODO 当前是前缀型跳过，不是最优的。树状结构可以实现最优。但目前的实现性能可以接受，后续根据需求再调整

        featureList = features.stream().sorted().collect(Collectors.toList());
        jumpList = new ArrayList<>();
        for (int i = 0; i < featureList.size(); i++) {
            String prefix = featureList.get(i);
            int j = i + 1;
            while (j < featureList.size() && isPrefix(featureList.get(j), prefix)) {
                j++;
            }
            jumpList.add(j);
        }
    }

    private boolean isPrefix(String feature, String prefix) {
        return feature.startsWith(prefix) && feature.charAt(prefix.length()) == ',';
    }

    public void reset() {
        currentIndex = 0;
        match = null;
    }

    public String getCurrentFeature() {
        return featureList.get(currentIndex);
    }

    public void setMatch(Boolean match) {
        this.match = match;
    }

    public boolean next() {
        if (Boolean.TRUE.equals(match)) {
            currentIndex++;
        } else {
            currentIndex = jumpList.get(currentIndex);
        }
        match = null;
        return currentIndex < featureList.size();
    }

}
