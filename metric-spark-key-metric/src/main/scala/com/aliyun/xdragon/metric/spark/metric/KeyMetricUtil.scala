package com.aliyun.xdragon.metric.spark.metric

import com.aliyun.xdragon.metric.spark.config.MonitorConfig
import com.aliyun.xdragon.metric.spark.util.DateUtil

import java.sql.Timestamp
import scala.collection.mutable.ListBuffer
import scala.language.postfixOps
import scala.util.control.Breaks.{break, breakable}

/**
 * 定义一些key metric计算的通用函数
 *
 * <AUTHOR>
 */
class KeyMetricUtil(val monitorConfig: MonitorConfig) extends Serializable {

  val instanceSet: Set[String] = Set("i-j6c1y3hop28jczw27we3", "i-t4n0nhazeqyimnzyxn2v", "i-j6c0ccak9v1r5gngarel", "i-7xvcqb09dlm61xvpyxsh", "i-wz97ke0t3i3rxlkxaqm5", "i-2ze6kf0lsrq4aynjexb9", "i-2ze6ifbjhjyvqpps5zqa", "i-bp1b0swrdd0xmztm63zz", "i-uf689bb9liyetet1k9ru", "i-2ze3txre2ntumpjvajsc", "i-0jl7rhx0lex0ya5wnowe", "i-0jl7rhx0lex0xudnr91s", "i-0jl76srbmwqxvf9j5l4v", "i-bp16blb1bbwsdwe11adu", "i-0jl2915ygn63rjczeqii", "i-0jl11v0t9n69zd7qbbzk", "i-0jlj1bbn1zk50lg7jqzd", "i-j6cf6uwn59rr6v7cd6u4", "i-0jlj1bbn1zk50lg7jqzc", "i-wz94xxpptlp6s673gmvy", "i-wz9h2ay44dcocd8zgi4k", "i-rj93a465cyg0u2v6ehbg", "i-uf6bl95zcmcih0xzo8qi", "i-t4nj478o34c29s43tn52", "i-wz91abrwrhavpunsrzub", "i-t4n68k987zcausjfzhkz", "i-bp18cq091ncbfsvwjazt", "i-8vb0xrlg52m2bwv7uvy6", "i-j6cdsbgjsfirky97nu87", "i-bp14k5dvd9o1gtoeldir", "i-2ze9103odozrxt7zftfj", "i-wz91wztuvsiftcdtqc4f", "i-2zegf8231x53mp0jtgha", "i-0jlgij332ngorxfwhxsv", "i-wz92gauwq11tgpu1q9ll", "i-t4nfdezkzk53q6iz5kqq", "i-0jlg32by260q5r2qyt8e", "i-uf6c4nj55o8opnrobelf", "i-bp1idbsi5o2graa0tc16", "i-2vcdqgc12e4bzta44qiw", "i-uf63hj0qh9d040l6jdt0", "i-2zeaeombwj4b7983sq9f", "i-bp196nbc4ub4oartfeag", "i-8vb2656b672bfno5ylzk", "i-2ze3ryhgmpy3mmls8l17", "i-2ze186vgbfasrd94nfh7", "i-uf68cbsj30mgkgdilyhx", "i-8ps9znf93tx11m7szc4k", "i-t4nfdfmzcw1fbvgy2ezd", "i-j6c54ydzfe9j09nhnn2x", "i-bp1cchat3g4jg0tul00d", "i-gc71n55anh13i7nycwue", "i-2vcj7gsl19wnouhep8nd", "i-bp1et2qekjwu3ldsdj9n", "i-j6cghtxixjqbvcxllphs", "i-2ze6rmzq3gg3ca7f779n", "i-wz9ak44m4x9tddlogkpr", "i-uf6f696pmoko89912qpe", "i-j6cdwj4tvcrz54p9xk28", "i-wz9h17q8h9v0u9sokzmx", "i-bp12xohukzy3puavibzh", "i-f8z28a8ucyoj0iuqhoaz", "i-2vc76landdq42nmq8xvx", "i-8vbhar5ghexslelqg4a8", "i-0jl1kyi7iiuyc26gzbfc", "i-8pshjgwsyi7kp8sbsp1n", "i-8ps9znf93tx11m7szc4j", "i-wz90saqpbsavjp9hnac5", "i-8vb09drusrfbri1h8d6r", "i-2ze4u0ntw5ffiv6aoik8", "i-8vbi5f19cufm2cbcbcxi", "i-j6cc3hydkhan5ri17drr", "i-2vc711cu4c5lr2esuos2", "i-0jl1kyi7iiuxgpksjfd4", "i-0jlaln9eb6y65g5bc3f1", "i-bp1d2cbgi4jspctgkez6", "i-6we5xogfj3aphgctxnee", "i-8vb17rhl3gm3bohjweun", "i-wz9ag8659h7ihmsucc1x", "i-8ps6o9hpk0i12vd1lxjo", "i-wz99vgpeg03ces0vqdcx", "i-8ps6o9hpk0i0l62286tc", "i-j6cgac0h8vktsitwbbpb", "i-m5efd1wzm80xrv16jors", "i-8vb57xg2xi5vc9v83lhi", "i-wz9d4h7bg5di1zaoxs3v", "i-uf6g8eomwy97xwti63vf", "i-8psc3rcytmtd6a5edfl6", "i-bp1bq5g3dxxhktm8eesp", "i-wz984fp76m1393n15iom", "i-j6cdzphp060x4ha1i2l0", "eci-uf610bra8ilf56x4ndlf", "i-2vchiqrg8msk6mtggpax", "i-wz9779uwgmvbkopabg04", "eci-bp1ii1c5oc6urowh4cuf", "i-2ze7df4vd9lrt3bstb9t", "i-bp1572vvvjhzcynf2gug", "i-2ze5hxc6vp56h0ngwyqj", "i-8vbcmutfgeixysegcnn9", "i-uf6377m2iy8qoht91fxh", "i-2zei7ghriibticmxx3pq", "i-bp1asmmpjm5wy15ormo8", "i-hp3hmzsdtirbpkteyg0u", "i-8vbj5smxj3mw8cqmnten", "i-m0y3d7vti68pknv58s7e", "i-wz98qflsstbteproeet4", "i-wz976zg7ynyac5r35p9y", "eci-uf6e8xi56f15m86j5qpn", "i-uf67tb9ohgwgpdsddvm6", "i-2zedb1chetnag9a9mjgv", "i-k1a0varcbt4wvllxtigw", "i-uf63dj8i3kf8mdmr9nnq", "i-wz9hwjh31jv8cipeode8", "i-j6c45va0xalwji0gpcz6", "eci-uf6h746s4rweq09f8vm0", "i-2vc711cu4c5lr2esuos6", "i-wz93s5k0qqa6qmbsxdcy", "i-uf69fr8tccq1bmiz27xm", "i-uf6ccn2fev3ve40l569i", "i-bp1f65hlkq9lw93fdw4n", "i-uf61nabaaw5l8aqe3zdw", "i-bp188xbr3e15fhdpt15f", "i-wz9d7algyzs9116vyloi", "i-uf6imu1o8l4v9i2kbsan", "i-bp1hz4epqih2bwflhyg4", "i-hp3iadu8x6wvd8o8zwhv", "i-wz93u9zgy4e1af3p0unj", "i-bp168ybtzh8ee5kv1a86", "i-2zecsjx8pv7pwpcuqhk7", "i-uf6exlxvoqw2hzuv6ls6", "i-2ze8s9cjdf2aa4wsk943", "eci-bp1exfz44yprwjic7dt5", "i-hp3fyr7sv05m8e3vwlkk", "i-8vb2nlubkow0i0awcnc1", "i-wz9hgcn5aqif3xalkkdc", "i-2ze9tymm114eg97p7wfh", "eci-bp1dcnzq7odf9oja3bty", "i-rj9cw8fe4ocponnf0xvn", "i-2ze5b3slqruj5vzjylft", "i-j6c6xurs3q34l1ivhkay", "i-rj933q8nxhzjpqvp24ql", "i-8vb3muoqyttieg6hqoox", "i-8vb8e3qizh20w4j6ai5a", "eci-uf65qvbb6e3i5emeajk0", "i-7xvg86x22w4ijb6gfvj9", "i-bp17pgjo9trkefarty6b", "i-0jl3f6ea2x0z43k2kouc", "i-2ze86euwpbbbzvdtq9p5", "i-bp1b34hcl0g1whtli1id", "i-uf61xsur77kfbtsxqzmx", "i-wz95km8ocqkqur0azcau", "i-wz90u1syk2gy7235ts4e", "i-wz9h70iv04n7r9ku2rrm", "i-2zecs7uz11v3ovnxp9do", "i-2vc8tgfa7vu4xq1vqqcj", "i-bp13n3cedfv6adw1ixhj", "i-8vbehe9dpiwnlphzf6wv", "i-0jlj7jvby861r7k3vlix", "i-0jlcqjnf4x7v4yjgd1th", "i-bp17zqespk45rvp5fo5x", "i-uf6g3h4h0wrdpo1yl09d", "i-2zeggpphsoteiapcqksm", "i-2zeb30om78zv6qtom5wl", "i-bp12wrz2rgxatqkkh56c", "i-7xv45t5go8hu1s3qsm2f", "i-0jlixdtswe0u75qbwujx", "i-wz9fvubgtrl68eirdltx", "i-bp12mb54xt5jnodgi9y4", "i-2zehycnr2ydwuz9roqjd", "i-wz9es7bhhrskhhxhzi73", "i-8vb8kdtnxtjdsj42a0uv", "i-uf61xsur77kfbtsxqzmy", "i-2zedecj8nl679rsr839s", "i-2ze49w79e4zu3b9gq2s5", "i-bp17p9laal0pya5zaei8", "i-6we5xogfj3aphgctxnef", "i-uf61ti9rpcu5bpbza6c2", "i-2ze4aat96ck7ircfmvrd", "i-2ze11fv3gfx8wss1aq2w", "i-bp108qj1yfxgdlvvq7c1", "i-wz9f0xu750v8vp4f7zki", "i-2zeb4sgp2f2g2uvp2tbb", "i-uf65saybbvau1jvqeyzx", "i-8vbcmookvg1wrvelb0wl", "i-j6cgb8bmm0ddexg8ey1o", "i-uf63dj8i3kf8mdmr9nnp", "i-wz9gazojx195gzt52ymh", "i-7xvi1mwwaxjlqcs8yd16", "i-wz97fy4b6bcmnwdkjr80", "i-hp32k0nvs1qi2brqj744", "i-2zehpf7vuf4nxfr729w5", "i-7xvbahf4qry7pxg3n4az", "i-wz90n340dj1k2nf4iigi", "i-uf6fgt7bxps6ntemeneb", "i-hp3dxmxu0bzwcwrr6r9m", "i-bp1e159gxzh5jzlodk10", "i-m5edas42agjxodiat8pw", "i-f8z0b7b4qmlyj5hzrsmd", "i-7xv2d42k3dhcint7rm27", "i-j6cek7rpr0q16cfh97bv", "i-j6c4xjpss29tb6htxis8", "i-uf6dazrfkcp40aync19g", "eci-bp1baqhrp9cxcvyzdgyn", "i-uf6e82uz10rwn5yyeqp3", "i-uf6fdqadq9pchrwb0jb0", "i-bp11s0pdimb2kh2i3yjh", "i-2ze6imb1cs59wdldph7e", "i-bp1cz3fi8l1qkm4ltt1e")
  // 判断异常类型：unavailable / performance / control
  val classify: (Map[String, String]) => String = (params: Map[String, String]) => {
    monitorConfig.getMonitorType(params("exception_name"), params("warning_level"), params("reason"), params("additional_info"),
      params("extension"), params("warning_value"), params("biz_status"), params("is_win"), params("status"), params("instance_type_family"),
      params("vcpu_mod"), params("zone_group_flag"), params("is_test_account"), params("ali_uid")).getName
  }
  // 判断异常等级：L1 / L2 / L3 / L4
  val grade: (Map[String, String]) => Int = (params: Map[String, String]) => {
    monitorConfig.getMonitorLevel(params("exception_name"), params("warning_level"), params("reason"), params("additional_info"),
      params("extension"), params("warning_value"), params("biz_status"), params("is_win"), params("status"), params("instance_type_family"),
      params("vcpu_mod"), params("zone_group_flag"), params("is_test_account"), params("ali_uid"))
  }
  val rename: (Map[String, String]) => String = (params: Map[String, String]) => {
    monitorConfig.getNewName(params("exception_name"), params("warning_level"), params("reason"), params("additional_info"),
      params("extension"), params("warning_value"), params("biz_status"), params("is_win"), params("status"), params("instance_type_family"),
      params("vcpu_mod"), params("zone_group_flag"), params("is_test_account"), params("ali_uid"))
  }
  // 得到不可用异常的事件类型，开始事件｜结束事件｜点事件
  val getEventType: String => String = (monitor: String) => {
    monitorConfig.getEventType(monitor)
  }
  // 获取异常的受损时长
  val durationFunc: (String) => Int = (exceptionName: String) => {
    monitorConfig.getDuration(exceptionName)
  }
  // 对一些性能有损异常精细化异常时长
  val durationFineTuning: (String, Double) => Double = (exceptionName: String, duration: Double) => {
    if (exceptionName.equals("live_migrate_on_dst_nc_finish")) {
      duration / 10.0
    } else {
      duration * 60.0
    }
  }
  // 点事件 => (开始事件，结束事件)
  val unavailableMonitorRefactor: (String, String, String) => List[(String, String)] = (exceptionName: String, exceptionTime: String, warningValue: String) => {
    val result = ListBuffer[(String, String)]()
    if (exceptionAndThreshold.contains(exceptionName) || monitorConfig.isPointEvent(exceptionName)) {
      result.append((exceptionName + "_start", exceptionTime))
      var endExceptionTime = ""
      if (monitorConfig.isPointEvent(exceptionName)) {
        val duration: Int = monitorConfig.getDuration(exceptionName) / 1000
        endExceptionTime = DateUtil.modifyDate(dateStr = exceptionTime, seconds = duration)
      } else { // exceptionAndThreshold
        endExceptionTime = DateUtil.modifyDate(dateStr = exceptionTime, seconds = warningValue.toDouble.toInt / exceptionAndThreshold(exceptionName))
      }
      result.append((exceptionName + "_end", endExceptionTime))
    } else {
      result.append((exceptionName, exceptionTime))
    }
    result.toList
  }
  private val exceptionAndThreshold: Map[String, Int] = Map("vm_pasued_resume_check_ntp" -> 1, "xdragon_hot_upgrade_fpga_down_event" -> 1000)

  /**
   * 根据给定的时间范围计算数据面不可用时长
   *
   * @param startTime        计算异常的开始时间
   * @param endTime          计算异常的截止时间
   * @param rawExceptionList 原始异常数据
   * @return
   */
  def computeUnavailableTimeWithRange(startTime: String, endTime: String, rawExceptionList: List[(String, String)]): Double = {
    val result = listUnavailableEventWithoutCoverage(startTime = startTime, endTime = endTime, rawRecords = rawExceptionList)
    result.map(_._4).sum
  }

  /**
   * 统计不可用事件，会对时间上互相覆盖的区域进行去重，当前的去重的逻辑是哪个不可用事件先开始，哪个占用互相覆盖的时间区域
   * 一个不可用事件一条数据
   *
   * @param startTime  时间窗口的开始时间
   * @param endTime    时间窗口的结束时间
   * @param rawRecords 原始数据，一般包含时间窗口的前一天数据和后一天的数据，用来保证数据的准确性
   * @return (异常名称，开始时间，结束时间，持续时长) 的集合
   */
  def listUnavailableEventWithoutCoverage(startTime: String, endTime: String, rawRecords: List[(String, String)]): ListBuffer[(String, String, String, Double)] = {
    // 添加数据集大小检查，避免处理超大数据集
    if (rawRecords.size > 50000) {
      println(s"Warning: Large dataset detected (${rawRecords.size} records). Consider processing in batches.")
      return listUnavailableEventInBatches(startTime, endTime, rawRecords, 10000)
    }
    
    val sortedRecords = sortAndFilter(rawRecords)
    val startEventList = findUnclosedStartEvent(sortedRecords, startTime) // 找到窗口之前未闭合的开始事件
    val records = sortedRecords.filter(_._2 >= startTime)
    val exceptionNameList = records.map(_._1)
    var lastExceptionTime = startTime
    var lastExceptionName = ""
    var shouldBackUp = false
    val result = ListBuffer[(String, String, String, Double)]()
    breakable {
      for (i <- records.indices) {
        val record = records(i)
        val exceptionName = record._1
        val exceptionTime = record._2
        if (exceptionTime > endTime) {
          //          shouldBackUp = monitorConfig.isEndEvent(exceptionName) && !monitorConfig.needCorresponding(exceptionName)  && !exceptionName.equals(lastExceptionName) && !(startEventList.isEmpty && exceptionName.equals("vm_vsock_icmp_ping_loss_recovery"))
          //          lastExceptionName = exceptionName
          break()
        }
        if (!monitorConfig.needCorresponding(exceptionName) || unavailableSpecialLogic(exceptionName, i, exceptionNameList, startEventList)) {
          if (monitorConfig.isStartEvent(exceptionName)) {
            if (!startEventList.contains(exceptionName)) {
              if (startEventList.isEmpty) {
                lastExceptionTime = exceptionTime
              }
              startEventList.append(exceptionName)
            }
          } else {
            var find = false

            val correspondingEventList = monitorConfig.correspondingEventsMap.get(exceptionName)
            if (correspondingEventList == null) {
              println("correspondingEventList is null. exceptionName: ", exceptionName)
            }
            else {
              for (i <- 0 until correspondingEventList.size()) {
                val startEvent = correspondingEventList.get(i)
                if (startEventList.contains(startEvent)) {
                  if (!find) {
                    val index = startEventList.indexOf(startEvent)
                    if (index == 0) {
                      val duration = DateUtil.datetime2Timestamp(exceptionTime) - DateUtil.datetime2Timestamp(lastExceptionTime)
                      result.append((startEvent, lastExceptionTime, exceptionTime, duration))
                      find = true
                    }
                  }
                  startEventList -= startEvent
                }
              }
              if (find) {
                lastExceptionTime = exceptionTime
              }
            }
            lastExceptionName = exceptionName
          }
        }
      }
    }
    if (startEventList.nonEmpty) {
      val nextTimestamp = DateUtil.datetime2Timestamp(endTime)
      val lastTimestamp = DateUtil.datetime2Timestamp(lastExceptionTime)
      val startEvent = startEventList.head
      result.append((startEvent, lastExceptionTime, endTime, (nextTimestamp - lastTimestamp)))
    }
    result
  }

  // 分批处理超大数据集的不可用事件分析
  private def listUnavailableEventInBatches(startTime: String, endTime: String, rawRecords: List[(String, String)], batchSize: Int): ListBuffer[(String, String, String, Double)] = {
    val sortedRecords = sortAndFilter(rawRecords)
    val batches = sortedRecords.grouped(batchSize).toList
    val result = ListBuffer[(String, String, String, Double)]()
    
    var lastBatchEndTime = startTime
    for (batch <- batches) {
      val batchResult = listUnavailableEventWithoutCoverage(lastBatchEndTime, endTime, batch)
      result ++= batchResult
      if (batchResult.nonEmpty) {
        lastBatchEndTime = batchResult.last._3 // 更新下一批次的开始时间
      }
    }
    result
  }

  /**
   * 找到在endTime之前未闭合的开始事件，返回的是有序的集合
   *
   * @param records 有序的事件集合
   * @param endTime 时间阈值
   * @return 在endTime之前未闭合的开始事件的集合
   */
  def findUnclosedStartEvent(records: List[(String, String)], endTime: String): scala.collection.mutable.ListBuffer[String] = {
    val startEventList = scala.collection.mutable.ListBuffer[String]()
    breakable {
      for (record <- records) {
        if (record._2 > endTime) {
          break()
        }
        val exceptionName = record._1
        if (monitorConfig.isStartEvent(exceptionName) && !startEventList.contains(exceptionName)) {
          startEventList.append(exceptionName)
        } else if (monitorConfig.isEndEvent(exceptionName)) {
          val correspondingEventList = monitorConfig.correspondingEventsMap.get(exceptionName)
          // 如果是结束事件，则把对应的多个开始事件全部结束
          for (i <- 0 until correspondingEventList.size()) {
            val startEvent = correspondingEventList.get(i)
            if (startEventList.contains(startEvent)) {
              startEventList -= startEvent
            }
          }
        }
      }
    }
    startEventList
  }

  /**
   * 用来处理计算不可用时长的一些特殊逻辑，比如一些备选的结束事件的处理
   *
   * @param exceptionName  备选的异常
   * @param location       备选异常的下标
   * @param exceptionList  某个实例所有的异常（有序）
   * @param startEventList 开始事件集合
   * @return 是否需要使用备选异常
   */
  private def unavailableSpecialLogic(exceptionName: String, location: Int, exceptionList: List[String], startEventList: ListBuffer[String]): Boolean = {
    val priorityEventList = monitorConfig.getPriorityEvents(exceptionName)
    val size = exceptionList.size
    // 如果存在优先的异常，则检索后续异常中是否有优先的异常，如果有则该异常不作为闭合事件
    if (priorityEventList != null && location != size - 1) {
      val sliceExceptionList = exceptionList.slice(location, size)
      for (i <- 0 until (priorityEventList.size())) {
        if (sliceExceptionList.contains(priorityEventList.get(i))) {
          return false
        }
      }
    }
    val correspondingEventList = monitorConfig.correspondingEventsMap.get(exceptionName)
    for (i <- 0 until correspondingEventList.size()) {
      if (startEventList.contains(correspondingEventList.get(i))) {
        return true
      }
    }
    return false
  }

  /**
   * 用来保存计算数据面不可用时长的原始数据
   * 这里保存的原始数据是不去重的，也就是可能存在不可用时长重复的地方
   *
   * @param startTime  计算异常的开始时间
   * @param endTime    计算异常的截止时间
   * @param rawRecords 原始异常数据，tuple的第一位是异常名称，第二位是异常时间
   */
  def listUnavailableException(startTime: String, endTime: String, rawRecords: List[(String, String)]): ListBuffer[(String, String, Double)] = {
    val sortedRecords = sortAndFilter(rawRecords)
    val preExceptionList = sortedRecords.filter(_._2 < startTime)
    val startEventSet = findUnclosedStartEvent(preExceptionList)

    val records = sortedRecords.filter(_._2 >= startTime)
    val eventMap = scala.collection.mutable.Map[String, ListBuffer[(String, String)]]()
    startEventSet.foreach(startEvent => {
      // 如果后续出现了一个开始事件对应多个结束事件，这部分代码需要修改
      val key = startEvent + "-" + monitorConfig.correspondingEventsMap.get(startEvent).get(0)
      eventMap += (key -> ListBuffer[(String, String)]((startEvent, startTime)))
    })
    // 先根据每一类起始事件划分数据
    for (monitor <- records) {
      val correspondEventList = monitorConfig.correspondingEventsMap.get(monitor._1)
      for (i <- 0 until correspondEventList.size) {
        val correspondEvent = correspondEventList.get(i)
        var key = monitor._1 + "-" + correspondEvent
        if (monitorConfig.isEndEvent(monitor._1)) {
          key = correspondEvent + "-" + monitor._1
        }
        if (eventMap.contains(key)) {
          eventMap(key).append((monitor._1, monitor._2))
        } else {
          // 如果是该闭合事件是首次出现，而且最先出现的是开始事件，则加入map中
          if (monitorConfig.isStartEvent(monitor._1)) {
            eventMap += (key -> ListBuffer[(String, String)]((monitor._1, monitor._2)))
          }
        }
      }
    }
    val result = ListBuffer[(String, String, Double)]()
    for ((events, exceptionList) <- eventMap) {
      val startEvent = events.split("-")(0)
      var isLastStartEvent = false
      var lastExceptionName = ""
      var lastExceptionTime = startTime
      val exceptionTimeConclude = ListBuffer[String]()
      var duration = 0.0
      breakable {
        for (tuple <- exceptionList) {
          val exceptionName = tuple._1
          val exceptionTime = tuple._2
          if (exceptionTime > endTime) {
            isLastStartEvent = isLastStartEvent || monitorConfig.isEndEvent(exceptionName) && !exceptionName.equals(lastExceptionName)
            break()
          }
          if (monitorConfig.isPointEvent(exceptionName)) {
            exceptionTimeConclude.append(exceptionTime)
            duration += 60
          } else if (monitorConfig.isEndEvent(exceptionName) && !exceptionName.equals(lastExceptionName)) {
            exceptionTimeConclude.append(lastExceptionTime + "-" + exceptionTime)
            duration += DateUtil.datetime2Timestamp(exceptionTime) - DateUtil.datetime2Timestamp(lastExceptionTime)
          } else if (!isLastStartEvent) {
            lastExceptionTime = exceptionTime
          }
          isLastStartEvent = monitorConfig.isStartEvent(exceptionName)
          lastExceptionName = exceptionName
        }
      }
      if (isLastStartEvent) {
        exceptionTimeConclude.append(lastExceptionTime + "-" + endTime)
        duration += DateUtil.datetime2Timestamp(endTime) - DateUtil.datetime2Timestamp(lastExceptionTime)
      }
      if (exceptionTimeConclude.nonEmpty) {
        result.append((startEvent, exceptionTimeConclude.mkString(","), duration / 60))
      }
    }
    result
  }

  /**
   * 找到窗口之前的未闭合的开始事件
   *
   * @param records 窗口之前的不可用事件
   * @return scala.collection.mutable.Set[String]
   */
  def findUnclosedStartEvent(records: List[(String, String)]): scala.collection.mutable.Set[String] = {
    val startEventSet = scala.collection.mutable.Set[String]()
    for (record <- records) {
      val exceptionName = record._1
      if (monitorConfig.isStartEvent(exceptionName)) {
        startEventSet.add(exceptionName)
      } else if (monitorConfig.isEndEvent(exceptionName)) {
        val correspondingEventList = monitorConfig.correspondingEventsMap.get(exceptionName)
        // 如果是结束事件，则把对应的多个开始事件全部结束
        for (i <- 0 until correspondingEventList.size()) {
          if (startEventSet.contains(correspondingEventList.get(i))) {
            startEventSet.remove(correspondingEventList.get(i))
          }
        }

      }
    }
    startEventSet
  }

  /**
   * 排序和过滤相邻的重复数据
   *
   * @param rawRecords 原始数据，未排序，未去重
   * @return List[(String, String)]
   */
  def sortAndFilter(rawRecords: List[(String, String)]): List[(String, String)] = {
    // 对于超大数据集，使用流式处理避免内存溢出
    if (rawRecords.size > 100000) {
      println(s"Warning: Very large dataset detected (${rawRecords.size} records). Using stream processing.")
      return sortAndFilterLargeDataset(rawRecords)
    }
    
    if (rawRecords.isEmpty) return List.empty
    
    val records = rawRecords.sortWith(sortException)
    val exceptionListBuffer = ListBuffer[(String, String)]()
    exceptionListBuffer.append(records.head)
    var expectDown = records.head._1.equals("key_customer_event_generate")
    for (i <- 1 until records.size) {
      val currentRecord = records(i)
      if (currentRecord._1.equals("key_customer_event_generate")) {
        expectDown = true
      }
      // 把一些相邻的重复的开始事件和结束事件过滤掉，这些重复数据有些是噪声数据
      if (!currentRecord._1.equals(exceptionListBuffer.last._1) && (!expectDown || !currentRecord._1.equals("nc_down_alert"))) {
        exceptionListBuffer.append((currentRecord._1, currentRecord._2))
      }
    }
    exceptionListBuffer.toList
  }

  // 处理超大数据集的排序和过滤，使用分批处理避免内存溢出
  private def sortAndFilterLargeDataset(rawRecords: List[(String, String)]): List[(String, String)] = {
    val batchSize = 50000
    val sortedBatches = rawRecords.grouped(batchSize).map(_.sortWith(sortException)).toList
    
    // 合并已排序的批次
    val mergedSorted = mergeSortedBatches(sortedBatches)
    
    // 对合并后的数据进行过滤
    filterDuplicates(mergedSorted)
  }

  // 合并多个已排序的批次
  private def mergeSortedBatches(batches: List[List[(String, String)]]): List[(String, String)] = {
    batches.reduce { (batch1, batch2) =>
      mergeTwoSortedLists(batch1, batch2)
    }
  }

  // 合并两个已排序的列表
  private def mergeTwoSortedLists(list1: List[(String, String)], list2: List[(String, String)]): List[(String, String)] = {
    val result = ListBuffer[(String, String)]()
    var i = 0
    var j = 0
    
    while (i < list1.length && j < list2.length) {
      if (sortException(list1(i), list2(j))) {
        result += list1(i)
        i += 1
      } else {
        result += list2(j)
        j += 1
      }
    }
    
    // 添加剩余元素
    while (i < list1.length) {
      result += list1(i)
      i += 1
    }
    while (j < list2.length) {
      result += list2(j)
      j += 1
    }
    
    result.toList
  }

  // 过滤重复数据
  private def filterDuplicates(records: List[(String, String)]): List[(String, String)] = {
    if (records.isEmpty) return List.empty
    
    val result = ListBuffer[(String, String)]()
    result.append(records.head)
    var expectDown = records.head._1.equals("key_customer_event_generate")
    
    for (i <- 1 until records.size) {
      val currentRecord = records(i)
      if (currentRecord._1.equals("key_customer_event_generate")) {
        expectDown = true
      }
      if (!currentRecord._1.equals(result.last._1) && (!expectDown || !currentRecord._1.equals("nc_down_alert"))) {
        result.append((currentRecord._1, currentRecord._2))
      }
    }
    result.toList
  }

  /**
   * 根据异常的异常名称和异常时间排序，先按照异常时间排序，如果时间先等的话，则按照异常名称排序，开始事件排在结束事件的前面
   *
   * @param exception1 异常
   * @param exception2 异常
   * @return Boolean
   */
  def sortException(exception1: (String, String), exception2: (String, String)): Boolean = {
    // 如果两个异常的异常时间相等，排序顺序start>end>point
    if (exception1._2.equals(exception2._2)) {
      val eventType1 = monitorConfig.getEventType(exception1._1)
      val eventType2 = monitorConfig.getEventType(exception2._1)
      if (eventType1.equals(eventType2)) {
        exception1._1.compareTo(exception2._1) < 0
      } else if (monitorConfig.isStartEvent(exception1._1)) {
        true
      } else {
        false
      }
    } else {
      exception1._2 < exception2._2
    }
  }

  /**
   * 根据一种异常的多个开始事件和结束事件，匹配成（开始时间，结束时间，持续时长）的记录
   *
   * @param data        原始数据，允许有重复数据，格式为（异常类型，异常时间），异常类型为"start" or "end"
   * @param prePadding  是否在前面填充一个开始事件
   * @param postPadding 是否在后面填充一个结束事件
   * @return （异常事件，开始时间，结束时间，持续时长）
   */
  def listExceptionInterval(exceptionName: String, source: List[(String, String)], startTime: String, endTime: String, prePadding: Boolean = false, postPadding: Boolean = false): List[(String, String, String, Int)] = {
    val filteredData = scala.collection.mutable.ListBuffer[(String, String)]()
    val data = source.sortBy(_._2)
    val size = data.size
    for (i <- 0 until size) {
      val monitor = data(i)
      val eventType = monitor._1
      if (i == 0) {
        filteredData.append(monitor)
      } else if (eventType.equals("start") && data(i - 1)._1.equals("end") || eventType.equals("end") && data(i - 1)._1.equals("start")) {
        filteredData.append(monitor)
      }
    }
    if (prePadding) {
      if (filteredData.nonEmpty && filteredData.head._1.equals("end")) {
        filteredData.prepend(("start", startTime))
      }
    } else {
      if (filteredData.nonEmpty && filteredData.head._1.equals("end")) {
        filteredData.remove(0)
      }
    }
    if (postPadding) {
      if (filteredData.nonEmpty && filteredData.last._1.equals("start")) {
        filteredData.append(("end", endTime))
      }
    } else {
      if (filteredData.nonEmpty && filteredData.last._1.equals("start")) {
        filteredData.remove(filteredData.size - 1)
      }
    }
    val result = scala.collection.mutable.ListBuffer[(String, String, String, Int)]()
    for (i <- Range(0, filteredData.size, 2)) {
      val startTime = filteredData(i)._2
      val endTime = filteredData(i + 1)._2
      val startTimestamp = DateUtil.datetime2Timestamp(startTime)
      val endTimestamp = DateUtil.datetime2Timestamp(endTime)
      val duration = endTimestamp - startTimestamp
      result.append((exceptionName, startTime, endTime, duration.toInt))
    }
    result.toList
  }

  /**
   * 计算vm的加权性能受损时长和控制面异常时长，默认传进来的就是时间窗口内的数据
   *
   * @param rawRecords vm的性能受损时长和控制面受损时长，元组表示为（异常名称，异常时间，异常等级）
   * @return Double 受损时长
   */
  def computeWeightedDamagedTime(rawRecords: List[(String, String, Int)]): Double = {
    val minuteAndExceptionMap = scala.collection.mutable.Map[String, scala.collection.mutable.Set[(String, Int)]]()
    // 将同一分钟的异常放到一个集合中，同时去重同一分钟内反复出现的异常
    for (monitor <- rawRecords) {
      val minute = monitor._2.substring(0, 16)
      if (minuteAndExceptionMap.contains(minute)) {
        minuteAndExceptionMap(minute).add((monitor._1, monitor._3))
      } else {
        minuteAndExceptionMap += (minute -> scala.collection.mutable.Set[(String, Int)]((monitor._1, monitor._3)))
      }
    }
    minuteAndExceptionMap
      .map(item => item._2.map(tuple => 1.2 - tuple._2 * 0.2).sum)
      .sum
  }

  /**
   * 计算vm的性能受损时长和控制面异常时长，默认传进来的就是时间窗口内的数据
   *
   * @param rawRecords vm的性能受损时长和控制面受损时长，元组表示为（异常名称，异常时间，异常等级）
   * @return Double 受损时长
   */
  def computeDamagedTime(rawRecords: List[(String, String, Int)]): Double = {
    rawRecords.map(row => row._2.substring(0, 16)).distinct.size.toDouble
  }

  def count(data: List[String], window: Int): Int = {
    data.map(getClosestTime(_, window)).toSet.size
  }

  /**
   * 获取最接近的时间
   *
   * @param time   时间
   * @param window 窗口大小
   * @return String 最接近的时间
   */
  private def getClosestTime(time: String, window: Int): String = {
    val minutes = time.substring(14, 16).toInt
    var newMinutes = (minutes / window * window).toString
    if (newMinutes.length < 2) {
      newMinutes = "0" + newMinutes
    }
    time.substring(0, 14) + newMinutes
  }

  /**
   * 用于性能受损数据的去重计算
   *
   * @param records 三元组第一个元素为异常名称，第二个为异常时间，第三个为异常状态：start|end
   * @return 返回的结果中，二元组的第一个元素代表异常名称，第二个为异常时长
   */
  def performanceDurationDeduplication(records: List[(String, String, String)]): List[(String, Double)] = {
    val sortedRecords = records.sortBy(_._2)
    val startEvents = scala.collection.mutable.ListBuffer[String]()
    var lastExceptionTime = sortedRecords.head._2
    startEvents.append(sortedRecords.head._1)
    val result = scala.collection.mutable.Map[String, Double]()
    for (i <- 1 until (sortedRecords.size)) {
      val exception = sortedRecords(i)
      val name = exception._1
      val time = exception._2
      val event = exception._3
      if (event.equals("start")) {
        startEvents.append(name)
        if (startEvents.size == 1) {
          lastExceptionTime = time
        }
      } else {
        val index = startEvents.indexOf(name)
        if (index == 0) {
          var duration = result.getOrElse(name, 0.0)
          duration += DateUtil.datetime2Timestamp(time) - DateUtil.datetime2Timestamp(lastExceptionTime)
          result.put(name, duration)
          lastExceptionTime = time
        }
        startEvents.remove(index)
      }
    }
    result.toList
  }

  def startEndProcessing(records: List[MonitorWithWarning]): ListBuffer[(String, String, Double, String, Int, String, String, String, String, String)] = {
    val startTime = "2024-09-21 00:00:00"
    val endTime = "2024-09-22 00:00:00"

    val exceptionList = records.map(record => (record.exception_name, record.exception_time, record.warning_level))
    val tuples = this.listPerformanceEventWithoutCoverage(startTime = startTime, endTime = endTime, exceptionList)
    val exceptionMap = scala.collection.mutable.Map[String, ListBuffer[MonitorWithWarning]]()
    for (record <- records) {
      if (exceptionMap.contains(record.exception_name)) {
        exceptionMap(record.exception_name).append(record)
      } else {
        exceptionMap.put(record.exception_name, ListBuffer(record))
      }
    }
    for ((k, v) <- exceptionMap) {
      val sortedV = v.sortBy(_.exception_time)
      exceptionMap.put(k, sortedV)
    }

    val result = ListBuffer[(String, String, Double, String, Int, String, String, String, String, String)]()

    for (tuple <- tuples) {
      val startTime = tuple._2
      val list = exceptionMap(tuple._1)
      var target = list.head
      breakable {
        for (i <- list.indices) {
          if (list(i).exception_time > startTime) {
            if (i > 0) {
              target = list(i - 1)
            }
            break
          }
        }
      }
      result.append((target.instance_id, tuple._1, tuple._4, target.monitor_type, target.monitor_level, target.nc_ip, target.nc_id, target.sn, target.is_local_disk, target.ali_uid))
    }
    result
  }

  def listPerformanceEventWithoutCoverage(startTime: String, endTime: String, rawRecords: List[(String, String, String)]): ListBuffer[(String, String, String, Double)] = {
    // 添加数据集大小检查
    if (rawRecords.size > 30000) {
      println(s"Warning: Large performance dataset detected (${rawRecords.size} records). Processing in batches.")
      return listPerformanceEventInBatches(startTime, endTime, rawRecords, 5000)
    }

    def findWarningLevel(record: (String, String), rawRecords: List[(String, String, String)]): String = {
      for (i <- rawRecords.indices) {
        val exceptionName = rawRecords(i)._1
        val exceptionTime = rawRecords(i)._2
        val warningLevel = rawRecords(i)._3
        if (exceptionName.equals(record._1) && exceptionTime.equals(record._2)) {
          return warningLevel
        }
      }
      ""
    }

    // Convert to List[(String, String)]
    val recordsOnlyTwo = rawRecords.map(record => (record._1, record._2))
    val sortedRecords = sortAndFilter(recordsOnlyTwo)
    var records = sortedRecords.map(record => (record._1, record._2, findWarningLevel(record, rawRecords)))
    val startEventList = findUnclosedPerformanceStartEvent(records, startTime) // 找到窗口之前未闭合的开始事件
    records = records.filter(_._2 >= startTime)

    var lastExceptionTime = startTime
    var lastExceptionName = ""
    val result = ListBuffer[(String, String, String, Double)]()
    breakable {
      for (i <- records.indices) {
        val record = records(i)
        val exceptionName = record._1
        val exceptionTime = record._2
        val warningLevel = record._3
        if (exceptionTime > endTime) {
          //          shouldBackUp = monitorConfig.isEndEvent(exceptionName) && !monitorConfig.needCorresponding(exceptionName)  && !exceptionName.equals(lastExceptionName) && !(startEventList.isEmpty && exceptionName.equals("vm_vsock_icmp_ping_loss_recovery"))
          //          lastExceptionName = exceptionName
          break()
        }
        if (!monitorConfig.needCorresponding(exceptionName)) {
          if (monitorConfig.getEventType(exceptionName, warningLevel).equals("start")) {
            if (!startEventList.contains(exceptionName)) {
              if (startEventList.isEmpty) {
                lastExceptionTime = exceptionTime
              }
              startEventList.append(exceptionName)
            }
          } else {
            var find = false
            val correspondingEventList = monitorConfig.correspondingEventsMap.get(exceptionName)
            for (i <- 0 until correspondingEventList.size()) {
              val startEvent = correspondingEventList.get(i)
              if (startEventList.contains(startEvent)) {
                if (!find) {
                  val index = startEventList.indexOf(startEvent)
                  if (index == 0) {
                    val duration = DateUtil.datetime2Timestamp(exceptionTime) - DateUtil.datetime2Timestamp(lastExceptionTime)
                    result.append((startEvent, lastExceptionTime, exceptionTime, duration))
                    find = true
                  }
                }
                startEventList -= startEvent
              }
            }
            if (find) {
              lastExceptionTime = exceptionTime
            }
            lastExceptionName = exceptionName
          }
        }
      }
    }
    if (startEventList.nonEmpty) {
      val nextTimestamp = DateUtil.datetime2Timestamp(endTime)
      val lastTimestamp = DateUtil.datetime2Timestamp(lastExceptionTime)
      val startEvent = startEventList.head
      result.append((startEvent, lastExceptionTime, endTime, (nextTimestamp - lastTimestamp)))
    }
    result
  }

  // 分批处理性能事件数据
  private def listPerformanceEventInBatches(startTime: String, endTime: String, rawRecords: List[(String, String, String)], batchSize: Int): ListBuffer[(String, String, String, Double)] = {
    val batches = rawRecords.grouped(batchSize).toList
    val result = ListBuffer[(String, String, String, Double)]()
    
    for (batch <- batches) {
      val batchResult = listPerformanceEventWithoutCoverage(startTime, endTime, batch)
      result ++= batchResult
    }
    result
  }

  def findUnclosedPerformanceStartEvent(records: List[(String, String, String)], endTime: String): scala.collection.mutable.ListBuffer[String] = {
    val startEventList = scala.collection.mutable.ListBuffer[String]()
    breakable {
      for (record <- records) {
        if (record._2 > endTime) {
          break()
        }
        val exceptionName = record._1
        val warningLevel = record._3
        if (monitorConfig.getEventType(exceptionName, warningLevel).equals("start") && !startEventList.contains(exceptionName)) {
          startEventList.append(exceptionName)
        } else if (monitorConfig.getEventType(exceptionName, warningLevel).equals("end")) {
          val correspondingEventList = monitorConfig.correspondingEventsMap.get(exceptionName)
          // 如果是结束事件，则把对应的多个开始事件全部结束
          for (i <- 0 until correspondingEventList.size()) {
            val startEvent = correspondingEventList.get(i)
            if (startEventList.contains(startEvent)) {
              startEventList -= startEvent
            }
          }
        }
      }
    }
    startEventList
  }

  def splitTimeRange(startTime: Timestamp, endTime: Timestamp, excludeRanges: List[(Timestamp, Timestamp)]): List[(Timestamp, Timestamp)] = {
    // 辅助方法：判断两个区间是否相交
    def overlaps(a: (Timestamp, Timestamp), b: (Timestamp, Timestamp)): Boolean =
      a._1.before(b._2) && b._1.before(a._2)

    // 辅助方法：合并重叠区间 - 使用迭代避免 StackOverflow
    def mergeRanges(ranges: List[(Timestamp, Timestamp)]): List[(Timestamp, Timestamp)] = {
      if (ranges.isEmpty) return Nil
      if (ranges.size > 10000) {
        // 对于超大数据集，使用简化的分批处理避免内存溢出
        val batchSize = 5000
        val sorted = ranges.sortBy(_._1.getTime)
        val batches = sorted.grouped(batchSize).toList
        
        return batches.foldLeft(List.empty[(Timestamp, Timestamp)]) { (acc, batch) =>
          val mergedBatch = if (batch.isEmpty) List.empty else {
            val sortedBatch = batch.sortBy(_._1.getTime)
            val result = scala.collection.mutable.ListBuffer[(Timestamp, Timestamp)]()
            var current = sortedBatch.head
            for (i <- 1 until sortedBatch.length) {
              val next = sortedBatch(i)
              if (next._1.before(current._2) || next._1 == current._2) {
                val newEnd = if (current._2.after(next._2)) current._2 else next._2
                current = (current._1, newEnd)
              } else {
                result += current
                current = next
              }
            }
            result += current
            result.toList
          }
          if (acc.isEmpty) mergedBatch
          else {
            // 简单合并，避免递归
            (acc ++ mergedBatch).sortBy(_._1.getTime).foldLeft(List.empty[(Timestamp, Timestamp)]) { (result, curr) =>
              if (result.isEmpty) List(curr)
              else {
                val last = result.last
                if (curr._1.before(last._2) || curr._1 == last._2) {
                  val newEnd = if (last._2.after(curr._2)) last._2 else curr._2
                  result.dropRight(1) :+ (last._1, newEnd)
                } else {
                  result :+ curr
                }
              }
            }
          }
        }
      }

      val sorted = ranges.sortBy(_._1.getTime)
      val result = scala.collection.mutable.ListBuffer[(Timestamp, Timestamp)]()
      
      var current = sorted.head
      for (i <- 1 until sorted.length) {
        val next = sorted(i)
        if (next._1.before(current._2) || next._1 == current._2) {
          // 合并重叠区间
          val newEnd = if (current._2.after(next._2)) current._2 else next._2
          current = (current._1, newEnd)
        } else {
          // 没有重叠，添加当前区间到结果
          result += current
          current = next
        }
      }
      result += current  // 添加最后一个区间
      result.toList
    }



    // 主逻辑
    val sourceRange = (startTime, endTime)

    // 1. 找出所有相交区间
    val overlapping = excludeRanges.filter(overlaps(sourceRange, _))

    // 2. 合并相交区间
    val merged = mergeRanges(overlapping)

    // 3. 切割原始区间
    var current = startTime
    var result = List.empty[(Timestamp, Timestamp)]

    merged.foreach { case (s, e) =>
      if (s.after(current)) {
        result :+= (current, s)
      }
      current = if (e.after(current)) e else current
    }

    if (current.before(endTime)) {
      result :+= (current, endTime)
    }

    result
  }

  /**
   * 判断目标区间与区间列表是否相交（参数保持元组形式）
   *
   * @param targetStart 目标开始时间
   * @param targetEnd   目标结束时间
   * @param timeRanges  待检查的时间区间列表
   * @return 存在相交返回true，否则false
   */
  def hasOverlap(targetStart: Timestamp, targetEnd: Timestamp, timeRanges: List[(Timestamp, Timestamp)]): Boolean = {
    // 辅助方法保持相同逻辑
    def overlaps(a: (Timestamp, Timestamp), b: (Timestamp, Timestamp)): Boolean =
      a._1.before(b._2) && b._1.before(a._2)

    timeRanges.exists(overlaps((targetStart, targetEnd), _))
  }

}

object KeyMetricUtil {
  def apply(monitorConfig: MonitorConfig = new MonitorConfig()): KeyMetricUtil = new KeyMetricUtil(monitorConfig)
}
