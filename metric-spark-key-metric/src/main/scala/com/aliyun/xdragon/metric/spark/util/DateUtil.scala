package com.aliyun.xdragon.metric.spark.util


import java.sql.Timestamp
import java.text.SimpleDateFormat
import java.util.{Calendar, Date}

object DateUtil {

  val dateFormat = "yyyyMMdd"
  val dateTimeFormat = "yyyy-MM-dd HH:mm:ss"

  def getDate(days: Int): String = {
    val format: SimpleDateFormat = new SimpleDateFormat(dateFormat)
    val calendar: Calendar = Calendar.getInstance()
    // 往前一天
    calendar.add(Calendar.DATE, days)
    format.format(calendar.getTime)
  }

  def getCurrentTimestamp(): Timestamp = {
    new Timestamp(System.currentTimeMillis())
  }

  def getMidnight(days: Int, dateTimeFormat: String = dateTimeFormat): String = {
    val format: SimpleDateFormat = new SimpleDateFormat(dateTimeFormat)
    val calendar: Calendar = Calendar.getInstance()
    // 往前一天
    calendar.add(Calendar.DATE, days)
    format.format(calendar.getTime).substring(0, 10) + " 00:00:00"
  }

  def datetime2Timestamp(dateTime: String, dateTimeFormat: String = dateTimeFormat, detail: Boolean = false): Long = {
    val format: SimpleDateFormat = new SimpleDateFormat(dateTimeFormat)
    val timestamp = format.parse(dateTime).getTime
    if (detail) timestamp else timestamp / 1000
  }

  def timestamp2Datetime(timestamp: Long, dateTimeFormat: String = dateTimeFormat): String = {
    val format: SimpleDateFormat = new SimpleDateFormat(dateTimeFormat)
    // 判断时间戳是秒级还是毫秒级，如果是秒级则转换为毫秒级
    val millisTimestamp = if (timestamp < 10000000000L) timestamp * 1000 else timestamp
    format.format(new Date(millisTimestamp))
  }
  
  def modifyDate(dateStr: String, months: Int = 0, days: Int = 0, hours: Int = 0, minutes: Int = 0, seconds: Int = 0, dateTimeFormat: String = dateTimeFormat): String = {
    val format: SimpleDateFormat = new SimpleDateFormat(dateTimeFormat)
    val date = format.parse(dateStr)
    val calendar: Calendar = Calendar.getInstance()
    calendar.setTime(date)
    calendar.add(Calendar.MONTH, months)
    calendar.add(Calendar.DATE, days)
    calendar.add(Calendar.HOUR, hours)
    calendar.add(Calendar.MINUTE, minutes)
    calendar.add(Calendar.SECOND, seconds)
    format.format(calendar.getTime)
  }

  def changeFormat(dateStr: String, oldFormat: String, newFormat: String): String = {
    val oldDateFormat = new SimpleDateFormat(oldFormat)
    val newDateFormat = new SimpleDateFormat(newFormat)
    newDateFormat.format(oldDateFormat.parse(dateStr))
  }

  def formatTimestamp(ts: Timestamp, formatStr: String): String = {
    val format: SimpleDateFormat = new SimpleDateFormat(formatStr)
    format.format(ts)
  }
}
