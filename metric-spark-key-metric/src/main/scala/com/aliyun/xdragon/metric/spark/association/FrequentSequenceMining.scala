package com.aliyun.xdragon.metric.spark.association

import com.aliyun.xdragon.metric.spark.util.DateUtil
import org.apache.spark.sql.SparkSession
import fpm.PrefixSpan.findFrequentSequentialPatterns
import org.apache.spark.sql.functions.{col, lit}

import scala.collection.mutable.ListBuffer
import scala.util.control.Breaks.{break, breakable}

/**
 * <AUTHOR>
 * @note 使用PrefixSpan挖掘宕机nc的频繁序列
 */

object FrequentSequenceMining extends Serializable {

  val unavailableFeatures = Set("driver_issue_cause_panic", "kernel_npe", "cn_system_crash", "system_crash", "tianji_pingmesh_down", "" +
    "nc_down_active", "upgrade_nc_down_active", "nc_real_down", "nc_dpdk_driver_flow_drop_all", "cn_key_process_oom", "xdragon_cn_down",
  "xdragon_cn_reboot", "kernel_fault_with_detail", "cn_hang_issue_analysed")

  def main(args: Array[String]): Unit = {

    val spark = SparkSession.builder
      .appName("sequence mining")
      .config("spark.sql.broadcastTimeout", 20 * 60)
      .config("spark.sql.crossJoin.enabled", value = true)
      .getOrCreate()

    val ds = args(0)
    //  时间窗口为2天
    val window = 2
    val lastDs = DateUtil.modifyDate(ds, days = -window, dateTimeFormat = DateUtil.dateFormat)
    val timestampThreshold = DateUtil.datetime2Timestamp(ds, DateUtil.dateFormat, true)


    val featureSql = s"SELECT DISTINCT feature_name FROM xdc_feature_definition where pt='$ds' and" +
      s" reason IN ('nc_down', 'nc_hang') and feature_type not in ('ops', 'ecs_controller') and feature_name <> 'pync_heartbeat_log_loss' "

    import spark.implicits._
    val downFeatures = spark
      .sql(featureSql)
      .map(_.getString(0))
      .collect().toList

    val gammaTable = "ecs_release_plan_gamma_host_v2"
    val gammaSql = s"SELECT distinct nc_ip FROM $gammaTable WHERE ds = '$ds'"

    val table = "xdc_feature_data"
    val sql = s"SELECT distinct target_id, feature_name, CAST(matched_timestamp as bigint) as matched_timestamp, " +
      s"ds as slideWindow FROM $table WHERE ds <='$ds' and ds >= '$lastDs' and target_id not in ($gammaSql) " +
      s" and target_id in ( select distinct target_id from $table where ds=$ds and feature_name in ($featureSql))"

    val df = spark
      .sql(sql)
      .as[Feature]
      .groupByKey(row=>(row.target_id))
      .mapGroups((_, iter)=>{
        val featureSeq = iter.toList.sortBy(_.matched_timestamp).map(row=>(row.matched_timestamp, row.feature_name))
        val sequence = ListBuffer[(Long, String)]()
        var downTimestamp = 0L
        var fail = false
        breakable{
          for (r <- featureSeq) {
            if (downFeatures.contains(r._2) && r._1 < timestampThreshold) {
              fail = true
              break()
            } else if (downFeatures.contains(r._2)) {
              sequence.append(r)
              downTimestamp = r._1
              break()
            }
            sequence.append(r)
          }
        }
        if (fail) {
          Seq()
        } else {
          sequence
            .filter(r => r._1 >= (downTimestamp - 172800000))
            .map(_._2)
            .distinct
            .map(x => Seq(x))
        }
      }).toDF("sequence")
      .filter(row=>row.getSeq(0).nonEmpty)

    df.show(truncate = false)

    val support = 0.05

    // 获取频繁模式序列，并去除长度为1的序列，会有些反复出现的特征，这种特征会造成干扰，这类特征会和其它特征以及自己组成频繁序列模式
    val frequentPatterns = findFrequentSequentialPatterns(df, support = support, maxLen = 5)
      .map(row=>(row.getSeq[Seq[String]](0), row.getSeq[Seq[String]](0).last.head, row.getLong(1), row.getSeq(0).size))
      .filter(_._4 > 1)
      .toDF("sequence", "target", "freq", "len")

    val total = df.count()

    frequentPatterns
      .withColumn("total", lit(total))
      .withColumn("support", lit(support))
      .withColumn("ds", lit(ds))
      .write
      .mode("overwrite")
      .insertInto("xdragon_frequent_sequence_mining")

  }

}
