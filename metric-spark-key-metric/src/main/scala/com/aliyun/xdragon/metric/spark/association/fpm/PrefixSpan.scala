package com.aliyun.xdragon.metric.spark.association.fpm

import org.apache.spark.sql.{DataFrame}
import org.apache.spark.ml.fpm.PrefixSpan

/**
 * <AUTHOR>
 * @note 考虑时间顺序的频繁序列挖掘，可以用于运维规则的生成，比如输入某一项特征，然后挖掘出该特征的频繁序列模型，将该特征作为结果项，
 *       前序特征作为根因
 */
object PrefixSpan {

  def findFrequentSequentialPatterns(df: DataFrame, support: Double = 0.5, maxLen: Int = 7,
                                     sequenceCol: String = "sequence"): DataFrame = {
    val model = new PrefixSpan()
      .setMinSupport(support)
      .setMaxPatternLength(maxLen)
      .setSequenceCol(sequenceCol)

    model.findFrequentSequentialPatterns(df)
  }
}
