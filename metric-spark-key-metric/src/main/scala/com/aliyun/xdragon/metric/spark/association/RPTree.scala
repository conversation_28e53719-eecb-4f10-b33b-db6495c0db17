package com.aliyun.xdragon.metric.spark.association

import com.aliyun.xdragon.metric.spark.association.fpm.FPGrowth
import com.aliyun.xdragon.metric.spark.util.DateUtil
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions.{col, countDistinct, lit, udf}

import scala.collection.mutable
import scala.collection.mutable.ListBuffer
import scala.util.control.Breaks.{break, breakable}


/**
 * 使用FP-tree来实现RP-tree
 * 1、先获得rare item，可以考虑直接根据数据量来计算
 * 2、只保留含rate item和目标特征的项集
 * 3、使用FP-Growth实现rare itemsets的关联关系的挖掘
 * 4、过滤条件:
 *    1）根据宕机特征出现的频率，过滤一些频繁出现的宕机特征，排除pync_heartbeat_log_loss
 *    2）第二个是要看命中的特征和宕机特征之间有没有提前量，将和宕机特征同时的特征排除掉
 *
 * feature_type为ecs_controller的和ops
 */
object RPTree {

  def main(args: Array[String]): Unit = {

    val spark = SparkSession
      .builder
      .appName("RP-tree")
      .config("spark.sql.broadcastTimeout", 20 * 60)
      .config("spark.sql.crossJoin.enabled", value = true)
      .getOrCreate()


    val ds = args(0)
    //  向前回溯，以2天作为一个时间窗口
    val days = 1
    val lastDs = DateUtil.modifyDate(ds, days = -days, dateTimeFormat = DateUtil.dateFormat)

    val gammaTable = "ecs_release_plan_gamma_host_v2"
    val gammaSql = s"SELECT distinct nc_ip FROM $gammaTable WHERE ds = '$ds'"


    // 主动运维之后的特征都不考虑了
    val table = "xdc_feature_data"
    val sql = s"SELECT distinct target_id, feature_name, CAST(matched_timestamp as bigint) as matched_timestamp, " +
      s"ds as slideWindow FROM $table WHERE ds <= '$ds' and ds >= '$lastDs' and target_id not in ($gammaSql)"

//    // 设置稀有项集的阈值参数，这个阈值不好设置，直接使用数量来代替阈值
//    val rareRate = 0.005
//    val minimumSup = 0.001

    import spark.implicits._
    val featureDF = spark
      .sql(sql)
      .as[Feature]

//    val transactionNum = featureDF
//      .agg(countDistinct("target_id"))
//      .head()
//      .getLong(0)

    val rareThreshold = 300.0
    val minimumThreshold = 10.0

    val rareFeatures = featureDF
      .groupBy("feature_name")
      .agg(countDistinct("target_id").as("count"))
      .filter(col("count") >= minimumThreshold && col("count") <= rareThreshold)
      .select("feature_name")
      .collect()
      .map(_.getString(0))
      .toList

    val featureSql = s"SELECT DISTINCT feature_name FROM xdc_feature_definition where pt='$ds' and" +
      s" reason IN ('nc_down', 'nc_hang') and feature_type not in ('ops', 'ecs_controller') and feature_name <> 'pync_heartbeat_log_loss' "

    val downFeatures1 = spark
      .sql(featureSql)
      .map(_.getString(0))
      .collect()
      .toList

    val downFeatures2 = List("down_start_trigger_fastpath", "nic_error_cause_nc_down", "eci_independent_pool_controll_vm_down_need_realease",
    "inspect_nc_down_affect_vm_fastpath", "jinlun_detect_other_error", "jinlun_detect_software_error", "vm_migrate_cause_nc_down",
    "eci_os_panic", "nc_lost_power_cause_nc_down", "memory_error_cause_nc_down", "hardware_cause_nc_down_low_ratio", "unknown_power_error",
    "kernel_npe", "low_critical_kernel_issue", "system_crash", "down_start_trigger", "inspect_nc_down_affect_vm", "power_error_cause_nc_down",
    "kernel_fault_with_detail", "cpu_microcode_fault_cause_nc_down")

    val opsFeatureSql = s"SELECT DISTINCT feature_name FROM xdc_feature_definition where pt='$ds' and feature_type = 'ops' "
    val opsFeatures = spark
      .sql(opsFeatureSql)
      .map(_.getString(0))
      .collect()
      .toList

    val downFeatures = downFeatures1 ++ downFeatures2

    val colName = "items"

    val itemDF = featureDF
      .groupByKey(row=>(row.target_id))
      .mapGroups((_, iter) => {
        // 把一些没有提前运维空间的特征给它过滤掉
        val features = iter.toList.sortBy(_.matched_timestamp).map(row=>(row.feature_name, row.matched_timestamp)).distinct
        val targetFeatures = ListBuffer[(String, Long)]()
        breakable{
          for (f <- features) {
            if (downFeatures.contains(f._1)) {
              targetFeatures.append(f)
              break()
            } else if (opsFeatures.contains(f._1)) {
              break()
            } else if (rareFeatures.contains(f._1)) {
              targetFeatures.append(f)
            }
          }
        }
        var downTimestamp: Long = -1
        val size = targetFeatures.size - 1
        breakable{
          for (i <- Range(size, -1, -1)) {
            if (downTimestamp == -1) {
              downTimestamp = targetFeatures(i)._2
            } else if (targetFeatures(i)._2 == downTimestamp) {
              targetFeatures.remove(i)
            } else {
              break()
            }
          }
        }
        targetFeatures.map(_._1).distinct.toSeq
      }).toDF(colName)
      .filter(_.getSeq(0).nonEmpty )

    val size = itemDF.count()

    val support = minimumThreshold / size
    val confidence = 0.3
    //  partition number = executor * cores * (2, 3)
    val partitionNum = 600
    val result = FPGrowth.FPGrowthProcess(itemDF, support = support, confidence = confidence, partition = partitionNum,
      col_name = colName, false)

    val consequentFunc = (x: mutable.WrappedArray[String]) => {
      downFeatures.contains(x.head)
    }
    val consequentUdf = udf(consequentFunc)

    // 只保留宕机特征的关联关系
    val ruleDF = result._2
      .filter(col("lift") > 1.0)
      .filter(consequentUdf(col("consequent")))

    val rule = "rare-item"

    result
      ._1
      .withColumn("total_items", lit(size))
      .withColumn("support", lit(support))
      .withColumn("ds", lit(ds))
      .withColumn("rule", lit(rule))
      .select("item_set", "frequency", "set_size", "total_items", "support", "rule", "ds")
      .write
      .mode("overwrite")
      .insertInto("ecs_feature_rare_item_set")

    ruleDF
      .withColumn("minimum_size", lit(minimumThreshold))
      .withColumn("ds", lit(ds))
      .withColumn("rule", lit(rule))
      .select("antecedent", "consequent", "confidence", "lift", "minimum_size", "rule", "ds")
      .write
      .mode("overwrite")
      .insertInto("ecs_association_rule_by_rare_items")

  }

}
