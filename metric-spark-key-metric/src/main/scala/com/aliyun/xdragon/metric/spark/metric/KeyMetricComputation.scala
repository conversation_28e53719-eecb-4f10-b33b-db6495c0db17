package com.aliyun.xdragon.metric.spark.metric

import com.aliyun.xdragon.metric.spark.config.{MetricCorrectionConfig, MonitorConfig, MonitorExclusionConfig}
import com.aliyun.xdragon.metric.spark.util.DateUtil
import com.aliyun.xdragon.metric.spark.util.DateUtil.{dateFormat, dateTimeFormat}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.LongType
import org.apache.spark.sql.{DataFrame, Encoders, SparkSession}

import java.sql.Timestamp
import scala.collection.JavaConversions.asScalaBuffer
import scala.collection.mutable.ListBuffer
import scala.language.postfixOps
import scala.util.control.Breaks.{break, breakable}


case class Record(instance_id: String, monitor_type: String, abnormal_time: Double)

case class Monitor(instance_id: String, exception_name: String, exception_time: String, monitor_type: String,
                   monitor_level: Int, status: String, warning_value: String, nc_ip: String, nc_id: String, sn: String,
                   ali_uid: String, additional_info: String, is_local_disk: String)

case class MonitorWithWarning(instance_id: String, exception_name: String, exception_time: String, monitor_type: String,
                              monitor_level: Int, status: String, warning_value: String, nc_ip: String, nc_id: String, sn: String,
                              ali_uid: String, additional_info: String, is_local_disk: String, warning_level: String)

case class UnavailableRecord(instance_id: String, exception_name: String, monitor_type: String, monitor_level: Int, start_time: String, end_time: String, duration: Double, nc_ip: String, nc_id: String, sn: String, ali_uid: String, is_local_disk: String)

/*
 * <AUTHOR>
 * @note 用来计算实例的key metric
 */

object KeyMetricComputation {

  def main(args: Array[String]): Unit = {

    val spark = SparkSession.builder
      .appName("key metric computation for vm")
      .config("spark.sql.broadcastTimeout", 30 * 60)
      .config("spark.sql.crossJoin.enabled", value = true)
      .getOrCreate()
    val ds = args(0)
    val process = if (args.length > 1) args(1) else "test"

    val exclusionConfig = new MonitorExclusionConfig(ds)
    val monitorConfig = new MonitorConfig()
    val correctionConfig = new MetricCorrectionConfig()

    if (process == "prod") {
      mainFlow(ds, spark, "ecs_key_metric_clean_monitor", "ecs_original_key_metric", "ecs_daily_key_metric", "ecs_key_metric_raw_event", "ecs_key_metric_raw_monitor", "ecs_key_metric_unexpected_event", monitorConfig, exclusionConfig, correctionConfig)
    } else if (process == "test") {
      mainFlow(ds, spark, "ecs_key_metric_clean_monitor_for_test", "", "", "ecs_key_metric_raw_event_for_test", "ecs_key_metric_raw_monitor_for_test", "", monitorConfig, exclusionConfig, correctionConfig)
    }
  }

  def mainFlow(ds: String, spark: SparkSession, cleanTable: String, originalTable: String, dailyTable: String, unavailableTable: String, damageTable: String, unexpectedTable: String, monitorConfig: MonitorConfig, exclusionConfig: MonitorExclusionConfig, correctionConfig: MetricCorrectionConfig): Unit = {

    val keyMetricUtil = KeyMetricUtil(monitorConfig)

    // 传入参数，startTime是当天凌晨时间，endTime是下一天的凌晨时间
    val startTime = DateUtil.changeFormat(ds, dateFormat, dateTimeFormat)
    val endTime = DateUtil.modifyDate(startTime, days = 1)

    // 监控数据表需要获取三天的数据，获取前一天数据是为了找到未闭合的开始事件，获取后一天的数据是因为监控数据会"迟到"，导致有些监控数据不在当天的ds分区内
    val preDay = DateUtil.modifyDate(startTime, days = -1)
    val startDs = DateUtil.changeFormat(preDay, dateTimeFormat, dateFormat)
    val endDs = DateUtil.changeFormat(endTime, dateTimeFormat, dateFormat)

    import spark.implicits._

    val typeFunc = udf(keyMetricUtil.getEventType)

    val filteredDF = dataClean(spark, keyMetricUtil, exclusionConfig, ds, startDs, endDs, startTime, endTime, preDay, endTime, correctionConfig)

    var monitorDF = filteredDF
      .select(col("instance_id"), col("exception_name"), col("exception_time"),
        col("monitor_type"), col("monitor_level"), col("status"), col("warning_value"),
        col("nc_ip"), col("nc_id"), col("sn"), col("ali_uid"), col("additional_info"), col("is_local_disk"), col("warning_level"))
      .as[MonitorWithWarning]
      .flatMap(monitor => {
        if (monitor.monitor_type.equals("unavailable")) {
          // 需要将一些性能有损的异常转为不可用异常
          keyMetricUtil.unavailableMonitorRefactor(monitor.exception_name, monitor.exception_time, monitor.warning_value)
            .map(row => (monitor.instance_id, row._1, row._2, monitor.monitor_type, monitor.monitor_level, monitor.status, monitor.warning_value, monitor.nc_ip, monitor.nc_id, monitor.sn, monitor.ali_uid, monitor.additional_info, monitor.is_local_disk, monitor.warning_level))
        } else {
          Seq((monitor.instance_id, monitor.exception_name, monitor.exception_time, monitor.monitor_type, monitor.monitor_level, monitor.status, monitor.warning_value, monitor.nc_ip, monitor.nc_id, monitor.sn, monitor.ali_uid, monitor.additional_info, monitor.is_local_disk, monitor.warning_level))
        }
      }).toDF("instance_id", "exception_name", "exception_time", "monitor_type", "monitor_level", "status", "warning_value", "nc_ip", "nc_id", "sn", "ali_uid", "additional_info", "is_local_disk", "warning_level")
      .as[MonitorWithWarning]

    monitorDF.cache()

    // end time, 当日 0 点
    val endTimeStart: String = DateUtil.changeFormat(ds, DateUtil.dateFormat, DateUtil.dateTimeFormat)

    // 获取本地盘发送redeploy事件的数据，过滤发送事件以后的异常数据，发送redeploy的都是本地盘
    val eventSQL = s"SELECT vm_name, min(publish_time) as publish_time FROM ecs_dw.vm_ops_event WHERE dt = '$ds' AND ops_code LIKE '%Redeploy%' AND publish_time < '$endTime' AND event_status IN ('Executing', 'Inquiring', 'Scheduled') GROUP BY vm_name"

    // 获取所有 Redeploy 事件：未完成 & 已完成的
    // 1. 在 endTimeStart 之后闭合的 Redeploy 事件
    // 2. 所有未闭合的 Redeploy 事件
    // 3. 最早的 Redeploy 事件 和 endTimeStart 之后发布的 Redeploy 事件
    val redeploySQL = "WITH filtered_base AS (SELECT vm_name, publish_time, end_time, MIN(publish_time) OVER (PARTITION BY vm_name) min_pub FROM ecs_dw.vm_ops_event " +
      s" WHERE dt = '$ds' AND ops_code LIKE '%Redeploy%' AND (end_time IS NULL OR end_time > '$endTimeStart') AND vm_name IS NOT NULL )" +
      s" SELECT vm_name, publish_time, end_time FROM filtered_base WHERE end_time IS NOT NULL OR publish_time = min_pub OR publish_time > '$endTimeStart' GROUP BY vm_name, publish_time, end_time ORDER BY publish_time DESC"

    var redeployRecordDF = spark.sql(redeploySQL)
    redeployRecordDF = redeployRecordDF.select("vm_name", "publish_time", "end_time")

    // Redeploy map is a Map<String, List<Timestamp, Timestamp>>, key: vm_name, value: list of Pair(publish_time, end_time)
    val redeployRecordMap = redeployRecordDF.groupByKey(_.getString(0)).mapGroups((key, iter) => {
      val records = iter.toList
      val result = ListBuffer[(Timestamp, Timestamp)]()
      for (record <- records) {
        var endTime = DateUtil.getCurrentTimestamp()
        if (record.getTimestamp(2) != null) {
          endTime = record.getTimestamp(2)
        }
        result.append((record.getTimestamp(1), endTime))
      }
      (key, result)
    }).collect().toMap[String, ListBuffer[(Timestamp, Timestamp)]]

    println("Redeploy Record Map:")
    println(redeployRecordMap)

    var eventDF = spark.sql(eventSQL)

    // vm_info_for_key_metric包含实例信息以及运行时长
    val vmInfoTable = "vm_info_for_key_metric"
    // 过滤已经在ds之前释放掉的机器,is_online=True or running_time > 0 用来保留那些多次被捞回来的实例
    val vmInfoFilter = exclusionConfig.unequalExpression.replaceAll("region", "region_name")
    val vmInfoSQL = "select instance_id,ali_uid,cluster_alias,az_name as iz,instance_type_family,region_name as region,is_local_disk,running_time,ds, nc_ip, nc_id, serial_number as sn " +
      s"from $vmInfoTable where ds = '$ds' and (REPLACE(SUBSTR(gmt_release, 1, 10), '-', '') >= '$ds' OR gmt_release is NULL or is_online=True or running_time > 0) " +
      s"and ( instance_id is not null and instance_id <> '' )  and $vmInfoFilter "

    // 将实例不可用时长df和实例运行时长df join，使用Seq参数可以避免相同列名join时歧义的问题
    val joinAttributes = Seq("instance_id", "ds")

    // 根据vm name过滤掉一些实例，这些实例的key metric很大
    val instanceIdFilter = (col("instance_id").startsWith("i-") && !col("instance_id").startsWith("i-auto")) || col("instance_id").startsWith("hbm-") ||
      col("instance_id").startsWith("AY") || col("instance_id").startsWith("eci-") ||
      col("instance_id").startsWith("cp-") || col("instance_id").startsWith("ic-") || col("instance_id").startsWith("ay") || col("instance_id").startsWith("acs-") || col("instance_id").startsWith("sys-")

    val vmInfoDF = spark.sql(vmInfoSQL).filter(instanceIdFilter)

    // Redeploy 事件 和 key_customer_event_generate 作为本地盘结束事件
    eventDF = monitorDF
      .filter(col("exception_name") === "key_customer_event_generate" && col("is_local_disk") === "True")
      .withColumnRenamed("instance_id", "vm_name")
      .groupBy("vm_name")
      .agg(min("exception_time").alias("publish_time"))
      .union(eventDF)
      .dropDuplicates("vm_name")

    // 对于本地盘，把发送redeploy事件之后的数据全都过滤掉，这样子会把一些闭合事件给过滤掉，所以应该把不可用的开始事件过滤掉，不过滤结束事件，同时过滤掉性能受损和控制面的事件
    monitorDF = monitorDF
      .withColumn("event_type", typeFunc(col("exception_name")))
      .join(eventDF, monitorDF("instance_id") === eventDF("vm_name"), "left")
      .filter(col("publish_time").isNull // 没有Redeploy事件的实例
        || col("exception_time") <= col("publish_time") // 异常事件在 Redeploy 之前的
        || col("monitor_type") === "unavailable" && col("event_type") === "end" // 不可用的结束事件
        || col("exception_name") === "user_accept_event_to_avoid_issue_start") //
      .select("instance_id", "exception_name", "exception_time", "monitor_type", "monitor_level", "status", "warning_value", "nc_ip", "nc_id", "sn", "ali_uid", "additional_info", "is_local_disk", "warning_level")
      .as[MonitorWithWarning]

    def inTimeRangeList(time: Timestamp, timeList: ListBuffer[(Timestamp, Timestamp)]): Boolean = {
      if (timeList.isEmpty) return false
      for (tuple <- timeList if tuple != null && tuple._1 != null && tuple._2 != null) {
        if (time == null) {
          println("time is null")
          return false
        }
        if (time.after(tuple._1) && time.before(tuple._2)) {
          return true
        }
      }
      false
    }

    // 更新 monitorDF ， 对于其中的 vm_name，如果在 redeployMap 中，且 exception_time < publish_time, 则过滤掉，且添加一条结束事件，时间为 publish_time
    monitorDF = monitorDF.flatMap(monitor => {
      if (redeployRecordMap.contains(monitor.instance_id)) {
        val timeList = redeployRecordMap(monitor.instance_id)
        val exceptionTime = Timestamp.valueOf(monitor.exception_time)
        if (exceptionTime == null)
          println("exceptionTime is null. monitor.exception_time: ", monitor.exception_time)
        else
          println("exceptionTime is not null. monitor.exception_time: ", monitor.exception_time)
        val eventType = monitorConfig.getEventType(monitor.exception_name, monitor.warning_level)
        if (eventType != "start" && eventType != "end" && inTimeRangeList(exceptionTime, timeList)) {
          Seq()
        } else {
          Seq(monitor)
        }
      }
      else {
        Seq(monitor)
      }
    }
    ).toDF("instance_id", "exception_name", "exception_time", "monitor_type", "monitor_level", "status", "warning_value", "nc_ip", "nc_id", "sn", "ali_uid", "additional_info", "is_local_disk", "warning_level").as[MonitorWithWarning]

    // 获取数据面的异常数据
    var unavailableRawDataDF = monitorDF
      .filter(col("monitor_type") === "unavailable")
      .groupByKey(_.instance_id)
      .flatMapGroups((key, iter) => {
        val records = iter.toList
        val exceptionList = records.map(record => (record.exception_name, record.exception_time))
        val tuples = keyMetricUtil.listUnavailableEventWithoutCoverage(startTime = startTime, endTime = endTime, exceptionList)
        val exceptionMap = scala.collection.mutable.Map[String, ListBuffer[MonitorWithWarning]]()
        for (record <- records) {
          if (exceptionMap.contains(record.exception_name)) {
            exceptionMap(record.exception_name).append(record)
          } else {
            exceptionMap.put(record.exception_name, ListBuffer(record))
          }
        }
        for ((k, v) <- exceptionMap) {
          val sortedV = v.sortBy(_.exception_time)
          exceptionMap.put(k, sortedV)
        }
        val result = ListBuffer[(String, String, String, Int, String, String, Double, String, String, String, String, String)]()
        for (tuple <- tuples) {
          val startTime = tuple._2
          val list = exceptionMap(tuple._1)
          var target = list.head
          breakable {
            for (i <- list.indices) {
              if (list(i).exception_time > startTime) {
                if (i > 0) {
                  target = list(i - 1)
                }
                break
              }
            }
          }
          result.append((key, tuple._1, target.monitor_type, target.monitor_level, tuple._2, tuple._3, tuple._4, target.nc_ip, target.nc_id, target.sn, target.ali_uid, target.is_local_disk))
        }
        result
      }).toDF("instance_id", "exception_name", "monitor_type", "monitor_level", "start_time", "end_time", "duration", "nc_ip", "nc_id", "sn", "ali_uid", "is_local_disk")

    val filterDf = unavailableRawDataDF
      .filter(col("exception_name") === "vm_panic_event_start")
      .groupBy("instance_id", "exception_name")
      .agg(count("start_time").as("cnt"))
      .withColumnRenamed("instance_id", "vm_name")
      .withColumnRenamed("exception_name", "monitor_name")

    unavailableRawDataDF = unavailableRawDataDF
      .join(filterDf, unavailableRawDataDF("instance_id") === filterDf("vm_name") && unavailableRawDataDF("exception_name") === filterDf("monitor_name"), "left")
      .filter(col("cnt").isNull || col("cnt") < 2)
      .select("instance_id", "exception_name", "monitor_type", "monitor_level", "start_time", "end_time", "duration", "nc_ip", "nc_id", "sn", "ali_uid", "is_local_disk")


    val unavailableLocalRawDataDF = unavailableRawDataDF.filter(col("is_local_disk") === "True").flatMap(
      row => {
        val timeList = redeployRecordMap.getOrElse(row.getString(0), ListBuffer[(Timestamp, Timestamp)]())
        if (timeList.isEmpty) {
          Seq(UnavailableRecord(
            row.getString(0),
            row.getString(1),
            row.getString(2),
            row.getInt(3),
            row.getString(4),
            row.getString(5),
            row.getDouble(6),
            row.getString(7),
            row.getString(8),
            row.getString(9),
            row.getString(10),
            row.getString(11)
          ))
        } else {
          val startTime = Timestamp.valueOf(row.getString(4))
          val endTime = Timestamp.valueOf(row.getString(5))
          val splitedList = keyMetricUtil.splitTimeRange(startTime, endTime, timeList.toList)
          splitedList.map(
            timeRange => UnavailableRecord(
              row.getString(0),
              row.getString(1),
              row.getString(2),
              row.getInt(3),
              DateUtil.formatTimestamp(timeRange._1, DateUtil.dateTimeFormat),
              DateUtil.formatTimestamp(timeRange._2, DateUtil.dateTimeFormat),
              (timeRange._2.getTime - timeRange._1.getTime) / 1000L,
              row.getString(7),
              row.getString(8),
              row.getString(9),
              row.getString(10),
              row.getString(11)
            )
          )
        }
      }
    )(Encoders.product[UnavailableRecord]).toDF("instance_id", "exception_name", "monitor_type", "monitor_level", "start_time", "end_time", "duration", "nc_ip", "nc_id", "sn", "ali_uid", "is_local_disk")

    unavailableRawDataDF = unavailableRawDataDF.filter(col("is_local_disk") === "False").union(unavailableLocalRawDataDF)


    val getEventTypeUDF = udf((exceptionName: String, warningLevel: String) => monitorConfig.getEventType(exceptionName, warningLevel))
    // 计算实例的性能受损时长以及控制面异常时长
    var temporaryMonitorDF = monitorDF
      .filter(col("exception_time") >= startTime && (col("monitor_type") === "performance" || col("monitor_type") === "control"))
      .filter(getEventTypeUDF(col("exception_name"), col("warning_level")) =!= lit("start") && getEventTypeUDF(col("exception_name"), col("warning_level")) =!= lit("end"))
      .withColumn("minute", col("exception_time").substr(0, 16))


    val monitorCorrespondingEventsSeq = monitorConfig.getPerformanceCorrespondingUnavailableEvents.toSeq

    // 补齐结束事件
    val endEventMonitorDF = monitorDF.filter(col("monitor_type") === "unavailable" && col("exception_name").isin(monitorCorrespondingEventsSeq: _*))
    // 处理有开始结束的异常逻辑

    var startEndMonitorDF = monitorDF
      .filter(col("monitor_type") === "performance" || col("monitor_type") === "control")
      .filter(getEventTypeUDF(col("exception_name"), col("warning_level")) === lit("start") || getEventTypeUDF(col("exception_name"), col("warning_level")) === lit("end"))
      .union(endEventMonitorDF)
      .groupByKey(_.instance_id)
      .flatMapGroups((key, iter) => {
        val records = iter.toList
        val exceptionList = records.map(record => (record.exception_name, record.exception_time, record.warning_level))
        val tuples = keyMetricUtil.listPerformanceEventWithoutCoverage(startTime = startTime, endTime = endTime, exceptionList)
        val exceptionMap = scala.collection.mutable.Map[String, ListBuffer[MonitorWithWarning]]()
        for (record <- records) {
          if (exceptionMap.contains(record.exception_name)) {
            exceptionMap(record.exception_name).append(record)
          } else {
            exceptionMap.put(record.exception_name, ListBuffer(record))
          }
        }
        for ((k, v) <- exceptionMap) {
          val sortedV = v.sortBy(_.exception_time)
          exceptionMap.put(k, sortedV)
        }

        val result = ListBuffer[(String, String, Double, String, Int, String, String, String, String, String)]()

        for (tuple <- tuples) {
          val startTime = tuple._2
          val list = exceptionMap(tuple._1)
          var target = list.head
          breakable {
            for (i <- list.indices) {
              if (list(i).exception_time > startTime) {
                if (i > 0) {
                  target = list(i - 1)
                }
                break
              }
            }
          }
          result.append((key, tuple._1, tuple._4, target.monitor_type, target.monitor_level, target.nc_ip, target.nc_id, target.sn, target.is_local_disk, target.ali_uid))
        }
        result
      }).toDF("instance_id", "exception_name", "duration", "monitor_type", "monitor_level", "nc_ip", "nc_id", "sn", "is_local_disk", "ali_uid")

    println("startEndMonitorDF:", startEndMonitorDF.count())

    // 如果是不预留rlock的集群，则不可用时长最高不超过10分钟
    val correctThreshold = 600
    val qiNiuUid = "1397400369921234"

    val clusterList = correctionConfig.getCorrectionTarget("cluster", 0)
    val correctionValue = correctionConfig.getCorrectionValue("cluster", 0)
    val clusterJudge = (cluster: String, exceptionName: String, uid: String, localDisk: String) => {
      (exceptionName.equals("nc_down_alert") || exceptionName.equals("cloudops_reboot_vm_event")) &&
        (clusterList.contains(cluster) || uid != null && localDisk != null && uid.equals(qiNiuUid) && localDisk.equals("True"))
    }
    val clusterJudgeFunc = udf(clusterJudge)

    val vmClusterDF = vmInfoDF.select("instance_id", "cluster_alias")

    unavailableRawDataDF = unavailableRawDataDF
      .join(vmClusterDF, Seq("instance_id"), "left")
      .withColumn("needCorrection", clusterJudgeFunc(col("cluster_alias"), col("exception_name"), col("ali_uid"), col("is_local_disk")))
      .withColumn("duration", when(col("needCorrection") && col("duration") > correctThreshold, correctionValue).otherwise(col("duration")))

    if (originalTable.nonEmpty) {
      // 聚合数据面的异常时长
      val unavailableMonitorDF = unavailableRawDataDF
        .groupBy("instance_id", "monitor_type")
        .agg(sum("duration").as("abnormal_time"))
        .withColumn("abnormal_time", col("abnormal_time") / 60.0)

      val damagedMonitorDF = temporaryMonitorDF
        .groupBy("instance_id", "monitor_type")
        .agg(countDistinct("minute"))

      // 将不可用时长，性能受损时长和控制面异常时长的记录reduce到一条记录中
      val instanceDamagedDF = unavailableMonitorDF
        .union(damagedMonitorDF)
        .as[Record]
        .groupByKey(_.instance_id)
        .flatMapGroups((_, iter) => {
          val records = iter.toList
          var unavailableTime = 0.0
          var performanceTime = 0.0
          var controlTime = 0.0
          for (record <- records) {
            if (record.monitor_type.equals("unavailable")) {
              unavailableTime = record.abnormal_time
            } else if (record.monitor_type.equals("performance")) {
              performanceTime = record.abnormal_time
            } else if (record.monitor_type.equals("control")) {
              controlTime = record.abnormal_time
            }
          }
          val record = records.head
          List((record.instance_id, unavailableTime, performanceTime, controlTime))
        }).toDF("instance_id", "unavailable_time", "performance_time", "control_time")
        .withColumn("ds", lit(ds))

      // 获取受损实例的不可用时长，性能受损时长，控制面异常时长和实例数量
      val totalAbnormalTime = instanceDamagedDF.select(sum("unavailable_time"), sum("performance_time"),
        sum("control_time"), count("instance_id")).head()

      // 用于填充缺失值
      val fillMap = Map("running_time" -> 1440, "unavailable_time" -> 0.0, "performance_time" -> 0.0, "control_time" -> 0.0)
      val instanceKeyMetricDF = instanceDamagedDF
        .join(vmInfoDF, joinAttributes, joinType = "full") // unavailableMonitorDF("instanceId") === vmLifeDF("instance_id")
        .na.fill(fillMap)
        .withColumn("running_time", when(col("running_time") < col("unavailable_time"), col("running_time") + col("unavailable_time"))
          .when(col("running_time") < col("control_time"), col("control_time")).otherwise(col("running_time")))
        .withColumn("running_time", when(col("running_time") > 1440, 1440).otherwise(col("running_time")).cast(LongType))

      val totalVmLifeTime = instanceKeyMetricDF.select(count("instance_id"), sum("running_time")).head()

      val data = Seq(
        (totalAbnormalTime.getDouble(0), totalAbnormalTime.getDouble(1), totalAbnormalTime.getDouble(2), totalAbnormalTime.getLong(3),
          totalVmLifeTime.getLong(0), totalVmLifeTime.getLong(1), ds)
      )
      val dailyKeyMetricDF = spark.createDataFrame(data).toDF("unavailable_time", "performance_time", "control_time",
        "damaged_vm", "total_vm", "running_time", "ds")

      // 写入每个实例的key metric的数据
      instanceKeyMetricDF.select("instance_id", "unavailable_time", "performance_time", "control_time", "ali_uid", "cluster_alias",
          "iz", "instance_type_family", "region", "is_local_disk", "running_time", "nc_ip", "nc_id", "sn", "ds")
        .write
        .mode("overwrite")
        .insertInto(originalTable)

      // 写入全网的聚合key metric统计数据
      dailyKeyMetricDF.select("unavailable_time", "performance_time", "control_time",
          "damaged_vm", "total_vm", "running_time", "ds")
        .write
        .mode("overwrite")
        .insertInto(dailyTable)
    }

    if (unavailableTable.nonEmpty) {
      // 写入不可用异常数据
      unavailableRawDataDF
        .withColumn("ds", lit(ds))
        .select("instance_id", "exception_name", "start_time", "end_time", "duration", "monitor_type", "monitor_level", "nc_ip", "nc_id", "sn", "is_local_disk", "ali_uid", "ds")
        .write
        .mode("overwrite")
        .insertInto(unavailableTable)

      // 将清洗后的数据写入odps
      filteredDF
        .filter(col("exception_time") >= startTime)
        .withColumn("ds", lit(ds))
        .withColumn("exception_time2", col("exception_time").substr(0, 16))
        .select("instance_id", "exception_name", "monitor_type", "monitor_level", "exception_time", "exception_time2", "warning_level", "reason", "additional_info",
          "extension", "status", "warning_value", "nc_ip", "nc_id", "sn", "ali_uid", "biz_status", "is_win", "is_local_disk",
          "cluster_alias", "region", "ds")
        .withColumn("biz_status", getEventTypeUDF(col("exception_name"), col("warning_level")))
        .write
        .mode("overwrite")
        .insertInto(cleanTable)
    }

    if (damageTable.nonEmpty) {
      // 获取性能和控制面的异常数据
      temporaryMonitorDF = temporaryMonitorDF
        .withColumn("exception_name", when(col("exception_name") === "process_oom_exception_in_band", "process_oom_exception").otherwise(col("exception_name"))) // 将带外和带内的process_oom整合成一个异常
        .groupBy("instance_id", "exception_name", "monitor_type", "monitor_level", "nc_ip", "nc_id", "sn", "ali_uid", "is_local_disk")
        .agg(countDistinct("minute").as("duration"))
        .withColumn("ds", lit(ds))

      val durationFineTuningFunc = udf(keyMetricUtil.durationFineTuning)

      startEndMonitorDF = startEndMonitorDF
        .groupBy("instance_id", "exception_name", "monitor_type", "monitor_level", "nc_ip", "nc_id", "sn", "ali_uid", "is_local_disk")
        .agg(sum("duration").as("duration"))
        .withColumn("ds", lit(ds))
      println("Write stage startEndMonitorDF:", startEndMonitorDF.count())

      println("Write stage temporaryMonitorDF:", temporaryMonitorDF.count())
      val damagedRawDataDF = temporaryMonitorDF

      // 写入性能受损和控制面异常数据，时长单位为秒
      damagedRawDataDF
        .withColumn("duration", durationFineTuningFunc(col("exception_name"), col("duration")))
        .select("instance_id", "exception_name", "duration", "monitor_type", "monitor_level", "nc_ip", "nc_id", "sn", "is_local_disk", "ali_uid", "ds")
        .union(startEndMonitorDF.select("instance_id", "exception_name", "duration", "monitor_type", "monitor_level", "nc_ip", "nc_id", "sn", "is_local_disk", "ali_uid", "ds"))
        .write
        .mode("overwrite")
        .insertInto(damageTable)

      println("Write stage UnionDF:", damagedRawDataDF
        .withColumn("duration", durationFineTuningFunc(col("exception_name"), col("duration")))
        .select("instance_id", "exception_name", "duration", "monitor_type", "monitor_level", "nc_ip", "nc_id", "sn", "is_local_disk", "ali_uid", "ds")
        .union(startEndMonitorDF)
        .count())

    }

    if (unexpectedTable.nonEmpty) {
      val exceptionName = "nc_down_alert"
      // 获取非预期宕机的数据
      val unexpectedEventDF = monitorDF.filter(col("exception_name") === exceptionName && !col("additional_info").like("%active%") || col("exception_name") === "vm_down_end")
        .filter(col("exception_time") >= startTime)
        .groupByKey(_.instance_id)
        .flatMapGroups((key, iter) => {
          val data = iter.toList
          val head = data.head
          val sortedRecords = data.map(row => (monitorConfig.getEventType(row.exception_name), row.exception_time))
          keyMetricUtil.listExceptionInterval(exceptionName, sortedRecords, startTime, endTime, prePadding = false, postPadding = true)
            .map(row => (key, row._1, row._2, row._3, row._4, head.nc_ip, head.nc_id, head.sn))
        }).toDF("instance_id", "exception_name", "start_time", "end_time", "duration", "nc_ip", "nc_id", "sn")

      // 写入非预期宕机的数据
      unexpectedEventDF.withColumn("ds", lit(ds))
        .write
        .mode("overwrite")
        .insertInto(unexpectedTable)
    }
  }

  def dataClean(spark: SparkSession, keyMetricUtil: KeyMetricUtil, exclusionConfig: MonitorExclusionConfig, ds: String,
                startDs: String, endDs: String, startTime: String, endTime: String, preDay: String, endDay: String, correctionConfig: MetricCorrectionConfig): DataFrame = {
    // 全量表过滤 gamma 的机器，monitor_exception_sls_alert 的 is_gamma_machine 不准
    val gammaTable = "ecs_release_plan_gamma_host_v2"
    val gammaReleasedTable = "ecs_release_plan_gamma_host_his_v2"
    val gammaSQL =
      s"""
  SELECT nc_ip as ip
  FROM $gammaTable
  WHERE ds = $ds
  UNION
  SELECT nc_ip as ip
  FROM $gammaReleasedTable
  WHERE ds = $ds AND SUBSTRING(history_time, 1, 10) = date_format(to_date(CAST(ds AS STRING), 'yyyyMMdd'), 'yyyy-MM-dd')
"""

    //  过滤演练的机器，pet_drill_event_details 是全量表,只需要获取当天的数据即可
    val drillTable = "pet_drill_event_details"
    val drillSQL = s"select machine_id from $drillTable where ds='$ds' and start_time < '$endTime' and end_time > '$startTime'"

    // 过滤加黑的机器，blacklist_content 是一个全量表，获取当天的分区即可
    val blackListTable = "blacklist_content"
    val blackInstanceSQL = s"select distinct content from $blackListTable where ds='$ds' and start_due_time < '$endTime' " +
      s"and due_time > '$startTime' and metainfo_id IN (4,9, 1559, 6489, 469, 1654, 5735, 1094, 974, 12756) and locate('.', content) = 0"
    val blackNcSQL = s"select distinct content from $blackListTable where ds='$ds' and start_due_time < '$endTime' " +
      s"and due_time > '$startTime' and metainfo_id IN (1,2,3,4,5,6,7,9,10,6254,6255,6256,6257,6258,6494) and locate('.', content) > 0" // 删除 metainfo_id = 8 的条件，防止过滤掉真实用户
    val blackClusterSQL = s"select distinct content from $blackListTable where ds='$ds' and start_due_time < '$endTime' " +
      s"and due_time > '$startTime' and metainfo_id IN (1,2,3,4,5,6,7,8,9,10,6254,6255,6256,6257,6258,6494) and locate('.', content) = 0"

    //  过滤一些测试的ali uid和特殊的集群
    val monitorFilter = exclusionConfig.unequalExpression.replaceAll("ali_uid", "aliuid")

    // 异常表的表名
    val monitorTable = "monitor_exception_sls_alert"

    val tagTable = "xdc_tag"
    val tagSQL = s"select target_name from $tagTable where ds = '$ds' and tag_name = 'LLCSensitiveUser' and target_name = monitor_exception_sls_alert.aliuid "

    val subQuery = s"SELECT distinct ncip as nc_ip FROM (SELECT ncip, MAX(isfullnc) as isfull FROM $monitorTable  WHERE ds='$ds' " +
      s" AND (exceptionname = 'inspect_cpu_power_tdp_amd' and usercnt > 1 and cores <> 128 and cores <> 192 and cpu_generation = 'Icelake' or exceptionname = 'nc_cpu_socket_tdp_reached' and usercnt > 1 and cores <> 128 and cores <> 192)" +
      s" AND ncip is not null GROUP BY ncip HAVING isfull = 'False') as t "

    val monitorSQL = s"select distinct instanceid as instance_id,exceptionname as exception_name," +
      s"IF (exceptionname = 'vm_down_end' AND warninglevel = 'low_warning' AND gmtsync IS NOT NULL , gmtsync, exceptiontime) as exception_time," +
      s"warninglevel as warning_level,reason,additionalinfo as additional_info,extension,status,warningvalue as warning_value," +
      s"ncip as nc_ip,nc_id,sn, aliuid as ali_uid, biz_status, iswin as is_win, islocaldisk as is_local_disk, instancetype as instance_type, " +
      s"instancetypefamily as instance_type_family, vcpu_mod, cluster_alias as cluster_alias2, " +
      s"zonegroupflag as zone_group_flag, istestaccount as is_test_account, ds from $monitorTable where ds>='$startDs' and ds<='$endDs' " +
      s"and exceptiontime >= '$preDay' and exceptiontime <= '$endDay' " +
      s"and cluster_usage <> '测试' and zonegroupflag <> 'biz_cloudbox' and ( is_gamma_machine is null or is_gamma_machine = 0) " +
      s"and instanceid is not null and $monitorFilter and ncip not in ($blackNcSQL) and cluster_alias not in ($blackClusterSQL) and instanceid not in ($blackInstanceSQL) " +
      s"and (instancetype not LIKE 'ecs.g5ne%'  or aliUid <> ****************) " +
      s"and (exceptionname <> 'dpdkavs_cpu_high_multi_user' or islocaldisk = 'False') " +
      s"and instancetypefamily not in ('ecs.ebmgn7e', 'ecs.gn7e', 'ecs.sccgn7ex', 'ecs.ebmgn7ex', 'ecs.ebmgn7vx', 'ecs.f1') " + // A100 gpu机器目前没有迁移能力，先过滤
      s"and (exceptionname <> 'vm_crash_event' or aliuid <> '****************') " + // 过滤用户****************测试造成的vm_crash
      s"and ((exceptionname <> 'inspect_cpu_power_tdp_amd' or usercnt > 1 and cores <> 128 and cores <> 192 and cpu_generation = 'Icelake') or (exceptionname <> 'nc_cpu_socket_tdp_reached' or usercnt > 1 and cores <> 128 and cores <> 192)) " + //功耗打满特殊场景
      s"and instancetype NOT LIKE 'ecs.lbm%' AND instancetype NOT LIKE 'ecs.sbm%' " +
      s"and (exceptionname <> 'vm_vcpu_steal' or aliuid <> '1613503146525114' and (warninglevel = 'fatal' or warninglevel = 'critical' or warninglevel = 'normal' and vcpu_mod = 'exclusive' and warningvalue >= 15)) " + // vcpu争抢特殊场景
      s"AND NOT (exceptionname = 'vm_vcpu_steal' and aliuid = '1796589535993896') " + // 排除用户1796589535993896测试造成的vm_vcpu_steal
      s"and (exceptionname <> 'vm_vcpu_freq_exception' or aliuid <> '1079135428626537') " + // 排除 vm_vcpu_freq_exception 下用户 1079135428626537
      s"and (exceptionname <> 'nc_dpdk_driver_flow_drop' or usercnt>1) " + // 排除nc_dpdk_driver_flow_drop单租户的场景
      s"and (instanceid <> 'i-2ze3u7y9mplnf80yj35n' ) " + // 过滤掉一个测试实例
      s"and (instanceid <> 'i-t4n4fbrvq87ljiiyshun' ) " + // 过滤连接到机尾故障集群AY287T
      s"and (instanceid <> 'i-gw85s45i04i6q8yzd07f' ) " + // kms 密钥失效永远无法启动 https://project.aone.alibaba-inc.com/v2/project/1182328/bug/65116371
      s"and (instanceid <> 'i-0jla07ygff5hbzubihq1' ) " + // vsock_ping一直不通
      s"and (instanceid <> 'i-2ze33ll5faocuup38q79' ) " + // vsock_ping一直不通, https://project.aone.alibaba-inc.com/v2/project/1182328/bug/65906238
      s"AND NOT (exceptionname = 'dpdkavs_cpu_high_multi_user' and instancetypefamily IN ('ecs.e', 'ecs.t5', 'ecs.t6', 'ecs.s6', 'ecs.xn4', 'ecs.mn4', 'ecs.e4')) " + // 排除：dpdkavs_cpu_high_multi_user, ecs.e, ecs.t5, ecs.t6, ecs.s6, ecs.xn4, ecs.mn4, ecs.e4
      s"AND NOT (exceptionname in ('gpu_inspection_fatal_error_report', 'physical_gpu_firmware_boot_fail', 'gpu_lostcard_physical', 'physical_gpu_ecc_error', 'gpu_check_service_vm_fabric_manager', 'gpu_inspection_status', 'gpu_local_inspection_status', 'gpu_guestos_diagnose_status', 'service_vm_rm_init_adapter_fail') and physical_model not like 'G%' and instancetypefamily not like 'ecs.ebmgn%') " + // 过滤不为 GPU 实例的 GPU 异常
      s"AND NOT (exceptionname = 'vm_paused_exception' AND aliuid = '1862103444230781') " + // 1862103444230781 是无影账号（金融云主账号） vm_paused_exception warningLevel: critical  需要过滤掉无影账号
      s"and (exceptionname <> 'vm_iohang_start' or storage_type <> 'io9') " + // 过滤掉 vm_iohang_start 且 storage_type 为 io9 的异常
      s"AND NOT (instanceid NOT LIKE 'sys-%' AND exceptionname LIKE 'sysvm_%') " + // 去掉 sysvm_ 异常在非 sys- 开头的实例上的记录
      s"and (exceptionname <> 'vm_llc_inhibition' or aliuid is not null and exists ($tagSQL)) " // 目前只关注llc敏感客户的llc抑制异常
    println(monitorSQL)

    // 根据vm name过滤掉一些实例，这些实例的key metric很大
    val instanceIdFilter = (col("instance_id").startsWith("i-") && !col("instance_id").startsWith("i-auto")) || col("instance_id").startsWith("hbm-") ||
      col("instance_id").startsWith("AY") || col("instance_id").startsWith("eci-") ||
      col("instance_id").startsWith("cp-") || col("instance_id").startsWith("ic-") || col("instance_id").startsWith("ay") || col("instance_id").startsWith("acs-") || col("instance_id").startsWith("sys-")

    // 分类和分等级的udf
    val classifyFunc = udf(keyMetricUtil.classify)
    val gradeFunc = udf(keyMetricUtil.grade)
    val rename = udf(keyMetricUtil.rename)

    val drillDF = spark.sql(drillSQL)

    val gammaDF = spark.sql(gammaSQL)

    val rawDF = spark.sql(monitorSQL)

    val clusterList = correctionConfig.getCorrectionTarget("cluster", 0)

    val exclusionFilter: (String, String, String, String, String, String, String) => Boolean = (exception: String, exceptionTime: String, instance: String, aliUid: String, cluster: String, region: String, instanceType: String) => {
      !(exception.equals("vm_panic_event") && keyMetricUtil.instanceSet.contains(instance)) &&
        !exclusionConfig.getAliUidList.contains(aliUid) && !exclusionConfig.getClusterList.contains(cluster) &&
        !exclusionConfig.getRegionList.contains(region) &&
        (instanceType == null || aliUid == null || !(instanceType.startsWith("ecs.g5ne") && aliUid.equals("****************"))) &&
        !(exception.equals("nc_down_alert") && exceptionTime < startTime && clusterList.contains(cluster))
    }
    val exclusionFunc = udf(exclusionFilter)

    // 将实例不可用时长df和实例运行时长df join，使用Seq参数可以避免相同列名join时歧义的问题
    val joinAttributes = Seq("instance_id", "ds")
    // vm_info_for_key_metric包含实例信息以及运行时长
    val vmInfoTable = "vm_info_for_key_metric"
    // 用于帮忙解决原始数据中存在ali_uid或者cluster字段缺失导致的脏数据未过滤的问题
    val backVmInfoSQL = s"select instance_id,ali_uid as ali_uid2,cluster_alias,region_name as region,ds, az_name from $vmInfoTable " +
      s"where ds between '$startDs' and '$ds' and instance_id is not null"
    val backVmInfoDF = spark.sql(backVmInfoSQL)

    // TODO: 新加坡临时加黑
    val excludedAzNames = Seq("ap-southeast-x3") // # 2025.2.19: 新加坡x3环境，910的毒☠️

    val filteredDF = rawDF
      .join(drillDF, rawDF("nc_ip") === drillDF("machine_id") || rawDF("instance_id") === drillDF("machine_id"), "left")
      .join(gammaDF, rawDF("nc_ip") === gammaDF("ip"), "left")
      .filter(col("machine_id").isNull && col("ip").isNull)
      .join(backVmInfoDF, joinAttributes, "left")
      .filter(!col("az_name").isin(excludedAzNames: _*)) // TODO: 新加坡临时加黑
      .drop("az_name")
      // TODO: 新加坡临时加黑
      .withColumn("ali_uid", when(col("ali_uid").isNull || col("ali_uid") === '0', col("ali_uid2")).otherwise(col("ali_uid")))
      .withColumn("cluster_alias", when(col("cluster_alias2").isNull, col("cluster_alias")).otherwise(col("cluster_alias2")))
      .withColumn("udf_args", map(
        lit("exception_name"), col("exception_name"),
        lit("warning_level"), col("warning_level"),
        lit("reason"), col("reason"),
        lit("additional_info"), col("additional_info"),
        lit("extension"), col("extension"),
        lit("warning_value"), col("warning_value"),
        lit("biz_status"), col("biz_status"),
        lit("is_win"), col("is_win"),
        lit("status"), col("status"),
        lit("instance_type_family"), col("instance_type_family"),
        lit("vcpu_mod"), col("vcpu_mod"),
        lit("zone_group_flag"), col("zone_group_flag"),
        lit("is_test_account"), col("is_test_account"),
        lit("ali_uid"), col("ali_uid")
      ))
      .withColumn("monitor_type", classifyFunc(col("udf_args")))
      .filter(col("monitor_type") =!= "others" && instanceIdFilter && exclusionFunc(col("exception_name"), col("exception_time"), col("instance_id"), col("ali_uid2"), col("cluster_alias"), col("region"), col("instance_type")))
      .withColumn("monitor_level", gradeFunc(col("udf_args")))
      .withColumn("exception_time", date_format(col("exception_time"), format = dateTimeFormat))
      .withColumn("exception_name", rename(col("udf_args")))
      .filter(col("monitor_type") === "unavailable" || (col("monitor_type") === "performance" && col("status") =!= "Shutted" || col("monitor_type") === "control") && col("status") =!= "Destroyed")

    val tdpDF = spark.sql(subQuery)
    val tdpExceptionDF = filteredDF
      .filter(col("exception_name") === "nc_cpu_socket_tdp_reached" || col("exception_name") === "inspect_cpu_power_tdp_amd")
      .join(tdpDF, Seq("nc_ip"), "inner")

    val cleanDF = filteredDF
      .filter(col("exception_name") =!= "nc_cpu_socket_tdp_reached" && col("exception_name") =!= "inspect_cpu_power_tdp_amd")
      .unionByName(tdpExceptionDF)
    cleanDF
  }
}
