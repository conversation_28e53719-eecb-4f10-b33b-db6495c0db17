package com.aliyun.xdragon.metric.spark.util

import com.aliyun.xdragon.metric.spark.model.FeatureSkipList

import scala.collection.JavaConverters._

/**
 * <AUTHOR>
 * @date 2024/12/26
 */
class RuleOptimizer(val rule: String) extends Serializable {

  private var featureSkipList: FeatureSkipList = _
  private var exclusions: List[String] = _
  private var keyTerms: List[List[String]] = _

  def this(rule: String, features: List[String], exclusion: List[String], condition: List[String], allFeatureSet: Set[String]) = {
    this(rule)
    this.featureSkipList = new FeatureSkipList(features.asJava)
    this.exclusions = createExclusion(exclusion, condition, allFeatureSet)
    this.keyTerms = extractKeyTerms(exclusions)
  }

  def ruleMatch(dimension: Map[String, String], features: List[String]): List[String] = {
    var matchedFeatures = List[String]()
    // 如果满足排除条件，直接返回空列表
    if (exclusionMatch(dimension, features)) {
      return matchedFeatures
    }

    featureSkipList.reset()
    do {
      val candidate = featureSkipList.getCurrentFeature
      // 判断是否命中
      var matched = true
      candidate.split(",").foreach(feature => {
        if (!features.contains(feature)) {
          matched = false
        }
      })
      // 设置匹配结果
      featureSkipList.setMatch(matched)
      if (matched) {
        matchedFeatures ::= candidate
      }
    } while (featureSkipList.next())

    matchedFeatures
  }

  private def createExclusion(exclusion: List[String], condition: List[String], allFeatureSet: Set[String]): List[String] = {
    var result = List[String]()
    // 提取条件中的关键字
    val conditionKeys = extractKeyTerms(condition)
    for (i <- conditionKeys.indices) {
      // 条件中是否包含tag
      val tagExist = conditionKeys(i).exists(x => x.startsWith("tag."))
      // 条件中是否只包含特征
      val onlyFeatureExist = allFeatureSet.intersect(conditionKeys(i).toSet).size == conditionKeys(i).size
      if (!tagExist && !onlyFeatureExist) {
        // 如果条件中不包含Tag，且关键字并不都是特征，则取反后加入排除条件
        result ::= "not (" + condition(i) + ")"
      }
    }
    // 提取排除条件中的关键字
    val exclusionKeys = extractKeyTerms(exclusion)
    for (i <- exclusionKeys.indices) {
      // 条件中是否包含tag
      val tagExist = exclusionKeys(i).exists(x => x.startsWith("tag."))
      if (!tagExist) {
        // 如果条件中不包含tag，则直接加入排除条件
        result ::= exclusion(i)
      }
    }
    result
  }

  private def extractKeyTerms(exclusions: List[String]): List[List[String]] = {
    exclusions.map(exclusion => {
      val pattern = "\\{\\{.*?}}".r
      pattern.findAllIn(exclusion).map(s => s.substring(2, s.length - 2)).toList
    })
  }

  private def exclusionMatch(dimension: Map[String, String], features: List[String]): Boolean = {
    for (i <- keyTerms.indices) {
      var condition = exclusions(i)
      for (term <- keyTerms(i)) {
        // 根据 term 在 dimension 和 features 中的存在情况设置 value 值
        val value: String = dimension.get(term) match { // 尝试获取维度中的 term 对应值
          case Some(v) => filterIllegalCharacters(v) // 如果找到，则直接使用该值
          case None =>
            if (features.contains(term)) "true" // 检查是否在 features 中，是则设为 true
            else "false" // 否则设为 false
        }
        condition = condition.replace("{{" + term + "}}", value)
      }
      // 判断排除条件是否命中
      var conditionFlag = true
      try {
        conditionFlag = ExpressionParser.parseExpression(condition)
      } catch {
        // 如果表达式出错，说明机器维度存在脏字段，机器状态不正常，不做规则命中，直接排除
        case e: Throwable =>
          println("Expression error. Raw condition: " + exclusions(i) + s", Parsed condition: $condition. ${e.getMessage}")
      }
      if (conditionFlag) {
        return true
      }
    }
    false
  }

  /**
   * 过滤掉不合法字符
   *
   * @param term 需要过滤的字符串
   * @return 过滤后的字符串
   */
  private def filterIllegalCharacters(term: String): String = {
    term.trim().replace("'", "").replace("\"", "")
  }

}
