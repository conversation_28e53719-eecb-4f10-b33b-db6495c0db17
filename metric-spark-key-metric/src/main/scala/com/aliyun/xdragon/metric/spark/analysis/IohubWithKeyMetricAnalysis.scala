package com.aliyun.xdragon.metric.spark.analysis

import com.aliyun.xdragon.metric.spark.util.DateUtil
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions.{col, lit, sum, udf, when}

/**
 * <AUTHOR>
 * @note 对iohub升级前后的key metric进行分析，现在没用了
 */

object IohubWithKeyMetricAnalysis {

  val timeSuffix = " 00:00:00"

  def main(args: Array[String]): Unit = {
    val spark = SparkSession
      .builder()
      .appName("key metric of iohub upgrade ")
      .config("spark.sql.broadcastTimeout", 20 * 60)
      .config("spark.sql.crossJoin.enabled", value = true)
      .getOrCreate()

    val ds = args(0)

    val iohubTable = "xdragon_hot_upgrade_iohub_downtime"
    val iohubSQL = s"select * from $iohubTable where ds='$ds'"

    val nameFunc = udf((vmName: String)=>{
      vmName.split("\\.")(0)
    })

    val iohubDF = spark.sql(iohubSQL)
      .withColumn("vm_name", nameFunc(col("vm_name")))

    val currentDay = ds.substring(0, 4) + "-" + ds.substring(4, 6) + "-" + ds.substring(6, 8) + timeSuffix
    val preDs = DateUtil.modifyDate(currentDay, days = -1).substring(0, 10).replaceAll("-", "")
    val lastDs= DateUtil.modifyDate(currentDay, days = 1).substring(0, 10).replaceAll("-", "")
    val keyMetricTable = "ecs_original_key_metric"
    val keyMetricSQL = s"select instance_id,unavailable_time,performance_time,control_time,total_life_time,ds as dt from $keyMetricTable where " +
      s"ds>='$preDs' and ds<='$lastDs'"

    val keyMetricDF = spark.sql(keyMetricSQL)

    val joinExpression = iohubDF("vm_name") === keyMetricDF("instance_id")

    val joinedDF = keyMetricDF.join(iohubDF, joinExpression, "inner")
//      .withColumn("is_before", when(col("ds")>col("dt"), true).otherwise(false))
//      .groupBy("start_time", "old_version", "ip", "vm_name", "is_advance", "downtime")
//      .agg(sum("unavailable_time").as("unavailable_time"), sum("performance_time").as("performance_time"),
//        sum("control_time").as("control_time"), sum("total_life_time").as("total_life_time"))
      .select("start_time", "old_version", "ip", "vm_name", "is_advance", "downtime", "dt", "unavailable_time", "performance_time", "control_time", "total_life_time")
      .withColumn("ds", lit(ds))

    joinedDF.write
      .mode("overwrite")
      .insertInto("xdragon_hot_upgrade_iohub_key_metric")

  }

}
