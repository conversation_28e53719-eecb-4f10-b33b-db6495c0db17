package com.aliyun.xdragon.metric.spark.rules

import com.aliyun.xdragon.metric.spark.common.DateUtil
import com.aliyun.xdragon.metric.spark.rule.RuleActionConfig
import com.aliyun.xdragon.metric.spark.util.{RuleMatcher, RuleOptimizer}
import com.google.gson.Gson
import org.apache.spark.rdd.RDD
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Row, SparkSession}

/**
 * <AUTHOR>
 * @date 2024/12/18
 */
object RuleMatching {
  private val days: Int = 180

  def main(args: Array[String]): Unit = {
    // 初始化spark
    val spark = SparkSession
      .builder
      .appName("Rule optimization (Lingzun)")
      .config("spark.sql.broadcastTimeout", 20 * 60)
      .config("spark.sql.crossJoin.enabled", value = true)
      .getOrCreate()
    println("Spark environment is ready")
    // 构造优化器
    val currentDs = args(0)
    println("Current ds is " + currentDs)
    val ruleOptimizers = constructRuleOptimizers(spark, currentDs)
    if (ruleOptimizers.isEmpty) {
      return
    }
    // 构造关系
    val relationDf = constructRelations(spark)
    relationDf.cache()
    // 查询数据
    val dataDf = queryRuleMatchTable(spark, currentDs)
    val initialEventDf = queryOpsEventTable(spark, currentDs)
    // 整理事件
    val eventDf = processEvents(initialEventDf, relationDf)
    // 执行匹配
    val matchedDf = processMatch(ruleOptimizers, dataDf, relationDf)
    // 计算收益
    val resultDf = calculateBenefit(matchedDf, eventDf)
    // 写入结果
    val partition = s"ds='$currentDs', rule='nic_offline'"
    writeTable(spark, partition, resultDf)
    println("ResultDf is written")
    // 关闭spark
    spark.stop()
  }

  def constructRelations(spark: SparkSession): DataFrame = {
    // 读取配置
    val list: java.util.List[RuleActionConfig] = RuleActionConfig.load()
    var data: List[Row] = List()
    for (k <- 0 until list.size()) {
      val ruleActionConfig = list.get(k)
      for (i <- 0 until ruleActionConfig.getRules.size()) {
        for (j <- 0 until ruleActionConfig.getActions.size()) {
          data = data :+ Row(ruleActionConfig.getRules.get(i), ruleActionConfig.getActions.get(j), ruleActionConfig.getName)
        }
      }
    }
    // 构造广播的DataFrame
    val schema = StructType(Array(
      StructField("related_rule", StringType, nullable = true),
      StructField("related_action", StringType, nullable = true),
      StructField("type_name", StringType, nullable = true)
    ))
    val rdd: RDD[Row] = spark.sparkContext.parallelize(data)
    broadcast(spark.createDataFrame(rdd, schema))
  }

  def processEvents(eventDf: DataFrame, relateDf: DataFrame): DataFrame = {
    // 关联类型
    val withTypeDf = eventDf.join(relateDf, eventDf("action_code") === relateDf("related_action"), "inner")
    // 得到每一个类别每个目标每天最早的运维事件
    val daySpec = Window.partitionBy(col("resource_id"), col("ds"), col("type_name")).orderBy(col("ts"))
    val dailyEventDf = withTypeDf
      .withColumn("row_number", row_number().over(daySpec))
      .filter(col("row_number") === 1)
      .drop("row_number")

    // 扩展字段
    val jsonUdf = udf((s: String) => {
      // 提取alertRuleName。用Json提取遇到了一堆问题，所以直接用字符串提取
      val key = "alertRuleName\":\""
      val startId = s.indexOf(key) + key.length
      val endId = s.indexOf("\"", startId)
      s.substring(startId, endId)
    }: String)
    val extendUdf = udf((s: String) => {
      // 产生前一天、当天和后一天的ds，作为需要关注这个事件的日期
      Seq(DateUtil.modifyDateByDay(s, -1, "yyyyMMdd"), s, DateUtil.modifyDateByDay(s, 1, "yyyyMMdd"))
    }: Seq[String])
    val extendDf = dailyEventDf
      .withColumn("alertRuleName", jsonUdf(col("extend_data")))
      .withColumn("care_ds", extendUdf(col("ds")))
      .select(explode(col("care_ds")).as("care_ds"), col("event_id"), col("resource_type"), col("resource_id"),
        col("ts"), col("action_code"), col("alertRuleName"), col("type_name"), col("ds"))

    // 对每一个目标每一天，都看他前一天、当天和后一天，得到最早的运维事件
    val windowSpec = Window.partitionBy(col("resource_id"), col("care_ds")).orderBy(col("ds"))
    val resultDf = extendDf.withColumn("row_number", row_number().over(windowSpec))
      .filter(col("row_number") === 1)
      .drop("row_number").drop("ds")

    resultDf
  }

  private def constructRuleOptimizers(spark: SparkSession, ds: String): List[RuleOptimizer] = {
    // 查询特征定义
    val featureSet = queryFeatureSet(spark)
    // 查询规则定义
    val ruleDefMap = queryRuleDefMap(spark)
    // 查询候选的特征组合
    val candidateDf = queryFeatureCandidates(spark, ds)
    // 过滤并按规则分组
    val featureMap = filterCandidateMap(candidateDf, featureSet, ruleDefMap)
    val rules = featureMap.keys.toList
    // 构造优化器
    val commonExclusions = List("{{nc_offline_event}}", "{{upgrade_nc_down_active}}", "{{nc_down_active}}",
      "{{isNcOffline}}", "{{isGammaNC}}", "{{batch_ops_by_tuoluo}}", "{{old_arch_ops_by_tuoluo}}",
      "{{cost_ops_by_tuoluo}}", "{{batch_risk_ops_by_tuoluo}}", "{{nc_shutdown_ops}}", "{{offline_process_too_long}}",
      "'{{ddhId}}' != ''", "'{{bizStatus}}' == 'nc_down'")
    rules.filter({ rule => featureMap.contains(rule) && ruleDefMap.contains(rule) })
      .map({ rule => new RuleOptimizer(rule, featureMap(rule), ruleDefMap(rule)._2 ++ commonExclusions, ruleDefMap(rule)._1, featureSet) })
  }

  private def filterCandidateMap(candidates: DataFrame, featureSet: Set[String], ruleDefMap: Map[String, (List[String], List[String])]): Map[String, List[String]] = {
    // 构造匹配器
    val ruleMatchers = ruleDefMap.map(x => new RuleMatcher(x._1, x._2._1, featureSet)).filter(_.isLegal).toList
    println("Rule matchers are ready with " + ruleMatchers.length + " rules")
    // 注册过滤UDF（过滤已被任意规则包含的特征组合）
    val matchRuleUDF = udf((features: String) => {
      !ruleMatchers.exists(_.matchRule(features))
    })
    // 过滤
    val filteredCandidates = candidates.repartition(16).cache() // 强制使用16分区并行加速
      .filter(col("features").contains(",")) // 过滤掉单特征
      .filter(matchRuleUDF(col("features"))) // 过滤掉已被任意规则包含的特征组合
      .select(col("rule_name"), col("features"))
      .collect()
    println("There are " + filteredCandidates.length + " candidates after filtering")
    // 按规则分组
    val featureMap = filteredCandidates.groupBy(row => row.getString(0))
      .map(pair => (pair._1, pair._2.map(x => x.getString(1)).toList))
    featureMap.foreach(x => println((x._1, x._2.length)))
    featureMap
  }

  private def queryRuleDefMap(spark: SparkSession): Map[String, (List[String], List[String])] = {
    val gson = new Gson()
    val ruleDefinitionDf = queryOpsRuleTable(spark)
    val ruleDefMap = ruleDefinitionDf.collect().map { row => {
      val ruleName = row.getString(0)
      val conditions = row.getString(1)
      val exclusions = row.getString(2)
      val jsonElement1 = gson.fromJson(conditions, classOf[com.google.gson.JsonElement])
      val jsonElement2 = gson.fromJson(exclusions, classOf[com.google.gson.JsonElement])
      if (jsonElement1.isJsonArray && jsonElement2.isJsonArray) {
        val jsonArray1 = jsonElement1.getAsJsonArray
        val jsonArray2 = jsonElement2.getAsJsonArray
        (ruleName, ((0 until jsonArray1.size()).map(i => jsonArray1.get(i).getAsString).toList,
          (0 until jsonArray2.size()).map(i => jsonArray2.get(i).getAsString).toList))
      } else {
        throw new IllegalArgumentException("Input is not a valid JSON array")
      }
    }
    }.toMap
    ruleDefMap
  }

  private def ruleMatch(optimizers: List[RuleOptimizer], dimension: Map[String, String], features: List[String]): Seq[(String, String)] = {
    val matches = optimizers.map { optimizer =>
      (optimizer.rule, optimizer.ruleMatch(dimension, features))
    }

    matches.flatMap { case (rule, matchedFeatures) =>
      matchedFeatures.map(feature => (rule, feature))
    }
  }

  private def processMatch(optimizers: List[RuleOptimizer], dataDf: DataFrame, relateDf: DataFrame): DataFrame = {
    // 匹配规则
    val matchUdf = udf((dimension: Map[String, String], features: Seq[String]) => {
      ruleMatch(optimizers, dimension, features.toList)
    })
    val matchedDf = dataDf.withColumn("udfSeq", matchUdf(col("dimensions"), col("features")))
      .select(explode(col("udfSeq")).as("udfColumns"), col("matchingtime"), col("targetid"), col("targettype"), col("ds"))
      .select(col("udfColumns._1").as("detailrule"), col("udfColumns._2").as("features"), col("matchingtime"), col("targetid"), col("targettype"), col("ds"))
      .groupBy(col("features"), col("targetid"), col("targettype"), col("detailrule"), col("ds"))
      .agg(min(col("matchingtime")).as("matchingtime"))

    matchedDf.join(relateDf, matchedDf("detailrule") === relateDf("related_rule"), "inner")
      .select(col("features"), col("matchingtime"), col("targetid"), col("targettype"), col("detailrule"), col("type_name"), col("ds"))
  }

  private def calculateBenefit(matchedDf: DataFrame, eventDf: DataFrame): DataFrame = {
    // 关联运维事件
    val leadUdf = udf((eventTs: java.lang.Long, matchTs: java.lang.Long) => {
      if (eventTs == null || matchTs == null) {
        -1
      } else {
        Math.max(0, eventTs - matchTs)
      }
    })
    matchedDf.join(eventDf, matchedDf("targetid") === eventDf("resource_id") && matchedDf("type_name") === eventDf("type_name")
        && matchedDf("targettype") === eventDf("resource_type") && matchedDf("ds") === eventDf("care_ds"), "left")
      .withColumn("leadtime", leadUdf(col("ts"), col("matchingtime")))
  }

  private def writeTable(spark: SparkSession, partition: String, resultDf: DataFrame): DataFrame = {
    resultDf.createOrReplaceTempView("tempResultView")
    val sql = "INSERT INTO TABLE xdc_rule_feature_matching_results " +
      s"PARTITION ($partition) " +
      "SELECT features, matchingtime, targetid, targettype, leadtime, detailrule, event_id, ts, action_code, alertRuleName " +
      "FROM tempResultView"
    spark.sql(sql)
  }

  private def queryRuleMatchTable(spark: SparkSession, ds: String): DataFrame = {
    val startDs = DateUtil.modifyDateByDay(ds, -days, "yyyyMMdd")
    val endDs = DateUtil.modifyDateByDay(ds, -3, "yyyyMMdd")
    val sql = "select dimensions, features, matchingtime, targetid, targettype, ds " +
      "From xdc_rule_matching " +
      s"Where ds >= '$startDs' and ds <= '$endDs' and targettype = 'nc'"
    spark.sql(sql)
  }

  private def queryOpsEventTable(spark: SparkSession, ds: String): DataFrame = {
    // 查询运维事件
    val startDs = DateUtil.modifyDateByDay(ds, -days - 1, "yyyyMMdd")
    val endDs = DateUtil.modifyDateByDay(ds, -2, "yyyyMMdd")
    val sql = "select event_id, resource_type, resource_id, UNIX_TIMESTAMP(gmt_create) * 1000 as ts, action_code, extend_data, ds " +
      "From pet_plan_event_daily " +
      s"Where ds >= '$startDs' and ds <= '$endDs' " +
      s"and event_sourc = 'alarm_xdc'"
    spark.sql(sql)
  }

  private def queryFeatureCandidates(spark: SparkSession, ds: String): DataFrame = {
    val unixTime = DateUtil.datetime2Timestamp(ds, "yyyyMMdd", true)
    val date = DateUtil.timestamp2Datetime(unixTime - 90 * 86400L * 1000L)
    val sql = "SELECT features, rule as rule_name " +
      "FROM ops_rule_mining_detail " +
      s"WHERE pt = '$ds' " +
      s"AND (status = 'New' OR (status = 'Backtracked' AND gmt_modified < '$date')) " +
      s"ORDER BY rule, features " +
      s"LIMIT 200"

    spark.sql(sql)
  }

  private def queryOpsRuleTable(spark: SparkSession): DataFrame = {
    val sql = "SELECT name, conditions, exclusions " +
      "FROM xdc_rule_definition " +
      s"WHERE ds = (select max(ds) from xdc_rule_definition) " +
      s"and ops_type <> 'autoOptiRule' " +
      s"and is_deleted = 0 "

    spark.sql(sql)
  }

  private def queryFeatureSet(spark: SparkSession): Set[String] = {
    val sql = "SELECT distinct feature_name " +
      "FROM xdc_feature_definition " +
      s"WHERE pt = (select max(pt) from xdc_feature_definition)"

    val featureDefDf = spark.sql(sql)
    //这是一个千行量级的小表，直接转为set
    featureDefDf.collect().map(row => row.getString(0)).toSet
  }

}
