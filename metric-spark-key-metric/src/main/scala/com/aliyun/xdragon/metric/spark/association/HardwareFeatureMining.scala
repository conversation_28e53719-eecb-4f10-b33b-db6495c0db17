package com.aliyun.xdragon.metric.spark.association

import AssociationUtil.{timeSuffix, windowFunc}
import com.aliyun.xdragon.metric.spark.association.fpm.FPGrowth
import com.aliyun.xdragon.metric.spark.config.FeatureConfig
import com.aliyun.xdragon.metric.spark.util.DateUtil
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions.{col, collect_set, explode, lit, udf}
import org.apache.spark.sql.types.StringType

/**
 * <AUTHOR>
 * 这个代码用于生成重保客户宕机预测运维规则
 * 总的思路：只关心硬件特征，把所有硬件特征和目标特征的实例都找到，然后分析这些实例里面的特征（仅有硬件特征+宕机相关特征）之间的关联关系
 */

object HardwareFeatureMining {

  val involvedFeatures: String = "(%s)".format(new FeatureConfig().getFeatures)
  val targetFeatures = Set("nc_hang_too_long", "vm_crash", "xdragon_cn_down", "nc_short_ping_loss_by_ag", "pingmesh_latency_exception")

  def main(args: Array[String]): Unit = {

    val spark = SparkSession.builder
      .appName("hardware feature association rules mining")
      .config("spark.sql.broadcastTimeout", 20 * 60)
      .config("spark.sql.crossJoin.enabled", value = true)
      .getOrCreate()

    val featureTable = "xdc_feature_data"
    val ds = args(0)
    val days = 60
    val currentDay = ds.substring(0, 4) + "-" + ds.substring(4, 6) + "-" + ds.substring(6, 8) + timeSuffix
    val startDate= DateUtil.modifyDate(currentDay, days = -days).substring(0, 10).replaceAll("-", "")
    val featureSQL = "SELECT DISTINCT target_id, ds, feature_name FROM %s WHERE ds >='%s' AND ds <='%s' AND feature_name in %s".format(featureTable, startDate, ds, involvedFeatures)

    val windowUdf = udf(windowFunc)

    val featureDF = spark.sql(featureSQL)
      .withColumn("dsWindow", windowUdf(col("ds")))
      .withColumn("slideWindow", explode(col("dsWindow")))
      .groupBy("slideWindow", "target_id")
      .agg(collect_set("feature_name").as("items"))

    val size = featureDF.count().intValue()

    val support = 0.00003
    val confidence = 0.05
    val minimumSize = (size * support).intValue()
    val result = FPGrowth
      .FPGrowthProcess(featureDF, support = support, confidence = confidence,  50, "items", false)

    result._1.withColumn("total_items", lit(size))
      .withColumn("support", lit(support))
      .withColumn("ds", lit(ds))
      .select("item_set", "frequency","set_size", "total_items", "support", "ds")
      .write
      .mode("overwrite")
      .insertInto("ecs_feature_frequent_item_set")

    val consequentFilter = col( "consequent").cast(StringType) === "[%s]".format("nc_hang_too_long") ||
      col( "consequent").cast(StringType) === "[%s]".format("vm_crash") ||
      col( "consequent").cast(StringType) === "[%s]".format("xdragon_cn_down") ||
      col( "consequent").cast(StringType) === "[%s]".format("nc_short_ping_loss_by_ag") ||
      col( "consequent").cast(StringType) === "[%s]".format("pingmesh_latency_exception")

    val antecedentFunc = udf((antecedent: Seq[String]) =>{
      targetFeatures.map(feature=>antecedent.contains(feature)).reduce((a, b)=> a || b)
    })

    val antecedentLengthFunc = udf((antecedent: Seq[String]) =>{
      antecedent.size
    })

    result._2.filter(col("lift") > 1.0)
      .filter(consequentFilter)
      .filter(antecedentFunc(col("antecedent")) === false)
      .withColumn("minimum_size", lit(minimumSize))
      .withColumn("antecedent_length", antecedentLengthFunc(col("antecedent")))
      .withColumn("ds", lit(ds))
      .select("antecedent", "consequent", "confidence", "lift", "minimum_size", "antecedent_length", "ds")
      .write
      .mode("overwrite")
      .insertInto("ecs_feature_association_rule_2")
  }

}
