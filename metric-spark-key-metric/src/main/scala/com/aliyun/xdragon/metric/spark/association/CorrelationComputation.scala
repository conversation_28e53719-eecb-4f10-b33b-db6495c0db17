package com.aliyun.xdragon.metric.spark.association

import com.alibaba.fastjson.{JSON, JSONObject}
import com.aliyun.xdragon.metric.spark.association.fpm.FPGrowth
import com.aliyun.xdragon.metric.spark.common.FileUtil.readSQLConfigFile
import com.aliyun.xdragon.metric.spark.util.DateUtil
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions.{col, lit, udf}

import scala.collection.mutable
import scala.collection.mutable.ListBuffer
import scala.language.postfixOps
import scala.util.control.Breaks.{break, breakable}

/**
 * <AUTHOR>
 * 1、以实例为一条事务，实例中的特征为元素，对特征进行去重（只保留第一次出现的位置），根据时间维度进行排序，每个特征只考虑出现在后面的特征
 * 2、使用FP-growth得到特征之间的关联关系
 * 3、结合特征之间的时间顺序，过滤不满足时间顺序的特征关联关系
 */

object CorrelationComputation {

  val category = "feature"
  val jsonText: String = readSQLConfigFile("association/feature_generation.json", "").replaceAll(" ", "")
  val featureGeneration: JSONObject = JSON.parseObject(jsonText)

  def main(args: Array[String]): Unit = {

    val spark = SparkSession.builder
      .appName("correlation computation")
      .config("spark.sql.broadcastTimeout", 20 * 60)
      .config("spark.sql.crossJoin.enabled", value = true)
      .config("spark.driver.maxResultSize", "12g")
      .getOrCreate()

    val ds = args(0)
//    val table = "monitor_exception_sls_alert"
//    val sql = s"SELECT DISTINCT instanceid as target_id, exceptionname as feature_name, ds FROM $table WHERE ds>='$lastDs' " +
//      s"and ds <='$ds' and exceptionname in $involvedExceptions"

    // 从xdc_feature_definition中获取所有和主动运维相关的特征
    val featureSql = s"SELECT distinct feature_name FROM xdc_feature_definition where pt='$ds' " +
      s"AND (feature_type = 'ops' OR feature_type like '%controller%' OR reason IN ('nc_down', 'nc_hang') or short_desc LIKE '%宕机%' or category = 'CONTROLLER_FEATURE') AND feature_name <> 'pync_heartbeat_log_loss' "
    import spark.implicits._
    val opsFeatures = spark
      .sql(featureSql)
      .map(_.getString(0))
      .collect()
      .toList

    // 向前回溯，从xdc_feature_data中获取前2天的所有特征数据，并排除Gamma机器
    val days = 1
    val lastDs = DateUtil.modifyDate(ds, days = -days, dateTimeFormat = DateUtil.dateFormat)
    val gammaTable = "ecs_release_plan_gamma_host_v2"
    val gammaSql = s"SELECT distinct nc_ip FROM $gammaTable WHERE ds = '$ds'"
    val table = "xdc_feature_data"
    val sql = s"SELECT distinct target_id, feature_name, CAST(matched_timestamp as bigint) as matched_timestamp, " +
      s"ds as slideWindow FROM $table WHERE ds <='$ds' and ds >= '$lastDs' and target_id not in ($gammaSql)"
    val df = spark.sql(sql)
      .as[Feature]

    //计算特征共现关系，并写入到ecs_feature_items
    val featureAndTransactionDF = df
      .groupByKey(_.target_id)
      .flatMapGroups((key, iter)=>{
        val items = iter.toList.sortBy(_.matched_timestamp).map(v=>v.feature_name).distinct
        val result = ListBuffer[String]()
        breakable{
          for (item <- items) {
            //不考虑任何主动运维之后的特征
            if (opsFeatures.contains(item)) {
              break()
            }
            result.append(item)
          }
        }
        if (result.size < 1) {
          Seq()
        } else {
          Seq((key, result.toList, "feature", ds))
        }
      }).toDF("target_id", "items", "category", "ds")
    val itemTable = "ecs_feature_items"
    featureAndTransactionDF
      .write
      .mode("overwrite")
      .insertInto(itemTable)

    val window = 2
    val startDs = DateUtil.modifyDate(ds, days = -window, dateTimeFormat = DateUtil.dateFormat)
    val itemSql = s"select item_set from $itemTable where ds between '$startDs' and '$ds' "

    val colName = "items"

    // 这里必须去除空格，否则下面的joinUDF会匹配不上
    val itemDF = spark
      .sql(itemSql)
      .map(row => row.getAs[String](0).replaceAll("\\[|\\]|\\s", "").split(","))
      .toDF(colName)

    val size = itemDF.count()
    // 支持数和置信度阈值，当前暂不使用，因为每一天计算出来的结果都可以进行合并
    val support = 500.0/size
    val confidence = 0.85
    //  partition number = executor * cores * (2, 3)
    val partitionNum = 800


    val joinFunc: (mutable.WrappedArray[String], mutable.WrappedArray[String], String, String) => Boolean = (x: mutable.WrappedArray[String], y: mutable.WrappedArray[String], z: String, w: String) => {
      var isMatched = x.contains(z) && y.contains(w)
      if (isMatched) {
        true
      } else {
        breakable{
          for (feature <- opsFeatures) {
            if (x.contains(feature)) {
              isMatched = true
              break()
            }
          }
        }
        isMatched
      }
    }
    val joinUdf = udf(joinFunc)

    val result = FPGrowth.FPGrowthProcess(itemDF, support = support, confidence = confidence, partition = partitionNum,
      col_name = colName, false)

    // 这个表是特征之间近30天的关联关系，可以当作可以信赖的特征之间的顺序关系，过滤FP-Growth产出的不符合顺序的特征关联关系
    // 如果只包含单向的关联关系，则可以当作特征之间的顺序关系
    // 如果包含双向的关联关系，则以频数更高的方向作为特征之间的关联关系
    val associationTable = "xdragon_feature_association_mining"
//    val associationSql = s"select antecedent as antecedent1, consequent as consequent1 from $associationTable where ds = '$ds' and co_occurrence > 100 "
//      s"and confidence > 0.2 and imbalance_ratio < 0.9 and lift > 1.0 "

    val innerSql = s"SELECT antecedent, consequent, co_occurrence FROM $associationTable where ds = '$ds' AND co_occurrence > 100 "

    val associationSql = s"SELECT t1.antecedent AS antecedent1, t1.consequent AS consequent1 FROM ($innerSql) t1 LEFT JOIN  " +
      s"($innerSql) t2 ON (t1.antecedent = t2.consequent and t1.consequent = t2.antecedent) WHERE t2.antecedent IS NULL OR t1.co_occurrence > t2.co_occurrence"

    val associationDF = spark.sql(associationSql)

    val ruleDF = result._2.filter(col("lift") > 1.0)

    val noiseDF = ruleDF
      .join(associationDF, joinUdf(ruleDF("antecedent"), ruleDF("consequent"), associationDF("consequent1"), associationDF("antecedent1")), "inner")
      .select("antecedent", "consequent")
      .distinct()

    val rule = "frequent-items"

    result._1
      .withColumn("total_items", lit(size))
      .withColumn("support", lit(support))
      .withColumn("ds", lit(ds))
      .withColumn("rule", lit(rule))
      .select("item_set", "frequency", "set_size", "total_items", "support", "rule", "ds")
      .write
      .mode("overwrite")
      .insertInto("ecs_feature_frequent_item_set")

    val minimumSize = (size * support).intValue()

    // 通过这个函数可以过滤长度太长的关联关系，太长的关联关系并不好解释
    val itemsLenFunc = (x: mutable.WrappedArray[String]) => {
      x.size
    }
    val itemsLenUdf = udf(itemsLenFunc)

    ruleDF
      .join(noiseDF, ruleDF("antecedent") === noiseDF("antecedent") && ruleDF("consequent") === noiseDF("consequent"), "leftanti")
      .withColumn("minimum_size", lit(minimumSize))
      .withColumn("ds", lit(ds))
      .withColumn("rule", lit(rule))
      .withColumn("antecedent_len", itemsLenUdf(col("antecedent")))
      .filter(col("antecedent_len") < 4)
      .select("antecedent", "consequent", "confidence", "lift", "minimum_size", "rule", "ds")
      .write
      .mode("overwrite")
      .insertInto("ecs_feature_association_rule")
  }

}
