monitors:
  - name: vm_unexpected_killed_by_other
    monitorType: unavailable
    level: 1
    warningLevels:
      - critical
    eventType: start
    correspondingEvent:
      - vm_crash_end
    component: compute
    subComponent: unavailable
  - name: vm_migrate_nc_event
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - cloudops_reboot_vm_event
    component: compute
    needCorresponding: true
    priorityEvent:
      - cloudops_reboot_vm_recovery
    status: Running
  - name: vhost_blk_start_failed_too_many
    monitorType: unavailable
    level: 1
    warningLevels:
      - normal
    eventType: end
    correspondingEvent:
      - vm_iohang_start
    needCorresponding: true
  - name: open_pangu_config_failed
    monitorType: unavailable
    level: 1
    warningLevels:
      - normal
    eventType: end
    correspondingEvent:
      - vm_iohang_start
    component: storage
    needCorresponding: true
  - name: virtio_blk_create_device_failed
    monitorType: unavailable
    level: 1
    warningLevels:
      - normal
    eventType: end
    correspondingEvent:
      - vm_iohang_start
    needCorresponding: true
  - name: tdc_operation_failed_detail
    monitorType: unavailable
    level: 1
    warningLevels:
      - normal
    eventType: end
    correspondingEvent:
      - vm_iohang_start
    needCorresponding: true
  - name: vm_vport_lost
    monitorType: unavailable
    level: 1
    warningLevels:
      - fatal
    eventType: start
    correspondingEvent:
      - vm_vport_lost_recovery
    component: network
    subComponent: control
  - name: vm_vport_lost_recovery
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - vm_vport_lost
    component: network
  - name: key_customer_event_generate
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - nc_down_alert
      - vm_iohang_start
    component: compute
    additionalInfo:
      - Redeploy
    needCorresponding: true
  - name: active_vm_ops_event
    monitorType: unavailable
    level: 1
    eventType: point
    correspondingEvent:
      - active_vm_ops_event
    component: compute
    subComponent: control
    additionalInfo:
      - 强制KillVM:VM处于暂停状态且gshell检查异常
    duration: 180000
  - name: active_vm_ops_event_start
    monitorType: unavailable
    level: 1
    eventType: start
    correspondingEvent:
      - active_vm_ops_event_end
    component: compute
    subComponent: null
    additionalInfo:
      - 强制KillVM:VM处于暂停状态且gshell检查异常
  - name: active_vm_ops_event_end
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - active_vm_ops_event_start
    component: compute
    additionalInfo:
      - 强制KillVM:VM处于暂停状态且gshell检查异常
  - name: vm_panic_event
    monitorType: unavailable
    level: 1
    eventType: point
    correspondingEvent:
      - vm_panic_event
    component: compute
    subComponent: unavailable
    additionalInfo:
      - hv_relaxed
    duration: 60000
    isWin: true
  - name: vm_panic_event_start
    monitorType: unavailable
    level: 1
    eventType: start
    correspondingEvent:
      - vm_panic_event_end
    component: compute
    subComponent: null
    additionalInfo:
      - hv_relaxed
  - name: vm_panic_event_end
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - vm_panic_event_start
    component: compute
    additionalInfo:
      - hv_relaxed
  - name: user_accept_event_to_avoid_issue
    monitorType: unavailable
    level: 1
    eventType: point
    correspondingEvent:
      - user_accept_event_to_avoid_issue
    component: compute
    subComponent: control
    duration: 60000
  - name: user_accept_event_to_avoid_issue_start
    monitorType: unavailable
    level: 1
    eventType: start
    correspondingEvent:
      - user_accept_event_to_avoid_issue_end
    component: compute
    subComponent: control
  - name: user_accept_event_to_avoid_issue_end
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - user_accept_event_to_avoid_issue_start
      - nc_down_alert
    component: compute
  - name: wild_vm_disk_open_failed_hang
    monitorType: unavailable
    level: 1
    eventType: point
    correspondingEvent:
      - wild_vm_disk_open_failed_hang
    component: storage
    subComponent: tdc
    duration: 60000
  - name: wild_vm_disk_open_failed_hang_start
    monitorType: unavailable
    level: 1
    eventType: start
    correspondingEvent:
      - wild_vm_disk_open_failed_hang_end
    component: storage
    subComponent: null
  - name: wild_vm_disk_open_failed_hang_end
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - wild_vm_disk_open_failed_hang_start
    component: storage
  - name: nc_down_alert
    monitorType: unavailable
    level: 1
    eventType: start
    correspondingEvent:
      - key_customer_event_generate
      - vm_down_end
      - vm_vsock_icmp_ping_loss_recovery
      - user_accept_event_to_avoid_issue_end
    component: compute
    subComponent: unavailable
  - name: vm_vsock_icmp_ping_loss_new
    monitorType: unavailable
    level: 1
    eventType: point
    correspondingEvent:
      - vm_vsock_icmp_ping_loss_new
    component: network
    subComponent: unavailable
    additionalInfo:
      - fpga_caterr
      - fpga_chip_invalid
      - core_dump_generated
      - cn_hang_task_detected
      - nic_hardware_alarm
      - nic_hardware_alarm2
      - nic_hardware_alarm3
      - nic_hardware_alarm4
      - nic_hardware_alarm5
      - nic_hardware_alarm6
      - nic_hardware_alarm7
      - cn_mce_killing_error
      - tx_hang_conman
      - mce_killing_error
      - avs_monitor_warning
      - vm_vfio_err_notifier
      - avs_check_error
    duration: 60000
  - name: vm_vsock_icmp_ping_loss_new_start
    monitorType: unavailable
    level: 1
    eventType: start
    correspondingEvent:
      - vm_vsock_icmp_ping_loss_new_end
    component: network
    subComponent: null
    additionalInfo:
      - fpga_caterr
      - fpga_chip_invalid
      - core_dump_generated
      - cn_hang_task_detected
      - nic_hardware_alarm
      - nic_hardware_alarm2
      - nic_hardware_alarm3
      - nic_hardware_alarm4
      - nic_hardware_alarm5
      - nic_hardware_alarm6
      - nic_hardware_alarm7
      - cn_mce_killing_error
      - tx_hang_conman
      - mce_killing_error
      - avs_monitor_warning
      - vm_vfio_err_notifier
      - avs_check_error
  - name: vm_vsock_icmp_ping_loss_new_end
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - vm_vsock_icmp_ping_loss_new_start
    component: network
    additionalInfo:
      - fpga_caterr
      - fpga_chip_invalid
      - core_dump_generated
      - cn_hang_task_detected
      - nic_hardware_alarm
      - nic_hardware_alarm2
      - nic_hardware_alarm3
      - nic_hardware_alarm4
      - nic_hardware_alarm5
      - nic_hardware_alarm6
      - nic_hardware_alarm7
      - cn_mce_killing_error
      - tx_hang_conman
      - mce_killing_error
      - avs_monitor_warning
      - vm_vfio_err_notifier
      - avs_check_error
  - name: vm_crash_event
    monitorType: unavailable
    level: 1
    warningLevels:
      - warning
      - fatal
      - critical
    eventType: start
    correspondingEvent:
      - vm_crash_end
      - vm_down_end
    component: compute
    subComponent: unavailable
  - name: vm_iohang_start
    monitorType: unavailable
    level: 1
    warningLevels:
      - fatal
      - warning
      - low_warning
    eventType: start
    correspondingEvent:
      - key_customer_event_generate
      - vm_iohang_end
      - vhost_blk_start_failed_too_many
      - open_pangu_config_failed
      - virtio_blk_create_device_failed
      - tdc_operation_failed_detail
      - cloudops_reboot_vm_recovery
    component: storage
    subComponent: tdc
    reason:
      - disk_hang
  - name: cloudops_reboot_vm_event
    monitorType: unavailable
    level: 1
    eventType: start
    correspondingEvent:
      - cloudops_reboot_vm_recovery
      - vm_migrate_nc_event
    component: compute
    subComponent: control
    additionalInfo:
      - Need Running
  - name: vm_livemigrate_exception
    monitorType: unavailable
    level: 1
    warningLevels:
      - fatal
    eventType: start
    correspondingEvent:
      - vm_crash_end
      - vm_down_end
    component: compute
    subComponent: unavailable
  - name: vm_pasued_resume_check_ntp_start
    monitorType: unavailable
    level: 1
    eventType: start
    correspondingEvent:
      - vm_pasued_resume_check_ntp_end
    component: compute
    subComponent: null
  - name: xdragon_hot_upgrade_fpga_down_event_start
    monitorType: unavailable
    level: 1
    eventType: start
    correspondingEvent:
      - xdragon_hot_upgrade_fpga_down_event_end
    component: fpga
    subComponent: null
  - name: vm_pasued_resume_check_ntp_end
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - vm_pasued_resume_check_ntp_start
    component: compute
  - name: xdragon_hot_upgrade_fpga_down_event_end
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - xdragon_hot_upgrade_fpga_down_event_start
    component: fpga
  - name: vm_down_end
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - nc_down_alert
      - vm_crash_event
      - live_migrate_on_src_nc_event
  - name: vm_vsock_icmp_ping_loss_recovery
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - nc_down_alert
    component: network
    needCorresponding: true
    priorityEvent:
      - vm_down_end
  - name: vm_crash_end
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - vm_crash_event
      - vm_livemigrate_exception
      - vm_unexpected_killed_by_other
  - name: vm_iohang_end
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - vm_iohang_start
  - name: cloudops_reboot_vm_recovery
    monitorType: unavailable
    level: 1
    warningLevels:
      - warning
      - low_warning
      - normal
    eventType: end
    correspondingEvent:
      - cloudops_reboot_vm_event
    component: compute
    additionalInfo:
      - Need Running
  - name: qemu_error_hardware_entry_failed
    monitorType: unavailable
    level: 1
    eventType: point
    correspondingEvent:
      - qemu_error_hardware_entry_failed
    component: compute
    subComponent: unavailable
    duration: 60000
  - name: qemu_error_hardware_entry_failed_start
    monitorType: unavailable
    level: 1
    eventType: start
    correspondingEvent:
      - qemu_error_hardware_entry_failed_end
    component: compute
    subComponent: null
  - name: qemu_error_hardware_entry_failed_end
    monitorType: unavailable
    level: 1
    eventType: end
    correspondingEvent:
      - qemu_error_hardware_entry_failed_start
    component: compute
  - name: vm_paused_exception
    monitorType: unavailable
    level: 1
    warningLevels:
      - fatal
      - critical
    eventType: point
    correspondingEvent:
      - vm_paused_exception
    component: compute
    subComponent: unavailable
    duration: 60000
    zoneGroupFlag:
      - not:test_before_online
    testAccount:
      - "False"
    aliUid:
      - not:****************
  - name: vm_paused_exception_start
    monitorType: unavailable
    level: 1
    warningLevels:
      - fatal
    eventType: start
    correspondingEvent:
      - vm_paused_exception_end
    component: compute
    subComponent: null
    zoneGroupFlag:
      - not:test_before_online
    testAccount:
      - "False"
    aliUid:
      - not:****************
  - name: vm_paused_exception_end
    monitorType: unavailable
    level: 1
    warningLevels:
      - fatal
    eventType: end
    correspondingEvent:
      - vm_paused_exception_start
    component: compute
    zoneGroupFlag:
      - not:test_before_online
    testAccount:
      - "False"
    aliUid:
      - not:****************
  - name: nc_exception_oob_status_error
    monitorType: control
    level: 1
    component: compute
    subComponent: hardware
  - name: vm_start_in_bios_many_times
    monitorType: control
    level: 1
    component: compute
    subComponent: guestos
  - name: nc_hang_task_detected
    monitorType: control
    level: 2
    component: compute
    subComponent: null
    needRename: true
    reason:
      - libvirtd
      - iohub-pcie-admi
      - virsh
      - river_admin
      - tdc_admin
      - avs-sysctl
      - network-pync-master
      - pync-master
  - name: nc_hang_task_detected_conman
    monitorType: control
    level: 2
    component: os
    subComponent: cpu
    needRename: true
    reason:
      - libvirtd
      - iohub-pcie-admi
      - virsh
      - river_admin
      - tdc_admin
      - avs-sysctl
      - network-pync-master
      - pync-master
      - iohub-fwd
  - name: process_oom_exception
    monitorType: control
    level: 2
    component: os
    subComponent: process
    additionalInfo:
      - libvirtd
      - iohub-pcie-admi
      - virsh
      - river_admin
      - tdc_admin
      - avs-sysctl
      - network-pync-master
      - pync-master
      - avs_dhcpd
      - /controller
      - avs-vswitchd
      - vswctl
      - adf_netdev
      - net_dev
  - name: process_oom_exception_in_band
    monitorType: control
    level: 2
    component: os
    subComponent: process
    additionalInfo:
      - libvirtd
      - iohub-pcie-admi
      - virsh
      - river_admin
      - tdc_admin
      - avs-sysctl
      - network-pync-master
      - pync-master
      - avs_dhcpd
      - /controller
      - avs-vswitchd
      - vswctl
      - adf_netdev
      - net_dev
  - name: cn_process_oom_exception
    monitorType: control
    level: 2
    component: os
    subComponent: mem
    additionalInfo:
      - libvirtd
      - virsh
  - name: core_dump_generated
    monitorType: control
    level: 2
    component: os
    subComponent: process
    additionalInfo:
      - libvirtd
      - iohub-pcie-admi
      - virsh
      - river_admin
      - tdc_admin
      - avs-sysctl
      - network-pync-master
      - pync-master
      - avs_dhcpd
      - /controller
      - avs-vswitchd
      - vswctl
      - adf_netdev
      - net_dev
  - name: xdragon_cn_hang_or_down
    monitorType: control
    level: 2
    warningLevels:
      - critical
      - warning
    component: compute
    subComponent: unavailable
  - name: vm_start_failed
    monitorType: control
    level: 3
    component: compute
    subComponent: control
  - name: start_vm_failed_xml
    monitorType: control
    level: 3
    component: compute
    subComponent: control
  - name: virt_stop_vm_failed
    monitorType: control
    level: 3
    component: compute
    subComponent: control
  - name: vm_pined_status_exception
    monitorType: control
    level: 3
    component: compute
    subComponent: performance
  - name: vm_vcpu_pin_failed
    monitorType: control
    level: 3
    warningLevels:
      - critical
    component: compute
    subComponent: control
  - name: vm_vcpu_pin_failed_pync
    monitorType: control
    level: 3
    component: compute
    subComponent: control
  - name: vhost_blk_start_failed
    monitorType: control
    level: 3
    component: storage
    subComponent: blk
  - name: virtio_blk_pci_initial_failed
    monitorType: control
    level: 3
    component: storage
    subComponent: blk
  - name: open_pangu_config_failed
    monitorType: control
    level: 3
    component: storage
    subComponent: blk
  - name: xen_open_pangu_config_failed
    monitorType: control
    level: 3
    component: storage
    subComponent: blk
  - name: blkpmd_open_disk_unfinished_inflight
    monitorType: control
    level: 3
    component: storage
    subComponent: pmd
  - name: disk_attach_detach_fail
    monitorType: control
    level: 3
    component: storage
    subComponent: tdc
  - name: virt_eni_attach_detach_fail
    monitorType: control
    level: 3
    component: compute
    subComponent: control
  - name: vm_vcpu_steal
    monitorType: performance
    level: 1
    component: compute
    subComponent: performance
  - name: cn_process_oom_exception
    monitorType: performance
    level: 1
    warningLevels:
      - fatal
      - critical
    component: os
    subComponent: mem
  - name: nc_hang_task_detected
    monitorType: performance
    level: 1
    warningLevels:
      - fatal
      - critical
    component: os
    subComponent: cpu
    needRename: true
    reason:
      - qemu-kvm
      - qemu-system-x86
      - dragonball
      - dragonfly
      - /usr/bin/qemu-kvm
      - CPU-/KVM
      - pangu_chunkserv
      - river_server
      - td_connector
      - watchdog_tdc.py
      - /apsara/tdc/td_connector
      - vcpu-worker
      - systemd
      - dpdkavs
      - lcore-slave
  - name: nc_hang_task_detected_conman
    monitorType: performance
    level: 1
    warningLevels:
      - fatal
      - critical
    component: os
    subComponent: cpu
    needRename: true
    reason:
      - qemu-kvm
      - qemu-system-x86
      - dragonball
      - dragonfly
      - /usr/bin/qemu-kvm
      - CPU-/KVM
      - pangu_chunkserv
      - river_server
      - td_connector
      - watchdog_tdc.py
      - /apsara/tdc/td_connector
      - vcpu-worker
      - systemd
      - dpdkavs
      - lcore-slave
  - name: process_oom_exception
    monitorType: performance
    level: 1
    warningLevels:
      - fatal
      - critical
    component: os
    subComponent: process
    additionalInfo:
      - qemu-kvm
      - qemu-system-x86
      - dragonball
      - dragonfly
      - /usr/bin/qemu-kvm
      - CPU-/KVM
      - pangu_chunkserv
      - river_server
      - td_connector
      - watchdog_tdc.py
      - /apsara/tdc/td_connector
      - vcpu-worker
      - systemd
      - dpdkavs
      - lcore-slave
    needRename: true
  - name: process_oom_exception_in_band
    monitorType: performance
    level: 1
    warningLevels:
      - fatal
      - critical
    component: os
    subComponent: process
    additionalInfo:
      - qemu-kvm
      - qemu-system-x86
      - dragonball
      - dragonfly
      - /usr/bin/qemu-kvm
      - CPU-/KVM
      - pangu_chunkserv
      - river_server
      - td_connector
      - watchdog_tdc.py
      - /apsara/tdc/td_connector
      - vcpu-worker
      - systemd
      - dpdkavs
      - lcore-slave
    needRename: true
  - name: cn_process_oom_exception
    monitorType: performance
    level: 1
    warningLevels:
      - fatal
      - critical
    component: os
    subComponent: mem
    additionalInfo:
      - qemu-kvm
      - qemu-system-x86
      - dragonball
      - dragonfly
      - /usr/bin/qemu-kvm
      - CPU-/KVM
      - pangu_chunkserv
      - river_server
      - td_connector
      - watchdog_tdc.py
      - /apsara/tdc/td_connector
      - vcpu-worker
      - systemd
      - dpdkavs
      - lcore-slave
    needRename: true
  - name: core_dump_generated
    monitorType: performance
    level: 1
    warningLevels:
      - fatal
      - critical
      - warning
    component: os
    subComponent: process
    additionalInfo:
      - qemu-kvm
      - qemu-system-x86
      - dragonball
      - dragonfly
      - /usr/bin/qemu-kvm
      - CPU-/KVM
      - pangu_chunkserv
      - river_server
      - td_connector
      - watchdog_tdc.py
      - /apsara/tdc/td_connector
      - vcpu-worker
      - systemd
      - dpdkavs
      - lcore-slave
    needRename: true
  - name: nc_hang_too_long
    monitorType: performance
    level: 1
    component: compute
    subComponent: performance
  - name: key_customer_local_disk_event_generate
    monitorType: performance
    level: 1
    component: local_storage
    subComponent: spoold
  - name: xdragon_cn_hang_too_long
    monitorType: performance
    level: 1
    warningLevels:
      - critical
      - fatal
    component: compute
    subComponent: performance
  - name: idc_hardware_exception
    monitorType: performance
    level: 1
    component: compute
    subComponent: hardware
    reason:
      - hardware_pcie_gpu
      - hardware_tpm_card_error
      - hardware_aep_error
  - name: cn_mce_killing_error
    monitorType: performance
    level: 1
    component: compute
    subComponent: hardware
  - name: gpu_device_xid_error
    monitorType: performance
    level: 1
    component: gpu
    subComponent: hardware
    additionalInfo:
      - ": 79"
      - ": 74"
  - name: gpu_device_xid_error_conman
    monitorType: performance
    level: 1
    component: gpu
    subComponent: hardware
    additionalInfo:
      - ": 79"
      - ": 74"
  - name: nc_error_impact_vm_performance
    monitorType: performance
    level: 2
    component: network
    subComponent: perf
  - name: nic_error_impact_vm_performance
    monitorType: performance
    level: 2
    component: network
    subComponent: nic
  - name: xdragon_cn_ping_loss_too_much
    monitorType: performance
    level: 2
    warningLevels:
      - critical
    component: compute
    subComponent: performance
  - name: ag_nc_short_ping_check_failed
    monitorType: performance
    level: 2
    warningLevels:
      - warning
    component: network
    subComponent: perf
  - name: live_migrate_perf_degrade_event
    monitorType: performance
    level: 2
    component: compute
    subComponent: performance
  - name: vm_disk_io_latency_exception
    monitorType: performance
    level: 2
    warningLevels:
      - warning
    component: storage
    subComponent: blk
  - name: nc_dpdk_driver_flow_drop
    monitorType: performance
    level: 2
    warningLevels:
      - fatal
      - critical
      - warning
    component: network
    subComponent: perf
    bizStatus:
      - nc_down
  - name: live_migrate_on_src_nc_event
    monitorType: performance
    level: 2
    component: compute
    subComponent: control
    extension:
      - perf_loss
  - name: nic_flapping_cause_disk_slowio
    monitorType: performance
    level: 2
    component: storage
    subComponent: blk
  - name: vm_pasued_resume_check_ntp
    monitorType: performance
    level: 2
    component: compute
    subComponent: null
    transform: true
    transformThreshold: 60
  - name: xdragon_hot_upgrade_fpga_down_event
    monitorType: performance
    level: 2
    warningLevels:
      - fatal
      - critical
      - warning
    component: fpga
    subComponent: software
    transform: true
    transformThreshold: 60000
  - name: avs_hotup_vport_downtime
    monitorType: performance
    level: 2
    warningLevels:
      - fatal
      - critical
    component: network
    subComponent: avs
  - name: vm_erdma_engine_error
    monitorType: performance
    level: 2
    warningLevels:
      - critical
    component: network
    subComponent: pmd
  - name: mem_bw_contend_exception
    monitorType: performance
    level: 3
    warningLevels:
      - fatal
      - critical
    component: compute
    subComponent: performance
  # - name: nc_cpu_socket_tdp_reached
  #   monitorType: performance
  #   level: 3
  #   warningLevels:
  #     - warning
  #     - critical
  #   component: compute
  #   subComponent: performance
  - name: nc_hang_uniform_check
    monitorType: performance
    level: 3
    warningLevels:
      - critical
      - warning
      - low_warning
    component: compute
    subComponent: performance
  - name: vm_vcpu_freq_exception
    monitorType: performance
    level: 3
    warningLevels:
      - warning
    component: compute
    subComponent: performance
    isFullNc: "false"
  - name: vm_llc_inhibition
    monitorType: performance
    level: 3
    warningLevels:
      - warning
    component: compute
    subComponent: performance
    vcpuMode: exclusive
  - name: cn_for_vm_llc_contention
    monitorType: performance
    level: 3
    warningLevels:
      - critical
      - fatal
    component: compute
    subComponent: performance
  - name: llc_contention_potential
    monitorType: performance
    level: 3
    component: compute
    subComponent: performance
  - name: pingmesh_latency_error_public
    monitorType: performance
    level: 3
    warningLevels:
      - critical
      - warning
    component: network
    subComponent: unavailable
  - name: pingmesh_latency_error_aliproduct
    monitorType: performance
    level: 3
    warningLevels:
      - critical
      - warning
    component: network
    subComponent: null
  - name: nc_session_too_large
    monitorType: performance
    level: 3
    warningLevels:
      - fatal
    component: network
    subComponent: perf
  - name: dpdkavs_cpu_high_multi_user
    monitorType: performance
    level: 3
    component: network
    subComponent: perf
  - name: fpga_check_cn_pcie_slow
    monitorType: performance
    level: 3
    component: fpga
    subComponent: pcie
  - name: live_migrate_on_dst_nc_finish
    monitorType: performance
    level: 4
    warningLevels:
      - normal
    component: compute
    subComponent: control
    duration: 100
  - name: qemu_hot_upgrade_down_time_event
    monitorType: performance
    level: 4
    warningLevels:
      - fatal
      - critical
      - warning
    component: compute
    subComponent: performance
    duration: 100
  - name: vm_perf_disturbed_event
    monitorType: performance
    level: 4
    warningLevels:
      - normal
    component: compute
    subComponent: performance
  - name: live_migrate_on_src_nc_event
    monitorType: performance
    level: 1
    warningLevels:
      - warning
    eventType: start
    correspondingEvent:
      - live_migrate_on_src_nc_finish
      - vm_down_end
    component: compute
    subComponent: control
  - name: live_migrate_on_src_nc_finish
    monitorType: performance
    level: 1
    warningLevels:
      - warning
    eventType: end
    correspondingEvent:
      - live_migrate_on_src_nc_event
  - name: amd_socket_mem_bw_high
    monitorType: performance
    level: 3
    warningLevels:
      - fatal
      - warning
    component: compute
    subComponent: performance
  - name: vm_vcpu_freq_flapping
    monitorType: performance
    level: 1
    warningLevels:
      - warning
      - fatal
      - critical
    component: compute
    subComponent: performance
    isFullNc: "false"
  - name: vpc_async_task_timeout
    monitorType: control
    level: 1
    warningLevels:
      - warning
      - low_warning
      - critical
    component: network
    subComponent: control
  - name: sysvm_kubelet_restart_need_sync_fastpath
    monitorType: control
    level: 1
    warningLevels:
      - critical
    component: container
  - name: sysvm_not_ready_fastpath
    monitorType: control
    level: 1
    warningLevels:
      - critical
    component: container
  - name: sysvm_os_restart_need_to_sync_fastpath
    monitorType: control
    level: 1
    warningLevels:
      - critical
    component: container
  - name: cn_hang_task_detected
    monitorType: performance
    level: 1
    component: os
    subComponent: cpu
  - name: nic_error_cause_vm_slowio
    monitorType: performance
    level: 1
    component: network
    subComponent: nic
  - name: vm_net_queue_error
    monitorType: performance
    level: 1
    component: network
    subComponent: pmd
    warningLevels:
      - fatal

  - name: gpu_inspection_fatal_error_report
    monitorType: unavailable
    eventType: point
    component: gpu
    subComponent: hardware
    duration: 1000
    warningLevels:
      - fatal
    correspondingEvent:
      - gpu_inspection_fatal_error_report
  - name: gpu_inspection_fatal_error_report_start
    monitorType: unavailable
    eventType: start
    component: gpu
    subComponent: hardware
    warningLevels:
      - fatal
    correspondingEvent:
      - gpu_inspection_fatal_error_report_end
  - name: gpu_inspection_fatal_error_report_end
    monitorType: unavailable
    eventType: end
    component: gpu
    subComponent: hardware
    warningLevels:
      - fatal
    correspondingEvent:
      - gpu_inspection_fatal_error_report_start

  - name: physical_gpu_firmware_boot_fail
    monitorType: unavailable
    eventType: point
    level: 1
    duration: 1000
    component: gpu
    subComponent: virt
    correspondingEvent:
      - physical_gpu_firmware_boot_fail
    warningLevels:
      - critical
  - name: physical_gpu_firmware_boot_fail_start
    monitorType: unavailable
    eventType: start
    level: 1
    component: gpu
    subComponent: virt
    warningLevels:
      - critical
    correspondingEvent:
      - physical_gpu_firmware_boot_fail_end
  - name: physical_gpu_firmware_boot_fail_end
    monitorType: unavailable
    eventType: end
    level: 1
    component: gpu
    subComponent: virt
    warningLevels:
      - critical
    correspondingEvent:
      - physical_gpu_firmware_boot_fail_start

  - name: gpu_lostcard_physical
    monitorType: unavailable
    eventType: point
    duration: 1000
    level: 1
    component: gpu
    subComponent: hardware
    correspondingEvent:
      - gpu_lostcard_physical
    warningLevels:
      - fatal
  - name: gpu_lostcard_physical_start
    monitorType: unavailable
    eventType: start
    level: 1
    component: gpu
    subComponent: hardware
    warningLevels:
      - fatal
    correspondingEvent:
      - gpu_lostcard_physical_end
  - name: gpu_lostcard_physical_end
    monitorType: unavailable
    eventType: end
    level: 1
    component: gpu
    subComponent: hardware
    warningLevels:
      - fatal
    correspondingEvent:
      - gpu_lostcard_physical_start

  - name: physical_gpu_ecc_error
    monitorType: unavailable
    eventType: point
    duration: 1000
    level: 1
    component: gpu
    subComponent: hardware
    correspondingEvent:
      - physical_gpu_ecc_error
    warningLevels:
      - critical
  - name: physical_gpu_ecc_error_start
    monitorType: unavailable
    eventType: start
    level: 1
    component: gpu
    subComponent: hardware
    warningLevels:
      - critical
    correspondingEvent:
      - physical_gpu_ecc_error_end
  - name: physical_gpu_ecc_error_end
    monitorType: unavailable
    eventType: end
    level: 1
    component: gpu
    subComponent: hardware
    warningLevels:
      - critical
    correspondingEvent:
      - physical_gpu_ecc_error_start

  - name: gpu_check_service_vm_fabric_manager
    monitorType: unavailable
    eventType: point
    duration: 1000
    level: 1
    component: gpu
    subComponent: virt
    correspondingEvent:
      - gpu_check_service_vm_fabric_manager
    warningLevels:
      - warning
  - name: gpu_check_service_vm_fabric_manager_start
    monitorType: unavailable
    eventType: start
    level: 1
    component: gpu
    subComponent: virt
    warningLevels:
      - warning
    correspondingEvent:
      - gpu_check_service_vm_fabric_manager_end
  - name: gpu_check_service_vm_fabric_manager_end
    monitorType: unavailable
    eventType: end
    level: 1
    component: gpu
    subComponent: virt
    warningLevels:
      - warning
    correspondingEvent:
      - gpu_check_service_vm_fabric_manager_start

  - name: gpu_inspection_status
    monitorType: unavailable
    eventType: point
    duration: 1000
    level: 1
    component: gpu
    subComponent: hardware
    correspondingEvent:
      - gpu_inspection_status
  - name: gpu_inspection_status_start
    monitorType: unavailable
    eventType: start
    level: 1
    component: gpu
    subComponent: hardware
    correspondingEvent:
      - gpu_inspection_status_end
  - name: gpu_inspection_status_end
    monitorType: unavailable
    eventType: end
    level: 1
    component: gpu
    subComponent: hardware
    correspondingEvent:
      - gpu_inspection_status_start

  - name: gpu_local_inspection_status
    monitorType: unavailable
    eventType: point
    duration: 1000
    level: 1
    component: gpu
    warningLevels:
      - fatal
    subComponent: hardware
    correspondingEvent:
      - gpu_local_inspection_status
  - name: gpu_local_inspection_status_start
    monitorType: unavailable
    eventType: start
    level: 1
    component: gpu
    warningLevels:
      - fatal
    subComponent: hardware
    correspondingEvent:
      - gpu_local_inspection_status_end
  - name: gpu_local_inspection_status_end
    monitorType: unavailable
    eventType: end
    level: 1
    component: gpu
    warningLevels:
      - fatal
    subComponent: hardware
    correspondingEvent:
      - gpu_local_inspection_status_start

  - name: gpu_guestos_diagnose_status
    monitorType: unavailable
    eventType: point
    duration: 1000
    level: 1
    component: gpu
    subComponent: hardware
    correspondingEvent:
      - gpu_guestos_diagnose_status
  - name: gpu_guestos_diagnose_status_start
    monitorType: unavailable
    eventType: start
    level: 1
    component: gpu
    subComponent: hardware
    correspondingEvent:
      - gpu_guestos_diagnose_status_end
  - name: gpu_guestos_diagnose_status_end
    monitorType: unavailable
    eventType: end
    level: 1
    component: gpu
    subComponent: hardware
    correspondingEvent:
      - gpu_guestos_diagnose_status_start

  - name: service_vm_rm_init_adapter_fail
    monitorType: unavailable
    eventType: point
    duration: 1000
    level: 1
    component: gpu
    subComponent: virt
    correspondingEvent:
      - service_vm_rm_init_adapter_fail
    warningLevels:
      - critical
  - name: service_vm_rm_init_adapter_fail_start
    monitorType: unavailable
    eventType: start
    level: 1
    component: gpu
    subComponent: virt
    warningLevels:
      - critical
    correspondingEvent:
      - service_vm_rm_init_adapter_fail_end
  - name: service_vm_rm_init_adapter_fail_end
    monitorType: unavailable
    eventType: end
    level: 1
    component: gpu
    subComponent: virt
    warningLevels:
      - critical
    correspondingEvent:
      - service_vm_rm_init_adapter_fail_start

  - name: nic_hardware_alarm
    monitorType: performance
    level: 1
    component: network
    subComponent: nic
  - name: nic_hardware_alarm2
    monitorType: performance
    level: 1
    component: network
    subComponent: nic
  - name: nic_hardware_alarm3
    monitorType: performance
    level: 1
    component: network
    subComponent: nic
  - name: nic_hardware_alarm4
    monitorType: performance
    level: 1
    component: network
    subComponent: nic
  - name: nic_hardware_alarm5
    monitorType: performance
    level: 1
    component: network
    subComponent: nic
  - name: nic_hardware_alarm6
    monitorType: performance
    level: 1
    component: network
    subComponent: nic
  - name: nic_hardware_alarm7
    monitorType: performance
    level: 1
    component: network
    subComponent: nic
  - name: nic_error_10min
    monitorType: performance
    level: 1
    component: network
    subComponent: nic
  - name: nic_error_30min
    monitorType: performance
    level: 1
    component: network
    subComponent: nic