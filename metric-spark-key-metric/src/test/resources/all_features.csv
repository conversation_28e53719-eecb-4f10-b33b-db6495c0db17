"feature_name"
"inspect_fpga_bm_full"
"multi_bit_ecc"
"nvidia_vgpu_mgr_abnormal"
"vgpu_lost_card_by_xid79"
"avs_report_ddr_crc"
"data_disk_error_local_hdd_cn"
"nvme_flash_error_cn"
"vm_shallow_hibernate_unpause"
"taiji_allocate_mem_error"
"inspect_gpu_uce_from_ipmi"
"fpga_net_queue_too_large"
"GuestOS.GPU.PowerCableError"
"GuestOS.GPU.XidError"
"GuestOS.GPU.DeviceLost"
"GuestOS.GPU.InfoRomCorrupted"
"GuestOS.GPU.RmInitAdapterError"
"gamma_offline_event"
"dpdkcpu_max_util_high_multi_user"
"vm_hang_screenshot_ocr_reason"
"vm_shallow_hibernate"
"gpu_hardware_issue_high_risk"
"gpu_memory_hardware_prediction_samsung"
"llc_contention_disinhibition"
"nc_system_fd_open_too_many"
"virt_nc_data_loss"
"nc_network_flow_too_high"
"moc_memory_hardware_prediction"
"live_migrate_on_dst_nc_finish"
"check_virtio_fd_channel_error"
"inspect_abnormal_long_process"
"hugepage_continuance_fix"
"gpu_inspection_fatal_status"
"user_action_cause_kvm_entry_failed"
"bmc_or_fw_upgrade"
"vm_hang_event_send"
"vm_local_storage_io_hang"
"cloudbox_network_error"
"network_key_process_restart"
"vm_arp_ping_check_not_ok"
"inspect_steal_cause_try_perf_loss_migrate"
"nc_filesystem_error"
"blkpmd_get_buf_failed_cause_stop_queue"
"blkpmd_get_buf_failed_cause_slow_io"
"inspect_steal_cause_perf_loss_migrate"
"nc_power_increased"
"wuzong_perf_degrade_spot_kill"
"open_nc_down_migration"
"workitem_event"
"vm_arp_ping_recovery"
"vm_gshell_check_ready"
"bond_single_nic_down_too_long"
"asw_exception_dedicated_region"
"cn_exec_power_reset_or_cycle"
"cn_exec_power_off"
"vm_load_high_live_migrate_fail_dpdkcpu"
"eci_independent_pool_controll_vm_down_need_realease"
"vm_ha_live_migrate"
"virt_report_moc_mce_event"
"ag_vm_allocate_memory_failed"
"vm_network_retry_high_with_userid"
"GuestOS.PAM.PamDenySSHorLogin"
"GuestOS.Service.FirewallServiceNotRunning"
"GuestOS.Memory.CannotAllocateMemoryEvent"
"GuestOS.PAM.MissPamModule"
"GuestOS.HostsAccess.DenySSHConnect"
"GuestOS.RDP.PortNotStandard"
"GuestOS.Disk.PartitionUnknown"
"virt_report_vm_crash_event"
"storage_controller_process_core"
"cn_cpu_cache_error_warning"
"cn_cpu_cache_error_critical"
"nc_down_uniform_check_error_fastpath"
"down_start_trigger_fastpath"
"nc_real_down_fastpath"
"vm_netcpu_limit_fastpath"
"GuestOS.WinSystemProcess.FirewallServiceNotRunning"
"baremetal_libvirt_hang"
"virtio_vring_err"
"nc_memory_sunreclaim_used_too_much"
"GuestOS.MountRoot.Readonly"
"GuestOS.Grub.IncorrectConfig"
"GuestOS.Disk.IOError"
"GuestOS.SystemUserFile.NotUnixFormat"
"GuestOS.NetworkFirewall.Enabled"
"GuestOS.Disk.MissingDiskByUUID"
"GuestOS.Udev.MacAddressNotExist"
"GuestOS.System.EnterEmergencyMode"
"GuestOS.SystemFile.LostInitRamFS"
"GuestOS.NetworkProxy.Enabled"
"GuestOS.System.MissingLibrary"
"GuestOS.Filesystems.UUIDConflicts"
"GuestOS.SSH.IncorrectOwnerGroup"
"GuestOS.WinCrashDump.Disabled"
"GuestOS.DHCPService.CustomPort"
"GuestOS.SysctlTcpMaxTwBuckets.Unreasonable"
"GuestOS.KMSService.MismatchedKey"
"GuestOS.WinRDPPort.Closed"
"GuestOS.System.HungTask"
"GuestOS.SSH.ProcessNotRunning"
"GuestOS.Mountpoint.Multiple"
"GuestOS.Disk.FileSystemReadOnly"
"GuestOS.SysPrepService.InitFailed"
"GuestOS.Memory.OutOfMemoryKillingEvent"
"GuestOS.WinDiskStatus.Offline"
"GuestOS.RDPService.Unavailable"
"GuestOS.RDP.ForceGuestAccess"
"GuestOS.Memory.HighUtilization"
"GuestOS.SSH.SSHDTFail"
"GuestOS.FileSystems.PartitionUnaligned"
"GuestOS.SystemDisk.Corrupted"
"GuestOS.VirtIOVersion.Low"
"GuestOS.WinDiskStatus.MftOverload"
"GuestOS.WinMemory.LicenseCorrupted"
"GuestOS.NetworkInterfaceMultiQueue.Disabled"
"GuestOS.RDP.BlockedByFirewall"
"GuestOS.VirtIODriver.DiskIDConflicts"
"GuestOS.Network.InvalidDefaultGateway"
"GuestOS.Filesystem.RequireDirectory"
"GuestOS.WSUS.Disconnected"
"GuestOS.SELinuxService.Enabled"
"GuestOS.Disk.RootFileSystemCheckFailed"
"GuestOS.SystemDisk.InsufficientSpace"
"GuestOS.Filesystem.InvalidDirectorySymlink"
"GuestOS.Filesystem.BadGeometryEvent"
"GuestOS.FstabFile.MountRootDirNotFound"
"GuestOS.SysctlNetfilterNfMaxConnections.Unreasonable"
"GuestOS.Memory.OOM"
"GuestOS.System.ResourcesUnavailable"
"GuestOS.CloutinitService.StartFailed"
"GuestOS.FstabFile.MountFeatureConflict"
"GuestOS.Operation.InfluencedByAntivirusProcess"
"GuestOS.WinActivationStatus.False"
"GuestOS.System.OSPanic"
"GuestOS.RDP.CertExpired"
"GuestOS.Disk.Ext4FilesystemError"
"GuestOS.SystemFile.LostOSReleaseFile"
"GuestOS.WinFirewall.Enabled"
"GuestOS.Disk.NFConntrackTableFull"
"GuestOS.System.SoftLockup"
"GuestOS.SSH.IncorrectSSHFilePermission"
"GuestOS.WinRdpDependentService.Failed"
"GuestOS.SysctlIPv4TCPTWRecycle.Enabled"
"GuestOS.FstabFile.LossMountDevice"
"GuestOS.WinCPU.HighUtilization"
"GuestOS.KMSService.Disconnected"
"GuestOS.Account.Locked"
"GuestOS.HugePageSize.UnreasonableConfig"
"GuestOS.SysctlIPv4TCPSACK.Disabled"
"GuestOS.Mountpoint.Overlap"
"GuestOS.SystemUser.MissingInfo"
"GuestOS.SysctlUnknownNmiPanic.Enabled"
"GuestOS.WinNetworkInterface.LackIPV4Address"
"GuestOS.WinCoreCPU.HighUtilization"
"GuestOS.SSHD.ForbiddenPassword"
"GuestOS.Network.InvalidNetmask"
"GuestOS.Filesystem.RequireFile"
"GuestOS.RDP.BlockedByConfig"
"GuestOS.CPU.HighUtilization"
"GuestOS.Disk.UEFIBootFailed"
"GuestOS.FstabFile.InvalidFormatExists"
"GuestOS.SSH.ForbiddenRootLogin"
"GuestOS.DBus.NeedUpgrade"
"GuestOS.WinMemory.HighUtilization"
"GuestOS.SSH.MissingCriticalFileOrDirectory"
"GuestOS.SystemPatch.Incorrect"
"GuestOS.Filesystem.InvalidPermission"
"GuestOS.WinPort.Conflict"
"GuestOS.CloutinitService.BadDriverStatus"
"GuestOS.WinNetworkInterface.Disabled"
"GuestOS.WinLicence.Expired"
"GuestOS.SPPSVCService.Unhealthy"
"GuestOS.Filesystem.LostCriticalFile"
"GuestOS.WinFileSystem.InsufficientSpace"
"GuestOS.Metaserver.Disconnected"
"GuestOS.Network.NetworkServiceStartFailed"
"GuestOS.SSH.ListeningPortMismatchWithConfig"
"GuestOS.FileSystem.InvalidFileSymlink"
"GuestOS.DiskFilterDriver.Vestigital"
"GuestOS.SSH.IncorrectOwnerUser"
"GuestOS.TimeSyncService.Disabled"
"GuestOS.VirtIODriver.Low"
"GuestOS.DHCPService.Disabled"
"Instance.Type.Xen"
"GuestOS.NetworkConfig.InvalidInterface"
"GuestOS.LimitsFile.UnreasonableConfig"
"GuestOS.Disk.FullFilesystemSpaceUsage"
"GuestOS.AuditConfig.AutoShutdown"
"GuestOS.WinDHCPService.Disabled"
"GuestOS.Disk.InvalidSecurityInfo"
"GuestOS.WinOSVersion.Low"
"GuestOS.NvmeIOTimeout.UnreasonableConfig"
"GuestOS.SysPrepService.Interrupted"
"GuestOS.SystemUserFile.InvalidExtensionAttribute"
"GuestOS.FileSystem.InnodeBitmapError"
"GuestOS.SystemFile.LostVmLinuz"
"GuestOS.Filesystem.LostCriticalDirectory"
"GuestOS.WinFiles.Missing"
"GuestOS.WinAdministrator.NotExist"
"GuestOS.FileSystem.IncorrectFilesystemType"
"GuestOS.SysctlIPv4TCPTWReuse.Disabled"
"GuestOS.FstabFile.InvalidDevice"
"GuestOS.LimitsFile.UnreasonableConfigTest"
"tdc_startup_check_fail"
"vm_packet_error_cause_fpga_error"
"memory_hardware_prediction_micron"
"memory_hardware_prediction_samsung_c"
"memory_hardware_prediction_samsung_bc"
"inspect_pyc_file_broken_mock"
"vm_hang_ubuntu_hibernate"
"cloudassitant_heartbeat_uptime_drop"
"iohub_pmd_cannot_allocate_thread"
"cpu_power_decreased"
"vm_recover_preemption_rule_matched"
"ag_vm_perf_exception"
"ag_vm_data_loss_too_much"
"dpdk_avs_util_too_high_5min_multi_user"
"guest_panic_core"
"core_process_too_much"
"cn_message_log_loss_too_long"
"netpmd_tx_fifo_full"
"key_process_segfault_too_many"
"iohub_seu_detected"
"handle_gpu_xml_config_failed"
"fpga_single_backpressure"
"dragonbox_hardware_error_need_online_repair"
"cloud_disk_reach_io_limit"
"nc_bond0_invalid"
"bare_metal_guest_kernel_hard_lockup"
"dpdkavs_cpu_part_high"
"dual_nic_error"
"single_nic_error"
"virsh_op_failed"
"vm_down_not_recovery"
"single_nic_crc_error"
"single_nic_fec_error"
"moc25_netq_exception"
"tdc_cpu_util_high"
"ramos_in_free_nc"
"nic_repair_finished"
"qemu_not_support_some_cpu_feature"
"vm_live_migrate_fail_dpdkcpu"
"iohub_precheck_failed"
"nic_error_after_cable_repair"
"nic_error_impact_vm_performance"
"cn_for_vm_kernel_virt_stack_err"
"dpdkavs_util_high_few_cpu"
"start_stop_failed_cause_migration_failed"
"iohub_open_backend_failed"
"cn_bmc_idc_order"
"vm_llc_hit_rtio_increased"
"vm_llc_hit_ratio_increased"
"cn_system_disk_open_failed"
"network_pync_stack_error"
"vhost_vq_stop"
"ecs_ebs_create_failed_not_available_iz"
"ecs_ebs_failed_resource_is_empty"
"ecs_ebs_create_failed_subnet_not_in_vpc"
"ecs_ebs_create_failed_ip_not_in_subnet"
"ecs_ebs_create_failed_vrc_route_conflict"
"hardware_memory_hot"
"single_nic_down"
"nic_online_repairing"
"system_util_high"
"memory_hardware_prediction_moc"
"fpga_prfifo_error"
"zbuf_allocate_failed"
"vm_cpu_limit_drop"
"vm_slowpath_pps_high"
"vm_inner_restart_event"
"cloudops_ops_flow_limit"
"avs_dpdk_status_abnormal"
"packet_dropped_rate_too_long"
"vm_netcpu_credit_use_up"
"blkpmd_check_fpga_qid_invalid"
"storage_queue_connect_failed"
"nc_gpu_driver_or_hw_critical_error"
"inspect_panic_cause_guest_restart_times"
"vm_netcpu_credit_low"
"cn_hang_issue_analysed"
"fpga_check_cn_pcie_link_down"
"user_event_short_time_not_migrate"
"vm_single_core_high"
"process_oom_too_much"
"fpga_pr_check_precheck_failed"
"qemu_zombie_process_exist"
"inspect_vm_perf_impact_event"
"inspect_hardware_or_driver_gpu_err"
"bare_metal_nvme_timeout"
"vm_arp_ping_timeout_ratio_high"
"avs_hotup_cause_vport_downtime"
"netpmd_tx_packet_paddr_error"
"blkpmd_cause_disk_remained"
"dpdk_avs_max_util_high_multi_user"
"pync_call_third_cmd_error"
"guestos_dump_diagnose_event"
"force_kill_vm_event"
"ecs_houyi_create_failed_subnet_ip_exhaust"
"inspect_hotup_cause_vm_error"
"cloudops_offline_success"
"fpga_hot_upgrade_event"
"cloudbox_auto_recovery_too_much"
"cn_ping_latency_high_by_sa"
"iohub_bridge_restart"
"nc_inlet_temp_high"
"nc_psu_inlet_temp_high"
"nvme_temp_high"
"memory_temp_high"
"gpu_temp_high"
"cpu_temp_high"
"hardware_other_temp_high"
"optical_module_temp_high"
"user_accept_cloudops_vm_event"
"cloudops_aep_clear_success"
"parse_xml_error_detail"
"suspect_vm_migrate_cause_nc_down"
"regionmaster_create_volume_failed"
"nic_flapping_accidentally_too_long"
"vm_reserve_memory_error"
"net_rx_queue_err"
"local_disk_sic_drop"
"vip_panic_too_many_times"
"fpga_inner_mem_leak"
"failed_to_connect_dbus"
"ehpc_vcpu_flapping"
"empty_nc_down_too_long"
"key_process_zombie"
"gpu_vfio_init_failed"
"libvirt_process_not_running"
"cpu_cgroup_config_error"
"cmci_storm_none_batch"
"xdragon_monitor_fpga_exception"
"eds_vm_steal_high"
"user_event_avoided"
"vm_no_resource_migrate_unlock_nc"
"nic_stop_and_repairing"
"pcie_write_cmd_no_resp"
"windows_os_problem"
"cn_network_card_op_err"
"special_vm_llc_contention"
"ack_node_init_failed"
"idc_asw_device_change_detail"
"avs_check_error"
"tcp_allocate_and_inuse_increased"
"network_pync_msg_hang"
"erdma_io_thread_hang"
"erdma_rto_timeout"
"fpga_check_cn_pcie_slow"
"fpga_cfg_reg_error"
"cpu8_controller_resource_contension"
"old_arch_ops_by_tuoluo"
"cost_ops_by_tuoluo"
"batch_risk_ops_by_tuoluo"
"zbuf_wrong_addr"
"virt_report_nc_exception"
"nc_nic_mtu_error"
"nc_config_consistence_batch_ops"
"cpu_tor_timeout"
"vm_slowpath_pps_too_high"
"route_single_nic_down_too_long"
"vm_rdma_driver_err"
"pcie_bus_corrected_error"
"vm_slow_io_ratio_high"
"jinlun_detect_memory_error"
"jinlun_detect_cpu_error"
"jinlun_detect_other_error"
"jinlun_detect_pcie_error"
"jinlun_detect_software_error"
"ddh_empty_nc_down_event"
"vm_vport_lost"
"ddh_empty_cn_down_event"
"cloudops_softrepair_success"
"vm_vport_lost_wild_vm"
"vm_virt_feature_unexpected"
"guestos_huge_page_unreasonable_config"
"eds_vm_critical_steal_high"
"vm_illegal_activity"
"vm_account_security_issue"
"vm_inner_shut_down_event"
"guestos_dhcp_service_disabled"
"guestos_fstab_file_invalid_device"
"guestos_rdp_force_guest_access"
"guestos_sys_prep_service_interrupted"
"guestos_virt_io_driver_disk_id_conflicts"
"guestos_file_system_lost_critical_directory"
"guestos_mount_root_readonly"
"guestos_system_file_lost_vm_linuz"
"guestos_cloud_init_service_bad_driver_status"
"guestos_selinux_service_enabled"
"guestos_mountpoint_multiple"
"guestos_cloud_init_service_start_failed"
"guestos_network_proxy_enabled"
"guestos_sysctl_net_filter_nf_max_connections_unreasonable"
"guestos_system_file_lost_init_ramfs"
"guestos_rdp_blocked_by_config"
"guestos_win_core_cpu_high_utilization"
"guestos_limits_file_unreasonable_config"
"guestos_file_system_require_file"
"guestos_mountpoint_overlap"
"guestos_win_memory_high_utilization"
"guestos_rdp_service_unavailable"
"guestos_system_disk_insufficient_space"
"guestos_win_licence_expired"
"guestos_file_system_require_directory"
"guestos_rdp_cert_expired"
"guestos_network_invalid_default_gateway"
"guestos_disk_filter_driver_vestigital"
"guestos_file_system_invalid_permission"
"guestos_win_os_version_low"
"guestos_ssh_listening_port_mismatch_with_config"
"guestos_metaserver_disconnected"
"guestos_win_files_missing"
"guestos_kms_service_mismatched_key"
"guestos_sysctl_unknown_nmi_panic_enabled"
"guestos_file_system_innode_bitmap_error"
"guestos_win_activation_status_false"
"guestos_fstab_file_invalid_format_exists"
"guestos_file_systems_partition_unaligned"
"guestos_win_firewall_enabled"
"guestos_grub_incorrect_config"
"guestos_win_cpu_high_utilization"
"guestos_Nvmeio_timeout_unreasonable_config"
"guestos_network_interface_multi_queue_disabled"
"guestos_memory_oom"
"guestos_win_port_conflict"
"guestos_cpu_high_utilization"
"guestos_ssh_forbidden_root_login"
"guestos_win_network_interface_disabled"
"guestos_system_user_not_unix_format"
"guestos_ssh_incorrect_ssh_file_permission"
"guestos_win_disk_status_offline"
"guestos_file_system_invalid_directory_symlink"
"guestos_fstab_file_loss_mount_device"
"guestos_sysctl_tcp_max_twbuckets_unreasonable"
"guestos_rdp_blocked_by_firewall"
"guestos_audit_config_auto_shutdown"
"guestos_win_network_interface_lack_ipv4_address"
"guestos_file_system_invalid_file_symlink"
"guestos_virt_io_driver_low"
"guestos_file_system_bad_geometry_event"
"guestos_sppsvc_service_unhealthy"
"guestos_kms_service_disconnected"
"guestos_type_xen"
"guestos_time_sync_service_disabled"
"guestos_ssh_dt_fail"
"guestos_sysctl_ipv4_tcp_tw_recycle_enabled"
"guestos_sysctl_ipv4_tcp_twreuse_disabled"
"guestos_win_disk_status_mftoverload"
"guestos_wsus_disconnected"
"guestos_network_invalid_netmask"
"guestos_win_administrator_not_exist"
"guestos_system_user_invalid_extension_attribute"
"guestos_ssh_missing_critical_file_or_directory"
"guestos_sysctl_ipv4_tcpsock_disabled"
"guestos_dhcp_service_custom_port"
"guestos_dbus_needupgrade"
"guestos_file_systems_uuid_conflicts"
"guestos_system_file_lost_os_release_file"
"guestos_udev_mac_address_not_exist"
"guestos_operation_influenced_by_antivirus_process"
"guestos_win_rdp_port_closed"
"guestos_sys_prep_service_initFailed"
"guestos_network_config_invalid_interface"
"guestos_file_system_lost_critical_file"
"guestos_win_file_system_insufficient_space"
"guestos_win_rdp_dependent_service_failed"
"guestos_system_user_missing_info"
"guestos_fstab_file_mount_feature_conflict"
"guestos_win_dhcp_service_disabled"
"guestos_system_disk_corrupted"
"guestos_win_crash_dump_disabled"
"guestos_memory_high_utilization"
"guestos_system_patch_incorrect"
"guestos_win_memory_license_corrupted"
"guestos_virtio_version_low"
"guestos_firewall"
"guestos_fstab_file_incorrect_file_system_type"
"multi_xdragon_cn_down"
"multi_xdragon_cn_busy_too_long"
"multi_xdragon_cn_hang_too_long"
"batch_vm_start_failed_in_nc"
"key_process_missed"
"blkpmd_open_disk_duplicated"
"avs_monitor_warning_too_long"
"vm_system_io_bps_high_limited"
"cancel_vm_down_migrate"
"mock_test_feature"
"dpdk_memseg_error"
"guestos_account_locked"
"guestos_disk_invail_security_info"
"nc_oob_status_off"
"guestos_sshd_forbidden_password"
"dpdk_memseg_error_impact_hotup"
"vm_attach_create_vport_failed"
"moc20_fpga_soft_fault_pr_check"
"moc20_fpga_soft_fault_static_check"
"moc15_fpga_soft_fault_pcie_error"
"fpga_soft_fault_ram_error"
"moc15_fpga_hard_fault_moc_cpu"
"fpga_soft_fault_pr_sum_check"
"moc15_fpga_soft_fault_mod_error"
"moc20_fpga_hard_fault_rctimeout"
"fpga_soft_fault_seu"
"moc15_fpga_soft_fault_human_pr"
"moc15_fpga_soft_fault_virtio_ram"
"syslog_service_error"
"ht_pedestal_start"
"ht_pedestal_end"
"cloudbox_bandwidth_full"
"vm_netcpu_reach_to_quota"
"guestos_ssh_process_not_running"
"guestos_disk_mount_root_dir_not_found"
"guestos_ssh_incorrect_owner_group"
"guestos_ssh_incorrect_owner_user"
"vm_iohub_check_nvme_error"
"vm_iohub_check_virtio_error"
"nc_iohub_check_fpga_error"
"vm_vsock_icmp_ping_loss_recovery"
"dmar_exception"
"vm_vport_lost_too_long"
"fpga_check_cn_pcie_slow_too_many"
"create_order_out_guarantee"
"virtio_nobuf_drop"
"idc_network_device_error"
"customer_image_problem"
"rusty_nc_proxy_failed"
"key_gpu_vm_start_failed"
"cloud_disk_reach_bps_limit"
"inspect_fpga_err_iohang_by_pmd"
"live_migrate_on_dst_nc_event"
"certificate_expired"
"cannot_fork_thread_or_process"
"memory_hardware_prediction_samsung"
"vm_deep_hibernate"
"vm_virtio_nobuf_drop"
"other_vm_deep_hibernate"
"vm_start_paused"
"vm_check_health_assistagent_fail"
"softrepair_not_allowed"
"guestos_sys_enter_mode"
"guestos_sys_os_panic"
"guestos_disk_file_sys_read_only"
"guestos_network_net_service_start_failed"
"guestos_disk_table_full"
"guestos_sys_hung_task"
"guestos_sys_mess_lib"
"guestos_disk_io_error"
"guestos_mem_event"
"guestos_disk_sys_error"
"guestos_disk_usage"
"guestos_sys_resources_unavailable"
"guestos_disk_uefi_failed"
"guestos_sys_soft_lockup"
"guestos_disk_miss_disk_by_uuid"
"guestos_disk_root_failed"
"nc_short_ping_loss_by_ag_fastpath"
"inspect_nc_down_affect_vm_fastpath"
"vm_virtio_csum_too_much"
"tdc_pangu_session_timeout"
"mem_bw_node_almost_full"
"nvme_driver_issue_cause_vm_err"
"nc_template_changed"
"baremetal_start_kernel_assign_mem_err"
"vm_mem_bw_occupy_too_large"
"nc_dns_ping_fatal_error"
"seu_report_unknown_pattern"
"nc_cpu_cache_error_warning"
"cpu_data_cache_unit_error"
"cpu_pysical_itself_error"
"cpu_mlc_l2_cache_error"
"nc_cpu_socket_tdp_reached"
"cpu_cahce_consistence_module_error"
"cpu_instruction_fetch_unit_error"
"cpu_data_translation_lookup_buffer_error"
"open_vm_down_migration"
"pcie_slow_cn_fix_fail"
"eci_gpucard_lost_detect"
"gpu_vm_inner_restart_event"
"virt_key_process_hang_task"
"dpdkcpu_part_high_fastpath"
"cpu_micro_upgrade"
"cpu_security_intel00950"
"nc_shutdown_ops"
"idc_repair_order_unexpected_confirm"
"gpu_memory_hardware_prediction"
"xdragon_cn_down_fastpath"
"vm_vcpu_freq_flapping"
"vm_steal_too_many"
"user_report_host_hang"
"user_report_host_down"
"vm_guest_network_service_error"
"cpu_mca_pcie_error"
"cpu_mca_nbio_error"
"cpu_mca_cs_error"
"cpu_mca_umc_error"
"cpu_mca_mp5_error"
"cpu_mca_ex_error"
"cpu_mca_de_error"
"cpu_mca_ls_error"
"cpu_mca_pie_error"
"cpu_mca_psp_error"
"cpu_mca_smu_error"
"cpu_mca_fp_error"
"cpu_mca_l3_error"
"cpu_mca_pb_error"
"cpu_mca_l2_error"
"cpu_mca_if_error"
"amd_cpu_mca_error"
"vm_hang_event_end"
"local_disk_reach_io_limit"
"local_disk_reach_bps_limit"
"ddh_vm_down_not_recovery"
"iohub_pmd_wait_io_too_long"
"vm_arp_rtt_increase"
"vm_mem_bw_occupy_too_large_small_type"
"ebs_recover_for_iohang_fail"
"xdragon_bare_metal_kernel_panic"
"bm_to_io_pcie_block"
"dpdkavs_cpu_part_high_multi_user"
"dpdk_avs_util_high_3min"
"inspect_shmem_memory_leak"
"tdc_thread_polling_slow"
"special_vm_running_event"
"dpdk_avs_util_high_single_user"
"blkpmd_communicate_iohub_ctrl_error"
"vm_expect_status_not_matched_long_time"
"batch_ops_cause_cnforvm_error"
"nic_aoc_status_abnormal"
"nic_aoc_status_alert"
"vm_vcpu_freq_exception"
"system_util_high_control"
"second_pcie_ltssm_status_error"
"mem_bw_increase_high"
"iohub_upgrade_event"
"resource_rotation_to_bm_fail"
"vm_all_vcpu_util_high"
"fpga_bm_drop"
"alarm_agent_exceed_max_socket_fastpath"
"virt_report_moc_panic_event"
"inspect_hardware_error_cause_nc_hang"
"vm_vport_lost_fatal_too_long"
"fpga_bm_drop_with_emr"
"nic_port_out_error_high"
"vm_end_to_end_loss_rate_high_too_long"
"tdc_mode_unexpected"
"vm_heartbeat_loss_too_long_fatal"
"nc_session_too_large_fatal"
"idc_change_event_server_may_lost_power"
"vm_all_vcpu_util_high_new_start"
"nc_llc_contention"
"fpx_hw_non_fatal_err_too_many"
"jinlun_detect_moc_hardware_error"
"base_kernel_sched_cause_panic"
"base_kernel_net_cause_panic"
"base_kernel_fs_cause_panic"
"base_kernel_mm_cause_panic"
"base_kernel_other_cause_panic"
"virt_kernel_model_err_cause_panic"
"taiji_model_cause_kernel_panic"
"kernel_model_err_cause_panic"
"fpga_seu_error"
"fm7_net_vgroup_error_fatal"
"fm7_net_vgroup_error"
"ha_live_migrate_event"
"vm_all_vcpu_util_high_ak_leak"
"diagnose_nmi_crash"
"tdc_block_storage_backend_comm_error"
"inspect_moc_cpu_error"
"inspect_hardware_other_error_down_ratio"
"memory_error_cause_panic_from_vmcore"
"memory_configuration_error"
"jinlun_detect_memory_error_exclude_ce"
"hardware_pcie_switch_error"
"vmoc_vm_hang_too_long"
"vmoc_vm_hang"
"vmoc_vm_ping_all_loss"
"vmoc_vm_ping_part_loss"
"IDC批量网络抖动"
"pingmesh_latency_error_in_idc"
"vmoc_vm_disk_almost_full"
"cn_memory_fragmentation"
"vm_tx_limit_cause_netcpu_high_too_much"
"escape_live_migrate"
"vmoc_vm_oom_err"
"offline_process_too_long"
"test_pod_feature"
"vm_batch_hang_in_nc"
"ak_leak_event"
"jinlun_detect_memory_ce_error"
"ipmi_memory_smi_print_too_much"
"vm_dpdkcpu_preempting_adjust"
"taiji_mem_assign_slow"
"inspect_kernel_vmem_error"
"taiji_adjust_water_level"
"test_aliUid_feature"
"acs_flex_vm_cpu_use_high"
"none_critical_seu_err"
"fpga_ddr_double_bit_ecc"
"fpga_ddr_single_bit_ecc"
"fpga_pull_blk_desc_err"
"ak_remove_event"
"vm_net_queue_error"
"vm_net_queue_error_fatal"
"cloudassitant_heartbeat_loss_after_upgrade"
"cloudassitant_process_killed_exception"
"llc_contension_potential"
"eci_vm_retained_in_nc"
"vm_gshellcheck_not_ok_before_upgrade"
"process_memory_leak_teamd"
"taiji_ept_cnt_error_critical"
"fpga_len_field_err"
"taiji_ept_cnt_error_warning"
"nc_heartbeat_loss_by_xhp_fastpath"
"nc_tsc_unstable"
"aliuid_ak_leak_event"
"teamd_iohub_critical_error"
"vm_migrate_nc_event"
"vm_pcie_backpressure_drop"
"guest_os_not_response_normal"
"virt_release_post_check_failed"
"guest_os_key_process_error"
"llc_contention_potential"
"nc_vport_over_flow"
"release_blocked_by_exception_virt"
"hyper_panic_yitian"
"y_cable_error"
"fpga_ram_error"
"iohub_pmd_wait_io_too_long_povserver"
"pcie_switch_error"
"aliuid_ak_leak_70_high_load"
"vm_under_tlp_ddos"
"nc_total_traffic_too_high"
"test_vm_feature"
"hotupgrade_bug_cause_panic"
"df20_symbolfindbug_cause_panic"
"odps_tlbissue_cause_panic"
"softirq_deadlock_cause_panic"
"kernel_known_issue_cause_panic"
"hardware_bitrevert_cause_panic"
"storage_level_high"
"vm_public_bps_limited"
"sysrq_crash"
"test_nc_feature"
"fpga_order_out_of_warranty"
"vm_all_vcpu_high_new_start_low_gc"
"user_batch_create_multi_region_nomal_risk"
"user_batch_create_multi_region_high_risk"
"op_timeout_cause_cpu_error"
"dpdkcpu_max_util_high"
"cn_for_vm_kernel_kvm_fatal_err"
"virt_upgrade_cause_panic"
"tdc_io_thread_busy_wait_io"
"ddr_calibration_not_done"
"inspect_nc_down_affect_vm_fastpath_new"
"down_start_trigger_fastpath_new"
"ram_leak_event_aliuid"
"inspect_aliuid_ak_leak_70"
"controller_cpu_100_repeatedly"
"offline_process_too_long_day"
"hardware_regfault_cause_panic"
"user_high_load_vm_too_many"
"user_high_load_vm_too_many_high_risk"
"release_blocked_by_exception_storage"
"exclusive_group_process_unexpected_iohub"
"vm_eni_hang"
"user_high_load_vm_too_many_medium_risk"
"iohubfwd_hang"
"kvm_mark_vm_sleeping"
"virtio_desc_exception"
"kernel_fault_cause_panic"
"virt_fault_cause_panic"
"hardware_cause_panic"
"hardware_cause_panic_corrected"
"virt_fault_cause_panic_corrected"
"kernel_fault_cause_panic_corrected"
"gpu_guestos_diagnose_status"
"fpga_soft_fault_sum_check"
"fpga_prchk_error"
"fpga_fifo_error"
"vm_attack_event"
"vm_attack_event_fatal"
"frequency_reduction_potential"
"live_ha_migrate_finish"
"memory_hardware_prediction_gen7x8"
"fpga_bm_underflow"
"membw_contention_potential"
"vm_attack_too_many"
"dpdkcpu_util_high_multi_vip_user"
"nc_split_lock"
"nc_multi_vm_split_lock_exception"
"nc_storage_high"
"guest_os_panic"
"vm_lost_irq_hanlder"
"repeated_fpga_error_occurred"
"receive_poisoned_data_from_memory"
"received_poisoned_data_from_memory"
"vm_live_migrate_dpdkcpu"
"inspect_mining_instance"
"fpga_ram_err_fm7_e6"
"nic_doorbell_error"
"nc_cpu_socket_tdp_reached_intel_6"
"hardware_other_uce"
"idc_fpga_order"
"avs_upgrade_failure"
"memory_uce_mcelog"
"ag_mem_less"
"user_accept_event_to_avoid_issue"
"vm_iohub_check_nvme_sq_error"
"network_process_runq_delay"
"storage_process_runq_delay"
"mlx5_timeout_cause_resource_leak"
"nc_moc_kernel_warning_error"
"system_util_high_storage"
"iohub_pcie_process_err_tlp_timeout"
"monitor_instruction_hign_error"
"gpu_fabric_manager_process_quit"
"iohub_ctrl_process_crash"
"vport_online_failed"
"xdragon_resize_disk_event_xen"
"vm_io_hang_test_oxs"
"vm_iohang_too_many_drill_test"
"inspect_mining_uid"
"vm_forbid_to_live_migrate_dpdkcpu"
"nc_avx_command_cause_tdp_full"
"jinlun_detect_memory_fatal_error"
"uid_batch_create_instance"
"hardware_power_error_old_nc"
"nc_iohub_check_error"
"ecs_vgroup_credit_drop"
"virt_report_cn_fatal_event"
"monitor_instruction_high_error"
"fpga_error_classified"
"fpga_soft_fault_fatal"
"wait_handshake_from_qemu_failed"
"fpga_check_cn_pcie_lane_slow"
"ddm_fix_error"
"uid_vm_all_vcpu_util_high"
"vmoc_guest_os_hang"
"vmoc_guest_os_panic"
"instance_compute_perf_loss_by_ai"
"amd_pcie_tlp_fatal_error"
"vm_netcpu_recover_failure"
"fpga_crc_mismatch_ur_data"
"cpu_fw_error_cause_panic"
"bare_metal_oob_op_failed"
"memory_hardware_ensemble_memory_all_taat"
"memory_hardware_xgb_memory_all"
"internal_cable_critical_error"
"service_vm_rm_init_adapter_fail"
"vm_guest_process_segment_fault"
"memory_hardware_prediction_hynixc"
"nic_offline_repair"
"vm_irq_lost_err"
"vmem_memory_management_module_error"
"vm_llc_occ_dip"
"process_memory_leak_flowlog_mon"
"vnic_single_rx_mode"
"iohub_unexpect_exit"
"process_memory_leak_virtgatherman"
"ncrs_unhealth"
"gpu_ecc_error"
"gpu_firmware_boot_fail"
"vm_restart_recreate_start_failed"
"vm_acquire_erdma_qp_fail"
"virt_report_exception_ping_failed"
"vm_nvme_probe_failed"
"gpu_lostcard"
"vm_deep_hibernate_wakeup"
"trigger_gpu_local_inspect"
"cn_exec_power_on"
"sda_predicted_to_fault"
"multi_host_cn_down_mlock"
"nc_down_prediction_for_vip_with_tag_test"
"memory_hardware_prediction_xgb_bank_agg_v1"
"libvirtd_cgroup_mem_less_cn"
"cpu_bank_uce_occured"
"dpdk_avs_util_too_high_15min_multi_user"
"nc_dpdk_high_live_migrate_too_many"
"memory_hardware_prediction_samsung_repeat"
"iohub_receive_cn_reset_msg"
"recover_vm_fail_event"
"hardware_mce_bank0"
"tdx_ept_misconfig_err"
"vm_core_generated"
"avs_upgrade_emergency"
"memory_hardware_taat_group_embedding_window_feature_min"
"sysvm_unhealthy_need_restart"
"acs_pod_crashed_need_ops"
"sysvm_crashed_need_start"
"sysvm_reboot_need_sync"
"monitor_data_loss_for_days"
"vm_end_to_end_loss_too_many_in_nc_warning"
"memory_mce_low_warning"
"vm_local_disk_slow_io"
"avs_upgrade_emergency_impact_vm"
"host_gpu_driver_error"
"vm_disk_full"
"avs_root_disk_full"
"vm_indrop_need_check_cpu"
"vm_fstab_config_error"
"nc_offline_event"
"ops_restart_nc_by_ops"
"nc_oversell_error"
"rlock_ready_but_not_recovery"
"regionmaster_system_error"
"rlock_not_required"
"rlock_overused_cause_not_migrate"
"res_idc_stop_supply"
"virt_get_backend_cluster_failed"
"vport_not_enough"
"start_vm_failed_mem_less"
"mem_less_alarm_agent_error"
"resource_change_cause_iohang"
"numa_config_error"
"virt_storage_flow_control"
"local_disk_vm_create_failed"
"create_device_failed"
"vpc_controller_busy"
"snapshot_cause_create_failed"
"ip_resource_not_enough"
"find_no_nclist_for_schedule"
"vm_resource_issue"
"start_system_error"
"unknown_error_cause_migrate_failed"
"query_vpc_version_internal_error"
"vm_down_load_iso_failed"
"pync_exit"
"vpc_operation_speed_limit"
"nc_controller_warning"
"connect_pync_failed"
"vpc_async_task_timeout"
"pync_start_vm_failed"
"vm_critical_operation_failed"
"regionmaster_workflow_slow"
"vm_disk_in_arrears"
"cpu_credit_less"
"vm_locked_by_security"
"fpga_error_recovery"
"nc_down_restart_by_discovery"
"nc_drop_cache"
"gpu_error_notify"
"nc_mlock_event"
"nc_capacity_change_dpdk_check_failed"
"force_restart_nc_event"
"nc_disk_online_repaired"
"local_disk_vm_restart"
"resource_empty_vm_down_recovery"
"auto_submit_idc_order"
"cn_down_migrate"
"cn_down_flag_local_disk"
"nc_down_flag_local_disk"
"nc_down_migrate"
"error_vm_start_event"
"error_vm_restart_event"
"vm_down_end"
"vm_down_not_recovery_resource_issue"
"agent_group_oom"
"monitor_data_loss_5min"
"user_migrate_vm_event"
"cloudops_vm_recovery_failed"
"restart_nc_twice"
"nc_hang_recovery"
"vm_live_migrate_cancel"
"vm_redeploy_failed"
"pync_udp_lost"
"monitor_data_loss_too_long"
"ecs_controller_api_failed"
"nc_need_restart_but_not_recover"
"alarm_agent_runtime_error"
"user_stop_reboot_event"
"user_diagnose_vm_event"
"vm_down_not_recovery_none_resource_issue"
"reonline_nc_event"
"vm_stop_unexpected_not_not_recovery"
"vpc_controller_cgroup_oom"
"ecs_controller_cgroup_oom"
"avs_controller_exception"
"nc_vpc_control_proces_crash"
"vport_operation_failed"
"attach_create_vport_failed"
"nc_info_format_issue_cause_error"
"virt_package_upgrade"
"nc_controller_pakage_upgrade"
"storage_pakage_upgrade"
"avs_package_upgrade"
"idc_repair_order_closed"
"vpc_api_rate_limit"
"xdragon_resize_disk_event"
"regionmaster_startvm_lazyload"
"nc_down_12_hour"
"vm_migrate_cause_nc_down"
"inspect_hdd_random_read_buzy"
"win_virtio_driver_issue"
"guest_slot_not_enough"
"vm_filesystem_error"
"vm_gpu_fallen_issue"
"intel_microcode_error_cause_vm_error"
"eci_os_panic"
"vm_guest_os_panic"
"xdragon_cn_kernel_panic"
"eci_os_oom"
"guest_os_oom"
"eci_system_hang_task"
"vm_guest_os_hang"
"physical_machine_guest_hang_task"
"guest_cn_kernel_hang_outer_user"
"vm_network_flooding"
"vm_vsock_icmp_ping_loss"
"vm_arp_increase"
"cn_arp_ping_timeout_too_long"
"guest_os_not_response"
"vm_network_retry_high"
"physical_machine_mem_frag"
"gpu_driver_issue"
"physical_machine_user_reboot"
"eci_vm_core_process_oom"
"xdragon_cn_vda_hang"
"nc_lost_power_cause_nc_down"
"power_supply_backplane_error"
"disk_backplane_error"
"sda_error"
"system_readonly"
"memory_uce"
"pcie_gpu_error"
"fpga_data_error"
"cpu_error"
"inspect_cpu_error"
"pcie_bus_fatal_error"
"non_fatal_pcie_uce"
"mpt2sas0_error"
"fatal_motherboard_error"
"motherboard_error"
"sata_ssd_expire"
"pcie_flash_error"
"xdragon_cn_fatal_hardware_error"
"memory_error_cause_nc_down"
"nc_fatal_hardware_error_down"
"data_disk_error_nc_down"
"network_card_fatal_hang"
"network_card_uce_packet"
"network_card_ecc_packet"
"nic_error_cause_nc_down"
"disk_repair_finished"
"data_disk_error_local_hdd"
"data_disk_error_local_ssd"
"vm_local_disk_error_conman"
"nc_lost_power"
"memory_ce_error"
"hardware_cause_nc_down_low_ratio"
"nc_ce_low_warning"
"xdragon_cn_down"
"xdragon_cn_reboot"
"xdragon_hardware_unknown_fatal_error"
"storage_controller_error_low_warning"
"cable_error"
"cable_error_online_repaired"
"inspect_gpu_error_cause_hang"
"cable_error_not_repair_order"
"nc_fake_down"
"tpm_card_error"
"smartctrl_d"
"nvme_error_cause_spoold_exception"
"nc_hardware_warning"
"motherboard_low_warning_error"
"inspect_cpu_error_cause_vm_paused"
"hardware_error_not_disk"
"fpga_error"
"unknown_power_error"
"inspect_memory_error"
"memory_hardware_prediction"
"fpga_temperature_error"
"cn_pcie_link_down"
"cpu_instruction_fetch_error"
"data_disk_error_cause_start_vm_failed"
"offline_page_failed"
"fpga_power_error"
"power_exception"
"pcie_ssd_life_cycle_end"
"unable_to_power_on"
"cmci_storm"
"qemu_mce_killed"
"expander_card_error"
"network_card_error"
"mem_hardware_corrupted_too_much"
"hardware_memory_lost"
"mce_count_too_much"
"unfinished_repaired_order_exclude_disk"
"data_disk_error"
"fan_error"
"edac_hardware_error"
"memory_page_error_high_edac"
"hardware_fault_affect_process"
"key_process_mce_killing"
"mce_too_much_short_time"
"hardware_error_erase_disk_failed_xdragon"
"avs_exception_vm_ping_failed"
"xdragon_local_device_error"
"hardware_error_false_positives"
"nvme_error_cause_vm_panic"
"moc_cn_bmc_link_error"
"dragonbox_cn_bmc_link_error"
"cn_bmc_ping_error"
"user_report_hardware_error"
"io_block_broke_raid5_auto_recovery"
"idc_network_drill"
"idc_lost_power"
"asw_exception"
"idc_miss_operation"
"vm_network_loss_drill"
"nc_down_drill"
"nc_hang_drill"
"tcp_retry_high_gather"
"inspect_asw_error_cause_vm_retry_high"
"offline_test_cause_power_high"
"rack_power_gpu"
"rack_power_for_sell_rate"
"rack_power_error"
"idc_lost_power_dual"
"idc_lost_power_single"
"idc_asw_network_exception"
"kernel_npe"
"low_critical_kernel_issue"
"cn_system_crash"
"system_crash"
"system_key_process_exception"
"memory_fragmentation_start_vm_failed"
"memory_fragmentation"
"tianji_pingmesh_down"
"nc_down_active"
"upgrade_nc_down_active"
"nc_real_down"
"nc_down_uniform_check_error"
"vm_cpu_contention"
"qemu_kvm_hang_task"
"pangu_hang_task"
"system_process_hang"
"kernel_error"
"cpu_util_high_cause_nc_hang"
"sda_disk_util_exception"
"nc_hang_too_long"
"nc_ssh_latency_too_high"
"cn_ssh_latency_too_high"
"nc_ssh_latency_increase"
"ssh_latency_high_too_long"
"xdragon_cn_ping_loss_too_much"
"xdragon_cn_hang_too_long"
"xdragon_cn_busy_too_long"
"nc_hugepage_issue"
"nc_network_flow_high"
"packet_dropped_rate_too_much"
"zombie_process_too_mush"
"zombie_process_increase"
"tcp_retry_too_high"
"nc_cpu_runq_error"
"nc_load_fatal_exception"
"nc_load_critical_exception"
"nc_cpu0_util_high"
"network_transmit_cgroup_usage_high"
"fusion_nc_storage_core_cpu_high"
"dbus_error"
"nc_controller_resource_contension"
"controller_root_disk_full"
"tdc_root_disk_full"
"virt_root_disk_full"
"root_disk_full"
"sda_system_error"
"local_hdd_filesystem_error"
"nc_memory_less_start_failed"
"cloud_nc_disk_hang"
"nc_host_mem_less"
"nc_host_mem_less_3G"
"nc_host_mem_fatal_less"
"nc_mem_frag_cause_control_error"
"nc_key_process_core"
"libvirtd_cgroup_mem_less"
"qemu_oom"
"storage_network_key_process_oom"
"cn_key_process_oom"
"cn_hang"
"cn_busy"
"kworker_d"
"down_start_trigger"
"pingmesh_latency_exception"
"nc_hang_3min"
"inspect_nc_down_affect_vm"
"process_oom"
"regionmaster_judge_nc_down"
"nc_kernel_fatal_hang_task"
"inspect_sda_system_error"
"data_disk_util_error_too_much"
"nc_hang"
"process_allocate_mem_failed_cause_hang"
"memory_less_start_process_failed"
"high_order_memory_less_to_trim"
"nc_kernel_hard_lockup"
"bcache_exception"
"vm_pin_failed"
"virtStorage_local_disk_op_failed"
"nc_virtStorage_compute_critical"
"vm_resize_fs_error"
"iohub_pcie_issue"
"virt_hook_script_exec_failed"
"vm_virtStorage_warning"
"vm_image_warning"
"xdragon_vhost_blk_failed"
"nc_virtStorage_warning"
"nc_controller_process_core"
"iohub_pcie_process_error"
"qemu_command_internal_error"
"libvirt_connect_failed"
"virt_version_error"
"virt_gpu_resource_parse_error"
"prepare_vm_failed_by_script"
"resource_retension_in_nc"
"virt_start_stop_vm_failed"
"storage_issue_cause_libvirt_hang"
"live_migration_cause_libvirt_hang"
"virt_error_cause_start_stop_failed"
"iso_vm_still_in_nc"
"hostdev_detach_timeout"
"virt_attach_detect_disk_failed"
"vm_cgroup_set_failed"
"cpu_error_cause_vm_paused"
"vm_livemigrate_crash"
"vm_crash"
"avs_key_process_exception"
"nc_vpc_critical"
"nc_virtStorage_storage_nc_critical"
"dpdk_single_core_full"
"vm_ntp_exception"
"storage_key_process_restart"
"vm_live_migrate_event"
"qemu_hot_upgrade"
"dpdk_avs_util_high"
"xdragon_key_process_upgrade_event"
"vm_crash_end"
"iohub_avs_failed"
"gpu_key_process_oom"
"xenwatch_d"
"spoold_internal_error"
"storage_service_core"
"inspect_wild_vms"
"iohub_pcie_process_crash"
"vm_io_hang_end"
"hypervisor_process_core"
"process_init_memory_failed"
"xdragon_key_process_core"
"hvm_virtio_initial_failed"
"virtio_initial_failed"
"qemu_host_mem_too_large"
"mcelog_core"
"vm_io_hang"
"vm_slow_io"
"nc_resource_contention"
"vm_system_network_perf"
"vcpu_steal_high"
"net_dev_in_drop"
"vm_network_drop"
"vm_rx_acl_drop_packect"
"vm_tx_acl_drop_packect"
"vm_steal_high_exclusive"
"cloud_disk_qos_by_tdc"
"vm_split_lock_cause_perf_drop"
"vm_network_credit_less"
"ecs_ddos_ip_black_hole_add"
"ecs_ddos_netflow_defense"
"vmexit_error_after_down_migrate"
"vm_cloudassitant_heartbeat_loss"
"host_mem_oom_may_cause_hang"
"brctl_hang"
"inpsect_nfs_hang"
"cloud_disk_qos_by_tdc_for_nc"
"high_order_mem_compact_by_ops"
"mute_nc_down_migration"
"mute_vm_down_migration"
"user_report_instance_error"
"vm_disk_full_cause_start_failed"
"hardware_motherboard_critical"
"power_error_cause_nc_down"
"idc_network_error_by_huoshui"
"agent_hook_error_monitor_data_error"
"cn_for_vm_crash"
"vm_vport_pps_bps_limit"
"slb_session_reset_by_live_migration"
"batch_ops_by_tuoluo"
"perf_drop_by_live_migration"
"avs_error_may_cause_vm_netloss"
"iowait_high_cause_nc_hang"
"block_storage_control_failed"
"vm_io_hang_in_iso"
"nc_power_off_by_api"
"nc_too_much_irq"
"nc_avs_session_resource_contention"
"vm_session_exceed_limitation"
"live_migration_by_batch_ops"
"xdragon_cn_oom_panic"
"cn_find_no_irq_handler"
"nc_dpdk_flow_drop"
"dpdk_avs_util_high_1min"
"xend_syscall_failed"
"nc_hang_restart_by_ops"
"ce_cause_dpdk_flow_drop"
"nc_avail_mem_drop_too_fast"
"vm_host_device_create_failed"
"memory_less_restart_by_ops"
"network_card_flapping"
"idc_repair_break_sla"
"virt_start_vtpm_failed"
"vtpm_process_oom"
"vtpm_process_core"
"nc_system_message_log_loss"
"virto_blk_device_create_failed"
"nic_flip_online_fix"
"kmod_access_error"
"vm_mem_page_offline"
"control_data_inconsistency"
"mem_uncorrected_patrol_scrub"
"tdc_upgrade_precheck_failed_mem"
"dragonfly20_channel_error"
"libvirt_hang_by_iohubctl"
"memory_error_cause_fw_failed"
"vm_util_high_in_power_high_rack"
"rpm_unrecovered_error"
"iohub_lacp_error"
"pync_libvirt_pool_full"
"nc_systemd_session_cnt_large"
"vm_underlay_ip_allocate_failed"
"blkpmd_cause_libvirt_hang"
"live_migrate_cause_nc_hang"
"rw_fpga_reg_failed_iohub_pcie"
"vm_ddos_cause_nc_perf_drop"
"inspect_wild_vm_cause_vm_hang"
"vm_retained_in_nc"
"pcie_device_failed_to_recovery"
"vfio_uncorrected_error"
"cn_for_vm_nfs_error"
"vm_paused"
"inspect_piracy_win_vm_io_hang"
"libvirt_hang_cause_control_failed"
"virt_error_vm_start_stop_failed"
"ecs_ddos_blackhole_ip_del"
"ecs_ddos_defense_finished"
"nc_sda_flush_io_hang"
"vm_disk_active_ops_event"
"vm_redeploy_event"
"vm_reboot_event"
"parse_xml_failed_cause_start_failed"
"iohub_net_forward_process_error"
"virt_allocate_vcpu_failed"
"cn_for_vm_cpu_offline"
"xdragon_machine_cpu_offline"
"fpga_ur_tlp_event"
"xdragon_cn_ping_100_loss"
"server_two_power_error"
"idc_asw_device_change"
"inspect_backplane_error_cause_perf_loss"
"vm_cpu_increase_high"
"nc_power_off_by_api_dragonbox"
"nc_ping_loss_by_sa"
"nc_ssh_failed_by_sa"
"pync_heartbeat_log_loss"
"nic_repair_break_sla"
"nic_enable_failed_exceed_max_retries"
"nic_enable_failed_cannot_allocate_memory"
"iohub_blk_error"
"cn_ping_loss_by_sa"
"pingmesh_latency_public_increase_high"
"start_failed_in_iso_stage"
"inspect_unknown_hardware_error_cause_panic"
"oob_error_need_shutdown_repair"
"xdragon_hardware_error_need_local_repair"
"dragonbox_hardware_error_need_local_repair"
"nc_all_resource_migrate"
"avs_error_manual_report"
"avs_dhcp_error"
"inspect_pyc_file_broken"
"hugepage_surp_too_many"
"hardware_error_need_local_repair"
"zbuf_almost_full"
"gpu_error_from_console"
"cpu_high_after_live_migration"
"nc_short_ping_loss_by_ag"
"vm_status_inconsistent"
"vm_netcpu_too_high"
"vm_systemfailure_reboot_event"
"vm_start_failed"
"detach_vport_timeout"
"vm_session_increased"
"vm_network_traffic_decreased"
"vm_network_session_drop"
"user_session_cause_nc_high"
"gpu_key_process_hang"
"asw_exception_cloudbox"
"cloudbox_nc_down_12_hour"
"cn_down_long_time_no_recovery"
"xdragon_disk_lost_exception"
"fpga_ur_check"
"nc_netdev_queue_size_adjust"
"nc_dpdk_driver_flow_drop_too_long"
"vm_heartbeat_loss_too_long"
"bare_metal_nmve_timeout"
"tdc_control_task_full"
"libvirt_zombie"
"nvme_qid_timeout"
"vm_split_lock_has_restrain"
"error_vm_notify_and_reboot_event"
"fpga_hard_fault"
"fpga_soft_fault"
"key_process_upgrade"
"nc_rpm_unhealthy"
"moc_cycle_cause_caterr_fault"
"cpu_double_fault"
"migrating_vm_down"
"cn_host_mem_less"
"nvme_qid_timeout_too_long"
"dpdkavs_cpu_high"
"iohub_int_check_error"
"nc_cpu_pressure_high"
"nc_rpm_broken"
"xdragon_hot_upgrade_fpga_perf_drop"
"nic_flapping_cause_disk_slowio"
"local_disk_drop_need_reset"
"local_disk_drop_need_offline"
"local_disk_mctp_process_error"
"fpga_afull_check"
"cloud_instance_disk_qos_by_tdc"
"brare_metal_vm_crash"
"ht_cahl_start"
"ht_cahl_end"
"dpdk_avs_util_too_high"
"pingmesh_latency_failpoint_increased"
"fpga_bdring_full_error"
"cloudops_reboot_vm_event"
"vpc_avs_control_binary_broken"
"avs_key_process_hang"
"nc_down_cause_vm_down"
"avs_not_running"
"nc_ping_loss_by_ag"
"nc_dpdk_driver_flow_drop_too_high"
"nc_control_command_hang"
"hardware_aep_error"
"sda_partition_table_incorrect"
"check_pync_nofile"
"share_host_vcpu_util_high"
"chronyc_process_exception"
"kworker_process_exception"
"nic_dual_port_down"
"win_vminit_error"
"cn_for_vm_kernel_warning"
"bare_metal_kernel_warning"
"network_card_jitter"
"vm_guest_kernel_double_fault"
"virtio_version_may_cause_disk_loss"
"virtio_version_incompatible_with_moc"
"win_virtio_version_error_with_moc"
"virtio_version_disk_operation_failed"
"suspect_wild_vm_from_odps"
"aep_critical_error"
"create_eci_microvm_error"
"nc_power_off_by_api_start_event"
"kms_invalid_cause_disk_open_failed"
"oob_error"
"vm_perf_loss_live_migrate"
"nc_ssh_service_exception"
"nc_nic_error_dpdk_vf_error"
"recover_preemption_disk_event"
"nc_exception_oob_status_error"
"spoold_communicate_mctp_timeout"
"vm_status_error_after_live_migration"
"xdragon_cn_deadlock_memory_panic"
"gpu_lost_card_by_xid"
"fpga_common_check_error"
"gpu_driver_issue_high_risk"
"guest_gpu_app_issue"
"blkpmd_check_failed_stop_disk_queue"
"fpga_link_down"
"cn_dma_exception"
"baremetal_cn_guest_mce_kill"
"vm_iso_sysprep_interupted"
"memory_hardware_prediction_high_risk"
"wuzong_perf_exception"
"physical_machine_cn_net_loss_for_user"
"vm_iohang_too_many_in_nc"
"vm_ept_metric_increaseed"
"vm_iohang_too_many"
"vm_stop_failed"
"vm_heartbeat_loss_too_many_in_nc"
"nvme_reset_with_iohang"
"system_journald_process_exception"
"vm_start_in_bios_many_times"
"kworker_util_fix"
"chronyc_util_fix"
"nic_reset_event"
"bare_metal_console_log_too_much"
"vmexit_error_hang"
"windows_boot_error"
"vm_stuck_in_disk_load"
"rpm_process_too_much"
"vm_llc_contention"
"vm_bitcoin_mining"
"vm_llc_contention_exclusive"
"cn_load_exception"
"blkpmd_open_disk_unfinished_inflight"
"fpga_crc_check_error"
"vm_oom_panic"
"hardware_memory_risk_event"
"oob_operation_internal_error"
"fpga_cmd_ring_full"
"vm_unexpected_killed_by_other"
"vm_key_file_lost"
"vm_key_path_permission_error"
"kernel_fault_with_detail"
"suspect_hardware_error_cause_kernel_crash"
"edac_mem_error_page_too_many"
"fpga_err_cause_avs_error"
"nc_edac_mem_error_page_too_many"
"share_host_vcpu_util_large_70"
"hardware_risk_event"
"local_disk_abnormal_vm"
"ipmitool_process_too_much"
"modify_vm_can_not_recovery"
"modify_vm_support_recovery"
"cn_invalid_device_request"
"memory_page_hard_fault"
"memory_err_col_row_too_many"
"asw_port_net_flow_too_much"
"vm_paused_recovery"
"share_memory_used_too_much"
"vm_paused_warning"
"ddos_flow_limit"
"memory_err_col_row_high_risk"
"nc_edac_mem_error_page_high_risk"
"cpu_temp_error"
"predict_memory_partol_error"
"start_vm_load_kernel_failed"
"nc_env_error_cause_start_failed"
"memory_test_failed"
"ht_cahl_start2"
"vm_stuck_in_migrating_status"
"gpu_cn_high_order_memory_less"
"cpu_microcode_fault_cause_nc_down"
"split_lock_exception"
"nc_oom_panic"
"idc_power_temp_error_oob_off"
"dpdk_avs_util_too_high_5min"
"dpdk_avs_util_high_fatal"
"dpdkavs_cpu_high_multi_user"
"vm_stoped_by_account_blance"
"gpu_dbe_error"
"gpu_other_hw_error"
"nc_recover_by_preemption_disk"
"nc_process_segfault_too_many"
"vm_end_to_end_loss_too_many_in_nc"
"null_device_broken"
"bare_metal_heartbeat_loss_too_long"
"user_reboot_event_not_migrate"
"vm_heartbeat_loss_too_long_critical"
"vm_batch_panic_or_hang_in_nc"
"cable_frame_err_too_much"
"dpdkavs_cpu_high_5min"
"nc_repin_open"
"nc_repin_close"
"start_failed_device_not_found"
"nc_dpdk_driver_flow_drop_all"
"console_log_too_much"
"vm_end_to_end_loss_rate_high"
"cn_reboot_fail_in_bios_stage"
"cn_reboot_fail_in_kernel_stage"
"cn_reboot_fail_unknown_reason"
"libvirt_hang_virt_err"
"toutiao_start_physical_machine_failed"
"driver_issue_cause_panic"
"no_bootable_device_start_failed"
"high_risk_split_lock_customer_event"
"dpdkavs_cpu_high_abnormal"
"dpdkavs_cpu_high_normal"
"dpdkavs_cpu_high_multi_user_abnormal"
"dpdkavs_cpu_high_multi_user_normal"
"nc_isov_process_core"
"pync_unexpected_attribute_error"
"cn_new_system_session_increased"
"libvirtd_key_process_restart"
"eci_kernel_critical_issue_on_same_nc"
"vmem_page_offline_by_vm"
"node_pressure_high"
"ipmitool_hang"
"file_broke_cause_process_segfault"
"split_lock_unrestrain"
"iohub_check_failed"
"eci_container_agent_health"
"vm_system_io_high_limited"
"nc_ntp_issue_critical"
"nc_ntp_issue_warning"
"image_format_error"
"fpga_warning_aligned_len_error"
"fpga_warning_sumcheck_error"
"failed_to_start_daemon_virt"
"virt_cannot_open_devnull"
"virt_suspend_by_max_clients"
"qemu_kvm_core"
"data_disk_bdf_format_error"
