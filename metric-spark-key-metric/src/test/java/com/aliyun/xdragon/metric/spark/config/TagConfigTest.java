package com.aliyun.xdragon.metric.spark.config;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class TagConfigTest {

    private TagConfig tagConfig;

    @Before
    public void setUp() {
        LocalDate dayBeforeYesterday = LocalDate.now().minusDays(7);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String ds = dayBeforeYesterday.format(formatter);
        tagConfig = new TagConfig(ds);
    }

    @Test
    public void testGetTestUid() {
        List<String> uids = tagConfig.getTestUid();
        Assert.assertFalse(uids.isEmpty());
    }

    @Test
    public void testGetNoRLockCluster() {
        List<String> clusters = tagConfig.getNoRLockCluster();
        Assert.assertFalse(clusters.isEmpty());
    }

}
