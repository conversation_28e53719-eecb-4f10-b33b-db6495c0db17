package com.aliyun.xdragon.metric.spark.metric

import com.aliyun.xdragon.metric.spark.config.MonitorConfig
import org.scalatest.Suite

import scala.collection.mutable.ListBuffer

class KeyMetricUtilTest extends Suite {
  val precision = 0.0000000000001

  val keyMetricUtil: KeyMetricUtil = KeyMetricUtil(new MonitorConfig())

  def testComputeWeightedDamagedTime(): Unit = {
    val testData = ListBuffer[(String, String, Int)]()
    testData.append(("dpdk_avs_util_high", "2022-01-13 13:11:12", 3))
    testData.append(("dpdk_avs_util_high", "2022-01-13 13:11:13", 3))
    testData.append(("live_migrate_perf_degrade_event", "2022-01-13 17:50:12", 2))
    testData.append(("process_oom_exception", "2022-01-13 17:50:15", 1))
    // computeWeightedDamagedTime(testData.toList) = 2.4
    assert(2.4 - keyMetricUtil.computeWeightedDamagedTime(testData.toList) <= precision)
  }

  def testComputeDamagedTime(): Unit = {
    val testData = ListBuffer[(String, String, Int)]()
    testData.append(("dpdk_avs_util_high", "2022-01-13 13:11:12", 3))
    testData.append(("dpdk_avs_util_high", "2022-01-13 13:11:13", 3))
    testData.append(("live_migrate_perf_degrade_event", "2022-01-13 17:50:12", 2))
    testData.append(("process_oom_exception", "2022-01-13 17:50:13", 1))
    // computeDamagedTime(testData.toList)=2.0
    assert(2 - keyMetricUtil.computeDamagedTime(testData.toList) <= precision)
  }

  def testComputeUnavailableTimeWithRange(): Unit = {
    val startTime = "2023-07-15 15:07:25"
    val endTime = "2023-07-17 15:07:25"
    val records = ListBuffer[(String, String)]()
    records.append(("vm_crash_end", "2023-07-16 20:41:14"))
    records.append(("vm_crash_end", "2023-07-16 20:39:07"))
    // computeUnavailableTimeWithRange(startTime, endTime, records.toList) = 1080
    val unavailableTime = keyMetricUtil.computeUnavailableTimeWithRange(startTime, endTime, records.toList)
    assert((unavailableTime - 0).abs <= precision)
  }

  def testListUnavailableException(): Unit = {
    val startTime = "2022-01-09 00:00:00"
    val endTime = "2022-01-10 00:00:00"
    val records = ListBuffer[(String, String)]()
    records.append(("vm_crash_end", "2022-01-09 18:00:00"))
    records.append(("vm_livemigrate_exception", "2022-01-08 17:57:45"))
    // computeUnavailableTimeWithRange(startTime, endTime, records.toList) = 1080
    val unavailableEvents = keyMetricUtil.listUnavailableException(startTime, endTime, records.toList)
    assert(unavailableEvents.equals(ListBuffer(("vm_livemigrate_exception", "2022-01-09 00:00:00-2022-01-09 18:00:00", 1080.0))))
  }

  def testSortException(): Unit = {
    val records = ListBuffer[(String, String)]()
    records.append(("vm_crash_end", "2022-01-09 18:00:00"))
    records.append(("vm_crash_event", "2022-01-08 18:00:00"))
    records.append(("vm_vsock_icmp_ping_loss_new_end", "2022-01-08 18:57:45"))
    records.append(("vm_vsock_icmp_ping_loss_new_start", "2022-01-08 18:57:45"))
    val sortedRecords = records.sortWith(keyMetricUtil.sortException)
    assert(sortedRecords.equals(ListBuffer(("vm_crash_event", "2022-01-08 18:00:00"), ("vm_vsock_icmp_ping_loss_new_start", "2022-01-08 18:57:45"), ("vm_vsock_icmp_ping_loss_new_end", "2022-01-08 18:57:45"), ("vm_crash_end", "2022-01-09 18:00:00"))))
  }

  def testListUnavailableEventWithoutCoverage(): Unit = {
    val records = ListBuffer[(String, String)]()
    records.append(("nc_down_alert", "2023-08-23 02:29:53"))
    records.append(("key_customer_event_generate", "2023-08-23 02:32:51"))
    records.append(("vm_down_end", "2023-08-23 16:47:04"))
    val startTime = "2023-08-23 00:00:00"
    val endTime = "2023-08-24 00:00:00"
    val result = keyMetricUtil.listUnavailableEventWithoutCoverage(startTime, endTime, records.toList)
    assert(result.head._4 - 178.0 <= precision)
  }

  def testListUnavailableEventWithoutCoverage2(): Unit = {
    val records = ListBuffer[(String, String)]()
    records.append(("nc_down_alert", "2022-03-26 14:50:31"))
    records.append(("nc_down_alert", "2022-03-26 14:50:31"))
    records.append(("user_accept_event_to_avoid_issue_start", "2022-03-26 14:49:40"))
    records.append(("user_accept_event_to_avoid_issue_end", "2022-03-26 14:54:40"))
    records.append(("vm_down_end", "2022-03-26 14:52:34"))
    val startTime = "2022-03-26 00:00:00"
    val endTime = "2022-03-27 00:00:00"
    val result = keyMetricUtil.listUnavailableEventWithoutCoverage(startTime, endTime, records.toList)
    println(result)
  }

  def testUnavailableMonitorRefactor(): Unit = {
    val exceptionName = "user_accept_event_to_avoid_issue"
    val exceptionTime = "2022-01-10 11:00:00"
    val warningValue = "1"
    assert(keyMetricUtil.unavailableMonitorRefactor(exceptionName, exceptionTime, warningValue).equals(List(("user_accept_event_to_avoid_issue_start", "2022-01-10 11:00:00"), ("user_accept_event_to_avoid_issue_end", "2022-01-10 11:01:00"))))
    val exceptionName2 = "active_vm_ops_event"
    assert(keyMetricUtil.unavailableMonitorRefactor(exceptionName2, exceptionTime, warningValue).equals(List(("active_vm_ops_event_start", "2022-01-10 11:00:00"), ("active_vm_ops_event_end", "2022-01-10 11:03:00"))))
  }

  def testListExceptionInterval(): Unit = {
    val startTime = "2020-02-20 00:00:00"
    val endTime = "2020-02-21 00:00:00"
    val padding = true
    val exceptionName = "exception1"
    val records = ListBuffer[(String, String)]()
    records.append(("end", "2020-02-20 01:00:00"))
    records.append(("start", "2020-02-20 02:00:00"))
    records.append(("start", "2020-02-20 02:05:00"))
    records.append(("start", "2020-02-20 02:10:00"))
    records.append(("end", "2020-02-20 02:06:00"))
    records.append(("end", "2020-02-20 02:50:00"))
    records.append(("start", "2020-02-20 03:00:00"))
    records.append(("end", "2020-02-20 04:00:00"))
    records.append(("start", "2020-02-20 23:00:00"))
    var result = keyMetricUtil.listExceptionInterval(exceptionName, records.toList, startTime, endTime, true, true)
    println(result)
    result = keyMetricUtil.listExceptionInterval(exceptionName, records.toList, startTime, endTime)
    println(result)
  }

  def testSortAndFilter(): Unit = {
    val records = ListBuffer[(String, String)]()
    records.append(("key_customer_event_generate", "2022-03-26 14:50:31"))
    records.append(("nc_down_alert", "2022-03-26 15:50:31"))
    records.append(("nc_down_alert", "2022-03-26 15:50:31"))
    records.append(("user_accept_event_to_avoid_issue_start", "2022-03-26 14:49:40"))
    records.append(("user_accept_event_to_avoid_issue_end", "2022-03-26 14:54:40"))
    records.append(("vm_down_end", "2022-03-26 15:52:34"))
    records.append(("vm_down_end", "2022-03-26 15:52:34"))
    val result = keyMetricUtil.sortAndFilter(records.toList)
    assert(result.equals(List(("user_accept_event_to_avoid_issue_start", "2022-03-26 14:49:40"), ("key_customer_event_generate", "2022-03-26 14:50:31"), ("user_accept_event_to_avoid_issue_end", "2022-03-26 14:54:40"), ("vm_down_end", "2022-03-26 15:52:34"))))
  }

  def testPerformanceDurationDeduplication(): Unit = {
    val records = ListBuffer[(String, String, String)]()
    records.append(("D", "2023-05-01 06:00:00", "end"))
    records.append(("D", "2023-05-01 05:00:00", "start"))
    records.append(("A", "2023-05-01 01:00:00", "start"))
    records.append(("C", "2023-05-01 05:00:00", "end"))
    records.append(("B", "2023-05-01 02:30:00", "end"))
    records.append(("B", "2023-05-01 01:30:00", "start"))
    records.append(("C", "2023-05-01 02:00:00", "start"))
    records.append(("B", "2023-05-01 02:10:00", "start"))
    records.append(("B", "2023-05-01 03:10:00", "end"))
    records.append(("A", "2023-05-01 04:00:00", "end"))
    val result = keyMetricUtil.performanceDurationDeduplication(records.toList)
    assert(result.equals(List(("D", 3600.0), ("A", 10800.0), ("C", 3600.0))))
  }

  def testFindUnclosedStartEvent(): Unit = {
    val records = ListBuffer[(String, String, String)]()
    //    records.append(("live_migrate_on_src_nc_finish", "2024-09-22 11:01:34"))
    //    records.append(("live_migrate_on_src_nc_event", "2024-09-22 11:01:33"))
    //    records.append(("live_migrate_on_src_nc_finish", "2024-09-22 06:56:15"))
    //    records.append(("live_migrate_on_src_nc_event", "2024-09-22 06:56:15"))
    //    records.append(("live_migrate_on_src_nc_event", "2024-09-22 05:26:14"))
    //    records.append(("live_migrate_on_src_nc_finish", "2024-09-22 04:33:59"))
    //    records.append(("live_migrate_on_src_nc_finish", "2024-09-22 04:33:59"))
    //    records.append(("live_migrate_on_src_nc_event", "2024-09-22 04:33:57"))
    //    records.append(("live_migrate_on_src_nc_event", "2024-09-22 04:33:57"))
    //    records.append(("live_migrate_on_src_nc_finish", "2024-09-22 04:03:24"))
    //    records.append(("live_migrate_on_src_nc_event", "2024-09-22 04:03:23"))
    //    records.append(("live_migrate_on_src_nc_finish", "2024-09-22 04:01:16"))
    //    records.append(("live_migrate_on_src_nc_event", "2024-09-22 04:01:15"))
    //    records.append(("live_migrate_on_src_nc_finish", "2024-09-22 03:56:33"))
    //    records.append(("live_migrate_on_src_nc_event", "2024-09-22 03:56:32"))
    //    records.append(("live_migrate_on_src_nc_finish", "2024-09-22 03:31:34"))
    //    records.append(("live_migrate_on_src_nc_event", "2024-09-22 03:31:33"))
    //    records.append(("live_migrate_on_src_nc_event", "2024-09-22 03:31:33"))
    //    records.append(("live_migrate_on_src_nc_finish", "2024-09-22 03:26:21"))
    //    records.append(("live_migrate_on_src_nc_event", "2024-09-22 03:26:19"))
    //    records.append(("live_migrate_on_src_nc_event", "2024-09-22 03:26:19"))
    records.append(("live_migrate_on_src_nc_finish", "2024-09-21 20:51:03", "warning"))
    records.append(("live_migrate_on_src_nc_event", "2024-09-21 20:50:17", "warning"))
    records.append(("live_migrate_on_src_nc_event", "2024-09-21 20:50:17", "warning"))
    records.append(("vm_down_end", "2024-09-20 20:53:17", "low_warning"))
    records.append(("live_migrate_on_src_nc_event", "2024-09-20 20:50:17", "warning"))

    //    val sortedRecords = keyMetricUtil.sortAndFilter(records.toList)
    //    val preExceptionList = sortedRecords.filter(_._2 < startTime)
    //
    //        println("findUnclosedStartEvent 结果：")
    //    var result = keyMetricUtil.findUnclosedStartEvent(preExceptionList, "2024-09-21 00:00:00")
    //    println(result)

    println("listPerformanceEventWithoutCoverage 结果：")
    var newresult = keyMetricUtil.listPerformanceEventWithoutCoverage("2024-09-21 00:00:00", "2024-09-22 00:00:00", records.toList)
    println(newresult)
  }

  def testProcessing(): Unit = {
    val records = ListBuffer[MonitorWithWarning]()
    records.append(MonitorWithWarning("i-bp1f8bfg6zonezdvqlwp", "live_migrate_on_src_nc_finish", "2024-09-21 20:51:03", "performance", 0, "Running", "end", "26.38.117.19", "34468-27", "F9022CTIB08AL21P02Y", "1915657960764041", "26.38.117.19 -HA_EVACUATION-> 26.38.118.36", "False", "warning"))
    records.append(MonitorWithWarning("i-bp1f8bfg6zonezdvqlwp", "live_migrate_on_src_nc_event", "2024-09-21 20:50:17", "performance", 0, "Running", "start", "26.38.117.19", "34468-27", "F9022CTIB08AL21P02Y", "1915657960764041", "26.38.117.19 -HA_EVACUATION-> 26.38.118.36", "False", "warning"))
    records.append(MonitorWithWarning("i-bp1f8bfg6zonezdvqlwp", "vm_down_end", "2024-09-21 20:51:03", "unavailable", 1, "Running", "1", "26.38.117.19", "34468-27", "F9022CTIB08AL21P02Y", "1915657960764041", "AutoRecover", "False", "low_warning"))

    println("processing 结果：")
    var result = keyMetricUtil.startEndProcessing(records.toList)
    println(result)

  }
}
