# 0001
## Input

实例 i-gw8erqghr3vzs5s5e9mu 在2024年12月20日15点左右发生重启，看下重启原因

## Output

实例所在的宿主机************在2024-12-20 14:53:00因FPGA check sum出错宕机，导致实例重启。

## Mock

- classPath: "com.aliyun.xdragon.biz.chat.ChatDiagnoseServiceImpl"   
  methodName: "diagnoseWithConfig"             
  result: "{\"desc\":\"FPGA故障\",\"detectMatterType\":\"实例可用性可能受到影响\",\"endTime\":\"2024-12-20 16:00:00\",\"exceptionTime\":\"2024-12-20 15:03:40\",\"machineId\":\"i-gw8erqghr3vzs5s5e9mu\",\"ncIp\":\"************\",\"reason\":\"hardware:fpga:warning\",\"rootCause\":\"宿主机硬件\",\"startTime\":\"2024-12-20 14:00:00\"}"           
  restrictions:                         
  - #machineId == 'i-gw8erqghr3vzs5s5e9mu'
  - #startTime.getTime() < 1734677400000
  - #endTime.getTime() > 1734678000000
  - #config == "REASON_VM_APPLICABILITY"
- classPath: "com.aliyun.xdragon.biz.workitem.service.impl.OpsInfoRetrievalServiceImpl"   
  methodName: "getOpsEvents"    
  result: "[{\"instance\":\"i-gw8erqghr3vzs5s5e9mu\",\"code\":\"SystemFailure.Reboot\",\"start\":\"2024-12-20 14:52:44\",\"type\":\"ecs_alarm_center.vm.vm_down\",\"nc\":\"************\",\"publish\":\"2024-12-20 14:52:44\",\"end\":\"2024-12-20 14:53:46\",\"reasonLevel2\":\"ServerNodeError\",\"plan\":\"2024-12-20 14:52:44\",\"status\":\"Executed\"}]"
  restrictions:                         
  - #vm == 'i-gw8erqghr3vzs5s5e9mu'
  - #startTime.getTime() < 1734677400000
  - #endTime.getTime() > 1734678000000
  - #statuses.size() == 0
