support: 3
rsupport: 3
cutLines: 5
cutLength: 30
weightThreshold: 0.04
maskPrefix: ""
maskSuffix: ""
delimiters: ["[", "]", ",", ":", "：", "，", "=", "!", "\"", "u'", "'", "{", "}", "#", "(", ")", "&", "<", ">"]
maskingInstructionHelpers:
  - pattern: "((\\d\\d){1,2})[/-](?:0?[1-9]|1[0-2])[/-](?:(?:0[1-9])|(?:[12][0-9])|(?:3[01])|[1-9])[T ](?:2[0123]|[01]?[0-9]):?(?:[0-5][0-9])(?::?(?:(?:[0-5]?[0-9]|60)(?:[:.,][0-9]+)?))?(?:Z|[+-](?:2[0123]|[01]?[0-9])(?::?(?:[0-5][0-9])))?"
    maskWith: "REGEX"
  - pattern: "((?<=[^A-Za-z0-9])|^)OrderedDict((.)*)((?=[^A-Za-z0-9])|$)"
    maskWith: "REGEX"
  - pattern: "((?<=[^A-Za-z0-9])|^)args:.*kwargs: ?\\{.*\\}((?=[^A-Za-z0-9])|$)"
    maskWith: "REGEX"
  - pattern: "http[s]?:(?:/[A-Za-z0-9$.+!*'(){},~:;=@#%_\\-]*)+(?:\\?[A-Za-z0-9$.+!*'|(){},~@#%&/=:;_?\\-\\[\\]]*)?"
    maskWith: "REGEX"
  - pattern: "(?:/[A-Za-z0-9$.+!*'(){},~:;=@#%_\\-]*)+(?:\\?[A-Za-z0-9$.+!*'|(){},~@#%&/=:;_?\\-\\[\\]]*)"
    maskWith: "REGEX"
  - pattern: "((?<=[^A-Za-z0-9])|^)\\[((, |,)?[u]?('|\")[A-Z0-9a-z.-\u4e00-\u9fa5]+('|\"))+\\]((?=[^A-Za-z0-9])|$)"
    maskWith: "REGEX"
  - pattern: "((?<=[^A-Za-z0-9])|^)\\{((, |,)?[u]?('|\")?[A-Z0-9a-z.-\u4e00-\u9fa5]+('|\")?:( |)[u]?('|\")?[A-Z0-9a-z.-\u4e00-\u9fa5]+('|\")?)+\\}((?=[^A-Za-z0-9])|$)"
    maskWith: "REGEX"
  - pattern: "((?<=[^A-Za-z0-9])|^)\\{((, |,)?(\"?[.]+\"?|u?'?.+'?):( |)(\"?[.]+\"?|u?'?[.]+'?))+\\}((?=[^A-Za-z0-9])|$)"
    maskWith: "REGEX"
  - pattern: "((?<=[^A-Za-z0-9])|^)\\{.+\\}((?=[^A-Za-z0-9])|$)"
    maskWith: "REGEX"
  - pattern: "((?<=[^A-Za-z0-9])|^)([,]?\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})+((?=[^A-Za-z0-9])|$)"
    maskWith: "REGEX"
  - pattern: "((?<=[^A-Za-z0-9])|^).+aliyuncs\\.com((?=[^A-Za-z0-9])|$)"
    maskWith: "REGEX"
  - pattern: "(?:ASW-)[A-Z0-9-.]{12,}"
    maskWith: "REGEX"
  - pattern: "(?:host: ?)([,]?[A-Z0-9]{8,12})+[\"]?"
    maskWith: "REGEX"
  - pattern: "((?<=[^A-Za-z0-9-_])|^)(?:i-|hbm-|AY|eci-|cp-|ic-|BVT|bvt|wh|cn|wy|qy|hm|VM|al|uh|ay|tmpvm|ace|vm|houyiecsay)[-]?[a-zA-Z0-9-_]{8,62}((?=[^A-Z0-9])|$)"
    maskWith: "REGEX"
  - pattern: "(?:cn|ap|me|eu|us|rus)(-[A-Z0-9a-z]+)+"
    maskWith: "REGEX"
  - pattern: "((?<=[^A-Z0-9])|^)([A-Z0-9]{12,})((?=[^A-Z0-9])|$)"
    maskWith: "REGEX"
  - pattern: "((?<=[^A-Z0-9])|^)((u')?[a-z0-9-]{32,}'?)((?=[^A-Z0-9])|$)"
    maskWith: "REGEX"
  - pattern: "((?<=[^A-Za-z0-9])|^)([0-9A-F]{4} ?){3,}((?=[^A-Za-z0-9])|$)"
    maskWith: "REGEX"
  - pattern: "((?<=[^A-Za-z0-9])|^)(0x[a-f0-9A-F]+)((?=[^A-Za-z0-9])|$)"
    maskWith: "REGEX"
  - pattern: "\\d+(-\\d+)+"
    maskWith: "REGEX"
  - pattern: "((?<=[^A-Za-z0-9])|^)([\\-\\+]?\\d+\\.?\\d*)((?=[^A-Za-z0-9])|$)"
    maskWith: "REGEX"
