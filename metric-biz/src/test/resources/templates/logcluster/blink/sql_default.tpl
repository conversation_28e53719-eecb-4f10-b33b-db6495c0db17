--SQL
--********************************************************************--
--Author: 日志分析代码生成器
--CreateTime: ${time}
--Comment: ${description}
--********************************************************************--

CREATE TEMPORARY TABLE sls_input_table (
${raw},
${filter_fields}  `__timestamp__` BIGINT METADATA VIRTUAL,
  `__source__` STRING METADATA VIRTUAL,
${ext_fields}  ts as TO_TIMESTAMP(`__timestamp__` * 1000),
  WATERMARK wk FOR ts as withOffset (ts, ${offset})
) with (
  'connector' = 'sls',
  'accessId' = '${r'${sls.input.accessId}'}',
  'accessKey' = '${r'${sls.input.accessKey}'}',
  'endPoint' = '${r'${sls.input.endPoint}'}',
  'project' = '${r'${sls.input.project}'}',
  'logStore' = '${r'${sls.input.logStore}'}',
  'consumerGroup' = '${r'${sls.input.consumerGroup}'}',
  'batchGetSize' = '200',
  'startTime' = '2022-03-21 00:00:00'
);


<#if filterStatus>
CREATE TEMPORARY TABLE filter_table (
  `nc_ip` STRING,
  PRIMARY KEY(nc_ip) NOT ENFORCED
) with (
  'connector' = 'odps',
  'endpoint' = '${r'${filter.endpoint}'}',
  'project' = '${r'${filter.project}'}',
  'tableName' = '${r'${filter.tableName}'}',
  'accessId' = '${r'${filter.accessId}'}',
  'accessKey' = '${r'${filter.accessKey}'}',
  'partition' = 'max_pt_with_done()',
  'cache' = 'ALL',
  'cacheTTLMs' = '1800000'
);
</#if>

<#if filterGamma>
CREATE TEMPORARY TABLE gamma_table (
  `nc_ip` STRING,
  PRIMARY KEY(nc_ip) NOT ENFORCED
) with (
  'connector' = 'rds',
  'url' = '${r'${gamma.url}'}',
  'tableName' = '${r'${gamma.tableName}'}',
  'userName' = '${r'${gamma.userName}'}',
  'password' = '${r'${gamma.password}'}',
  'cacheTTLMs' = '3600000'
);
</#if>

CREATE TEMPORARY TABLE remain_table (
  `task_id` BIGINT,
  `region` VARCHAR,
  `logstore` VARCHAR,
  `log` VARCHAR,
  `md5` VARCHAR,
  `exts` VARCHAR,
  `time` TIMESTAMP
) WITH (
  'connector' = 'sls',
  'accessId' = '${r'${sls.output.accessId}'}',
  'accessKey' = '${r'${sls.output.accessKey}'}',
  'endPoint' = '${r'${remain.endPoint}'}',
  'project' = '${r'${remain.project}'}',
  'logStore' = '${r'${remain.logStore}'}'
);

CREATE TEMPORARY TABLE pattern_table (
  `task_id` BIGINT,
  `region` VARCHAR,
  `logstore` VARCHAR,
  `time` TIMESTAMP,
  `pattern` VARCHAR,
  `hashcode` BIGINT,
  `support` BIGINT,
  `window_size` BIGINT,
  `md5` VARCHAR,
  `nc_cnt` INT
) with (
  'connector' = 'adb3.0',
  'url' = '**********************************************************************************',
  'tableName' = '${r'${adb.sinkTableName}'}',
  'userName' = '${r'${adb.userName}'}',
  'password' = '${r'${adb.password}'}'
);

CREATE TEMPORARY TABLE distribution_table (
  `task_id` BIGINT,
  `region` VARCHAR,
  `logstore` VARCHAR,
  `time` TIMESTAMP,
  `hashcode` BIGINT,
  `support` BIGINT,
  `type_id` BIGINT,
  `detail_key` VARCHAR,
  `detail_support` BIGINT,
  `md5` VARCHAR
) with (
  'connector' = 'adb3.0',
  'url' = '**********************************************************************************',
  'tableName' = '${r'${adb.detailTableName}'}',
  'userName' = '${r'${adb.userName}'}',
  'password' = '${r'${adb.password}'}'
);

CREATE TEMPORARY TABLE mapping_table (
  `task_id` BIGINT,
  `region` VARCHAR,
  `time` TIMESTAMP,
  `md5` VARCHAR,
  `ids` VARCHAR,
  `ip_begin` BIGINT,
  `ip_end` BIGINT,
  `binary_ids` BYTES
) with (
  'connector' = 'adb3.0',
  'url' = '**********************************************************************************',
  'tableName' = '${r'${mapping.tableName}'}',
  'userName' = '${r'${adb.userName}'}',
  'password' = '${r'${adb.password}'}'
);

<#if sinkToSls>
CREATE TEMPORARY TABLE sls_table (
  `metric` VARCHAR,
  `timestamp` BIGINT,
  `value` BIGINT,
  `task_id` BIGINT,
  `region` VARCHAR,
  `logstore` VARCHAR,
  `time` TIMESTAMP,
  `pattern` VARCHAR,
  `hashcode` BIGINT,
  `support` BIGINT,
  `md5` VARCHAR,
  `sourceId` BIGINT
) with (
  'connector' = 'sls',
  'accessId' = '${r'${sls.output.accessId}'}',
  'accessKey' = '${r'${sls.output.accessKey}'}',
  'endPoint' = '${r'${sls.output.endPoint}'}',
  'project' = '${r'${sls.output.project}'}',
  'logStore' = '${r'${sls.output.logStore}'}'
);

</#if>

CREATE TEMPORARY VIEW pretreat_view AS
SELECT
  ${raw_usage} AS raw_log,
${filter_trans}${ext_trans}  ${join_prefix}ts AS ts
FROM
  sls_input_table${join_alias}
  <#if filterStatus>
    LEFT JOIN
      filter_table FOR SYSTEM_TIME AS OF PROCTIME () AS f ON t.__source__ = f.nc_ip
  </#if>
  <#if filterGamma>
    LEFT JOIN
      gamma_table FOR SYSTEM_TIME AS OF PROCTIME () AS g ON t.__source__ = g.nc_ip
  </#if>
  WHERE
  ${raw_filter}
  <#if filterStatus>
    AND f.nc_ip IS NULL
  </#if>
  <#if filterGamma>
    AND g.nc_ip IS NULL
  </#if>
  ${filter_usage};

CREATE TEMPORARY VIEW match_view AS
SELECT
  `raw_log`,
  `raw_md5`,
  `pattern`,
  `md5`,
${ext_list}  ts
FROM
  pretreat_view,
  LATERAL table (matchPattern(raw_log)) AS T(`raw_md5`, `pattern`, `md5`);

CREATE TEMPORARY VIEW remain_view AS
SELECT
  `raw_log`,
  `raw_md5`,
  CONCAT_WS('|'${ext_usage}) AS `exts`,
  ts
FROM
  match_view
WHERE
  `pattern` IS NULL OR `md5` IS NULL;

CREATE TEMPORARY VIEW retain_view AS
SELECT
  `raw_md5`,
  `pattern`,
  `md5`,
${ext_list}  ts
FROM
  match_view
WHERE
  `pattern` IS NOT NULL AND `md5` IS NOT NULL;

CREATE TEMPORARY VIEW pattern_view AS
SELECT
  TUMBLE_START(ts, INTERVAL '${window}' MINUTE) AS window_start,
  TUMBLE_END(ts, INTERVAL '${window}' MINUTE) AS window_end,
  `md5`,
  `pattern`,
  COUNT(*) AS `support`,
  COUNT(DISTINCT `ext1`) AS `nc_cnt`
FROM
  retain_view
GROUP BY `md5`, `pattern`, TUMBLE(ts, INTERVAL '${window}' MINUTE);

CREATE TEMPORARY VIEW mapping_view AS
SELECT
  TUMBLE_START(ts, INTERVAL '${window}' MINUTE) AS window_start,
  TUMBLE_END(ts, INTERVAL '${window}' MINUTE) AS window_end,
  `md5`,
  `pattern`,
  idCollect(`raw_md5`, `ext1`) AS `id_list`
FROM
  retain_view
GROUP BY `md5`, `pattern`, LEFT(`ext1`, 6), TUMBLE(ts, INTERVAL '${window}' MINUTE);

<#list distribution_view as dv>
CREATE TEMPORARY VIEW distribution${dv.id}_view AS
SELECT
  TUMBLE_START(ts, INTERVAL '${dv.window}' MINUTE) AS window_start,
  TUMBLE_END(ts, INTERVAL '${dv.window}' MINUTE) AS window_end,
  ${dv.index} AS `type_id`,
  `md5`,
  `pattern`,
  `ext${dv.id}`,
  COUNT(*) AS `support`
FROM
  retain_view
GROUP BY `md5`, `pattern`, ext${dv.id}, TUMBLE(ts, INTERVAL '${dv.window}' MINUTE);
</#list>


BEGIN STATEMENT SET;

INSERT INTO remain_table
SELECT
  `task_id`,
  `region`,
  `logstore`,
  `raw_log`,
  `raw_md5`,
  `exts`,
  ts
FROM
  remain_view,
  LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`);

INSERT INTO pattern_table
SELECT
  `task_id`,
  `region`,
  `logstore`,
  `window_start`,
  `pattern`,
  0,
  `support`,
  CAST(${window} AS BIGINT),
  `md5`,
  CAST(`nc_cnt` AS INT)
FROM
  pattern_view,
  LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`);

INSERT INTO mapping_table
SELECT
  `task_id`,
  `region`,
  `window_start`,
  `md5`,
  null,
  `begin`,
  `end`,
  `ids`
FROM
  mapping_view,
  LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`),
  LATERAL table (idFlat(id_list)) AS T(`ids`, `begin`, `end`);

<#list distribution_insert as dt>
INSERT INTO distribution_table
SELECT
  `task_id`,
  `region`,
  `logstore`,
  window_start,
  0,
  0,
  `type_id`,
  COALESCE(`ext${dt.id}`, 'null'),
  `support`,
  `md5`
FROM
  distribution${dt.id}_view,
  LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`);
 </#list>

<#if sinkToSls>
INSERT INTO sls_table
SELECT
  CONCAT_WS('/', 'logcluster', `task_id`, `md5`, `region`, `logstore`),
  UNIX_TIMESTAMP(`window_start`),
  `support`,
  `task_id`,
  `region`,
  `logstore`,
  `window_start`,
  `pattern`,
  0,
  `support`,
  `md5`,
  ${pattern_source_id}
FROM
  pattern_view,
LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`);

INSERT INTO sls_table
SELECT
  CONCAT_WS('/', 'logclusternccnt', `task_id`, `md5`, `region`, `logstore`),
  UNIX_TIMESTAMP(`window_start`),
  `nc_cnt`,
  `task_id`,
  `region`,
  `logstore`,
  `window_start`,
  `pattern`,
  0,
  `nc_cnt`,
  `md5`,
  ${count_source_id}
FROM
  pattern_view,
LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`);
</#if>

END;