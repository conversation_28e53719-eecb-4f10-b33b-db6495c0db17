package com.aliyun.xdragon.biz.chat.handle;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.xdragon.api.service.chat.model.AgentAnswerResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
public class CustomerEventHandleServiceImplTest {
    @InjectMocks
    private CustomerEventHandleServiceImpl customerEventHandleService;

    @Test
    public void aiAgentResCheck() throws Exception {
        AgentAnswerResponse response = new AgentAnswerResponse();
        Assert.assertFalse(customerEventHandleService.aiAgentResCheck(response));
        JSONObject params = new JSONObject();
        params.put("machine_id","[]");
        response.setParams(params);
        Assert.assertFalse(customerEventHandleService.aiAgentResCheck(response));
        params.put("machine_id","[\"machine_id\"]");
        Assert.assertTrue(customerEventHandleService.aiAgentResCheck(response));
    }
}