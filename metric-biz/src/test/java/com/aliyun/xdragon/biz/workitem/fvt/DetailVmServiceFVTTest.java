package com.aliyun.xdragon.biz.workitem.fvt;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.aliyun.xdragon.biz.BaseTestCase;
import com.aliyun.xdragon.biz.workitem.service.impl.DetailVmServiceImpl;
import com.aliyuncs.exceptions.ClientException;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

public class DetailVmServiceFVTTest extends BaseTestCase {

    @Resource
    DetailVmServiceImpl detailVmService;

    @Test
    @Ignore
    public void testQueryNcIp() throws ClientException {
        List<String> instanceIdList = Arrays.asList("i-bp1i11llygg5gzdiz3d3");
        detailVmService.setRegionId("cn-hangzhou-dg-a01");
        Map<String, String> map = detailVmService.queryNcIp(instanceIdList);
        Assert.assertTrue(map.size() > 0);
    }

    @Test
    @Ignore
    public void testQuerySocketsByNc() throws ClientException {
        Map<String, List<Integer>> map = detailVmService.queryNodesAndSocketsByNc("************",
            "2024-04-02 12:00:00","2024-04-02 14:00:00");
        Assert.assertTrue(map.size() > 0);
    }

}
