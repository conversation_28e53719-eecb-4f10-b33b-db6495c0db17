package com.aliyun.xdragon.biz.chat.handle;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.xdragon.api.service.chat.ChatOpsService;
import com.aliyun.xdragon.api.service.chat.model.AgentAnswerResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class SelfDiagnosisHandleServiceImplTest {
    @InjectMocks
    private SelfDiagnosisHandleServiceImpl selfDiagnosisHandleService;

    @Mock
    private ChatOpsService chatOpsService;

    @Test
    public void testBasic() throws Exception {
        Assert.assertNull(selfDiagnosisHandleService.getContent(null));
        Assert.assertNull(selfDiagnosisHandleService.getUrl(null, null));
        Assert.assertNull(selfDiagnosisHandleService.getLinks(null));
        Assert.assertNull(selfDiagnosisHandleService.getAones(null));
        Assert.assertTrue(selfDiagnosisHandleService.aiAgentResCheck(null));
    }

    @Test
    public void testGetViewContent() throws Exception {
        AgentAnswerResponse response = new AgentAnswerResponse();
        JSONObject p = new JSONObject();
        p.put("machine_id", Arrays.asList("i-j6c3n2k75uevflcqmrto", "i-p6c3n2k75uevflcqmrt6"));
        response.setParams(p);
        JSONObject workFlowDefine = new JSONObject();
        workFlowDefine.put("name", "test");
        when(chatOpsService.queryWorkFlowDefine(any())).thenReturn(workFlowDefine);
        String resContent = selfDiagnosisHandleService.getViewContent(response, 2);
        Assert.assertNotNull(resContent);

        AgentAnswerResponse response1 = new AgentAnswerResponse();
        response1.setFinish(true);
        JSONObject p1 = new JSONObject();
        p1.put("code", "[{\"name\":\"instanceId\",\"type\":\"String\",\"required\":true}]");
        response1.setParams(p1);
        String viewContent = selfDiagnosisHandleService.getViewContent(response1, 2);
        Assert.assertNotNull(viewContent);
    }


}