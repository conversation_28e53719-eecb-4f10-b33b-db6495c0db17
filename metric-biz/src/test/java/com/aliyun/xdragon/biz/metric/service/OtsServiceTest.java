package com.aliyun.xdragon.biz.metric.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.AssertTrue;

import com.alicloud.openservices.tablestore.TimeseriesClient;
import com.alicloud.openservices.tablestore.model.ColumnType;
import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.Response;
import com.alicloud.openservices.tablestore.model.timeseries.GetTimeseriesDataResponse;
import com.alicloud.openservices.tablestore.model.timeseries.TimeseriesKey;
import com.alicloud.openservices.tablestore.model.timeseries.TimeseriesRow;
import com.aliyun.xdragon.biz.BaseTestCase;
import com.aliyun.xdragon.biz.metric.config.MetricOtsConfig;
import com.aliyun.xdragon.biz.metric.service.impl.OtsServiceImpl;
import com.aliyun.xdragon.common.model.metric.MetricSeries;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"javax.crypto.*","javax.net.ssl.*" })
public class OtsServiceTest {

    @InjectMocks
    private OtsServiceImpl service;

    @Mock
    TimeseriesClient client;

    @Mock
    MetricOtsConfig metricOtsConfig;


    @Before
    public void setUp() {
        Mockito.when(metricOtsConfig.getAccessId()).thenReturn("ak");
        Mockito.when(metricOtsConfig.getInstance()).thenReturn("feature");
        Mockito.when(metricOtsConfig.getEndpoint()).thenReturn("http://ecs-ops-feature.cn-beijing.ots-inner.xxxxx.com");
        Mockito.when(metricOtsConfig.getAccessKey()).thenReturn("sk");
        Mockito.when(metricOtsConfig.getTable()).thenReturn("xdragom_metric_ops");

        Mockito.when(client.getTimeseriesData(any())).thenReturn(prepareOTSResponse());
    }

    @Test
    public void queryMetricSeries() {
        HashMap<String, String> metrics = new HashMap<String, String>(){{
            put("VmNetworkRetryMetric/tx_retry", "i-t4ni77wqjeqqxlhl7eq8");
            put("VmNetworkRetryMetric/rx_retry", "i-t4ni77wqjeqqxlhl7eq8");
        }};

        MetricSeries series = service.queryMetricSeries(client, "VmNetworkRetryMetric/tx_retry",
            "i-t4ni77wqjeqqxlhl7eq8", 1718890620L - 3600, 1718890620L);
        Assert.assertEquals(10, series.getSeries().size());

        List<MetricSeries> seriesList = service.queryMetricSeries(client, "xdragom_metric_ops", metrics,
            1718890620L - 3600, 1718890620L);
        Assert.assertEquals(2, seriesList.size());

    }

    public GetTimeseriesDataResponse prepareOTSResponse() {
        GetTimeseriesDataResponse response = new GetTimeseriesDataResponse(new Response());
        Map<String, String> tags = new HashMap<String, String>(){{
            put("job_name", "xdragon-metric");
        }};
        // 通过measurementName、dataSource和tags构建TimeseriesKey。
        TimeseriesKey timeseriesKey = new TimeseriesKey("test", "i-test", tags);

        ArrayList<TimeseriesRow> rows = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            TimeseriesRow row = new TimeseriesRow(timeseriesKey);
            row.setTimeInUs((1718890620 + i * 60) * 1000000);
            row.setFields(Collections.singletonMap("value", new ColumnValue((double)i, ColumnType.DOUBLE)));
            rows.add(row);
        }
        response.setRows(rows);
        response.setNextToken(new byte[]{Byte.parseByte("1"), Byte.parseByte("3"), Byte.parseByte("5")});
        return response;
    }
}
