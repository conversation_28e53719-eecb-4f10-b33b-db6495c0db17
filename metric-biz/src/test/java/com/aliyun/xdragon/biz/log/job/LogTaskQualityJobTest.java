package com.aliyun.xdragon.biz.log.job;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.xdragon.biz.log.repository.ClusterRegionConfigDao;
import com.aliyun.xdragon.biz.log.repository.LogUnionTaskDao;
import com.aliyun.xdragon.common.enumeration.log.StreamingTaskType;
import com.aliyun.xdragon.common.generate.model.LogClusterConfigRegion;
import com.aliyun.xdragon.common.generate.model.LogUnionTask;
import com.aliyun.xdragon.service.common.config.FlinkMetricSlsConfig;
import com.aliyun.xdragon.service.common.config.diamond.ConfigService;
import com.aliyun.xdragon.service.common.model.SLSQueryResult;
import com.aliyun.xdragon.service.common.repository.FlinkMetricDao;
import com.aliyun.xdragon.service.common.util.SlsUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.stubbing.Answer;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

/**
 * <AUTHOR>
 * @date 2025/05/12
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(SlsUtil.class)
public class LogTaskQualityJobTest {
    @InjectMocks
    private LogTaskQualityJob job;

    @Mock
    private ClusterRegionConfigDao configDao;

    @Mock
    private LogUnionTaskDao logUnionTaskDao;

    @Mock
    private FlinkMetricDao flinkMetricDao;

    @Mock
    private FlinkMetricSlsConfig flinkMetricSlsConfig;

    @Mock
    private ConfigService configService;

    @Before
    public void init() {
        mockStatic(SlsUtil.class);
        when(SlsUtil.parseLogs(anyList())).thenAnswer((Answer<List<Map<String, String>>>) invocation -> {
            List<QueriedLog> logs = invocation.getArgument(0);
            List<Map<String, String>> ret = new LinkedList<>();
            logs.forEach(log -> {
                Map<String, String> m = new HashMap<>();
                log.GetLogItem().GetLogContents().forEach(c -> {
                    m.put(c.GetKey(), c.GetValue());
                });
                ret.add(m);
            });
            return ret;
        });
        when(flinkMetricSlsConfig.getProject()).thenReturn("project");
        when(flinkMetricSlsConfig.getLogQualityLogstore()).thenReturn("log_store");
    }

    @Test
    public void genTask() {
        when(logUnionTaskDao.getUnionTasksByStatus(any(), any(), anyBoolean())).thenReturn(Lists.newArrayList(new LogUnionTask()));
        when(configDao.listAllConfigsWithStatus(any(), any())).thenReturn(Lists.newArrayList(new LogClusterConfigRegion()));
        List<Object> tasks = job.genTask(null, 0);
        Assert.assertEquals(3, tasks.size());
        Assert.assertTrue(tasks.get(0) instanceof LogUnionTask);
        Assert.assertTrue(tasks.get(2) instanceof LogClusterConfigRegion);
    }

    @Test
    public void processFail() throws Exception {
        // not supported class
        ProcessResult result = job.processSubTask(null, 123, 123, new Integer(123));
        Assert.assertEquals(InstanceStatus.FAILED, result.getStatus());

        // unknown task type
        LogClusterConfigRegion regionConfig = new LogClusterConfigRegion();
        regionConfig.setTaskType(StreamingTaskType.DEFAULT.getType());
        result = job.processSubTask(null, 123, 123, regionConfig);
        Assert.assertEquals(InstanceStatus.FAILED, result.getStatus());


        LogUnionTask task = new LogUnionTask();
        task.setPlatformId("flink_id");
        // query delay fail
        when(flinkMetricDao.queryUpTime(anyString(), anyInt(), anyInt())).thenReturn(null);
        when(flinkMetricDao.queryDelay(anyString(), anyInt(), anyInt())).thenReturn(null);
        result = job.processSubTask(null, 123, 123, task);
        Assert.assertEquals(InstanceStatus.FAILED, result.getStatus());

        // query restart fail
        reset(flinkMetricDao);
        when(flinkMetricDao.queryUpTime(anyString(), anyInt(), anyInt())).thenReturn(null);
        when(flinkMetricDao.queryDelay(anyString(), anyInt(), anyInt())).thenReturn(Lists.newArrayList(Pair.of("host", 123L)));
        when(flinkMetricDao.queryRestart(anyString(), anyInt(), anyInt())).thenReturn(null);
        result = job.processSubTask(null, 123, 123, task);
        Assert.assertEquals(InstanceStatus.FAILED, result.getStatus());
    }

    @Test
    public void processSuccess() throws Exception {
        LogUnionTask unionTask = new LogUnionTask();
        unionTask.setPlatformId("union_flink_id");
        unionTask.setTaskId(1L);
        unionTask.setUnionId(2L);
        unionTask.setSourceParam("{\"accountName\":\"newbie_ecs\",\"endpoint\":\"cn-zhangjiakou-2-intranet.log.aliyuncs.com\",\"project\":\"log-cluster\",\"logStore\":\"log_remain\"}");

        LogClusterConfigRegion matchTask = new LogClusterConfigRegion();
        matchTask.setPlatformId("match_flink_id");
        matchTask.setTaskType(StreamingTaskType.MATCH.getType());
        matchTask.setTaskId(1L);
        matchTask.setRegionCid(3L);
        matchTask.setInputSourceParam("{\"accountName\":\"newbie_ecs\",\"endpoint\":\"cn-zhangjiakou-2-intranet.log.aliyuncs.com\",\"project\":\"log-cluster\",\"logStore\":\"log_remain\"}");

        LogClusterConfigRegion clusterTask = new LogClusterConfigRegion();
        clusterTask.setPlatformId("match_flink_id");
        clusterTask.setTaskType(StreamingTaskType.ONLINE_CLUSTER.getType());
        clusterTask.setTaskId(1L);
        clusterTask.setRegionCid(4L);
        clusterTask.setInputSourceParam("{\"accountName\":\"newbie_ecs\",\"endpoint\":\"cn-zhangjiakou-2-intranet.log.aliyuncs.com\",\"project\":\"log-cluster\",\"logStore\":\"log_remain\"}");

        // new job
        when(flinkMetricDao.queryUpTime(anyString(), anyInt(), anyInt())).thenReturn(0);
        ProcessResult result = job.processSubTask(null, 123, 123, unionTask);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());

        // old job
        reset(flinkMetricDao);
        when(flinkMetricDao.queryUpTime(anyString(), anyInt(), anyInt())).thenReturn(*********);
        when(flinkMetricDao.queryDelay(anyString(), anyInt(), anyInt())).thenReturn(Lists.newArrayList(Pair.of("host", 12345L)));
        when(flinkMetricDao.queryRestartWithDetail(anyString(), anyInt(), anyInt())).thenReturn(Pair.of(3,2));
        SLSQueryResult queryResult = mock(SLSQueryResult.class);
        when(SlsUtil.querySLS(any(), anyString(), anyString(), anyInt(), anyInt(), anyString(),
            anyString())).thenReturn(queryResult);
        LogItem item = new LogItem();
        item.PushBack("cnt", "100");
        QueriedLog queriedLog = new QueriedLog("", item);
        when(queryResult.getLogs()).thenReturn(Lists.newArrayList(queriedLog));
        result = job.processSubTask(null, 123, 123, unionTask);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());

        result = job.processSubTask(null, 123, 123, matchTask);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());

        result = job.processSubTask(null, 123, 123, clusterTask);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());

    }


}
