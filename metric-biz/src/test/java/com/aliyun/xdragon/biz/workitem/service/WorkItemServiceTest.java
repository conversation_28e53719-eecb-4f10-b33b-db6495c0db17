package com.aliyun.xdragon.biz.workitem.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.xdragon.biz.workitem.service.impl.DetailVmConsistentImpl;
import com.aliyun.xdragon.common.enumeration.aone.AoneReqStatusEnum;
import com.aliyun.xdragon.common.model.workItem.WorkItem;
import com.aliyun.xdragon.service.common.service.aone.AoneIssueProxy;
import com.aliyun.xdragon.service.common.util.DateUtil;
import com.aliyun.xdragon.service.common.util.aone.AoneIssueUtils;
import com.aliyun.xdragon.service.common.config.aone.AoneConstants;
import com.aliyun.xdragon.biz.workitem.model.ComplaintRecord;
import com.aliyun.xdragon.common.model.workItem.CreateIssue;
import com.aliyun.xdragon.biz.workitem.model.MachineInfo;
import com.aliyun.xdragon.biz.BaseTestCase;
import com.taobao.api.domain.KeludeIssue;
import com.taobao.api.response.KeludeIssueGetResponse;
import com.taobao.api.response.KeludeIssueSearchResponse;
import com.taobao.api.response.KeludeProjectTagsGetResponse;
import com.taobao.api.response.KeludeTagGetbytargetResponse;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


public class WorkItemServiceTest extends BaseTestCase {

    @Autowired
    WorkItemService workItemService;

    @Autowired
    DetailVmConsistentImpl detailVmConsistent;

    @Test
    public void searchWorkItemByIdTest() {
        KeludeIssueGetResponse.Issue result = workItemService.searchWorkItemById(57250453);
        TestCase.assertNotNull(result.getSubject());
    }

    @Test
    public void searchWorkItemTest() throws ParseException {
        String from = "2023-09-13 15:55:27";
        String to = "2023-09-13 15:59:27";
        WorkItem item = new WorkItem();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        item.setStamp("Req");
        item.setExcludeRelatedAkProject(false);
        item.setCreatedAtFrom(format.parse(from));
        item.setCreatedAtTo(format.parse(to));
        item.setRelatedAKProjectIds(Arrays.asList(1182328));
        List<KeludeIssueGetResponse.Issue> issues = workItemService.searchWorkItems(item);
        Assert.assertTrue(issues.size() > 0);
    }

    @Test
    @Ignore
    public void searchWorkItemByCreatTimeTest() throws ParseException {
        Date createdFrom = DateUtil.genDateFromStr("2023-09-05 14:40:00");
        Date createdTo = DateUtil.genDateFromStr("2023-09-05 14:45:00");
        List<KeludeIssueGetResponse.Issue> result =  workItemService.searchWorkItemByCreatTime(new HashSet<>(Arrays.asList(2052019L)), createdFrom, createdTo);
        TestCase.assertTrue(result.size() > 0);
        Date createdTo2 = DateUtil.genDateFromStr("2022-06-24 11:40:00");
        List<KeludeIssueGetResponse.Issue> nullResult = workItemService.searchWorkItemByCreatTime(WorkItemService.PROJECT_MAP.keySet(), createdFrom, createdTo2);
        TestCase.assertEquals(0, nullResult.size());
    }

    @Test
    public void createIssueTest2() {
        CreateIssue issue = new CreateIssue();
        Long result = workItemService.createIssue(issue);
        TestCase.assertNull(result);
    }

    @Test
    public void listTagsOfProjectTest() {
        int projectId = 1182328;
        List<KeludeProjectTagsGetResponse.Result> tagList = workItemService.listTagsOfProject(projectId);
        TestCase.assertTrue(tagList.size() > 0);
        Set<Long> tagNameSet = new HashSet<>();
        for (KeludeProjectTagsGetResponse.Result tag:tagList) {
            tagNameSet.add(tag.getId());
            if (tag.getName().equals("值班工单")){
            }
        }
    }

    @Test
    @Ignore
    public void createIssueTest() {
        List<ComplaintRecord> complaintRecordList = new ArrayList<>();
        String jsonStr = "[{\"用户\":\"OOS自动带宽临时升级失败出现报错：'modifyInstanceNetworkSpec loop task fail because failures exceeded MaxErrors -> requestId: FCD95313-4CB8-55F3-8657-419205503B71 code: OperationDenied.UnpaidOrder message: The specif\",\"名称\":\"车石开\"},{\"名称\":\"小S\",\"支持者\":\"支持人员韩星宇加入会话，目前有3个问题正在处理，预计将在25分钟11秒内回复，请给予理解并耐心等待\"},{\"用户\":\"[unsupported]\",\"名称\":\"车石开\"},{\"名称\":\"韩星宇\",\"支持者\":\"好的，我看看\"},{\"用户\":\"辛苦老师\",\"名称\":\"车石开\"},{\"名称\":\"韩星宇\",\"支持者\":\"i-2ze42q1waqyyssgivlg8  看下这个实例应该是有订单没有支付吧\"},{\"用户\":\"[unsupported]\",\"名称\":\"车石开\"},{\"用户\":\"没有未支付的订单\",\"名称\":\"车石开\"},{\"名称\":\"韩星宇\",\"支持者\":\"uid提供一下\"},{\"用户\":\"1866641717628167\",\"名称\":\"车石开\"},{\"用户\":\"[unsupported]\",\"名称\":\"车石开\"},{\"用户\":\"现在提交新的任务会提示这个\",\"名称\":\"车石开\"},{\"名称\":\"韩星宇\",\"支持者\":\"\u200B@青逸 帮忙看下这个问题，咱们这边显示有未完成支付的订单，能帮忙查一下吗\"},{\"名称\":\"韩星宇\",\"支持者\":\"\u200B@青逸 帮忙看下这个问题，咱们这边显示有未完成支付的订单，能帮忙查一下吗\"},{\"名称\":\"青逸\",\"支持者\":\"有未支付订单的话要么把订单支付了，要么把订单作废了。才能做后面的操作。\"},{\"名称\":\"韩星宇\",\"支持者\":\"提供的截图看都已经支付了，或者作废了\"},{\"用户\":\"目前没有未支付的订单了\",\"名称\":\"车石开\"},{\"名称\":\"韩星宇\",\"支持者\":\"\u200B@青逸 帮忙在看看呢\"},{\"名称\":\"韩星宇\",\"支持者\":\"\u200B@青逸 帮忙在看看呢\"},{\"名称\":\"青逸\",\"支持者\":\"那就重试看看吧\"},{\"用户\":\"好的\",\"名称\":\"车石开\"},{\"名称\":\"韩星宇\",\"支持者\":\"重试后可以了吗\u200B@车石开 \"},{\"名称\":\"韩星宇\",\"支持者\":\"重试后可以了吗\u200B@车石开 \"},{\"用户\":\"目前客户还未反馈，联系不上\",\"名称\":\"车石开\"},{\"名称\":\"大S\",\"支持者\":\"对于问题解决不满意？可以点此进行\"},{\"名称\":\"韩星宇\",\"支持者\":\"客户还没有反馈是吧，那这里就先关了，有问题在找我\u200B@车石开 \"},{\"名称\":\"韩星宇\",\"支持者\":\"客户还没有反馈是吧，那这里就先关了，有问题在找我\u200B@车石开 \"},{\"名称\":\"大S\",\"支持者\":\"FORMTAG\"}]";
        JSONArray jsonArray = JSONObject.parseArray(jsonStr);
        StringBuilder stringBuilder = new StringBuilder();
        for (Object object:jsonArray) {
            JSONObject jsonObject = (JSONObject) object;
            String user = jsonObject.getString("名称");
            String words;
            if (jsonObject.containsKey("用户")){
                words = jsonObject.getString("用户");
            } else {
                words = jsonObject.getString("支持者");
            }
            stringBuilder.append("<br>" + user + ": " + words + "</br>");
        }
        ComplaintRecord complaintRecord = new ComplaintRecord();
        complaintRecord.setDescription(stringBuilder.toString());
        complaintRecord.setSubject("测试diamond");
        complaintRecord.setId("1");
        complaintRecord.setProject("测试");
        complaintRecord.setTime("2022-01-14 12:00:00");
        MachineInfo machineInfo = new MachineInfo("1", new HashMap<String, String>(){{put("rc", "根因");}});
        complaintRecord.setInstanceIdList(Arrays.asList(machineInfo));
        complaintRecord.setLabel("存储异常");
        complaintRecord.setUrl("https://aone.xxxxx.com/issue/45409396");
        complaintRecordList.add(complaintRecord);
        for (ComplaintRecord record:complaintRecordList) {
            Map<String, String> issueMap = new HashMap<>();
            issueMap.put("author", AoneConstants.DEFAULT_AONE_SUBMITTER);
            issueMap.put("subject", record.getSubject());
            issueMap.put("description", record.toString());
            issueMap.put("moduleName","其它工单");
            issueMap.put("createAt",record.getTime());
            if (record.getLabel() != null && record.getLabel().length() > 0) {
                issueMap.put("assignedTo", "320310");
            }
            CreateIssue issue = AoneIssueUtils.createAoneIssue(issueMap);
            issue.setAkProjectId(1182328L);
            issue.setStamp(AoneConstants.DEFAULT_STAMP);
            Long createResult = workItemService.createIssue(issue);
            TestCase.assertNotNull(createResult);
            String aoneUrl = "https://aone.xxx.com/issue/" + createResult;
            record.setAone(aoneUrl);
        }
        TestCase.assertNotNull(complaintRecord.getAone());
    }

    @Test
    public void addTagTest() {
        Long projectId = 832307L;
        Long issueId = 48963492L;
        String tagName = "智能运维";
        String userId = "266081";
        Boolean result = workItemService.addTag(projectId, issueId, tagName, userId);
        Assert.assertTrue(result);
    }

    @Test
    public void addTagTest2() {
        Long issueId = 49318644L;
        String userId = "266081";
        List<Long> tagIds = Arrays.asList(413884L);
        // add tag不会覆盖，之后往上增加新的标签
        Boolean result = workItemService.addTag(issueId, tagIds, userId);
        Assert.assertTrue(result);
    }

    @Test
    public void updateIssueTest() {
        String modifier = "266081";
        Long issueId = 52187915L;
        String description = "测试module使用diamond的效果";
        CreateIssue issue = new CreateIssue();
        issue.setIssueId(issueId);
        issue.setModifier(modifier);
        issue.setDescription(description);
        issue.setModuleName("其它工单");
//        issue.setAssignedTo("320310");
        // 这里的状态更新不是所有都可以，有限制，比如已经Fixed不能再次更新为Fixed
//        issue.setStatus("Reopen");
        boolean result = workItemService.updateIssue(issue);
        Assert.assertTrue(result);
    }

    @Test
    public void queryReleasedVmInfoTest() {
        List<String> vmIds = Arrays.asList("i-uf6h3sn3ap3cghq4uig2", "i-bp16ougnejp3w2v0s6sr");
        List<String> releasedVms = detailVmConsistent.queryReleasedVmInfo(new ArrayList<>(vmIds));
        Assert.assertEquals(1, releasedVms.size());
    }
}
