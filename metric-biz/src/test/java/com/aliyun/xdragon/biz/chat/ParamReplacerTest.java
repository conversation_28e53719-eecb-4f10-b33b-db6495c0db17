package com.aliyun.xdragon.biz.chat;

import org.junit.Assert;
import org.junit.Test;

public class ParamReplacerTest {
    @Test
    public void testIsParamMatched() {
        Assert.assertTrue(ParamReplacer.isParamMatched(null, null));
        Assert.assertTrue(ParamReplacer.isParamMatched("", null));
        Assert.assertTrue(ParamReplacer.isParamMatched("nc", "*******"));
        Assert.assertFalse(ParamReplacer.isParamMatched("nc", "1.2.3.300"));
        Assert.assertTrue(ParamReplacer.isParamMatched("aliuid", "0123456789101112"));
        Assert.assertFalse(ParamReplacer.isParamMatched("aliuid", "*******"));
    }

    @Test
    public void testGetAmendedUrl() {
        Assert.assertEquals("http://cloudbot.xxxxx-inc.com/cloudbot/ng2/#/moon-micro-main/ecs-plan-market/preview-gray-info/grayChangeMonitor?startTime=&endTime=&ncIp=*******&exceptionName=", ParamReplacer.getAmendedUrl("http://cloudbot.xxxxx-inc.com/cloudbot/ng2/#/moon-micro-main/ecs-plan-market/preview-gray-info/grayChangeMonitor?startTime=&endTime=&ncIp=i-0jl5uezay8hbljci7gth&exceptionName=", "*******"));
    }
}