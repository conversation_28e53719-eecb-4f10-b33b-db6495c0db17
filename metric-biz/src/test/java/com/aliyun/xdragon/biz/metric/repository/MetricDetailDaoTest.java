package com.aliyun.xdragon.biz.metric.repository;

import com.aliyun.xdragon.biz.AbstractDbTest;
import com.aliyun.xdragon.common.generate.model.MetricDetail;
import com.aliyun.xdragon.common.generate.model.MetricDetail.Column;
import org.assertj.core.api.Assertions;
import org.assertj.core.util.Lists;
import org.checkerframework.checker.units.qual.Temperature;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Import(MetricDetailDao.class)
public class MetricDetailDaoTest extends AbstractDbTest {
    @Autowired
    private MetricDetailDao metricDetailDao;

    @Test
    public void test() {
        MetricDetail metricDetail1 = new MetricDetail();
        metricDetail1.setMetricName("metric1");
        metricDetail1.setPeriodLength(48);
        metricDetail1.setMetricAnomalyId(1L);
        metricDetail1.setAnomalyCheck(false);
        metricDetail1.setExpertLostThreshold(86400L);
        metricDetail1.setLostCheck(false);
        metricDetail1.setMissingRate((byte)5);
        metricDetail1.setPeriodScore((byte)10);
        metricDetail1.setSourceId(1L);
        metricDetailDao.addMetricDetail(metricDetail1);
        List<MetricDetail> details = metricDetailDao.listMetrics();
        Assertions.assertThat(details).hasSize(1).element(0).hasFieldOrPropertyWithValue("periodLength", 48);

        // create
        MetricDetail metricDetail2 = new MetricDetail();
        metricDetail2.setMetricName("metric2");
        metricDetail2.setPeriodLength(48);
        metricDetail2.setMissingRate((byte)5);
        metricDetail2.setPeriodScore((byte)30);
        metricDetail2.setSourceId(1L);
        metricDetail2.setAnomalyCheck(false);
        metricDetail2.setLostCheck(false);

        // update
        MetricDetail metricDetail3 = new MetricDetail();
        metricDetail3.setMetricName("metric1");
        metricDetail3.setPeriodLength(24);
        metricDetail3.setMissingRate((byte)0);
        metricDetail3.setPeriodScore((byte)50);
        metricDetail3.setSourceId(1L);
        metricDetail3.setAnomalyCheck(false);
        metricDetail3.setLostCheck(false);

        metricDetailDao.batchInsertOrUpdateMetric(new ArrayList<>(Arrays.asList(metricDetail2, metricDetail3)),
            Column.metricName, Column.sourceId, Column.missingRate, Column.periodScore, Column.periodLength);
        metricDetail3.setDetailId(metricDetailDao.listMetrics().get(1).getDetailId());
        metricDetail3.setMetricName("metric3");
        metricDetailDao.updateMetricDetailWithId(metricDetail3);

        details = metricDetailDao.listMetrics();
        Assertions.assertThat(details).hasSize(2).element(1).hasFieldOrPropertyWithValue("periodLength", 24);
        Assertions.assertThat(metricDetailDao.listMetrics(null, 0, 10, "metric")).hasSize(2).element(1).hasFieldOrPropertyWithValue("periodLength", 24);
        List<MetricDetail> details2 = metricDetailDao.listMetrics(1L, 0, 10, "met");
        Assertions.assertThat(details2).hasSize(2).element(0).hasFieldOrPropertyWithValue("periodLength", 24);
        Assert.assertEquals(2L, metricDetailDao.getMetricDetailCount(1L, null));
        Assert.assertEquals(details2.get(0).getMetricName(), metricDetailDao.getMetricDetailById(details2.get(0).getDetailId()).getMetricName());

        for (MetricDetail detail: details2) {
            if (detail.getMetricName().equals("metric1")) {
                detail.setLostCheck(true);
            }
            if (detail.getMetricName().equals("metric3")) {
                detail.setAnomalyCheck(true);
            }
        }
        metricDetailDao.batchInsertOrUpdateMetric(details2, Column.detailId, Column.anomalyCheck, Column.lostCheck);
        List<MetricDetail> details3 = metricDetailDao.listMetrics(1L, 0, 10, null);
        for (MetricDetail detail: details3) {
            if (detail.getMetricName().equals("metric1")) {
                Assert.assertTrue(detail.getLostCheck());
            }
        }

        // create
        MetricDetail metricDetail4 = new MetricDetail();
        metricDetail4.setMetricName("metric4");
        metricDetail4.setSourceId(1L);

        // update
        MetricDetail metricDetail5 = new MetricDetail();
        metricDetail5.setMetricName("metric5");
        metricDetail5.setSourceId(1L);
        int res = metricDetailDao.batchInsertOrUpdateMetric(new ArrayList<>(Arrays.asList(metricDetail4, metricDetail5)),
            Column.metricName, Column.sourceId, Column.missingRate, Column.periodScore, Column.periodLength);
        Assert.assertTrue(res > 0);

        // create
        MetricDetail metricDetail6 = new MetricDetail();
        metricDetail6.setMetricName("metric6");
        metricDetail6.setSourceId(1L);
        int res1 = metricDetailDao.upsertMetricDetail(metricDetail6);
        Assert.assertTrue(res1 > 0);

        // update
        metricDetail6.setMetricName("metric66");
        metricDetailDao.upsertMetricDetail(metricDetail6);
        Assert.assertEquals("metric66", metricDetailDao.getMetricDetailById(metricDetail6.getDetailId()).getMetricName());
        Assert.assertEquals("metric66", metricDetailDao.getMetricDetailByName("metric66", 1L).getMetricName());
    }

    @Test
    public void test2() {
        Long now = System.currentTimeMillis();
        MetricDetail metricDetail1 = new MetricDetail();
        metricDetail1.setMetricName("metric1");
        metricDetail1.setPeriodLength(48);
        metricDetail1.setMetricAnomalyId(now);
        metricDetail1.setAnomalyCheck(true);
        metricDetail1.setExpertLostThreshold(86400L);
        metricDetail1.setLostCheck(false);
        metricDetail1.setMissingRate((byte)5);
        metricDetail1.setPeriodScore((byte)10);
        metricDetail1.setSourceId(now);
        metricDetailDao.addMetricDetail(metricDetail1);

        MetricDetail metricDetail2 = new MetricDetail();
        metricDetail2.setMetricName("metric2");
        metricDetail2.setPeriodLength(48);
        metricDetail2.setMetricAnomalyId(now);
        metricDetail2.setAnomalyCheck(true);
        metricDetail2.setExpertLostThreshold(86400L);
        metricDetail2.setLostCheck(true);
        metricDetail2.setMissingRate((byte)5);
        metricDetail2.setPeriodScore((byte)10);
        metricDetail2.setSourceId(now);
        metricDetailDao.addMetricDetail(metricDetail2);

        List<MetricDetail> details = metricDetailDao.getLostCheckMetricsBySourceId(now);
        Assertions.assertThat(details).hasSize(1).element(0).hasFieldOrPropertyWithValue("anomalyCheck", true);

        MetricDetail md = details.get(0);
        md.setLostThreshold(11111L);
        metricDetailDao.updateMetricDetailWithId(md);

        details = metricDetailDao.listMetricsByNames(now, Lists.newArrayList(md.getMetricName()));
        Assertions.assertThat(details).hasSize(1).element(0).hasFieldOrPropertyWithValue("lostThreshold", 11111L);

        int res = metricDetailDao.deleteDetailBySourceId(now);
        Assert.assertTrue(res >= 2);
    }

    @Test
    public void getAllLostCheckSourceId() {
        Long now = System.currentTimeMillis();
        MetricDetail metricDetail1 = new MetricDetail();
        metricDetail1.setMetricName("metric1");
        metricDetail1.setPeriodLength(48);
        metricDetail1.setMetricAnomalyId(now);
        metricDetail1.setAnomalyCheck(true);
        metricDetail1.setExpertLostThreshold(86400L);
        metricDetail1.setLostCheck(true);
        metricDetail1.setMissingRate((byte)5);
        metricDetail1.setPeriodScore((byte)10);
        metricDetail1.setSourceId(now);
        metricDetailDao.addMetricDetail(metricDetail1);
        metricDetail1.setMetricName("metric2");
        metricDetailDao.addMetricDetail(metricDetail1);
        metricDetail1.setSourceId(now + 1);
        metricDetail1.setLostCheck(false);
        metricDetailDao.addMetricDetail(metricDetail1);
        List<Long> ids = metricDetailDao.getAllLostCheckSourceId();
        Assert.assertTrue(ids.contains(now));
    }
}
