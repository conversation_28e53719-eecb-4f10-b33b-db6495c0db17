package com.aliyun.xdragon.biz.log.job;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.aliyun.xdragon.common.model.SlsRegionProject;
import com.aliyun.xdragon.service.common.job.JobHelper;
import com.aliyun.xdragon.service.common.model.JobTask;
import com.aliyun.xdragon.biz.log.config.MetricsConfig;
import com.aliyun.xdragon.biz.log.service.MetricsPickupService;
import com.aliyun.xdragon.biz.log.service.PickUpConfigService;
import com.aliyun.xdragon.biz.log.service.TsdbOperatorService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class MetricsPickUpJobTest {

    @Mock
    PickUpConfigService pickUpConfigService;

    @Mock
    MetricsPickupService metricsPickupService;

    @Mock
    TsdbOperatorService tsdbOperatorService;

    @InjectMocks
    MetricsPickUpJob job;

    @Mock
    Client slsClient;

    @Mock
    GetLogsResponse logsResponse;

    @Mock
    MetricsConfig config;

    @Before
    public void init() {
        Whitebox.setInternalState(job, "randomTimeRange", 1);
    }

    @Test
    public void testRootProcess() throws Exception {
        JobContext context = JobContext.newBuilder().setTaskName("MAP_TASK_ROOT").setDataTime(DateTime.now())
            .setScheduleTime(DateTime.now()).build();
        MetricsPickUpJob spyJob = Mockito.spy(job);
        MetricsConfig metricsConfig = new MetricsConfig();
        doReturn(Lists.newArrayList(metricsConfig)).when(pickUpConfigService).loadMetricsConfig();
        doReturn(getProcessResult()).when(spyJob).map(any(), anyString());
        ProcessResult result = spyJob.process(context);
        assert result.getStatus() == InstanceStatus.SUCCESS;

        JobContext context2 = JobContext.newBuilder().setTaskName("MAP_TASK_ROOT").setJobParameters(
            "{\"mode\":\"common_period\"}").setDataTime(DateTime.now()).setScheduleTime(DateTime.now()).build();
        spyJob.process(context2);
        verify(pickUpConfigService, times(1)).loadMetricsConfigCommonPeriod();
        verify(pickUpConfigService, times(0)).loadMetricsConfigCommon();

        JobContext context3 = JobContext.newBuilder().setTaskName("MAP_TASK_ROOT").setJobParameters(
            "{\"mode\":\"common\"}").setDataTime(DateTime.now()).setScheduleTime(DateTime.now()).build();
        spyJob.process(context3);
        verify(pickUpConfigService, times(1)).loadMetricsConfigCommonPeriod();
        verify(pickUpConfigService, times(1)).loadMetricsConfigCommon();

        JobContext context4 = JobContext.newBuilder().setTaskName("MAP_TASK_ROOT").setJobParameters(
            "{\"mode\":\"common123\"}").setDataTime(DateTime.now()).setScheduleTime(DateTime.now()).build();
        spyJob.process(context4);
        verify(pickUpConfigService, times(1)).loadMetricsConfigCommonPeriod();
        verify(pickUpConfigService, times(1)).loadMetricsConfigCommon();
        verify(pickUpConfigService, times(2)).loadMetricsConfig();
    }

    @Test
    public void testChildProcess() throws Exception {
        List<String> someModelList = new ArrayList<>();
        someModelList.add("xxx");
        when(config.getLabels()).thenReturn(someModelList);
        when(config.getProject()).thenReturn("ecs-xuanjian");
        when(config.getLogStore()).thenReturn("xxxx");
        when(config.getSlsQuery()).thenReturn("xxxx");
        when(config.getPickInterval()).thenReturn("3600");
        JobTask<MetricsConfig> task = new JobTask<>();
        task.setTask(config);
        task.setRequestId("123");
        SlsRegionProject slsRegionProject = new SlsRegionProject();
        slsRegionProject.setRegion("cn-hangzhou-crop");
        slsRegionProject.setUser("newbei-ecs");
        slsRegionProject.setRealProject("ecs-xunjian-hz");
        when(metricsPickupService.findProjectConfigsByName(anyString())).thenReturn(
            Lists.newArrayList(slsRegionProject));
        when(metricsPickupService.getSlsClient(any())).thenReturn(slsClient);
        when(metricsPickupService.getLogs(any(), anyString(), anyString(), anyInt(), anyInt(), anyString(),
                anyString())).thenReturn(getLogs());
        when(tsdbOperatorService.writeToTSDB(any(), any(), any(), anyInt(), eq(null))).thenReturn(true);
        when(tsdbOperatorService.writeToTSDB(anyList(), anyString(), anyInt(), eq(null))).thenReturn(true);
        when(tsdbOperatorService.buildLabels(anyString(), any(), any())).thenReturn(
            Collections.singletonList("key"));
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        DateTime d = DateTime.parse("2018-4-23 11:15:16", format);
        JobContext context = JobContext.newBuilder().setTaskName(JobHelper.SUB_TASK_KEY).setTask(task).setDataTime(d)
            .setScheduleTime(d).build();
        job.setRandomTimeRange(1);
        ProcessResult result = job.process(context);
        assert result.getStatus() == InstanceStatus.SUCCESS;

    }

    @Test
    public void testChildProcessNotInTime() throws Exception {
        List<String> someModelList = new ArrayList<>();
        someModelList.add("xxx");
        when(config.getLabels()).thenReturn(someModelList);
        when(config.getProject()).thenReturn("ecs-xuanjian");
        when(config.getLogStore()).thenReturn("xxxx");
        when(config.getSlsQuery()).thenReturn("xxxx");
        when(config.getInterval()).thenReturn(86400);
        when(config.getPickInterval()).thenReturn("86400");
        JobTask<MetricsConfig> task = new JobTask<>();
        task.setTask(config);
        task.setRequestId("123");
        SlsRegionProject slsRegionProject = new SlsRegionProject();
        slsRegionProject.setRegion("cn-hangzhou-crop");
        slsRegionProject.setUser("newbei-ecs");
        slsRegionProject.setRealProject("ecs-xunjian-hz");
        when(metricsPickupService.findProjectConfigsByName(anyString())).thenReturn(
            Lists.newArrayList(slsRegionProject));
        when(metricsPickupService.getSlsClient(any())).thenReturn(slsClient);
        when(metricsPickupService.getLogs(any(), anyString(), anyString(), anyInt(), anyInt(), anyString(),
                anyString())).thenReturn(getLogs());
        when(tsdbOperatorService.writeToTSDB(any(), any(), any(), anyInt(), eq(null))).thenReturn(true);
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        DateTime d = DateTime.parse("2018-4-23 13:12:16", format);
        JobContext context = JobContext.newBuilder().setTaskName(JobHelper.SUB_TASK_KEY).setTask(task).setDataTime(d)
            .setScheduleTime(d).build();
        job.setRandomTimeRange(1);
        ProcessResult result = job.process(context);
        assert result.getStatus() == InstanceStatus.SUCCESS;
    }

    @Test
    public void testChildProcessMinute() throws Exception {
        List<String> someModelList = new ArrayList<>();
        someModelList.add("xxx");
        when(config.getLabels()).thenReturn(someModelList);
        when(config.getProject()).thenReturn("ecs-xuanjian");
        when(config.getLogStore()).thenReturn("xxxx");
        when(config.getSlsQuery()).thenReturn("xxxx");
        when(config.getInterval()).thenReturn(30);
        when(config.getPickInterval()).thenReturn("60");
        JobTask<MetricsConfig> task = new JobTask<>();
        task.setTask(config);
        task.setRequestId("123");
        SlsRegionProject slsRegionProject = new SlsRegionProject();
        slsRegionProject.setRegion("cn-hangzhou-crop");
        slsRegionProject.setUser("newbei-ecs");
        slsRegionProject.setRealProject("ecs-xunjian-hz");
        when(metricsPickupService.findProjectConfigsByName(anyString())).thenReturn(
            Lists.newArrayList(slsRegionProject));
        when(metricsPickupService.getSlsClient(any())).thenReturn(slsClient);
        when(metricsPickupService.getLogs(any(), anyString(), anyString(), anyInt(), anyInt(), anyString(),
                anyString())).thenReturn(getLogs());
        when(tsdbOperatorService.writeToTSDB(any(), any(), any(), anyInt(), eq(null))).thenReturn(true);
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        DateTime d = DateTime.parse("2018-4-23 13:12:16", format);
        JobContext context = JobContext.newBuilder().setTaskName(JobHelper.SUB_TASK_KEY).setTask(task).setDataTime(d)
            .setScheduleTime(d).build();
        job.setRandomTimeRange(1);
        ProcessResult result = job.process(context);
        assert result.getStatus() == InstanceStatus.SUCCESS;
    }

    @Test
    public void testChildProcessTenMinutesNotInTime() throws Exception {
        List<String> someModelList = new ArrayList<>();
        someModelList.add("xxx");
        when(config.getLabels()).thenReturn(someModelList);
        when(config.getProject()).thenReturn("ecs-xuanjian");
        when(config.getLogStore()).thenReturn("xxxx");
        when(config.getSlsQuery()).thenReturn("xxxx");
        when(config.getInterval()).thenReturn(100);
        when(config.getPickInterval()).thenReturn("600");
        JobTask<MetricsConfig> task = new JobTask<>();
        task.setTask(config);
        task.setRequestId("123");
        SlsRegionProject slsRegionProject = new SlsRegionProject();
        slsRegionProject.setRegion("cn-hangzhou-crop");
        slsRegionProject.setUser("newbei-ecs");
        slsRegionProject.setRealProject("ecs-xunjian-hz");
        when(metricsPickupService.findProjectConfigsByName(anyString())).thenReturn(
            Lists.newArrayList(slsRegionProject));
        when(metricsPickupService.getSlsClient(any())).thenReturn(slsClient);
        when(metricsPickupService.getLogs(any(), anyString(), anyString(), anyInt(), anyInt(), anyString(),
                anyString())).thenReturn(getLogs());
        when(tsdbOperatorService.writeToTSDB(any(), any(), any(), anyInt(), eq(null))).thenReturn(true);
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        DateTime d = DateTime.parse("2018-4-23 13:14:16", format);
        JobContext context = JobContext.newBuilder().setTaskName(JobHelper.SUB_TASK_KEY).setTask(task).setDataTime(d)
            .setScheduleTime(d).build();
        job.setRandomTimeRange(1);
        ProcessResult result = job.process(context);
        assert result.getStatus() == InstanceStatus.SUCCESS;
    }

    @Test
    public void testChildProcessThreeHoursNotInTime() throws Exception {
        List<String> someModelList = new ArrayList<>();
        someModelList.add("xxx");
        when(config.getLabels()).thenReturn(someModelList);
        when(config.getProject()).thenReturn("ecs-xuanjian");
        when(config.getLogStore()).thenReturn("xxxx");
        when(config.getSlsQuery()).thenReturn("xxxx");
        when(config.getInterval()).thenReturn(10800);
        when(config.getPickInterval()).thenReturn("10800");
        JobTask<MetricsConfig> task = new JobTask<>();
        task.setTask(config);
        task.setRequestId("123");
        SlsRegionProject slsRegionProject = new SlsRegionProject();
        slsRegionProject.setRegion("cn-hangzhou-crop");
        slsRegionProject.setUser("newbei-ecs");
        slsRegionProject.setRealProject("ecs-xunjian-hz");
        when(metricsPickupService.findProjectConfigsByName(anyString())).thenReturn(
            Lists.newArrayList(slsRegionProject));
        when(metricsPickupService.getSlsClient(any())).thenReturn(slsClient);
        when(metricsPickupService.getLogs(any(), anyString(), anyString(), anyInt(), anyInt(), anyString(),
                anyString())).thenReturn(getLogs());
        when(tsdbOperatorService.writeToTSDB(any(), any(), any(), anyInt(), eq(null))).thenReturn(true);
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        DateTime d = DateTime.parse("2018-4-23 12:01:00", format);
        JobContext context = JobContext.newBuilder().setTaskName(JobHelper.SUB_TASK_KEY).setTask(task).setDataTime(d)
            .setScheduleTime(d).build();
        job.setRandomTimeRange(1);
        ProcessResult result = job.process(context);
        assert result.getStatus() == InstanceStatus.SUCCESS;
    }

    @Test
    public void testChildProcessError1() throws Exception {
        List<String> someModelList = new ArrayList<>();
        when(config.getLabels()).thenReturn(someModelList);
        when(config.getProject()).thenReturn("ecs-xuanjian");
        when(config.getLogStore()).thenReturn("xxxx");
        when(config.getSlsQuery()).thenReturn("xxxx");
        when(config.getPickInterval()).thenReturn("3600");
        JobTask<MetricsConfig> task = new JobTask<>();
        task.setTask(config);
        task.setRequestId("123");
        SlsRegionProject slsRegionProject = new SlsRegionProject();
        slsRegionProject.setRegion("cn-hangzhou-crop");
        slsRegionProject.setUser("newbei-ecs");
        slsRegionProject.setRealProject("ecs-xunjian-hz");
        when(metricsPickupService.findProjectConfigsByName(anyString())).thenReturn(
            Lists.newArrayList(slsRegionProject));
        when(metricsPickupService.getSlsClient(any())).thenReturn(null);
        when(metricsPickupService.getLogs(any(), anyString(), anyString(), anyInt(), anyInt(), anyString(),
                anyString())).thenReturn(getLogs());
        when(tsdbOperatorService.writeToTSDB(any(), any(), any(), anyInt(), eq(null))).thenReturn(true);
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        DateTime d = DateTime.parse("2018-4-23 11:15:16", format);
        JobContext context = JobContext.newBuilder().setTaskName(JobHelper.SUB_TASK_KEY).setTask(task).setDataTime(d)
            .setScheduleTime(d).build();
        job.setRandomTimeRange(1);
        ProcessResult result = job.process(context);
        assert result.getStatus() == InstanceStatus.SUCCESS;
    }

    @Test
    public void testChildProcessError2() throws Exception {
        List<String> someModelList = new ArrayList<>();
        when(config.getLabels()).thenReturn(someModelList);
        when(config.getProject()).thenReturn("ecs-xuanjian");
        when(config.getLogStore()).thenReturn("xxxx");
        when(config.getSlsQuery()).thenReturn("xxxx");
        when(config.getPickInterval()).thenReturn("3600");
        JobTask<MetricsConfig> task = new JobTask<>();
        task.setTask(config);
        task.setRequestId("123");
        SlsRegionProject slsRegionProject = new SlsRegionProject();
        slsRegionProject.setRegion("cn-hangzhou-crop");
        slsRegionProject.setUser("newbei-ecs");
        slsRegionProject.setRealProject("ecs-xunjian-hz");
        when(metricsPickupService.findProjectConfigsByName(anyString())).thenReturn(
            Lists.newArrayList(slsRegionProject));
        when(metricsPickupService.getSlsClient(any())).thenReturn(null);
        when(metricsPickupService.getLogs(any(), anyString(), anyString(), anyInt(), anyInt(), anyString(),
                anyString())).thenReturn(null);
        when(tsdbOperatorService.writeToTSDB(any(), any(), any(), anyInt(), eq(null))).thenReturn(true);
        job.setRandomTimeRange(1);
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        DateTime d = DateTime.parse("2018-4-23 11:15:16", format);
        JobContext context = JobContext.newBuilder().setScheduleTime(d).setDataTime(d).setTaskName(
                JobHelper.SUB_TASK_KEY).setTask(task).build();
        ProcessResult result = job.process(context);
        assert result.getStatus() == InstanceStatus.SUCCESS;

    }

    @Test
    public void testChildProcessError3() throws Exception {
        List<String> someModelList = new ArrayList<>();
        when(config.getLabels()).thenReturn(someModelList);
        when(config.getProject()).thenReturn("ecs-xuanjian");
        when(config.getLogStore()).thenReturn("xxxx");
        when(config.getSlsQuery()).thenReturn("xxxx");
        when(config.getPickInterval()).thenReturn("3600");
        JobTask<MetricsConfig> task = new JobTask<>();
        task.setTask(config);
        task.setRequestId("123");
        SlsRegionProject slsRegionProject = new SlsRegionProject();
        slsRegionProject.setRegion("cn-hangzhou-crop");
        slsRegionProject.setUser("newbei-ecs");
        slsRegionProject.setRealProject("ecs-xunjian-hz");
        when(metricsPickupService.findProjectConfigsByName(anyString())).thenReturn(
            Lists.newArrayList(slsRegionProject));
        when(metricsPickupService.getSlsClient(any())).thenReturn(slsClient);
        when(metricsPickupService.getLogs(any(), anyString(), anyString(), anyInt(), anyInt(), anyString(),
                anyString())).thenReturn(null);
        when(tsdbOperatorService.writeToTSDB(any(), any(), any(), anyInt(), eq(null))).thenReturn(true);
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        DateTime d = DateTime.parse("2018-4-23 11:15:16", format);
        JobContext context = JobContext.newBuilder().setTaskName(JobHelper.SUB_TASK_KEY).setTask(task).setDataTime(d)
            .setScheduleTime(d).build();
        job.setRandomTimeRange(1);
        ProcessResult result = job.process(context);
        assert result.getStatus() == InstanceStatus.SUCCESS;
    }

    @Test
    public void testChildProcessError4() throws Exception {
        List<String> someModelList = new ArrayList<>();
        when(config.getLabels()).thenReturn(someModelList);
        when(config.getProject()).thenReturn("ecs-xuanjian");
        when(config.getLogStore()).thenReturn("xxxx");
        when(config.getSlsQuery()).thenReturn("xxxx");
        when(config.getPickInterval()).thenReturn("3600");
        JobTask<MetricsConfig> task = new JobTask<>();
        task.setTask(config);
        task.setRequestId("123");
        SlsRegionProject slsRegionProject = new SlsRegionProject();
        slsRegionProject.setRegion("cn-hangzhou-crop");
        slsRegionProject.setUser("newbei-ecs");
        slsRegionProject.setRealProject("ecs-xunjian-hz");
        when(metricsPickupService.findProjectConfigsByName(anyString())).thenReturn(
            Lists.newArrayList(slsRegionProject));
        when(metricsPickupService.getSlsClient(any())).thenReturn(slsClient);
        when(metricsPickupService.getLogs(any(), anyString(), anyString(), anyInt(), anyInt(), anyString(),
                anyString())).thenReturn(getLogs());
        when(tsdbOperatorService.writeToTSDB(any(), any(), any(), anyInt(), eq(null))).thenThrow(new RuntimeException());
        DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        DateTime d = DateTime.parse("2018-4-23 11:15:16", format);
        JobContext context = JobContext.newBuilder().setTaskName(JobHelper.SUB_TASK_KEY).setTask(task).setDataTime(d)
            .setScheduleTime(d).build();
        job.setRandomTimeRange(1);
        ProcessResult result = job.process(context);
        assert result.getStatus() == InstanceStatus.SUCCESS;

    }

    //@Test
    //public void testChildProcessError5() throws Exception {
    //    List<String> someModelList = new ArrayList<>();
    //    when(config.getLabels()).thenReturn(someModelList);
    //    when(config.getProject()).thenReturn("ecs-xuanjian");
    //    when(config.getLogStore()).thenReturn("xxxx");
    //    when(config.getSlsQuery()).thenReturn("xxxx");
    //    when(config.getPickInterval()).thenReturn("3600");
    //    JobTask<MetricsConfig> task = new JobTask<>();
    //    task.setTask(config);
    //    task.setRequestId("123");
    //    DateTimeFormatter format = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
    //    DateTime d = DateTime.parse("2018-4-23 11:15:16", format);
    //    when(metricsPickupService.findProjectConfigsByName(anyString())).thenReturn(new LinkedList<>());
    //    JobContext context = JobContext.newBuilder().setTaskName(JobHelper.SUB_TASK_KEY).setTask(task).setDataTime(d)
    //        .setScheduleTime(d).build();
    //    job.setRandomTimeRange(1);
    //    ProcessResult result = job.process(context);
    //    assert result.getStatus() == InstanceStatus.SUCCESS;
    //}

    @Test
    public void mergePartValueToTotal() {
        LogContent logContent = new LogContent("testKey", "123");
        Map<String, Integer> m = new HashMap<>();
        job.mergePartValueToTotal(logContent, m);
        assert m.size() == 1;

        logContent = new LogContent("testKey", "test2");
        Map<String, Integer> m1 = new HashMap<>();
        job.mergePartValueToTotal(logContent, m1);
        assert m1.size() == 0;
    }

    @Test
    public void emptyConfig() throws Exception {
        JobContext context = JobContext.newBuilder().setTaskName("MAP_TASK_ROOT").setJobParameters(
            "{\"mode\":\"common\"}").setDataTime(DateTime.now()).setScheduleTime(DateTime.now()).build();
        List<SlsRegionProject> regionProjects = new LinkedList<>();
        SlsRegionProject p1 = new SlsRegionProject();
        p1.setRegion("cn-hangzhou");
        regionProjects.add(p1);
        when(metricsPickupService.findProjectConfigsByName(anyString())).thenReturn(regionProjects);
        MetricsConfig metricsConfig = new MetricsConfig();
        metricsConfig.setProject("151");
        metricsConfig.setRegion("cn-beijing");
        metricsConfig.setInterval(60);
        ProcessResult result = job.processSubTask(context, 123, 123, metricsConfig);
        Assert.assertTrue(result.getResult().contains("no project 151 for region cn-beijing"));
    }

    @Test
    public void writeNc() throws Exception {
        JobContext context = JobContext.newBuilder().setTaskName("MAP_TASK_ROOT").setJobParameters(
            "{\"mode\":\"common_period\"}").setDataTime(DateTime.now()).setScheduleTime(DateTime.now()).build();
        MetricsConfig c = new MetricsConfig();
        c.setInterval(3600);
        c.setPickInterval("3600");
        c.setProject("ecs-xunjian");
        c.setLogStore("pync");
        c.setSlsQuery("sql");
        c.setNcKey("ip");
        c.setQueryType(1);
        c.setNcFilterType(1);
        int runTs = 1677640320;
        job.setRandomTimeRange(1);
        SlsRegionProject slsRegionProject = new SlsRegionProject();
        slsRegionProject.setRegion("cn-hangzhou-crop");
        slsRegionProject.setUser("newbei-ecs");
        slsRegionProject.setRealProject("ecs-xunjian-hz");
        when(metricsPickupService.findProjectConfigsByName(anyString())).thenReturn(
            Lists.newArrayList(slsRegionProject));
        when(metricsPickupService.getSlsClient(any())).thenReturn(slsClient);
        when(metricsPickupService.getLogs(any(), anyString(), anyString(), anyInt(), anyInt(), anyString(),
            anyString())).thenReturn(getLogs());
        Map<String, Object> m = new HashMap<>();
        m.put("metric", "metric");
        m.put("timestamp", 123);
        m.put("value", "99");
        Map<String, Object> add = new HashMap<>();
        add.put("logstore", "store");
        add.put("project", "pro");
        add.put("interval", "600");
        add.put("total_nc_count", 100);
        add.put("log_nc_count", 99);
        m.put("additional", add);
        when(tsdbOperatorService.writeLogCovToTSDB(any(), any(), any(), anyInt(), eq(null))).thenReturn(Pair.of(true, m));
        when(tsdbOperatorService.writeLogCovToTSDB(any(), anyInt(), anyInt(), eq(null))).thenReturn(Pair.of(true, m));
        when(metricsPickupService.isLogStoreExist(any(), anyString(), anyString())).thenReturn(true);
        job.processSubTask(context, runTs, runTs, c);
        ThreadPoolExecutor pool = Whitebox.getInternalState(job, "executorService");
        while (pool.getActiveCount() > 0) {
            Thread.sleep(100L);
        }

        verify(metricsPickupService, times(2)).addCovDetail(any());
        verify(metricsPickupService, times(1)).addCovDetailNc(any(), any(), anyInt(), anyString(), anyInt(), eq(null));
    }

    @Test
    public void queryDetail() throws Exception {
        JobContext context = JobContext.newBuilder().setTaskName("MAP_TASK_ROOT").setJobParameters(
            "{\"mode\":\"common_period\"}").setDataTime(DateTime.now()).setScheduleTime(DateTime.now()).build();
        MetricsConfig c = new MetricsConfig();
        c.setInterval(3600);
        c.setPickInterval("3600");
        c.setProject("ecs-xunjian");
        c.setLogStore("pync");
        c.setSlsQuery("sql");
        c.setQueryType(2);
        c.setNcKey("ip");
        c.setNcFilterType(1);
        int runTs = 1677640320;
        job.setRandomTimeRange(1);
        SlsRegionProject slsRegionProject = new SlsRegionProject();
        slsRegionProject.setRegion("cn-hangzhou-crop");
        slsRegionProject.setUser("newbei-ecs");
        slsRegionProject.setRealProject("ecs-xunjian-hz");
        when(metricsPickupService.findProjectConfigsByName(anyString())).thenReturn(
            Lists.newArrayList(slsRegionProject));
        when(metricsPickupService.getSlsClient(any())).thenReturn(slsClient);

        Set<String> logIps = new HashSet();
        Set<String> filterNcs = new HashSet<>();
        for (int i = 1; i < 15; i++) {
            if (i < 10) {
                filterNcs.add(String.valueOf(i));
            }
            logIps.add(String.valueOf(i));
        }
        filterNcs.add("15");
        when(metricsPickupService.getLogIps(any(Client.class), anyString(), anyString(), anyInt(), anyInt(), anyInt(),
            anyString())).thenReturn(logIps);
        when(metricsPickupService.getFilterNc(anyInt(), anyInt(), anyString())).thenReturn(filterNcs);
        Map<String, Object> m = new HashMap<>();
        m.put("metric", "metric");
        m.put("timestamp", 123);
        m.put("value", "99");
        Map<String, Object> add = new HashMap<>();
        add.put("logstore", "store");
        add.put("project", "pro");
        add.put("interval", "600");
        add.put("total_nc_count", 100);
        add.put("log_nc_count", 99);
        m.put("additional", add);
        when(tsdbOperatorService.writeLogCovToTSDB(any(), any(), any(), anyInt(), eq(null))).thenReturn(Pair.of(true, m));
        when(tsdbOperatorService.writeLogCovToTSDB(any(), anyInt(), anyInt(), eq(null))).thenReturn(Pair.of(true, m));
        ArgumentCaptor<List<String>> missCap = ArgumentCaptor.forClass(List.class);
        doNothing().when(metricsPickupService).addCovDetailNc(any(), any(), anyInt(), anyString(), anyInt(), missCap.capture());
        when(metricsPickupService.isLogStoreExist(any(Client.class), anyString(), anyString())).thenReturn(true);
        job.processSubTask(context, runTs, runTs, c);
        ThreadPoolExecutor pool = Whitebox.getInternalState(job, "executorService");
        while (pool.getActiveCount() > 0) {
            Thread.sleep(100L);
        }

        verify(metricsPickupService, times(2)).addCovDetail(any());
        verify(metricsPickupService, times(1)).addCovDetailNc(any(), any(), anyInt(), anyString(), anyInt(), anyList());
        List<String> missNcs = missCap.getValue();
        Assert.assertEquals(1, missNcs.size());
        Assert.assertTrue(missNcs.contains("15"));
    }

    private ProcessResult getProcessResult() {
        return new ProcessResult(true);
    }

    private ArrayList<QueriedLog> getLogs() {
        ArrayList<QueriedLog> list = new ArrayList<>();
        LogContent logContent = new LogContent("nccnt", "123");
        LogContent logContent1 = new LogContent("cnt", "456");
        ArrayList<LogContent> mContents = new ArrayList<>();
        mContents.add(logContent);
        mContents.add(logContent1);
        LogItem item = new LogItem(123, mContents);
        QueriedLog log = new QueriedLog("source", item);
        list.add(log);
        return list;
    }
}
