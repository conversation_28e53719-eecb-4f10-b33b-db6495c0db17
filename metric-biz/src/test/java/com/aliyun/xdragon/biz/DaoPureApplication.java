package com.aliyun.xdragon.biz;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @date 2022/04/13
 */
@SpringBootApplication(scanBasePackages = {"com.aliyun.xdragon"})
@ComponentScan(lazyInit = true, useDefaultFilters = false)
@Import({DBConfig.class, ADBConfig.class, PlanDBConfig.class})
public class DaoPureApplication {
    public static void main(String[] args) {
        SpringApplication.run(DaoPureApplication.class);
    }
}
