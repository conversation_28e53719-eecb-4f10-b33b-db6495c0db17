package com.aliyun.xdragon.biz.log.job;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.xdragon.biz.log.repository.ClusterTaskDao;
import com.aliyun.xdragon.biz.log.repository.PatternDao;
import com.aliyun.xdragon.biz.log.repository.PatternTagDao;
import com.aliyun.xdragon.common.generate.log.model.LogPatternTag;
import com.aliyun.xdragon.common.generate.model.LogClusterTask;
import com.aliyun.xdragon.service.common.service.XdragonAlertService;
import com.aliyuncs.ecsops.model.v20160401.OpsXdragonSendAlertResponse;
import com.aliyuncs.exceptions.ClientException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class LogCountAlarmJobTest {
    @InjectMocks
    private LogCountAlarmJob job;

    @Mock
    private PatternTagDao patternTagDao;

    @Mock
    private PatternDao patternDao;

    @Mock
    private ClusterTaskDao clusterTaskDao;

    @Mock
    private XdragonAlertService alertService;

    @Test
    public void testGenTask() {
        List<Long> taskIds = new ArrayList<>(2);
        taskIds.add(1L);
        taskIds.add(132L);
        when(patternTagDao.getToAlarmTaskIds()).thenReturn(taskIds);
        List<Long> task = job.genTask(null, 0);
        Assert.assertNotNull(task);
        Assert.assertEquals(1, task.size());
        Assert.assertEquals(1L, task.get(0).longValue());
    }

    @Test
    public void testProcessSubTask() throws Exception {
        ProcessResult result = job.processSubTask(null, 0, 0, 1L);
        Assert.assertNotNull(result);
        Assert.assertEquals(InstanceStatus.FAILED, result.getStatus());

        LogClusterTask task = new LogClusterTask();
        when(clusterTaskDao.getTaskById(anyLong())).thenReturn(task);
        when(patternTagDao.getToAlarmTagsByTaskId(anyLong())).thenReturn(Collections.emptyList());
        result = job.processSubTask(null, 0, 0, 1L);
        Assert.assertNotNull(result);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());

        LogPatternTag tag = new LogPatternTag();
        tag.setIgnore(0);
        tag.setFlag(0);
        tag.setMd5("md5");
        tag.setAlarmDetail("{\"allRegion\":true,\"allRegionTimeRange\":\"30\",\"allAlarm\":[{\"minRange\":\"0\",\"maxRange\":\"100\",\"severity\":\"low_warning\"},{\"minRange\":\"100\",\"maxRange\":\"1000\",\"severity\":\"critical\"},{\"minRange\":\"1000\",\"maxRange\":\"1000000\",\"severity\":\"fatal\"}],\"region\":null,\"regionConfig\":[{\"regionNames\":null,\"timeRange\":null,\"alarm\":[{\"minRange\":null,\"maxRange\":null,\"severity\":null}]}]}");
        when(patternTagDao.getToAlarmTagsByTaskId(anyLong())).thenReturn(Collections.singletonList(tag));
        when(patternDao.countSumPatterns(anyLong(), any(Date.class), any(Date.class), any(List.class), any(List.class))).thenReturn(100L);
        when(alertService.sendAlert(any(List.class), any(), any(List.class), any(), any())).thenThrow(new ClientException("client exception"));
        result = job.processSubTask(null, 123456789, 123456789, 1L);
        Assert.assertNotNull(result);
        Assert.assertEquals(InstanceStatus.FAILED, result.getStatus());

        OpsXdragonSendAlertResponse resp = new OpsXdragonSendAlertResponse();
        List<OpsXdragonSendAlertResponse.SendResponse> responses = new ArrayList<>();
        OpsXdragonSendAlertResponse.SendResponse response1 = new OpsXdragonSendAlertResponse.SendResponse();
        response1.setSuccessed(true);
        OpsXdragonSendAlertResponse.SendResponse response2 = new OpsXdragonSendAlertResponse.SendResponse();
        response2.setSuccessed(false);
        responses.add(response1);
        responses.add(response2);
        resp.setSendResponses(responses);
        when(alertService.sendAlert(any(List.class), any(), any(List.class), any(), any())).thenReturn(resp);
        result = job.processSubTask(null, 123456789, 123456789, 1L);
        Assert.assertNotNull(result);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());
    }
}