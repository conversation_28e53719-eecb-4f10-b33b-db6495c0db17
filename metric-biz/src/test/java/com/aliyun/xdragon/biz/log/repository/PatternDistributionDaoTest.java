package com.aliyun.xdragon.biz.log.repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import com.aliyun.xdragon.biz.AbstractADBTest;
import com.aliyun.xdragon.common.generate.log.model.ClusterDistributionDetail;
import com.aliyun.xdragon.common.generate.log.model.LogClusterPattern;
import com.aliyun.xdragon.common.generate.log.model.NcAssociationInfo;
import com.aliyun.xdragon.common.generate.log.model.map.ClusterDistributionDetailCustomMapper;
import com.aliyun.xdragon.common.generate.log.model.map.ClusterDistributionDetailMapper;
import com.aliyun.xdragon.common.model.ExtInfo;
import com.aliyun.xdragon.common.model.log.DayTimeRange;
import com.aliyun.xdragon.common.model.log.FieldDetail;
import com.aliyun.xdragon.common.model.log.LogClusterPatternExt;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

import static com.aliyun.xdragon.service.common.util.DateUtil.firstSecondForDate;

/**
 * <AUTHOR>
 * @date 2022/08/17
 */
@Import({PatternDistributionDao.class, NcAssociationInfoDao.class})
public class PatternDistributionDaoTest extends AbstractADBTest {
    @Autowired
    private PatternDistributionDao distributionDao;

    @Autowired
    private NcAssociationInfoDao ncAssociationInfoDao;

    @Autowired
    private ClusterDistributionDetailMapper detailMapper;

    @Autowired
    private ClusterDistributionDetailCustomMapper customMapper;

    private void addDistribution(Long taskId, long timeMs) {
        addDistribution(taskId, timeMs, null);
    }

    private void addDistribution(Long taskId, long timeMs, String key) {
        ClusterDistributionDetail distribution = new ClusterDistributionDetail();
        distribution.setTaskId(taskId);
        distribution.setRegion("center");
        distribution.setTime(new Date(timeMs));
        distribution.setTypeId(0L);
        if (key == null) {
            distribution.setDetailKey("*******");
        } else {
            distribution.setDetailKey(key);
        }
        distribution.setSupport(1000L);
        distribution.setDetailSupport(100L);
        distribution.setHashcode(timeMs);
        distribution.setMd5(String.valueOf(taskId));
        detailMapper.insertSelective(distribution);
    }

    private void addNcAssociation(long timeMs) {
        addNcAssociation(timeMs, null, null);
    }

    private void addNcAssociation(long timeMs, String ncIp, String virtType) {
        List<NcAssociationInfo> ncInfoList = new ArrayList<>();
        Date date = new Date(timeMs);
        NcAssociationInfo ncInfo = new NcAssociationInfo();
        ncInfo.setTime(date);
        if (ncIp == null) {
            ncInfo.setNcip("*******");
        } else {
            ncInfo.setNcip(ncIp);
        }
        if (virtType == null) {
            ncInfo.setVirtType("dragon");
        } else {
            ncInfo.setVirtType(virtType);
        }
        ncInfo.setVirtType2("MOC2.0");
        ncInfoList.add(ncInfo);
        ncAssociationInfoDao.batchInsertAssociationInfo(ncInfoList);
        ncInfo.setTime(firstSecondForDate(date));
        ncAssociationInfoDao.batchInsertAssociationInfo(ncInfoList);
    }

    @Test
    public void testGetTopFieldList() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        addDistribution(5L, now);
        addDistribution(5L, now + 86400L * 1000L);

        Date start = new Date(now - 86400L * 1000L);
        Date end = new Date(now + 86401L * 1000L);

        List<FieldDetail> ips = distributionDao.getTopFieldList(5L, null, start, end, String.valueOf(5L), 0,
            null, 10, false);
        Assert.assertNotNull(ips);
        Assert.assertFalse(ips.isEmpty());
    }

    @Test
    public void testGetSpecialTopFieldList() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        addDistribution(5L, now);
        addDistribution(5L, now + 86400L * 1000L);

        Date start = new Date(now - 86400L * 1000L);
        Date end = new Date(now + 86401L * 1000L);

        List<FieldDetail> ips = distributionDao.getSpecialTopFieldList(5L, null, start, end, new Date(now),
            String.valueOf(5L), "cluster_id", 10, false);
        Assert.assertNotNull(ips);
        Assert.assertTrue(ips.isEmpty());
    }

    @Test
    public void testGetDistinctNcList() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        addDistribution(5L, now);

        Date start = new Date(now - 86400L * 1000L);
        Date end = new Date(now + 86400L * 1000L);
        List<ExtInfo> ncInfoList = distributionDao.getDistinctNcList(5L, start, end, false);
        Assert.assertNotNull(ncInfoList);
        Assert.assertFalse(ncInfoList.isEmpty());
    }

    @Test
    public void testGetExtensionDistribution() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        addDistribution(5L, now);

        Date start = new Date(now - 86400L * 1000L);
        Date end = new Date(now + 86400L * 1000L);
        List<ExtInfo> extList = distributionDao.getExtensionDistribution(5L, start, end,
                Collections.singletonList("center"), 0L,
            String.valueOf(5L), false);
        Assert.assertNotNull(extList);
        Assert.assertFalse(extList.isEmpty());
    }

    @Test
    public void testGetNcDistributionWithLimit() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        addDistribution(5L, now);
        addNcAssociation(now);

        Date start = new Date(now - 86400L * 1000L);
        Date end = new Date(now + 86400L * 1000L);
        List<Pair<String, Long>> ncList = distributionDao.getNcDistributionWithLimit(5L, start, end,
                firstSecondForDate(start), end, Collections.singletonList("center"), String.valueOf(5L), "virt_type", 31, false);
        Assert.assertNotNull(ncList);
        Assert.assertFalse(ncList.isEmpty());
    }

    @Test
    public void testGetMd5sWithCondition() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;

        addDistribution(now, now);
        addNcAssociation(now);

        Date start = new Date(now - 86400L * 1000L);
        Date end = new Date(now + 86400L * 1000L);
        Set<String> excludeMd5 = new HashSet<>(Collections.singletonList(String.valueOf(now)));
        Pair<DayTimeRange, List<String>> rawHashcodeMap = distributionDao.getMd5sWithConditionByDay(now, start, end,
            new Date(now), null, excludeMd5, true, null, 0,
                Collections.singletonList("*******"), false);
        Map<DayTimeRange, List<String>> hashcodeMap = parseMd5sWithConditionByDay(rawHashcodeMap);
        Assert.assertNotNull(hashcodeMap);
        Assert.assertFalse(hashcodeMap.isEmpty());

        Map<String, List<String>> conditionMap = new HashMap<>(4);
        List<String> virtList = new ArrayList<>(4);
        virtList.add("dragon");
        conditionMap.put("virt_type", virtList);
        rawHashcodeMap = distributionDao.getMd5sWithConditionByDay(now, start, end, new Date(now), null,
            excludeMd5, true, conditionMap, -1, null, false);
        hashcodeMap = parseMd5sWithConditionByDay(rawHashcodeMap);
        Assert.assertNotNull(hashcodeMap);
        Assert.assertFalse(hashcodeMap.isEmpty());
    }

    @Test
    public void testGetClusterPatternWithCondition() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;

        addDistribution(now, now);
        addNcAssociation(now);
        Date start = new Date(now - 86400L * 1000L);
        Date end = new Date(now + 86400L * 1000L);
        Set<String> md5s = new HashSet<>(8);
        md5s.addAll(Arrays.asList(String.valueOf(now), "a", "b", "c"));
        List<LogClusterPattern> patterns = distributionDao.getMd5SumWithConditionByDay(now, start, end, new Date(now),
                Collections.singletonList("center"), md5s, true, null, 0, Collections.singletonList("*******"), false);
        Assert.assertNotNull(patterns);
        Assert.assertFalse(patterns.isEmpty());

        Map<String, List<String>> conditionMap = new HashMap<>(4);
        List<String> virtList = new ArrayList<>(4);
        virtList.add("dragon");
        conditionMap.put("virt_type", virtList);

        patterns = distributionDao.getMd5SumWithConditionByDay(now, start, end, new Date(now),
                Collections.singletonList("center"),
            md5s, true, conditionMap, -1, null, false);
        Assert.assertNotNull(patterns);
        Assert.assertFalse(patterns.isEmpty());
    }

    @Test
    public void testListSumMd5WithCondition() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        addDistribution(now, now);
        addNcAssociation(now);

        Date start = new Date(now - 86400L * 1000L);
        Date end = new Date(now + 86400L * 1000L);
        Set<String> md5s = new HashSet<>(8);
        md5s.addAll(Arrays.asList(String.valueOf(now), String.valueOf(now + 1), String.valueOf(now + 2),
            String.valueOf(now + 3)));
        Set<String> idMd5s = new HashSet<>(8);
        md5s.forEach(m -> idMd5s.add(now + "|" + m));
        List<String> extValues = Collections.singletonList("*******");

        List<LogClusterPatternExt> patterns = distributionDao.listSumMd5WithConditionByDay(now, start, end,
                new Date(now), Collections.singletonList("center"), new LinkedList<>(md5s), new LinkedList<>(md5s), true,
            null, 0, extValues, false);
        Assert.assertNotNull(patterns);
        Assert.assertFalse(patterns.isEmpty());
        patterns = distributionDao.listSumMd5WithConditionByDay(now, start, end, new Date(now), new LinkedList<>(idMd5s),
                new LinkedList<>(idMd5s), true, null, 0, extValues, false);
        Assert.assertNotNull(patterns);
        Assert.assertFalse(patterns.isEmpty());

        Map<String, List<String>> conditionMap = new HashMap<>(4);
        List<String> virtList = new ArrayList<>(4);
        virtList.add("dragon");
        conditionMap.put("virt_type", virtList);

        List<LogClusterPatternExt> patterns1 = distributionDao.listSumMd5WithConditionByDay(now, start, end,
                new Date(now), Collections.singletonList("center"), new LinkedList<>(md5s), new LinkedList<>(md5s), true,
            conditionMap, -1, null, false);

        List<LogClusterPatternExt> patterns2 = distributionDao.listSumMd5WithConditionByDay(now, start, end,
                new Date(now), Collections.singletonList("center"), new ArrayList<>(md5s), null, true,
            conditionMap, -1, null, false);

        List<LogClusterPatternExt> patterns3 = distributionDao.listSumMd5WithConditionByDay(now, start, end,
                new Date(now), Collections.singletonList("center"), new ArrayList<>(md5s), new ArrayList<>(), true,
            conditionMap, -1, null, false);

        List<LogClusterPatternExt> patterns4 = distributionDao.listSumMd5WithConditionByDay(now, start, end,
                new Date(now), Collections.singletonList("center"), new ArrayList<>(md5s), Lists.newArrayList(String.valueOf(now)),
            false, conditionMap, -1, null, false);

        md5s.remove(String.valueOf(now));
        List<LogClusterPatternExt> patterns5 = distributionDao.listSumMd5WithConditionByDay(now, start, end,
                new Date(now), Collections.singletonList("center"), new ArrayList<>(md5s), Lists.newArrayList(String.valueOf(now),
                String.valueOf(now + 1)), false, conditionMap, -1, null, false);
        Assert.assertNotNull(patterns1);
        Assert.assertFalse(patterns1.isEmpty());
        Assert.assertNotNull(patterns2);
        Assert.assertFalse(patterns2.isEmpty());
        Assert.assertTrue(patterns3.isEmpty());
        Assert.assertEquals(0, patterns4.size());
        Assert.assertEquals(1, patterns5.size());
    }

    @Test
    public void createAndCheckTable() throws InterruptedException {
        long taskId = System.currentTimeMillis() % 10000000L * 1000L;
        try {
            boolean exist = distributionDao.checkTable(taskId);
            Assert.assertFalse(exist);
            distributionDao.createTable(taskId);
            exist = distributionDao.checkTable(taskId);
            Assert.assertTrue(exist);

            addDistribution(taskId, taskId);
            addDistribution(taskId, taskId);
            distributionDao.sumDetail(taskId, new Date(taskId - 1000), new Date(taskId + 1000));

            int cnt = customMapper.countDetail(taskId, new Date(taskId - 1000), new Date(taskId + 1000), true);
            Assert.assertEquals(1, cnt);
            List<Map<String, Object>> ms = customMapper.queryDetail(taskId, new Date(taskId - 1000),
                new Date(taskId + 1000), true);
            Assert.assertEquals(200L, ms.get(0).get("detail_support"));
            Assert.assertEquals(2000L, ms.get(0).get("support"));

            customMapper.deleteDetail(taskId, new Date(taskId - 1000), new Date(taskId + 1000), true);
            Thread.sleep(1000);
            cnt = customMapper.countDetail(taskId, new Date(taskId - 2000), new Date(taskId + 2000), true);
            Assert.assertEquals(0, cnt);
        } finally {
            customMapper.dropTable(taskId);
        }
    }

    @Test
    public void getRegionIpMap() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        addDistribution(5L, now);

        Date start = new Date(now - 86400L * 1000L);
        Date end = new Date(now + 86400L * 1000L);

        Map<String, List<String>> regionIpMap = distributionDao.getRegionIpMap(5L, start, end, null, null, null, false);
        Assert.assertNotNull(regionIpMap);
        Assert.assertFalse(regionIpMap.isEmpty());

        regionIpMap = distributionDao.getRegionIpMap(5L, start, end, null, Collections.singletonList("*******"), null, false);
        Assert.assertNotNull(regionIpMap);
        Assert.assertFalse(regionIpMap.isEmpty());
    }

    @Test
    public void getPatternSeriesByType() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        addDistribution(now, now);
        addDistribution(now, now);
        addDistribution(now, now + 300 * 1000);
        List<ClusterDistributionDetail> details = distributionDao.getPatternSeriesByType(now, 0,
            Lists.newArrayList(String.valueOf(now)), new Date(now - 1000), new Date(now + 301000),
            Lists.newArrayList("center"), false);
        Assert.assertEquals(2, details.size());
        Assert.assertEquals(new Long(200), details.get(0).getDetailSupport());
        Assert.assertEquals(new Long(100), details.get(1).getDetailSupport());
    }

    @Test
    public void getPatternSeriesByDim() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        long taskId = now;
        addDistribution(taskId, now);
        addDistribution(taskId, now + 86400 * 1000);
        addNcAssociation(now);
        addNcAssociation(now + 86400 * 1000);
        List<ClusterDistributionDetail> details = distributionDao.getPatternSeriesByDim(taskId, "virt_type",
            Lists.newArrayList(String.valueOf(taskId)), now - 600000, now + 86900000, null,
            false);
        Assert.assertEquals(2, details.size());
        Assert.assertEquals(new Long(100), details.get(1).getDetailSupport());
    }

    @Test
    public void drillByTypes() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        long taskId = now;
        addDistribution(taskId, now);
        addDistribution(taskId, now);
        List<ClusterDistributionDetail> details = distributionDao.drillByTypes(taskId, new Date(now),
            String.valueOf(taskId), "center", false);
        Assert.assertEquals(1, details.size());
        Assert.assertEquals(new Long(200), details.get(0).getDetailSupport());
    }

    @Test
    public void drillByDim() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        long taskId = now;
        addDistribution(taskId, now);
        addNcAssociation(now);
        addDistribution(taskId, now, "1.2.3.5");
        addNcAssociation(now, "1.2.3.5", "kvm");
        List<ClusterDistributionDetail> details = distributionDao.drillByDim(taskId, new Date(now), "virt_type",
            String.valueOf(taskId), "center", false);
        Assert.assertEquals(2, details.size());
        Assert.assertEquals("dragon", details.get(0).getDetailKey());
        Assert.assertEquals("kvm", details.get(1).getDetailKey());
    }

    @Test
    public void calNcInfos() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        long taskId = now;
        addDistribution(taskId, now);

        String md5 = String.valueOf(taskId);
        List<String> md5s = new ArrayList<>();
        md5s.add(md5);
        List<Map<String, Object>> rawNcInfos = distributionDao.calNcInfos(taskId, null, new Date(now),
            new Date(now + 86400L), md5s, true, false);
        List<Pair<String, Long>> ncInfos = parseNcInfos(rawNcInfos);
        Assert.assertNotNull(ncInfos);
        Assert.assertFalse(ncInfos.isEmpty());
        Assert.assertEquals(1, ncInfos.size());
        Assert.assertEquals("*******", ncInfos.get(0).getKey());
        Assert.assertEquals(Long.valueOf(100L), ncInfos.get(0).getValue());
    }

    @Test
    public void calNcInfosWithCondition() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        long taskId = now;
        addDistribution(taskId, now);
        addNcAssociation(now, "*******", "moc2.5");
        String md5 = String.valueOf(taskId);
        List<String> md5s = new ArrayList<>();
        md5s.add(md5);
        Map<String, List<String>> conditionMap = new HashMap<>();
        List<String> values = Arrays.asList("moc1.0", "moc2.0", "moc2.5");
        conditionMap.put("virt_type", values);
        List<String> ips = Arrays.asList("*******", "*******");

        List<Map<String, Object>> rawNcInfos = distributionDao.calNcInfosWithCondition(taskId, null, new Date(now),
            new Date(now + 86400L), new Date(now), md5s, conditionMap, ips, true, false);
        List<Pair<String, Long>> ncInfos = parseNcInfos(rawNcInfos);
        Assert.assertNotNull(ncInfos);
        Assert.assertFalse(ncInfos.isEmpty());
        Assert.assertEquals(1, ncInfos.size());
        Assert.assertEquals("*******", ncInfos.get(0).getKey());
        Assert.assertEquals(Long.valueOf(100L), ncInfos.get(0).getValue());
    }

    @Test
    public void getMd5AppearTime() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        long taskId = now;
        addDistribution(taskId, now);
        addDistribution(taskId, now + 864000L);
        Date minTime = distributionDao.getMd5MinAppearTime(taskId, new Date(now), new Date(now + 864000L), String.valueOf(taskId), "*******", null, false);
        Date maxTime = distributionDao.getMd5MaxAppearTime(taskId, new Date(now), new Date(now + 864000L), String.valueOf(taskId), "*******", null, false);
        Assert.assertNotNull(minTime);
        Assert.assertNotNull(maxTime);
        Assert.assertEquals(now, minTime.getTime());
        Assert.assertEquals(now + 864000L, maxTime.getTime());
    }

    @Test
    public void checkRecordExist() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        long taskId = now;
        addDistribution(taskId, now);
        addDistribution(taskId, now + 864000L);
        Assert.assertTrue(distributionDao.isRecordExist(taskId, new Date(now), new Date(now + 864000L), String.valueOf(taskId), "*******", null, false));
        Assert.assertFalse(distributionDao.isRecordExist(taskId, new Date(now - 864000L), new Date(now - 1L), String.valueOf(taskId), "*******", null, false));
    }

    @Test
    public void getNcSupportPoints() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        long taskId = now;
        addDistribution(taskId, now);
        addDistribution(taskId, now + 864000L);
        addDistribution(taskId, now + 864000L * 2L);
        addDistribution(taskId, now + 864000L * 5L);
        addDistribution(taskId, now + 864000L * 3L);
        List<Pair<Integer, Double>> points = distributionDao.getNcSupportPoints(taskId, new Date(now), new Date(now + 864000L * 10L), String.valueOf(taskId), "*******", null, false);
        Assert.assertNotNull(points);
        Assert.assertEquals(5, points.size());
    }

    @Test
    public void getValidNcCnt() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        long taskId = now;
        addDistribution(taskId, now);
        List<Pair<String, Long>> ncCnts = distributionDao.getValidNcCnt(taskId, new Date(now), new Date(now + 86400L), "center", null, String.valueOf(taskId), false);
        Assert.assertNotNull(ncCnts);
        Assert.assertEquals(1, ncCnts.size());
        Assert.assertEquals("*******", ncCnts.get(0).getLeft());
    }


    @Test
    public void queryNcByMd5() {
        long now = System.currentTimeMillis() % 10000000L * 1000L;
        long taskId = now;
        addDistribution(taskId, now);
        List<String> ips = distributionDao.queryNcByMd5(taskId, String.valueOf(taskId), new Date(now), false);
        Assert.assertEquals(1, ips.size());
    }

    private List<Pair<String, Long>> parseNcInfos(List<Map<String, Object>> records) {
        List<Pair<String, Long>> ncInfos = new ArrayList<>(records.size());
        records.forEach(r -> {
            String nc = "";
            long cnt = 0L;
            if (r.containsKey("nc")) {
                nc = r.get("nc").toString();
            }
            if (r.containsKey("cnt")) {
                cnt = Long.parseLong(r.get("cnt").toString());
            }
            ncInfos.add(Pair.of(nc, cnt));
        });
        return ncInfos;
    }

    private Map<DayTimeRange, List<String>> parseMd5sWithConditionByDay(Pair<DayTimeRange, List<String>> p) {
        Map<DayTimeRange, List<String>> md5Map = new TreeMap<>();
        md5Map.put(p.getKey(), p.getValue());
        return md5Map;
    }
}
