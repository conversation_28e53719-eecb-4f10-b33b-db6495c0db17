package com.aliyun.xdragon.biz.chat.handle;

import com.aliyun.xdragon.api.service.chat.model.AgentAnswerResponse;
import com.aliyun.xdragon.biz.chat.repository.ChatOssDao;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.ByteArrayInputStream;

import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class RuleHandleServiceImplTest {
    @InjectMocks
    private RuleHandleServiceImpl ruleHandleService;

    @Mock
    private ChatOssDao ossDao;

    private static final String QUERY = "~·？·~key~·？·~query";

    @Test
    public void testBasic() {
        Assert.assertNull(ruleHandleService.getUrl(null, null));
        Assert.assertNull(ruleHandleService.getLinks(null));
        Assert.assertNull(ruleHandleService.getAones(null));
        try {
            Assert.assertTrue(ruleHandleService.aiAgentResCheck(null));
        } catch (Exception e) {
            fail();
        }
    }

    @Test
    public void testGetContent() {
        AgentAnswerResponse response = new AgentAnswerResponse();
        response.setAsynToken(QUERY);
        when(ossDao.fileExist(anyString())).thenReturn(false);
        String resContent;
        try {
            resContent = ruleHandleService.getContent(response);
            Assert.assertEquals("未查询到相关库内容", resContent);
            resContent = ruleHandleService.getViewContent(response, 1);
            Assert.assertEquals("未查询到相关库内容", resContent);
        } catch (Exception e) {
            fail();
        }

        when(ossDao.fileExist(anyString())).thenReturn(true);
        when(ossDao.getFileETagUnsafe(anyString())).thenReturn(null);
        try {
            resContent = ruleHandleService.getContent(response);
            Assert.assertEquals("未查询到相关库内容", resContent);
            resContent = ruleHandleService.getViewContent(response, 1);
            Assert.assertEquals("未查询到相关库内容", resContent);
        } catch (Exception e) {
            fail();
        }

        when(ossDao.getFileETagUnsafe(anyString())).thenReturn("");
        try {
            resContent = ruleHandleService.getContent(response);
            Assert.assertEquals("未查询到相关库内容", resContent);
            resContent = ruleHandleService.getViewContent(response, 1);
            Assert.assertEquals("未查询到相关库内容", resContent);
        } catch (Exception e) {
            fail();
        }

        when(ossDao.getFileETagUnsafe(anyString())).thenReturn("tag");
        String json = "{\"key\":\"value\"}";
        when(ossDao.getFileInputStreamUnsafe(anyString())).thenReturn(new ByteArrayInputStream(json.getBytes()));
        try {
            resContent = ruleHandleService.getContent(response);
            Assert.assertEquals("value", resContent);
            resContent = ruleHandleService.getViewContent(response, 1);
            Assert.assertEquals("value", resContent);
        } catch (Exception e) {
            fail();
        }
    }
}