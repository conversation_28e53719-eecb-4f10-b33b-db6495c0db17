package com.aliyun.xdragon.biz.rule.job;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.odps.data.ArrayRecord;
import com.aliyun.odps.data.Record;
import com.aliyun.xdragon.biz.correlation.repository.CorrelationRuleMiningDao;
import com.aliyun.xdragon.biz.rule.repository.OpsRuleMiningDetailDao;
import com.aliyun.xdragon.common.generate.model.OpsRuleMiningDetail;
import com.aliyun.xdragon.service.common.agent.OdpsClient;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2025/02/05
 */
@RunWith(PowerMockRunner.class)
public class RuleOptimizationJobTest {

    @InjectMocks
    private RuleOptimizationJob ruleOptimizationJob;

    @Mock
    private OpsRuleMiningDetailDao opsRuleMiningDetailDao;

    @Mock
    private CorrelationRuleMiningDao correlationRuleMiningDao;

    @Mock
    private OdpsClient odpsClient;

    @Test
    public void testProcessNormalCase() throws Exception {
        // 准备数据
        OpsRuleMiningDetail detail1 = new OpsRuleMiningDetail();
        detail1.setRule("R1");
        detail1.setFeatures("F1,F3");
        OpsRuleMiningDetail detail2 = new OpsRuleMiningDetail();
        detail2.setRule("R1");
        detail2.setFeatures("F2,F3");
        List<OpsRuleMiningDetail> candidateList = Arrays.asList(detail1, detail2);
        when(opsRuleMiningDetailDao.queryCandidateFeatures()).thenReturn(candidateList);

        List<Record> featureRecords = mockRecordForFeature();
        List<Record> ruleRecords = mockRecordForRule();
        when(odpsClient.runSql(anyString())).thenReturn(featureRecords).thenReturn(ruleRecords);

        when(correlationRuleMiningDao.listOpsRule(anyList())).thenReturn(Collections.emptyList());

        when(opsRuleMiningDetailDao.batchUpdateStatus(anyList()))
                .thenAnswer(invocation -> {
                    List<?> list = invocation.getArgument(0); // 获取第一个参数
                    return list.size(); // 返回参数的长度
                });
        when(correlationRuleMiningDao.batchInsert(anyList())).thenReturn(true);
        // 执行
        ProcessResult result = ruleOptimizationJob.process(null, 1, 2);

        // 验证
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());
    }

    private List<Record> mockRecordForFeature() {
        Record record1 = mock(ArrayRecord.class);
        when(record1.getString("feature_name")).thenReturn("F1");
        Record record2 = mock(ArrayRecord.class);
        when(record2.getString("feature_name")).thenReturn("F2");
        Record record3 = mock(ArrayRecord.class);
        when(record3.getString("feature_name")).thenReturn("F3");
        Record record4 = mock(ArrayRecord.class);
        when(record4.getString("feature_name")).thenReturn("F4");
        return Arrays.asList(record1, record2, record3, record4);
    }

    private List<Record> mockRecordForRule() {
        Record mockRecord = mock(ArrayRecord.class);
        when(mockRecord.getString("reason")).thenReturn("R1");
        when(mockRecord.getString("actions")).thenReturn("A1");
        when(mockRecord.getString("exclusions")).thenReturn("E1");
        when(mockRecord.getString("extensions")).thenReturn("E1");
        when(mockRecord.getString("name")).thenReturn("R1");
        when(mockRecord.getString("id")).thenReturn("1");
        when(mockRecord.getString("conditions")).thenReturn("[\"{{tag.gc_level}} or false\",\"{{F2}}\"]");
        when(mockRecord.getString("ops_type")).thenReturn("OT1");
        when(mockRecord.getString("target_type")).thenReturn("TT1");
        when(mockRecord.getString("short_desc")).thenReturn("SD1");
        when(mockRecord.getString("level")).thenReturn("RL1");
        return Collections.singletonList(mockRecord);
    }

}