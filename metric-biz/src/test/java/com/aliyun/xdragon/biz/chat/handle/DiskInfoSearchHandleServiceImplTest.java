package com.aliyun.xdragon.biz.chat.handle;

import com.alibaba.ecs.devops.consistent.response.Disk;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.xdragon.api.service.chat.model.AgentAnswerResponse;
import com.aliyun.xdragon.biz.workitem.service.impl.DetailDiskConsistentImpl;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class DiskInfoSearchHandleServiceImplTest {
    @InjectMocks
    private DiskInfoSearchHandleServiceImpl diskInfoSearchHandleService;

    @Mock
    private DetailDiskConsistentImpl detailDiskConsistent;

    @Mock
    private Configuration logFreeMarkerConfig;

    @Test
    public void testBasic() {
        Assert.assertNull(diskInfoSearchHandleService.getUrl(null, null));
        Assert.assertNull(diskInfoSearchHandleService.getLinks(null));
        Assert.assertNull(diskInfoSearchHandleService.getAones(null));
    }

    @Test
    public void testAiAgentResCheck() throws Exception {
        AgentAnswerResponse response = new AgentAnswerResponse();
        Assert.assertFalse(diskInfoSearchHandleService.aiAgentResCheck(response));
        response.setFinish(true);
        Assert.assertFalse(diskInfoSearchHandleService.aiAgentResCheck(response));
        JSONObject params = new JSONObject();
        response.setParams(params);
        Assert.assertFalse(diskInfoSearchHandleService.aiAgentResCheck(response));
        List<String> ids = new ArrayList<>();
        ids.add("d-adf334kljkjfas345");
        params.put("disk_id", ids);
        Assert.assertTrue(diskInfoSearchHandleService.aiAgentResCheck(response));
    }

    @Test
    public void testGetContent() throws Exception {
        AgentAnswerResponse response = new AgentAnswerResponse();
        JSONObject params = new JSONObject();
        List<String> ids = new ArrayList<>();
        ids.add("d-adf334kljkjfas345");
        params.put("disk_id", ids);
        response.setParams(params);
        when(detailDiskConsistent.queryDiskByYaochiId(any(List.class))).thenReturn(Collections.EMPTY_LIST);
        String content = diskInfoSearchHandleService.getContent(response);
        Assert.assertNotNull(content);
        Assert.assertFalse(content.isEmpty());

        Disk disk = new Disk();
        disk.setInstanceId("i-12fas345");
        List<Disk> disks = new ArrayList<>(1);
        disks.add(disk);
        when(detailDiskConsistent.queryDiskByYaochiId(any(List.class))).thenReturn(disks);
        content = diskInfoSearchHandleService.getContent(response);
        Assert.assertNotNull(content);
        Assert.assertFalse(content.isEmpty());
    }

    @Test
    public void testGetViewContent() throws Exception {
        AgentAnswerResponse response = new AgentAnswerResponse();
        JSONObject params = new JSONObject();
        List<String> ids = new ArrayList<>();
        ids.add("d-adf334kljkjfas345");
        params.put("disk_id", ids);
        response.setParams(params);
        when(detailDiskConsistent.queryDiskByYaochiId(any(List.class))).thenReturn(Collections.EMPTY_LIST);
        String viewContent = diskInfoSearchHandleService.getViewContent(response, null);
        Assert.assertNotNull(viewContent);
        Assert.assertEquals("查询到的磁盘信息为空！", viewContent);

        Disk disk = new Disk();
        disk.setInstanceId("i-12fas345");
        List<Disk> disks = new ArrayList<>(1);
        disks.add(disk);
        when(detailDiskConsistent.queryDiskByYaochiId(any(List.class))).thenReturn(disks);
        Template template = PowerMockito.mock(Template.class);
        when(logFreeMarkerConfig.getTemplate(anyString())).thenReturn(template);
        viewContent = diskInfoSearchHandleService.getViewContent(response, null);
        Assert.assertTrue(StringUtils.isBlank(viewContent));
    }
}