package com.aliyun.xdragon.biz.keymetric.repository;

import com.aliyun.xdragon.biz.AbstractADBTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

import java.util.HashMap;
import java.util.Map;

@Import(OriginalKeyMetricDao.class)
public class OriginalKeyMetricDaoTest extends AbstractADBTest {

    @Autowired
    OriginalKeyMetricDao dao;

    @Test
    public void testGetTotalLifeTime() {
        long lifetime = dao.getTotalLifeTime("20250101");
        assert lifetime > 0;

        lifetime = dao.getTotalLifeTime("20250101", "20250102");
        assert lifetime > 0;

        lifetime = dao.getTotalLifeTime("20250101", "20250102", "region", "cn-shenzhen-st3-a01");
        assert lifetime > 0;
    }


    @Test
    public void testGetTotalLifeTimeByDs() {
        Map<String, Long> res = dao.getTotalLifeTimeByDs("20250101", "20250102");
        assert res != null;

        Map<String, String> options = new HashMap<>();
        options.put("region", "cn-shenzhen-st3-a01");
        Map<String, Long> res2 = dao.getTotalLifeTimeByDs("20250101", "20250102", options);
        assert res2 != null;
    }


    @Test
    public void testGetMaxDsByExample() {
        String maxDs = dao.getMaxDsByExample("20250101", "20250105");
        assert maxDs != null;
        assert Integer.parseInt(maxDs) > 20250101;
    }
}