package com.aliyun.xdragon.biz.allinonerisk.repository;

import java.util.Date;
import java.util.List;

import com.aliyun.xdragon.biz.AbstractDbTest;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRiskAction;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

@Import(AllinoneRiskActionDao.class)
public class AllinoneRiskActionDaoTest extends AbstractDbTest {

    @Autowired
    private AllinoneRiskActionDao allinoneRiskActionDao;

    @Test
    public void test() {
        AllinoneRiskAction allinoneRiskAction = new AllinoneRiskAction();
        allinoneRiskAction.setActionName("测试");
        allinoneRiskAction.setOwner("320310");
        allinoneRiskAction.setRiskId(1L);
        allinoneRiskAction.setIssueStatus("处理中");
        allinoneRiskAction.setGmtDeadline(new Date());
        allinoneRiskAction.setIssueId(12345678L);

        allinoneRiskAction = allinoneRiskActionDao.insertSelective(allinoneRiskAction);
        Assert.assertNotNull(allinoneRiskAction);
        allinoneRiskAction.setActionName("测试2");
        allinoneRiskAction = allinoneRiskActionDao.updateSelective(allinoneRiskAction);
        Assert.assertNotNull(allinoneRiskAction.getActionId());
        AllinoneRiskAction allinoneRiskAction1 = allinoneRiskActionDao.getActionById(allinoneRiskAction.getActionId());
        Assert.assertNotNull(allinoneRiskAction);
        Assert.assertEquals("测试2", allinoneRiskAction.getActionName());
        AllinoneRiskAction allinoneRiskAction2 = allinoneRiskActionDao.getActionByRiskIssue(allinoneRiskAction.getRiskId(), allinoneRiskAction.getIssueId());
        Assert.assertEquals("测试2", allinoneRiskAction1.getActionName());


        AllinoneRiskAction allinoneRiskAction3 = allinoneRiskActionDao.getActionByIssueId(12345678L);
        Assert.assertNotNull(allinoneRiskAction3);

        List<AllinoneRiskAction> list = allinoneRiskActionDao.listValidActionsByRiskId(1L);
        Assert.assertFalse(list.isEmpty());

        int res = allinoneRiskActionDao.deleteByRiskId(1L);
        Assert.assertTrue(res > 0);
    }
}
