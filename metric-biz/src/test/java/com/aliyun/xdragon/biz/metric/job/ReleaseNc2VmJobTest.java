package com.aliyun.xdragon.biz.metric.job;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.schedulerx.worker.domain.JobContext;

import com.aliyun.xdragon.biz.metric.repository.ReleasePlanDao;
import com.aliyun.xdragon.service.common.agent.EdmClient;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
public class ReleaseNc2VmJobTest {
    @InjectMocks
    private ReleaseNc2VmJob releaseNc2VmJob;

    @Mock
    private ReleasePlanDao releasePlanDao;

    @Mock
    private EdmClient edmClient;

    @Test
    public void testProcess() throws Exception {
        JobContext context = JobContext.newBuilder()
            .setDataTime(DateTime.now())
            .setScheduleTime(DateTime.now())
            .setJobParameters("interval=60")
            .build();

        Mockito.when(releasePlanDao.getLogs(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(getLogs());
        Mockito.when(edmClient.getVmsByNcIps(Mockito.anyList())).thenReturn(getVmsByNcIps());
        Mockito.when(releasePlanDao.putByVmLogs(Mockito.anyList())).thenReturn(true);

        releaseNc2VmJob.process(context);
    }

    private List<Map<String, String>> getLogs() {
        List<Map<String, String>> logs = new ArrayList<>();
        logs.add(new HashMap<String, String>(){{
            put("nc_ip", "nc_ip_1");
            put("target_id", "instance_id_1");
            put("deploy_type", "vm");
        }});
        logs.add(new HashMap<String, String>(){{
            put("nc_ip", "nc_ip_2");
            put("deploy_type", "nc");
        }});
        return logs;
    }

    private Map<String, Set<String>> getVmsByNcIps() {
        return new HashMap<String, Set<String>>(){{
            put("nc_ip_2", new HashSet<String>(){{
                add("instance_id_2");
                add("instance_id_3");
                add("instance_id_4");
            }});
        }};
    }
}
