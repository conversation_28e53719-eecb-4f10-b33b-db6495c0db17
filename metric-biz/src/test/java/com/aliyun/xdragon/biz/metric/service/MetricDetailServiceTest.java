package com.aliyun.xdragon.biz.metric.service;

import java.util.*;


import com.aliyun.xdragon.biz.log.repository.AnomalyStoreDao;
import com.aliyun.xdragon.biz.metric.repository.MetricDetailDao;
import com.aliyun.xdragon.biz.metric.repository.MetricSlsDao;
import com.aliyun.xdragon.biz.metric.repository.MetricSourceDao;
import com.aliyun.xdragon.biz.metric.service.impl.MetricDetailServiceImpl;
import com.aliyun.xdragon.common.enumeration.MetricTsdbLogStore;
import com.aliyun.xdragon.common.exception.XdragonMetricMetricException;
import com.aliyun.xdragon.common.exception.XdragonMetricViolationException;
import com.aliyun.xdragon.common.generate.model.MetricDetail;
import com.aliyun.xdragon.common.generate.model.MetricSource;
import com.aliyun.xdragon.common.model.MetricAnomalyData;
import com.aliyun.xdragon.common.model.PageableInfo;
import com.aliyun.xdragon.common.model.XdragonMetricRequest;
import com.aliyun.xdragon.common.model.XdragonMetricResponse;
import com.aliyun.xdragon.common.model.metric.MetricAnomalyFeature;
import com.aliyun.xdragon.common.model.metric.MetricAnomalyPoints;
import com.aliyun.xdragon.common.model.metric.MetricPoint;
import com.aliyun.xdragon.common.model.metric.MetricSeries;
import com.aliyun.xdragon.common.model.metric.request.ListMetricDetailRequest;
import com.aliyun.xdragon.common.model.metric.request.MetricOutlierClusterRequest;
import com.aliyun.xdragon.common.model.metric.request.QueryMetricAnomalyFeatureRequest;
import com.aliyun.xdragon.common.model.metric.request.QueryMetricAnomalyRequest;
import com.aliyun.xdragon.common.model.metric.request.QueryMetricSeriesRequest;
import com.aliyun.xdragon.common.model.metric.request.UpsertMetricDetailRequest;
import org.assertj.core.api.Assertions;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
public class MetricDetailServiceTest {
    @InjectMocks
    private MetricDetailServiceImpl metricDetailService;

    @Mock
    private MetricDetailDao metricDetailDao;

    @Mock
    private MetricSourceDao metricSourceDao;

    @Mock
    private MetricSlsDao metricSlsDao;

    @Mock
    private AnomalyStoreDao anomalyStoreDao;

    @Mock
    private MetricAggService metricAggService;

    @Mock
    private OtsService otsService;

    MetricDetail metricDetail = new MetricDetail();

    @Before
    public void setUp() {
        metricDetail.setSourceId(1L);
        metricDetail.setMetricName("metric1/exception_test");
        metricDetail.setPeriodLength(48);
        metricDetail.setMetricAnomalyId(1L);
        metricDetail.setAnomalyCheck(true);
        metricDetail.setExpertLostThreshold(86400L);
        metricDetail.setLostCheck(true);
        metricDetail.setMissingRate((byte)5);
        metricDetail.setPeriodScore((byte)10);

        Mockito.when(metricDetailDao.upsertMetricDetail(any(MetricDetail.class))).thenReturn(1);
        Mockito.when(metricDetailDao.listMetrics(anyLong(), anyInt(), anyInt(), anyString())).thenReturn(
            Collections.singletonList(metricDetail));
        Mockito.when(metricDetailDao.listMetrics(ArgumentMatchers.isNull(), anyInt(), anyInt(), anyString())).thenReturn(
            Collections.singletonList(metricDetail));
        Mockito.when(metricDetailDao.listMetrics(anyLong(), anyInt(), anyInt(), ArgumentMatchers.isNull())).thenReturn(
            Collections.singletonList(metricDetail));
        Mockito.when(metricDetailDao.getMetricDetailById(anyLong())).thenReturn(null);
        Mockito.when(metricDetailDao.getMetricDetailCount(anyLong(), ArgumentMatchers.isNull())).thenReturn(1L);
        Mockito.when(metricDetailDao.getMetricDetailCount(ArgumentMatchers.isNull(), anyString())).thenReturn(1L);
        Mockito.when(metricSlsDao.getMetricSeries(any(), anyList(), anyInt(), anyInt(), anyInt())).thenReturn(getSeries());
        Mockito.when(anomalyStoreDao.queryAnomalyByMetrics(anyList(), anyInt(), anyInt(), anyInt(), any())).thenReturn(getAnomaly());
        Mockito.when(metricSlsDao.getMetricAnomalyFeatures(anyString(), anyString(), anyInt(), anyInt(), anyInt())).thenReturn(getFeatures());
        Mockito.when(otsService.queryMetricSeries(any(), anyLong(), anyLong())).thenReturn(getSeries());
    }


    @Test
    public void testUpsert() {
        UpsertMetricDetailRequest request1 = new UpsertMetricDetailRequest();
        request1.setDetailId(1L);
        request1.setExpertLostThreshold(7200L);
        request1.setMetricAnomalyId(10L);
        request1.setExpertPeriodLength(24);
        request1.setFeedback("feedback");
        request1.setLostCheck(true);
        request1.setAnomalyCheck(true);

        XdragonMetricRequest<UpsertMetricDetailRequest> request = new XdragonMetricRequest<>();
        request.setParameters(request1);
        XdragonMetricResponse<MetricDetail> response = metricDetailService.upsertMetricDetail(request);
        Assert.assertEquals("指标不存在", response.getMessage());

        Mockito.when(metricDetailDao.getMetricDetailById(anyLong())).thenReturn(metricDetail);
        XdragonMetricResponse<MetricDetail> response2 = metricDetailService.upsertMetricDetail(request);
        Assertions.assertThat(response2.getData()).hasFieldOrPropertyWithValue("metricAnomalyId", 10L);
        Mockito.when(metricDetailDao.upsertMetricDetail(any(MetricDetail.class))).thenReturn(0);
        XdragonMetricResponse<MetricDetail> response3 = metricDetailService.upsertMetricDetail(request);
        Assert.assertEquals("更新指标失败", response3.getMessage());

        request1.setDetailId(null);
        request.setParameters(request1);
        XdragonMetricResponse<MetricDetail> response4 = metricDetailService.upsertMetricDetail(request);
        Assert.assertEquals("请选择正确的数据源", response4.getMessage());

        request1.setSourceId(5L);
        request.setParameters(request1);
        Mockito.when(metricSourceDao.getMetricSourceById(anyLong())).thenReturn(new MetricSource());
        XdragonMetricResponse<MetricDetail> response5 = metricDetailService.upsertMetricDetail(request);
        Assert.assertEquals("请填写指标名称", response5.getMessage());

        request1.setMetricName("test");
        request.setParameters(request1);
        Mockito.when(metricDetailDao.getMetricDetailByName(anyString(), anyLong())).thenReturn(new MetricDetail());
        Mockito.when(metricDetailDao.upsertMetricDetail(any(MetricDetail.class))).thenReturn(1);
        XdragonMetricResponse<MetricDetail> response6 = metricDetailService.upsertMetricDetail(request);
        Assert.assertEquals("test", response6.getData().getMetricName());
    }

    @Test
    public void listTest() {
        ListMetricDetailRequest request1 = new ListMetricDetailRequest();
        request1.setPageIndex(1);
        request1.setPageSize(10);
        request1.setSourceId(1L);
        XdragonMetricRequest<ListMetricDetailRequest> request = new XdragonMetricRequest<>();
        request.setParameters(request1);
        XdragonMetricResponse<PageableInfo<MetricDetail>> response = metricDetailService.listMetricDetail(request);
        Assertions.assertThat(response.getData().getData().get(0)).hasFieldOrPropertyWithValue("periodLength", 48);

        ListMetricDetailRequest request2 = new ListMetricDetailRequest();
        request2.setPageIndex(0);
        request2.setPageSize(10);
        request2.setSearchValue("metric");
        XdragonMetricRequest<ListMetricDetailRequest> request3 = new XdragonMetricRequest<>();
        request3.setParameters(request2);
        XdragonMetricResponse<PageableInfo<MetricDetail>> response1 = metricDetailService.listMetricDetail(request3);
        Assertions.assertThat(response1.getData().getData().get(0)).hasFieldOrPropertyWithValue("periodLength", 48);
    }

    @Test
    public void seriesTest() {
        QueryMetricSeriesRequest request1 = new QueryMetricSeriesRequest();
        request1.setSourceId(1L);
        request1.setStartTs(7201);
        request1.setEndTs(7200);
        List<String> metrics = new ArrayList<>();
        for (int i = 0; i < 32; i++) {
            metrics.add("test_" + i);
        }
        request1.setMetricNames(metrics);

        XdragonMetricRequest<QueryMetricSeriesRequest> request = new XdragonMetricRequest<>();
        request.setParameters(request1);
        XdragonMetricResponse<List<MetricSeries>> response = metricDetailService.queryMetricSeries(request);
        Assert.assertEquals("400", response.getCode());
        request1.setStartTs(3600);
        request1.setEndTs(7200);
        request.setParameters(request1);
        XdragonMetricResponse<List<MetricSeries>> response1 = metricDetailService.queryMetricSeries(request);
        Assert.assertEquals("数据源不存在", response1.getMessage());

        MetricSource metricSource = new MetricSource();
        metricSource.setSourceLogstore("xdragon-metric");
        Mockito.when(metricSourceDao.getMetricSourceById(anyLong())).thenReturn(metricSource);
        XdragonMetricResponse<List<MetricSeries>> response2 = metricDetailService.queryMetricSeries(request);
        Assertions.assertThat(response2.getData()).hasSize(1).element(0).hasFieldOrPropertyWithValue("metric", "metric/instanceId");

        request1.setTsdbLogStore(MetricTsdbLogStore.XdragonMetricOps);
        List<String> metrics1 = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            if (i % 2 == 0){
                metrics1.add("test_" + i);
            } else {
                metrics1.add("test" + i + "/" + i);
            }
        }
        request1.setMetricNames(metrics1);
        request.setParameters(request1);
        XdragonMetricResponse<List<MetricSeries>> response3 = metricDetailService.queryMetricSeries(request);
        Assertions.assertThat(response3.getData()).hasSize(1).element(0).hasFieldOrPropertyWithValue("metric", "metric/instanceId");
    }

    @Test
    public void anomalyTest() {
        QueryMetricAnomalyRequest request1 = new QueryMetricAnomalyRequest();
        request1.setSourceId(1L);
        request1.setStartTs(7201);
        request1.setEndTs(7200);
        List<String> metrics = new ArrayList<>();
        for (int i = 0; i < 32; i++) {
            metrics.add("test_" + i);
        }
        request1.setMetricNames(metrics);

        XdragonMetricRequest<QueryMetricAnomalyRequest> request = new XdragonMetricRequest<>();
        request.setParameters(request1);
        XdragonMetricResponse<List<MetricAnomalyPoints>> response = metricDetailService.queryMetricAnomaly(request);
        Assert.assertEquals("400", response.getCode());
        request1.setStartTs(3600);
        request1.setEndTs(7200);
        request.setParameters(request1);
        XdragonMetricResponse<List<MetricAnomalyPoints>> response1 = metricDetailService.queryMetricAnomaly(request);
        Assert.assertEquals("数据源不存在", response1.getMessage());

        MetricSource metricSource = new MetricSource();
        metricSource.setSourceLogstore("xdragon-metric1");
        Mockito.when(metricSourceDao.getMetricSourceById(anyLong())).thenReturn(metricSource);
        XdragonMetricResponse<List<MetricAnomalyPoints>> response2 = metricDetailService.queryMetricAnomaly(request);
        Assert.assertEquals("数据源所在TSDB不存在", response2.getMessage());

        metricSource.setSourceLogstore("xdragon-metric");
        Mockito.when(metricSourceDao.getMetricSourceById(anyLong())).thenReturn(metricSource);
        XdragonMetricResponse<List<MetricAnomalyPoints>> response3 = metricDetailService.queryMetricAnomaly(request);
        Assertions.assertThat(response3.getData()).hasSize(1).element(0).hasFieldOrPropertyWithValue("metric", "metric");
    }

    @Test
    public void anomalyFeatureTest() {
        QueryMetricAnomalyFeatureRequest request1 = new QueryMetricAnomalyFeatureRequest();
        request1.setStartTs(7201);
        request1.setEndTs(7200);
        request1.setTsdbLogStore(MetricTsdbLogStore.XdragonMetricOps);

        XdragonMetricRequest<QueryMetricAnomalyFeatureRequest> request = new XdragonMetricRequest<>();
        request.setParameters(request1);
        XdragonMetricResponse<List<MetricAnomalyFeature>> response = metricDetailService.queryMetricAnomalyFeature(request);
        Assert.assertEquals("400", response.getCode());

        request1.setStartTs(3600);
        request1.setEndTs(7200);
        request.setParameters(request1);
        XdragonMetricResponse<List<MetricAnomalyFeature>> response1 = metricDetailService.queryMetricAnomalyFeature(request);
        Assert.assertEquals("please choose target vm or nc", response1.getMessage());

        request1.setInstanceId("ncIp");
        XdragonMetricResponse<List<MetricAnomalyFeature>> response2 = metricDetailService.queryMetricAnomalyFeature(request);
        Assertions.assertThat(response2.getData()).hasSize(5);

        request1.setInstanceId("vmId");
        XdragonMetricResponse<List<MetricAnomalyFeature>> response3 = metricDetailService.queryMetricAnomalyFeature(request);
        Assertions.assertThat(response3.getData()).hasSize(5);
    }

    @Test
    public void metricClusterTest() {
        MetricOutlierClusterRequest request1 = new MetricOutlierClusterRequest();
        request1.setStartTs(7201);
        request1.setEndTs(7200);
        request1.setMetricName("metric");
        request1.setClusterThreshold(0.6);

        XdragonMetricRequest<MetricOutlierClusterRequest> request = new XdragonMetricRequest<>();
        request.setParameters(request1);
        try {
            XdragonMetricResponse<List<List<MetricSeries>>> response = metricDetailService.metricOutlierCluster(request);
            Assert.fail();
        } catch (XdragonMetricViolationException e) {
            Assert.assertEquals("PARAM_ERROR", e.getCode());
        }

        request1.setStartTs(0);
        request1.setEndTs(300);
        request1.setInstanceIds(Collections.emptyList());
        request.setParameters(request1);
        try {
            XdragonMetricResponse<List<List<MetricSeries>>> response1 = metricDetailService.metricOutlierCluster(request);
            Assert.fail();
        } catch (XdragonMetricViolationException e) {
            Assert.assertEquals("instance count must be greater than or equal to two", e.getMessage());
        }

        request1.setInstanceIds(Arrays.asList("instance1","instance2","instance3"));
        request.setParameters(request1);
        try {
            XdragonMetricResponse<List<List<MetricSeries>>> response2 = metricDetailService.metricOutlierCluster(request);
            Assert.fail();
        } catch (XdragonMetricViolationException e) {
            Assert.assertEquals("metric name format error, must be metricGroup/metric", e.getMessage());
        }


        Mockito.when(metricAggService.queryMetricSeries(any(), any(), anyList(), anyInt(), anyInt(), anyInt())).thenReturn(null);
        request1.setMetricName("metricGroup/metric");
        request.setParameters(request1);
        try {
            metricDetailService.metricOutlierCluster(request);
            Assert.fail();
        } catch (XdragonMetricMetricException e) {
            Assert.assertEquals("query metric got null or empty result", e.getMessage());
        }

        Mockito.when(metricAggService.queryMetricSeries(any(), any(), anyList(), anyInt(), anyInt(), anyInt())).thenReturn(Lists.newArrayList(getSeries()));
        request1.setMetricName("metricGroup/metric");
        request.setParameters(request1);
        try {
            XdragonMetricResponse<List<List<MetricSeries>>> response3 = metricDetailService.metricOutlierCluster(request);
            Assert.fail();
        } catch (XdragonMetricViolationException e) {
            Assert.assertEquals("metric series not complete, please check and retry", e.getMessage());
        }

        List<MetricSeries> metricSeries = new ArrayList<MetricSeries>(){{
                addAll(getSeries());
                addAll(getSeries());
                addAll(getSeries());
            }};
        Mockito.when(metricAggService.queryMetricSeries(any(), any(), anyList(), anyInt(), anyInt(), anyInt())).thenReturn(metricSeries);
        XdragonMetricResponse<List<List<MetricSeries>>> response4 = metricDetailService.metricOutlierCluster(request);
        Assertions.assertThat(response4.getData()).hasSize(1);
    }

    private List<MetricAnomalyFeature> getFeatures() {
        List<MetricAnomalyFeature> res = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            MetricAnomalyFeature feature = new MetricAnomalyFeature("vmId_" + i, "1.1.1." + i);
            feature.setScore(0.9);
            feature.addFeature(new HashMap<String, String>(){{
                put("metricName", "metricName");
                put("metricSet", "metricSet");
                put("anomaly", "spike");
                put("score", "0.89");
                put("anomalyTime", "2023-08-20 12:00:00");
                put("alarmTime", "2023-08-20 12:05:00");
                put("description", "测试");
            }});
            res.add(feature);
        }
        return res;
    }

    private List<MetricSeries> getSeries() {
        List<MetricSeries> res = new ArrayList<>();
        List<MetricPoint> points = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            points.add(new MetricPoint(i * 60 + 60, i * 1.0));
        }
        res.add(new MetricSeries("metric/instanceId", points));
        return res;
    }

    private List<MetricAnomalyPoints> getAnomaly() {
        List<MetricAnomalyPoints> res = new ArrayList<>();
        List<MetricAnomalyData> points = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            MetricAnomalyData anomalyData = new MetricAnomalyData();
            anomalyData.setAnomaly("spike");
            anomalyData.setMetric("metric");
            anomalyData.setTimestamp(7200);
            anomalyData.setStartTimestamp(3600);
            anomalyData.setValue(100.0);
            anomalyData.setAnomalyValue(98.0);
            anomalyData.setAdditional("{}");
            points.add(anomalyData);
        }
        res.add(new MetricAnomalyPoints("metric", points));
        return res;
    }
}
