package com.aliyun.xdragon.biz.log.job;

import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.xdragon.biz.metric.repository.MetricDetailDao;
import com.aliyun.xdragon.biz.metric.repository.MetricSourceDao;
import com.aliyun.xdragon.common.enumeration.MatchingRule;
import com.aliyun.xdragon.common.enumeration.algorithm.AnomalyType;
import com.aliyun.xdragon.common.generate.model.MetricAnomalyConfig;
import com.aliyun.xdragon.common.generate.model.MetricDetail;
import com.aliyun.xdragon.common.generate.model.MetricSource;
import com.aliyun.xdragon.service.common.cache.RedisUtil;
import com.aliyun.xdragon.biz.log.repository.AnomalyStoreDao;
import com.aliyun.xdragon.biz.metric.repository.MetricAnomalyConfigDao;
import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2022/09/15
 */
@RunWith(PowerMockRunner.class)
public class LostPointCheckJobTest {
    @InjectMocks
    private LostPointCheckJob lostPointCheckJob;

    @Mock
    private MetricAnomalyConfigDao metricAnomalyConfigDao;

    @Mock
    private MetricDetailDao metricDetailDao;

    @Mock
    private MetricSourceDao metricSourceDao;

    @Mock
    private AnomalyStoreDao anomalyStoreDao;

    @Mock
    private RedisUtil redisUtil;

    @Test
    public void genTask() {
        MetricSource source = new MetricSource();
        source.setSourceName("metric");
        source.setAnomalyCheck(true);
        source.setSourceId(1L);
        source.setMetricAnomalyId(1L);
        source.setTsInterval(300);
        MetricSource source2 = new MetricSource();
        source2.setSourceName("metric2");
        source2.setAnomalyCheck(true);
        source2.setSourceId(2L);
        source2.setMetricAnomalyId(2L);
        source2.setTsInterval(600);
        when(metricSourceDao.listAllMetricSource(any(), any(), any())).thenReturn(new LinkedList<>()).thenReturn(Lists.newArrayList(source, source2));

        List<Long> tasks = lostPointCheckJob.genTask(null, 2700);
        Assertions.assertThat(tasks).hasSize(0);
        tasks = lostPointCheckJob.genTask(null, 2700);
        Assertions.assertThat(tasks).hasSize(1);
    }

    @Test
    public void check() {
        MetricSource source = new MetricSource();
        source.setSourceName("metric");
        source.setMetricAnomalyId(1L);
        source.setSourceLogstore("logstore");
        source.setSourceId(1L);
        source.setTsInterval(300);

        int t = 1662613200;
        Map<String, Integer> lastTime = new HashMap<>();
        lastTime.put("metric", t - 26000);
        when(anomalyStoreDao.lastUploadTimes(anyString(), anyString(), anyInt(), anyInt())).thenReturn(lastTime);
        Map<String, Integer> baseLine = new HashMap<>();
        baseLine.put("metric", 5);
        when(anomalyStoreDao.metricPercentValues(anyString(), anyString(), anyInt(), anyInt())).thenReturn(baseLine);
        when(redisUtil.get(anyString())).thenReturn(null);
        MetricDetail md = new MetricDetail();
        md.setMetricName("metric");
        md.setLostThreshold(100L);
        when(metricDetailDao.getLostCheckMetricsBySourceId(anyLong())).thenReturn(Collections.emptyList()).thenReturn(Lists.newArrayList(md));
        int startTs = t + 300;
        lostPointCheckJob.check(source, startTs);
        verify(anomalyStoreDao, times(0)).addAnomalyData(anyString(), anyInt(), anyInt(),
                any(Map.class), anyInt(), any(AnomalyType.class), anyLong());

        ArgumentCaptor<Map<String, Object>> arg = ArgumentCaptor.forClass(Map.class);
        when(anomalyStoreDao.addAnomalyData(anyString(), anyInt(), anyInt(), arg.capture(), anyInt(), any(AnomalyType.class), anyLong())).thenReturn(true);
        lostPointCheckJob.check(source, startTs);
        Map<String, Object> additional = arg.getValue();
        Assert.assertEquals(100L, (long) additional.get("tolerateTime"));
    }

    @Test
    public void processSubTask() throws Exception {
        MetricSource source = new MetricSource();
        source.setSourceName("metric");
        source.setMetricAnomalyId(1L);
        source.setSourceLogstore("logstore");
        source.setTsInterval(300);

        when(metricSourceDao.getMetricSourceById(anyLong())).thenReturn(null).thenReturn(source);

        ProcessResult result = lostPointCheckJob.processSubTask(null, 1, 2, 1L);
        Assert.assertEquals(InstanceStatus.FAILED, result.getStatus());

        when(metricDetailDao.getLostCheckMetricsBySourceId(anyLong())).thenReturn(Collections.emptyList());
        result = lostPointCheckJob.processSubTask(null, 1, 2, 1L);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());
        verify(anomalyStoreDao, times(0)).lastUploadTimes(anyString(), anyString(), anyInt(), anyInt());
    }
}
