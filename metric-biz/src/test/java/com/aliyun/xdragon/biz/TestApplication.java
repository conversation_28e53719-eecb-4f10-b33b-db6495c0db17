package com.aliyun.xdragon.biz;

import com.alibaba.cloudops.api.goc.QuickResumeService;
import com.alibaba.cloudops.exception.OpenApiException;
import com.alibaba.cloudops.model.failure.FailureInfo;
import com.alibaba.cloudops.model.failure.NcFailureInfo;
import com.alibaba.cloudops.model.failure.VmFailureExtDO;
import com.alibaba.cloudops.model.failure.VmFailureInfo;
import com.alibaba.cloudops.model.ops.*;
import com.alibaba.cloudops.model.quickresume.FaultStatusInfo;
import com.alibaba.cloudops.result.CloudOpsResult;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.xdragonalertmanager.model.AgentCheckService;
import com.alibaba.xdragonalertmanager.model.AgentServiceDetail;
import com.alibaba.xdragonalertmanager.model.MachineIdTagInfo;
import com.alibaba.xdragonalertmanager.response.SendResponse;
import com.aliyun.ecs.devops.plan.model.breakCause.GetDeployRootCauseRecordResponse;
import com.aliyun.ecs.devops.plan.model.breakCause.GetDeployRootCauseRequest;
import com.aliyun.ecs.devops.plan.model.breakCause.GetDeployRootGraphResponse;
import com.aliyun.ecs.devops.plan.model.deployStats.*;
import com.aliyun.ecs.devops.plan.service.DeployStatService;
import com.aliyun.ecs.devopsapi.domain.BaseParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsAddAgentServiceParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsAddOwnerGroupParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsAddSourceLabelParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsChangeManagementParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsDeleteAgentServiceParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsDeleteRouterByRouterIdParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsDeleteRouterParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsDeleteSafeGuardRouterParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsEndTransactionalSendAlertParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsGrayCheckItemParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsGrayCheckItemReleaseParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsListExistedLabelsParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsMachineIdTagParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsModifyAgentServiceParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsOriginSendBackHistoryParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsQueryAgentServiceDetailParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsQueryAgentServiceParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsQueryAlertMapByKeywordParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsQueryAlertRouterParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsQueryAlertTempInfoParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsQueryMachineTagInfoListParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsQueryReceiverInfoByKeywordParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsQueryRouteListParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsQueryRouterAlertConfigInfoParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsQueryRouterDetailInfoByRouterIdsParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsQueryRouterParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsQuerySafeGuardRouterParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsRouterMonitorRelationParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsRouterParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsSendAlertParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsSendAlertTempInfoParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsStartTransactionalSendAlertParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsTagRouterRelationParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsTemplateParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsUpdateMachineIdTagParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsXamPreviewTemplateParam;
import com.aliyun.ecs.devopsapi.model.xam.DevOpsOriginSendItemDetailModel;
import com.aliyun.ecs.devopsapi.model.xam.DevOpsRouterModel;
import com.aliyun.ecs.devopsapi.model.xam.DevOpsSafeGuardRouterModel;
import com.aliyun.ecs.devopsapi.model.xam.DevOpsSendAlertModel;
import com.aliyun.ecs.devopsapi.model.xam.result.DevOpsXamResult;
import com.aliyun.ecs.devopsapi.service.XDragonAlertManagerService;
import com.aliyun.ecs.devopsapi.service.XDragonDiagnosisService;
import com.aliyun.phoenix.api.common.response.ListResult;
import com.aliyun.phoenix.api.common.response.MapResult;
import com.aliyun.phoenix.api.common.response.PaginatorResult;
import com.aliyun.phoenix.api.common.response.PlainResult;
import com.aliyun.xdragon.service.common.config.RedisCacheConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.lang.reflect.Proxy;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

@SpringBootApplication(scanBasePackages = {"com.aliyun.xdragon"})
@EnableTransactionManagement
@Import({RedisCacheConfig.class})
@EnableCaching
@EnableAsync
@EnableRetry
public class TestApplication {

    // 因为devopsapi UT环境不通？强制mock一个，没有地方依赖，这个不重要。
    @MockBean
    XDragonDiagnosisService xDragonDiagnosisService;

    @MockBean
    DeployStatService deployStatService;

    @MockBean
    QuickResumeService quickResumeService;

    @Bean
    public QuickResumeService quickResumeService() {
        return new QuickResumeService() {
            @Override
            public CloudOpsResult<PageableInfo<ExceptionOverviewInfo>> queryExceptionOverviewInfoV2(ExceptionRequest exceptionRequest) throws OpenApiException {
                return null;
            }

            @Override
            public CloudOpsResult<List<ExceptionOverviewInfoAsync>> queryExceptionOverviewInfoAsync(ExceptionRequest exceptionRequest) throws OpenApiException {
                return null;
            }

            @Override
            public List<ExceptionNcInfo> queryAllNcFailureList(ExceptionRequest exceptionRequest) throws OpenApiException {
                return Collections.emptyList();
            }

            @Override
            public PageableInfo<Map<String, Object>> getExceptionRecoveryNcInfoList(ExceptionRequest exceptionRequest, Boolean aBoolean) throws OpenApiException {
                return null;
            }

            @Override
            public List<Map<String, Object>> queryExceptionNcInfoWithMaintenances(List<ExceptionNcInfo> list, Date date, Date date1) {
                return Collections.emptyList();
            }

            @Override
            public List<Map<String, Object>> queryControlNcInfoWithHealthInfo(List<ExceptionNcInfo> list, Date date, Date date1) {
                return Collections.emptyList();
            }

            @Override
            public PageableInfo<NcFailureInfo> queryNcFailureInfoList(ExceptionRequest exceptionRequest) throws OpenApiException {
                return null;
            }

            @Override
            public PageableInfo<NcFailureInfo> queryNcFailureInfoListForTask(ExceptionRequest exceptionRequest) throws OpenApiException {
                return null;
            }

            @Override
            public CloudOpsResult<Integer> deleteNcOrVmFailureInfo(ExceptionRequest exceptionRequest) {
                return null;
            }

            @Override
            public Integer getQuickResumeNcInfluenceVm(ExceptionRequest exceptionRequest) throws OpenApiException {
                return 0;
            }

            @Override
            public Integer getQuickResumeVmInfluenceNc(ExceptionRequest exceptionRequest) throws OpenApiException {
                return 0;
            }

            @Override
            public CloudOpsResult<FaultStatusInfo> queryVmFailureStatusInfo(ExceptionRequest exceptionRequest) throws OpenApiException {
                return null;
            }

            @Override
            public PageableInfo<VmFailureInfo> queryVmFailureInfoList(ExceptionRequest exceptionRequest) throws OpenApiException {
                return null;
            }

            @Override
            public PageableInfo<VmFailureInfo> queryVmBasicInfoList(ExceptionRequest exceptionRequest) throws OpenApiException {
                return null;
            }

            @Override
            public List<VmFailureExtDO> queryVmExtStatusByInstanceIdList(List<String> list) {
                return Collections.emptyList();
            }

            @Override
            public void checkHealthUpdateVmHouyiState(List<QuickResumeCheckHealthStartRequest> list) {

            }

            @Override
            public Integer queryVmTotalCount(ExceptionRequest exceptionRequest) throws OpenApiException {
                return 0;
            }

            @Override
            public Integer queryVmTotalCountForTask(ExceptionRequest exceptionRequest) throws OpenApiException {
                return 0;
            }

            @Override
            public Integer queryNcTotalCount(ExceptionRequest exceptionRequest) throws OpenApiException {
                return 0;
            }

            @Override
            public Integer queryTotalNcFailureForTask(ExceptionRequest exceptionRequest) throws OpenApiException {
                return 0;
            }

            @Override
            public Integer quickSubmitOps(ExceptionRequest exceptionRequest, String s, String s1, String s2) throws OpenApiException {
                return 0;
            }

            @Override
            public List<String> syncNetwork(String s, List<String> list, String s1) throws OpenApiException {
                return Collections.emptyList();
            }

            @Override
            public JSONArray getQuickResumeOpsTypeInfo() {
                return null;
            }

            @Override
            public List<String> queryFeedbackUserList(String s, List<String> list) {
                return Collections.emptyList();
            }

            @Override
            public JSONObject inventoryEvaluation(String s, String s1, String s2, String s3, boolean b, boolean b1, String s4, String s5) throws OpenApiException {
                return null;
            }

            @Override
            public List<FailureInfo> queryMachineRelateFault(List<String> list, Date date, Date date1) throws OpenApiException {
                return Collections.emptyList();
            }
        };
    }

    @Bean
    public DeployStatService deployStatService() {
        return new DeployStatService() {
            @Override
            public PlainResult<GetSlsFilterAndGroupByDataResponse<DeployRecord>> getDeployStatSummary(GetDeployStatSummaryRequest getDeployStatSummaryRequest) {
                return null;
            }

            @Override
            public PlainResult<GetSlsFilterAndGroupByDataResponse<DeployExceptionRecord>> getDeployExceptionSummary(GetDeployExceptionRequest getDeployExceptionRequest) {
                return null;
            }

            @Override
            public PlainResult<GetSlsFilterAndGroupByDataResponse<DeployRecord>> getDeployScoreSummary(GetDeployScoreRequest getDeployScoreRequest) {
                return null;
            }

            @Override
            public void logDeployScore(GetSlsFilterAndGroupByDataResponse<DeployRecord> getSlsFilterAndGroupByDataResponse) {

            }

            @Override
            public PlainResult<GetDeployRootCauseRecordResponse> getDeployRootCauseRecord(GetDeployRootCauseRequest getDeployRootCauseRequest) {
                return null;
            }

            @Override
            public PlainResult<GetDeployRootGraphResponse> getDeployRootGraph(GetDeployRootCauseRequest getDeployRootCauseRequest) {
                return null;
            }
        };
    }

    @Bean
    public XDragonAlertManagerService xDragonAlertManagerService() {
        // mock a alertManagerService
        return new XDragonAlertManagerService() {
            @Override
            public DevOpsXamResult<DevOpsChangeManagementParam> submitRouterChange(DevOpsChangeManagementParam devOpsChangeManagementParam) {
                return null;
            }

            @Override
            public DevOpsXamResult<DevOpsChangeManagementParam> submitTemplateChange(DevOpsChangeManagementParam devOpsChangeManagementParam) {
                return null;
            }

            @Override
            public DevOpsXamResult<String> getTemplateByName(DevOpsTemplateParam devOpsTemplateParam) {
                return null;
            }

            @Override
            public PaginatorResult<DevOpsRouterModel> getRoutersByTemplateName(DevOpsTemplateParam devOpsTemplateParam) {
                return null;
            }

            @Override
            public DevOpsXamResult<Integer> addNewRouter(DevOpsRouterParam devOpsRouterParam) {
                return null;
            }

            @Override
            public DevOpsXamResult<DevOpsRouterModel> getRouterByRouterName(DevOpsQueryRouterParam devOpsQueryRouterParam) {
                return null;
            }

            @Override
            public DevOpsXamResult<Boolean> setRouterFvtEnv(String s, int i) {
                return null;
            }

            @Override
            public PaginatorResult<DevOpsRouterModel> listRoutersBySourceLabelAndOwnerGroup(DevOpsQueryRouteListParam devOpsQueryRouteListParam) {
                return null;
            }

            @Override
            public DevOpsXamResult<Boolean> deleteRouterByRouterName(DevOpsDeleteRouterParam devOpsDeleteRouterParam) {
                return null;
            }

            @Override
            public PlainResult<Boolean> deleteSafeGuardRouterByAliUid(DevOpsDeleteSafeGuardRouterParam devOpsDeleteSafeGuardRouterParam) {
                return null;
            }

            @Override
            public DevOpsXamResult<Boolean> updateRouterByRouterName(DevOpsRouterParam devOpsRouterParam) {
                return null;
            }

            @Override
            public DevOpsXamResult<Map<String, SendResponse>> sendAlert(DevOpsSendAlertParam devOpsSendAlertParam) {
                return null;
            }

            @Override
            public DevOpsXamResult<List<DevOpsSendAlertModel>> sendAlert2(DevOpsSendAlertParam devOpsSendAlertParam) {
                return null;
            }

            @Override
            public DevOpsXamResult<String> startTransactionalSendAlert(DevOpsStartTransactionalSendAlertParam devOpsStartTransactionalSendAlertParam) {
                return null;
            }

            @Override
            public DevOpsXamResult<Boolean> endTransactionalSendAlert(DevOpsEndTransactionalSendAlertParam devOpsEndTransactionalSendAlertParam) {
                return null;
            }

            @Override
            public PaginatorResult<DevOpsSafeGuardRouterModel> listAllSafeGuardRouter(DevOpsQuerySafeGuardRouterParam devOpsQuerySafeGuardRouterParam) {
                return null;
            }

            @Override
            public PaginatorResult<DevOpsRouterModel> queryRouterInfoListByParams(DevOpsQueryRouteListParam devOpsQueryRouteListParam) {
                return null;
            }

            @Override
            public PlainResult<Boolean> deleteRouterByRouterId(DevOpsDeleteRouterByRouterIdParam devOpsDeleteRouterByRouterIdParam) {
                return null;
            }

            @Override
            public ListResult<String> queryAlertRouterDataByKeyword(DevOpsQueryAlertRouterParam devOpsQueryAlertRouterParam) {
                return null;
            }

            @Override
            public MapResult<String, String> queryAlertMapByKeyword(DevOpsQueryAlertMapByKeywordParam devOpsQueryAlertMapByKeywordParam) {
                return null;
            }

            @Override
            public ListResult queryRouterAlertConfigInfo(DevOpsQueryRouterAlertConfigInfoParam devOpsQueryRouterAlertConfigInfoParam) {
                return null;
            }

            @Override
            public ListResult<DevOpsRouterModel> queryRouterDetailInfoByRouterIds(DevOpsQueryRouterDetailInfoByRouterIdsParam devOpsQueryRouterDetailInfoByRouterIdsParam) {
                return null;
            }

            @Override
            public ListResult queryTagRouterRelationByMonitorLabel(DevOpsTagRouterRelationParam devOpsTagRouterRelationParam) {
                return null;
            }

            @Override
            public ListResult queryRouterMonitorRelationByMonitorLabel(DevOpsRouterMonitorRelationParam devOpsRouterMonitorRelationParam) {
                return null;
            }

            @Override
            public MapResult<String, SendResponse> grayRequestAddCheckItem(DevOpsGrayCheckItemParam devOpsGrayCheckItemParam) {
                return null;
            }

            @Override
            public MapResult<String, SendResponse> grayRequestReleaseCheckItem(DevOpsGrayCheckItemReleaseParam devOpsGrayCheckItemReleaseParam) {
                return null;
            }

            @Override
            public PlainResult<Boolean> addSourceLabel(DevOpsAddSourceLabelParam devOpsAddSourceLabelParam) {
                return null;
            }

            @Override
            public PlainResult<Boolean> addOwnerGroup(DevOpsAddOwnerGroupParam devOpsAddOwnerGroupParam) {
                return null;
            }

            @Override
            public ListResult<String> querySourceLabelList(BaseParam baseParam) {
                return null;
            }

            @Override
            public ListResult<String> queryOwnerGroupList(BaseParam baseParam) {
                return null;
            }

            @Override
            public ListResult queryMonitorLabelTree(BaseParam baseParam) {
                return null;
            }

            @Override
            public ListResult<DevOpsOriginSendItemDetailModel> queryOriginSendItemDetail(DevOpsOriginSendBackHistoryParam devOpsOriginSendBackHistoryParam) {
                return null;
            }

            @Override
            public PlainResult<String> queryAlertTempInfo(DevOpsQueryAlertTempInfoParam devOpsQueryAlertTempInfoParam) {
                return null;
            }

            @Override
            public PlainResult<String> previewTemplateByItemDetail(DevOpsXamPreviewTemplateParam devOpsXamPreviewTemplateParam) {
                return null;
            }

            @Override
            public ListResult<String> listExistedLabels(DevOpsListExistedLabelsParam devOpsListExistedLabelsParam) {
                return null;
            }

            @Override
            public MapResult<String, Map<String, String>> queryReceiverInfoByKeyword(DevOpsQueryReceiverInfoByKeywordParam devOpsQueryReceiverInfoByKeywordParam) {
                return null;
            }

            @Override
            public ListResult<AgentCheckService> listAgentAllCheckService(DevOpsQueryAgentServiceParam devOpsQueryAgentServiceParam) {
                return null;
            }

            @Override
            public PlainResult<Boolean> addAgentCheckService(DevOpsAddAgentServiceParam devOpsAddAgentServiceParam) {
                return null;
            }

            @Override
            public PlainResult<Boolean> deleteAgentCheckService(DevOpsDeleteAgentServiceParam devOpsDeleteAgentServiceParam) {
                return null;
            }

            @Override
            public PlainResult<AgentServiceDetail> getAgentCheckServiceDetail(DevOpsQueryAgentServiceDetailParam devOpsQueryAgentServiceDetailParam) {
                return null;
            }

            @Override
            public PlainResult<Boolean> modifyAgentCheckServiceDetail(DevOpsModifyAgentServiceParam devOpsModifyAgentServiceParam) {
                return null;
            }

            @Override
            public ListResult<MachineIdTagInfo> queryMachineTagInfoList(DevOpsQueryMachineTagInfoListParam devOpsQueryMachineTagInfoListParam) {
                return null;
            }

            @Override
            public PlainResult<Integer> batchAddMachineIdTag(DevOpsMachineIdTagParam devOpsMachineIdTagParam) {
                return null;
            }

            @Override
            public PlainResult<Integer> batchUpdateMachineIdTag(DevOpsMachineIdTagParam devOpsMachineIdTagParam) {
                return null;
            }

            @Override
            public PlainResult<Boolean> updateMachineIdTag(DevOpsUpdateMachineIdTagParam devOpsUpdateMachineIdTagParam) {
                return null;
            }

            @Override
            public PlainResult<Boolean> sendAlertTempInfo(DevOpsSendAlertTempInfoParam devOpsSendAlertTempInfoParam) {
                return null;
            }
        };
    }

}


