package com.aliyun.xdragon.biz.rule.util;

import java.util.Arrays;
import java.util.List;

import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2025/01/09
 */
public class RuleOrganizerTest {

    @Test
    public void test1() {
        RuleOrganizer organizer = new RuleOrganizer();
        String[] rawRules = {
                "cable_error, nc_down_uniform_check_error_fastpath",
                "cable_error, down_start_trigger_fastpath",
                "nc_fake_down, nic_port_out_error_high",
                "down_start_trigger_fastpath, monitor_data_loss_too_long, nc_down_uniform_check_error_fastpath",
                "down_start_trigger_fastpath, monitor_data_loss_too_long, pingmesh_latency_exception",
                "down_start_trigger_fastpath, monitor_data_loss_too_long, nc_down_uniform_check_error",
                "down_start_trigger_fastpath, pingmesh_latency_exception, unfinished_repaired_order_exclude_disk",
                "cable_error, nc_fake_down, nic_online_repairing",
                "cable_error, nc_short_ping_loss_by_ag, nc_ssh_failed_by_sa",
                "nc_hang, pingmesh_latency_exception, unfinished_repaired_order_exclude_disk",
                "down_start_trigger_fastpath, pingmesh_latency_public_increase_high, "
                        + "unfinished_repaired_order_exclude_disk",
                "down_start_trigger_fastpath, nic_online_repairing, unfinished_repaired_order_exclude_disk",
                "cable_error_not_repair_order, down_start_trigger_fastpath, unfinished_repaired_order_exclude_disk",
                "down_start_trigger_fastpath, monitor_data_loss_5min, nc_down_uniform_check_error_fastpath",
                "down_start_trigger_fastpath, monitor_data_loss_5min, pingmesh_latency_exception",
                "down_start_trigger_fastpath, monitor_data_loss_5min, nc_down_uniform_check_error",
                "inspect_nc_down_affect_vm_fastpath, nic_online_repairing, unfinished_repaired_order_exclude_disk",
                "inspect_nc_down_affect_vm, nic_online_repairing, unfinished_repaired_order_exclude_disk",
                "down_start_trigger, pingmesh_latency_exception, unfinished_repaired_order_exclude_disk",
                "down_start_trigger_fastpath, nic_port_out_error_high, pingmesh_latency_exception",
                "down_start_trigger, nic_port_out_error_high, pingmesh_latency_exception",
                "nc_down_uniform_check_error_fastpath, nic_port_out_error_high, pingmesh_latency_exception",
                "nc_down_uniform_check_error, nic_port_out_error_high, pingmesh_latency_exception"
        };

        List<List<String>> result = organizer.organize(Arrays.asList(rawRules));
        Assert.assertEquals(9, result.size());
    }

    @Test
    public void test2() {
        RuleOrganizer organizer = new RuleOrganizer();
        String[] rawRules = {
                "cable_error,nc_nic_mtu_error,offline_process_too_long",
                "nc_nic_mtu_error,nic_online_repairing,offline_process_too_long",
                "nc_nic_mtu_error,nic_repair_break_sla,offline_process_too_long",
                "nc_nic_mtu_error,offline_process_too_long",
                "nc_nic_mtu_error,offline_process_too_long,unfinished_repaired_order_exclude_disk",
                "nc_hardware_warning,nc_nic_mtu_error,offline_process_too_long"
        };

        List<List<String>> result = organizer.organize(Arrays.asList(rawRules));
        Assert.assertEquals(1, result.size());
        Assert.assertEquals(2, result.get(0).size());
        Assert.assertEquals("{{nc_nic_mtu_error}}", result.get(0).get(0));
        Assert.assertEquals("{{offline_process_too_long}}", result.get(0).get(1));
    }

    @Test
    public void test3() {
        RuleOrganizer organizer = new RuleOrganizer();
        String[] rawRules = {
                "bond_single_nic_down_too_long,cable_error,nic_repair_break_sla",
                "bond_single_nic_down_too_long,nc_hardware_warning,nic_repair_break_sla",
                "bond_single_nic_down_too_long,nic_repair_break_sla",
                "cable_error,cable_error_not_repair_order,nic_repair_break_sla",
                "cable_error,network_card_jitter,nic_repair_break_sla",
                "nc_hardware_warning,network_card_flapping,nic_repair_break_sla",
                "network_card_flapping,nic_repair_break_sla,unfinished_repaired_order_exclude_disk",
                "nic_online_repairing,nic_repair_break_sla,single_nic_down",
                "nic_online_repairing,offline_process_too_long,single_nic_down",
                "nic_repair_break_sla,single_nic_down",
                "nic_repair_break_sla,single_nic_down,unfinished_repaired_order_exclude_disk",
                "nic_repair_break_sla,unfinished_repaired_order_exclude_disk",
                "bond_single_nic_down_too_long,nic_online_repairing,nic_repair_break_sla",
                "bond_single_nic_down_too_long,nic_repair_break_sla,single_nic_down",
                "bond_single_nic_down_too_long,nic_repair_break_sla,unfinished_repaired_order_exclude_disk",
                "cable_error,nc_hardware_warning,nic_repair_break_sla",
                "cable_error,network_card_flapping,nic_repair_break_sla",
                "cable_error,nic_online_repairing,nic_repair_break_sla",
                "cable_error,nic_repair_break_sla",
                "cable_error,nic_repair_break_sla,single_nic_down",
                "cable_error,nic_repair_break_sla,unfinished_repaired_order_exclude_disk",
                "cable_error_not_repair_order,nc_hardware_warning,nic_repair_break_sla",
                "cable_error_not_repair_order,nic_repair_break_sla,unfinished_repaired_order_exclude_disk",
                "nc_hardware_warning,network_card_jitter,nic_repair_break_sla",
                "nc_hardware_warning,nic_repair_break_sla,single_nic_down",
                "nc_hardware_warning,nic_repair_break_sla,unfinished_repaired_order_exclude_disk",
                "network_card_jitter,nic_repair_break_sla,unfinished_repaired_order_exclude_disk",
                "nic_online_repairing,nic_repair_break_sla,unfinished_repaired_order_exclude_disk",
                "nic_online_repairing,offline_process_too_long",
                "offline_process_too_long,unfinished_repaired_order_exclude_disk"
        };
        List<List<String>> result = organizer.organize(Arrays.asList(rawRules));
        System.out.println(result);
    }

    @Test
    @SneakyThrows
    public void test4() {
        String[] rawRules = IOUtils.toString(getClass().getResourceAsStream("/rule/test_data.txt")).split("\n");
        RuleOrganizer organizer = new RuleOrganizer();
        List<List<String>> result = organizer.organize(Arrays.asList(rawRules));
        for (List<String> expressions : result) {
            for (String expression : expressions) {
                System.out.println(expression);
            }
            System.out.println();
        }
    }

    @Test
    public void getCoveredRules() {
        String[] rawRules = {
                "cable_error,nc_nic_mtu_error,offline_process_too_long",
                "nc_nic_mtu_error,nic_online_repairing,offline_process_too_long",
                "nc_nic_mtu_error,nic_repair_break_sla,offline_process_too_long",
                "nc_nic_mtu_error,offline_process_too_long",
                "nc_nic_mtu_error,offline_process_too_long,unfinished_repaired_order_exclude_disk",
                "nc_hardware_warning,nc_nic_mtu_error,offline_process_too_long",
                "cable_error,offline_process_too_long"
        };
        String[] rule = {"{{nc_nic_mtu_error}}", "{{offline_process_too_long}}"};

        RuleOrganizer organizer = new RuleOrganizer();
        List<String> covered = organizer.getCoveredRules(Arrays.asList(rule), Arrays.asList(rawRules));
        Assert.assertEquals(6, covered.size());
    }

}