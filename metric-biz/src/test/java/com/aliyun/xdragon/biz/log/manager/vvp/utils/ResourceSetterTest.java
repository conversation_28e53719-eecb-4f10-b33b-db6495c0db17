package com.aliyun.xdragon.biz.log.manager.vvp.utils;

import com.aliyun.ververica_inner20220718.models.BriefResourceSetting;
import com.aliyun.xdragon.biz.log.manager.config.ResourceConfig;
import org.junit.Assert;
import org.junit.Test;

import static com.aliyun.xdragon.biz.log.manager.vvp.utils.ResourceSetter.fullySpecifiedBriefResourceSetting;

public class ResourceSetterTest {
    @Test
    public void testResourceSetter() {
        ResourceConfig config = new ResourceConfig();
        Assert.assertNotNull(config);
        BriefResourceSetting setting = fullySpecifiedBriefResourceSetting(config, null);
        Assert.assertNotNull(setting);
    }
}
