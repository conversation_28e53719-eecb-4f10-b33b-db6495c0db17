package com.aliyun.xdragon.biz.workitem.repository;

import com.aliyun.xdragon.biz.AbstractDbTest;
import com.aliyun.xdragon.common.generate.model.WorkItemStatistics;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Import(WorkItemStatisticsDao.class)
public class WorkItemStatisticsDaoTest extends AbstractDbTest {

    @Resource
    private WorkItemStatisticsDao dao;

    @Test
    public void batchInsertTest() {
        List<WorkItemStatistics> data = new ArrayList<>();
        WorkItemStatistics statistics = new WorkItemStatistics();
        statistics.setType(2);
        statistics.setDescription("test");
        statistics.setObjectName("1");
        statistics.setObjectValue(1.0);
        data.add(statistics);
        boolean result = dao.batchInsert(data);
        Assert.assertTrue(result);
    }

    @Test
    public void listByTypeTest() {
        Integer type = 3;
        String startDs = "20230903";
        String endDs = "20230908";
        List<String> objectNames = new ArrayList<>();
        List<WorkItemStatistics> result = dao.listByType(type, startDs, endDs, objectNames);
        Assert.assertTrue(result.size() > 0);
    }

    @Test
    public void listByKeyTest() {
        List<String> keys = Arrays.asList("1", "2", "3");
        String startDs = "20230103";
        String endDs = "20230107";
        List<WorkItemStatistics> result = dao.listByKey(keys, startDs, endDs);
        Assert.assertTrue(result.size() > 0);
    }

    @Test
    public void getListByTypeForWeek() {
        String startDs = "20230529";
        String endDs = "20230604";
        String description = "各数据来源每周工单量统计";
        List<String> objectNames = new ArrayList<>();
        List<WorkItemStatistics> result = dao.getListByTypeForWeek(0, startDs, endDs, objectNames, description);
        Assert.assertTrue(result.size() > 0);
    }

    @Test
    public void getListByTypeForMonth() {
        String startDs = "20230600";
        String endDs = "20230700";
        String description = "一级标签每周工单量统计";
        List<String> objectNames = new ArrayList<>();
        List<WorkItemStatistics> result = dao.getListByTypeForMonth(0, startDs, endDs, objectNames, description);
        Assert.assertTrue(result.size() > 0);
    }

    @Test
    public void batchDeleteTest() {
        WorkItemStatistics workItemStatistics = new WorkItemStatistics();
        workItemStatistics.setType(3);
        workItemStatistics.setDt("20230906");
        workItemStatistics.setObjectName("98");
        workItemStatistics.setId(187647L);
        int num = dao.batchDelete(workItemStatistics);
        Assert.assertTrue(num > 0);
    }

}
