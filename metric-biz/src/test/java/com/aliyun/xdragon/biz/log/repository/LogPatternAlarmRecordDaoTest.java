package com.aliyun.xdragon.biz.log.repository;

import com.aliyun.xdragon.biz.AbstractDbTest;
import com.aliyun.xdragon.common.generate.model.LogPatternAlarmRecord;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/04/16
 */
@Import(LogPatternAlarmRecordDao.class)
public class LogPatternAlarmRecordDaoTest extends AbstractDbTest {
    @Autowired
    private LogPatternAlarmRecordDao logPatternAlarmRecordDao;

    private LogPatternAlarmRecord genRecord(Long taskId, String md5, String region) {
        LogPatternAlarmRecord record = new LogPatternAlarmRecord();
        record.setTaskId(taskId);
        record.setMd5(md5);
        record.setHashcode(0);
        record.setRegion(region);
        record.setLogstore("logstore");
        record.setSupport(1234L);
        record.setAnomalyTime(new Date(taskId));
        record.setStatus(0);
        record.setHitRelease(taskId % 2 == 0);
        record.setGenAone(taskId % 3 + 1);
        record.setOwner("owner");
        record.setAnomalyType("type");
        return record;
    }

    @Test
    public void test() {
        long taskId1 = System.currentTimeMillis() % 1712153L * 1000L;
        long taskId2 = System.currentTimeMillis() % 1231231L * 1000L;
        long taskId3 = System.currentTimeMillis() % 543317 * 1000L;
        LogPatternAlarmRecord record1 = genRecord(taskId1, "md5a", "beijing");
        LogPatternAlarmRecord record2 = genRecord(taskId2, "md5b", "hangzhou");
        LogPatternAlarmRecord record3 = genRecord(taskId3, "md5c", "shanghai");
        logPatternAlarmRecordDao.insert(record1);
        logPatternAlarmRecordDao.batchInsert(Arrays.asList(record2, record3));

        List<LogPatternAlarmRecord> getRecord1 = logPatternAlarmRecordDao.getPatternAlarmRecords(taskId1, "md5a",
            "type", new Date(taskId1), 0, "beijing", "logstore", taskId1 % 2 == 0, taskId1 % 3 + 1);
        Assert.assertNotNull(getRecord1);
        Assert.assertEquals(1, getRecord1.size());

        List<LogPatternAlarmRecord> getRecord2 = logPatternAlarmRecordDao.getPatternAlarmRecords(null, null, "type",
            null, null, null, null, null, null);
        Assert.assertNotNull(getRecord2);
        Assert.assertEquals(3, getRecord2.size());

        List<LogPatternAlarmRecord> getRecord3 = logPatternAlarmRecordDao.getPatternAlarmRecordsWithTimeRange(taskId3,
            "md5c", "type", new Date(taskId3), new Date(taskId3 + 1000L));
        Assert.assertNotNull(getRecord3);
        Assert.assertEquals(1, getRecord3.size());

        long maxT = Math.max(Math.max(taskId1, taskId2), taskId3);
        long minT = Math.min(Math.min(taskId1, taskId2), taskId3);
        List<LogPatternAlarmRecord> getAones = logPatternAlarmRecordDao.getTaskPatternAones(
            Lists.newArrayList(taskId1, taskId2, taskId3), new Date(minT - 1), new Date(maxT + 1),
            Lists.newArrayList("type"), null, true, taskId1 %3+1);
        Assert.assertFalse(getAones.isEmpty());

        record1.setGenAone(11111L);
        logPatternAlarmRecordDao.updateById(record1);

        List<LogPatternAlarmRecord> records = logPatternAlarmRecordDao.getRecordsByAoneIds(null, new Date(minT - 1), new Date(maxT + 1), Lists.newArrayList(11111L));
        Assert.assertFalse(records.isEmpty());

    }
}