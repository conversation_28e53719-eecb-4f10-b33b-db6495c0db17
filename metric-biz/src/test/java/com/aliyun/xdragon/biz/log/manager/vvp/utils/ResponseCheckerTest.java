package com.aliyun.xdragon.biz.log.manager.vvp.utils;

import com.aliyun.ververica_inner20220718.models.CreateDeploymentResponse;
import com.aliyun.ververica_inner20220718.models.CreateDeploymentResponseBody;
import com.aliyun.ververica_inner20220718.models.DeleteDeploymentResponse;
import com.aliyun.ververica_inner20220718.models.DeleteDeploymentResponseBody;
import com.aliyun.ververica_inner20220718.models.DeleteJobResponse;
import com.aliyun.ververica_inner20220718.models.DeleteJobResponseBody;
import com.aliyun.ververica_inner20220718.models.Deployment;
import com.aliyun.ververica_inner20220718.models.GetDeploymentResponse;
import com.aliyun.ververica_inner20220718.models.GetDeploymentResponseBody;
import com.aliyun.ververica_inner20220718.models.Job;
import com.aliyun.ververica_inner20220718.models.JobSummary;
import com.aliyun.ververica_inner20220718.models.StartJobResponse;
import com.aliyun.ververica_inner20220718.models.StartJobResponseBody;
import com.aliyun.ververica_inner20220718.models.StopJobResponse;
import com.aliyun.ververica_inner20220718.models.StopJobResponseBody;
import com.aliyun.ververica_inner20220718.models.StreamingResourceSetting;
import com.aliyun.ververica_inner20220718.models.UpdateDeploymentResponse;
import com.aliyun.ververica_inner20220718.models.UpdateDeploymentResponseBody;
import com.aliyun.xdragon.biz.log.manager.enums.TaskManagerErrorCode;
import com.aliyun.xdragon.biz.log.manager.enums.TaskState;
import com.aliyun.xdragon.biz.log.manager.param.InstanceResponseParam;
import com.aliyun.xdragon.biz.log.manager.param.ResponseParam;
import org.junit.Assert;
import org.junit.Test;

import static com.aliyun.xdragon.biz.log.manager.vvp.utils.ResponseChecker.checkCreateResponse;
import static com.aliyun.xdragon.biz.log.manager.vvp.utils.ResponseChecker.checkDeleteDeploymentResponse;
import static com.aliyun.xdragon.biz.log.manager.vvp.utils.ResponseChecker.checkDeleteJobResponse;
import static com.aliyun.xdragon.biz.log.manager.vvp.utils.ResponseChecker.checkGetDeploymentResponse;
import static com.aliyun.xdragon.biz.log.manager.vvp.utils.ResponseChecker.checkStartResponse;
import static com.aliyun.xdragon.biz.log.manager.vvp.utils.ResponseChecker.checkStopResponse;
import static com.aliyun.xdragon.biz.log.manager.vvp.utils.ResponseChecker.checkUpdateResponse;

public class ResponseCheckerTest {
    @Test
    public void testCheckCreateResponse() {
        ResponseParam response = checkCreateResponse(null);
        Assert.assertNotNull(response);
        Assert.assertTrue(response.getRequestId().isEmpty());
        Assert.assertEquals(TaskManagerErrorCode.RESPONSE_NULL, response.getErrorCode());
        Assert.assertEquals("Null response", response.getErrorMessage());

        CreateDeploymentResponse createResponse = new CreateDeploymentResponse();
        CreateDeploymentResponseBody body = new CreateDeploymentResponseBody();
        body.setSuccess(false);
        body.setRequestId("id");
        body.setData(new Deployment());
        createResponse.setBody(body);
        response = checkCreateResponse(createResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals("id", response.getRequestId());
        Assert.assertEquals(TaskManagerErrorCode.CMD_FAILED, response.getErrorCode());
        Assert.assertTrue(response.getErrorMessage().startsWith("Failed to create deployment"));

        body.setSuccess(true);
        response = checkCreateResponse(createResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals("id", response.getRequestId());
        Assert.assertEquals(TaskManagerErrorCode.SUCCESS, response.getErrorCode());
        Assert.assertTrue(response.getErrorMessage().isEmpty());
    }

    @Test
    public void testCheckUpdateResponse() {
        ResponseParam response = checkUpdateResponse(null);
        Assert.assertNotNull(response);
        Assert.assertTrue(response.getRequestId().isEmpty());
        Assert.assertEquals(TaskManagerErrorCode.RESPONSE_NULL, response.getErrorCode());
        Assert.assertEquals("Null response", response.getErrorMessage());

        UpdateDeploymentResponse updateResponse = new UpdateDeploymentResponse();
        UpdateDeploymentResponseBody body = new UpdateDeploymentResponseBody();
        body.setSuccess(false);
        body.setRequestId("id");
        updateResponse.setBody(body);
        response = checkUpdateResponse(updateResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals("id", response.getRequestId());
        Assert.assertEquals(TaskManagerErrorCode.CMD_FAILED, response.getErrorCode());
        Assert.assertTrue(response.getErrorMessage().startsWith("Failed to update deployment"));

        body.setSuccess(true);
        response = checkUpdateResponse(updateResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals("id", response.getRequestId());
        Assert.assertEquals(TaskManagerErrorCode.SUCCESS, response.getErrorCode());
        Assert.assertTrue(response.getErrorMessage().isEmpty());
    }

    @Test
    public void testCheckDeleteDeploymentResponse() {
        ResponseParam response = checkDeleteDeploymentResponse(null);
        Assert.assertNotNull(response);
        Assert.assertTrue(response.getRequestId().isEmpty());
        Assert.assertEquals(TaskManagerErrorCode.RESPONSE_NULL, response.getErrorCode());
        Assert.assertEquals("Null response", response.getErrorMessage());

        DeleteDeploymentResponse deleteResponse = new DeleteDeploymentResponse();
        DeleteDeploymentResponseBody body = new DeleteDeploymentResponseBody();
        body.setSuccess(false);
        body.setRequestId("id");
        deleteResponse.setBody(body);
        response = checkDeleteDeploymentResponse(deleteResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals("id", response.getRequestId());
        Assert.assertEquals(TaskManagerErrorCode.CMD_FAILED, response.getErrorCode());
        Assert.assertTrue(response.getErrorMessage().startsWith("Failed to delete deployment"));

        body.setSuccess(true);
        response = checkDeleteDeploymentResponse(deleteResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals("id", response.getRequestId());
        Assert.assertEquals(TaskManagerErrorCode.SUCCESS, response.getErrorCode());
        Assert.assertTrue(response.getErrorMessage().isEmpty());
    }

    @Test
    public void testCheckStartResponse() {
        InstanceResponseParam response = checkStartResponse(null);
        Assert.assertNotNull(response);
        Assert.assertTrue(response.getRequestId().isEmpty());
        Assert.assertEquals(TaskManagerErrorCode.RESPONSE_NULL, response.getErrorCode());
        Assert.assertEquals("Null response", response.getErrorMessage());

        StartJobResponse startResponse = new StartJobResponse();
        StartJobResponseBody body = new StartJobResponseBody();
        body.setSuccess(false);
        body.setRequestId("id");
        startResponse.setBody(body);
        response = checkStartResponse(startResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals("id", response.getRequestId());
        Assert.assertEquals(TaskManagerErrorCode.CMD_FAILED, response.getErrorCode());
        Assert.assertTrue(response.getErrorMessage().startsWith("Failed to start deployment"));
        Assert.assertEquals(TaskState.RUNNING, response.getExpectedState());
        Assert.assertEquals(TaskState.NONE, response.getCurrentState());

        body.setSuccess(true);
        Job job = new Job();
        job.setJobId("jobid");
        job.setDeploymentId("depid");
        job.setDeploymentName("name");
        job.setStreamingResourceSetting(new StreamingResourceSetting());
        body.setData(job);
        startResponse.setBody(body);
        response = checkStartResponse(startResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals("id", response.getRequestId());
        Assert.assertEquals(TaskManagerErrorCode.SUCCESS, response.getErrorCode());
        Assert.assertTrue(response.getErrorMessage().isEmpty());
        Assert.assertEquals(TaskState.RUNNING, response.getExpectedState());
        Assert.assertEquals(TaskState.RUNNING, response.getCurrentState());
    }

    @Test
    public void testCheckStopResponse() {
        InstanceResponseParam response = checkStopResponse(null);
        Assert.assertNotNull(response);
        Assert.assertTrue(response.getRequestId().isEmpty());
        Assert.assertEquals(TaskManagerErrorCode.RESPONSE_NULL, response.getErrorCode());
        Assert.assertEquals("Null response", response.getErrorMessage());

        StopJobResponse stopResponse = new StopJobResponse();
        StopJobResponseBody body = new StopJobResponseBody();
        body.setSuccess(false);
        body.setRequestId("id");
        stopResponse.setBody(body);
        response = checkStopResponse(stopResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals("id", response.getRequestId());
        Assert.assertEquals(TaskManagerErrorCode.CMD_FAILED, response.getErrorCode());
        Assert.assertTrue(response.getErrorMessage().startsWith("Failed to stop job"));
        Assert.assertEquals(TaskState.TERMINATED, response.getExpectedState());
        Assert.assertEquals(TaskState.NONE, response.getCurrentState());

        body.setSuccess(true);
        Job job = new Job();
        job.setJobId("jobid");
        job.setDeploymentId("depid");
        job.setDeploymentName("name");
        job.setStreamingResourceSetting(new StreamingResourceSetting());
        body.setData(job);
        stopResponse.setBody(body);
        response = checkStopResponse(stopResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals("id", response.getRequestId());
        Assert.assertEquals(TaskManagerErrorCode.SUCCESS, response.getErrorCode());
        Assert.assertTrue(response.getErrorMessage().isEmpty());
        Assert.assertEquals(TaskState.TERMINATED, response.getExpectedState());
        Assert.assertEquals(TaskState.TERMINATED, response.getCurrentState());
    }

    @Test
    public void testCheckDeleteJobResponse() {
        InstanceResponseParam response = checkDeleteJobResponse(null);
        Assert.assertNotNull(response);
        Assert.assertTrue(response.getRequestId().isEmpty());
        Assert.assertEquals(TaskManagerErrorCode.RESPONSE_NULL, response.getErrorCode());
        Assert.assertEquals("Null response", response.getErrorMessage());

        DeleteJobResponse deleteResponse = new DeleteJobResponse();
        DeleteJobResponseBody body = new DeleteJobResponseBody();
        body.setSuccess(false);
        body.setRequestId("id");
        deleteResponse.setBody(body);
        response = checkDeleteJobResponse(deleteResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals("id", response.getRequestId());
        Assert.assertEquals(TaskManagerErrorCode.CMD_FAILED, response.getErrorCode());
        Assert.assertTrue(response.getErrorMessage().startsWith("Failed to delete job"));
        Assert.assertEquals(TaskState.OFFLINE, response.getExpectedState());
        Assert.assertEquals(TaskState.NONE, response.getCurrentState());

        body.setSuccess(true);
        response = checkDeleteJobResponse(deleteResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals("id", response.getRequestId());
        Assert.assertEquals(TaskManagerErrorCode.SUCCESS, response.getErrorCode());
        Assert.assertTrue(response.getErrorMessage().isEmpty());
        Assert.assertEquals(TaskState.OFFLINE, response.getExpectedState());
        Assert.assertEquals(TaskState.OFFLINE, response.getCurrentState());
    }

    @Test
    public void testCheckGetDeploymentResponse() {
        InstanceResponseParam response = checkGetDeploymentResponse(null);
        Assert.assertNotNull(response);
        Assert.assertTrue(response.getRequestId().isEmpty());
        Assert.assertEquals(TaskManagerErrorCode.RESPONSE_NULL, response.getErrorCode());
        Assert.assertEquals("Null response", response.getErrorMessage());

        GetDeploymentResponse getResponse = new GetDeploymentResponse();
        GetDeploymentResponseBody body = new GetDeploymentResponseBody();
        body.setSuccess(false);
        body.setRequestId("id");
        getResponse.setBody(body);
        response = checkGetDeploymentResponse(getResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals("id", response.getRequestId());
        Assert.assertEquals(TaskManagerErrorCode.CMD_FAILED, response.getErrorCode());
        Assert.assertTrue(response.getErrorMessage().startsWith("Failed to get deployment"));
        Assert.assertEquals(TaskState.NONE, response.getCurrentState());

        body.setSuccess(true);
        Deployment deployment = new Deployment();
        JobSummary summary = new JobSummary();
        summary.setCancelled(0);
        summary.setCancelling(0);
        summary.setFailed(0);
        summary.setFinished(0);
        summary.setRunning(1);
        summary.setStarting(0);
        deployment.setJobSummary(summary);
        body.setData(deployment);
        response = checkGetDeploymentResponse(getResponse);
        Assert.assertNotNull(response);
        Assert.assertEquals("id", response.getRequestId());
        Assert.assertEquals(TaskManagerErrorCode.SUCCESS, response.getErrorCode());
        Assert.assertTrue(response.getErrorMessage().isEmpty());
        Assert.assertEquals(TaskState.OFFLINE, response.getCurrentState());
    }
}
