package com.aliyun.xdragon.biz.log.service;

import com.aliyun.xdragon.biz.log.config.AnomalyStoreConfig;
import com.aliyun.xdragon.biz.log.config.LogClusterSlsConfig;
import com.aliyun.xdragon.biz.log.manager.config.DatabaseConfig;
import com.aliyun.xdragon.biz.log.manager.enums.TaskManagerErrorCode;
import com.aliyun.xdragon.biz.log.manager.enums.TaskState;
import com.aliyun.xdragon.biz.log.manager.param.CreateRequestParam;
import com.aliyun.xdragon.biz.log.manager.param.InstanceResponseParam;
import com.aliyun.xdragon.biz.log.manager.param.RequestParam;
import com.aliyun.xdragon.biz.log.manager.param.ResponseParam;
import com.aliyun.xdragon.biz.log.manager.param.UpdateRequestParam;
import com.aliyun.xdragon.biz.log.manager.vvp.VvpTaskManager;
import com.aliyun.xdragon.biz.log.repository.ClusterRegionConfigDao;
import com.aliyun.xdragon.biz.log.repository.LogUnionTaskDao;
import com.aliyun.xdragon.biz.log.service.impl.LogFlinkTaskServiceImpl;
import com.aliyun.xdragon.common.enumeration.log.ClusterTaskStatus;
import com.aliyun.xdragon.common.generate.model.LogClusterConfigRegion;
import com.aliyun.xdragon.common.generate.model.LogClusterTask;
import com.aliyun.xdragon.common.generate.model.LogUnionTask;
import com.aliyun.xdragon.common.model.log.PlatformParam;
import com.aliyun.xdragon.common.model.log.SlsSourceParam;
import com.aliyun.xdragon.common.model.log.response.LogTaskStartStrategy;
import com.aliyun.xdragon.service.common.config.FlinkMetricSlsConfig;
import com.aliyun.xdragon.service.common.config.diamond.ConfigService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;
import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024/11/09
 */
@RunWith(PowerMockRunner.class)
public class LogFlinkTaskServiceTest {
    @InjectMocks
    private LogFlinkTaskServiceImpl logFlinkTaskService;

    @Mock
    private ConfigService configService;

    @Mock
    private VvpTaskManager vvpTaskManager;

    @Mock
    private LogUnionTaskDao logUnionTaskDao;

    @Mock
    private ClusterRegionConfigDao regionConfigDao;

    @Mock
    private DatabaseConfig dbConfig;

    @Mock
    private LogClusterSlsConfig logClusterSlsConfig;

    @Mock
    private FlinkMetricSlsConfig flinkMetricSlsConfig;

    @Mock
    private AnomalyStoreConfig anomalyStoreConfig;

    @Before
    public void init() {
        when(anomalyStoreConfig.getEndpoint()).thenReturn("endpoint");
        when(anomalyStoreConfig.getAccessId()).thenReturn("access_key_id");
        when(anomalyStoreConfig.getAccessKey()).thenReturn("access_key_secret");
        when(anomalyStoreConfig.getProject()).thenReturn("project");
        when(anomalyStoreConfig.getPatternStore()).thenReturn("logstore");
    }

    @Test
    public void createUnionTask() {
        LogClusterTask clusterTask = new LogClusterTask();
        clusterTask.setTaskId(1L);
        clusterTask.setTaskName("xdragon-metric");
        clusterTask.setTaskDescription("log cluster");
        clusterTask.setGmtCreate(new Date());
        when(configService.loadSlsUsers()).thenReturn(new HashMap<>());
        ResponseParam response = new ResponseParam();
        response.setPlatformId("xxxx");
        response.setRequestId("req_id");
        response.setErrorCode(TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.createUnionTask(any(CreateRequestParam.class))).thenReturn(response);
        LogUnionTask logUnionTask = logFlinkTaskService.createUnionTask(clusterTask, "cn-hangzhou",
            new SlsSourceParam(), new PlatformParam(), true);
        boolean ret = logUnionTask.getPlatformId() != null;
        Assert.assertTrue(ret);
        verify(logUnionTaskDao, times(1)).addUnionTask(any(LogUnionTask.class));
        verify(logUnionTaskDao, times(1)).updateUnionTaskWithId(any(), any(LogUnionTask.class));

        reset(logUnionTaskDao);
        reset(vvpTaskManager);
        response.setErrorCode(TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.createUnionTask(any(CreateRequestParam.class))).thenReturn(response);
        logUnionTask = logFlinkTaskService.createUnionTask(clusterTask, "cn-hangzhou", new SlsSourceParam(),
            new PlatformParam(), true);
        ret = logUnionTask.getPlatformId() != null;
        Assert.assertFalse(ret);
        verify(logUnionTaskDao, times(1)).addUnionTask(any(LogUnionTask.class));
        verify(logUnionTaskDao, times(1)).updateUnionTaskWithId(any(), any(LogUnionTask.class));
    }

    @Test
    public void updateUnionTask() {
        LogClusterTask clusterTask = new LogClusterTask();
        clusterTask.setTaskId(1L);
        clusterTask.setTaskName("xdragon-metric");
        clusterTask.setTaskDescription("log cluster");
        clusterTask.setGmtCreate(new Date());
        LogUnionTask unionTask = new LogUnionTask();
        unionTask.setUnionId(1L);
        unionTask.setTaskStatus(16);
        unionTask.setPlatformId("xxx");
        unionTask.setSourceParam("{}");

        // not updatable
        boolean ret = logFlinkTaskService.updateUnionTask(clusterTask, unionTask, new LogTaskStartStrategy());
        Assert.assertFalse(ret);
        verify(logUnionTaskDao, times(0)).updateUnionTaskWithId(anyLong(), any());

        // update fail
        unionTask.setTaskStatus(3);
        ResponseParam responseFail = new ResponseParam();
        responseFail.setErrorCode(TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.updateUnionTask(any())).thenReturn(responseFail);
        ret = logFlinkTaskService.updateUnionTask(clusterTask, unionTask, new LogTaskStartStrategy());
        Assert.assertFalse(ret);
        verify(logUnionTaskDao, times(0)).updateUnionTaskWithId(anyLong(), any());

        // update success, stop fail
        unionTask.setTaskStatus(3);
        reset(vvpTaskManager);
        reset(logUnionTaskDao);
        ResponseParam responseUpdateSucc = new ResponseParam("req", TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.updateUnionTask(any())).thenReturn(responseUpdateSucc);

        InstanceResponseParam responseStopFail = new InstanceResponseParam("req", TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.stopTask(any())).thenReturn(responseStopFail);
        ret = logFlinkTaskService.updateUnionTask(clusterTask, unionTask, new LogTaskStartStrategy());
        Assert.assertFalse(ret);
        verify(logUnionTaskDao, times(3)).updateUnionTaskWithId(anyLong(), any());

        // update success, stop success, start fail
        unionTask.setTaskStatus(3);
        reset(vvpTaskManager);
        reset(logUnionTaskDao);
        InstanceResponseParam responseStopSucc = new InstanceResponseParam("req", TaskManagerErrorCode.SUCCESS);
        InstanceResponseParam responseStartFail = new InstanceResponseParam("req", TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.updateUnionTask(any())).thenReturn(responseUpdateSucc);
        when(vvpTaskManager.stopTask(any())).thenReturn(responseStopSucc);
        when(vvpTaskManager.startTask(any())).thenReturn(responseStartFail);
        ret = logFlinkTaskService.updateUnionTask(clusterTask, unionTask, new LogTaskStartStrategy());
        Assert.assertFalse(ret);
        verify(logUnionTaskDao, times(5)).updateUnionTaskWithId(anyLong(), any());

        // update success, stop success, start success
        unionTask.setTaskStatus(3);
        reset(vvpTaskManager);
        reset(logUnionTaskDao);
        InstanceResponseParam responseStartSucc = new InstanceResponseParam("req", TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.updateUnionTask(any())).thenReturn(responseUpdateSucc);
        when(vvpTaskManager.stopTask(any())).thenReturn(responseStopSucc);
        when(vvpTaskManager.startTask(any())).thenReturn(responseStartSucc);
        ret = logFlinkTaskService.updateUnionTask(clusterTask, unionTask, new LogTaskStartStrategy());
        Assert.assertTrue(ret);
        verify(logUnionTaskDao, times(5)).updateUnionTaskWithId(anyLong(), any());

    }

    @Test
    public void startUnionTask() {
        LogClusterTask clusterTask = new LogClusterTask();
        clusterTask.setTaskId(1L);
        clusterTask.setTaskName("xdragon-metric");
        clusterTask.setTaskDescription("log cluster");
        clusterTask.setGmtCreate(new Date());
        LogUnionTask unionTask = new LogUnionTask();
        unionTask.setUnionId(1L);
        unionTask.setTaskStatus(16);
        unionTask.setSourceParam("{}");

        // create task fail
        ResponseParam createResponse = new ResponseParam();
        createResponse.setPlatformId("xxxx");
        createResponse.setRequestId("req_id");
        createResponse.setErrorCode(TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.createUnionTask(any())).thenReturn(createResponse);
        boolean ret = logFlinkTaskService.startUnionTask(clusterTask, unionTask, new LogTaskStartStrategy());
        Assert.assertFalse(ret);
        verify(logUnionTaskDao, times(2)).updateUnionTaskWithId(anyLong(), any());

        // start success
        reset(vvpTaskManager);
        reset(logUnionTaskDao);
        unionTask.setPlatformId("xxxxx");

        InstanceResponseParam responseSucc = new InstanceResponseParam("req_id", TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.startTask(any())).thenReturn(responseSucc);
        ret = logFlinkTaskService.startUnionTask(clusterTask, unionTask, new LogTaskStartStrategy());
        Assert.assertTrue(ret);
        verify(logUnionTaskDao, times(2)).updateUnionTaskWithId(anyLong(), any());

        // start fail
        reset(vvpTaskManager);
        reset(logUnionTaskDao);
        unionTask.setPlatformId("xxxxx");

        InstanceResponseParam responseFail = new InstanceResponseParam("req_id", TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.startTask(any())).thenReturn(responseFail);
        ret = logFlinkTaskService.startUnionTask(clusterTask, unionTask, new LogTaskStartStrategy());
        Assert.assertFalse(ret);
        verify(logUnionTaskDao, times(2)).updateUnionTaskWithId(anyLong(), any());
    }

    @Test
    public void stopUnionTask() {
        LogUnionTask unionTask = new LogUnionTask();
        unionTask.setUnionId(1L);
        unionTask.setPlatformId("xxxxxx");
        InstanceResponseParam responseSucc = new InstanceResponseParam("req_id", TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.stopTask(any())).thenReturn(responseSucc);
        boolean ret = logFlinkTaskService.stopUnionTask(unionTask);
        Assert.assertTrue(ret);
        verify(logUnionTaskDao, times(2)).updateUnionTaskWithId(anyLong(), any(LogUnionTask.class));

        reset(vvpTaskManager);
        reset(logUnionTaskDao);
        InstanceResponseParam responseFail = new InstanceResponseParam("req_id", TaskManagerErrorCode.STATE_ERROR);
        responseFail.setCurrentState(TaskState.TERMINATED);
        when(vvpTaskManager.stopTask(any())).thenReturn(responseFail);
        ret = logFlinkTaskService.stopUnionTask(unionTask);
        Assert.assertTrue(ret);
        verify(logUnionTaskDao, times(2)).updateUnionTaskWithId(anyLong(), any(LogUnionTask.class));

        reset(vvpTaskManager);
        reset(logUnionTaskDao);
        InstanceResponseParam responseFail2 = new InstanceResponseParam("req_id", TaskManagerErrorCode.STATE_ERROR);
        responseFail.setCurrentState(TaskState.RUNNING);
        when(vvpTaskManager.stopTask(any())).thenReturn(responseFail2);
        ret = logFlinkTaskService.stopUnionTask(unionTask);
        Assert.assertFalse(ret);
        verify(logUnionTaskDao, times(2)).updateUnionTaskWithId(anyLong(), any(LogUnionTask.class));
    }

    @Test
    public void deleteUnionTask() {
        LogUnionTask unionTask = new LogUnionTask();
        unionTask.setUnionId(1L);
        unionTask.setPlatformId("xxxxxx");
        ResponseParam responseSucc = new ResponseParam("req_id", TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.deleteTask(any())).thenReturn(responseSucc);
        boolean ret = logFlinkTaskService.deleteUnionTask(unionTask);
        Assert.assertTrue(ret);
        verify(logUnionTaskDao, times(1)).updateUnionTaskWithId(anyLong(), any());

        reset(vvpTaskManager);
        reset(logUnionTaskDao);
        ResponseParam responseNotExist = new ResponseParam("req_id", TaskManagerErrorCode.TASK_NOT_EXIST);
        when(vvpTaskManager.deleteTask(any())).thenReturn(responseNotExist);
        ret = logFlinkTaskService.deleteUnionTask(unionTask);
        Assert.assertTrue(ret);
        verify(logUnionTaskDao, times(1)).updateUnionTaskWithId(anyLong(), any());

        reset(vvpTaskManager);
        reset(logUnionTaskDao);
        ResponseParam responseFail = new ResponseParam("req_id", TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.deleteTask(any())).thenReturn(responseFail);
        ret = logFlinkTaskService.deleteUnionTask(unionTask);
        Assert.assertFalse(ret);
        verify(logUnionTaskDao, times(0)).updateUnionTaskWithId(anyLong(), any());
    }

    @Test
    public void createMatchTask() {
        LogClusterConfigRegion task = new LogClusterConfigRegion();
        task.setTaskId(1L);
        task.setInputSourceParam(
            "{\"sourceType\":\"SLS\",\"accountName\":\"newbie_ecs\",\"regionType\":2,\"endpoint\":\"cn-hangzhou-corp"
                + ".sls.aliyuncs.com\",\"project\":\"ecs-xunjian\",\"logStore\":\"cloudops_service_error_v2\","
                + "\"consumerGroup\":\"log_cluster_consumer\",\"logField\":\"message\",\"filterField\":\"level\","
                + "\"filterString\":\"ERROR\",\"extFields\":\"__source__,__tag__:__path__\",\"filterGamma\":true,"
                + "\"filterStatus\":true,\"sinkParam\":{\"sinkSls\":true,\"sinkPretreat\":false,\"sinkDetail\":{}}}");
        task.setPlatformParam(
            "{\"cpu\":4.0,\"memory\":\"60Gi\",\"parallelism\":12,"
                + "\"last_run_resource\":\"{\\\"expertResourceSetting\\\":{\\\"jobmanagerResourceSettingSpec"
                + "\\\":{\\\"cpu"
                + "\\\":1.0,\\\"memory\\\":\\\"4GiB\\\"},"
                + "\\\"resourcePlan\\\":\\\"{\\\\\\\"ssgProfiles\\\\\\\":[{\\\\\\\"name\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.07,\\\\\\\"heap\\\\\\\":\\\\\\\"2 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"944 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"3\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"1 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"64 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"432 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"48 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,\\\\\\\"heap\\\\\\\":\\\\\\\"2 "
                + "gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"992 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"cpu\\\\\\\":8.0,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"32 gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 mb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}}],"
                + "\\\\\\\"nodes\\\\\\\":[{\\\\\\\"id\\\\\\\":1,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecTableSourceScan\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Source: "
                + "sls_input_table[26004]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":8,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":2,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26005]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":3,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecWatermarkAssigner\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"WatermarkAssigner[26006]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":4,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26007]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":5,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26008]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":6,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26009]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":7,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26010]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":8,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26011]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":9,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26012]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"parallelism\\\\\\\":16,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":10,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26013]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":11,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26014]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":12,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26015]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":14,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26017]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":15,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26018]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":16,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26019]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":18,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26021]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":19,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26022]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":20,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26023]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":22,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26025]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":23,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26026]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":24,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26027]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":25,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26028]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":27,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Join[26030]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1},"
                + "\\\\\\\"state\\\\\\\":[{\\\\\\\"index\\\\\\\":0,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"leftState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true},"
                + "{\\\\\\\"index\\\\\\\":1,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"rightState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true}]},"
                + "{\\\\\\\"id\\\\\\\":28,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26031]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":29,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26032]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":30,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26033]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":31,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26034]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":32,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26035]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":33,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "remain_table[26036]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":128,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":34,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26037]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":35,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26038]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":36,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26039]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":38,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26041]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":39,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26042]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":40,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26043]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":41,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26044]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":42,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26045]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":43,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26046]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":44,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26047]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":45,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26048]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":46,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26049]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":47,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26050]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":48,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26051]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":49,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26052]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":51,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26054]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":52,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26055]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":53,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26056]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":54,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26057]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":55,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26058]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":56,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26059]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":57,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26060]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":59,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26062]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":60,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26063]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":61,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26064]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":62,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26065]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":63,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26066]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":64,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26067]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":65,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26068]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":66,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26069]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":67,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26070]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":68,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26071]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":69,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26072]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":70,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26073]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":71,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26074]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":72,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26075]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":73,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26076]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":74,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26077]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}}],\\\\\\\"edges\\\\\\\":[{\\\\\\\"source\\\\\\\":1,"
                + "\\\\\\\"target\\\\\\\":2,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":2,"
                + "\\\\\\\"target\\\\\\\":3,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":3,"
                + "\\\\\\\"target\\\\\\\":4,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":4,\\\\\\\"target\\\\\\\":5,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":5,"
                + "\\\\\\\"target\\\\\\\":6,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":6,\\\\\\\"target\\\\\\\":7,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":7,"
                + "\\\\\\\"target\\\\\\\":8,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":8,\\\\\\\"target\\\\\\\":9,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":9,"
                + "\\\\\\\"target\\\\\\\":10,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":11,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":12,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":14,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":14,"
                + "\\\\\\\"target\\\\\\\":15,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":15,"
                + "\\\\\\\"target\\\\\\\":16,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":16,"
                + "\\\\\\\"target\\\\\\\":18,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":18,\\\\\\\"target\\\\\\\":19,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":19,\\\\\\\"target\\\\\\\":20,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":20,"
                + "\\\\\\\"target\\\\\\\":22,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":22,"
                + "\\\\\\\"target\\\\\\\":23,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":23,\\\\\\\"target\\\\\\\":24,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":24,"
                + "\\\\\\\"target\\\\\\\":25,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":12,"
                + "\\\\\\\"target\\\\\\\":27,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":25,"
                + "\\\\\\\"target\\\\\\\":27,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":27,\\\\\\\"target\\\\\\\":28,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":28,"
                + "\\\\\\\"target\\\\\\\":29,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":29,"
                + "\\\\\\\"target\\\\\\\":30,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":30,"
                + "\\\\\\\"target\\\\\\\":31,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":31,"
                + "\\\\\\\"target\\\\\\\":32,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":32,"
                + "\\\\\\\"target\\\\\\\":33,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":34,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":35,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":35,"
                + "\\\\\\\"target\\\\\\\":36,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":36,"
                + "\\\\\\\"target\\\\\\\":38,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":38,\\\\\\\"target\\\\\\\":39,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":39,\\\\\\\"target\\\\\\\":40,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":41,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":41,"
                + "\\\\\\\"target\\\\\\\":42,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":23,"
                + "\\\\\\\"target\\\\\\\":43,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":43,"
                + "\\\\\\\"target\\\\\\\":44,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":44,"
                + "\\\\\\\"target\\\\\\\":45,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":46,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":46,"
                + "\\\\\\\"target\\\\\\\":47,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":48,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":48,"
                + "\\\\\\\"target\\\\\\\":49,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":49,"
                + "\\\\\\\"target\\\\\\\":51,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":51,\\\\\\\"target\\\\\\\":52,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":52,\\\\\\\"target\\\\\\\":53,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":53,"
                + "\\\\\\\"target\\\\\\\":54,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":54,"
                + "\\\\\\\"target\\\\\\\":55,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":56,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":56,"
                + "\\\\\\\"target\\\\\\\":57,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":57,"
                + "\\\\\\\"target\\\\\\\":59,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":59,\\\\\\\"target\\\\\\\":60,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":60,\\\\\\\"target\\\\\\\":61,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":61,"
                + "\\\\\\\"target\\\\\\\":62,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":62,"
                + "\\\\\\\"target\\\\\\\":63,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":64,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":64,"
                + "\\\\\\\"target\\\\\\\":65,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":65,"
                + "\\\\\\\"target\\\\\\\":66,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":67,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":67,"
                + "\\\\\\\"target\\\\\\\":68,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":69,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":69,"
                + "\\\\\\\"target\\\\\\\":70,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":71,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":71,"
                + "\\\\\\\"target\\\\\\\":72,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":73,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":73,"
                + "\\\\\\\"target\\\\\\\":74,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"}],"
                + "\\\\\\\"vertices\\\\\\\":{\\\\\\\"40384a6339eef72bc6b9656f8cf2bcaf\\\\\\\":[28,29,30,31,32,33],"
                + "\\\\\\\"fe817019d2e9b45766686a93ee230ef0\\\\\\\":[38,39,40,41,42,67,68,71,72],"
                + "\\\\\\\"a408d3633275bd7d9d07fe871f1a4f35\\\\\\\":[27],"
                + "\\\\\\\"5d70c8a84279c549c09bb6a963637464\\\\\\\":[22],"
                + "\\\\\\\"9fef778f6d856a2525f7f06f6f32e5c4\\\\\\\":[18,19,20],"
                + "\\\\\\\"693f85ee17f8d43483be5737897dafc0\\\\\\\":[51,52,53,54,55],"
                + "\\\\\\\"8fe87100eddafceda9257ebf607aa098\\\\\\\":[10,11,12,14,15,16,34,35,36,48,49,56,57],"
                + "\\\\\\\"5edb50593c2d5e4cd49b5a1427720bd1\\\\\\\":[23,24,25,43,44,45,46,47,64,65,66,69,70,73,74],"
                + "\\\\\\\"788a2d89ac307fe9cb2d3a97e995b244\\\\\\\":[9],"
                + "\\\\\\\"717c7b8afebbfb7137f6f0f99beb2a94\\\\\\\":[1],"
                + "\\\\\\\"0e8289f2bf927649dd2511bbc2bb6759\\\\\\\":[2,3,4,5,6,7,8],"
                + "\\\\\\\"9a03d13f8211c1b0f332bbaf15991fd7\\\\\\\":[59,60,61,62,63]}}\\\"},"
                + "\\\"resourceSettingMode\\\":\\\"EXPERT\\\"}\"}");
        task.setAlgParam(
            "{\"waterMarker\":30000,\"windowSize\":15,\"support\":50,\"rsupport\":5,\"cutLength\":50000,"
                + "\"cutLines\":5000,\"weightThreshold\":0.04,\"preprocess\":true,\"trimMode\":\"top\","
                + "\"threshold\":10000,\"regExps\":{\"customer\":[\"102;98;95;96;99;93;92;91;90;65;89;64;94;97;100;"
                + "45;43;"
                + "4;10;11;12;17;19;22;24;28;30;31;33;41;42;58;63;2;62\"],\"regMap\":{}}}");
        task.setOutputSinkParam(
            "{\"sinkSls\":true,\"sinkPretreat\":false,\"sinkDetail\":{},"
                + "\"sourceLogStore\":\"xdragon-metric-application\",\"sourceProject\":\"log-cluster\"}");
        when(configService.loadSlsUsers()).thenReturn(new HashMap<>());

        ResponseParam response = new ResponseParam();
        response.setPlatformId("xxxx");
        response.setRequestId("req_id");
        response.setErrorCode(TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.createMatchTask(any(CreateRequestParam.class))).thenReturn(response);
        LogClusterTask logClusterTask = new LogClusterTask();
        logClusterTask.setTaskName("tm");
        LogClusterConfigRegion task2 = logFlinkTaskService.createMatchTask(logClusterTask, task, true);
        Assert.assertNotNull(task2);

        response.setErrorCode(TaskManagerErrorCode.CMD_FAILED);
        response.setErrorMessage("create task command failed");
        task2 = logFlinkTaskService.createMatchTask(logClusterTask, task, true);
        Assert.assertEquals(task2.getStatus().intValue(), ClusterTaskStatus.CREATE_FAILED.getStatus().intValue());
    }

    @Test
    public void updateMatchTask() {
        LogClusterConfigRegion task = new LogClusterConfigRegion();
        task.setTaskId(1L);
        task.setPlatformId("xxxx");
        task.setInputSourceParam(
            "{\"sourceType\":\"SLS\",\"accountName\":\"newbie_ecs\",\"regionType\":2,\"endpoint\":\"cn-hangzhou-corp"
                + ".sls.aliyuncs.com\",\"project\":\"ecs-xunjian\",\"logStore\":\"cloudops_service_error_v2\","
                + "\"consumerGroup\":\"log_cluster_consumer\",\"logField\":\"message\",\"filterField\":\"level\","
                + "\"filterString\":\"ERROR\",\"extFields\":\"__source__,__tag__:__path__\",\"filterGamma\":true,"
                + "\"filterStatus\":true,\"sinkParam\":{\"sinkSls\":true,\"sinkPretreat\":false,\"sinkDetail\":{}}}");
        task.setPlatformParam(
            "{\"cpu\":4.0,\"memory\":\"60Gi\",\"parallelism\":12,"
                + "\"last_run_resource\":\"{\\\"expertResourceSetting\\\":{\\\"jobmanagerResourceSettingSpec"
                + "\\\":{\\\"cpu"
                + "\\\":1.0,\\\"memory\\\":\\\"4GiB\\\"},"
                + "\\\"resourcePlan\\\":\\\"{\\\\\\\"ssgProfiles\\\\\\\":[{\\\\\\\"name\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.07,\\\\\\\"heap\\\\\\\":\\\\\\\"2 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"944 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"3\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"1 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"64 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"432 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"48 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,\\\\\\\"heap\\\\\\\":\\\\\\\"2 "
                + "gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"992 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"cpu\\\\\\\":8.0,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"32 gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 mb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}}],"
                + "\\\\\\\"nodes\\\\\\\":[{\\\\\\\"id\\\\\\\":1,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecTableSourceScan\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Source: "
                + "sls_input_table[26004]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":8,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":2,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26005]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":3,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecWatermarkAssigner\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"WatermarkAssigner[26006]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":4,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26007]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":5,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26008]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":6,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26009]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":7,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26010]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":8,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26011]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":9,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26012]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"parallelism\\\\\\\":16,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":10,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26013]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":11,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26014]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":12,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26015]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":14,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26017]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":15,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26018]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":16,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26019]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":18,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26021]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":19,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26022]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":20,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26023]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":22,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26025]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":23,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26026]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":24,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26027]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":25,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26028]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":27,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Join[26030]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1},"
                + "\\\\\\\"state\\\\\\\":[{\\\\\\\"index\\\\\\\":0,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"leftState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true},"
                + "{\\\\\\\"index\\\\\\\":1,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"rightState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true}]},"
                + "{\\\\\\\"id\\\\\\\":28,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26031]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":29,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26032]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":30,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26033]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":31,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26034]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":32,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26035]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":33,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "remain_table[26036]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":128,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":34,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26037]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":35,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26038]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":36,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26039]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":38,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26041]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":39,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26042]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":40,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26043]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":41,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26044]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":42,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26045]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":43,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26046]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":44,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26047]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":45,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26048]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":46,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26049]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":47,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26050]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":48,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26051]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":49,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26052]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":51,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26054]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":52,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26055]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":53,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26056]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":54,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26057]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":55,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26058]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":56,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26059]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":57,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26060]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":59,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26062]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":60,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26063]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":61,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26064]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":62,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26065]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":63,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26066]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":64,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26067]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":65,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26068]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":66,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26069]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":67,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26070]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":68,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26071]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":69,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26072]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":70,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26073]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":71,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26074]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":72,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26075]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":73,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26076]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":74,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26077]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}}],\\\\\\\"edges\\\\\\\":[{\\\\\\\"source\\\\\\\":1,"
                + "\\\\\\\"target\\\\\\\":2,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":2,"
                + "\\\\\\\"target\\\\\\\":3,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":3,"
                + "\\\\\\\"target\\\\\\\":4,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":4,\\\\\\\"target\\\\\\\":5,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":5,"
                + "\\\\\\\"target\\\\\\\":6,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":6,\\\\\\\"target\\\\\\\":7,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":7,"
                + "\\\\\\\"target\\\\\\\":8,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":8,\\\\\\\"target\\\\\\\":9,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":9,"
                + "\\\\\\\"target\\\\\\\":10,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":11,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":12,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":14,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":14,"
                + "\\\\\\\"target\\\\\\\":15,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":15,"
                + "\\\\\\\"target\\\\\\\":16,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":16,"
                + "\\\\\\\"target\\\\\\\":18,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":18,\\\\\\\"target\\\\\\\":19,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":19,\\\\\\\"target\\\\\\\":20,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":20,"
                + "\\\\\\\"target\\\\\\\":22,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":22,"
                + "\\\\\\\"target\\\\\\\":23,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":23,\\\\\\\"target\\\\\\\":24,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":24,"
                + "\\\\\\\"target\\\\\\\":25,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":12,"
                + "\\\\\\\"target\\\\\\\":27,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":25,"
                + "\\\\\\\"target\\\\\\\":27,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":27,\\\\\\\"target\\\\\\\":28,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":28,"
                + "\\\\\\\"target\\\\\\\":29,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":29,"
                + "\\\\\\\"target\\\\\\\":30,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":30,"
                + "\\\\\\\"target\\\\\\\":31,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":31,"
                + "\\\\\\\"target\\\\\\\":32,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":32,"
                + "\\\\\\\"target\\\\\\\":33,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":34,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":35,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":35,"
                + "\\\\\\\"target\\\\\\\":36,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":36,"
                + "\\\\\\\"target\\\\\\\":38,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":38,\\\\\\\"target\\\\\\\":39,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":39,\\\\\\\"target\\\\\\\":40,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":41,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":41,"
                + "\\\\\\\"target\\\\\\\":42,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":23,"
                + "\\\\\\\"target\\\\\\\":43,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":43,"
                + "\\\\\\\"target\\\\\\\":44,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":44,"
                + "\\\\\\\"target\\\\\\\":45,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":46,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":46,"
                + "\\\\\\\"target\\\\\\\":47,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":48,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":48,"
                + "\\\\\\\"target\\\\\\\":49,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":49,"
                + "\\\\\\\"target\\\\\\\":51,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":51,\\\\\\\"target\\\\\\\":52,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":52,\\\\\\\"target\\\\\\\":53,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":53,"
                + "\\\\\\\"target\\\\\\\":54,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":54,"
                + "\\\\\\\"target\\\\\\\":55,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":56,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":56,"
                + "\\\\\\\"target\\\\\\\":57,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":57,"
                + "\\\\\\\"target\\\\\\\":59,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":59,\\\\\\\"target\\\\\\\":60,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":60,\\\\\\\"target\\\\\\\":61,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":61,"
                + "\\\\\\\"target\\\\\\\":62,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":62,"
                + "\\\\\\\"target\\\\\\\":63,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":64,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":64,"
                + "\\\\\\\"target\\\\\\\":65,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":65,"
                + "\\\\\\\"target\\\\\\\":66,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":67,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":67,"
                + "\\\\\\\"target\\\\\\\":68,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":69,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":69,"
                + "\\\\\\\"target\\\\\\\":70,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":71,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":71,"
                + "\\\\\\\"target\\\\\\\":72,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":73,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":73,"
                + "\\\\\\\"target\\\\\\\":74,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"}],"
                + "\\\\\\\"vertices\\\\\\\":{\\\\\\\"40384a6339eef72bc6b9656f8cf2bcaf\\\\\\\":[28,29,30,31,32,33],"
                + "\\\\\\\"fe817019d2e9b45766686a93ee230ef0\\\\\\\":[38,39,40,41,42,67,68,71,72],"
                + "\\\\\\\"a408d3633275bd7d9d07fe871f1a4f35\\\\\\\":[27],"
                + "\\\\\\\"5d70c8a84279c549c09bb6a963637464\\\\\\\":[22],"
                + "\\\\\\\"9fef778f6d856a2525f7f06f6f32e5c4\\\\\\\":[18,19,20],"
                + "\\\\\\\"693f85ee17f8d43483be5737897dafc0\\\\\\\":[51,52,53,54,55],"
                + "\\\\\\\"8fe87100eddafceda9257ebf607aa098\\\\\\\":[10,11,12,14,15,16,34,35,36,48,49,56,57],"
                + "\\\\\\\"5edb50593c2d5e4cd49b5a1427720bd1\\\\\\\":[23,24,25,43,44,45,46,47,64,65,66,69,70,73,74],"
                + "\\\\\\\"788a2d89ac307fe9cb2d3a97e995b244\\\\\\\":[9],"
                + "\\\\\\\"717c7b8afebbfb7137f6f0f99beb2a94\\\\\\\":[1],"
                + "\\\\\\\"0e8289f2bf927649dd2511bbc2bb6759\\\\\\\":[2,3,4,5,6,7,8],"
                + "\\\\\\\"9a03d13f8211c1b0f332bbaf15991fd7\\\\\\\":[59,60,61,62,63]}}\\\"},"
                + "\\\"resourceSettingMode\\\":\\\"EXPERT\\\"}\"}");
        task.setAlgParam(
            "{\"waterMarker\":30000,\"windowSize\":15,\"support\":50,\"rsupport\":5,\"cutLength\":50000,"
                + "\"cutLines\":5000,\"weightThreshold\":0.04,\"preprocess\":true,\"trimMode\":\"top\","
                + "\"threshold\":10000,\"regExps\":{\"customer\":[\"102;98;95;96;99;93;92;91;90;65;89;64;94;97;100;"
                + "45;43;"
                + "4;10;11;12;17;19;22;24;28;30;31;33;41;42;58;63;2;62\"],\"regMap\":{}}}");
        task.setOutputSinkParam(
            "{\"sinkSls\":true,\"sinkPretreat\":false,\"sinkDetail\":{},"
            + "\"sourceLogStore\":\"xdragon-metric-application\",\"sourceProject\":\"log-cluster\"}");
        task.setStatus(Integer.valueOf(6).byteValue());
        when(configService.loadSlsUsers()).thenReturn(new HashMap<>());

        // not updatable
        boolean ret = logFlinkTaskService.updateMatchTask(null, task, new LogTaskStartStrategy());
        Assert.assertFalse(ret);

        // update fail
        task.setStatus(Integer.valueOf(3).byteValue());
        ResponseParam responseFail = new ResponseParam();
        responseFail.setErrorCode(TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.updateMatchTask(any(UpdateRequestParam.class))).thenReturn(responseFail);
        ret = logFlinkTaskService.updateMatchTask(null, task, new LogTaskStartStrategy());
        Assert.assertFalse(ret);

        // update fail, stop fail
        ResponseParam responseUpdateSucc = new ResponseParam("req", TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.updateUnionTask(any())).thenReturn(responseUpdateSucc);

        InstanceResponseParam responseStopFail = new InstanceResponseParam("req", TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.stopTask(any())).thenReturn(responseStopFail);
        ret = logFlinkTaskService.updateMatchTask(null, task, new LogTaskStartStrategy());
        Assert.assertFalse(ret);

        // update fail, stop success, start fail
        InstanceResponseParam responseStopSucc = new InstanceResponseParam("req", TaskManagerErrorCode.SUCCESS);
        InstanceResponseParam responseStartFail = new InstanceResponseParam("req", TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.updateMatchTask(any())).thenReturn(responseUpdateSucc);
        when(vvpTaskManager.stopTask(any())).thenReturn(responseStopSucc);
        when(vvpTaskManager.startTask(any())).thenReturn(responseStartFail);
        ret = logFlinkTaskService.updateMatchTask(null, task, new LogTaskStartStrategy());
        Assert.assertFalse(ret);

        // update success, stop success, start success
        InstanceResponseParam responseStartSucc = new InstanceResponseParam("req", TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.updateMatchTask(any())).thenReturn(responseUpdateSucc);
        when(vvpTaskManager.stopTask(any())).thenReturn(responseStopSucc);
        when(vvpTaskManager.startTask(any())).thenReturn(responseStartSucc);
        ret = logFlinkTaskService.updateMatchTask(null, task, new LogTaskStartStrategy());
        Assert.assertTrue(ret);
    }

    @Test
    public void startMatchTask() {
        LogClusterConfigRegion task = new LogClusterConfigRegion();
        task.setTaskId(1L);
        task.setInputSourceParam(
            "{\"sourceType\":\"SLS\",\"accountName\":\"newbie_ecs\",\"regionType\":2,\"endpoint\":\"cn-hangzhou-corp"
                + ".sls.aliyuncs.com\",\"project\":\"ecs-xunjian\",\"logStore\":\"cloudops_service_error_v2\","
                + "\"consumerGroup\":\"log_cluster_consumer\",\"logField\":\"message\",\"filterField\":\"level\","
                + "\"filterString\":\"ERROR\",\"extFields\":\"__source__,__tag__:__path__\",\"filterGamma\":true,"
                + "\"filterStatus\":true,\"sinkParam\":{\"sinkSls\":true,\"sinkPretreat\":false,\"sinkDetail\":{}}}");
        task.setPlatformParam(
            "{\"cpu\":4.0,\"memory\":\"60Gi\",\"parallelism\":12,"
                + "\"last_run_resource\":\"{\\\"expertResourceSetting\\\":{\\\"jobmanagerResourceSettingSpec"
                + "\\\":{\\\"cpu"
                + "\\\":1.0,\\\"memory\\\":\\\"4GiB\\\"},"
                + "\\\"resourcePlan\\\":\\\"{\\\\\\\"ssgProfiles\\\\\\\":[{\\\\\\\"name\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.07,\\\\\\\"heap\\\\\\\":\\\\\\\"2 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"944 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"3\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"1 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"64 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"432 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"48 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,\\\\\\\"heap\\\\\\\":\\\\\\\"2 "
                + "gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"992 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"cpu\\\\\\\":8.0,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"32 gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 mb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}}],"
                + "\\\\\\\"nodes\\\\\\\":[{\\\\\\\"id\\\\\\\":1,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecTableSourceScan\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Source: "
                + "sls_input_table[26004]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":8,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":2,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26005]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":3,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecWatermarkAssigner\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"WatermarkAssigner[26006]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":4,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26007]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":5,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26008]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":6,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26009]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":7,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26010]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":8,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26011]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":9,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26012]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"parallelism\\\\\\\":16,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":10,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26013]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":11,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26014]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":12,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26015]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":14,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26017]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":15,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26018]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":16,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26019]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":18,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26021]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":19,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26022]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":20,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26023]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":22,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26025]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":23,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26026]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":24,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26027]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":25,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26028]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":27,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Join[26030]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1},"
                + "\\\\\\\"state\\\\\\\":[{\\\\\\\"index\\\\\\\":0,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"leftState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true},"
                + "{\\\\\\\"index\\\\\\\":1,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"rightState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true}]},"
                + "{\\\\\\\"id\\\\\\\":28,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26031]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":29,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26032]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":30,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26033]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":31,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26034]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":32,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26035]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":33,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "remain_table[26036]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":128,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":34,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26037]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":35,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26038]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":36,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26039]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":38,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26041]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":39,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26042]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":40,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26043]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":41,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26044]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":42,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26045]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":43,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26046]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":44,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26047]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":45,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26048]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":46,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26049]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":47,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26050]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":48,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26051]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":49,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26052]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":51,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26054]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":52,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26055]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":53,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26056]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":54,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26057]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":55,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26058]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":56,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26059]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":57,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26060]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":59,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26062]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":60,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26063]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":61,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26064]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":62,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26065]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":63,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26066]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":64,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26067]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":65,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26068]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":66,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26069]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":67,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26070]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":68,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26071]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":69,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26072]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":70,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26073]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":71,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26074]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":72,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26075]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":73,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26076]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":74,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26077]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}}],\\\\\\\"edges\\\\\\\":[{\\\\\\\"source\\\\\\\":1,"
                + "\\\\\\\"target\\\\\\\":2,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":2,"
                + "\\\\\\\"target\\\\\\\":3,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":3,"
                + "\\\\\\\"target\\\\\\\":4,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":4,\\\\\\\"target\\\\\\\":5,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":5,"
                + "\\\\\\\"target\\\\\\\":6,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":6,\\\\\\\"target\\\\\\\":7,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":7,"
                + "\\\\\\\"target\\\\\\\":8,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":8,\\\\\\\"target\\\\\\\":9,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":9,"
                + "\\\\\\\"target\\\\\\\":10,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":11,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":12,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":14,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":14,"
                + "\\\\\\\"target\\\\\\\":15,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":15,"
                + "\\\\\\\"target\\\\\\\":16,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":16,"
                + "\\\\\\\"target\\\\\\\":18,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":18,\\\\\\\"target\\\\\\\":19,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":19,\\\\\\\"target\\\\\\\":20,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":20,"
                + "\\\\\\\"target\\\\\\\":22,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":22,"
                + "\\\\\\\"target\\\\\\\":23,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":23,\\\\\\\"target\\\\\\\":24,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":24,"
                + "\\\\\\\"target\\\\\\\":25,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":12,"
                + "\\\\\\\"target\\\\\\\":27,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":25,"
                + "\\\\\\\"target\\\\\\\":27,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":27,\\\\\\\"target\\\\\\\":28,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":28,"
                + "\\\\\\\"target\\\\\\\":29,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":29,"
                + "\\\\\\\"target\\\\\\\":30,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":30,"
                + "\\\\\\\"target\\\\\\\":31,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":31,"
                + "\\\\\\\"target\\\\\\\":32,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":32,"
                + "\\\\\\\"target\\\\\\\":33,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":34,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":35,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":35,"
                + "\\\\\\\"target\\\\\\\":36,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":36,"
                + "\\\\\\\"target\\\\\\\":38,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":38,\\\\\\\"target\\\\\\\":39,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":39,\\\\\\\"target\\\\\\\":40,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":41,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":41,"
                + "\\\\\\\"target\\\\\\\":42,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":23,"
                + "\\\\\\\"target\\\\\\\":43,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":43,"
                + "\\\\\\\"target\\\\\\\":44,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":44,"
                + "\\\\\\\"target\\\\\\\":45,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":46,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":46,"
                + "\\\\\\\"target\\\\\\\":47,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":48,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":48,"
                + "\\\\\\\"target\\\\\\\":49,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":49,"
                + "\\\\\\\"target\\\\\\\":51,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":51,\\\\\\\"target\\\\\\\":52,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":52,\\\\\\\"target\\\\\\\":53,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":53,"
                + "\\\\\\\"target\\\\\\\":54,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":54,"
                + "\\\\\\\"target\\\\\\\":55,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":56,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":56,"
                + "\\\\\\\"target\\\\\\\":57,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":57,"
                + "\\\\\\\"target\\\\\\\":59,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":59,\\\\\\\"target\\\\\\\":60,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":60,\\\\\\\"target\\\\\\\":61,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":61,"
                + "\\\\\\\"target\\\\\\\":62,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":62,"
                + "\\\\\\\"target\\\\\\\":63,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":64,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":64,"
                + "\\\\\\\"target\\\\\\\":65,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":65,"
                + "\\\\\\\"target\\\\\\\":66,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":67,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":67,"
                + "\\\\\\\"target\\\\\\\":68,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":69,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":69,"
                + "\\\\\\\"target\\\\\\\":70,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":71,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":71,"
                + "\\\\\\\"target\\\\\\\":72,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":73,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":73,"
                + "\\\\\\\"target\\\\\\\":74,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"}],"
                + "\\\\\\\"vertices\\\\\\\":{\\\\\\\"40384a6339eef72bc6b9656f8cf2bcaf\\\\\\\":[28,29,30,31,32,33],"
                + "\\\\\\\"fe817019d2e9b45766686a93ee230ef0\\\\\\\":[38,39,40,41,42,67,68,71,72],"
                + "\\\\\\\"a408d3633275bd7d9d07fe871f1a4f35\\\\\\\":[27],"
                + "\\\\\\\"5d70c8a84279c549c09bb6a963637464\\\\\\\":[22],"
                + "\\\\\\\"9fef778f6d856a2525f7f06f6f32e5c4\\\\\\\":[18,19,20],"
                + "\\\\\\\"693f85ee17f8d43483be5737897dafc0\\\\\\\":[51,52,53,54,55],"
                + "\\\\\\\"8fe87100eddafceda9257ebf607aa098\\\\\\\":[10,11,12,14,15,16,34,35,36,48,49,56,57],"
                + "\\\\\\\"5edb50593c2d5e4cd49b5a1427720bd1\\\\\\\":[23,24,25,43,44,45,46,47,64,65,66,69,70,73,74],"
                + "\\\\\\\"788a2d89ac307fe9cb2d3a97e995b244\\\\\\\":[9],"
                + "\\\\\\\"717c7b8afebbfb7137f6f0f99beb2a94\\\\\\\":[1],"
                + "\\\\\\\"0e8289f2bf927649dd2511bbc2bb6759\\\\\\\":[2,3,4,5,6,7,8],"
                + "\\\\\\\"9a03d13f8211c1b0f332bbaf15991fd7\\\\\\\":[59,60,61,62,63]}}\\\"},"
                + "\\\"resourceSettingMode\\\":\\\"EXPERT\\\"}\"}");
        task.setAlgParam(
            "{\"waterMarker\":30000,\"windowSize\":15,\"support\":50,\"rsupport\":5,\"cutLength\":50000,"
                + "\"cutLines\":5000,\"weightThreshold\":0.04,\"preprocess\":true,\"trimMode\":\"top\","
                + "\"threshold\":10000,\"regExps\":{\"customer\":[\"102;98;95;96;99;93;92;91;90;65;89;64;94;97;100;"
                + "45;43;"
                + "4;10;11;12;17;19;22;24;28;30;31;33;41;42;58;63;2;62\"],\"regMap\":{}}}");
        task.setOutputSinkParam(
            "{\"sinkSls\":true,\"sinkPretreat\":false,\"sinkDetail\":{},"
            + "\"sourceLogStore\":\"xdragon-metric-application\",\"sourceProject\":\"log-cluster\"}");
        task.setStatus(Integer.valueOf(1).byteValue());

        // create task fail
        ResponseParam createResponse = new ResponseParam();
        createResponse.setPlatformId("xxxx");
        createResponse.setRequestId("req_id");
        createResponse.setErrorCode(TaskManagerErrorCode.CMD_FAILED);
        createResponse.setErrorMessage("create failed");
        when(vvpTaskManager.createMatchTask(any(CreateRequestParam.class))).thenReturn(createResponse);
        LogClusterTask logClusterTask = new LogClusterTask();
        logClusterTask.setTaskName("tm");
        boolean ret = logFlinkTaskService.startMatchTask(logClusterTask, task, new LogTaskStartStrategy());
        Assert.assertFalse(ret);

        // start success
        task.setPlatformId("xxxxx");
        InstanceResponseParam responseSucc = new InstanceResponseParam("req_id", TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.startTask(any())).thenReturn(responseSucc);
        ret = logFlinkTaskService.startMatchTask(logClusterTask, task, new LogTaskStartStrategy());
        Assert.assertTrue(ret);

        // start fail
        InstanceResponseParam responseFail = new InstanceResponseParam("req_id", TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.startTask(any())).thenReturn(responseFail);
        ret = logFlinkTaskService.startMatchTask(logClusterTask, task, new LogTaskStartStrategy());
        Assert.assertFalse(ret);
    }

    @Test
    public void stopMatchTask() {
        LogClusterConfigRegion task = new LogClusterConfigRegion();
        task.setTaskId(1L);
        task.setPlatformId("xxxxx");
        task.setInputSourceParam(
            "{\"sourceType\":\"SLS\",\"accountName\":\"newbie_ecs\",\"regionType\":2,\"endpoint\":\"cn-hangzhou-corp"
                + ".sls.aliyuncs.com\",\"project\":\"ecs-xunjian\",\"logStore\":\"cloudops_service_error_v2\","
                + "\"consumerGroup\":\"log_cluster_consumer\",\"logField\":\"message\",\"filterField\":\"level\","
                + "\"filterString\":\"ERROR\",\"extFields\":\"__source__,__tag__:__path__\",\"filterGamma\":true,"
                + "\"filterStatus\":true,\"sinkParam\":{\"sinkSls\":true,\"sinkPretreat\":false,\"sinkDetail\":{}}}");
        task.setPlatformParam(
            "{\"cpu\":4.0,\"memory\":\"60Gi\",\"parallelism\":12,"
                + "\"last_run_resource\":\"{\\\"expertResourceSetting\\\":{\\\"jobmanagerResourceSettingSpec"
                + "\\\":{\\\"cpu"
                + "\\\":1.0,\\\"memory\\\":\\\"4GiB\\\"},"
                + "\\\"resourcePlan\\\":\\\"{\\\\\\\"ssgProfiles\\\\\\\":[{\\\\\\\"name\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.07,\\\\\\\"heap\\\\\\\":\\\\\\\"2 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"944 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"3\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"1 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"64 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"432 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"48 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,\\\\\\\"heap\\\\\\\":\\\\\\\"2 "
                + "gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"992 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"cpu\\\\\\\":8.0,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"32 gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 mb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}}],"
                + "\\\\\\\"nodes\\\\\\\":[{\\\\\\\"id\\\\\\\":1,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecTableSourceScan\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Source: "
                + "sls_input_table[26004]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":8,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":2,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26005]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":3,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecWatermarkAssigner\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"WatermarkAssigner[26006]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":4,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26007]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":5,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26008]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":6,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26009]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":7,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26010]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":8,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26011]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":9,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26012]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"parallelism\\\\\\\":16,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":10,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26013]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":11,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26014]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":12,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26015]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":14,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26017]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":15,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26018]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":16,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26019]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":18,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26021]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":19,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26022]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":20,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26023]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":22,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26025]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":23,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26026]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":24,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26027]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":25,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26028]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":27,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Join[26030]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1},"
                + "\\\\\\\"state\\\\\\\":[{\\\\\\\"index\\\\\\\":0,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"leftState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true},"
                + "{\\\\\\\"index\\\\\\\":1,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"rightState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true}]},"
                + "{\\\\\\\"id\\\\\\\":28,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26031]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":29,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26032]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":30,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26033]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":31,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26034]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":32,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26035]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":33,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "remain_table[26036]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":128,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":34,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26037]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":35,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26038]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":36,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26039]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":38,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26041]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":39,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26042]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":40,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26043]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":41,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26044]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":42,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26045]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":43,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26046]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":44,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26047]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":45,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26048]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":46,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26049]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":47,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26050]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":48,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26051]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":49,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26052]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":51,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26054]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":52,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26055]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":53,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26056]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":54,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26057]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":55,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26058]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":56,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26059]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":57,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26060]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":59,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26062]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":60,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26063]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":61,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26064]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":62,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26065]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":63,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26066]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":64,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26067]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":65,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26068]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":66,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26069]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":67,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26070]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":68,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26071]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":69,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26072]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":70,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26073]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":71,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26074]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":72,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26075]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":73,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26076]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":74,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26077]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}}],\\\\\\\"edges\\\\\\\":[{\\\\\\\"source\\\\\\\":1,"
                + "\\\\\\\"target\\\\\\\":2,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":2,"
                + "\\\\\\\"target\\\\\\\":3,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":3,"
                + "\\\\\\\"target\\\\\\\":4,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":4,\\\\\\\"target\\\\\\\":5,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":5,"
                + "\\\\\\\"target\\\\\\\":6,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":6,\\\\\\\"target\\\\\\\":7,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":7,"
                + "\\\\\\\"target\\\\\\\":8,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":8,\\\\\\\"target\\\\\\\":9,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":9,"
                + "\\\\\\\"target\\\\\\\":10,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":11,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":12,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":14,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":14,"
                + "\\\\\\\"target\\\\\\\":15,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":15,"
                + "\\\\\\\"target\\\\\\\":16,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":16,"
                + "\\\\\\\"target\\\\\\\":18,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":18,\\\\\\\"target\\\\\\\":19,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":19,\\\\\\\"target\\\\\\\":20,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":20,"
                + "\\\\\\\"target\\\\\\\":22,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":22,"
                + "\\\\\\\"target\\\\\\\":23,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":23,\\\\\\\"target\\\\\\\":24,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":24,"
                + "\\\\\\\"target\\\\\\\":25,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":12,"
                + "\\\\\\\"target\\\\\\\":27,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":25,"
                + "\\\\\\\"target\\\\\\\":27,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":27,\\\\\\\"target\\\\\\\":28,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":28,"
                + "\\\\\\\"target\\\\\\\":29,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":29,"
                + "\\\\\\\"target\\\\\\\":30,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":30,"
                + "\\\\\\\"target\\\\\\\":31,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":31,"
                + "\\\\\\\"target\\\\\\\":32,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":32,"
                + "\\\\\\\"target\\\\\\\":33,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":34,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":35,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":35,"
                + "\\\\\\\"target\\\\\\\":36,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":36,"
                + "\\\\\\\"target\\\\\\\":38,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":38,\\\\\\\"target\\\\\\\":39,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":39,\\\\\\\"target\\\\\\\":40,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":41,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":41,"
                + "\\\\\\\"target\\\\\\\":42,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":23,"
                + "\\\\\\\"target\\\\\\\":43,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":43,"
                + "\\\\\\\"target\\\\\\\":44,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":44,"
                + "\\\\\\\"target\\\\\\\":45,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":46,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":46,"
                + "\\\\\\\"target\\\\\\\":47,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":48,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":48,"
                + "\\\\\\\"target\\\\\\\":49,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":49,"
                + "\\\\\\\"target\\\\\\\":51,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":51,\\\\\\\"target\\\\\\\":52,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":52,\\\\\\\"target\\\\\\\":53,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":53,"
                + "\\\\\\\"target\\\\\\\":54,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":54,"
                + "\\\\\\\"target\\\\\\\":55,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":56,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":56,"
                + "\\\\\\\"target\\\\\\\":57,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":57,"
                + "\\\\\\\"target\\\\\\\":59,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":59,\\\\\\\"target\\\\\\\":60,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":60,\\\\\\\"target\\\\\\\":61,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":61,"
                + "\\\\\\\"target\\\\\\\":62,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":62,"
                + "\\\\\\\"target\\\\\\\":63,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":64,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":64,"
                + "\\\\\\\"target\\\\\\\":65,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":65,"
                + "\\\\\\\"target\\\\\\\":66,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":67,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":67,"
                + "\\\\\\\"target\\\\\\\":68,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":69,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":69,"
                + "\\\\\\\"target\\\\\\\":70,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":71,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":71,"
                + "\\\\\\\"target\\\\\\\":72,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":73,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":73,"
                + "\\\\\\\"target\\\\\\\":74,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"}],"
                + "\\\\\\\"vertices\\\\\\\":{\\\\\\\"40384a6339eef72bc6b9656f8cf2bcaf\\\\\\\":[28,29,30,31,32,33],"
                + "\\\\\\\"fe817019d2e9b45766686a93ee230ef0\\\\\\\":[38,39,40,41,42,67,68,71,72],"
                + "\\\\\\\"a408d3633275bd7d9d07fe871f1a4f35\\\\\\\":[27],"
                + "\\\\\\\"5d70c8a84279c549c09bb6a963637464\\\\\\\":[22],"
                + "\\\\\\\"9fef778f6d856a2525f7f06f6f32e5c4\\\\\\\":[18,19,20],"
                + "\\\\\\\"693f85ee17f8d43483be5737897dafc0\\\\\\\":[51,52,53,54,55],"
                + "\\\\\\\"8fe87100eddafceda9257ebf607aa098\\\\\\\":[10,11,12,14,15,16,34,35,36,48,49,56,57],"
                + "\\\\\\\"5edb50593c2d5e4cd49b5a1427720bd1\\\\\\\":[23,24,25,43,44,45,46,47,64,65,66,69,70,73,74],"
                + "\\\\\\\"788a2d89ac307fe9cb2d3a97e995b244\\\\\\\":[9],"
                + "\\\\\\\"717c7b8afebbfb7137f6f0f99beb2a94\\\\\\\":[1],"
                + "\\\\\\\"0e8289f2bf927649dd2511bbc2bb6759\\\\\\\":[2,3,4,5,6,7,8],"
                + "\\\\\\\"9a03d13f8211c1b0f332bbaf15991fd7\\\\\\\":[59,60,61,62,63]}}\\\"},"
                + "\\\"resourceSettingMode\\\":\\\"EXPERT\\\"}\"}");
        task.setAlgParam(
            "{\"waterMarker\":30000,\"windowSize\":15,\"support\":50,\"rsupport\":5,\"cutLength\":50000,"
                + "\"cutLines\":5000,\"weightThreshold\":0.04,\"preprocess\":true,\"trimMode\":\"top\","
                + "\"threshold\":10000,\"regExps\":{\"customer\":[\"102;98;95;96;99;93;92;91;90;65;89;64;94;97;100;"
                + "45;43;"
                + "4;10;11;12;17;19;22;24;28;30;31;33;41;42;58;63;2;62\"],\"regMap\":{}}}");
        task.setStatus(Integer.valueOf(1).byteValue());

        // stop success
        InstanceResponseParam responseSucc = new InstanceResponseParam("req_id", TaskManagerErrorCode.SUCCESS);
        responseSucc.setStreamingResourceSetting("{}");
        when(vvpTaskManager.stopTask(any())).thenReturn(responseSucc);
        boolean ret = logFlinkTaskService.stopMatchTask(task);
        Assert.assertTrue(ret);

        // stop success because of state terminated
        InstanceResponseParam responseFail = new InstanceResponseParam("req_id", TaskManagerErrorCode.CMD_FAILED);
        responseFail.setErrorCode(TaskManagerErrorCode.STATE_ERROR);
        responseFail.setCurrentState(TaskState.TERMINATED);
        when(vvpTaskManager.stopTask(any())).thenReturn(responseFail);
        ret = logFlinkTaskService.stopMatchTask(task);
        Assert.assertTrue(ret);

        // stop failed
        responseFail.setErrorCode(TaskManagerErrorCode.CMD_FAILED);
        responseFail.setErrorMessage("stop task cmd failed");
        ret = logFlinkTaskService.stopMatchTask(task);
        Assert.assertFalse(ret);
    }

    @Test
    public void deleteMatchTask() {
        LogClusterConfigRegion task = new LogClusterConfigRegion();
        task.setTaskId(1L);
        task.setPlatformId("xxxxx");
        task.setStatus(Integer.valueOf(1).byteValue());

        // delete success
        ResponseParam responseSucc = new ResponseParam("req_id", TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.deleteTask(any(RequestParam.class))).thenReturn(responseSucc);
        boolean ret = logFlinkTaskService.deleteMatchTask(task);
        Assert.assertTrue(ret);

        // delete success because of task not exist
        ResponseParam responseNotExist = new ResponseParam("req_id", TaskManagerErrorCode.TASK_NOT_EXIST);
        when(vvpTaskManager.deleteTask(any())).thenReturn(responseNotExist);
        ret = logFlinkTaskService.deleteMatchTask(task);
        Assert.assertTrue(ret);

        // delete failed
        ResponseParam responseFail = new ResponseParam("req_id", TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.deleteTask(any())).thenReturn(responseFail);
        ret = logFlinkTaskService.deleteMatchTask(task);
        Assert.assertFalse(ret);
    }

    @Test
    public void createClusterTask() {
        LogClusterConfigRegion task = new LogClusterConfigRegion();
        task.setTaskId(1L);
        task.setInputSourceParam(
            "{\"sourceType\":\"SLS\",\"accountName\":\"newbie_ecs\",\"regionType\":2,\"endpoint\":\"cn-hangzhou-corp"
                + ".sls.aliyuncs.com\",\"project\":\"ecs-xunjian\",\"logStore\":\"cloudops_service_error_v2\","
                + "\"consumerGroup\":\"log_cluster_consumer\",\"logField\":\"message\",\"filterField\":\"level\","
                + "\"filterString\":\"ERROR\",\"extFields\":\"__source__,__tag__:__path__\",\"filterGamma\":true,"
                + "\"filterStatus\":true,\"sinkParam\":{\"sinkSls\":true,\"sinkPretreat\":false,\"sinkDetail\":{}}}");
        task.setPlatformParam(
            "{\"cpu\":4.0,\"memory\":\"60Gi\",\"parallelism\":12,"
                + "\"last_run_resource\":\"{\\\"expertResourceSetting\\\":{\\\"jobmanagerResourceSettingSpec"
                + "\\\":{\\\"cpu"
                + "\\\":1.0,\\\"memory\\\":\\\"4GiB\\\"},"
                + "\\\"resourcePlan\\\":\\\"{\\\\\\\"ssgProfiles\\\\\\\":[{\\\\\\\"name\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.07,\\\\\\\"heap\\\\\\\":\\\\\\\"2 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"944 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"3\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"1 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"64 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"432 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"48 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,\\\\\\\"heap\\\\\\\":\\\\\\\"2 "
                + "gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"992 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"cpu\\\\\\\":8.0,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"32 gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 mb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}}],"
                + "\\\\\\\"nodes\\\\\\\":[{\\\\\\\"id\\\\\\\":1,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecTableSourceScan\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Source: "
                + "sls_input_table[26004]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":8,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":2,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26005]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":3,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecWatermarkAssigner\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"WatermarkAssigner[26006]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":4,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26007]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":5,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26008]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":6,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26009]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":7,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26010]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":8,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26011]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":9,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26012]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"parallelism\\\\\\\":16,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":10,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26013]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":11,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26014]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":12,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26015]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":14,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26017]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":15,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26018]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":16,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26019]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":18,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26021]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":19,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26022]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":20,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26023]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":22,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26025]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":23,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26026]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":24,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26027]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":25,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26028]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":27,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Join[26030]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1},"
                + "\\\\\\\"state\\\\\\\":[{\\\\\\\"index\\\\\\\":0,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"leftState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true},"
                + "{\\\\\\\"index\\\\\\\":1,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"rightState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true}]},"
                + "{\\\\\\\"id\\\\\\\":28,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26031]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":29,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26032]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":30,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26033]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":31,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26034]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":32,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26035]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":33,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "remain_table[26036]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":128,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":34,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26037]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":35,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26038]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":36,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26039]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":38,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26041]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":39,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26042]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":40,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26043]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":41,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26044]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":42,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26045]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":43,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26046]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":44,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26047]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":45,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26048]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":46,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26049]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":47,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26050]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":48,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26051]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":49,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26052]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":51,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26054]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":52,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26055]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":53,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26056]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":54,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26057]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":55,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26058]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":56,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26059]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":57,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26060]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":59,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26062]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":60,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26063]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":61,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26064]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":62,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26065]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":63,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26066]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":64,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26067]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":65,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26068]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":66,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26069]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":67,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26070]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":68,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26071]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":69,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26072]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":70,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26073]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":71,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26074]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":72,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26075]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":73,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26076]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":74,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26077]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}}],\\\\\\\"edges\\\\\\\":[{\\\\\\\"source\\\\\\\":1,"
                + "\\\\\\\"target\\\\\\\":2,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":2,"
                + "\\\\\\\"target\\\\\\\":3,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":3,"
                + "\\\\\\\"target\\\\\\\":4,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":4,\\\\\\\"target\\\\\\\":5,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":5,"
                + "\\\\\\\"target\\\\\\\":6,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":6,\\\\\\\"target\\\\\\\":7,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":7,"
                + "\\\\\\\"target\\\\\\\":8,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":8,\\\\\\\"target\\\\\\\":9,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":9,"
                + "\\\\\\\"target\\\\\\\":10,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":11,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":12,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":14,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":14,"
                + "\\\\\\\"target\\\\\\\":15,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":15,"
                + "\\\\\\\"target\\\\\\\":16,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":16,"
                + "\\\\\\\"target\\\\\\\":18,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":18,\\\\\\\"target\\\\\\\":19,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":19,\\\\\\\"target\\\\\\\":20,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":20,"
                + "\\\\\\\"target\\\\\\\":22,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":22,"
                + "\\\\\\\"target\\\\\\\":23,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":23,\\\\\\\"target\\\\\\\":24,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":24,"
                + "\\\\\\\"target\\\\\\\":25,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":12,"
                + "\\\\\\\"target\\\\\\\":27,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":25,"
                + "\\\\\\\"target\\\\\\\":27,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":27,\\\\\\\"target\\\\\\\":28,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":28,"
                + "\\\\\\\"target\\\\\\\":29,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":29,"
                + "\\\\\\\"target\\\\\\\":30,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":30,"
                + "\\\\\\\"target\\\\\\\":31,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":31,"
                + "\\\\\\\"target\\\\\\\":32,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":32,"
                + "\\\\\\\"target\\\\\\\":33,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":34,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":35,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":35,"
                + "\\\\\\\"target\\\\\\\":36,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":36,"
                + "\\\\\\\"target\\\\\\\":38,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":38,\\\\\\\"target\\\\\\\":39,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":39,\\\\\\\"target\\\\\\\":40,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":41,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":41,"
                + "\\\\\\\"target\\\\\\\":42,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":23,"
                + "\\\\\\\"target\\\\\\\":43,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":43,"
                + "\\\\\\\"target\\\\\\\":44,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":44,"
                + "\\\\\\\"target\\\\\\\":45,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":46,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":46,"
                + "\\\\\\\"target\\\\\\\":47,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":48,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":48,"
                + "\\\\\\\"target\\\\\\\":49,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":49,"
                + "\\\\\\\"target\\\\\\\":51,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":51,\\\\\\\"target\\\\\\\":52,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":52,\\\\\\\"target\\\\\\\":53,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":53,"
                + "\\\\\\\"target\\\\\\\":54,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":54,"
                + "\\\\\\\"target\\\\\\\":55,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":56,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":56,"
                + "\\\\\\\"target\\\\\\\":57,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":57,"
                + "\\\\\\\"target\\\\\\\":59,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":59,\\\\\\\"target\\\\\\\":60,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":60,\\\\\\\"target\\\\\\\":61,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":61,"
                + "\\\\\\\"target\\\\\\\":62,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":62,"
                + "\\\\\\\"target\\\\\\\":63,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":64,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":64,"
                + "\\\\\\\"target\\\\\\\":65,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":65,"
                + "\\\\\\\"target\\\\\\\":66,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":67,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":67,"
                + "\\\\\\\"target\\\\\\\":68,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":69,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":69,"
                + "\\\\\\\"target\\\\\\\":70,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":71,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":71,"
                + "\\\\\\\"target\\\\\\\":72,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":73,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":73,"
                + "\\\\\\\"target\\\\\\\":74,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"}],"
                + "\\\\\\\"vertices\\\\\\\":{\\\\\\\"40384a6339eef72bc6b9656f8cf2bcaf\\\\\\\":[28,29,30,31,32,33],"
                + "\\\\\\\"fe817019d2e9b45766686a93ee230ef0\\\\\\\":[38,39,40,41,42,67,68,71,72],"
                + "\\\\\\\"a408d3633275bd7d9d07fe871f1a4f35\\\\\\\":[27],"
                + "\\\\\\\"5d70c8a84279c549c09bb6a963637464\\\\\\\":[22],"
                + "\\\\\\\"9fef778f6d856a2525f7f06f6f32e5c4\\\\\\\":[18,19,20],"
                + "\\\\\\\"693f85ee17f8d43483be5737897dafc0\\\\\\\":[51,52,53,54,55],"
                + "\\\\\\\"8fe87100eddafceda9257ebf607aa098\\\\\\\":[10,11,12,14,15,16,34,35,36,48,49,56,57],"
                + "\\\\\\\"5edb50593c2d5e4cd49b5a1427720bd1\\\\\\\":[23,24,25,43,44,45,46,47,64,65,66,69,70,73,74],"
                + "\\\\\\\"788a2d89ac307fe9cb2d3a97e995b244\\\\\\\":[9],"
                + "\\\\\\\"717c7b8afebbfb7137f6f0f99beb2a94\\\\\\\":[1],"
                + "\\\\\\\"0e8289f2bf927649dd2511bbc2bb6759\\\\\\\":[2,3,4,5,6,7,8],"
                + "\\\\\\\"9a03d13f8211c1b0f332bbaf15991fd7\\\\\\\":[59,60,61,62,63]}}\\\"},"
                + "\\\"resourceSettingMode\\\":\\\"EXPERT\\\"}\"}");
        task.setAlgParam(
            "{\"waterMarker\":30000,\"windowSize\":15,\"support\":50,\"rsupport\":5,\"cutLength\":50000,"
                + "\"cutLines\":5000,\"weightThreshold\":0.04,\"preprocess\":true,\"trimMode\":\"top\","
                + "\"threshold\":10000,\"regExps\":{\"customer\":[\"102;98;95;96;99;93;92;91;90;65;89;64;94;97;100;"
                + "45;43;"
                + "4;10;11;12;17;19;22;24;28;30;31;33;41;42;58;63;2;62\"],\"regMap\":{}}}");
        task.setOutputSinkParam(
            "{\"sinkSls\":true,\"sinkPretreat\":false,\"sinkDetail\":{},"
            + "\"sourceLogStore\":\"xdragon-metric-application\",\"sourceProject\":\"log-cluster\"}");
        when(configService.loadSlsUsers()).thenReturn(new HashMap<>());

        ResponseParam response = new ResponseParam();
        response.setPlatformId("xxxx");
        response.setRequestId("req_id");
        response.setErrorCode(TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.createClusterTask(any(CreateRequestParam.class))).thenReturn(response);
        LogClusterTask logClusterTask = new LogClusterTask();
        logClusterTask.setTaskName("tm");
        LogClusterConfigRegion configRegion = logFlinkTaskService.createClusterTask(logClusterTask, task, true);
        boolean ret = configRegion.getPlatformId() != null;
        Assert.assertTrue(ret);

        task.setPlatformId(null);
        response.setErrorCode(TaskManagerErrorCode.CMD_FAILED);
        response.setErrorMessage("create task command failed");
        configRegion = logFlinkTaskService.createClusterTask(logClusterTask, task, true);
        ret = configRegion.getPlatformId() != null;
        Assert.assertFalse(ret);
    }

    @Test
    public void updateClusterTask() {
        LogClusterConfigRegion task = new LogClusterConfigRegion();
        task.setTaskId(1L);
        task.setPlatformId("xxxx");
        task.setInputSourceParam(
            "{\"sourceType\":\"SLS\",\"accountName\":\"newbie_ecs\",\"regionType\":2,\"endpoint\":\"cn-hangzhou-corp"
                + ".sls.aliyuncs.com\",\"project\":\"ecs-xunjian\",\"logStore\":\"cloudops_service_error_v2\","
                + "\"consumerGroup\":\"log_cluster_consumer\",\"logField\":\"message\",\"filterField\":\"level\","
                + "\"filterString\":\"ERROR\",\"extFields\":\"__source__,__tag__:__path__\",\"filterGamma\":true,"
                + "\"filterStatus\":true,\"sinkParam\":{\"sinkSls\":true,\"sinkPretreat\":false,\"sinkDetail\":{}}}");
        task.setPlatformParam(
            "{\"cpu\":4.0,\"memory\":\"60Gi\",\"parallelism\":12,"
                + "\"last_run_resource\":\"{\\\"expertResourceSetting\\\":{\\\"jobmanagerResourceSettingSpec"
                + "\\\":{\\\"cpu"
                + "\\\":1.0,\\\"memory\\\":\\\"4GiB\\\"},"
                + "\\\"resourcePlan\\\":\\\"{\\\\\\\"ssgProfiles\\\\\\\":[{\\\\\\\"name\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.07,\\\\\\\"heap\\\\\\\":\\\\\\\"2 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"944 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"3\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"1 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"64 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"432 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"48 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,\\\\\\\"heap\\\\\\\":\\\\\\\"2 "
                + "gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"992 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"cpu\\\\\\\":8.0,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"32 gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 mb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}}],"
                + "\\\\\\\"nodes\\\\\\\":[{\\\\\\\"id\\\\\\\":1,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecTableSourceScan\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Source: "
                + "sls_input_table[26004]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":8,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":2,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26005]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":3,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecWatermarkAssigner\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"WatermarkAssigner[26006]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":4,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26007]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":5,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26008]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":6,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26009]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":7,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26010]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":8,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26011]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":9,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26012]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"parallelism\\\\\\\":16,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":10,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26013]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":11,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26014]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":12,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26015]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":14,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26017]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":15,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26018]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":16,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26019]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":18,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26021]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":19,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26022]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":20,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26023]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":22,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26025]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":23,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26026]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":24,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26027]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":25,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26028]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":27,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Join[26030]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1},"
                + "\\\\\\\"state\\\\\\\":[{\\\\\\\"index\\\\\\\":0,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"leftState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true},"
                + "{\\\\\\\"index\\\\\\\":1,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"rightState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true}]},"
                + "{\\\\\\\"id\\\\\\\":28,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26031]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":29,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26032]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":30,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26033]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":31,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26034]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":32,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26035]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":33,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "remain_table[26036]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":128,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":34,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26037]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":35,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26038]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":36,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26039]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":38,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26041]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":39,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26042]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":40,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26043]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":41,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26044]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":42,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26045]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":43,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26046]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":44,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26047]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":45,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26048]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":46,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26049]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":47,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26050]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":48,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26051]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":49,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26052]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":51,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26054]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":52,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26055]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":53,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26056]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":54,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26057]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":55,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26058]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":56,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26059]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":57,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26060]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":59,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26062]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":60,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26063]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":61,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26064]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":62,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26065]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":63,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26066]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":64,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26067]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":65,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26068]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":66,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26069]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":67,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26070]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":68,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26071]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":69,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26072]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":70,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26073]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":71,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26074]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":72,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26075]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":73,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26076]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":74,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26077]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}}],\\\\\\\"edges\\\\\\\":[{\\\\\\\"source\\\\\\\":1,"
                + "\\\\\\\"target\\\\\\\":2,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":2,"
                + "\\\\\\\"target\\\\\\\":3,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":3,"
                + "\\\\\\\"target\\\\\\\":4,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":4,\\\\\\\"target\\\\\\\":5,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":5,"
                + "\\\\\\\"target\\\\\\\":6,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":6,\\\\\\\"target\\\\\\\":7,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":7,"
                + "\\\\\\\"target\\\\\\\":8,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":8,\\\\\\\"target\\\\\\\":9,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":9,"
                + "\\\\\\\"target\\\\\\\":10,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":11,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":12,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":14,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":14,"
                + "\\\\\\\"target\\\\\\\":15,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":15,"
                + "\\\\\\\"target\\\\\\\":16,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":16,"
                + "\\\\\\\"target\\\\\\\":18,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":18,\\\\\\\"target\\\\\\\":19,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":19,\\\\\\\"target\\\\\\\":20,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":20,"
                + "\\\\\\\"target\\\\\\\":22,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":22,"
                + "\\\\\\\"target\\\\\\\":23,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":23,\\\\\\\"target\\\\\\\":24,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":24,"
                + "\\\\\\\"target\\\\\\\":25,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":12,"
                + "\\\\\\\"target\\\\\\\":27,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":25,"
                + "\\\\\\\"target\\\\\\\":27,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":27,\\\\\\\"target\\\\\\\":28,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":28,"
                + "\\\\\\\"target\\\\\\\":29,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":29,"
                + "\\\\\\\"target\\\\\\\":30,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":30,"
                + "\\\\\\\"target\\\\\\\":31,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":31,"
                + "\\\\\\\"target\\\\\\\":32,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":32,"
                + "\\\\\\\"target\\\\\\\":33,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":34,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":35,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":35,"
                + "\\\\\\\"target\\\\\\\":36,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":36,"
                + "\\\\\\\"target\\\\\\\":38,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":38,\\\\\\\"target\\\\\\\":39,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":39,\\\\\\\"target\\\\\\\":40,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":41,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":41,"
                + "\\\\\\\"target\\\\\\\":42,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":23,"
                + "\\\\\\\"target\\\\\\\":43,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":43,"
                + "\\\\\\\"target\\\\\\\":44,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":44,"
                + "\\\\\\\"target\\\\\\\":45,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":46,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":46,"
                + "\\\\\\\"target\\\\\\\":47,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":48,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":48,"
                + "\\\\\\\"target\\\\\\\":49,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":49,"
                + "\\\\\\\"target\\\\\\\":51,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":51,\\\\\\\"target\\\\\\\":52,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":52,\\\\\\\"target\\\\\\\":53,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":53,"
                + "\\\\\\\"target\\\\\\\":54,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":54,"
                + "\\\\\\\"target\\\\\\\":55,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":56,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":56,"
                + "\\\\\\\"target\\\\\\\":57,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":57,"
                + "\\\\\\\"target\\\\\\\":59,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":59,\\\\\\\"target\\\\\\\":60,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":60,\\\\\\\"target\\\\\\\":61,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":61,"
                + "\\\\\\\"target\\\\\\\":62,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":62,"
                + "\\\\\\\"target\\\\\\\":63,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":64,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":64,"
                + "\\\\\\\"target\\\\\\\":65,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":65,"
                + "\\\\\\\"target\\\\\\\":66,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":67,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":67,"
                + "\\\\\\\"target\\\\\\\":68,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":69,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":69,"
                + "\\\\\\\"target\\\\\\\":70,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":71,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":71,"
                + "\\\\\\\"target\\\\\\\":72,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":73,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":73,"
                + "\\\\\\\"target\\\\\\\":74,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"}],"
                + "\\\\\\\"vertices\\\\\\\":{\\\\\\\"40384a6339eef72bc6b9656f8cf2bcaf\\\\\\\":[28,29,30,31,32,33],"
                + "\\\\\\\"fe817019d2e9b45766686a93ee230ef0\\\\\\\":[38,39,40,41,42,67,68,71,72],"
                + "\\\\\\\"a408d3633275bd7d9d07fe871f1a4f35\\\\\\\":[27],"
                + "\\\\\\\"5d70c8a84279c549c09bb6a963637464\\\\\\\":[22],"
                + "\\\\\\\"9fef778f6d856a2525f7f06f6f32e5c4\\\\\\\":[18,19,20],"
                + "\\\\\\\"693f85ee17f8d43483be5737897dafc0\\\\\\\":[51,52,53,54,55],"
                + "\\\\\\\"8fe87100eddafceda9257ebf607aa098\\\\\\\":[10,11,12,14,15,16,34,35,36,48,49,56,57],"
                + "\\\\\\\"5edb50593c2d5e4cd49b5a1427720bd1\\\\\\\":[23,24,25,43,44,45,46,47,64,65,66,69,70,73,74],"
                + "\\\\\\\"788a2d89ac307fe9cb2d3a97e995b244\\\\\\\":[9],"
                + "\\\\\\\"717c7b8afebbfb7137f6f0f99beb2a94\\\\\\\":[1],"
                + "\\\\\\\"0e8289f2bf927649dd2511bbc2bb6759\\\\\\\":[2,3,4,5,6,7,8],"
                + "\\\\\\\"9a03d13f8211c1b0f332bbaf15991fd7\\\\\\\":[59,60,61,62,63]}}\\\"},"
                + "\\\"resourceSettingMode\\\":\\\"EXPERT\\\"}\"}");
        task.setAlgParam(
            "{\"waterMarker\":30000,\"windowSize\":15,\"support\":50,\"rsupport\":5,\"cutLength\":50000,"
                + "\"cutLines\":5000,\"weightThreshold\":0.04,\"preprocess\":true,\"trimMode\":\"top\","
                + "\"threshold\":10000,\"regExps\":{\"customer\":[\"102;98;95;96;99;93;92;91;90;65;89;64;94;97;100;"
                + "45;43;"
                + "4;10;11;12;17;19;22;24;28;30;31;33;41;42;58;63;2;62\"],\"regMap\":{}}}");
        task.setOutputSinkParam(
            "{\"sinkSls\":true,\"sinkPretreat\":false,\"sinkDetail\":{},"
            + "\"sourceLogStore\":\"xdragon-metric-application\",\"sourceProject\":\"log-cluster\"}");
        task.setStatus(Integer.valueOf(6).byteValue());
        when(configService.loadSlsUsers()).thenReturn(new HashMap<>());

        // not updatable
        boolean ret = logFlinkTaskService.updateClusterTask(null, task, new LogTaskStartStrategy());
        Assert.assertFalse(ret);

        // update fail
        task.setStatus(Integer.valueOf(3).byteValue());
        ResponseParam responseFail = new ResponseParam();
        responseFail.setErrorCode(TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.updateClusterTask(any(UpdateRequestParam.class))).thenReturn(responseFail);
        ret = logFlinkTaskService.updateClusterTask(null, task, new LogTaskStartStrategy());
        Assert.assertFalse(ret);

        // update fail, stop fail
        ResponseParam responseUpdateSucc = new ResponseParam("req", TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.updateClusterTask(any())).thenReturn(responseUpdateSucc);

        InstanceResponseParam responseStopFail = new InstanceResponseParam("req", TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.stopTask(any())).thenReturn(responseStopFail);
        ret = logFlinkTaskService.updateClusterTask(null, task, new LogTaskStartStrategy());
        Assert.assertFalse(ret);

        // update fail, stop success, start fail
        InstanceResponseParam responseStopSucc = new InstanceResponseParam("req", TaskManagerErrorCode.SUCCESS);
        InstanceResponseParam responseStartFail = new InstanceResponseParam("req", TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.updateClusterTask(any())).thenReturn(responseUpdateSucc);
        when(vvpTaskManager.stopTask(any())).thenReturn(responseStopSucc);
        when(vvpTaskManager.startTask(any())).thenReturn(responseStartFail);
        ret = logFlinkTaskService.updateClusterTask(null, task, new LogTaskStartStrategy());
        Assert.assertFalse(ret);

        // update success, stop success, start success
        InstanceResponseParam responseStartSucc = new InstanceResponseParam("req", TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.updateClusterTask(any())).thenReturn(responseUpdateSucc);
        when(vvpTaskManager.stopTask(any())).thenReturn(responseStopSucc);
        when(vvpTaskManager.startTask(any())).thenReturn(responseStartSucc);
        ret = logFlinkTaskService.updateClusterTask(null, task, new LogTaskStartStrategy());
        Assert.assertTrue(ret);
    }

    @Test
    public void startClusterTask() {
        LogClusterConfigRegion task = new LogClusterConfigRegion();
        task.setTaskId(1L);
        task.setInputSourceParam(
            "{\"sourceType\":\"SLS\",\"accountName\":\"newbie_ecs\",\"regionType\":2,\"endpoint\":\"cn-hangzhou-corp"
                + ".sls.aliyuncs.com\",\"project\":\"ecs-xunjian\",\"logStore\":\"cloudops_service_error_v2\","
                + "\"consumerGroup\":\"log_cluster_consumer\",\"logField\":\"message\",\"filterField\":\"level\","
                + "\"filterString\":\"ERROR\",\"extFields\":\"__source__,__tag__:__path__\",\"filterGamma\":true,"
                + "\"filterStatus\":true,\"sinkParam\":{\"sinkSls\":true,\"sinkPretreat\":false,\"sinkDetail\":{}}}");
        task.setPlatformParam(
            "{\"cpu\":4.0,\"memory\":\"60Gi\",\"parallelism\":12,"
                + "\"last_run_resource\":\"{\\\"expertResourceSetting\\\":{\\\"jobmanagerResourceSettingSpec"
                + "\\\":{\\\"cpu"
                + "\\\":1.0,\\\"memory\\\":\\\"4GiB\\\"},"
                + "\\\"resourcePlan\\\":\\\"{\\\\\\\"ssgProfiles\\\\\\\":[{\\\\\\\"name\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.07,\\\\\\\"heap\\\\\\\":\\\\\\\"2 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"944 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"3\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"1 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"64 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"432 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"48 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,\\\\\\\"heap\\\\\\\":\\\\\\\"2 "
                + "gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"992 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"cpu\\\\\\\":8.0,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"32 gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 mb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}}],"
                + "\\\\\\\"nodes\\\\\\\":[{\\\\\\\"id\\\\\\\":1,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecTableSourceScan\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Source: "
                + "sls_input_table[26004]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":8,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":2,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26005]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":3,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecWatermarkAssigner\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"WatermarkAssigner[26006]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":4,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26007]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":5,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26008]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":6,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26009]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":7,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26010]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":8,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26011]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":9,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26012]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"parallelism\\\\\\\":16,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":10,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26013]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":11,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26014]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":12,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26015]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":14,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26017]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":15,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26018]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":16,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26019]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":18,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26021]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":19,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26022]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":20,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26023]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":22,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26025]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":23,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26026]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":24,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26027]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":25,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26028]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":27,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Join[26030]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1},"
                + "\\\\\\\"state\\\\\\\":[{\\\\\\\"index\\\\\\\":0,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"leftState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true},"
                + "{\\\\\\\"index\\\\\\\":1,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"rightState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true}]},"
                + "{\\\\\\\"id\\\\\\\":28,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26031]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":29,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26032]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":30,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26033]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":31,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26034]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":32,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26035]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":33,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "remain_table[26036]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":128,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":34,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26037]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":35,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26038]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":36,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26039]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":38,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26041]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":39,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26042]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":40,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26043]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":41,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26044]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":42,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26045]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":43,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26046]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":44,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26047]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":45,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26048]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":46,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26049]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":47,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26050]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":48,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26051]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":49,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26052]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":51,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26054]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":52,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26055]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":53,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26056]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":54,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26057]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":55,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26058]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":56,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26059]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":57,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26060]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":59,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26062]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":60,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26063]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":61,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26064]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":62,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26065]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":63,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26066]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":64,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26067]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":65,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26068]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":66,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26069]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":67,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26070]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":68,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26071]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":69,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26072]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":70,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26073]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":71,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26074]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":72,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26075]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":73,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26076]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":74,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26077]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}}],\\\\\\\"edges\\\\\\\":[{\\\\\\\"source\\\\\\\":1,"
                + "\\\\\\\"target\\\\\\\":2,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":2,"
                + "\\\\\\\"target\\\\\\\":3,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":3,"
                + "\\\\\\\"target\\\\\\\":4,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":4,\\\\\\\"target\\\\\\\":5,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":5,"
                + "\\\\\\\"target\\\\\\\":6,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":6,\\\\\\\"target\\\\\\\":7,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":7,"
                + "\\\\\\\"target\\\\\\\":8,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":8,\\\\\\\"target\\\\\\\":9,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":9,"
                + "\\\\\\\"target\\\\\\\":10,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":11,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":12,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":14,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":14,"
                + "\\\\\\\"target\\\\\\\":15,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":15,"
                + "\\\\\\\"target\\\\\\\":16,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":16,"
                + "\\\\\\\"target\\\\\\\":18,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":18,\\\\\\\"target\\\\\\\":19,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":19,\\\\\\\"target\\\\\\\":20,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":20,"
                + "\\\\\\\"target\\\\\\\":22,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":22,"
                + "\\\\\\\"target\\\\\\\":23,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":23,\\\\\\\"target\\\\\\\":24,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":24,"
                + "\\\\\\\"target\\\\\\\":25,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":12,"
                + "\\\\\\\"target\\\\\\\":27,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":25,"
                + "\\\\\\\"target\\\\\\\":27,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":27,\\\\\\\"target\\\\\\\":28,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":28,"
                + "\\\\\\\"target\\\\\\\":29,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":29,"
                + "\\\\\\\"target\\\\\\\":30,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":30,"
                + "\\\\\\\"target\\\\\\\":31,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":31,"
                + "\\\\\\\"target\\\\\\\":32,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":32,"
                + "\\\\\\\"target\\\\\\\":33,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":34,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":35,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":35,"
                + "\\\\\\\"target\\\\\\\":36,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":36,"
                + "\\\\\\\"target\\\\\\\":38,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":38,\\\\\\\"target\\\\\\\":39,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":39,\\\\\\\"target\\\\\\\":40,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":41,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":41,"
                + "\\\\\\\"target\\\\\\\":42,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":23,"
                + "\\\\\\\"target\\\\\\\":43,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":43,"
                + "\\\\\\\"target\\\\\\\":44,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":44,"
                + "\\\\\\\"target\\\\\\\":45,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":46,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":46,"
                + "\\\\\\\"target\\\\\\\":47,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":48,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":48,"
                + "\\\\\\\"target\\\\\\\":49,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":49,"
                + "\\\\\\\"target\\\\\\\":51,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":51,\\\\\\\"target\\\\\\\":52,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":52,\\\\\\\"target\\\\\\\":53,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":53,"
                + "\\\\\\\"target\\\\\\\":54,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":54,"
                + "\\\\\\\"target\\\\\\\":55,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":56,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":56,"
                + "\\\\\\\"target\\\\\\\":57,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":57,"
                + "\\\\\\\"target\\\\\\\":59,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":59,\\\\\\\"target\\\\\\\":60,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":60,\\\\\\\"target\\\\\\\":61,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":61,"
                + "\\\\\\\"target\\\\\\\":62,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":62,"
                + "\\\\\\\"target\\\\\\\":63,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":64,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":64,"
                + "\\\\\\\"target\\\\\\\":65,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":65,"
                + "\\\\\\\"target\\\\\\\":66,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":67,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":67,"
                + "\\\\\\\"target\\\\\\\":68,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":69,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":69,"
                + "\\\\\\\"target\\\\\\\":70,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":71,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":71,"
                + "\\\\\\\"target\\\\\\\":72,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":73,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":73,"
                + "\\\\\\\"target\\\\\\\":74,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"}],"
                + "\\\\\\\"vertices\\\\\\\":{\\\\\\\"40384a6339eef72bc6b9656f8cf2bcaf\\\\\\\":[28,29,30,31,32,33],"
                + "\\\\\\\"fe817019d2e9b45766686a93ee230ef0\\\\\\\":[38,39,40,41,42,67,68,71,72],"
                + "\\\\\\\"a408d3633275bd7d9d07fe871f1a4f35\\\\\\\":[27],"
                + "\\\\\\\"5d70c8a84279c549c09bb6a963637464\\\\\\\":[22],"
                + "\\\\\\\"9fef778f6d856a2525f7f06f6f32e5c4\\\\\\\":[18,19,20],"
                + "\\\\\\\"693f85ee17f8d43483be5737897dafc0\\\\\\\":[51,52,53,54,55],"
                + "\\\\\\\"8fe87100eddafceda9257ebf607aa098\\\\\\\":[10,11,12,14,15,16,34,35,36,48,49,56,57],"
                + "\\\\\\\"5edb50593c2d5e4cd49b5a1427720bd1\\\\\\\":[23,24,25,43,44,45,46,47,64,65,66,69,70,73,74],"
                + "\\\\\\\"788a2d89ac307fe9cb2d3a97e995b244\\\\\\\":[9],"
                + "\\\\\\\"717c7b8afebbfb7137f6f0f99beb2a94\\\\\\\":[1],"
                + "\\\\\\\"0e8289f2bf927649dd2511bbc2bb6759\\\\\\\":[2,3,4,5,6,7,8],"
                + "\\\\\\\"9a03d13f8211c1b0f332bbaf15991fd7\\\\\\\":[59,60,61,62,63]}}\\\"},"
                + "\\\"resourceSettingMode\\\":\\\"EXPERT\\\"}\"}");
        task.setAlgParam(
            "{\"waterMarker\":30000,\"windowSize\":15,\"support\":50,\"rsupport\":5,\"cutLength\":50000,"
                + "\"cutLines\":5000,\"weightThreshold\":0.04,\"preprocess\":true,\"trimMode\":\"top\","
                + "\"threshold\":10000,\"regExps\":{\"customer\":[\"102;98;95;96;99;93;92;91;90;65;89;64;94;97;100;"
                + "45;43;"
                + "4;10;11;12;17;19;22;24;28;30;31;33;41;42;58;63;2;62\"],\"regMap\":{}}}");
        task.setOutputSinkParam(
            "{\"sinkSls\":true,\"sinkPretreat\":false,\"sinkDetail\":{},"
            + "\"sourceLogStore\":\"xdragon-metric-application\",\"sourceProject\":\"log-cluster\"}");
        task.setStatus(Integer.valueOf(1).byteValue());

        // create task fail
        ResponseParam createResponse = new ResponseParam();
        createResponse.setPlatformId("xxxx");
        createResponse.setRequestId("req_id");
        createResponse.setErrorCode(TaskManagerErrorCode.CMD_FAILED);
        createResponse.setErrorMessage("create failed");
        when(vvpTaskManager.createClusterTask(any(CreateRequestParam.class))).thenReturn(createResponse);
        LogClusterTask logClusterTask = new LogClusterTask();
        logClusterTask.setTaskName("tm");
        boolean ret = logFlinkTaskService.startClusterTask(logClusterTask, task, new LogTaskStartStrategy());
        Assert.assertFalse(ret);

        // start success
        task.setPlatformId("xxxxx");
        InstanceResponseParam responseSucc = new InstanceResponseParam("req_id", TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.startTask(any())).thenReturn(responseSucc);
        ret = logFlinkTaskService.startClusterTask(logClusterTask, task, new LogTaskStartStrategy());
        Assert.assertTrue(ret);

        // start fail
        InstanceResponseParam responseFail = new InstanceResponseParam("req_id", TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.startTask(any())).thenReturn(responseFail);
        ret = logFlinkTaskService.startClusterTask(logClusterTask, task, new LogTaskStartStrategy());
        Assert.assertFalse(ret);
    }

    @Test
    public void stopClusterTask() {
        LogClusterConfigRegion task = new LogClusterConfigRegion();
        task.setTaskId(1L);
        task.setPlatformId("xxxxx");
        task.setInputSourceParam(
            "{\"sourceType\":\"SLS\",\"accountName\":\"newbie_ecs\",\"regionType\":2,\"endpoint\":\"cn-hangzhou-corp"
                + ".sls.aliyuncs.com\",\"project\":\"ecs-xunjian\",\"logStore\":\"cloudops_service_error_v2\","
                + "\"consumerGroup\":\"log_cluster_consumer\",\"logField\":\"message\",\"filterField\":\"level\","
                + "\"filterString\":\"ERROR\",\"extFields\":\"__source__,__tag__:__path__\",\"filterGamma\":true,"
                + "\"filterStatus\":true,\"sinkParam\":{\"sinkSls\":true,\"sinkPretreat\":false,\"sinkDetail\":{}}}");
        task.setPlatformParam(
            "{\"cpu\":4.0,\"memory\":\"60Gi\",\"parallelism\":12,"
                + "\"last_run_resource\":\"{\\\"expertResourceSetting\\\":{\\\"jobmanagerResourceSettingSpec"
                + "\\\":{\\\"cpu"
                + "\\\":1.0,\\\"memory\\\":\\\"4GiB\\\"},"
                + "\\\"resourcePlan\\\":\\\"{\\\\\\\"ssgProfiles\\\\\\\":[{\\\\\\\"name\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.07,\\\\\\\"heap\\\\\\\":\\\\\\\"2 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"944 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"3\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"1 gb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 "
                + "mb\\\\\\\",\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"64 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"432 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"48 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,\\\\\\\"heap\\\\\\\":\\\\\\\"2 "
                + "gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"992 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\",\\\\\\\"managed\\\\\\\":{},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"cpu\\\\\\\":8.0,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"32 gb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},"
                + "\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"cpu\\\\\\\":0.5,"
                + "\\\\\\\"heap\\\\\\\":\\\\\\\"488 mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{},\\\\\\\"extended\\\\\\\":{}},{\\\\\\\"name\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 mb\\\\\\\","
                + "\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}},"
                + "{\\\\\\\"name\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"cpu\\\\\\\":0.25,\\\\\\\"heap\\\\\\\":\\\\\\\"448 "
                + "mb\\\\\\\",\\\\\\\"offHeap\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"managed\\\\\\\":{\\\\\\\"OPERATOR\\\\\\\":\\\\\\\"32 mb\\\\\\\","
                + "\\\\\\\"STATE_BACKEND\\\\\\\":\\\\\\\"512 mb\\\\\\\"},\\\\\\\"extended\\\\\\\":{}}],"
                + "\\\\\\\"nodes\\\\\\\":[{\\\\\\\"id\\\\\\\":1,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecTableSourceScan\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Source: "
                + "sls_input_table[26004]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"0\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":8,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":2,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26005]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":3,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecWatermarkAssigner\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"WatermarkAssigner[26006]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":4,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26007]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":5,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26008]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":6,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26009]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":7,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLookupJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LookupJoin[26010]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":8,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26011]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"parallelism\\\\\\\":64,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":9,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26012]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"parallelism\\\\\\\":16,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":10,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26013]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":11,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26014]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":12,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26015]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":14,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26017]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":15,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26018]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":16,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26019]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":18,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26021]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":19,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26022]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":20,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26023]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":22,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26025]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"5\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":23,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26026]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":24,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26027]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":25,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26028]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":27,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecJoin\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Join[26030]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"7\\\\\\\",\\\\\\\"parallelism\\\\\\\":1,"
                + "\\\\\\\"maxParallelism\\\\\\\":1,\\\\\\\"minParallelism\\\\\\\":1},"
                + "\\\\\\\"state\\\\\\\":[{\\\\\\\"index\\\\\\\":0,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"leftState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true},"
                + "{\\\\\\\"index\\\\\\\":1,\\\\\\\"ttl\\\\\\\":\\\\\\\"1 h\\\\\\\","
                + "\\\\\\\"name\\\\\\\":\\\\\\\"rightState\\\\\\\",\\\\\\\"userDefined\\\\\\\":true}]},"
                + "{\\\\\\\"id\\\\\\\":28,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26031]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1,"
                + "\\\\\\\"chainingStrategy\\\\\\\":\\\\\\\"HEAD\\\\\\\"}},{\\\\\\\"id\\\\\\\":29,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26032]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":30,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26033]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":31,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26034]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":32,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26035]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\",\\\\\\\"parallelism\\\\\\\":128,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":33,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "remain_table[26036]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"8\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":128,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":34,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26037]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":35,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26038]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":36,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26039]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":38,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26041]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":39,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26042]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":40,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26043]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":41,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26044]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":42,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26045]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":43,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26046]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":44,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26047]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":45,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26048]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":46,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26049]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":47,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "pattern_table[26050]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":48,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26051]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":49,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26052]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":51,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26054]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":52,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26055]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":53,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26056]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":54,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26057]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":55,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26058]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"10\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":56,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26059]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":57,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecLocalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"LocalWindowAggregate[26060]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"parallelism\\\\\\\":32,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":59,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecGlobalWindowAggregate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"GlobalWindowAggregate[26062]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":60,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26063]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":61,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26064]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":62,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26065]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":63,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26066]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"11\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":64,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCorrelate\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Correlate[26067]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":65,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26068]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":66,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "distribution_table[26069]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":67,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26070]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":68,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26071]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":69,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26072]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":70,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26073]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":71,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26074]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":72,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26075]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"9\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}},"
                + "{\\\\\\\"id\\\\\\\":73,\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecCalc\\\\\\\","
                + "\\\\\\\"desc\\\\\\\":\\\\\\\"Calc[26076]\\\\\\\","
                + "\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\",\\\\\\\"parallelism\\\\\\\":4,"
                + "\\\\\\\"maxParallelism\\\\\\\":32768,\\\\\\\"minParallelism\\\\\\\":1}},{\\\\\\\"id\\\\\\\":74,"
                + "\\\\\\\"type\\\\\\\":\\\\\\\"StreamExecSink\\\\\\\",\\\\\\\"desc\\\\\\\":\\\\\\\"Sink: "
                + "sls_table[26077]\\\\\\\",\\\\\\\"profile\\\\\\\":{\\\\\\\"group\\\\\\\":\\\\\\\"6\\\\\\\","
                + "\\\\\\\"parallelism\\\\\\\":4,\\\\\\\"maxParallelism\\\\\\\":32768,"
                + "\\\\\\\"minParallelism\\\\\\\":1}}],\\\\\\\"edges\\\\\\\":[{\\\\\\\"source\\\\\\\":1,"
                + "\\\\\\\"target\\\\\\\":2,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":2,"
                + "\\\\\\\"target\\\\\\\":3,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":3,"
                + "\\\\\\\"target\\\\\\\":4,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":4,\\\\\\\"target\\\\\\\":5,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":5,"
                + "\\\\\\\"target\\\\\\\":6,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":6,\\\\\\\"target\\\\\\\":7,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":7,"
                + "\\\\\\\"target\\\\\\\":8,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":8,\\\\\\\"target\\\\\\\":9,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":9,"
                + "\\\\\\\"target\\\\\\\":10,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":11,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":12,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":11,"
                + "\\\\\\\"target\\\\\\\":14,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":14,"
                + "\\\\\\\"target\\\\\\\":15,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":15,"
                + "\\\\\\\"target\\\\\\\":16,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":16,"
                + "\\\\\\\"target\\\\\\\":18,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":18,\\\\\\\"target\\\\\\\":19,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":19,\\\\\\\"target\\\\\\\":20,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":20,"
                + "\\\\\\\"target\\\\\\\":22,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":22,"
                + "\\\\\\\"target\\\\\\\":23,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":23,\\\\\\\"target\\\\\\\":24,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":24,"
                + "\\\\\\\"target\\\\\\\":25,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":12,"
                + "\\\\\\\"target\\\\\\\":27,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},{\\\\\\\"source\\\\\\\":25,"
                + "\\\\\\\"target\\\\\\\":27,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"GLOBAL\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":27,\\\\\\\"target\\\\\\\":28,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"REBALANCE\\\\\\\"},{\\\\\\\"source\\\\\\\":28,"
                + "\\\\\\\"target\\\\\\\":29,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":29,"
                + "\\\\\\\"target\\\\\\\":30,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":30,"
                + "\\\\\\\"target\\\\\\\":31,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":31,"
                + "\\\\\\\"target\\\\\\\":32,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":32,"
                + "\\\\\\\"target\\\\\\\":33,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":10,"
                + "\\\\\\\"target\\\\\\\":34,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":35,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":35,"
                + "\\\\\\\"target\\\\\\\":36,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":36,"
                + "\\\\\\\"target\\\\\\\":38,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":38,\\\\\\\"target\\\\\\\":39,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":39,\\\\\\\"target\\\\\\\":40,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":41,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":41,"
                + "\\\\\\\"target\\\\\\\":42,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":23,"
                + "\\\\\\\"target\\\\\\\":43,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":43,"
                + "\\\\\\\"target\\\\\\\":44,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":44,"
                + "\\\\\\\"target\\\\\\\":45,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":46,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":46,"
                + "\\\\\\\"target\\\\\\\":47,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":48,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":48,"
                + "\\\\\\\"target\\\\\\\":49,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":49,"
                + "\\\\\\\"target\\\\\\\":51,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":51,\\\\\\\"target\\\\\\\":52,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":52,\\\\\\\"target\\\\\\\":53,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":53,"
                + "\\\\\\\"target\\\\\\\":54,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":54,"
                + "\\\\\\\"target\\\\\\\":55,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":34,"
                + "\\\\\\\"target\\\\\\\":56,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":56,"
                + "\\\\\\\"target\\\\\\\":57,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":57,"
                + "\\\\\\\"target\\\\\\\":59,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"HASH\\\\\\\"},{\\\\\\\"source\\\\\\\":59,\\\\\\\"target\\\\\\\":60,"
                + "\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\",\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},"
                + "{\\\\\\\"source\\\\\\\":60,\\\\\\\"target\\\\\\\":61,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":61,"
                + "\\\\\\\"target\\\\\\\":62,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":62,"
                + "\\\\\\\"target\\\\\\\":63,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":64,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":64,"
                + "\\\\\\\"target\\\\\\\":65,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":65,"
                + "\\\\\\\"target\\\\\\\":66,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":67,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":67,"
                + "\\\\\\\"target\\\\\\\":68,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":69,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":69,"
                + "\\\\\\\"target\\\\\\\":70,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":40,"
                + "\\\\\\\"target\\\\\\\":71,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":71,"
                + "\\\\\\\"target\\\\\\\":72,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":45,"
                + "\\\\\\\"target\\\\\\\":73,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"},{\\\\\\\"source\\\\\\\":73,"
                + "\\\\\\\"target\\\\\\\":74,\\\\\\\"mode\\\\\\\":\\\\\\\"PIPELINED\\\\\\\","
                + "\\\\\\\"strategy\\\\\\\":\\\\\\\"FORWARD\\\\\\\"}],"
                + "\\\\\\\"vertices\\\\\\\":{\\\\\\\"40384a6339eef72bc6b9656f8cf2bcaf\\\\\\\":[28,29,30,31,32,33],"
                + "\\\\\\\"fe817019d2e9b45766686a93ee230ef0\\\\\\\":[38,39,40,41,42,67,68,71,72],"
                + "\\\\\\\"a408d3633275bd7d9d07fe871f1a4f35\\\\\\\":[27],"
                + "\\\\\\\"5d70c8a84279c549c09bb6a963637464\\\\\\\":[22],"
                + "\\\\\\\"9fef778f6d856a2525f7f06f6f32e5c4\\\\\\\":[18,19,20],"
                + "\\\\\\\"693f85ee17f8d43483be5737897dafc0\\\\\\\":[51,52,53,54,55],"
                + "\\\\\\\"8fe87100eddafceda9257ebf607aa098\\\\\\\":[10,11,12,14,15,16,34,35,36,48,49,56,57],"
                + "\\\\\\\"5edb50593c2d5e4cd49b5a1427720bd1\\\\\\\":[23,24,25,43,44,45,46,47,64,65,66,69,70,73,74],"
                + "\\\\\\\"788a2d89ac307fe9cb2d3a97e995b244\\\\\\\":[9],"
                + "\\\\\\\"717c7b8afebbfb7137f6f0f99beb2a94\\\\\\\":[1],"
                + "\\\\\\\"0e8289f2bf927649dd2511bbc2bb6759\\\\\\\":[2,3,4,5,6,7,8],"
                + "\\\\\\\"9a03d13f8211c1b0f332bbaf15991fd7\\\\\\\":[59,60,61,62,63]}}\\\"},"
                + "\\\"resourceSettingMode\\\":\\\"EXPERT\\\"}\"}");
        task.setAlgParam(
            "{\"waterMarker\":30000,\"windowSize\":15,\"support\":50,\"rsupport\":5,\"cutLength\":50000,"
                + "\"cutLines\":5000,\"weightThreshold\":0.04,\"preprocess\":true,\"trimMode\":\"top\","
                + "\"threshold\":10000,\"regExps\":{\"customer\":[\"102;98;95;96;99;93;92;91;90;65;89;64;94;97;100;"
                + "45;43;4;10;11;12;17;19;22;24;28;30;31;33;41;42;58;63;2;62\"],\"regMap\":{}}}");
        task.setStatus(Integer.valueOf(1).byteValue());

        // stop success
        InstanceResponseParam responseSucc = new InstanceResponseParam("req_id", TaskManagerErrorCode.SUCCESS);
        responseSucc.setStreamingResourceSetting("{}");
        when(vvpTaskManager.stopTask(any())).thenReturn(responseSucc);
        boolean ret = logFlinkTaskService.stopClusterTask(task);
        Assert.assertTrue(ret);

        // stop success because of state terminated
        InstanceResponseParam responseFail = new InstanceResponseParam("req_id", TaskManagerErrorCode.CMD_FAILED);
        responseFail.setErrorCode(TaskManagerErrorCode.STATE_ERROR);
        responseFail.setCurrentState(TaskState.TERMINATED);
        when(vvpTaskManager.stopTask(any())).thenReturn(responseFail);
        ret = logFlinkTaskService.stopClusterTask(task);
        Assert.assertTrue(ret);

        // stop failed
        responseFail.setErrorCode(TaskManagerErrorCode.CMD_FAILED);
        responseFail.setErrorMessage("stop task cmd failed");
        ret = logFlinkTaskService.stopClusterTask(task);
        Assert.assertFalse(ret);
    }

    @Test
    public void deleteClusterTask() {
        LogClusterConfigRegion task = new LogClusterConfigRegion();
        task.setTaskId(1L);
        task.setPlatformId("xxxxx");
        task.setStatus(Integer.valueOf(1).byteValue());

        // delete success
        ResponseParam responseSucc = new ResponseParam("req_id", TaskManagerErrorCode.SUCCESS);
        when(vvpTaskManager.deleteTask(any(RequestParam.class))).thenReturn(responseSucc);
        boolean ret = logFlinkTaskService.deleteClusterTask(task);
        Assert.assertTrue(ret);

        // delete success because of task not exist
        ResponseParam responseNotExist = new ResponseParam("req_id", TaskManagerErrorCode.TASK_NOT_EXIST);
        when(vvpTaskManager.deleteTask(any())).thenReturn(responseNotExist);
        ret = logFlinkTaskService.deleteClusterTask(task);
        Assert.assertTrue(ret);

        // delete failed
        ResponseParam responseFail = new ResponseParam("req_id", TaskManagerErrorCode.CMD_FAILED);
        when(vvpTaskManager.deleteTask(any())).thenReturn(responseFail);
        ret = logFlinkTaskService.deleteClusterTask(task);
        Assert.assertFalse(ret);
    }
}
