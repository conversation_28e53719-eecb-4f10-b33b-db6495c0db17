package com.aliyun.xdragon.biz.log.manager.vvp.utils;

import com.aliyun.ververica_inner20220718.models.Deployment;
import com.aliyun.xdragon.biz.log.config.LogClusterSlsConfig;
import com.aliyun.xdragon.biz.log.config.NcRegionOdpsConfig;
import com.aliyun.xdragon.biz.log.manager.config.ResourceConfig;
import com.aliyun.xdragon.biz.log.manager.enums.ResourceMode;
import com.aliyun.xdragon.biz.log.manager.enums.TaskManagerErrorCode;
import com.aliyun.xdragon.biz.log.manager.param.ConfigParam;
import com.aliyun.xdragon.biz.log.manager.param.CreateRequestParam;
import com.aliyun.xdragon.biz.log.manager.param.RequestParam;
import com.aliyun.xdragon.biz.log.manager.param.StartRequestParam;
import com.aliyun.xdragon.biz.log.manager.vvp.config.VvpManagerConfig;
import com.aliyun.xdragon.common.exception.TaskManagerException;
import com.aliyun.xdragon.common.model.log.PlatformParam;
import com.aliyun.xdragon.service.common.config.FlinkMetricSlsConfig;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.Date;

import static com.aliyun.xdragon.biz.log.manager.vvp.utils.RequestConvertor.genDeploymentId;
import static com.aliyun.xdragon.biz.log.manager.vvp.utils.RequestConvertor.genMatchTaskCreateDeployment;
import static com.aliyun.xdragon.biz.log.manager.vvp.utils.RequestConvertor.genStartResourceConfig;
import static org.junit.Assert.fail;

public class RequestConvertorTest {
    private VvpManagerConfig getManagerConfig() {
        VvpManagerConfig config = new VvpManagerConfig();
        config.setNamespace("namespace");
        config.setWorkspace("workspace");
        config.setEngineVersion("engineVersion");
        config.setDeploymentTargetName("targetName");
        return config;
    }

    @Test
    public void testGenDeploymentId() {
        RequestParam request = new RequestParam();
        try {
            genDeploymentId(request);
            fail();
        } catch (Exception e) {
            Assert.assertTrue(e instanceof TaskManagerException);
            TaskManagerException exception = (TaskManagerException)e;
            Assert.assertNotNull(exception.getRequestId());
            Assert.assertNotNull(exception.getErrorCode());
            Assert.assertNotNull(exception.getErrorDescription());
            Assert.assertTrue(exception.getRequestId().isEmpty());
            Assert.assertEquals(Integer.valueOf(TaskManagerErrorCode.PARAM_MISSING_FIELD.getCode()), exception.getErrorCode());
            Assert.assertEquals("Missing field jobName", exception.getErrorDescription());
        }

        request.setJobName("DeploymentId");
        String deploymentId = genDeploymentId(request);
        Assert.assertNotNull(deploymentId);
        Assert.assertFalse(deploymentId.isEmpty());
        Assert.assertEquals("DeploymentId", deploymentId);
    }

    //@Test
    //public void testGenCreateDeploymentAux() {
    //    VvpManagerConfig config = getManagerConfig();
    //    CreateRequestParam request = new CreateRequestParam();
    //    ConfigParam configParam = new ConfigParam();
    //    configParam.setTaskId(1L);
    //    FlinkMetricSlsConfig flinkMetricSlsConfig = new FlinkMetricSlsConfig();
    //    flinkMetricSlsConfig.setAccessId("id");
    //    flinkMetricSlsConfig.setAccessKey("key");
    //    flinkMetricSlsConfig.setEndpoint("endpoint");
    //    flinkMetricSlsConfig.setProject("p");
    //    flinkMetricSlsConfig.setLogStore("l");
    //    configParam.setMetricSlsConfig(flinkMetricSlsConfig);
    //    request.setConfigParam(configParam);
    //    Deployment deployment;
    //    try {
    //        deployment = genCreateDeployment(request, config);
    //        fail();
    //    } catch(Exception e) {
    //        Assert.assertTrue(e instanceof TaskManagerException);
    //        TaskManagerException exception = (TaskManagerException)e;
    //        Assert.assertNotNull(exception.getRequestId());
    //        Assert.assertNotNull(exception.getErrorCode());
    //        Assert.assertNotNull(exception.getErrorDescription());
    //        Assert.assertTrue(exception.getRequestId().isEmpty());
    //        Assert.assertEquals(Integer.valueOf(TaskManagerErrorCode.PARAM_MISSING_FIELD.getCode()), exception.getErrorCode());
    //        Assert.assertEquals("Missing field jobName or description", exception.getErrorDescription());
    //    }
    //
    //    request.setJobName("DeploymentId");
    //    request.getConfigParam().getSql().setHasLevelField(null);
    //    try {
    //        deployment = genCreateDeployment(request, config);
    //        fail();
    //    } catch(Exception e) {
    //        Assert.assertTrue(e instanceof TaskManagerException);
    //        TaskManagerException exception = (TaskManagerException)e;
    //        Assert.assertNotNull(exception.getRequestId());
    //        Assert.assertNotNull(exception.getErrorCode());
    //        Assert.assertNotNull(exception.getErrorDescription());
    //        Assert.assertTrue(exception.getRequestId().isEmpty());
    //        Assert.assertEquals(Integer.valueOf(TaskManagerErrorCode.PARAM_SUBSTITUTE_FAILED.getCode()), exception.getErrorCode());
    //        Assert.assertEquals("Failed to generate content", exception.getErrorDescription());
    //    }
    //
    //    request.getConfigParam().getSql().setHasLevelField(true);
    //    PlatformParam platformParam = new PlatformParam();
    //    platformParam.setCpu(1.0);
    //    platformParam.setMemory("4Gi");
    //    platformParam.setParallelism(4L);
    //    request.getConfigParam().setPlatformParam(platformParam);
    //    deployment = genCreateDeployment(request, config);
    //    Assert.assertNotNull(deployment);
    //}
    //
    //@Test
    //public void testGenUpdateDeployment() {
    //    VvpManagerConfig config = getManagerConfig();
    //    CreateRequestParam request = new CreateRequestParam();
    //    ConfigParam configParam = new ConfigParam();
    //    configParam.setTaskId(1L);
    //    FlinkMetricSlsConfig flinkMetricSlsConfig = new FlinkMetricSlsConfig();
    //    flinkMetricSlsConfig.setAccessId("id");
    //    flinkMetricSlsConfig.setAccessKey("key");
    //    flinkMetricSlsConfig.setEndpoint("endpoint");
    //    flinkMetricSlsConfig.setProject("p");
    //    flinkMetricSlsConfig.setLogStore("l");
    //    configParam.setMetricSlsConfig(flinkMetricSlsConfig);
    //    request.setConfigParam(configParam);
    //    Deployment deployment;
    //    try {
    //        deployment = genUpdateDeployment(request, config);
    //        fail();
    //    } catch(Exception e) {
    //        Assert.assertTrue(e instanceof TaskManagerException);
    //        TaskManagerException exception = (TaskManagerException)e;
    //        Assert.assertNotNull(exception.getRequestId());
    //        Assert.assertNotNull(exception.getErrorCode());
    //        Assert.assertNotNull(exception.getErrorDescription());
    //        Assert.assertTrue(exception.getRequestId().isEmpty());
    //        Assert.assertEquals(Integer.valueOf(TaskManagerErrorCode.PARAM_MISSING_FIELD.getCode()), exception.getErrorCode());
    //        Assert.assertEquals("Missing field", exception.getErrorDescription());
    //    }
    //
    //    request.setJobName("DeploymentId");
    //    request.getConfigParam().getSql().setHasLevelField(null);
    //    try {
    //        deployment = genUpdateDeployment(request, config);
    //        fail();
    //    } catch(Exception e) {
    //        Assert.assertTrue(e instanceof TaskManagerException);
    //        TaskManagerException exception = (TaskManagerException)e;
    //        Assert.assertNotNull(exception.getRequestId());
    //        Assert.assertNotNull(exception.getErrorCode());
    //        Assert.assertNotNull(exception.getErrorDescription());
    //        Assert.assertTrue(exception.getRequestId().isEmpty());
    //        Assert.assertEquals(Integer.valueOf(TaskManagerErrorCode.PARAM_SUBSTITUTE_FAILED.getCode()), exception.getErrorCode());
    //        Assert.assertEquals("Failed to generate content", exception.getErrorDescription());
    //    }
    //
    //    request.getConfigParam().getSql().setHasLevelField(true);
    //    deployment = genUpdateDeployment(request, config);
    //    Assert.assertNotNull(deployment);
    //}

    @Test
    public void testGenStartResourceConfig() {
        StartRequestParam request = new StartRequestParam();
        try {
            genStartResourceConfig(request);
            fail();
        } catch (Exception e) {
            Assert.assertTrue(e instanceof TaskManagerException);
            TaskManagerException exception = (TaskManagerException)e;
            Assert.assertNotNull(exception.getRequestId());
            Assert.assertNotNull(exception.getErrorCode());
            Assert.assertNotNull(exception.getErrorDescription());
            Assert.assertTrue(exception.getRequestId().isEmpty());
            Assert.assertEquals(Integer.valueOf(TaskManagerErrorCode.PARAM_MISSING_FIELD.getCode()), exception.getErrorCode());
            Assert.assertEquals("Missing field platformParam", exception.getErrorDescription());
        }

        PlatformParam platformParam = new PlatformParam();
        platformParam.setCpu(2.0);
        platformParam.setMemory("4Gi");
        platformParam.setParallelism(10L);
        request.setPlatformParam(platformParam);
        ResourceConfig config = genStartResourceConfig(request);
        Assert.assertNotNull(config);
        Assert.assertEquals(Double.valueOf(2.0), config.getCpu());
        Assert.assertEquals("4Gi", config.getMemory());
        Assert.assertEquals(Long.valueOf(10L), config.getParallelism());
        Assert.assertEquals(ResourceMode.BASIC, config.getMode());
        Assert.assertTrue(config.getConfMap().isEmpty());
    }

    @Test
    public void testGenMatchTaskCreateDeployment() {
        CreateRequestParam requestParam = new CreateRequestParam();
        requestParam.setJobName("matchTest");
        ConfigParam param = new ConfigParam();
        param.setPlatformParam(new PlatformParam());
        param.setTaskId(1L);
        param.getSql().setRawLogField("file+content");
        param.getSql().setLevelFilterMarkers("error,!debug,[C]![INFO],[C]!INFO");
        param.getSql().setCreateTime(new Date());
        param.getSql().setExtFields(Arrays.asList("__source__", "extA", "extB"));
        param.getSql().setWindowSize(10);
        FlinkMetricSlsConfig flinkMetricSlsConfig = new FlinkMetricSlsConfig();
        flinkMetricSlsConfig.setAccessId("id");
        flinkMetricSlsConfig.setAccessKey("key");
        flinkMetricSlsConfig.setEndpoint("endpoint");
        flinkMetricSlsConfig.setProject("p");
        flinkMetricSlsConfig.setLogStore("l");
        param.setMetricSlsConfig(flinkMetricSlsConfig);
        LogClusterSlsConfig logClusterSlsConfig = new LogClusterSlsConfig();
        logClusterSlsConfig.setEndpoint("endpoint");
        logClusterSlsConfig.setProject("p");
        logClusterSlsConfig.setAccessId("id");
        logClusterSlsConfig.setAccessKey("ak");
        param.setLogClusterSlsConfig(logClusterSlsConfig);
        NcRegionOdpsConfig ncRegionOdpsConfig = new NcRegionOdpsConfig();
        ncRegionOdpsConfig.setAccessId("id");
        ncRegionOdpsConfig.setAccessKey("ak");
        ncRegionOdpsConfig.setEndpoint("endpoint");
        ncRegionOdpsConfig.setProject("p");
        ncRegionOdpsConfig.setTableName("table");
        param.setNcRegionOdpsConfig(ncRegionOdpsConfig);
        param.getProp().setTaskId(1L);
        param.getProp().setRegion("region");
        param.getProp().setInputLogStore("input_logstore");
        param.getProp().setSupport(100);
        param.getProp().setRatioSupport(10);
        param.getProp().setCutLines(10000);
        param.getProp().setCutLength(100000);
        param.getProp().setIndicesStr("1;2;3");
        param.getProp().setLibraryId("library_id");
        param.getProp().setLibraryKey("library_key");
        param.getProp().setLibraryEndpoint("library_endpoint");
        param.getProp().setLibraryProject("library_project");
        param.getProp().setSlsAccessId("sls_id");
        param.getProp().setSlsAccessKey("sls_key");
        param.getProp().setSlsEndPoint("sls_endpoint");
        param.getProp().setSlsProject("sls_project");
        param.getProp().setSlsLogStore("sls_logstore");
        param.getProp().setOutputUserName("adb_user");
        param.getProp().setOutputPassword("adb_password");
        param.getProp().setOutputSinkTableName("adb_sink");
        param.getProp().setOutputDetailTableName("adb_detail");
        requestParam.setConfigParam(param);
        VvpManagerConfig config = getManagerConfig();
        Deployment deployment = genMatchTaskCreateDeployment(requestParam, config);
        Assert.assertNotNull(deployment);
    }
}