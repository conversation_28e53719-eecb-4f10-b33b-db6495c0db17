package com.aliyun.xdragon.biz.correlation.service;

import com.aliyun.xdragon.biz.BaseTestCase;
import com.aliyun.xdragon.common.generate.model.CorrelationFeatureAssociation;
import com.aliyun.xdragon.common.generate.model.CorrelationFeatureBlackList;
import com.aliyun.xdragon.common.generate.model.CorrelationRuleMining;
import com.aliyuncs.exceptions.ClientException;
import junit.framework.TestCase;
import org.junit.Ignore;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class CorrelationServiceImplTest extends BaseTestCase {
    @Resource
    private CorrelationServiceImpl correlationService;

    @Test
    public void insertFeatureBlackListTest() {
        String preFeature = "feature5";
        String postFeature = "feature6";
        boolean result = correlationService.insertFeatureBlackList(preFeature, postFeature);
        TestCase.assertFalse(result);
    }

    @Test
    @Ignore
    public void updateFeatureAssociation() throws ClientException {
        List<CorrelationFeatureAssociation> records = new ArrayList<>();
        CorrelationFeatureAssociation association = new CorrelationFeatureAssociation();
        association.setAntecedent("feature1");
        association.setConsequent("feature2");
        association.setConfidence(1.0);
        association.setProblemClassification("REASON_VM_PERF");
        association.setValid(true);
        records.add(association);
        // 更新图数据库，需谨慎
        boolean result = correlationService.updateFeatureAssociation(records);
        TestCase.assertTrue(result);
    }

    @Test
    public void listCorrelationDataTest() {
        String startTime = "2022-11-29";
        String endTime = "2022-12-01";
        List<CorrelationFeatureAssociation> result = correlationService.listCorrelationData(startTime, endTime);
        TestCase.assertTrue(result.size() > 0);
    }

    @Test
    public void listCorrelationFeatureBlackListTest() {
        List<CorrelationFeatureBlackList> result = correlationService.listCorrelationFeatureBlackList();
        TestCase.assertTrue(result.size() > 0);
    }

    @Test
    public void listOpsRuleTest() {
        List<CorrelationRuleMining> result = correlationService.listOpsRule();
        TestCase.assertTrue(result.size() > 0);
    }

    @Test
    public void queryOpsRuleByNameTest() {
        String name = "nc_down_prediction_for_vip";
        List<CorrelationRuleMining> result = correlationService.queryOpsRuleByName(name);
        TestCase.assertTrue(result.size() > 0);
    }

    @Test
    public void updateOpsRuleTest() {
        String name = "nc_down_prediction_for_vip";
        boolean result = correlationService.updateOpsRule(name, true);
        TestCase.assertTrue(result);
        name = "cloud_nc_cmci_offline_page_failed";
        result = correlationService.updateOpsRule(name, false);
        TestCase.assertTrue(result);
    }

    @Test
    public void queryApproximateFeatureTest() {
        List<String> features = Arrays.asList("down_start_trigger", "nc_controller_warning");
        Map<String, List<String>> map = correlationService.queryApproximateFeature(features);
        TestCase.assertTrue(map.size() > 0);
    }
}
