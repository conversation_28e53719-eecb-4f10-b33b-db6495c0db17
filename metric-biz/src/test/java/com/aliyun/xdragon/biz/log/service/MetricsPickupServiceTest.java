package com.aliyun.xdragon.biz.log.service;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.*;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.GetIndexResponse;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.aliyun.openservices.log.response.ListMachineGroupResponse;
import com.aliyun.openservices.log.response.ListMachinesResponse;
import com.aliyun.xdragon.biz.log.model.BasicAnomaly;
import com.aliyun.xdragon.biz.log.repository.LogCoverageConfigDao;
import com.aliyun.xdragon.biz.log.repository.LogCoverageDetailDao;
import com.aliyun.xdragon.biz.log.repository.LogCoverageDetailNcDao;
import com.aliyun.xdragon.common.generate.c9.model.NcDo;
import com.aliyun.xdragon.common.generate.model.LogCoverageConfig;
import com.aliyun.xdragon.common.generate.model.LogCoverageDetail;
import com.aliyun.xdragon.common.model.SlsProjectGroup;
import com.aliyun.xdragon.common.model.SlsRegionProject;
import com.aliyun.xdragon.common.model.SlsUserInfo;
import com.aliyun.xdragon.biz.log.config.TsdbConfig;
import com.aliyun.xdragon.biz.log.service.impl.MetricsPickupServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.net.ssl.*")
public class MetricsPickupServiceTest {
    @InjectMocks
    MetricsPickupServiceImpl metricsPickupService;

    @Mock
    private PickUpConfigService pickUpConfigService;

    @Mock
    private KongMingC9Service kongmingC9Service;

    @Mock
    private LogCoverageConfigDao logCoverageConfigDao;

    @Mock
    private LogCoverageDetailDao covDetailDao;

    @Mock
    private LogCoverageDetailNcDao covDetailNcDao;

    @Mock
    private LogNcFilterService logNcFilterService;

    @Mock
    private TsdbConfig tsdbConfig;

    @Mock
    Client client;

    @Mock
    GetLogsResponse response;

    @Test
    public void testGetSlsClient() {
        SlsRegionProject regionProject = new SlsRegionProject();
        regionProject.setRegion("cn-hangzhou-corp");
        regionProject.setRealProject("ecs-xunjian");
        regionProject.setUser("newbie_ecs");

        Map<String, String> regionEndpoint = new HashMap<>();
        when(pickUpConfigService.loadRegionEndpoint()).thenReturn(regionEndpoint);
        Client retclient = metricsPickupService.getSlsClient(regionProject);
        assert retclient == null;

        regionEndpoint.put("cn-hangzhou-corp", "cn-hangzhou-corp.sls.xxx.com");
        when(pickUpConfigService.getSlsUser(anyString())).thenReturn(null);
        retclient = metricsPickupService.getSlsClient(regionProject);
        assert retclient == null;

        Mockito.reset(pickUpConfigService);
        when(pickUpConfigService.loadRegionEndpoint()).thenReturn(regionEndpoint);
        SlsUserInfo slsUserInfo = new SlsUserInfo();
        slsUserInfo.setSk("sk");
        slsUserInfo.setAk("ak");
        slsUserInfo.setName("newbie_ecs");
        when(pickUpConfigService.getSlsUser(eq("newbie_ecs"))).thenReturn(slsUserInfo);
        retclient = metricsPickupService.getSlsClient(regionProject);
        assert retclient != null;

        retclient = metricsPickupService.getSlsClient(regionProject);
        assert retclient != null;
    }

    @Test
    public void getSlsClient2() {
        when(pickUpConfigService.loadRegionEndpoint()).thenReturn(new HashMap<>());
        Client client = metricsPickupService.getSlsClient("cn-hangzhou", null, "user");
        Assert.assertNull(client);

        reset(pickUpConfigService);
        Map<String, String> regionEndpoint = new HashMap<>();
        regionEndpoint.put("cn-hangzhou", "http://cn-hangzhou.xxx.com");
        when(pickUpConfigService.loadRegionEndpoint()).thenReturn(regionEndpoint);
        when(pickUpConfigService.getSlsUser(anyString())).thenReturn(null);
        client = metricsPickupService.getSlsClient("cn-hangzhou", null, "user");
        Assert.assertNull(client);

        reset(pickUpConfigService);
        when(pickUpConfigService.loadRegionEndpoint()).thenReturn(regionEndpoint);
        SlsUserInfo userInfo = new SlsUserInfo();
        when(pickUpConfigService.getSlsUser(anyString())).thenReturn(userInfo);
        client = metricsPickupService.getSlsClient("cn-hangzhou", null, "user");
        Assert.assertNotNull(client);
    }

    @Test
    public void testFindProjectConfigsByName() {
        SlsProjectGroup slsProjectGroup = new SlsProjectGroup();
        slsProjectGroup.setProject("ecs-xunjian");
        SlsRegionProject regionProject = new SlsRegionProject();
        regionProject.setRegion("cn-hangzhou-corp");
        regionProject.setRealProject("ecs-xunjian");
        regionProject.setUser("newbie_ecs");
        slsProjectGroup.setRegions(Lists.newArrayList(regionProject));
        when(pickUpConfigService.loadProjectGroupInfo()).thenReturn(Lists.newArrayList(slsProjectGroup));

        List<SlsRegionProject> ret = metricsPickupService.findProjectConfigsByName("ecs-xunjian");
        assert ret.size() == 1;
        assert Objects.equals(ret.get(0).getRealProject(), "ecs-xunjian");

        Mockito.reset(pickUpConfigService);
        when(pickUpConfigService.loadProjectGroupInfo()).thenReturn(new LinkedList<>());
        ret = metricsPickupService.findProjectConfigsByName("ecs-xunjian");
        assert ret.size() == 0;
    }

    @Test
    public void testGetLogs() throws LogException {
        when(response.IsCompleted()).thenReturn(false).thenReturn(true);
        when(client.GetLogs(anyString(), anyString(), anyInt(), anyInt(), anyString(), anyString())).thenReturn(
            response);
        when(response.GetLogs()).thenReturn(getLogs());
        List<QueriedLog> list = metricsPickupService.getLogs(client, "test", "test", 0, 0, "", "xxxxxx");
        assert list.size() == 1;
        when(response.GetLogs()).thenThrow(new RuntimeException());
        list = metricsPickupService.getLogs(client, "test", "test", 0, 0, "", "xxxxxx");
        assert list.size() == 0;
    }

    @Test
    public void testCountLogs() throws LogException {
        when(response.IsCompleted()).thenReturn(false).thenReturn(true);
        when(client.GetLogs(anyString(), anyString(), anyInt(), anyInt(), anyString(), anyString())).thenReturn(response);
        when(response.GetLogs()).thenReturn(getLogs());
        Long cnt = metricsPickupService.countLogs(client, "test", "test", 0, 0, "", "xxxxxx");
        assert cnt == 0L;
    }

    @Test
    public void testGetLogRetry() throws LogException {
        when(client.GetLogs(anyString(), anyString(), anyInt(), anyInt(), anyString(), anyString())).thenThrow(
            new LogException("ConnectionTimeout", "execution timeout", "id"));
        List<QueriedLog> list = metricsPickupService.getLogs(client, "test", "test", 0, 0, "", "xxxxxx");
        assert list.size() == 0;

        reset(client);
        when(client.GetLogs(anyString(), anyString(), anyInt(), anyInt(), anyString(), anyString())).thenThrow(
            new LogException("FAIL", "FAIL", "id"));
        list = metricsPickupService.getLogs(client, "test", "test", 0, 0, "", "xxxxxx");
        assert list.size() == 0;
    }

    private ArrayList<QueriedLog> getLogs() {
        ArrayList<QueriedLog> list = new ArrayList<>();
        LogContent logContent = new LogContent("testKey", "testValue");
        ArrayList<LogContent> mContents = new ArrayList();
        mContents.add(logContent);
        LogItem item = new LogItem(123, mContents);
        QueriedLog log = new QueriedLog("source", item);
        list.add(log);
        return list;
    }

    @Test
    public void testGetTsdbClient() {
        when(tsdbConfig.getUser()).thenReturn("newbei-ecs");
        when(tsdbConfig.getRegion()).thenReturn("cn-hangzhou-corp");
        when(pickUpConfigService.getSlsUser(anyString())).thenReturn(null);
        Client retClient = metricsPickupService.getTsdbClient();
        assert retClient == null;

        Mockito.reset(pickUpConfigService);
        SlsUserInfo userInfo = new SlsUserInfo();
        userInfo.setAk("ak");
        userInfo.setSk("sk");
        when(pickUpConfigService.getSlsUser(anyString())).thenReturn(userInfo);
        when(pickUpConfigService.loadRegionEndpoint()).thenReturn(new HashMap<>());
        retClient = metricsPickupService.getTsdbClient();
        assert retClient == null;

        Mockito.reset(pickUpConfigService);
        when(pickUpConfigService.getSlsUser(anyString())).thenReturn(userInfo);
        Map<String, String> regionEndpoint = new HashMap<>();
        regionEndpoint.put("cn-hangzhou-corp", "cn-hangzhou-crop.sls.xxx.con");
        when(pickUpConfigService.loadRegionEndpoint()).thenReturn(regionEndpoint);
        retClient = metricsPickupService.getTsdbClient();
        assert retClient != null;
    }

    @Test
    public void getIndex() throws LogException {
        Client client = mock(Client.class);
        GetIndexResponse resp = mock(GetIndexResponse.class);
        when(client.GetIndex(anyString(), anyString())).thenReturn(resp);
        Index index = mock(Index.class);
        when(resp.GetIndex()).thenReturn(index);
        when(index.ToJsonString()).thenReturn("{}");
        String s = metricsPickupService.getIndex(client, "p", "l");
        Assert.assertEquals("{}", s);

        reset(client);
        when(client.GetIndex(anyString(), anyString())).thenThrow(new LogException("", "", ""));
        Assert.assertNull(metricsPickupService.getIndex(client, "p", "l"));
    }

    @Test
    public void listSlsMachinesTest() throws Exception {
        Mockito.when(client.ListMachines(anyString(), anyString(), anyInt(), anyInt())).thenReturn(
            buildListMachinesResponse());

        List<String> ips = metricsPickupService.listSlsMachines(client, "ecs-xunjian", "ay208r_server");
        assert ips.size() == 1;
    }

    public ListMachineGroupResponse buildMachineGroupTest() {
        List<String> machines = new ArrayList<>(Arrays.asList("ay03b_server"));
        Map<String, String> m = new HashMap<>();
        ListMachineGroupResponse resp = new ListMachineGroupResponse(m, 500, 501, machines);

        return resp;
    }

    public ListMachinesResponse buildListMachinesResponse() {
        Map<String, String> m = new HashMap<>();
        Machine machine = new Machine("127.0.0.1", "111111", "11111", 11111);
        List<Machine> machines = new ArrayList<>(Arrays.asList(machine));
        ListMachinesResponse listMachinesResponse = new ListMachinesResponse(m, 1, 1, machines);

        return listMachinesResponse;
    }

    @Test
    public void buildAnomaly() {
        NcDo ncDo = new NcDo();
        ncDo.setIp("*******");
        List<BasicAnomaly> res = metricsPickupService.buildAnomaly(Lists.newArrayList(ncDo), "p", "l", "m", 123, "nc");
        Assert.assertEquals(1, res.size());
        Assert.assertEquals(res.get(0).getNcStatus().getIp(), ncDo.getIp());
    }

    @Test
    public void checkMachineStatusTest() {
        List<NcDo> ncStatuses = new ArrayList<>();
        List<NcDo> res = new ArrayList<>();
        List<String> ips = new ArrayList<>();
        for (int i = 0; i < 20; i++) {
            NcDo status = new NcDo();
            status.setBizStatus("free");
            status.setClusterId("123123");

            ncStatuses.add(status);
            ips.add("*************");
        }
        Mockito.when(kongmingC9Service.getNcStatusByNcIpsFromC9(any())).thenReturn(ncStatuses);

        res = metricsPickupService.checkMachineStatus(ips, null);
        assert res.size() == ncStatuses.size();

        for (int i = 0; i < 100; i++) {
            NcDo status = new NcDo();
            status.setBizStatus("offline");

            ncStatuses.add(status);
            ips.add("*************");
        }

        Mockito.when(kongmingC9Service.getNcStatusByNcIpsFromC9(any())).thenReturn(ncStatuses);
        res = metricsPickupService.checkMachineStatus(ips, 200);
        assert res.size() < ncStatuses.size();
    }

    @Test
    public void getSlsIps() throws LogException {
        Client client = mock(Client.class);
        Map<String, Client> clientMap = new ConcurrentHashMap<>();
        clientMap.put("hangzhou#newbie", client);
        Whitebox.setInternalState(metricsPickupService, "clientMap", clientMap);
        SlsRegionProject slsRegionProject = new SlsRegionProject();
        slsRegionProject.setRegion("hangzhou");
        slsRegionProject.setUser("newbie");
        slsRegionProject.setRealProject("ecs-xunjian");
        slsRegionProject.setEndpoint("http://sls.cn-hangzhou-corp.xxx.com");

        when(client.GetLogs(anyString(), anyString(), anyInt(), anyInt(), anyString(), anyString())).thenReturn(
            response);
        when(response.GetLogs()).thenReturn(new ArrayList<>()).thenReturn(getIpLogs("ncIp"));
        when(response.IsCompleted()).thenReturn(false).thenReturn(true);
        Set<String> ips = metricsPickupService.getSlsIps(slsRegionProject, "check_pycn", 123, 456);
        Assert.assertTrue(ips.isEmpty());
        ips = metricsPickupService.getSlsIps(slsRegionProject, "check_pycn", 123, 456);
        Assert.assertEquals(1, ips.size());
        Assert.assertTrue(ips.contains("*******"));
    }

    @Test
    public void getSourceDiff() throws LogException {
        Client client = mock(Client.class);
        Map<String, Client> clientMap = new ConcurrentHashMap<>();
        clientMap.put("hangzhou#newbie", client);
        Whitebox.setInternalState(metricsPickupService, "clientMap", clientMap);
        SlsRegionProject slsRegionProject = new SlsRegionProject();
        slsRegionProject.setRegion("hangzhou");
        slsRegionProject.setUser("newbie");
        slsRegionProject.setRealProject("ecs-xunjian");
        slsRegionProject.setEndpoint("http://sls.cn-hangzhou-corp.xxx.com");
        when(client.GetLogs(anyString(), anyString(), anyInt(), anyInt(), anyString(), anyString())).thenReturn(
            response);
        when(response.GetLogs()).thenReturn(getIpLogs("ncIp"));
        when(response.IsCompleted()).thenReturn(true);
        Pair<List<String>, Boolean> ips = metricsPickupService.getSourceDiff(slsRegionProject, "check_pync",
            12345, 23456, 300, null);

        Assert.assertEquals(1, ips.getLeft().size());
        Assert.assertTrue(ips.getLeft().contains("*******"));

        List<String> ncs = metricsPickupService.getSourceDiffStrong(slsRegionProject, "check_pync",
            100, 12345, 23456, 300, 2, "__source__");
        Assert.assertEquals(1, ncs.size());
        Assert.assertTrue(ncs.contains("*******"));

        reset(response);
        when(response.GetLogs()).thenReturn(getIpLogs("ncIp")).thenReturn(getIpLogs("ncIp")).thenReturn(
            getIpLogs("ip"));
        when(response.IsCompleted()).thenReturn(false).thenReturn(true);
        ncs = metricsPickupService.getSourceDiffStrong(slsRegionProject, "check_pync", 100, 12345, 23456, 300, 2, null);
        Assert.assertEquals(0, ncs.size());
    }

    @Test
    public void getConfigWithoutProjectAndLogstore() {
        List<Pair<String, String>> pl = Lists.newArrayList(Pair.of("p1", "l1"), Pair.of("p2", "l2"));
        LogCoverageConfig config1 = new LogCoverageConfig();
        config1.setProject("p1");
        config1.setLogstore("l1");
        LogCoverageConfig config2 = new LogCoverageConfig();
        config2.setProject("p1");
        config2.setLogstore("l2");
        LogCoverageConfig config3 = new LogCoverageConfig();
        config3.setProject("p2");
        config3.setLogstore("l1");
        LogCoverageConfig config4 = new LogCoverageConfig();
        config4.setProject("p2");
        config4.setLogstore("l2");
        when(logCoverageConfigDao.listConfigs()).thenReturn(Lists.newArrayList(config1, config2, config3, config4));
        List<LogCoverageConfig> cfs = metricsPickupService.getConfigWithoutProjectAndLogstore(pl);
        Assert.assertEquals(2, cfs.size());
    }

    private ArrayList<QueriedLog> getIpLogs(String ipKey) {
        ArrayList<QueriedLog> list = new ArrayList<>();
        LogContent logContent = new LogContent(ipKey, "*******");
        ArrayList<LogContent> mContents = new ArrayList();
        mContents.add(logContent);
        LogItem item = new LogItem(123, mContents);
        QueriedLog log = new QueriedLog("source", item);
        list.add(log);
        return list;
    }

    @Test
    public void addCovDetailNc() throws LogException {
        Client client = mock(Client.class);
        Map<String, Client> clientMap = new ConcurrentHashMap<>();
        clientMap.put("hangzhou#newbie", client);
        Whitebox.setInternalState(metricsPickupService, "clientMap", clientMap);
        SlsRegionProject slsRegionProject = new SlsRegionProject();
        slsRegionProject.setRegion("hangzhou");
        slsRegionProject.setUser("newbie");
        slsRegionProject.setRealProject("ecs-xunjian");
        slsRegionProject.setEndpoint("http://sls.cn-hangzhou-corp.xxx.com");
        when(client.GetLogs(anyString(), anyString(), anyInt(), anyInt(), anyString(), anyString())).thenReturn(
            response);
        when(response.GetLogs()).thenReturn(getIpLogs("ncIp"));
        when(response.IsCompleted()).thenReturn(true);

        LogCoverageDetail detail = new LogCoverageDetail();
        detail.setId(1L);
        detail.setLogstore("pync");
        detail.setPickInterval(600);
        detail.setTimestamp(12345);
        metricsPickupService.addCovDetailNc(slsRegionProject, detail, null, null, 900, null);
        verify(covDetailNcDao, times(1)).add(any());
    }

    @Test
    public void getLogIps() throws LogException {
        Client client = mock(Client.class);
        LogItem item = new LogItem(1, Lists.newArrayList(new LogContent("ip", "1")));
        QueriedLog log = new QueriedLog("", item);
        LogItem item2 = new LogItem(1, Lists.newArrayList(new LogContent("ip", "2")));
        QueriedLog log2 = new QueriedLog("", item2);
        GetLogsResponse logsResponse = mock(GetLogsResponse.class);
        when(logsResponse.IsCompleted()).thenReturn(true);
        when(logsResponse.GetLogs()).thenReturn(Lists.newArrayList(log, log2));
        when(client.GetLogs(anyString(), anyString(), anyInt(), anyInt(), anyString(), anyString())).thenReturn(
            logsResponse);

        Set<String> ips = metricsPickupService.getLogIps(client, "p", "l", 123, 789, 100, null);
        Assert.assertEquals(2, ips.size());
        Assert.assertTrue(ips.contains("1"));
        verify(client, times(7)).GetLogs(anyString(), anyString(), anyInt(), anyInt(), anyString(), anyString());
    }

    @Test
    public void getFilterNc() {
        Set<String> ncs = new HashSet<>();
        ncs.add("1");
        ncs.add("2");
        when(logNcFilterService.getNcByType(anyInt(), anyBoolean(), anyString())).thenReturn(ncs);

        Set<String> ret = metricsPickupService.getFilterNc(1, 3, "");
        Assert.assertEquals(2, ret.size());
    }
}
