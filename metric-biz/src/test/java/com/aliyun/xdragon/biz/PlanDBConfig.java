package com.aliyun.xdragon.biz;

import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @date 2022/04/11
 */
@Configuration
@EnableTransactionManagement
@MapperScan(basePackages = {"com.aliyun.xdragon.common.generate.plan.model.map"},
    sqlSessionFactoryRef = "planSqlSessionFactoryBean",
    sqlSessionTemplateRef = "planSqlSessionTemplate")
public class PlanDBConfig {

    @Bean(name = "planDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.plan")
    public DataSource metricDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "planPlatformTransactionManager")
    public PlatformTransactionManager createTransactionManager(@Qualifier("planDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "planSqlSessionFactoryBean")
    public SqlSessionFactoryBean sqlSessionFactory(@Qualifier("planDataSource") DataSource dataSource) {
        SqlSessionFactoryBean factory = new SqlSessionFactoryBean();
        factory.setDataSource(dataSource);
        return factory;
    }

    @Bean(name = "planSqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(
        @Qualifier("planSqlSessionFactoryBean") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
