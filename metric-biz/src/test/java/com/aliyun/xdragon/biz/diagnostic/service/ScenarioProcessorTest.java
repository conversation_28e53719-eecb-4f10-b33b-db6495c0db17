package com.aliyun.xdragon.biz.diagnostic.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.aliyun.xdragon.common.enumeration.DiagnosisStatus;
import com.aliyun.xdragon.common.model.metric.DiagnosisResult;
import com.aliyun.xdragon.common.model.metric.request.PerformanceDiagnosisRequest;
import com.aliyun.xdragon.common.model.metric.strategy.ScenarioStrategy;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024/01/15
 */
@RunWith(PowerMockRunner.class)
public class ScenarioProcessorTest {

    @InjectMocks
    private ScenarioProcessor scenarioProcessor;

    @Mock
    private ScenarioDiagnosis scenarioDiagnosis;

    @Mock
    private ScenarioDisplay scenarioDisplay;

    @Test
    public void hybridProcess() {
        DiagnosisResult result1 = new DiagnosisResult();
        result1.setStatus(DiagnosisStatus.HEALTHY);
        result1.setStartTs(0);
        result1.setEndTs(3000);
        DiagnosisResult result2 = new DiagnosisResult();
        result2.setStatus(DiagnosisStatus.DAMAGED);
        result2.setStartTs(480);
        result2.setEndTs(1200);
        when(scenarioDisplay.display(any(), any(), anyInt(), any())).thenReturn(Collections.singletonList(result1))
            .thenReturn(Collections.singletonList(result2));

        List<DiagnosisResult> diagnosisList = new ArrayList<>();
        int[] startTsDiagnosis = {0, 360, 780, 1500};
        int[] endTsDiagnosis = {300, 720, 1440, 3000};
        DiagnosisStatus[] statuses = {DiagnosisStatus.HEALTHY, DiagnosisStatus.DAMAGED, DiagnosisStatus.HEALTHY,
            DiagnosisStatus.DAMAGED};
        for (int i = 0; i < startTsDiagnosis.length; i++) {
            DiagnosisResult tmpResult = new DiagnosisResult();
            tmpResult.setStartTs(startTsDiagnosis[i]);
            tmpResult.setEndTs(endTsDiagnosis[i]);
            tmpResult.setStatus(statuses[i]);
            diagnosisList.add(tmpResult);
        }
        when(scenarioDiagnosis.diagnose(any(), any(), any())).thenReturn(diagnosisList);

        ScenarioStrategy strategy = new ScenarioStrategy();
        strategy.setType("hybrid");
        strategy.setScenario("MBW");
        strategy.setComponent("node");
        PerformanceDiagnosisRequest request = new PerformanceDiagnosisRequest();
        request.setSocket(Collections.singletonList(1));

        List<DiagnosisResult> results = scenarioProcessor.process(null, strategy, 0, request);
        Assert.assertEquals(diagnosisList, results);
        results = scenarioProcessor.process(null, strategy, 0, request);
        Assert.assertEquals(3, results.size());
        Assert.assertEquals(0, results.get(0).getStartTs());
        Assert.assertEquals(300, results.get(0).getEndTs());
        Assert.assertEquals(DiagnosisStatus.HEALTHY, results.get(0).getStatus());
        Assert.assertEquals("MBW-node", results.get(0).getComponent());
        Assert.assertEquals(480, results.get(1).getStartTs());
        Assert.assertEquals(1200, results.get(1).getEndTs());
        Assert.assertEquals(DiagnosisStatus.DAMAGED, results.get(1).getStatus());
        Assert.assertEquals("MBW-node", results.get(1).getComponent());
        Assert.assertEquals(1500, results.get(2).getStartTs());
        Assert.assertEquals(3000, results.get(2).getEndTs());
        Assert.assertEquals(DiagnosisStatus.DAMAGED, results.get(2).getStatus());
        Assert.assertEquals("MBW-node", results.get(2).getComponent());
    }

    @Test
    public void combine() {
        List<DiagnosisResult> diagnosisList = new ArrayList<>();
        int[] startTsDiagnosis = {0, 100, 150};
        int[] endTsDiagnosis = {20, 120, 180};
        for (int i = 0; i < startTsDiagnosis.length; i++) {
            DiagnosisResult result = mock(DiagnosisResult.class);
            when(result.getStartTs()).thenReturn(startTsDiagnosis[i]);
            when(result.getEndTs()).thenReturn(endTsDiagnosis[i]);
            diagnosisList.add(result);
        }
        List<DiagnosisResult> displayList = new ArrayList<>();
        int[] startTsDisplay = {50, 110};
        int[] endTsDisplay = {80, 160};
        for (int i = 0; i < startTsDisplay.length; i++) {
            DiagnosisResult result = mock(DiagnosisResult.class);
            when(result.getStartTs()).thenReturn(startTsDisplay[i]);
            when(result.getEndTs()).thenReturn(endTsDisplay[i]);
            displayList.add(result);
        }

        List<DiagnosisResult> results = scenarioProcessor.combine(diagnosisList, displayList);
        Assert.assertEquals(3, results.size());
        Assert.assertEquals(0, results.get(0).getStartTs());
        Assert.assertEquals(20, results.get(0).getEndTs());
        Assert.assertEquals(50, results.get(1).getStartTs());
        Assert.assertEquals(80, results.get(1).getEndTs());
        Assert.assertEquals(110, results.get(2).getStartTs());
        Assert.assertEquals(160, results.get(2).getEndTs());

        results = scenarioProcessor.combine(diagnosisList, Collections.emptyList());
        Assert.assertEquals(3, results.size());
        Assert.assertEquals(0, results.get(0).getStartTs());
        Assert.assertEquals(20, results.get(0).getEndTs());
        Assert.assertEquals(100, results.get(1).getStartTs());
        Assert.assertEquals(120, results.get(1).getEndTs());
        Assert.assertEquals(150, results.get(2).getStartTs());
        Assert.assertEquals(180, results.get(2).getEndTs());

        results = scenarioProcessor.combine(Collections.emptyList(), displayList);
        Assert.assertEquals(2, results.size());
        Assert.assertEquals(50, results.get(0).getStartTs());
        Assert.assertEquals(80, results.get(0).getEndTs());
        Assert.assertEquals(110, results.get(1).getStartTs());
        Assert.assertEquals(160, results.get(1).getEndTs());

        results = scenarioProcessor.combine(Collections.emptyList(), Collections.emptyList());
        Assert.assertEquals(0, results.size());
    }

}