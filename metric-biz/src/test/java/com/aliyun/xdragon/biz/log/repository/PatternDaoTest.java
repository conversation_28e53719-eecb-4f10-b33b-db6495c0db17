package com.aliyun.xdragon.biz.log.repository;

import com.aliyun.xdragon.biz.AbstractADBTest;
import com.aliyun.xdragon.common.generate.log.model.LogClusterPattern;
import com.aliyun.xdragon.common.generate.log.model.LogPatternTag;
import com.aliyun.xdragon.common.model.log.DayTimeRange;
import com.aliyun.xdragon.common.model.log.LogClusterCountSeq;
import com.aliyun.xdragon.common.model.log.LogNumberMetric;
import com.aliyun.xdragon.common.model.log.LogPatternRank;
import com.aliyun.xdragon.common.model.log.LogPatternSummary;
import com.aliyun.xdragon.common.model.log.LogTimeRange;
import com.aliyun.xdragon.common.model.log.PatternTypeCnt;
import com.aliyun.xdragon.common.model.log.response.RecallPatternResponse;
import org.assertj.core.api.Assertions;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.aliyun.xdragon.common.enumeration.log.PatternFilterMode.DEFAULT;
import static com.aliyun.xdragon.common.enumeration.log.PatternFilterMode.EXCLUDE;
import static com.aliyun.xdragon.common.enumeration.log.PatternFilterMode.INCLUDE;

/**
 * <AUTHOR>
 * @date 2022/06/08
 */
@Import({PatternDao.class, PatternTagDao.class})
public class PatternDaoTest extends AbstractADBTest {
    @Autowired
    private PatternDao patternDao;

    @Autowired
    private PatternTagDao patternTagDao;

    private void addPatterns(Long taskId, long now) {
        LogClusterPattern pattern = new LogClusterPattern();
        pattern.setTaskId(taskId);
        pattern.setPattern("log1");
        pattern.setHashcode(1L);
        pattern.setMd5("md51");
        pattern.setRegion("cn-hangzhou");
        pattern.setSupport(10L);
        pattern.setTime(new Date(now));
        pattern.setWindowSize(15L);
        patternDao.addPattern(pattern);
        pattern.setRegion("cn-beijing");
        patternDao.addPattern(pattern);

        LogClusterPattern pattern2 = new LogClusterPattern();
        pattern2.setTaskId(taskId);
        pattern2.setPattern("log2");
        pattern2.setHashcode(1L);
        pattern2.setMd5("md51");
        pattern2.setRegion("cn-hangzhou");
        pattern2.setSupport(10L);
        pattern2.setTime(new Date(now - 86400));
        pattern2.setWindowSize(30L);
        patternDao.addPattern(pattern2);
        pattern2.setRegion("cn-beijing");
        patternDao.addPattern(pattern2);

        LogClusterPattern pattern3 = new LogClusterPattern();
        pattern3.setTaskId(taskId);
        pattern3.setPattern("log3");
        pattern3.setHashcode(2L);
        pattern3.setMd5("md52");
        pattern3.setRegion("cn-hangzhou");
        pattern3.setSupport(10L);
        pattern3.setTime(new Date(now - 86400));
        pattern3.setWindowSize(60L);
        patternDao.addPattern(pattern3);
        pattern3.setRegion("cn-beijing");
        patternDao.addPattern(pattern3);
    }

    private void addAlarmTag(long taskId, String md5, int value) {
        LogPatternTag tag = new LogPatternTag();
        tag.setTaskId(taskId);
        tag.setAlarm(value);
        tag.setMd5(md5);
        patternTagDao.addLogTag(tag);
    }

    @Test
    public void deletePatterns() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        addPatterns(now, now);
        int cnt = patternDao.deletePatterns(now, new Date(now - 86400), new Date(now), Arrays.asList("cn-hangzhou"));
        Assert.assertEquals(3, cnt);
        cnt = patternDao.deletePatterns(now, new Date(now - 86400), new Date(now), null);
        Assert.assertEquals(3, cnt);
    }

    @Test
    public void getTotalCnt() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        addPatterns(now, now);
        Date start = new Date(now - 86400);
        Date end = new Date(now);
        Long cnt = patternDao.getSum(now, start, end, null, false);
        Assert.assertNotNull(cnt);
        Assert.assertTrue(cnt > 0);

        Set<String> md5s = new HashSet<>();
        md5s.add("md51");
        cnt = patternDao.getSum(now, start, end, md5s, true);
        Assert.assertNotNull(cnt);
        Assert.assertEquals(Long.valueOf(40L), cnt);

        md5s.add("md52");
        cnt = patternDao.getSumWithRegions(now, start, end, Arrays.asList("cn-hangzhou", "cn-beijing"), md5s, true);
        Assert.assertNotNull(cnt);
        Assert.assertEquals(Long.valueOf(60L), cnt);
    }

    @Test
    public void getTotalTypeCnt() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        addPatterns(now, now);
        Date start = new Date(now - 86400);
        Date end = new Date(now);
        Long cnt = patternDao.getTypeCnt(now, start, end, null, false);
        Assert.assertNotNull(cnt);
        Assert.assertTrue(cnt > 0);
    }

    @Test
    public void getNewTypeCnt() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        addPatterns(now, now);
        Date start = new Date(now - 86400);
        Date end = new Date(now);
        Long cnt = patternDao.getNewTypeCnt(now, start, start, end, end, null, false);
        Assert.assertNotNull(cnt);
    }

    @Test
    public void getMaxTime() throws ParseException {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        addPatterns(now, now);
        Date date = patternDao.getMaxTime(now, "cn-hangzhou");
        Assert.assertNotNull(date);
    }

    @Test
    public void getMaxTimeBeforeDate() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        addPatterns(now, now);
        Date date = patternDao.getMaxTimeBeforeDate(now, "cn-hangzhou", "md51", new Date(now));
        Assert.assertNotNull(date);
    }

    @Test
    public void getRegionById() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 10000;
        addPatterns(taskId, now);
        List<String> regions = patternDao.getRegionById(taskId);
        assert regions.size() >= 2;
    }

    @Test
    public void getNcRegionById() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 10000;
        addPatterns(taskId, now);
        List<String> regions = patternDao.getNcRegionById(taskId, new Date(now - 86400), new Date(now));
        assert regions.size() == 2;
    }

    @Test
    public void listPatternsByMd5OrRawPattern() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 10000;
        addPatterns(taskId, now);
        Date start = new Date(now - 60 * 60 * 1000L);
        Date end = new Date(now + 60 * 60 * 1000L);
        LogClusterPattern patterns = patternDao.listPatternsByMd5OrRawPattern(taskId, start, end, "log", null);
        Assert.assertNotNull(patterns);
        patterns = patternDao.listPatternsByMd5OrRawPattern(taskId, start, end, null, "md51");
        Assert.assertNotNull(patterns);
    }

    @Test
    public void listPatterns() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 10000;
        Date startDate = new Date(now - 86400);
        Date endDate = new Date(now);
        addPatterns(taskId, now);
        List<LogClusterPattern> patterns = patternDao.listPatterns(taskId, startDate, endDate, null, null, null, null,
                0, 100);
        Assertions.assertThat(patterns).hasSize(6);
        patterns = patternDao.listPatterns(startDate, endDate, null, null, null, 0, 100);
        Assertions.assertThat(patterns).hasSize(6);
        patterns = patternDao.listPatterns(taskId, startDate, endDate, null, Lists.newArrayList("md51"), "support",
                "desc", 0, 100);
        Assertions.assertThat(patterns).hasSize(4).element(0).hasFieldOrPropertyWithValue("hashcode", 1L);
        patterns = patternDao.listPatterns(startDate, endDate, Lists.newArrayList(taskId + "|md51"), "support", "desc", 0,
                100);
        Assertions.assertThat(patterns).hasSize(4);
        patterns = patternDao.listPatterns(taskId, new Date(now - 86400), new Date(now),
                Lists.newArrayList("cn-hangzhou"), Lists.newArrayList("md51"), null, null, 0, 100);
        Assertions.assertThat(patterns).hasSize(2).element(0).hasFieldOrPropertyWithValue("hashcode", 1L)
                .hasFieldOrPropertyWithValue("region", "cn-hangzhou");
    }

    @Test
    public void listPatternsWithLimit() {
        long start = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = 23333;
        int num = 10;
        int delta = 86400;
        for (int i = 0; i < num; ++i) {
            LogClusterPattern pattern = new LogClusterPattern();
            pattern.setTaskId(taskId);
            pattern.setPattern("log pattern");
            pattern.setHashcode(233L);
            pattern.setRegion("center");
            pattern.setSupport(10L + num);
            pattern.setTime(new Date(start + delta * i));
            pattern.setWindowSize(60L);
            patternDao.addPattern(pattern);
        }
        int limit = 4;
        List<LogClusterPattern> patterns = patternDao.listPatternsWithLimit(taskId, new Date(start),
                new Date(start + delta * num), Arrays.asList("center"), "log pattern", null, null, limit);
        Assertions.assertThat(patterns).hasSize(limit);
    }

    @Test
    public void listAndCountSumPatterns() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 10000;
        Date startDate = new Date(now - 86400);
        Date endDate = new Date(now);
        addPatterns(taskId, now);
        List<LogClusterPattern> patterns = patternDao.listSumPatterns(taskId, startDate, endDate, null, null, null,
                null, 0, 100);
        Assertions.assertThat(patterns).hasSize(3);

        Long cnt = patternDao.countSumPatterns(taskId, startDate, endDate, null, null);
        Assertions.assertThat(cnt).isEqualTo(2L);

        cnt = patternDao.countSumPatterns(startDate, endDate, null);
        Assertions.assertThat(cnt).isEqualTo(2L);

        patterns = patternDao.listSumPatterns(taskId, startDate, endDate, null, Lists.newArrayList("md51"),
                "support", "desc", 0, 100);
        Assertions.assertThat(patterns).hasSize(2).element(0).hasFieldOrPropertyWithValue("md5", "md51");

        cnt = patternDao.countSumPatterns(taskId, startDate, endDate, null, Lists.newArrayList("md51"));
        Assertions.assertThat(cnt).isEqualTo(1L);

        cnt = patternDao.countSumPatterns(startDate, endDate, Lists.newArrayList(taskId + "|md51"));
        Assertions.assertThat(cnt).isEqualTo(1L);

        patterns = patternDao.listSumPatterns(taskId, startDate, endDate, Lists.newArrayList("cn-hangzhou"),
                Lists.newArrayList("md51"), null, null, 0, 100);
        Assertions.assertThat(patterns).hasSize(2).element(0).hasFieldOrPropertyWithValue("md5", "md51");

        cnt = patternDao.countSumPatterns(taskId, startDate, endDate, Lists.newArrayList("cn-hangzhou"),
                Lists.newArrayList("md51"));
        Assertions.assertThat(cnt).isEqualTo(1L);
    }

    @Test
    public void listSumPatterns() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 10000;
        Date startDate = new Date(now - 86400);
        Date endDate = new Date(now);

        addPatterns(taskId, now);
        List<LogClusterPattern> patterns = patternDao.listSumPatterns(taskId, startDate, endDate, null, null, null,
                null, 0, 50);
        Assertions.assertThat(patterns).hasSize(3).element(0).hasFieldOrPropertyWithValue("support", 20L);
        patterns = patternDao.listSumPatterns(startDate, endDate, null, null, null, 0, 50);
        Assertions.assertThat(patterns).hasSize(3).element(0).hasFieldOrPropertyWithValue("support", 20L);

        patterns = patternDao.listSumPatterns(taskId, startDate, endDate, Lists.newArrayList("cn-hangzhou"),
                Lists.newArrayList("md51"), null, null, 0, 50);
        Assertions.assertThat(patterns).hasSize(2).element(0).hasFieldOrPropertyWithValue("support", 10L);
        patterns = patternDao.listSumPatterns(startDate, endDate, Lists.newArrayList(taskId + "|md51"), null, null,
                0, 50);
        Assertions.assertThat(patterns).hasSize(2).element(0).hasFieldOrPropertyWithValue("support", 20L);
    }

    @Test
    public void listClusteredTasks() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        addPatterns(now, now);
        Date start = new Date(now - 86400);
        Date end = new Date(now);
        List<Long> tasks = patternDao.listClusteredTasks(start, end);
        Assert.assertNotNull(tasks);
        Assert.assertEquals(1, tasks.size());
        Assert.assertEquals(Long.valueOf(now), tasks.get(0));
    }

    @Test
    public void listSumPatternsExcludePatternMd5s() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 10000;
        Date startDate = new Date(now - 86400);
        Date endDate = new Date(now);
        addPatterns(taskId, now);
        List<String> patternMd5s = Lists.newArrayList("md51");
        List<String> idMd5s = Lists.newArrayList(taskId + "|md51");
        List<Long> taskIds = Arrays.asList(taskId);
        List<LogClusterPattern> patterns = patternDao.listSumPatternsExcludePatternMd5s(taskId, startDate, endDate,
                null, patternMd5s, "", DEFAULT, null, null, 0, 100);
        Assertions.assertThat(patterns).hasSize(1).element(0).hasFieldOrPropertyWithValue("md5", "md52");
        patterns = patternDao.listSumPatternsExcludePatternMd5s(taskIds, startDate, endDate, idMd5s, "", DEFAULT, null,
                null, 0, 100);
        Assertions.assertThat(patterns).hasSize(1).element(0).hasFieldOrPropertyWithValue("support", 20L);
        long cnt = patternDao.countSumPatternsExcludePatternMd5s(taskId, startDate, endDate, null, patternMd5s, null,
                DEFAULT);
        Assert.assertEquals(1L, cnt);
        cnt = patternDao.countSumPatternsExcludePatternMd5s(taskIds, startDate, endDate, idMd5s, null, DEFAULT);
        Assert.assertEquals(1L, cnt);
        patterns = patternDao.listSumPatternsExcludePatternMd5s(taskId, startDate, endDate, null, patternMd5s, "log2",
                INCLUDE, null, null, 0, 100);
        Assertions.assertThat(patterns).hasSize(0);
        patterns = patternDao.listSumPatternsExcludePatternMd5s(taskIds, startDate, endDate, idMd5s, "log2", INCLUDE,
                null, null, 0, 100);
        Assertions.assertThat(patterns).hasSize(0);
        patterns = patternDao.listSumPatternsExcludePatternMd5s(taskId, startDate, endDate, null, patternMd5s, "log3",
                INCLUDE, null, null, 0, 100);
        Assertions.assertThat(patterns).hasSize(1).element(0).hasFieldOrPropertyWithValue("pattern", "log3");
        patterns = patternDao.listSumPatternsExcludePatternMd5s(taskIds, startDate, endDate, idMd5s, "log3", INCLUDE,
                null, null, 0, 100);
        Assertions.assertThat(patterns).hasSize(1).element(0).hasFieldOrPropertyWithValue("pattern", "log3");
        cnt = patternDao.countSumPatternsExcludePatternMd5s(taskId, startDate, endDate, null, patternMd5s, "log",
                INCLUDE);
        Assert.assertEquals(1L, cnt);
        cnt = patternDao.countSumPatternsExcludePatternMd5s(taskIds, startDate, endDate, idMd5s, "log", INCLUDE);
        Assert.assertEquals(1L, cnt);
    }

    @Test
    public void listPatternMd5() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 10000;
        Date startDate = new Date(now - 86400);
        Date endDate = new Date(now);
        addPatterns(taskId, now);
        List<String> patternMd5s = Lists.newArrayList("md51");
        List<String> idMd5s = Lists.newArrayList(taskId + "|md51");
        List<LogClusterPattern> patterns = patternDao.listPatternMd5(taskId, startDate, endDate, null, patternMd5s);
        Assertions.assertThat(patterns).hasSize(2);
        patterns = patternDao.listPatternMd5(startDate, endDate, idMd5s);
        Assertions.assertThat(patterns).hasSize(1);
    }

    @Test
    public void statRegionByPattern() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 10000;
        addPatterns(taskId, now);
        List<LogClusterPattern> patterns = patternDao.statRegionByPattern(taskId, new Date(now - 86400),
                new Date(now), null);
        Assertions.assertThat(patterns).hasSize(4);

        patterns = patternDao.statRegionByPattern(taskId, new Date(now - 86400), new Date(now),
                Lists.newArrayList("md51"));
        Assertions.assertThat(patterns).hasSize(2);
    }

    @Test
    public void sumMd5() {
        List<String> regions = new ArrayList<>(1);
        regions.add("center");
        Long sum = patternDao.sumMd5(1L, "md5s", new Date(0L), new Date(0L), regions);
        Assert.assertNotNull(sum);
        Assert.assertEquals(Long.valueOf(0L), sum);
    }

    @Test
    public void getCountSeqWithMd5Map() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 10000;

        for (int i = 0; i < 10; ++i) {
            if (i == 5) {
                continue;
            }
            LogClusterPattern pattern = new LogClusterPattern();
            pattern.setTaskId(taskId);
            pattern.setPattern("pattern" + i);
            pattern.setHashcode(Long.valueOf(i));
            pattern.setMd5("md5" + i);
            pattern.setRegion("center");
            pattern.setSupport(10L);
            pattern.setTime(new Date(now + i * 15L * 60L * 1000L));
            pattern.setWindowSize(15L);
            patternDao.addPattern(pattern);
        }
        List<String> md5List = new ArrayList<>();
        md5List.add("md51");
        md5List.add("md54");
        Map<DayTimeRange, List<String>> md5Map = new HashMap<>(2);
        md5Map.put(DayTimeRange.of(new Date(now)), md5List);

        List<LogClusterCountSeq> countSeqs = patternDao.getCountSeqWithMd5Map(taskId, new Date(now),
                new Date(now + 150L * 60L * 1000L), Arrays.asList("center"), md5Map);
        Assert.assertNotNull(countSeqs);
        Assert.assertEquals(1L, countSeqs.size());
        Assert.assertTrue(countSeqs.get(0).getDetail().size() > 0);
    }

    @Test
    public void getCountSeqWithMd5() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 10000;
        for (int i = 0; i < 10; ++i) {
            if (i == 5) {
                continue;
            }
            LogClusterPattern pattern = new LogClusterPattern();
            pattern.setTaskId(taskId);
            pattern.setPattern("pattern" + i);
            pattern.setMd5("md5" + i);
            pattern.setHashcode(Long.valueOf(i));
            pattern.setRegion("center");
            pattern.setSupport(10L);
            pattern.setTime(new Date(now + i * 15L * 60L * 1000L));
            pattern.setWindowSize(15L);
            patternDao.addPattern(pattern);
        }
        Set<String> md5Set = new HashSet<>();
        md5Set.add("md51");
        md5Set.add("md54");

        List<LogClusterCountSeq> countSeqs = patternDao.getCountSeqWithMd5s(taskId, new Date(now),
                new Date(now + 150L * 60L * 1000L), Arrays.asList("center"), md5Set, true);
        Assert.assertNotNull(countSeqs);
        Assert.assertEquals(1L, countSeqs.size());
        Assert.assertEquals(4L, countSeqs.get(0).getDetail().size());

        countSeqs = patternDao.getCountSeqWithMd5s(taskId, new Date(now), new Date(now + 150L * 60L * 1000L),
                Arrays.asList("center"), md5Set, false);
        Assert.assertNotNull(countSeqs);
        Assert.assertEquals(1L, countSeqs.size());
    }

    @Test
    public void getPatternTimeRange() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        Date startDate = new Date(now - 86400 - 3600);
        Date endDate = new Date(now + 3600);

        addPatterns(taskId, now);
        List<Map<String, Object>> rangesList = patternDao.getPatternTimeRange(taskId, null, Lists.newArrayList("md51"),
                startDate, endDate);
        List<LogTimeRange> ranges = new LinkedList<>();
        for (Map<String, Object> m : rangesList) {
            LogTimeRange ltr = new LogTimeRange();
            ltr.setHashcode((long) m.get("hashcode"));
            ltr.setMd5((String) m.get("md5"));
            ltr.setMaxTime((Date) m.get("max_time"));
            ltr.setMinTime((Date) m.get("min_time"));
            ranges.add(ltr);
        }
        LogTimeRange m = ranges.get(0);
        Assert.assertEquals(m.getMd5(), "md51");
        Date dmax = m.getMaxTime();
        Date dmin = m.getMinTime();
        Assertions.assertThat(dmax).isNotNull();
        Assertions.assertThat(dmin).isNotNull();
        ranges = patternDao.getPatternTimeRange(Lists.newArrayList(taskId + "|md51"), startDate, endDate);
        m = ranges.get(0);
        Assert.assertEquals(m.getMd5(), "md51");
        dmax = m.getMaxTime();
        dmin = m.getMinTime();
        Assertions.assertThat(dmax).isNotNull();
        Assertions.assertThat(dmin).isNotNull();
    }

    @Test
    public void getPattern() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        addPatterns(taskId, now);
        LogClusterPattern pattern = patternDao.getPattern(taskId, "md5233", (int) (now / 1000), "cn-beijing");
        Assert.assertNull(pattern);
        pattern = patternDao.getPattern(taskId, "md51", (int) (now / 1000), "cn-beijing");
        Assert.assertNotNull(pattern);
    }

    @Test
    public void getPatternByCode() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        addPatterns(taskId, now);
        LogClusterPattern pattern = patternDao.getPatternByCode(taskId, 1L);
        Assert.assertNotNull(pattern);
        pattern = patternDao.getPatternByCode(taskId, 123L);
        Assert.assertNull(pattern);
    }

    @Test
    public void getPatternByMd5() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        addPatterns(taskId, now);
        LogClusterPattern pattern = patternDao.getPatternByMd5(taskId, "md51");
        Assert.assertNotNull(pattern);
        pattern = patternDao.getPatternByMd5(taskId, "md5123");
        Assert.assertNull(pattern);
    }

    @Test
    public void getPatternTypeCnt() {
        List<PatternTypeCnt> records = patternDao.getPatternTypeCnt(null, null, null);
        Assert.assertNotNull(records);
        Assert.assertTrue(records.isEmpty());

        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 10000;
        addPatterns(taskId, now);
        records = patternDao.getPatternTypeCnt(Arrays.asList(taskId), Arrays.asList("cn-hangzhou"), new Date(now));
        Assert.assertNotNull(records);
        Assert.assertFalse(records.isEmpty());
        records = patternDao.getPatternTypeCnt(Arrays.asList(taskId), new Date(now));
        Assert.assertNotNull(records);
        Assert.assertFalse(records.isEmpty());
    }

    @Test
    public void getMd5PatternMap() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        Date startDate = new Date(now - 86400);
        Date endDate = new Date(now);
        addPatterns(taskId, now);
        Set<String> md5s = new HashSet<>(8);
        md5s.add("md51");
        md5s.add("m2");
        md5s.add("m3");

        List<Map<String, Object>> records = patternDao.getMd5PatternMap(taskId, startDate, endDate, null,
                new HashSet<>(Lists.newArrayList("m1")));
        Map<String, String> hashPatternMap = new HashMap<>();
        for (Map<String, Object> record : records) {
            String md5 = record.get("md5").toString();
            String pattern = record.get("pattern").toString();
            if (!hashPatternMap.containsKey(md5)) {
                hashPatternMap.put(md5, pattern);
            }
        }
        Assert.assertNotNull(hashPatternMap);
        Assert.assertTrue(hashPatternMap.isEmpty());

        records = patternDao.getIdMd5PatternMap(startDate, endDate, new HashSet<>(Lists.newArrayList(taskId + "|m1")));
        hashPatternMap.clear();
        for (Map<String, Object> record : records) {
            String md5 = record.get("md5").toString();
            String pattern = record.get("pattern").toString();
            if (!hashPatternMap.containsKey(md5)) {
                hashPatternMap.put(md5, pattern);
            }
        }
        Assert.assertNotNull(hashPatternMap);
        Assert.assertTrue(hashPatternMap.isEmpty());

        records = patternDao.getMd5PatternMap(taskId, startDate, endDate, null, md5s);
        hashPatternMap.clear();
        for (Map<String, Object> record : records) {
            String md5 = record.get("md5").toString();
            String pattern = record.get("pattern").toString();
            if (!hashPatternMap.containsKey(md5)) {
                hashPatternMap.put(md5, pattern);
            }
        }
        Assert.assertNotNull(hashPatternMap);
        Assert.assertFalse(hashPatternMap.isEmpty());

        Set<String> idMd5s = new HashSet<>(8);
        md5s.forEach(m -> idMd5s.add(taskId + "|" + m));
        records = patternDao.getIdMd5PatternMap(startDate, endDate, idMd5s);
        hashPatternMap.clear();
        for (Map<String, Object> record : records) {
            String md5 = record.get("idMd5").toString();
            String pattern = record.get("pattern").toString();
            if (!hashPatternMap.containsKey(md5)) {
                hashPatternMap.put(md5, pattern);
            }
        }
        Assert.assertNotNull(hashPatternMap);
        Assert.assertFalse(hashPatternMap.isEmpty());
    }

    @Test
    public void filterMd5() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        Date startDate = new Date(now - 86400);
        Date endDate = new Date(now);
        addPatterns(taskId, now);
        List<String> patternMd5s = Lists.newArrayList("md51", "md52", "md53", "m4");
        List<String> idMd5s = new ArrayList<>();
        patternMd5s.forEach(m -> idMd5s.add(taskId + "|" + m));
        List<String> md5s = patternDao.filterMd5(taskId, patternMd5s, "log", INCLUDE, startDate, endDate);
        Assertions.assertThat(md5s).hasSize(2);

        List<LogClusterPattern> idMd5List = patternDao.filterIdMd5(idMd5s, "log", true, startDate, endDate);
        Set<String> idMd5Set = new HashSet<>();
        idMd5List.forEach(p -> idMd5Set.add(p.getTaskId() + "|" + p.getMd5()));
        Assertions.assertThat(idMd5Set).hasSize(2);

        md5s = patternDao.filterMd5(taskId, patternMd5s, "haha", INCLUDE, startDate, endDate);
        Assertions.assertThat(md5s).hasSize(0);

        idMd5List = patternDao.filterIdMd5(idMd5s, "haha", true, startDate, endDate);
        idMd5Set.clear();
        idMd5List.forEach(p -> idMd5Set.add(p.getTaskId() + "|" + p.getMd5()));
        Assertions.assertThat(idMd5Set).hasSize(0);

        md5s = patternDao.filterMd5(taskId, patternMd5s, "log", EXCLUDE, startDate, endDate);
        Assertions.assertThat(md5s).hasSize(0);

        idMd5List = patternDao.filterIdMd5(idMd5s, "log", false, startDate, endDate);
        idMd5Set.clear();
        idMd5List.forEach(p -> idMd5Set.add(p.getTaskId() + "|" + p.getMd5()));
        Assertions.assertThat(idMd5Set).hasSize(0);

        idMd5List = patternDao.filterIdMd5(Collections.emptyList(), "log", false, startDate, endDate);
        Assertions.assertThat(idMd5List).hasSize(0);
    }

    @Test
    public void queryFilterMd5s() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        addPatterns(taskId, now);
        Date startDate = new Date(now - 86400);
        Date endDate = new Date(now);
        List<String> regions = Arrays.asList("cn-hangzhou", "cn-beijing");
        List<String> md5s = patternDao.queryFilterMd5s(taskId, regions, "", INCLUDE, startDate, endDate);
        Assert.assertNull(md5s);
        List<String> idMd5s = patternDao.queryFilterIdMd5s(Arrays.asList(taskId), "", INCLUDE, startDate, endDate);
        Assert.assertNull(idMd5s);
        md5s = patternDao.queryFilterMd5s(taskId, regions, "log", INCLUDE, startDate, endDate);
        Assert.assertEquals(2, md5s.size());
        idMd5s = patternDao.queryFilterIdMd5s(Arrays.asList(taskId), "log", INCLUDE, startDate, endDate);
        Assert.assertEquals(2, idMd5s.size());
        md5s = patternDao.queryFilterMd5s(taskId, regions, "haha", INCLUDE, startDate, endDate);
        Assert.assertEquals(0, md5s.size());
        idMd5s = patternDao.queryFilterIdMd5s(Arrays.asList(taskId), "haha", INCLUDE, startDate, endDate);
        Assert.assertEquals(0, idMd5s.size());
        md5s = patternDao.queryFilterMd5s(taskId, regions, "log", EXCLUDE, startDate, endDate);
        Assert.assertEquals(0, md5s.size());
        idMd5s = patternDao.queryFilterIdMd5s(Arrays.asList(taskId), "log", EXCLUDE, startDate, endDate);
        Assert.assertEquals(0, idMd5s.size());
    }

    @Test
    public void getPatterns() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;

        addPatterns(taskId, now);
        List<String> patterns = patternDao.getPatterns(taskId, null, new Date(now - 86400), new Date(now + 86400),
                null);
        Assert.assertNotNull(patterns);
        Assert.assertFalse(patterns.isEmpty());
    }

    @Test
    public void getLogPatternSummary() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        addPatterns(taskId, now);
        LogPatternSummary summary = patternDao.getLogPatternSummary(taskId, null, new Date(now - 86400),
                new Date(now + 86400), "log1", null);
        Assert.assertNotNull(summary);
        Assert.assertNotNull(summary.getPattern());
        Assert.assertNotNull(summary.getMd5());
        Assert.assertNotNull(summary.getStartDate());
        Assert.assertNotNull(summary.getEndDate());
        Assert.assertNotNull(summary.getCnt());
    }

    @Test
    public void getRegionMaxTime() {
        Map<String, Long> regionTimeMap = patternDao.getRegionMaxTime(-1L);
        Assert.assertNotNull(regionTimeMap);
        Assert.assertTrue(regionTimeMap.isEmpty());
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        addPatterns(taskId, now);
        regionTimeMap = patternDao.getRegionMaxTime(taskId);
        Assert.assertNotNull(regionTimeMap);
        Assert.assertFalse(regionTimeMap.isEmpty());
        Assert.assertTrue(regionTimeMap.size() > 1);
    }

    @Test
    public void getRecallInfos() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        addPatterns(taskId, now);
        List<RecallPatternResponse> recallInfos = patternDao.getRecallInfos(taskId, new Date(now - 86400), new Date(now + 86400), null, "log1");
        Assert.assertNotNull(recallInfos);
        Assert.assertEquals(2, recallInfos.size());
        RecallPatternResponse recallInfo1 = recallInfos.get(0);
        Assert.assertNotNull(recallInfo1);
        Assert.assertNotNull(recallInfo1.getRegion());
        Assert.assertNotNull(recallInfo1.getStartMs());
        Assert.assertNotNull(recallInfo1.getEndMs());
        RecallPatternResponse recallInfo2 = recallInfos.get(1);
        Assert.assertNotNull(recallInfo2);
        Assert.assertNotNull(recallInfo2.getRegion());
        Assert.assertNotNull(recallInfo2.getStartMs());
        Assert.assertNotNull(recallInfo2.getEndMs());
        Assert.assertNotNull(recallInfo2.getCnt());
        Assert.assertNotNull(recallInfo2.getCnt());
    }

    @Test
    public void getLogNumberMetricWithPeriod() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        addPatterns(taskId, now);
        Set<String> excludeSet = new HashSet<>();
        excludeSet.add("md52");
        LogNumberMetric numberMetric = patternDao.getLogNumberMetricWithPeriod(taskId, new Date(now - 86400), new Date(now + 86400), excludeSet);
        Assert.assertNotNull(numberMetric);
        Assert.assertEquals(Long.valueOf(1L), numberMetric.getTypeCnt());
        Assert.assertEquals(Math.log(40), numberMetric.getLogCntSum(), Math.ulp(1.0));
    }

    @Test
    public void getMd5SumMap() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        addPatterns(taskId, now);
        Map<String, Long> md5SumMap = patternDao.getMd5SumMap(taskId, new Date(now - 86400), new Date(now + 86400), Arrays.asList("md51", "md52"));
        Assert.assertNotNull(md5SumMap);
        Assert.assertEquals(2, md5SumMap.size());
    }

    @Test
    public void getAllMd5PatternMap() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        addPatterns(taskId, now);
        Map<String, String> md5PatternMap = patternDao.getAllMd5PatternMap(taskId, new Date(now - 86400), new Date(now + 86400));
        Assert.assertNotNull(md5PatternMap);
        Assert.assertEquals(2, md5PatternMap.size());
    }

    @Test
    public void getPatternRank() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        addPatterns(taskId, now);
        List<LogPatternRank> rankList = patternDao.getPatternRank(taskId, new Date(now - 86400), new Date(now + 86400), "total", 0, 2000);
        Assert.assertNotNull(rankList);
        Assert.assertEquals(3, rankList.size());
    }

    @Test
    public void getPatternRankByMd5() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        addPatterns(taskId, now);
        List<LogPatternRank> rankList = patternDao.getPatternRankByMd5(taskId, new Date(now - 86400L), new Date(now), Arrays.asList("md50", "md51"));
        Assert.assertNotNull(rankList);
        Assert.assertEquals(2, rankList.size());
    }

    @Test
    public void getNewestRecordTime() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        addPatterns(taskId, now);
        Date newest = patternDao.getNewestRecordTime(taskId, 1, "cn-hangzhou", new Date(now - 1000));
        Assert.assertNotNull(newest);
        Assert.assertEquals(new Date(now), newest);
        newest = patternDao.getNewestRecordTime(taskId, 1, "cn-beijing", new Date(now - 1000));
        Assert.assertNotNull(newest);
        Assert.assertEquals(new Date(now), newest);
    }

    @Test
    public void batchGetNewestRecordTime() throws ParseException {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        long taskId = now % 100000;
        addPatterns(taskId, now);
        Map<String, Date> newestMap = patternDao.batchGetNewestRecordTime(taskId, 1, Arrays.asList("cn-hangzhou", "cn-beijing"), new Date(now - 1000));
        Assert.assertNotNull(newestMap);
        Assert.assertEquals(2, newestMap.size());
        Assert.assertTrue(newestMap.containsKey("cn-hangzhou"));
        Assert.assertTrue(newestMap.containsKey("cn-beijing"));
        Assert.assertEquals(new Date(now), newestMap.get("cn-hangzhou"));
        Assert.assertEquals(new Date(now), newestMap.get("cn-beijing"));
    }

    @Test
    public void getPatternCount() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        addPatterns(now, now);
        Map<Long, Integer> patternCount = patternDao.getPatternCount(new Date(now - 86400), new Date(now + 86500), Arrays.asList(now));
        Assert.assertEquals(new Integer(2), patternCount.get(now));
    }

    @Test
    public void getPatternCountWithTag() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        addPatterns(now, now);
        addAlarmTag(now, "md51", 1);
        Map<Long, Integer> patternCount = patternDao.getPatternCountWithTag(new Date(now - 86400), new Date(now + 86500), Arrays.asList(now), "alarm", 1);
        Assert.assertEquals(new Integer(1), patternCount.get(now));
    }

    @Test
    public void getPatternAnalysis() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        addPatterns(now, now);
        List<Map<String, Object>> patternAnalysis = patternDao.getPatternAnalysis(now, new Date(now));
        Assert.assertNotNull(patternAnalysis);
        Assert.assertEquals(2, patternAnalysis.size());
    }

    @Test
    public void getPatternWithFirstAppear() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        addPatterns(now, now);
        Date start = new Date(now - 86400);
        Date end = new Date(now + 86400);
        List<Map<String, Object>> patterns = patternDao.getPatternWithFirstAppear(now, start, end, new Date(now));
        Assert.assertNotNull(patterns);
    }

    @Test
    public void getPatternAccuracyData() {
        long now = System.currentTimeMillis() % 10000000 * 1000;
        addPatterns(now, now);
        Date start = new Date(now - 86400);
        Date end = new Date(now + 86400);
        List<Map<String, Object>> patternAnalysis = patternDao.getPatternAccuracyData(now, start, end, new Date(now));
        Assert.assertNotNull(patternAnalysis);
    }
}
