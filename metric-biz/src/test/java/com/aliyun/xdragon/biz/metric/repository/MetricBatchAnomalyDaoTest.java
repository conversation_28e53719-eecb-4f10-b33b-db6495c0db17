package com.aliyun.xdragon.biz.metric.repository;

import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Vector;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.PutLogsResponse;
import com.aliyun.xdragon.biz.log.config.AnomalyStoreConfig;
import com.aliyun.xdragon.biz.log.repository.AnomalyStoreDao;
import com.aliyun.xdragon.common.enumeration.algorithm.AnomalyType;
import com.aliyun.xdragon.service.common.config.diamond.ConfigService;
import com.google.gson.Gson;
import org.assertj.core.api.Assertions;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.net.ssl.*")
public class MetricBatchAnomalyDaoTest {
    @InjectMocks
    private MetricBatchAnomalyDao metricBatchAnomalyDao;

    @Mock
    private AnomalyStoreConfig anomalyStoreConfig;

    @Mock
    private ConfigService configService;

    private Client client;

    @Before
    public void before() {
        when(anomalyStoreConfig.getProject()).thenReturn("project");
        when(anomalyStoreConfig.getLogStore()).thenReturn("store");
        when(anomalyStoreConfig.getBatchLogStore()).thenReturn("batchLogStore");
        client = mock(Client.class);
        Whitebox.setInternalState(metricBatchAnomalyDao, "client", client);
    }

    @Test
    public void addAnomalyData() throws LogException {
        ArgumentCaptor<Vector> content = ArgumentCaptor.forClass(Vector.class);
        when(client.PutLogs(anyString(), anyString(), anyString(), content.capture(), anyString(),
            anyString())).thenThrow(new LogException("", "dummy exception", "")).thenReturn(
            new PutLogsResponse(new HashMap<>()));

        Map<String, String> log = new LinkedHashMap<String, String>(){{
            put("metric", "metric");
            put("anomaly", "spike");
            put("additional", "{}");
            put("timestamp", "123");
            put("startTimestamp", "122");
            put("value", "320310");
            put("rootCause", "[[\"rc1\", \"rc2\"]]");
            put("dimensionCount", "{\"rc\":{\"rc1\":320000,\"rc2\":310}}");
        }};
        Boolean ret;
        ret = metricBatchAnomalyDao.addBatchAnomalyData(Collections.singletonList(log), null,"metric");
        Assert.assertFalse(ret);

        ret = metricBatchAnomalyDao.addBatchAnomalyData(Collections.singletonList(log), 123,"metric");
        Assert.assertTrue(ret);

        Vector<LogItem> logs = content.getValue();
        Assertions.assertThat(logs.get(0)).hasFieldOrPropertyWithValue("mLogTime", 123);
        Gson gson = new Gson();
        String s = gson.toJson(logs.get(0).GetLogContents());
        Assertions.assertThat(s).isEqualTo("[{\"mKey\":\"metric\",\"mValue\":\"metric\"},{\"mKey\":\"anomaly\","
            + "\"mValue\":\"spike\"},{\"mKey\":\"additional\",\"mValue\":\"{}\"},{\"mKey\":\"timestamp\","
            + "\"mValue\":\"123\"},{\"mKey\":\"startTimestamp\",\"mValue\":\"122\"},{\"mKey\":\"value\","
            + "\"mValue\":\"320310\"},{\"mKey\":\"rootCause\",\"mValue\":\"[[\\\"rc1\\\", \\\"rc2\\\"]]\"},"
            + "{\"mKey\":\"dimensionCount\",\"mValue\":\"{\\\"rc\\\":{\\\"rc1\\\":320000,\\\"rc2\\\":310}}\"}]");
    }
}
