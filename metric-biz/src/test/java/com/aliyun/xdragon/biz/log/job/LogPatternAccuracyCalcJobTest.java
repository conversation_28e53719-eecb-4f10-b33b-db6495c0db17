package com.aliyun.xdragon.biz.log.job;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.xdragon.biz.log.repository.ClusterTaskDao;
import com.aliyun.xdragon.biz.log.repository.LogPatternAccuracyCalcDao;
import com.aliyun.xdragon.biz.log.repository.LogPatternLibraryDao;
import com.aliyun.xdragon.biz.log.repository.PatternDao;
import com.aliyun.xdragon.common.generate.log.model.LogPatternLibrary;
import com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalc;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class LogPatternAccuracyCalcJobTest {
    private final JobContext context = JobContext.newBuilder().setTaskName("LOG_PATTERN_ACCURACY_CALC_JOB_UT")
            .setScheduleTime(new DateTime()).setDataTime(new DateTime()).build();

    @InjectMocks
    private LogPatternAccuracyCalcJob job;

    @Mock
    private ClusterTaskDao clusterTaskDao;

    @Mock
    private PatternDao patternDao;

    @Mock
    private LogPatternAccuracyCalcDao logPatternAccuracyCalcDao;

    @Mock
    private LogPatternLibraryDao logPatternLibraryDao;

    @Test
    public void genTask() {
        when(clusterTaskDao.getAllTaskIds()).thenReturn(Arrays.asList(1L, 2L, 3L, 4L));
        List<Long> tasks = job.genTask(context, 0);
        Assert.assertNotNull(tasks);
        Assert.assertEquals(4, tasks.size());
    }

    @Test
    public void processSubTask() throws ParseException {
        when(patternDao.getPatternWithFirstAppear(eq(1L), any(Date.class), any(Date.class), any(Date.class)))
                .thenReturn(Collections.emptyList());

        ProcessResult result = job.processSubTask(context, 0, 0, 1L);
        Assert.assertNotNull(result);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());

        long now = System.currentTimeMillis();
        List<Map<String, Object>> patternAppearData = new ArrayList<>();
        Map<String, Object> patternAppearDataItem1 = new HashMap<>();
        patternAppearDataItem1.put("md5", "md51");
        patternAppearDataItem1.put("pattern", "pattern1");
        patternAppearDataItem1.put("hashcode", 10L);
        patternAppearDataItem1.put("first_appear", new Date(now - 86400 * 10 * 1000L));
        Map<String, Object> patternAppearDataItem2 = new HashMap<>();
        patternAppearDataItem2.put("md5", "md51");
        patternAppearDataItem2.put("pattern", "pattern1");
        patternAppearDataItem2.put("hashcode", 1L);
        patternAppearDataItem2.put("first_appear", new Date(now - 86400 * 3 * 1000L));
        Map<String, Object> patternAppearDataItem3 = new HashMap<>();
        patternAppearDataItem3.put("md5", "md52");
        patternAppearDataItem3.put("pattern", "pattern2");
        patternAppearDataItem3.put("hashcode", 0L);
        patternAppearDataItem3.put("first_appear", new Date(now - 86400 * 4 * 1000L));
        patternAppearData.add(patternAppearDataItem1);
        patternAppearData.add(patternAppearDataItem2);
        patternAppearData.add(patternAppearDataItem3);
        when(patternDao.getPatternWithFirstAppear(eq(2L), any(Date.class), any(Date.class), any(Date.class)))
                .thenReturn(patternAppearData);
        List<LogPatternLibrary> libraries = new ArrayList<>();
        LogPatternLibrary lib1 = new LogPatternLibrary();
        lib1.setMd5("md51");
        libraries.add(lib1);
        List<LogPatternAccuracyCalc> calcRecords = new ArrayList<>();
        LogPatternAccuracyCalc calcRecord1 = new LogPatternAccuracyCalc();
        calcRecord1.setTaskId(2L);
        calcRecord1.setMd5("md52");
        calcRecord1.setPattern("pattern2");
        calcRecords.add(calcRecord1);
        when(logPatternLibraryDao.getPatternsByMd5s(eq(2L), any(List.class))).thenReturn(libraries);
        when(logPatternAccuracyCalcDao.getLogpatternAccuracyCalcByMd5(eq(2L), any(List.class))).thenReturn(calcRecords);
        when(logPatternAccuracyCalcDao.batchInsert(any(List.class))).thenReturn(1);

        LogPatternAccuracyCalc record1 = new LogPatternAccuracyCalc();
        record1.setTaskId(2L);
        record1.setMd5("md51");
        record1.setPattern("pattern1");
        record1.setAppearTime(new Date());
        record1.setPatternSource(1);
        record1.setStatus(0);
        LogPatternAccuracyCalc record2 = new LogPatternAccuracyCalc();
        record2.setTaskId(2L);
        record2.setMd5("md52");
        record2.setPattern("pattern2");
        record2.setAppearTime(new Date());
        record2.setPatternSource(10);
        record2.setStatus(0);
        List<LogPatternAccuracyCalc> unlabeledPatterns = new ArrayList<>();
        unlabeledPatterns.add(record1);
        unlabeledPatterns.add(record2);
        when(logPatternAccuracyCalcDao.getUnlabeledLogPatternAccuracyCalcRecords(eq(2L))).thenReturn(unlabeledPatterns);
        LogPatternLibrary library1 = new LogPatternLibrary();
        library1.setMd5("md51");
        library1.setStatus(1);
        library1.setModifyTime(new Date());
        LogPatternLibrary library2 = new LogPatternLibrary();
        library2.setMd5("md52");
        library2.setStatus(2);
        library2.setModifyTime(new Date());
        when(logPatternLibraryDao.getPatternLabelAndModifyTime(eq(2L), any(List.class))).thenReturn(Arrays.asList(library1, library2));
        when(logPatternAccuracyCalcDao.batchUpdate(any(List.class))).thenReturn(2);

        result = job.processSubTask(context, 0, 0, 2L);
        Assert.assertNotNull(result);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());
    }
}