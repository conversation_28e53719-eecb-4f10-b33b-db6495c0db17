package com.aliyun.xdragon.biz.metric.repository;

import java.util.List;

import com.aliyun.xdragon.biz.AbstractDbTest;
import com.aliyun.xdragon.common.generate.model.MetricImport;
import com.aliyun.xdragon.common.generate.model.map.MetricImportMapper;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @date 2023/05/29
 */
@Import(MetricImportDao.class)
public class MetricImportDaoTest extends AbstractDbTest {

    @Autowired
    private MetricImportDao importDao;

    @Autowired
    private MetricImportMapper mapper;

    @Test
    public void addImportConfig() {
        MetricImport config = new MetricImport();
        config.setProject("ecs-xunjian");
        config.setLogstore("test");
        config.setUser("ecs");
        config.setName("metric");
        config.setRegion("cn-hangzhou");
        importDao.addImportConfig(config);
        Assert.assertNotNull(config.getId());
    }

    @Test
    public void listConfigs() {
        MetricImport config = new MetricImport();
        config.setProject("ecs-xunjian");
        config.setLogstore("test");
        config.setUser("ecs");
        config.setName("metric");
        config.setRegion("cn-hangzhou");
        importDao.addImportConfig(config);
        List<MetricImport> configs = importDao.listConfigs();
        Assert.assertFalse(configs.isEmpty());
    }

    @Test
    public void updateConfigWithId() {
        MetricImport config = new MetricImport();
        config.setProject("ecs-xunjian");
        config.setLogstore("test");
        config.setUser("ecs");
        config.setName("metric");
        config.setRegion("cn-hangzhou");
        importDao.addImportConfig(config);
        config.setLastAggrTime(123);
        importDao.updateConfigWithId(config.getId(), config);
        MetricImport config2 = mapper.selectByPrimaryKey(config.getId());
        Assert.assertEquals(new Integer(123), config2.getLastAggrTime());
    }
}
