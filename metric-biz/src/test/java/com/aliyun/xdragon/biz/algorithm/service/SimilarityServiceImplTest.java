package com.aliyun.xdragon.biz.algorithm.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.aliyun.xdragon.api.service.algorithm.model.SimilarityCalcRequest;
import com.aliyun.xdragon.common.enumeration.algorithm.NormalizationType;
import com.aliyun.xdragon.common.enumeration.algorithm.SeriesSimilarityType;
import com.aliyun.xdragon.common.model.XdragonMetricRequest;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
public class SimilarityServiceImplTest {
    @InjectMocks
    SimilarityServiceImpl similarityService;


    @Test
    public void calcSimilarity() {
        SimilarityCalcRequest request1 = new SimilarityCalcRequest();
        request1.setSimilarityType(SeriesSimilarityType.Pearson);
        request1.setNormalizationType(NormalizationType.NonNormalized);
        request1.setSimilarityThreshold(0.8);
        List<Double> array1 = new ArrayList<>(Arrays.asList(1.2, 1.2, 1.0, 1.5, 1.0, 2.1, 2.0));
        List<Double> array2 = new ArrayList<>(Arrays.asList(0.2, 0.2, 0.15, 0.18, 0.13, 0.3, 0.32));
        request1.setArray1(array1);
        request1.setArray2(array2);
        XdragonMetricRequest<SimilarityCalcRequest> request = new XdragonMetricRequest<>();
        request.setParameters(request1);
        Assert.assertEquals(0.968, similarityService.calcSimilarity(request).getData().getSimilarity(), 1e-3);
        Assert.assertTrue(similarityService.calcSimilarity(request).getData().isSimilar());
        request1.setArray2(array1);
        Assert.assertEquals(1, similarityService.calcSimilarity(request).getData().getSimilarity(), 1e-3);
        List<Double> array3 = new ArrayList<>(Arrays.asList(0.2, 0.2, 0.18, 0.13, 0.32));
        request1.setArray2(array3);
        request1.setSimilarityType(SeriesSimilarityType.DTW);
        Assert.assertEquals(0.569, similarityService.calcSimilarity(request).getData().getSimilarity(), 1e-3);
        Assert.assertFalse(similarityService.calcSimilarity(request).getData().isSimilar());
    }
}
