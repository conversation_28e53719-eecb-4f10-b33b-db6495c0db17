package com.aliyun.xdragon.biz.clientops.repository;

import com.aliyun.xdragon.biz.AbstractADBTest;
import com.aliyun.xdragon.common.generate.log.model.HouyiEvent;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/11
 */
@Import(HouyiEventDao.class)
public class HouyiEventDaoTest extends AbstractADBTest {
    @Autowired
    private HouyiEventDao houyiEventDao;

    @Test
    public void getEventByDim() {
        HouyiEvent event = new HouyiEvent();
        long now = System.currentTimeMillis() / 1000;
        event.setPt(now);
        event.setVmName("i-a");
        event.setType("vm_status_change");
        event.setVmStatus("running");
        event.setVmOldStatus("starting");
        event.setDimFull(0);
        event.setEventTs(now);
        houyiEventDao.addEvent(event);
        Assert.assertNotNull(event.getEid());

        List<HouyiEvent> events = houyiEventDao.getEventByDim(0, 100, now - 86400, now);
        Assert.assertFalse(events.isEmpty());
        event.setDimFull(1);
        int c = houyiEventDao.updateEventByEidAndPt(event);
        Assert.assertEquals(1, c);
    }

    @Test
    public void batchUpdate() throws InterruptedException {
        List<HouyiEvent> events = new LinkedList<>();
        long now = System.currentTimeMillis() / 1000;
        for (int i = 0; i < 5; i++) {
            HouyiEvent event = new HouyiEvent();
            event.setPt(now);
            event.setVmName("i-a");
            event.setType("vm_status_change");
            event.setVmStatus("running");
            event.setVmOldStatus("starting");
            event.setDimFull(0);
            houyiEventDao.addEvent(event);
            events.add(event);
        }
        events.forEach(e -> {
            e.setDimFull(5);
            e.setVmName(e.getVmName() + "-1");
        });
        int r = houyiEventDao.batchUpdateDim(events);
        List<HouyiEvent> es = houyiEventDao.getEventByDim(5, 100, -1, -1);
        Assert.assertEquals(5, es.size());
        Assert.assertEquals(10, r);
    }

    @Ignore
    @Test
    public void queryRunningCpuByConditionTest() {
        //        String aliUid,String region,
        //                String azone,String cluster,String instanceType,
        //        long startEventTs,long endEventTs
        long now = System.currentTimeMillis() / 1000;
        HouyiEvent event = new HouyiEvent();
        event.setPt(now);
        event.setAliUid("1234");
        event.setVmName("i-a123");
        event.setVmStatus("Running");
        event.setVmOldStatus("starting");
        event.setRegionAlias("cn-bei");
        event.setAzName("cn-bei-q");
        event.setClusterAlias("qwer");
        event.setVmTypeFamily("ecs.u1");
        event.setEventTs(1709789400l);
        houyiEventDao.addEvent(event);
        List<HouyiEvent> events = houyiEventDao.queryRunningCpuByCondition("1234", "cn-bei",
            "cn-bei-q", "qwer", "ecs.u1", 1709789400l, 1709789460l);
        List<String> runStatusList = Arrays.asList("Running");
        List<HouyiEvent> eventList = houyiEventDao.queryVmRunningInfoByCondition("1234", "cn-bei",
            "cn-bei-q", "qwer", "ecs.u1",
            1709789400l, 1709789460l, 1, 100);

        List<HouyiEvent> historyList = houyiEventDao.queryVmHistory("1234", "i-a123");
        Assert.assertEquals(1, events.size());
        Assert.assertEquals(1, eventList.size());
        Assert.assertEquals(1, historyList.size());

    }

    @Test
    public void queryMissZoneVm() {
        HouyiEvent event = new HouyiEvent();
        long now = System.currentTimeMillis() / 1000;
        event.setPt(now - 1000);
        event.setVmName("i-a");
        event.setType("vm_status_change");
        event.setVmStatus("running");
        event.setVmOldStatus("starting");
        event.setDimFull(1);
        event.setEventTs(now);
        houyiEventDao.addEvent(event);

        List<String> vms = houyiEventDao.queryMissZoneVm(now - 1000, now, 0, 100);
        Assert.assertTrue(vms.contains("i-a"));
    }

    @Test
    public void queryVmZone() {
        HouyiEvent event = new HouyiEvent();
        long now = System.currentTimeMillis() / 1000;
        event.setPt(now);
        event.setVmName("i-a");
        event.setType("vm_status_change");
        event.setVmStatus("running");
        event.setVmOldStatus("starting");
        event.setAzName("cn-hangzhou-b");
        event.setRegionAlias("cn-hangzhou");
        event.setDimFull(1);
        event.setEventTs(now);
        houyiEventDao.addEvent(event);
        HouyiEvent e2 = houyiEventDao.queryVmZone("i-a", now);
        Assert.assertEquals("cn-hangzhou-b", e2.getAzName());
        Assert.assertEquals("cn-hangzhou", e2.getRegionAlias());

        List<HouyiEvent> e3 = houyiEventDao.queryVmZone(Lists.newArrayList("i-a"));
        Assert.assertEquals(1, e3.size());
        Assert.assertEquals("cn-hangzhou-b", e3.get(0).getAzName());
        Assert.assertEquals("cn-hangzhou", e3.get(0).getRegionAlias());
    }

    @Test
    public void updateVmZone() {
        HouyiEvent event = new HouyiEvent();
        long now = System.currentTimeMillis() / 1000 - 3000;
        event.setPt(now);
        event.setVmName("i-a");
        event.setType("vm_status_change");
        event.setVmStatus("running");
        event.setVmOldStatus("starting");
        event.setDimFull(1);
        event.setEventTs(now);
        houyiEventDao.addEvent(event);
        houyiEventDao.updateVmZone("i-a", "cn-hangzhou-a", "cn-hangzhou", now);
        HouyiEvent e2 = houyiEventDao.queryVmZone("i-a", now);
        Assert.assertEquals("cn-hangzhou-a", e2.getAzName());
        Assert.assertEquals("cn-hangzhou", e2.getRegionAlias());
    }

}
