package com.aliyun.xdragon.biz.chat;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)

public class ChatBaiLianServiceImplMockTest {
    @InjectMocks
    private ChatBailianServiceImpl chatBailianServiceImpl;


    @Test
    public void testFunctionCall() {
        try {
            // 测试 String.format 静态方法
            Object result = chatBailianServiceImpl.invokeMethod("java.lang.String", "format", "[\"%s is %d years old.\", \"Alice\", 30]");
            System.out.println("String.format result: " + result);
            // 测试 Integer.parseInt 静态方法
            result = chatBailianServiceImpl.invokeMethod("java.lang.Integer", "parseInt", "[\"123\"]");
            System.out.println("Integer.parseInt result: " + result);
            // 测试 Date.getTime 实例方法
            result = chatBailianServiceImpl.invokeMethod("java.util.Date", "getTime", "[]");
            System.out.println("Date.getTime result: " + result);
            // 测试 DateUtil.getDate 静态方法
            result = chatBailianServiceImpl.invokeMethod("com.aliyun.xdragon.metric.spark.util.DateUtil", "getDate", "{\"day\": 1}");
            System.out.println("DateUtil.getDate result: " + result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}