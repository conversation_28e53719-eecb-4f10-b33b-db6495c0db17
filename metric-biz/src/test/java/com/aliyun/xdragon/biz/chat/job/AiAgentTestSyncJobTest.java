package com.aliyun.xdragon.biz.chat.job;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.Arrays;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.xdragon.biz.chat.repository.ChatTestOssDao;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2025/01/13
 */
@RunWith(PowerMockRunner.class)
public class AiAgentTestSyncJobTest {

    @InjectMocks
    private AiAgentTestSyncJob job;

    @Mock
    private ChatTestOssDao ossDao;

    @Test
    @SneakyThrows
    public void test() {
        // Mock OSS
        OSSObjectSummary summary1 = mock(OSSObjectSummary.class);
        when(summary1.getKey()).thenReturn("0001.md");
        OSSObjectSummary summary2 = mock(OSSObjectSummary.class);
        when(summary2.getKey()).thenReturn("0002.txt");
        when(ossDao.listFiles(anyString(), anyInt())).thenReturn(Arrays.asList(summary1, summary2));
        InputStream in = getClass().getResourceAsStream("/AiAgentTest/0001.md");
        when(ossDao.getFileInputStreamUnsafe(anyString())).thenReturn(in);

        // 校验任务执行结果
        ProcessResult result = job.process(null, 0, 0);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());

        //捕获内部变量
        ArgumentCaptor<ByteArrayInputStream> argument = ArgumentCaptor.forClass(ByteArrayInputStream.class);
        verify(ossDao).uploadFile(anyString(), argument.capture());
        String[] lines = IOUtils.toString(argument.getValue(), Charset.defaultCharset()).split("\n");
        Assert.assertEquals(2, lines.length);
    }

}