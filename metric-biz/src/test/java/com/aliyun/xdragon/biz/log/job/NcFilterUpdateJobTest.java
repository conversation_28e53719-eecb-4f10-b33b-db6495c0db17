package com.aliyun.xdragon.biz.log.job;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.odps.Column;
import com.aliyun.odps.data.ArrayRecord;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.type.TypeInfoFactory;
import com.aliyun.xdragon.biz.log.repository.NcFilterDao;
import com.aliyun.xdragon.biz.log.service.KongMingC9Service;
import com.aliyun.xdragon.common.generate.c9.model.NcDo;
import com.aliyun.xdragon.common.generate.model.NcFilter;
import com.aliyun.xdragon.service.common.agent.OdpsClient;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2023/04/12
 */
@RunWith(PowerMockRunner.class)
public class NcFilterUpdateJobTest {
    @InjectMocks
    private NcFilterUpdateJob job;

    @Mock
    private NcFilterDao dao;

    @Mock
    private KongMingC9Service service;

    @Mock
    private OdpsClient client;

    @Test
    public void test() throws Exception {
        Assert.assertNotNull(job);

        NcDo nc1 = new NcDo();
        nc1.setIp("*******");
        nc1.setBizStatus("offline");
        Column[] columns = new Column[] {new Column("ip", TypeInfoFactory.STRING)};
        Object[] values = new Object[] {nc1.getIp()};
        Record record = new ArrayRecord(columns, values);
        List<Record> records = new ArrayList<>();
        records.add(record);
        NcDo nc2 = new NcDo();
        nc2.setIp("*******");
        nc2.setBizStatus("init");
        List<NcDo> ncDoList2 = new ArrayList<>(1);
        ncDoList2.add(nc2);
        List<NcFilter> ncFilterList = new ArrayList<>(1);
        ncFilterList.add(new NcFilter());
        when(client.runSql(any(String.class))).thenReturn(records);
        when(service.getNcIpsByStatusesFromC9(any(List.class))).thenReturn(ncDoList2);
        when(dao.addNcFilterList(any(List.class))).thenReturn(records.size() + ncDoList2.size());
        when(dao.deleteOldNcFilter(anyLong())).thenReturn(10);
        when(dao.getNewestNcFilterList()).thenReturn(ncFilterList);
        when(client.batchInsertRecords(any(String.class), any(String.class), any(List.class))).thenReturn(true);

        ProcessResult result = job.process(null, 100, 100);
        Assert.assertNotNull(result);
        Assert.assertEquals("success", result.getStatus().getDescription());
    }
}
