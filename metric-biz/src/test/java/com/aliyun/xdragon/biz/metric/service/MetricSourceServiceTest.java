package com.aliyun.xdragon.biz.metric.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.xdragon.biz.log.service.MetricsPickupService;
import com.aliyun.xdragon.biz.metric.repository.MetricAnomalyConfigDao;
import com.aliyun.xdragon.biz.metric.repository.MetricDetailDao;
import com.aliyun.xdragon.biz.metric.repository.MetricSourceDao;
import com.aliyun.xdragon.biz.metric.service.impl.MetricSourceServiceImpl;
import com.aliyun.xdragon.common.enumeration.MetricSourceType;
import com.aliyun.xdragon.common.enumeration.MetricTsdbLogStore;
import com.aliyun.xdragon.common.generate.model.MetricAnomalyConfig;
import com.aliyun.xdragon.common.generate.model.MetricDetail;
import com.aliyun.xdragon.common.generate.model.MetricSource;
import com.aliyun.xdragon.common.model.PageableInfo;
import com.aliyun.xdragon.common.model.XdragonMetricRequest;
import com.aliyun.xdragon.common.model.XdragonMetricResponse;
import com.aliyun.xdragon.common.model.metric.request.ListMetricSourceRequest;
import com.aliyun.xdragon.common.model.metric.request.SlsQueryCheckRequest;
import com.aliyun.xdragon.common.model.metric.request.UpsertMetricSourceRequest;
import org.assertj.core.api.Assertions;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.isNull;

@RunWith(PowerMockRunner.class)
public class MetricSourceServiceTest {
    @InjectMocks
    public MetricSourceServiceImpl metricSourceService;

    @Mock
    private MetricSourceDao metricSourceDao;

    @Mock
    private MetricDetailDao metricDetailDao;

    @Mock
    private MetricAnomalyConfigDao metricAnomalyConfigDao;
    @Mock
    private MetricsPickupService metricsPickupService;

    @Mock
    private Client client;

    MetricSource metricSource = new MetricSource();

    MetricDetail metricDetail = new MetricDetail();

    MetricAnomalyConfig metricAnomalyConfig = new MetricAnomalyConfig();

    @Before
    public void setUp() {
        metricSource.setSourceId(1L);
        metricSource.setSourceLogstore(MetricTsdbLogStore.XdragonMetric.getTsdb());
        metricSource.setMetricAnomalyId(1L);
        metricSource.setIsDeleted(false);
        metricSource.setComments("测试");
        metricSource.setIsPeriod(false);
        metricSource.setOwner("320310");
        metricSource.setTsInterval(60);
        metricSource.setSourceName("source_ok_name");
        metricSource.setAnomalyCheck(false);
        metricSource.setLostCheck(false);

        metricDetail.setSourceId(1L);
        metricDetail.setMetricName("metric1");
        metricDetail.setPeriodLength(48);
        metricDetail.setMetricAnomalyId(1L);
        metricDetail.setAnomalyCheck(false);
        metricDetail.setExpertLostThreshold(86400L);
        metricDetail.setLostCheck(false);
        metricDetail.setMissingRate((byte)5);
        metricDetail.setPeriodScore((byte)10);

        metricAnomalyConfig.setConfigId(2L);
        metricAnomalyConfig.setSourceIds("2,3");

        Mockito.when(metricSourceDao.upsertMetricSourceSelective(any(MetricSource.class))).thenReturn(1);
        Mockito.when(metricSourceDao.listMetricSource(anyInt(), anyInt(), anyString(), any(), any())).thenReturn(
            Collections.singletonList(metricSource));
        Mockito.when(metricSourceDao.listMetricSource(anyInt(), anyInt(), ArgumentMatchers.isNull(), any(), any())).thenReturn(
            Collections.singletonList(metricSource));
        Mockito.when(metricSourceDao.listAllMetricSource(ArgumentMatchers.isNull(), ArgumentMatchers.isNull(),
            ArgumentMatchers.isNull())).thenReturn(Collections.singletonList(metricSource));
        Mockito.when(metricSourceDao.getMetricSourceById(anyLong())).thenReturn(null);
        Mockito.when(metricSourceDao.getMetricSourceByName(anyString())).thenReturn(null).thenReturn(metricSource);
        Mockito.when(metricSourceDao.getMetricSourceCount(ArgumentMatchers.isNull())).thenReturn(1L);
        Mockito.when(metricSourceDao.deleteMetricSource(anyLong())).thenReturn(0);
        Mockito.when(metricDetailDao.deleteDetailBySourceId(anyLong())).thenReturn(1);
        Mockito.when(metricDetailDao.listMetrics(anyLong(), anyInt(), anyInt(), ArgumentMatchers.isNull())).thenReturn(
            Collections.singletonList(metricDetail)
        );
        Mockito.when(metricAnomalyConfigDao.getConfigById(anyLong())).thenReturn(metricAnomalyConfig);
    }

    @Test
    public void testUpsert() {
        UpsertMetricSourceRequest request1 = new UpsertMetricSourceRequest();
        request1.setComments("测试");
        request1.setSourceName("source_bad_name");
        request1.setSourceLogStore(MetricTsdbLogStore.XdragonMetric);
        request1.setIsPeriod(false);
        request1.setAnomalyCheck(true);
        request1.setLostCheck(true);
        request1.setTsInterval(60);
        request1.setAnomalyCheckAll(false);
        request1.setLostCheckAll(false);
        request1.setSourceType(MetricSourceType.FLINK);

        XdragonMetricRequest<UpsertMetricSourceRequest> request = new XdragonMetricRequest<>();
        request1.setSourceName("source_ok_name0");
        request.setParameters(request1);
        XdragonMetricResponse<MetricSource> response0 = metricSourceService.upsertMetricSource(request);
        Assert.assertEquals("source_ok_name0", response0.getData().getSourceName());

        request1.setSourceId(1L);
        request1.setSourceName("source_ok_name1");
        request.setParameters(request1);
        XdragonMetricResponse<MetricSource> response2 = metricSourceService.upsertMetricSource(request);
        Assert.assertEquals("目标数据源不存在", response2.getMessage());

        Mockito.when(metricSourceDao.getMetricSourceById(anyLong())).thenReturn(metricSource);
        XdragonMetricResponse<MetricSource> response1 = metricSourceService.upsertMetricSource(request);
        Assert.assertEquals("数据源名已存在", response1.getMessage());

        Mockito.when(metricSourceDao.getMetricSourceByName(anyString())).thenReturn(null);
        request1.setMetricAnomalyId(2L);
        request.setParameters(request1);
        XdragonMetricResponse<MetricSource> response3 = metricSourceService.upsertMetricSource(request);
        Assert.assertEquals("source_ok_name1", response3.getData().getSourceName());
        Assert.assertFalse(metricDetail.getAnomalyCheck());

        request1.setAnomalyCheckAll(true);
        request1.setLostCheckAll(true);
        request.setParameters(request1);
        metricSource.setLostCheck(false);
        metricSource.setAnomalyCheck(true);
        metricSourceService.upsertMetricSource(request);
        Assert.assertTrue(metricDetail.getLostCheck());

        request1.setSourceName("source_ok_name*&");
        request.setParameters(request1);
        XdragonMetricResponse<MetricSource> response4 = metricSourceService.upsertMetricSource(request);
        Assert.assertEquals("数据源名称格式不正确", response4.getMessage());

        request1.setIsDeleted(true);
        request.setParameters(request1);
        XdragonMetricResponse<MetricSource> response5 = metricSourceService.upsertMetricSource(request);
        Assert.assertEquals("删除数据源失败", response5.getMessage());

        Mockito.when(metricSourceDao.deleteMetricSource(anyLong())).thenReturn(1);
        XdragonMetricResponse<MetricSource> response6 = metricSourceService.upsertMetricSource(request);
        Assertions.assertThat(response6.getData()).hasFieldOrPropertyWithValue("sourceName", "source_ok_name1");

        request1.setSourceName("400_test_no_config");
        request1.setMetricAnomalyId(3L);
        request1.setIsDeleted(null);
        request.setParameters(request1);
        Mockito.when(metricAnomalyConfigDao.getConfigById(anyLong())).thenReturn(null);
        XdragonMetricResponse<MetricSource> response7 = metricSourceService.upsertMetricSource(request);
        Assert.assertEquals("目标异常检测策略不存在", response7.getMessage());
    }

    @Test
    public void listTest() {
        ListMetricSourceRequest request1 = new ListMetricSourceRequest();
        request1.setPageIndex(1);
        request1.setPageSize(10);
        XdragonMetricRequest<ListMetricSourceRequest> request = new XdragonMetricRequest<>();
        request.setParameters(request1);
        XdragonMetricResponse<PageableInfo<MetricSource>> response = metricSourceService.listMetricSource(request);
        Assert.assertEquals("测试", response.getData().getData().get(0).getComments());
        request1.setIsAll(true);
        request.setParameters(request1);
        XdragonMetricResponse<PageableInfo<MetricSource>> response1 = metricSourceService.listMetricSource(request);
        Assert.assertEquals("测试", response1.getData().getData().get(0).getComments());
        request1.setSourceId(1L);
        request1.setIsAll(false);
        request.setParameters(request1);
        Mockito.when(metricSourceDao.getMetricSourceById(anyLong())).thenReturn(metricSource);
        XdragonMetricResponse<PageableInfo<MetricSource>> response2 = metricSourceService.listMetricSource(request);
        Assert.assertEquals("测试", response2.getData().getData().get(0).getComments());
    }

    @Test
    public void slsCheckTest() {
        SlsQueryCheckRequest request1 = new SlsQueryCheckRequest();
        request1.setStartTs(3600);
        request1.setEndTs(7200);
        request1.setSlsConfig("{"
            + "\"user\":\"userTest\","
            + "\"region\":\"cn-hangzhou\","
            + "\"project\":\"xdragon-test\","
            + "\"logstore\":\"xdragon-test\","
            + "\"query\":\"exceptionName:nc_down_alert\","
            + "\"sourceName\":\"source_ok_name\","
            + "\"tsInterval\": 600,"
            + "\"values\":\"value\","
            + "\"analyze\":\"select count(*) as value where cluster_alias = '测试' group by cluster\"}");
        XdragonMetricRequest<SlsQueryCheckRequest> request = new XdragonMetricRequest<>();
        request.setParameters(request1);
        XdragonMetricResponse<List<Map<String, String>>> response = metricSourceService.slsQueryCheck(request);
        Assert.assertEquals("SLS连接配置错误", response.getMessage());

        ArgumentCaptor<String> sql = ArgumentCaptor.forClass(String.class);
        Mockito.when(metricsPickupService.getSlsClient(anyString(), isNull(), anyString())).thenReturn(client);
        Mockito.when(metricsPickupService.getLogs(any(), anyString(), anyString(), anyInt(), anyInt(), anyString(), sql.capture())).thenReturn(Collections.emptyList());
        XdragonMetricResponse<List<Map<String, String>>> response1 = metricSourceService.slsQueryCheck(request);
        Assert.assertEquals("SLS查询结果为空", response1.getMessage());

        request1.setSlsConfig("{"
            + "\"user\":\"userTest\","
            + "\"region\":\"cn-hangzhou\","
            + "\"project\":\"xdragon-test\","
            + "\"logstore\":\"xdragon-test\","
            + "\"query\":\"exceptionName:nc_down_alert\","
            + "\"sourceName\":\"source_ok_name\","
            + "\"tsInterval\": 600,"
            + "\"values\":\"value\","
            + "\"analyze\":\"select count(*) as value where cluster_alias = '测试'\"}");
        request.setParameters(request1);
        Mockito.when(metricsPickupService.getLogs(any(), anyString(), anyString(), anyInt(), anyInt(), anyString(), sql.capture())).thenReturn(getLogs());
        XdragonMetricResponse<List<Map<String, String>>> response2 = metricSourceService.slsQueryCheck(request);
        Assert.assertEquals(5, response2.getData().size());
        String sql1 = "exceptionName:nc_down_alert | select count(*) as value , __time__ - __time__ % 600 as timestamp where cluster_alias = '测试' group by timestamp  limit 10";
        Assert.assertEquals(sql1, sql.getValue());

        request1.setSlsConfig("{"
                + "\"user\":\"userTest\","
                + "\"region\":\"cn-hangzhou\","
                + "\"project\":\"xdragon-test\","
                + "\"logstore\":\"xdragon-test\","
                + "\"query\":\"exceptionName:nc_down_alert\","
                + "\"sourceName\":\"source_ok_name\","
                + "\"tsInterval\": 600,"
                + "\"values\":\"value\","
                + "\"analyze\":\"select count(*) as value where cluster_alias = '测试' group by exceptionName\"}");
        request.setParameters(request1);
        Mockito.when(metricsPickupService.getLogs(any(), anyString(), anyString(), anyInt(), anyInt(), anyString(), sql.capture())).thenReturn(getLogs());
        XdragonMetricResponse<List<Map<String, String>>> response3 = metricSourceService.slsQueryCheck(request);
        Assert.assertEquals(5, response3.getData().size());
        String sql2 = "exceptionName:nc_down_alert | select count(*) as value , __time__ - __time__ % 600 as timestamp where cluster_alias = '测试' group by timestamp,  exceptionName limit 10";
        Assert.assertEquals(sql2, sql.getValue());
    }

    @Test
    public void listLogstoreTest() {
        XdragonMetricResponse<List<MetricTsdbLogStore>> response = metricSourceService.listMetricSourceLogStore();
        Assert.assertEquals(MetricTsdbLogStore.values().length, response.getData().size());
    }


    private List<QueriedLog> getLogs() {
        List<QueriedLog> ret = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            LogItem item = new LogItem();
            item.PushBack("value", String.valueOf(i));
            item.PushBack("timestamp", String.valueOf(3600 * i));
            item.PushBack("metricName", "metricTest");
            QueriedLog queriedLog = new QueriedLog("1.2.3.4", item);
            ret.add(queriedLog);
        }
        return ret;
    }

}
