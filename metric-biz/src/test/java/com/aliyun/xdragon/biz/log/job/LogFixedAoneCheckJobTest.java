package com.aliyun.xdragon.biz.log.job;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.xdragon.api.service.workitem.AoneTopService;
import com.aliyun.xdragon.biz.log.repository.LogAnomalyAoneDao;
import com.aliyun.xdragon.biz.log.repository.PatternDao;
import com.aliyun.xdragon.biz.log.repository.PatternTagDao;
import com.aliyun.xdragon.biz.workitem.service.WorkItemService;
import com.aliyun.xdragon.common.enumeration.PatternTagSet;
import com.aliyun.xdragon.common.generate.log.model.LogAnomalyAone;
import com.aliyun.xdragon.common.generate.log.model.LogClusterPattern;
import com.aliyun.xdragon.common.generate.log.model.LogPatternTag;
import com.taobao.api.response.KeludeIssueGetResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024/11/19
 */
@RunWith(PowerMockRunner.class)
public class LogFixedAoneCheckJobTest {
    @InjectMocks
    private LogFixedAoneCheckJob job;

    @Mock
    private LogAnomalyAoneDao logAnomalyAoneDao;

    @Mock
    private WorkItemService workItemService;

    @Mock
    private AoneTopService aoneTopService;

    @Mock
    private PatternDao patternDao;

    @Mock
    private PatternTagDao patternTagDao;

    @Test
    public void testGenTask() {
        when(logAnomalyAoneDao.getAllTaskIds()).thenReturn(Arrays.asList(1L, 2L));

        JobContext context = JobContext.newBuilder().setJobInstanceId(101).build();
        List<Long> task = job.genTask(context, 123);
        Assert.assertNotNull(task);
        Assert.assertEquals(Arrays.asList(1L, 2L), task);
    }

    @Test
    public void testProcessSubTask() throws Exception {
        JobContext context = JobContext.newBuilder().setJobInstanceId(101).build();
        ProcessResult result = job.processSubTask(context, 111111, 222222, 1L);
        Assert.assertNotNull(result);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());

        when(logAnomalyAoneDao.getAoneIdsByTask(anyLong())).thenReturn(Arrays.asList(1L, 2L));
        List<KeludeIssueGetResponse.Issue> aones = new ArrayList<>(2);
        KeludeIssueGetResponse.Issue aone1 = new KeludeIssueGetResponse.Issue();
        aone1.setId(1L);
        aone1.setStatus("Closed");
        aone1.setAssignedTo("handler1");
        aones.add(aone1);
        when(workItemService.searchWorkItemsByIdList(any(List.class))).thenReturn(aones);
        result = job.processSubTask(context, 111111, 222222, 1L);
        Assert.assertNotNull(result);
        Assert.assertEquals(InstanceStatus.FAILED, result.getStatus());

        KeludeIssueGetResponse.Issue aone2 = new KeludeIssueGetResponse.Issue();
        aone2.setId(2L);
        aone2.setStatus("Fixed");
        aone2.setAssignedTo("handler2");
        aones.add(aone2);
        result = job.processSubTask(context, 111111, 222222, 1L);
        Assert.assertNotNull(result);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());

        LogAnomalyAone logAnomalyAone = new LogAnomalyAone();
        logAnomalyAone.setAoneId(1L);
        logAnomalyAone.setHandler("handler");
        logAnomalyAone.setStatus("New");
        logAnomalyAone.setRegion("center");
        logAnomalyAone.setMd5("md5");
        logAnomalyAone.setTaskId(1L);
        when(logAnomalyAoneDao.getLogAnomalyAoneByAoneId(anyLong())).thenReturn(logAnomalyAone);
        when(logAnomalyAoneDao.getReopenAoneList(anyLong())).thenReturn(Arrays.asList(2L, 1L));
        when(aoneTopService.updateIssueStatus(anyString(), anyLong(), anyString())).thenReturn(true);
        LogClusterPattern pattern = new LogClusterPattern();
        pattern.setTime(new Date());
        when(patternDao.getPatternByMd5(any(), any())).thenReturn(pattern);
        LogPatternTag tag = new LogPatternTag();
        tag.setIgnore(PatternTagSet.SET.getTag());
        when(patternTagDao.getLogTagByMd5(anyLong(), anyString())).thenReturn(tag).thenReturn(null);
        when(aoneTopService.addAoneComment(anyLong(), anyString(), anyString())).thenReturn(false);
        result = job.processSubTask(context, 111111, 222222, 1L);
        Assert.assertNotNull(result);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());
    }
}