package com.aliyun.xdragon.biz.workitem.service;

import com.alibaba.ecs.devops.consistent.queryField.VmQueryField;
import com.alibaba.ecs.devops.consistent.response.Vm;
import com.aliyun.xdragon.api.service.chat.model.UserOperationRecordInfo;
import com.aliyun.xdragon.biz.workitem.service.impl.DetailVmConsistentImpl;
import com.aliyun.xdragon.biz.workitem.service.impl.UserOperationRecordServiceImpl;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.ecsops.model.v20160401.OpsDescribeResourceActionTrailInfoRequest;
import com.aliyuncs.ecsops.model.v20160401.OpsDescribeResourceActionTrailInfoResponse;
import com.aliyuncs.exceptions.ClientException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;


@RunWith(PowerMockRunner.class)
public class UserOperationRecordServiceTest {
    @InjectMocks
    private UserOperationRecordServiceImpl userOperationRecordService;

    @Mock
    IAcsClient client;

    @Test
    public void testQueryActionTrail() throws ClientException {
        when(client.getAcsResponse(any(OpsDescribeResourceActionTrailInfoRequest.class))).thenReturn(getResponse());
        userOperationRecordService.queryActionTrail(Arrays.asList("i-qwe23e23e23er23"),1213123123L,"cn-chengdu",new Date(), new Date());
    }

    @Mock
    private DetailVmConsistentImpl detailVmConsistent;

    @Before
    public void setUp() {
        // 任何通用的设置代码
    }

    private OpsDescribeResourceActionTrailInfoResponse getResponse(){
        OpsDescribeResourceActionTrailInfoResponse response = new OpsDescribeResourceActionTrailInfoResponse();


        OpsDescribeResourceActionTrailInfoResponse.Resource.ResourceAction resourceAction = new OpsDescribeResourceActionTrailInfoResponse.Resource.ResourceAction();
        resourceAction.setActionEventName("Reboot");
        resourceAction.setActionEventTime("2024-12-01T08:11:23Z");
        resourceAction.setActionEventType("test");
        List<OpsDescribeResourceActionTrailInfoResponse.Resource.ResourceAction> resourceActionList = new ArrayList<>();
        resourceActionList.add(resourceAction);

        OpsDescribeResourceActionTrailInfoResponse.Resource resource = new OpsDescribeResourceActionTrailInfoResponse.Resource();
        resource.setResourceActions(resourceActionList);
        resource.setResourceId("i-qwe23e23e23er23");
        List<OpsDescribeResourceActionTrailInfoResponse.Resource> resourceList = new ArrayList();
        resourceList.add(resource);

        response.setResources(resourceList);

        return response;
    }

    @Test
    public void queryActionTrail_VmNotFound_ThrowsRuntimeException() throws ClientException {
        String vm = "vm1";
        Date startTime = new Date();
        Date endTime = new Date();
        List<VmQueryField> fields = Arrays.asList(VmQueryField.VmField.INSTANCE_ID, VmQueryField.VmField.ALI_UID, VmQueryField.VmField.REGION_NO_ALIAS);

        // 模拟 detailVmConsistent.queryVmInfo 返回一个空列表
        when(detailVmConsistent.queryVmInfo(Collections.singletonList(vm), fields.toArray(new VmQueryField[0])))
                .thenReturn(Collections.emptyList());

        assertThrows(RuntimeException.class, () -> userOperationRecordService.queryActionTrail(vm, startTime, endTime));
    }
}
