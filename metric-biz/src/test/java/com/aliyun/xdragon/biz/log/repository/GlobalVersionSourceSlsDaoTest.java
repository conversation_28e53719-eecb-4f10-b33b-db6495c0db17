package com.aliyun.xdragon.biz.log.repository;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.aliyun.xdragon.biz.log.config.LogClusterSlsConfig;
import com.aliyun.xdragon.common.generate.log.model.GlobalVersionSource;
import com.aliyun.xdragon.service.common.config.diamond.ConfigService;
import com.aliyun.xdragon.service.common.model.SLSQueryResult;
import com.aliyun.xdragon.service.common.util.SlsUtil;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

/**
 * <AUTHOR>
 * @date 2025/04/08
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(SlsUtil.class)
public class GlobalVersionSourceSlsDaoTest {
    @InjectMocks
    private GlobalVersionSourceSlsDao globalVersionSourceDao;

    @Mock
    private LogClusterSlsConfig logClusterSlsConfig;

    @Mock
    private ConfigService configService;

    @Before
    public void init() {
        Client client = mock(Client.class);
        when(logClusterSlsConfig.getUser()).thenReturn("user");
        when(logClusterSlsConfig.getRegion()).thenReturn("hz");
        when(logClusterSlsConfig.getProject()).thenReturn("project");
        when(configService.getSlsClient(anyString(), anyString(), any())).thenReturn(client);
        globalVersionSourceDao.initClient();
    }

    @Test
    public void getGlobalVersionSourceByNcIp() {
        SLSQueryResult queryResult = new SLSQueryResult();
        LogItem item = new LogItem();
        item.PushBack("azone", "azone");
        item.PushBack("cluster", "cluster");
        item.PushBack("time", "2025-04-07 23:03:37");
        item.PushBack("hw_hypervisor", "hw_hypervisor");
        item.PushBack("nc_ip", "nc_ip");
        item.PushBack("pkg_name", "pkg_name");
        item.PushBack("region", "region");
        item.PushBack("sw_hypervisor", "sw_hypervisor");
        item.PushBack("version", "version");
        item.PushBack("version_type", "version_type");
        item.PushBack("vm_name", "vm_name");
        QueriedLog log = new QueriedLog("", item);
        GetLogsResponse response = new GetLogsResponse(new HashMap<String, String>() {{
            put("x-log-progress", "Complete");
        }});
        response.SetLogs(Lists.newArrayList(log));
        queryResult.setResult(response);
        queryResult.setCompleted(true);
        ArgumentCaptor<String> captor = ArgumentCaptor.forClass(String.class);

        mockStatic(SlsUtil.class);
        when(SlsUtil.querySLS(any(Client.class), anyString(), anyString(), anyInt(), anyInt(), anyString(),
            captor.capture())).thenReturn(queryResult);
        List<GlobalVersionSource> ss = globalVersionSourceDao.getGlobalVersionSourceByNcIp(
            Lists.newArrayList("*******", "*******"), new Date(), new Date());
        Assert.assertFalse(ss.isEmpty());
        String sql
            = "nc_ip: ******* or nc_ip: ******* | select azone, cluster, collect_time as time, hw_hypervisor, nc_ip, "
            + "pkg_name, region, sn, sw_hypervisor, version, version_type, vm_name";
        Assert.assertEquals(sql, captor.getValue());
        Assert.assertEquals("sw_hypervisor", ss.get(0).getSwHypervisor());

        ss = globalVersionSourceDao.getGlobalVersionSourceByNcIp(new LinkedList<>(), new Date(), new Date());
        Assert.assertTrue(ss.isEmpty());
    }
}
