package com.aliyun.xdragon.biz.metric.repository;

import java.text.ParseException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.aliyun.xdragon.biz.AbstractDbTest;
import com.aliyun.xdragon.common.enumeration.RiskLevel;
import com.aliyun.xdragon.common.generate.model.MetricBatchRiskWithBLOBs;
import com.aliyun.xdragon.common.model.metric.request.BatchRiskRequest;
import com.aliyun.xdragon.service.common.util.DateUtil;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

@Import(MetricBatchRiskDao.class)
public class MetricBatchRiskDaoTest extends AbstractDbTest {
    @Autowired
    private MetricBatchRiskDao metricBatchRiskDao;

    @Test
    public void test() throws ParseException {
        Date date = new Date(System.currentTimeMillis() / 1000 * 1000 - 3600000);
        Date curDate = new Date(System.currentTimeMillis() / 1000 * 1000);
        MetricBatchRiskWithBLOBs metricBatchRiskWithBLOBs = new MetricBatchRiskWithBLOBs();
        metricBatchRiskWithBLOBs.setRiskLevel(RiskLevel.CRITICAL.getName());
        metricBatchRiskWithBLOBs.setSourceId(1L);
        metricBatchRiskWithBLOBs.setRiskName("metric1");
        metricBatchRiskWithBLOBs.setAnomaly("spike");
        metricBatchRiskWithBLOBs.setRiskType("异常");
        metricBatchRiskWithBLOBs.setIsMarked(false);
        metricBatchRiskWithBLOBs.setGmtAnomaly(curDate);
        metricBatchRiskWithBLOBs.setRiskLevel(RiskLevel.CRITICAL.getName());

        int res = metricBatchRiskDao.upsertSelective(metricBatchRiskWithBLOBs);
        Date maxDate1 = metricBatchRiskDao.getLatestUpdate(1L);

        Assert.assertTrue(res > 0);
        metricBatchRiskWithBLOBs.setIsMarked(true);
        metricBatchRiskWithBLOBs.setMarkComments("测试");
        int res0 = metricBatchRiskDao.updateSelective(metricBatchRiskWithBLOBs);
        Assert.assertTrue(res0 > 0);

        MetricBatchRiskWithBLOBs metricBatchRiskWithBLOBs1 = new MetricBatchRiskWithBLOBs();
        metricBatchRiskWithBLOBs1.setRiskLevel(RiskLevel.FATAL.getName());
        metricBatchRiskWithBLOBs1.setSourceId(1L);
        metricBatchRiskWithBLOBs1.setRiskName("metric2");
        metricBatchRiskWithBLOBs1.setAnomaly("lost");
        metricBatchRiskWithBLOBs1.setRiskType("特征");
        metricBatchRiskWithBLOBs1.setGmtAnomaly(curDate);
        metricBatchRiskWithBLOBs1.setRiskLevel(RiskLevel.FATAL.getName());
        int res1 = metricBatchRiskDao.batchInsert(Arrays.asList(metricBatchRiskWithBLOBs, metricBatchRiskWithBLOBs1));
        Assert.assertTrue(res1 > 0);

        Date date2 = new Date(System.currentTimeMillis() / 1000 * 1000 + 3600000);
        BatchRiskRequest request = new BatchRiskRequest();
        request.setStartDate(DateUtil.dateString(date));
        request.setEndDate(DateUtil.dateString(date2));
        request.setSearchValue("metric");
        request.setSourceId(1L);
        List<MetricBatchRiskWithBLOBs> list = metricBatchRiskDao.listMetricBatchRisk(request);
        Assert.assertNotNull(list);
        Assert.assertEquals(3, list.size());
        Assert.assertTrue(metricBatchRiskDao.exists(1L, "metric2", curDate));

        long count = metricBatchRiskDao.queryBatchRiskCountByCondition(request);
        Assert.assertEquals(3, count);
        request.setMarks(Arrays.asList(1, 0));
        long count2 = metricBatchRiskDao.queryBatchRiskCountByCondition(request);
        Assert.assertEquals(2, count2);

        Map<String, List<Integer>> ht = metricBatchRiskDao.getHistoryMarkedRisk(date, date2, Arrays.asList("metric1", "metric2"));
        Assert.assertEquals(1, ht.size());

        Map<String, Integer> trend = metricBatchRiskDao.queryBatchRiskTrend(DateUtil.dateString(date),
            DateUtil.dateString(date2), Arrays.asList("异常", "特征"));
        Assert.assertFalse(trend.isEmpty());

        Map<String, Integer> pie = metricBatchRiskDao.queryBatchRiskPie(DateUtil.dateString(date),
            DateUtil.dateString(date2), "risk_type");
        Assert.assertFalse(pie.isEmpty());

        BatchRiskRequest request2 = new BatchRiskRequest();
        request2.setStartDate(DateUtil.dateString(date));
        request2.setEndDate(DateUtil.dateString(date2));
        List<MetricBatchRiskWithBLOBs> list1 = metricBatchRiskDao.listMetricBatchRisk(request2);
        Assert.assertFalse(list1.isEmpty());

        List<MetricBatchRiskWithBLOBs> list2 = metricBatchRiskDao.listNewValidRisk(date, 1L);
        Assert.assertFalse(list2.isEmpty());
        request2.setPageIndex(1);
        request2.setPageSize(1);
        List<MetricBatchRiskWithBLOBs> list3 = metricBatchRiskDao.listMetricBatchRisk(request2);
        Assert.assertEquals(1, list3.size());

        MetricBatchRiskWithBLOBs metricBatchRisk = metricBatchRiskDao.getMetricBatchRisk(list3.get(0).getRiskId());
        Assert.assertNotNull(metricBatchRisk);

        MetricBatchRiskWithBLOBs metricBatchRiskWithBLOBs2 = new MetricBatchRiskWithBLOBs();
        metricBatchRiskWithBLOBs2.setRiskLevel(RiskLevel.FATAL.getName());
        metricBatchRiskWithBLOBs2.setSourceId(1L);
        metricBatchRiskWithBLOBs2.setRiskName("metric2");
        metricBatchRiskWithBLOBs2.setAnomaly("lost");
        metricBatchRiskWithBLOBs2.setRiskType("特征");
        metricBatchRiskWithBLOBs2.setGmtAnomaly(new Date());
        metricBatchRiskWithBLOBs2.setParentId(metricBatchRisk.getRiskId());
        int res3 = metricBatchRiskDao.upsertSelective(metricBatchRiskWithBLOBs2);
        Assert.assertTrue(res3 > 0);
        List<Long> childRiskIds = metricBatchRiskDao.getChildRiskIds(metricBatchRisk.getRiskId());
        Assert.assertFalse(childRiskIds.isEmpty());

        List<MetricBatchRiskWithBLOBs> childRiskList = metricBatchRiskDao.listChildMetricBatchRisk(metricBatchRisk.getRiskId());
        Assert.assertFalse(childRiskList.isEmpty());

        for (MetricBatchRiskWithBLOBs m: list) {
            metricBatchRiskDao.deleteMetricBatchRisk(m.getRiskId());
        }
        metricBatchRiskDao.deleteMetricBatchRisk(metricBatchRiskWithBLOBs2.getRiskId());

        BatchRiskRequest request3 = new BatchRiskRequest();
        request3.setStartDate(DateUtil.dateString(date));
        request3.setEndDate(DateUtil.dateString(date2));
        List<MetricBatchRiskWithBLOBs> list4 = metricBatchRiskDao.listMetricBatchRisk(request3);
        Assert.assertEquals(0, list4.size());

    }
}
