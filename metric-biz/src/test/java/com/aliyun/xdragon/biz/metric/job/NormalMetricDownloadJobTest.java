package com.aliyun.xdragon.biz.metric.job;

import java.io.ByteArrayOutputStream;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.xdragon.biz.log.repository.MetricDailyDao;
import com.aliyun.xdragon.biz.metric.repository.MetricOssDao;
import lombok.SneakyThrows;
import org.apache.commons.math3.util.Pair;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.doNothing;

/**
 * <AUTHOR>
 * @date 2023/11/14
 */

@RunWith(PowerMockRunner.class)
public class NormalMetricDownloadJobTest {

    @InjectMocks
    private NormalMetricDownloadJob normalMetricDownloadJob;

    @Mock
    MetricDailyDao metricDailyDao;

    @Mock
    MetricOssDao metricOssDao;

    @Test
    @SneakyThrows
    public void process() {
        doNothing().when(metricDailyDao).downloadExitMetricAnomaly(anyInt(), anyInt(), any(ByteArrayOutputStream.class),
            argThat(argument -> argument.add(Pair.create("i-001", 9999))));
        JobContext context = JobContext.newBuilder()
            .setTaskName("Metric_download")
            .setDataTime(DateTime.now())
            .setScheduleTime(DateTime.now())
            .build();
        ProcessResult result = normalMetricDownloadJob.process(context, 1695282549, 1695282549);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());
    }

}