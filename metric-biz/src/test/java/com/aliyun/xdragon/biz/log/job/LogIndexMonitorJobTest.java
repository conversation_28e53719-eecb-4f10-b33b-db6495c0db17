package com.aliyun.xdragon.biz.log.job;

import com.alibaba.schedulerx.worker.domain.JobContext;

import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.xdragon.biz.log.model.LogIndexMonitorTask;
import com.aliyun.xdragon.biz.log.service.LogIndexMonitorService;
import com.google.common.collect.Lists;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2022/07/28
 */
@RunWith(PowerMockRunner.class)
public class LogIndexMonitorJobTest {
    @InjectMocks
    private LogIndexMonitorJob job;

    @Mock
    private LogIndexMonitorService logIndexMonitorService;

    @Test
    public void process() throws Exception {
        JobContext context = JobContext.newBuilder().setScheduleTime(new DateTime())
            .setDataTime(new DateTime()).setTaskId(1L).setJobId(1L).build();

        LogIndexMonitorTask task = new LogIndexMonitorTask();
        task.setRegion("cn-wulanchabu");
        task.setLogStore("log_monitor_dev");
        task.setEndpoint("cn-wulanchabu-share.log.xxxx.com");
        task.setProject("ecs-predict-result");
        task.setUser("newbie_ecs");
        when(logIndexMonitorService.getMonitorTask()).thenReturn(Lists.newArrayList(task));

        LogItem item = new LogItem(1243);
        when(logIndexMonitorService.getIndexChangeItem(any(LogIndexMonitorTask.class), anyInt())).thenReturn(item);

        job.process(context);
        verify(logIndexMonitorService, times(1)).writeChangeLog(anyList());
    }
}
