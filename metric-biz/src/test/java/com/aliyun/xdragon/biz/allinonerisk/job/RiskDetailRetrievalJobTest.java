package com.aliyun.xdragon.biz.allinonerisk.job;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.xdragon.biz.allinonerisk.repository.AllinoneRiskDao;
import com.aliyun.xdragon.biz.allinonerisk.repository.AllinoneRiskNcDetailDao;
import com.aliyun.xdragon.biz.allinonerisk.repository.AllinoneRiskVmDetailDao;
import com.aliyun.xdragon.common.enumeration.AllinoneRiskType;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRisk;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetail;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRiskVmDetail;
import com.aliyun.xdragon.service.common.service.AllinoneRiskOdpsService;
import com.aliyun.xdragon.service.common.util.DateUtil;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doReturn;

@RunWith(MockitoJUnitRunner.class)
public class RiskDetailRetrievalJobTest {
    @InjectMocks
    private RiskDetailRetrievalJob riskDetailRetrievalJob;

    @Mock
    private AllinoneRiskOdpsService allinoneRiskOdpsService;

    @Mock
    private AllinoneRiskNcDetailDao allinoneRiskNcDetailDao;

    @Mock
    private AllinoneRiskVmDetailDao allinoneRiskVmDetailDao;

    @Mock
    private AllinoneRiskDao allinoneRiskDao;

    JobContext context = JobContext.newBuilder()
        .setTaskName("MAP_TASK_ROOT")
        .setDataTime(DateTime.now())
        .setScheduleTime(DateTime.now())
        .build();

    @Test
    public void testRootProcess() throws Exception {
        RiskDetailRetrievalJob spyJob = Mockito.spy(riskDetailRetrievalJob);
        doReturn(new ProcessResult(true)).when(spyJob).map(any(), anyString());
        ProcessResult result = spyJob.process(context);
        Assert.assertEquals(result.getStatus(), InstanceStatus.SUCCESS);
    }

    @Before
    public void init() throws InterruptedException {
        Map<String, Map<String, String>> ncInfo = new HashMap<>();
        ncInfo.put("127.0.0.1", new HashMap<String, String>(){{
            put("nc_id", "48-123");
            put("nc_sn", "SJDNUFG22");
            put("nc_ip", "127.0.0.1");
        }});
        Map<String, Map<String, String>> vmInfo = new HashMap<>();
        vmInfo.put("i-12nincuan32u2", new HashMap<String, String>(){{
            put("instance_id", "i-12nincuan32u2");
            put("nc_id", "48-123");
            put("nc_sn", "SJDNUFG22");
            put("nc_ip", "127.0.0.1");
        }});

        Mockito.when(allinoneRiskOdpsService.getNcInfo(anyString(), anyString(), anyList())).thenReturn(ncInfo);
        Mockito.when(allinoneRiskOdpsService.getVmInfo(anyString(), anyString(), anyList())).thenReturn(vmInfo);

        Mockito.when(allinoneRiskNcDetailDao.getNcDetailFromMap(any())).thenReturn(new AllinoneRiskNcDetail());
        Mockito.when(allinoneRiskVmDetailDao.getVmDetailFromMap(any())).thenReturn(new AllinoneRiskVmDetail());
    }

    @Test
    public void commonTest() throws Exception{
        JobContext context = JobContext.newBuilder()
            .setDataTime(DateTime.now())
            .setScheduleTime(DateTime.now())
            .build();

        int dataTs = new Long(DateUtil.datetime2Timestamp("2024-11-19 00:00:00", false)).intValue();
        int schedTs = dataTs;

        AllinoneRisk allinoneRisk = new AllinoneRisk();
        allinoneRisk.setRiskName("test");
        allinoneRisk.setRiskId(12345L);
        allinoneRisk.setRiskType(AllinoneRiskType.LIMIT.getName());

        AllinoneRiskVmDetail allinoneRiskVmDetail = new AllinoneRiskVmDetail();
        allinoneRiskVmDetail.setInstanceId("i-12nincuan32u2");
        allinoneRiskVmDetail.setVmDetailId(123L);
        allinoneRiskVmDetail.setExceptionName("exceptionName-test");
        allinoneRiskVmDetail.setExceptionTime("2024-12-12 12:12:12");
        allinoneRiskVmDetail.setRecoverTime("2024-12-12 12:15:12");

        Mockito.when(allinoneRiskVmDetailDao.listRiskVmDetail(any(), eq(0), any())).thenReturn(Arrays.asList(allinoneRiskVmDetail, allinoneRiskVmDetail));
        Mockito.when(allinoneRiskNcDetailDao.batchUpsert(anyList())).thenReturn(3);
        Mockito.when(allinoneRiskVmDetailDao.batchUpsert(anyList())).thenReturn(4);
        Mockito.when(allinoneRiskDao.listRisks(any(), any(), anyList())).thenReturn(new ArrayList<>());

        ProcessResult result1 = riskDetailRetrievalJob.processSubTask(context, dataTs, schedTs, AllinoneRiskType.LIMIT);
        Assert.assertEquals("RiskDetailRetrievalJob get 0 records for limit", result1.getResult());
        Assert.assertEquals(result1.getStatus(), InstanceStatus.SUCCESS);

        Mockito.when(allinoneRiskDao.listRisks(any(), any(), anyList())).thenReturn(new ArrayList<>(Collections.nCopies(10, allinoneRisk)));
        AllinoneRiskVmDetail vmDetail = new AllinoneRiskVmDetail();
        vmDetail.setInstanceId("i-12nincuan32u2");
        vmDetail.setExceptionName("exceptionName-test");
        vmDetail.setExceptionTime("2024-12-12 12:12:12");
        vmDetail.setRecoverTime("2024-12-12 12:15:12");
        vmDetail.setVmDetailId(123L);
        Mockito.when(allinoneRiskVmDetailDao.listRiskVmDetail(any(), eq(0), any())).thenReturn(new ArrayList<>(Collections.nCopies(2, vmDetail)));
        AllinoneRiskNcDetail ncDetail = new AllinoneRiskNcDetail();
        ncDetail.setNcDetailId(123L);
        ncDetail.setNcIp("127.0.0.1");
        ncDetail.setExceptionName("exceptionName-test");
        ncDetail.setExceptionTime("2024-12-12 12:12:12");
        ncDetail.setRecoverTime("2024-12-12 12:15:12");
        Mockito.when(allinoneRiskNcDetailDao.listRiskNcDetail(any(), eq(0), any())).thenReturn(new ArrayList<>(Collections.nCopies(2, ncDetail)));
        ProcessResult result2 = riskDetailRetrievalJob.processSubTask(context, dataTs, schedTs, AllinoneRiskType.LIMIT);
        Assert.assertEquals(result2.getStatus(), InstanceStatus.SUCCESS);

        Mockito.when(allinoneRiskDao.listRisks(any(), any(), anyList())).thenThrow(new RuntimeException());
        ProcessResult result3 = riskDetailRetrievalJob.processSubTask(context, dataTs, schedTs, AllinoneRiskType.LIMIT);
        Assert.assertEquals("RiskDetailRetrievalJob get 0 records for limit", result3.getResult());

    }

    @Test
    public void releaseRiskTest() throws Exception{
        JobContext context = JobContext.newBuilder()
            .setDataTime(DateTime.now())
            .setScheduleTime(DateTime.now())
            .build();

        int dataTs = new Long(DateUtil.datetime2Timestamp("2024-11-19 00:00:00", false)).intValue();
        int schedTs = dataTs;
        ProcessResult res1 = riskDetailRetrievalJob.processSubTask(context, dataTs, schedTs, AllinoneRiskType.DEPLOY);
        Assert.assertEquals("RiskDetailRetrievalJob get 0 records for deploy", res1.getResult());

        AllinoneRisk allinoneRisk = new AllinoneRisk();
        allinoneRisk.setRiskName("test");
        allinoneRisk.setRiskId(12345L);
        allinoneRisk.setRiskType(AllinoneRiskType.FAILURE.getName());
        Mockito.when(allinoneRiskDao.insertSelective(any())).thenReturn(allinoneRisk);
        Mockito.when(allinoneRiskOdpsService.getReleaseRisks(anyString())).thenReturn(Arrays.asList(allinoneRisk));

        ProcessResult res2 = riskDetailRetrievalJob.processSubTask(context, dataTs, schedTs, AllinoneRiskType.DEPLOY);
        Assert.assertEquals(res2.getStatus(), InstanceStatus.SUCCESS);

        // 测试插入失败
        Mockito.when(allinoneRiskDao.insertSelective(any(AllinoneRisk.class))).thenReturn(null);
        ProcessResult res3 = riskDetailRetrievalJob.processSubTask(context, dataTs, schedTs, AllinoneRiskType.DEPLOY);
        Assert.assertEquals(res3.getStatus(), InstanceStatus.SUCCESS);

        // 测试无risk，会打印日志
        Mockito.when(allinoneRiskDao.insertSelective(any(AllinoneRisk.class))).thenReturn(allinoneRisk);
        ProcessResult res4 = riskDetailRetrievalJob.processSubTask(context, dataTs, schedTs, AllinoneRiskType.DEPLOY);
        Assert.assertEquals(res4.getStatus(), InstanceStatus.SUCCESS);
    }
}
