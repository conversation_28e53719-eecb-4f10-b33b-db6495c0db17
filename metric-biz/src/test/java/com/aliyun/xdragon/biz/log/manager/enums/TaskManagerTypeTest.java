package com.aliyun.xdragon.biz.log.manager.enums;

import com.aliyun.xdragon.common.enumeration.log.TaskManagerType;
import org.junit.Assert;
import org.junit.Test;

public class TaskManagerTypeTest {
    @Test
    public void testTaskManagerType() {
        TaskManagerType type = TaskManagerType.None;
        Assert.assertEquals(new Integer(0), type.getCode());
        Assert.assertEquals("none", type.getName());

        type = TaskManagerType.Blink;
        Assert.assertEquals(new Integer(1), type.getCode());
        Assert.assertEquals("blink", type.getName());

        type = TaskManagerType.Flink;
        Assert.assertEquals(new Integer(2), type.getCode());
        Assert.assertEquals("flink", type.getName());
    }
}
