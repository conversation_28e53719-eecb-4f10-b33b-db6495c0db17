package com.aliyun.xdragon.biz.log.service;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.PutLogsResponse;
import com.aliyun.xdragon.biz.log.model.BasicAnomaly;
import com.aliyun.xdragon.common.generate.c9.model.NcDo;
import java.util.*;

import com.aliyun.xdragon.biz.log.config.TsdbConfig;
import com.aliyun.xdragon.biz.log.service.impl.PickUpAnomalyOpratorImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
public class PickUpAnomalyOperatorTest {
    @InjectMocks
    PickUpAnomalyOpratorImpl pickUpAnomalyOprator;

    @Mock
    TsdbConfig tsdbConfig;

    @Mock
    Client client;

    @Mock
    PutLogsResponse response;

    @Mock
    MetricsPickupService metricsPickupService;

    @Before
    public void setup() {
        pickUpAnomalyOprator.init();
    }

    @Test
    public void testWriteToTSDB1() throws LogException {
        Mockito.when(metricsPickupService.getTsdbClient()).thenReturn(client);
        Mockito.when(tsdbConfig.getProject()).thenReturn("ecs-xunjian");
        Mockito.when(tsdbConfig.getLogStorePickUp()).thenReturn("xdragon-metric-pickup-anomaly");

        Mockito.when(client.PutLogs(anyString(),anyString(),anyString(),anyList(),anyString())).thenReturn(response);
        List<BasicAnomaly> anomalies = new ArrayList<>();

        boolean success = pickUpAnomalyOprator.writeAnomaly(anomalies);
        assert success;

        NcDo status = new NcDo();
        BasicAnomaly anomaly = new BasicAnomaly();
        anomaly.setNcAnomaly(status, "123", "123", "123", 1000, "nc");
        anomalies.add(anomaly);
        success = pickUpAnomalyOprator.writeAnomaly(anomalies);
        assert success;

    }

    @Test
    public void testWriteToTSDB2() throws LogException {
        Mockito.when(metricsPickupService.getTsdbClient()).thenReturn(client);
        Mockito.when(tsdbConfig.getProject()).thenReturn("ecs-xunjian");
        Mockito.when(tsdbConfig.getLogStorePickUp()).thenReturn("xdragon-metric-pickup-anomaly");

        Mockito.when(client.PutLogs(anyString(),anyString(),anyString(),anyList(),anyString())).thenReturn(response);
        List<BasicAnomaly> anomalies = new ArrayList<>();
        boolean success = pickUpAnomalyOprator.writeAnomaly(anomalies);
        assert success;

        BasicAnomaly anomaly = new BasicAnomaly();
        anomaly.setRegionAnomaly("123", "123", "123", "123", 100,100, "region");
        anomalies.add(anomaly);
        success = pickUpAnomalyOprator.writeAnomaly(anomalies);
        assert success;

    }

    @Test
    public void testWriteToTSDBException() throws LogException {
        Mockito.when(metricsPickupService.getTsdbClient()).thenReturn(client);
        Mockito.when(tsdbConfig.getProject()).thenReturn("ecs-xunjian");
        Mockito.when(tsdbConfig.getLogStorePickUp()).thenReturn("xdragon-metric-pickup-anomaly");

        Mockito.when(client.PutLogs(anyString(),anyString(),anyString(),anyList(),anyString(),anyString())).thenThrow(new RuntimeException());

        List<BasicAnomaly> anomalies = new ArrayList<>();
        BasicAnomaly anomaly = new BasicAnomaly();
        anomaly.setRegionAnomaly("123", "123", "123", "123", 100,100, "region");
        anomalies.add(anomaly);

        boolean success = pickUpAnomalyOprator.writeAnomaly(anomalies);

        assert !success;

    }
}
