package com.aliyun.xdragon.biz.chat;

import com.aliyun.xdragon.biz.BaseTestCase;
import org.junit.Ignore;
import org.junit.Test;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;


public class ChatBaiLianServiceImplTest extends BaseTestCase {
    @Resource
    private ChatBailianServiceImpl chatBailianServiceImpl;


//    @Test
//    @Ignore
//    public void testDiag() {
//        XdragonMetricResponse<String> response = chatBailianServiceImpl.diag("帮我看一下 47.243.56.124 的 NC 信息");
//        System.out.println(response);
//    }

    @Test
    @Ignore
    public void testInvokeMethod() {
        try {
            // 测试 String.format 静态方法
            Object result = chatBailianServiceImpl.invokeMethod("java.lang.String", "format", "[\"%s is %d years old.\", \"Alice\", 30]");
            System.out.println("String.format result: " + result);
            // 测试 Integer.parseInt 静态方法
            result = chatBailianServiceImpl.invokeMethod("java.lang.Integer", "parseInt", "[\"123\"]");
            System.out.println("Integer.parseInt result: " + result);
            // 测试 Date.getTime 实例方法
            result = chatBailianServiceImpl.invokeMethod("java.util.Date", "getTime", "[]");
            System.out.println("Date.getTime result: " + result);
            // 测试 OpsInfoRetrievalServiceImpl.getVmInfo 实例方法
            result = chatBailianServiceImpl.invokeMethod("com.aliyun.xdragon.biz.workitem.service.impl.OpsInfoRetrievalServiceImpl", "getVmInfo", "[\"i-2zee5jgu555atieugihk\", [\"status\"]]");
            System.out.println("OpsInfoRetrievalServiceImpl.getVmInfo result: " + result);
            result = chatBailianServiceImpl.invokeMethod("com.aliyun.xdragon.biz.workitem.service.impl.OpsInfoRetrievalServiceImpl", "getVmInfo", "[\"i-2zee5jgu555atieugihk\", [\"status\"]]");
            System.out.println("OpsInfoRetrievalServiceImpl.getVmInfo result: " + result);
            result = chatBailianServiceImpl.invokeMethod("com.aliyun.xdragon.biz.workitem.service.impl.OpsInfoRetrievalServiceImpl", "getVmInfo", "[\"i-2zee5jgu555atieugihk\", [\"status\"]]");
            System.out.println("OpsInfoRetrievalServiceImpl.getVmInfo result: " + result);
            result = chatBailianServiceImpl.invokeMethod("com.aliyun.xdragon.biz.workitem.service.impl.OpsInfoRetrievalServiceImpl", "getVmInfo", "[\"i-2zee5jgu555atieugihk\", [\"status\"]]");
            System.out.println("OpsInfoRetrievalServiceImpl.getVmInfo result: " + result);
        } catch (InvocationTargetException e) {
            e.getCause().printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testInvokeMethodv2() {
        String classpath = "com.aliyun.xdragon.biz.chat.ChatDiagnoseServiceImpl";
        String methodName = "diagnoseWithConfig";
        String params = "{\"vm\":\"i-2ze7roap9qloyg3b8dv7\",\"startTime\": \"2024-12-27 09:00:00\", \"endTime\": \"2024-12-27 11:00:00\"}";
        try {
            Object result = chatBailianServiceImpl.invokeMethodv2(classpath, methodName, null, params);
            System.out.println("result: " + result);
        }catch (Exception e){
            e.printStackTrace();
        }

        classpath = "com.aliyun.xdragon.biz.workitem.service.impl.OpsInfoRetrievalServiceImpl";
        methodName = "getOpsEvents";
        params = "{\"vm\":\"i-2ze7roap9qloyg3b8dv7\",\"startTime\": \"2024-12-27 09:00:00\", \"endTime\": \"2024-12-27 11:00:00\"}";
        try {
            Object result = chatBailianServiceImpl.invokeMethodv2(classpath, methodName,null, params);
            System.out.println("result: " + result);
        }catch (Exception e){
            e.printStackTrace();
        }

    }
}