package com.aliyun.xdragon.biz.log.processor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.aliyun.xdragon.biz.log.repository.AnomalyStoreDao;
import com.aliyun.xdragon.biz.log.repository.ClusterTaskDao;
import com.aliyun.xdragon.biz.log.repository.LogDetailStatDao;
import com.aliyun.xdragon.biz.log.repository.LogOverallStatDao;
import com.aliyun.xdragon.biz.log.repository.LogUnionTaskDao;
import com.aliyun.xdragon.biz.log.repository.PatternDao;
import com.aliyun.xdragon.biz.log.repository.PatternTagDao;
import com.aliyun.xdragon.biz.log.service.PatternService;
import com.aliyun.xdragon.common.generate.model.LogClusterTask;
import com.aliyun.xdragon.common.generate.model.LogDetailStatistics;
import com.aliyun.xdragon.common.generate.model.LogOverallStatistics;
import com.aliyun.xdragon.common.generate.model.LogUnionTask;
import com.aliyun.xdragon.common.model.log.DayTimeRange;
import com.aliyun.xdragon.common.model.log.LogStatDetail;
import com.aliyun.xdragon.common.model.log.LogStatInfo;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class LogClusterScheduleStatProcessorTest {
    @InjectMocks
    private LogScheduleStatProcessor processor;

    @Mock
    private ClusterTaskDao clusterTaskDao;

    @Mock
    private LogUnionTaskDao logUnionTaskDao;

    @Mock
    private PatternDao patternDao;

    @Mock
    private PatternService patternService;

    @Mock
    private PatternTagDao patternTagDao;

    @Mock
    private AnomalyStoreDao anomalyStoreDao;

    @Mock
    private LogOverallStatDao overallStatDao;

    @Mock
    private LogDetailStatDao detailStatDao;

    @Test
    public void testProcess() {
        Date start = new Date(1665072000000L);
        Date end = new Date(1665590400000L);

        List<LogClusterTask> taskList = new ArrayList<>(1);
        LogClusterTask task = new LogClusterTask();
        task.setTaskId(3L);
        taskList.add(task);
        LogOverallStatistics overall = new LogOverallStatistics();
        LogDetailStatistics detail = new LogDetailStatistics();
        List<LogDetailStatistics> detailList = new ArrayList<>(1);
        detailList.add(detail);

        when(clusterTaskDao.countTask(any(), any(), anyBoolean())).thenReturn(1L);
        when(clusterTaskDao.listTask(any(), any(), anyBoolean())).thenReturn(taskList);
        when(logUnionTaskDao.getUnionTasksByStatus(any(), any(), anyBoolean())).thenReturn(Lists.newArrayList(new LogUnionTask()));
        when(patternService.getSum(any(), any(Date.class), any(Date.class), any(), anyBoolean())).thenReturn(1L);
        when(patternDao.getTypeCnt(any(), any(Date.class), any(Date.class), any(), anyBoolean())).thenReturn(null);
        when(patternDao.getNewTypeCnt(any(), any(Date.class), any(Date.class), any(Date.class), any(Date.class), any(),
            anyBoolean())).thenReturn(null);
        when(patternTagDao.queryPatternMd5WithFollowTag(anyLong(), any(), any(), any())).thenReturn(
            Collections.emptyMap());
        when(patternTagDao.queryPatternMd5WithIgnoreTag(anyLong(), any(), any(), any())).thenReturn(
            Collections.emptyMap());
        when(patternTagDao.queryPatternMd5WithAlarmTag(anyLong(), any(), any(), any())).thenReturn(
            Collections.emptyMap());
        when(anomalyStoreDao.countAnomalyLog(any(), anyInt(), anyInt(), any(), anyBoolean())).thenReturn(1L);
        when(anomalyStoreDao.countAnomalyLogTask(any(), anyInt(), anyInt())).thenReturn(1L);
        when(anomalyStoreDao.countAnomalyLogWithMd5Set(any(), anyInt(), anyInt(), any())).thenReturn(1L);
        when(anomalyStoreDao.queryAnomalyDataByMetricPrefix(any(), anyString(), any(), anyInt(), anyInt(), anyInt(), anyInt()))
            .thenReturn(Collections.emptyList());
        doNothing().when(overallStatDao).addStatistic(any(LogStatInfo.class));
        doNothing().when(detailStatDao).addStatistic(any(Date.class), any(Date.class), any(LogStatDetail.class));
        processor.process(DayTimeRange.of(start, end));
    }
}