package com.aliyun.xdragon.biz.metric.job;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.odps.data.Record;
import com.aliyun.xdragon.biz.metric.repository.MetricDetailDao;
import com.aliyun.xdragon.biz.metric.repository.MetricSourceDao;
import com.aliyun.xdragon.common.generate.model.MetricSource;
import com.aliyun.xdragon.service.common.agent.OdpsClient;
import com.aliyun.xdragon.service.common.util.DateUtil;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
public class MetricPeriodSyncJobTest {
    @InjectMocks
    private MetricPeriodSyncJob metricPeriodSyncJob;

    @Mock
    private OdpsClient odpsClient;

    @Mock
    private Record record;

    @Mock
    private MetricDetailDao metricDetailDao;

    @Mock
    private MetricSourceDao metricSourceDao;
    @Test
    public void processTest() throws Exception{
        JobContext context = JobContext.newBuilder()
                .setDataTime(DateTime.now())
                .setScheduleTime(DateTime.now())
                .build();

        Mockito.when(record.getString("metric")).thenReturn("metricTest");
        Mockito.when(record.getString("period_length")).thenReturn("48");
        Mockito.when(record.getString("logstore")).thenReturn("xdragon-metric");
        Mockito.when(record.getString("score")).thenReturn("50");
        Mockito.when(record.getString("missing_rate")).thenReturn("5");
        Mockito.when(metricDetailDao.batchInsertOrUpdateMetric(anyList(), any())).thenReturn(10);

        Mockito.when(metricSourceDao.listPeriodMetricSource()).thenReturn(new ArrayList<>());
        ProcessResult result = metricPeriodSyncJob.process(context);
        Assert.assertEquals(result.getStatus(), InstanceStatus.SUCCESS);

        Mockito.when(odpsClient.runSql(anyString())).thenReturn(new ArrayList<>());
        Mockito.when(metricSourceDao.listPeriodMetricSource()).thenReturn(getSources());
        ProcessResult result1 = metricPeriodSyncJob.process(context);
        Assert.assertEquals(result1.getStatus(), InstanceStatus.SUCCESS);

        String sql = "SELECT    metric, period_length, logstore, score, missing_rate\n" +
                "     FROM   ecs_dw.xdragon_metric_period_length t\n" +
                "     WHERE  ds = %s and score >= %s and missing_rate <= %s and period_length > 0\n" +
                "     LIMIT %s,%s;";

        Date dsDate = new Date();
        // 获取昨天的日期作为同步的数据时间
        String ds = DateUtil.dateFormat(DateUtil.modifyDateByDay(dsDate, -1), "yyyyMMdd");
        Mockito.when(odpsClient.runSql(String.format(sql, ds, MetricPeriodSyncJob.SCORE_THRESHOLD, MetricPeriodSyncJob.MISSING_RATE_THRESHOLD,
                0, MetricPeriodSyncJob.ODPS_PAGE_SIZE))).thenReturn(new ArrayList<>(Collections.nCopies(1000, record)));
        Mockito.when(odpsClient.runSql(String.format(sql, ds, MetricPeriodSyncJob.SCORE_THRESHOLD, MetricPeriodSyncJob.MISSING_RATE_THRESHOLD,
                1000, MetricPeriodSyncJob.ODPS_PAGE_SIZE))).thenReturn(new ArrayList<>(Collections.nCopies(10, record)));
        ProcessResult result2 = metricPeriodSyncJob.process(context);
        Assert.assertEquals(result2.getStatus(), InstanceStatus.SUCCESS);

        Mockito.when(record.getString("metric")).thenReturn("metricTes");
        Mockito.when(odpsClient.runSql(anyString())).thenReturn(new ArrayList<>(Collections.nCopies(5, record)));
        ProcessResult result3 = metricPeriodSyncJob.process(context);
        Assert.assertEquals(result3.getStatus(), InstanceStatus.SUCCESS);
    }

    private List<MetricSource> getSources() {
        MetricSource source1 = new MetricSource();
        source1.setSourceId(1L);
        source1.setSourceName("metric1");
        source1.setSourceLogstore("xdragon-metric");

        MetricSource source2 = new MetricSource();
        source2.setSourceName("metric2");
        source2.setSourceId(2L);
        source2.setSourceLogstore("xdragon-metric");

        return new ArrayList<>(Arrays.asList(source1, source2));
    }
}
