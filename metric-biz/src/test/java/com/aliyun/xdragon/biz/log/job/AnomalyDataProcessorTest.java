package com.aliyun.xdragon.biz.log.job;

import java.util.LinkedList;
import java.util.List;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.xdragon.common.generate.model.AnomalyDataProcess;
import com.aliyun.xdragon.common.model.AnomalyData;
import com.aliyun.xdragon.service.common.service.PostProcessor;
import com.aliyun.xdragon.service.common.service.ProcessorDispatcher;
import com.aliyun.xdragon.biz.log.repository.AnomalyDataProcessDao;
import com.aliyun.xdragon.biz.log.repository.AnomalyStoreDao;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2022/09/06
 */
@RunWith(PowerMockRunner.class)
public class AnomalyDataProcessorTest {
    @InjectMocks
    private AnomalyDataProcessor job;

    @Mock
    private AnomalyStoreDao anomalyStoreDao;

    @Mock
    private AnomalyDataProcessDao anomalyDataProcessDao;

    @Mock
    private ProcessorDispatcher dispatcher;

    @Test
    public void dispatch() throws Exception {
        AnomalyDataProcess d = new AnomalyDataProcess();
        d.setMetricPrefix("logcluster/");
        d.setProcessorName("logcluster");
        d.setRunInterval(30);
        d.setLastRunTime(120000L);
        AnomalyDataProcess d2 = new AnomalyDataProcess();
        d2.setMetricPrefix("abc/");
        d2.setProcessorName("logcluster");
        d2.setRunInterval(30);
        d2.setLastRunTime(123000L);
        when(anomalyDataProcessDao.listTasks()).thenReturn(Lists.newArrayList(d, d2));
        List<AnomalyDataProcess> tasks = job.genTask(null, 150);
        Assert.assertEquals(1, tasks.size());
        Assert.assertEquals("logcluster/", tasks.get(0).getMetricPrefix());
    }

    @Test
    public void processor() throws Exception {
        PostProcessor postProcessor = mock(PostProcessor.class);
        when(dispatcher.getProcessorByName(anyString())).thenReturn(null).thenReturn(postProcessor);
        AnomalyDataProcess d = new AnomalyDataProcess();
        d.setMetricPrefix("logcluster/");
        d.setProcessorName("logcluster");
        d.setRunInterval(30);
        d.setLastRunTime(0L);
        List<AnomalyData> dataList = new LinkedList<>();
        for (int i = 0; i < 25; i++) {
            AnomalyData d1 = new AnomalyData();
            d1.setMetric("logcluster/1/1/center/ecs");
            d1.setTimestamp(1234);
            d1.setAnomaly("dip");
            dataList.add(d1);
        }
        when(anomalyStoreDao.queryAnomalyDataByMetricPrefix(any(), anyString(), any(), anyInt(), anyInt(), anyInt(),
            anyInt())).thenReturn(dataList);
        ProcessResult result = job.processSubTask(null, 123, 123, d);
        Assert.assertEquals(result.getStatus(), InstanceStatus.FAILED);
        when(anomalyStoreDao.queryAnomalyDataByMetricPrefix(any(), anyString(), any(), anyInt(), anyInt(), anyInt(),
            anyInt())).thenReturn(dataList);
        result = job.processSubTask(null, 123, 123, d);
        Thread.sleep(1000L);
        Assert.assertEquals(result.getStatus(), InstanceStatus.SUCCESS);
    }
}
