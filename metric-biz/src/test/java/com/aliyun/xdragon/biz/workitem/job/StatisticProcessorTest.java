package com.aliyun.xdragon.biz.workitem.job;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.odps.Column;
import com.aliyun.odps.data.ArrayRecord;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.type.TypeInfoFactory;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.aliyun.xdragon.biz.workitem.repository.WorkItemClassificationDetailDao;
import com.aliyun.xdragon.biz.workitem.repository.WorkItemStatisticsDao;
import com.aliyun.xdragon.common.generate.model.WorkItemLabel;
import com.aliyun.xdragon.common.generate.model.WorkItemStatistics;
import com.aliyun.xdragon.service.common.agent.OdpsClient;
import com.aliyun.xdragon.service.common.config.sls.NbSlsClientFactoryService;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.when;

import com.aliyun.openservices.log.Client;
import org.powermock.reflect.Whitebox;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RunWith(PowerMockRunner.class)
@PrepareForTest
public class StatisticProcessorTest {

    @InjectMocks
    private StatisticProcessor processor;

    @Mock
    private WorkItemStatisticsDao workItemStatisticsDao;

    @Mock
    private OdpsClient odpsClient;

    @Mock
    private NbSlsClientFactoryService slsClientFactory;

    @Mock
    private WorkItemClassificationDetailDao workItemClassificationDetailDao;

    Client client;

    @Before
    public void before() {
        List<WorkItemLabel> labels = new ArrayList<>();
        WorkItemLabel label1 = new WorkItemLabel();
        label1.setName("llc争抢");
        label1.setId(1L);
        WorkItemLabel label2 = new WorkItemLabel();
        label2.setName("avs发布");
        label2.setId(2L);
        WorkItemLabel label3 = new WorkItemLabel();
        label3.setName("qemu发布");
        label3.setId(3L);
        WorkItemLabel label4 = new WorkItemLabel();
        label4.setName("fpga发布");
        label4.setId(4L);
        labels.add(label1);
        labels.add(label2);
        labels.add(label3);
        labels.add(label4);
        when(workItemClassificationDetailDao.queryLabelByName(anyList(), isNull())).thenReturn(labels);
        processor.initClient();
        client = PowerMockito.mock(Client.class);
        Whitebox.setInternalState(processor, "releaseClient", client);
    }


    @Test
    public void processTest() throws Exception {
        JobContext context = JobContext
                .newBuilder()
                .setDataTime(DateTime.parse("2022-09-06T09:00:00.000+08:00"))
                .setScheduleTime(DateTime.parse("2022-09-06T09:00:00.000+08:00"))
                .build();
        Map<String, Object> map1 = new HashMap<>();
        map1.put("data_source", "aone_workitem");
        map1.put("dt", "2023-08-06");
        map1.put("cnt", 6);
        Map<String, Object> map2 = new HashMap<>();
        map2.put("label_id", "1");
        map2.put("dt", "2023-08-06");
        map2.put("cnt", 6);
        WorkItemStatistics record = new WorkItemStatistics();
        record.setDt("2023-08-06");
        record.setType(2);
        record.setObjectValue(4.0);
        List<WorkItemStatistics> statistics = Arrays.asList(record, record);
        when(workItemClassificationDetailDao.groupByCount(anyString(), anyString(), anyString())).thenReturn(Arrays.asList(map1));
        when(workItemClassificationDetailDao.groupByLabel(anyString(), anyString(), anyString())).thenReturn(Arrays.asList(map2));
        when(workItemStatisticsDao.queryByDetail(eq("1"),  eq("20230806"), eq(2))).thenReturn(statistics);
        contentionCountTest();
        publishingCountTest();
        ProcessResult result = processor.process(context);
        Assert.assertTrue(result.getStatus().isFinish());
    }

    @Test
    public void publishingCountTest() throws LogException {
        Map<String, String> headers = new HashMap<>();
        headers.put("x-log-progress", "Complete");
        GetLogsResponse response = new GetLogsResponse(headers);
        List<QueriedLog> logs = new ArrayList<>();
        ArrayList<LogContent> contents = new ArrayList<>();
        LogContent logContent1 = new LogContent("deploy_service", "ecs_network.libvswitch_sr#");
        LogContent logContent2 = new LogContent("cnt", "1000");
        contents.add(logContent1);
        contents.add(logContent2);
        LogItem logItem = new LogItem(1, contents);
        QueriedLog queriedLog = new QueriedLog("sls", logItem);
        logs.add(queriedLog);
        logs.add(queriedLog);
        response.SetLogs(logs);
        when(client.GetLogs(anyString(), anyString(), anyInt(), anyInt(), anyString(), anyString())).thenReturn(response);
        Map<Long, Integer> result = processor.publishingCount(0, 1);
        Assert.assertTrue(result.size() > 0);
    }

    @Test
    public void contentionCountTest() {
        Column[] columns = new Column[] {new Column("cnt", TypeInfoFactory.STRING)};
        Object[] values = new Object[] {"100"};
        Record r = new ArrayRecord(columns, values);
        List<Record> records = Arrays.asList(r);
        when(odpsClient.runSql(anyString())).thenReturn(records);
        String ds = "20230824";
        Map<Long, Integer> result = processor.contentionCount(ds);
        Assert.assertTrue(result.size() > 0);
    }
}
