package com.aliyun.xdragon.biz.chat.handle;

import com.aliyun.xdragon.biz.BaseTestCase;
import com.aliyuncs.ecsinc.model.v20160314.InnerDescribeDiagnosticReportAttributesResponse;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;

/**
 * EcsDiagnosticServiceImplTest
 *
 * <AUTHOR>
 * @date 11/6/24
 */
public class EcsDiagnosticServiceImplTest extends BaseTestCase {
    @Resource
    private EcsDiagnosticServiceImpl ecsDiagnosticService;

    @Test
    public void testDiagnosticInit() {
        InnerDescribeDiagnosticReportAttributesResponse.MetricResult  mr = new InnerDescribeDiagnosticReportAttributesResponse.MetricResult();
        mr.setIssues(Arrays.asList(new InnerDescribeDiagnosticReportAttributesResponse.MetricResult.Issue(){{
            setIssueId("GuestOS.Filesystems.UUIDConflicts");
        }}));
        mr.setMetricId("GuestOS.FileSystems");
        Map<String, String> result = ecsDiagnosticService.getZhCnByReport(Arrays.asList(mr));
        Assert.assertEquals(2, result.size());
        Assert.assertNotNull(result.get("GuestOS.Filesystems.UUIDConflicts"));
        Assert.assertNotNull(result.get("GuestOS.FileSystems"));
    }
}
