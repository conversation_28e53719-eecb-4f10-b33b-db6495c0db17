package com.aliyun.xdragon.biz.log.processor;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.xdragon.biz.log.repository.LogUnionTaskDao;
import com.aliyun.xdragon.common.enumeration.log.ClusterTaskStatus;
import com.aliyun.xdragon.common.generate.model.LogClusterConfigRegion;
import com.aliyun.xdragon.common.generate.model.LogUnionTask;
import com.aliyun.xdragon.common.model.log.PatternTypeCnt;
import com.aliyun.xdragon.biz.log.model.JobErrorCode;
import com.aliyun.xdragon.biz.log.repository.ClusterRegionConfigDao;
import com.aliyun.xdragon.biz.log.repository.PatternDao;
import com.aliyun.xdragon.biz.log.service.TsdbOperatorService;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class PatternTypeCntProcessorTest {
    @Mock
    PatternDao patternDao;

    @Mock
    ClusterRegionConfigDao configDao;

    @Mock
    LogUnionTaskDao logUnionTaskDao;

    @Mock
    TsdbOperatorService tsdbOperatorService;

    @InjectMocks
    PatternTypeCntProcessor processor;

    @Test
    public void processTest() throws ParseException, LogException {
        LogClusterConfigRegion config = new LogClusterConfigRegion();
        config.setTaskId(1L);
        config.setAlgParam("{\"waterMarker\":30000,\"windowSize\":15,\"support\":5,\"rsupport\":5,\"cutLength\":50,"
            + "\"cutLines\":5,\"weightThreshold\":0.04,\"preprocess\":true,\"regExps\":{\"number\":[],\"ip\":[],"
            + "\"url\":[]}}");
        LogClusterConfigRegion configDummy = new LogClusterConfigRegion();
        configDummy.setTaskId(1L);
        configDummy.setAlgParam("{\"waterMarker\":30000,\"windowSize\":15,\"support\":5,\"rsupport\":5,\"cutLength\":50,"
                + "\"cutLines\":5,\"weightThreshold\":0.04,\"preprocess\":true,\"regExps\":{\"number\":[],\"ip\":[],"
                + "\"url\":[]}}");
        List<LogClusterConfigRegion> configList = new ArrayList<>(1);
        configList.add(config);
        configList.add(configDummy);
        PatternTypeCnt patternTypeCnt = new PatternTypeCnt();
        patternTypeCnt.setTaskId(1L);
        patternTypeCnt.setRegion("center");
        patternTypeCnt.setCnt(233L);
        List<PatternTypeCnt> recordList = new ArrayList<>(1);
        recordList.add(patternTypeCnt);

        when(configDao.listAllConfigsWithStatus(any(ClusterTaskStatus.class), any())).thenReturn(configList);

        LogUnionTask logUnionTask = new LogUnionTask();
        logUnionTask.setSlsRegion("center");
        when(logUnionTaskDao.getUnionTasksByStatus(anyLong(), any(), anyBoolean())).thenReturn(Lists.newArrayList(logUnionTask));
        when(patternDao.getPatternTypeCnt(any(), any(), any())).thenReturn(recordList);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date runDate = format.parse("2022-10-01 00:30:00");
        JobErrorCode code;
        code = processor.run(runDate.getTime());
        Assert.assertEquals(JobErrorCode.Fail, code);

        when(tsdbOperatorService.writeToTSDB(any(), anyString(), anyInt(), eq(null))).thenReturn(true);
        code = processor.run(runDate.getTime());
        Assert.assertEquals(JobErrorCode.Success, code);
    }
}
