package com.aliyun.xdragon.biz.chat.util;

import org.junit.Assert;
import org.junit.Test;

public class ParamCheckUtilTest {
    @Test
    public void testIsUid() {
        Assert.assertTrue(ParamCheckUtil.isUid("1437938940453106"));
        Assert.assertFalse(ParamCheckUtil.isUid(""));
        Assert.assertFalse(ParamCheckUtil.isUid("1.2.3.4"));
        Assert.assertFalse(ParamCheckUtil.isUid("123131312312"));
    }

    @Test
    public void testIsNcId() {
        Assert.assertTrue(ParamCheckUtil.isNcId("31228-674"));
        Assert.assertFalse(ParamCheckUtil.isNcId(""));
        Assert.assertFalse(ParamCheckUtil.isNcId("127.0.0.1"));
        Assert.assertFalse(ParamCheckUtil.isNcId("i-adfajioej22343"));
    }

    @Test
    public void testIsSn() {
        Assert.assertTrue(ParamCheckUtil.isSn("F9012CSVH01AL18M0EC"));
        Assert.assertFalse(ParamCheckUtil.isSn(""));
        Assert.assertFalse(ParamCheckUtil.isSn("127.0.0.1"));
        Assert.assertFalse(ParamCheckUtil.isSn("31228|674"));
    }

    @Test
    public void testIsAoneUrl() {
        Assert.assertTrue(ParamCheckUtil.isAoneUrl("https://aone.alibaba-inc.com/v2/project/1182328/bug#viewIdentifier=5cf5de9879987eb6f69a3377&openWorkitemIdentifier=61164082"));
        Assert.assertFalse(ParamCheckUtil.isAoneUrl(""));
        Assert.assertFalse(ParamCheckUtil.isAoneUrl("https://test.alibaba-inc.com/jobs/1752168?buildId=0&page=1"));
    }

    @Test
    public void testIsWorkitemId() {
        Assert.assertTrue(ParamCheckUtil.isWorkitemId("31228674"));
        Assert.assertFalse(ParamCheckUtil.isWorkitemId(""));
        Assert.assertFalse(ParamCheckUtil.isWorkitemId("127.0.0.1"));
        Assert.assertFalse(ParamCheckUtil.isWorkitemId("i-adfajioej22343"));
    }

    @Test
    public void testContains8Num() {
        Assert.assertTrue(ParamCheckUtil.contains8Num("31228674"));
        Assert.assertFalse(ParamCheckUtil.contains8Num(""));
        Assert.assertFalse(ParamCheckUtil.contains8Num("127.0.0.1"));
        Assert.assertFalse(ParamCheckUtil.contains8Num("i-adfajioej22343"));
    }

    @Test
    public void testExtractWorkitemId() {
        Assert.assertEquals("61164082", ParamCheckUtil.extractWorkitemId("https://aone.alibaba-inc.com/v2/project/1182328/bug#viewIdentifier=5cf5de9879987eb6f69a3377&openWorkitemIdentifier=61164082"));
    }

    @Test
    public void testGetIdFromAoneUrl() {
        Assert.assertEquals("61164082", ParamCheckUtil.getIdFromAoneUrl("https://aone.alibaba-inc.com/v2/project/1182328/bug#viewIdentifier=5cf5de9879987eb6f69a3377&openWorkitemIdentifier=61164082"));
        Assert.assertEquals("60844183", ParamCheckUtil.getIdFromAoneUrl("https://aone.alibaba-inc.com/v2/project/1182328/req/60844183"));
    }

    @Test
    public void testGetOpenWorkitemIdentifier() {
        Assert.assertEquals("61164082", ParamCheckUtil.getOpenWorkitemIdentifier("https://aone.alibaba-inc.com/v2/project/1182328/bug#viewIdentifier=5cf5de9879987eb6f69a3377&openWorkitemIdentifier=61164082"));
    }

    @Test
    public void testGetAoneId() {
        Assert.assertEquals("60844183", ParamCheckUtil.getAoneId("https://aone.alibaba-inc.com/v2/project/1182328/req/60844183"));
        Assert.assertNull(ParamCheckUtil.getAoneId(""));
    }

    @Test
    public void testIsNcIp() {
        Assert.assertTrue(ParamCheckUtil.isNcIp("127.0.0.1"));
        Assert.assertTrue(ParamCheckUtil.isNcIp("0.0.0.0"));
        Assert.assertTrue(ParamCheckUtil.isNcIp("***************"));
        Assert.assertFalse(ParamCheckUtil.isNcIp("127.0.0.a"));
        Assert.assertFalse(ParamCheckUtil.isNcIp("127.0.0. 1"));
        Assert.assertFalse(ParamCheckUtil.isNcIp("127.0.0.!"));
        Assert.assertFalse(ParamCheckUtil.isNcIp("127.0.0.abc"));
        Assert.assertFalse(ParamCheckUtil.isNcIp("127001"));
        Assert.assertFalse(ParamCheckUtil.isNcIp("127.0.0.1.1"));
        Assert.assertFalse(ParamCheckUtil.isNcIp(""));
    }

    @Test
    public void testIsInstanceId() {
        Assert.assertTrue(ParamCheckUtil.isInstanceId("i-12345678"));
        Assert.assertTrue(ParamCheckUtil.isInstanceId("i-1234567890abcdef0"));
        Assert.assertFalse(ParamCheckUtil.isInstanceId("i-12345678!"));
        Assert.assertFalse(ParamCheckUtil.isInstanceId(""));
        Assert.assertFalse(ParamCheckUtil.isInstanceId("12345678"));
    }
}