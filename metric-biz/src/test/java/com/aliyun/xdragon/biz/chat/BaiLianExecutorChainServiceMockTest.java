package com.aliyun.xdragon.biz.chat;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.xdragon.api.service.chat.ChatBaiLianAgentService;
import com.aliyun.xdragon.api.service.chat.ChatBailianService;
import com.aliyun.xdragon.api.service.chat.model.BaiLianAppResult;
import com.aliyun.xdragon.biz.chat.util.BaiLianDAGModel;
import com.aliyun.xdragon.biz.chat.util.BaiLianDAGNode;
import com.aliyun.xdragon.biz.chat.util.BaiLianDAGNode.NodeStatus;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

@RunWith(PowerMockRunner.class)
public class BaiLianExecutorChainServiceMockTest {
    @InjectMocks
    BaiLianExecutorChainService baiLianExecutorChainService;

    @Mock
    ChatBailianService chatBailianService;

    @Mock
    ChatBaiLianAgentService chatBaiLianAgentService;

    @Test
    public void testProcess() {
        BaiLianDAGNode node1 = new BaiLianDAGNode();
        node1.setName("node1");
        List<BaiLianDAGNode> dependencies = new ArrayList<>();
        node1.setDependencies(dependencies);
        BaiLianAppResult baiLianAppResult = BaiLianAppResult.builder().success(true).text(new JSONObject().toJSONString()).build();
        doReturn(baiLianAppResult).when(chatBaiLianAgentService).callApp(
                anyString(), eq(null), anyString(), anyMap());

        BaiLianDAGModel model = new BaiLianDAGModel(Collections.singletonList(node1));
        baiLianExecutorChainService.processChain(model);
        System.err.println(node1.getResult());

        BaiLianDAGNode node2 = new BaiLianDAGNode();
        node2.setName("node2");
        node2.setDependencies(new ArrayList<>());
        dependencies.add(node2);
        node1.setStatus(NodeStatus.WAITING);
        model = new BaiLianDAGModel(Arrays.asList(node1, node2));
        baiLianExecutorChainService.processChain(model);
        System.err.println(node1.getResult());


        node1.setBaiLianExecutor(baiLianExecutorChainService.defaultConclusionExecutor);
        node1.setStatus(NodeStatus.WAITING);
        node2.setStatus(NodeStatus.WAITING);
        baiLianExecutorChainService.processChain(model);
        System.err.println(node1.getResult());

    }

}
