package com.aliyun.xdragon.biz.workitem.service;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.aliyun.xdragon.biz.workitem.repository.WorkItemClassificationDetailDao;
import com.aliyun.xdragon.biz.workitem.repository.WorkItemStatisticsDao;
import com.aliyun.xdragon.common.exception.WorkItemClassificationException;
import com.aliyun.xdragon.common.generate.model.WorkItemClassificationDetailWithBLOBs;
import com.aliyun.xdragon.common.generate.model.WorkItemClassificationAndLabelDetail;
import com.aliyun.xdragon.common.generate.model.WorkItemLabel;
import com.aliyun.xdragon.common.generate.model.WorkItemStatistics;
import com.aliyun.xdragon.common.model.PageableInfo;
import com.aliyun.xdragon.common.model.XdragonMetricRequest;
import com.aliyun.xdragon.common.model.XdragonMetricResponse;
import com.aliyun.xdragon.common.model.workItem.CreateIssue;
import com.aliyun.xdragon.common.model.workItem.WorkItemEval;
import com.aliyun.xdragon.common.model.workItem.request.ClientListWorkItemRequest;
import com.aliyun.xdragon.common.model.workItem.request.ListWorkItemRequest;
import com.aliyun.xdragon.common.model.workItem.request.QueryLabelRequest;
import com.aliyun.xdragon.common.model.workItem.request.StatLabelInfoByTeamRequest;
import com.aliyun.xdragon.common.model.workItem.request.StatPieChartsDataRequest;
import com.aliyun.xdragon.common.model.workItem.request.UpdateWorkItemRequest;
import com.aliyun.xdragon.common.model.workItem.request.WorkItemAoneRequest;
import com.aliyun.xdragon.common.model.workItem.request.WorkItemLabelRequest;
import com.aliyun.xdragon.common.model.workItem.request.WorkItemStatisticsRequest;
import com.aliyun.xdragon.common.model.workItem.request.WorkItemTrendRequest;
import com.aliyun.xdragon.common.model.workItem.response.ClientListWorkItemResponse;
import com.aliyun.xdragon.service.common.cache.RedisUtil;
import com.aliyun.xdragon.service.common.config.sls.NbSlsClientFactoryService;
import com.aliyun.xdragon.service.common.service.aone.AoneIssueProxy;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2022/11/15
 */
@RunWith(PowerMockRunner.class)
public class WorkItemClassificationServiceTest {

    @InjectMocks
    private WorkItemClassificationServiceImpl workItemClassificationService;

    @Mock
    private WorkItemClassificationDetailDao classificationDetailDao;

    @Mock
    private AoneIssueProxy aoneIssueProxy;

    @Mock
    private RedisUtil redisUtil;

    @Mock
    private WorkItemStatisticsDao workItemStatisticsDao;

    @Mock
    private NbSlsClientFactoryService slsClientFactory;

    @Test
    public void getSearchKeysTest() {
        when(classificationDetailDao.getSearchKeys(anyString())).thenReturn(Lists.newArrayList("宕机"));
        Map<String, List<String>> keys = workItemClassificationService.getSearchKeys();
        Assert.assertNotNull(keys);
    }

    @Test
    public void listWorkItemTest() {
        List<String> keywords = Arrays.asList("无法启动", "虚拟化");
        String joinStr = "or";
        ListWorkItemRequest listWorkItemRequest = new ListWorkItemRequest();
        listWorkItemRequest.setStartMs(1664553600000L);
        listWorkItemRequest.setEndMs(1672502400000L);
        listWorkItemRequest.setStart(0);
        listWorkItemRequest.setPageSize(10);
        listWorkItemRequest.setFirstLabels(null);
        listWorkItemRequest.setSecondaryLabels(null);
        listWorkItemRequest.setOwners(null);
        listWorkItemRequest.setTeams(null);
        listWorkItemRequest.setSources(null);
        listWorkItemRequest.setProduct(null);
        listWorkItemRequest.setKeywords(keywords);
        listWorkItemRequest.setJoinStr(joinStr);
        WorkItemClassificationDetailWithBLOBs workItemClassificationDetail = new WorkItemClassificationDetailWithBLOBs();
        when(classificationDetailDao
            .listWorkItemClassificationDetail(anyString(), anyString(), anyString(), any(List.class), anyString(),
                any(List.class), any(List.class), any(List.class), any(List.class), anyBoolean(), any(List.class), anyString())).thenReturn(
            Lists.newArrayList(workItemClassificationDetail));
        PageableInfo<WorkItemClassificationAndLabelDetail> page = workItemClassificationService.listWorkItem(
            listWorkItemRequest);

        Assert.assertEquals(0, page.getTotal());
    }

    @Test
    public void listClientWorkItem() throws LogException {
        XdragonMetricRequest<ClientListWorkItemRequest> req = new XdragonMetricRequest<>();
        ClientListWorkItemRequest request = new ClientListWorkItemRequest();
        request.setAliUid("1976150850303355");
        request.setStartMs(1664553600000L);
        request.setEndMs(1672502400000L);
        req.setParameters(request);
        Long startMs = request.getStartMs();
        Long endMs = request.getStartMs();
        String aliUid = request.getAliUid();
        Date startDate = new Date(startMs);
        Date endDate = new Date(endMs);
        String startTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startDate);
        String endTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endDate);
        List<Map<String, String>> workItems = new ArrayList<>();
        Map<String, String> workItem1 = new HashMap<>();
        workItem1.put("taskid", "1");
        workItem1.put("subject", "三生万物方法");
        workItem1.put("description", "三生万物方法");
        workItem1.put("time", "2023-12-12 12:12:12");
        Map<String, String> workItem2 = new HashMap<>();
        workItem1.put("taskid", "2");
        workItem1.put("subject", "方法三生万物方法");
        workItem1.put("description", "方法三生万物方法");
        workItem1.put("time", "2023-11-11 11:11:11");
        workItems.add(workItem1);
        workItems.add(workItem2);
        Client client = PowerMockito.mock(Client.class);
        PowerMockito.when(slsClientFactory.getClient(anyString())).thenReturn(client);
        Map<String, String> headers = new HashMap<>();
        headers.put("x-log-progress", "Complete");
        GetLogsResponse response = new GetLogsResponse(headers);
        List<QueriedLog> logs = new ArrayList<>();
        ArrayList<LogContent> contents = new ArrayList<>();
        LogContent logContent1 = new LogContent("deploy_service", "ecs_network.libvswitch_sr#");
        LogContent logContent2 = new LogContent("cnt", "1000");
        contents.add(logContent1);
        contents.add(logContent2);
        LogItem logItem = new LogItem(1, contents);
        QueriedLog queriedLog = new QueriedLog("sls", logItem);
        logs.add(queriedLog);
        logs.add(queriedLog);
        response.SetLogs(logs);
        PowerMockito.when(client.GetLogs(anyString(), anyString(), anyInt(), anyInt(), anyString(), anyString())).thenReturn(response);
        workItemClassificationService.getCidByAliUid(startMs, endMs, aliUid);
        workItemClassificationService.getDingTalkWorkItems(startMs, endMs, startTime, endTime, aliUid);
        workItemClassificationService.getWorkbenchWorkItems(startMs, endMs, startTime, endTime, aliUid);
        workItemClassificationService.getWorkItems(workItems, "workbench");
        XdragonMetricResponse<ClientListWorkItemResponse> result = workItemClassificationService.listClientWorkItem(req);
       Assert.assertNotNull(result);
    }

    @Test
    public void getWorkItemInfo() {
        when(classificationDetailDao.getWorkItemById(eq(1L))).thenReturn(null);
        when(classificationDetailDao.getWorkItemById(eq(2L))).thenReturn(new WorkItemClassificationAndLabelDetail());
        WorkItemClassificationAndLabelDetail workItem = workItemClassificationService.getWorkItemInfo(2L);
        Assert.assertNotNull(workItem);
        try {
            workItemClassificationService.getWorkItemInfo(1L);
            Assert.fail();
        } catch (WorkItemClassificationException e) {
            Assert.assertTrue(e.getMessage().contains("not found"));
        }
    }

    @Test
    public void updateClusterTask() {
        UpdateWorkItemRequest updateWorkItemRequest = new UpdateWorkItemRequest();
        updateWorkItemRequest.setId(1L);
        updateWorkItemRequest.setAction("1L");
        updateWorkItemRequest.setCalibrationFirstLabel(1L);
        updateWorkItemRequest.setComment("1L");
        updateWorkItemRequest.setSecondaryLabel("2");
        updateWorkItemRequest.setExceptionTime("2023-06-21 11:01:45");
        workItemClassificationService.updateWorkItemInfo(updateWorkItemRequest);
        verify(classificationDetailDao, times(1)).updateWorkItem(anyLong(), any(WorkItemClassificationDetailWithBLOBs.class));
    }

    @Test
    public void deleteWorkItem() {
        workItemClassificationService.deleteWorkItem(1L);
        verify(classificationDetailDao, times(1)).deleteWorkItem(anyLong());
    }

    @Test
    public void workItemAone() {
        WorkItemAoneRequest request = new WorkItemAoneRequest();
        request.setId(1L);
        request.setSourceId(2L);
        request.setComment("test");
        request.setSubmitter("266081");

        WorkItemClassificationAndLabelDetail workItem = new WorkItemClassificationAndLabelDetail();
        workItem.setId(1L);
        workItem.setSourceId("2");
        when(redisUtil.get(anyString())).thenReturn(null);
        when(classificationDetailDao.getWorkItemById(anyLong())).thenReturn(null).thenReturn(workItem);
        try {
            workItemClassificationService.workItemAone(request);
            Assert.fail();
        } catch (WorkItemClassificationException e) {
            Assert.assertTrue(e.getMessage().contains("no workItem"));
        }
        Long ret = 123L;
        ArgumentCaptor<CreateIssue> argIssue = ArgumentCaptor.forClass(CreateIssue.class);
        when(aoneIssueProxy.createIssue(argIssue.capture())).thenReturn(ret);
        when(aoneIssueProxy.addTag(anyLong(), anyLong(), anyString(), anyString())).thenReturn(true);
        workItemClassificationService.workItemAone(request);
        reset(redisUtil);
        when(redisUtil.get(anyString())).thenReturn(123L);
        try {
            workItemClassificationService.workItemAone(request);
            Assert.fail();
        } catch (WorkItemClassificationException e) {
            Assert.assertTrue(e.getMessage().contains("frequent"));
        }
        reset(redisUtil);
        when(redisUtil.get(anyString())).thenReturn(null);
    }

    @Test
    public void queryLabelInfo() {
        WorkItemLabel workItemLabel = new WorkItemLabel();
        when(classificationDetailDao.queryLabelList()).thenReturn(Lists.newArrayList(workItemLabel));
        PageableInfo<WorkItemLabel> page = workItemClassificationService.queryLabelInfo();
        Assert.assertEquals(1, page.getTotal());
    }

    @Test
    public void queryLabelByTeam() {
        when(classificationDetailDao.queryLabelByTeam(anyList())).thenReturn(
            Collections.singletonList("宕机"));
        XdragonMetricRequest<QueryLabelRequest> request = new XdragonMetricRequest<>();
        QueryLabelRequest request1 = new QueryLabelRequest();
        request1.setTeams(Lists.newArrayList("虚拟化"));
        request.setParameters(request1);
        when(classificationDetailDao.queryLabelByTeam(anyList())).thenReturn(Lists.newArrayList("宕机"));
        XdragonMetricResponse<List<String>> response = workItemClassificationService.queryLabelByTeam(request);
        Assert.assertEquals(1, response.getData().size());
    }

    @Test
    public void getWorkItemTend() {
        Map<String, Integer> map = new HashMap<>();
        map.put("2024-08-20", 109);
        when(classificationDetailDao.groupDayByLabel(anyList(), anyList(), anyString(), anyString())).thenReturn(
            Collections.singletonList(map));
        XdragonMetricRequest<WorkItemTrendRequest> request = new XdragonMetricRequest<>();
        WorkItemTrendRequest request1 = new WorkItemTrendRequest();
        request1.setTeams(Lists.newArrayList("虚拟化"));
        request1.setLabels(Lists.newArrayList("宕机"));
        request1.setStartDate("2023-06-20 00:00:00");
        request1.setEndDate("2023-07-20 00:00:00");
        request.setParameters(request1);
        XdragonMetricResponse<List<Map<String, Integer>>> response = workItemClassificationService.getWorkItemTrend(request);
        Assert.assertEquals(1, response.getData().size());
    }

    @Test
    public void addLabelInfo() {
        WorkItemLabelRequest workItemLabelRequest = new WorkItemLabelRequest();
        workItemLabelRequest.setName("测试标签");
        workItemLabelRequest.setTagLevel((byte)2);
        workItemLabelRequest.setTeam("测试团队");
        workItemClassificationService.addLabelInfo(workItemLabelRequest);
        verify(classificationDetailDao, times(1)).addWorkItemLabel(any(WorkItemLabel.class));
    }

    @Test
    public void updateLabelInfo() {
        WorkItemLabelRequest workItemLabelRequest = new WorkItemLabelRequest();
        workItemLabelRequest.setId(1L);
        workItemLabelRequest.setName("测试标签");
        workItemLabelRequest.setTagLevel((byte)2);
        workItemLabelRequest.setTeam("测试团队");
        workItemClassificationService.updateLabelInfo(workItemLabelRequest);
        verify(classificationDetailDao, times(1)).updateWorkItemLabel(any(WorkItemLabel.class));
    }

    @Test
    public void deleteLabelInfo() {
        workItemClassificationService.deleteLabelInfo(1L);
        verify(classificationDetailDao, times(1)).deleteWorkItemLabel(anyLong());
    }

    @Test
    public void statChartsData() {
        Map<String, Object> map = new HashMap<>();
        when(classificationDetailDao.statChartsOfDataSource(anyString(), anyString())).thenReturn(
            Lists.newArrayList(map));
        StatPieChartsDataRequest request = new StatPieChartsDataRequest();
        request.setStartMs(1664553600000L);
        request.setEndMs(1672502400000L);

        request.setParamType("data_source");
        List<Pair<String, Long>> result1 = workItemClassificationService.statChartsData(request);
        Assert.assertNotNull(result1);

        request.setParamType("team");
        List<Pair<String, Long>> result2 = workItemClassificationService.statChartsData(request);
        Assert.assertNotNull(result2);

        request.setParamType("label_one_id");
        List<Pair<String, Long>> result3 = workItemClassificationService.statChartsData(request);
        Assert.assertNotNull(result3);

        request.setParamType("label_two_id");
        List<Pair<String, Long>> result4 = workItemClassificationService.statChartsData(request);
        Assert.assertNotNull(result4);
    }

    @Test
    public void statLabelInfoByTeam() {
        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("labelOne", "宕机");
        map.put("count", "10");
        mapList.add(map);
        when(classificationDetailDao.statLabelOneIdInfoByTeam(anyString(), anyString(), anyString())).thenReturn(
            Lists.newArrayList(mapList));
        StatLabelInfoByTeamRequest request = new StatLabelInfoByTeamRequest();
        request.setStartMs(1664553600000L);
        request.setEndMs(1672502400000L);
        request.setTeam("虚拟化");
        request.setParamType("labelOne");

        List<Pair<String, Long>> result1 = workItemClassificationService.statLabelInfoByTeam(request);
        Assert.assertNotNull(result1);

        request.setParamType("labelTwo");
        List<Pair<String, Long>> result2 = workItemClassificationService.statLabelInfoByTeam(request);
        Assert.assertNotNull(result2);
    }

    @Test
    public void listWorkItemStatisticsTest() {
        XdragonMetricRequest<WorkItemStatisticsRequest> request = new XdragonMetricRequest<>();
        WorkItemStatisticsRequest workItemStatisticsRequest = new WorkItemStatisticsRequest();
        workItemStatisticsRequest.setStartDs("2023-06-01 00:00:00");
        workItemStatisticsRequest.setEndDs("2023-07-07 00:00:00");

        workItemStatisticsRequest.setType(0);
        request.setParameters(workItemStatisticsRequest);
        XdragonMetricResponse<List<WorkItemStatistics>> response = workItemClassificationService.listWorkItemStatistics(request);
        Assert.assertNotNull(response);

        workItemStatisticsRequest.setType(3);
        request.setParameters(workItemStatisticsRequest);
        response = workItemClassificationService.listWorkItemStatistics(request);
        Assert.assertNotNull(response);

        workItemStatisticsRequest.setType(6);
        request.setParameters(workItemStatisticsRequest);
        response = workItemClassificationService.listWorkItemStatistics(request);
        Assert.assertNotNull(response);

        workItemStatisticsRequest.setType(null);
        List<String> keys = Arrays.asList("1", "2", "3");
        workItemStatisticsRequest.setObjectNames(keys);
        response = workItemClassificationService.listWorkItemStatistics(request);
        Assert.assertNotNull(response);
    }

    @Test
    public void evalWorkItemTest() {
        List<WorkItemStatistics> numerator = new ArrayList<>();
        WorkItemStatistics item1 = new WorkItemStatistics();
        item1.setObjectName("1");
        item1.setDt("20230702");
        item1.setObjectValue(4.0);
        item1.setType(2);
        WorkItemStatistics item2 = new WorkItemStatistics();
        item2.setObjectName("1");
        item2.setDt("20230802");
        item2.setObjectValue(4.0);
        item2.setType(2);
        numerator.add(item1);
        numerator.add(item2);
        List<WorkItemStatistics> denominator = new ArrayList<>();
        WorkItemStatistics item3 = new WorkItemStatistics();
        item3.setObjectName("1");
        item3.setDt("20230802");
        item3.setObjectValue(10.0);
        item3.setType(3);
        denominator.add(item3);
        when(workItemStatisticsDao.listByType(eq(2), anyString(), anyString(), anyList())).thenReturn(numerator);
        when(workItemStatisticsDao.listByType(eq(3), anyString(), anyString(), anyList())).thenReturn(denominator);
        String startDs = "20230803";
        String endDs = "20230901";
        List<WorkItemEval> result = workItemClassificationService.evalWorkItem(startDs, endDs, "1");
        Assert.assertTrue(result.size() > 0);
    }
}

