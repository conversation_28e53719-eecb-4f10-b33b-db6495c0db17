package com.aliyun.xdragon.biz.clientops.service;

import com.aliyun.xdragon.biz.clientops.repository.ClientResourceDao;
import com.aliyun.xdragon.common.enumeration.ClientResourceStatus;
import com.aliyun.xdragon.common.enumeration.ClientResourceType;
import com.aliyun.xdragon.common.generate.model.ClientResource;
import com.aliyun.xdragon.common.model.bpms.BpmsProcessStatus;
import com.aliyun.xdragon.common.model.bpms.BpmsStartProcess;
import com.aliyun.xdragon.common.model.PageableInfo;
import com.aliyun.xdragon.common.model.clientops.request.*;
import com.aliyun.xdragon.common.model.XdragonMetricResponse;
import com.aliyun.xdragon.service.common.agent.ApproveClient;
import com.aliyun.xdragon.service.common.agent.EcsInnerApiClient;
import com.aliyun.xdragon.service.common.agent.EdmClient;
import com.aliyun.xdragon.service.common.cache.RedisUtil;
import com.aliyuncs.ecsinc.model.v20160314.InnerEcsQueryByParamResponse;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.doNothing;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2024/04/03
 */
@RunWith(PowerMockRunner.class)
public class ClientResourceServiceTest {
    @Mock
    private ClientResourceDao resourceDao;

    @Mock
    private EcsInnerApiClient ecsInnerApiClient;

    @Mock
    private EdmClient edmClient;

    @Mock
    private ApproveClient approveClient;

    @Mock
    private RedisUtil redisUtil;

    @InjectMocks
    private ClientResourceServiceImpl clientResourceService;

    @Test
    public void listEmpResources() {
        when(resourceDao.listEmpResources(anyString(), anyList(), anyList(), anyInt(), anyInt())).thenReturn(Lists.newArrayList(new ClientResource()));
        when(resourceDao.countResource(anyString(), anyList(), any(), anyList())).thenReturn(100L);
        ListEmpResourcesRequest request = new ListEmpResourcesRequest();
        request.setEmpId("1");
        request.setPageNum(1);
        request.setPageSize(10);
        request.setResourceType(Lists.newArrayList("uid"));
        request.setApproveStatus(Lists.newArrayList(0));
        XdragonMetricResponse<PageableInfo<ClientResource>> ret = clientResourceService.listEmpResources(request);
        Assert.assertTrue(ret.isSuccess());
        Assert.assertEquals(100, ret.getData().getTotal());
    }

    @Test
    public void addResource() {
        AddClientResourceRequest request = new AddClientResourceRequest();
        request.setEmpId("1");
        request.setType(ClientResourceType.UID);
        request.setResources(Lists.newArrayList("123"));
        request.setComment("test");
        // add db fail
        when(resourceDao.addResource(anyString(), anyString(), anyList(), anyString())).thenReturn(Pair.of(0, 123L));
        XdragonMetricResponse<String> ret = clientResourceService.addResource(request);
        Assert.assertFalse(ret.isSuccess());
        Assert.assertTrue(ret.getMessage().contains("add resource fail"));

        // start approve fail
        Mockito.reset(resourceDao);
        when(resourceDao.addResource(anyString(), anyString(), anyList(), anyString())).thenReturn(Pair.of(1, 123L));
        when(approveClient.startProcess(anyString(), anyString(), anyString(),anyMap())).thenReturn(null);
        ret = clientResourceService.addResource(request);
        Assert.assertFalse(ret.isSuccess());
        Assert.assertTrue(ret.getMessage().contains("start approve fail"));

        //normal
        Mockito.reset(approveClient);
        BpmsStartProcess process = new BpmsStartProcess();
        process.setSuccess(true);
        BpmsStartProcess.Content content = process.new Content();
        content.setProcessInstanceId("123456789");
        process.setContent(content);
        when(approveClient.startProcess(anyString(), anyString(), anyString(),anyMap())).thenReturn(process);
        when(resourceDao.addResource(anyString(), anyString(), anyList(), anyString())).thenReturn(Pair.of(1, 123L));
        ret = clientResourceService.addResource(request);
        Assert.assertTrue(ret.isSuccess());
        Assert.assertEquals("123456789", ret.getData());
    }

    @Test
    public void updateResource() {
        // not exist
        when(resourceDao.getResourceWithId(anyInt())).thenReturn(null);
        UpdateClientResourceRequest request = new UpdateClientResourceRequest();
        request.setId(1);
        request.setResource("12345");
        XdragonMetricResponse<String> ret = clientResourceService.updateResource(request);
        Assert.assertFalse(ret.isSuccess());
        Assert.assertTrue(ret.getMessage().contains("not exist"));

        // update db fail
        Mockito.reset(resourceDao);
        ClientResource oldCr = new ClientResource();
        oldCr.setId(1);
        oldCr.setEmpId("123");
        oldCr.setResources("1234");
        when(resourceDao.getResourceWithId(anyInt())).thenReturn(oldCr);
        when(resourceDao.updateResourceWithId(anyInt(), any(ClientResource.class))).thenReturn(0);
        ret = clientResourceService.updateResource(request);
        Assert.assertFalse(ret.isSuccess());
        Assert.assertTrue(ret.getMessage().contains("modify resource fail"));

        // start approve fail
        Mockito.reset(resourceDao);
        when(resourceDao.getResourceWithId(anyInt())).thenReturn(oldCr);
        when(resourceDao.updateResourceWithId(anyInt(), any(ClientResource.class))).thenReturn(1);
        when(approveClient.startProcess(anyString(), anyString(), anyString(),anyMap())).thenReturn(null);
        ret = clientResourceService.updateResource(request);
        Assert.assertFalse(ret.isSuccess());
        Assert.assertTrue(ret.getMessage().contains("start approve fail"));

        // normal
//        Mockito.reset(resourceDao);
        Mockito.reset(approveClient);
//        when(resourceDao.updateResourceWithId(anyInt(), any(ClientResource.class))).thenReturn(1);
        BpmsStartProcess process = new BpmsStartProcess();
        process.setSuccess(true);
        BpmsStartProcess.Content content = process.new Content();
        content.setProcessInstanceId("123456789");
        process.setContent(content);
        when(approveClient.startProcess(anyString(), anyString(), anyString(),anyMap())).thenReturn(process);
        ret = clientResourceService.updateResource(request);
        Assert.assertTrue(ret.isSuccess());
        Assert.assertEquals("123456789", ret.getData());
    }

    @Test
    public void deleteResource() {
        when(resourceDao.deleteResourceWithId(anyList())).thenReturn(1);
        DelClientResourceRequest request = new DelClientResourceRequest();
        request.setIds(Lists.newArrayList(1));
        XdragonMetricResponse<Boolean> ret = clientResourceService.deleteResource(request);
        Assert.assertTrue(ret.getData());
    }

    @Test
    public void checkResource() {
        // check uid
        when(redisUtil.get(anyString())).thenReturn(false);
        CheckClientResourceRequest request = new CheckClientResourceRequest();
        request.setEmpId("1");
        request.setType(ClientResourceType.UID);
        request.setResources(Lists.newArrayList("12345"));
        when(resourceDao.countResource(anyString(), anyList(), eq("12345"), anyList())).thenReturn(1L);
        XdragonMetricResponse<Pair<Boolean,String>> ret = clientResourceService.checkResource(request);
        Assert.assertTrue(ret.getData().getKey());

        reset(resourceDao);
        request.setResources(Lists.newArrayList("54321"));
        when(resourceDao.countResource(anyString(), anyList(), eq("54321"), anyList())).thenReturn(0L);
        when(edmClient.getUserCid(eq("54321"))).thenReturn(null);
        ret = clientResourceService.checkResource(request);
        Assert.assertFalse(ret.getData().getKey());

        reset(resourceDao);
        reset(edmClient);
        when(edmClient.getUserCid(eq("54321"))).thenReturn(24680L);
        when(resourceDao.countResource(anyString(), anyList(), eq("24680"), anyList())).thenReturn(1L);
        ret = clientResourceService.checkResource(request);
        Assert.assertTrue(ret.getData().getKey());

        // check vm
        reset(resourceDao);
        CheckClientResourceRequest request2 = new CheckClientResourceRequest();
        request2.setEmpId("1");
        request2.setType(ClientResourceType.INSTANCE);
        request2.setResources(Lists.newArrayList("i-12345"));
        when(resourceDao.countResource(anyString(), anyList(), eq("i-12345"), anyList())).thenReturn(1L);
        ret = clientResourceService.checkResource(request2);
        Assert.assertTrue(ret.getData().getKey());

        reset(resourceDao);
        reset(ecsInnerApiClient);
        when(resourceDao.countResource(anyString(), anyList(), eq("i-54321"), anyList())).thenReturn(0L);
        InnerEcsQueryByParamResponse.Item item = new InnerEcsQueryByParamResponse.Item();
        item.setAliUid(12345L);
        when(ecsInnerApiClient.queryVmDetail(anyString())).thenReturn(item);
        when(resourceDao.countResource(anyString(), anyList(), eq("12345"), anyList())).thenReturn(1L);
        request2.setResources(Lists.newArrayList("i-54321"));
        ret = clientResourceService.checkResource(request2);
        Assert.assertTrue(ret.getData().getKey());

        reset(resourceDao);
        reset(ecsInnerApiClient);
        reset(edmClient);
        when(resourceDao.countResource(anyString(), anyList(), eq("i-12345"), anyList())).thenReturn(0L);
        item.setAliUid(123456L);
        when(ecsInnerApiClient.queryVmDetail(anyString())).thenReturn(item);
        when(resourceDao.countResource(anyString(), anyList(), eq("123456"), anyList())).thenReturn(0L);
        when(edmClient.getUserCid(eq("123456"))).thenReturn(24680L);
        when(resourceDao.countResource(anyString(), anyList(), eq("24680"), anyList())).thenReturn(1L);

        ret = clientResourceService.checkResource(request2);
        Assert.assertFalse(ret.getData().getKey());
    }

    @Test
    public void updateApproveStatus() {
        ClientResource cr = new ClientResource();
        cr.setId(1);
        cr.setBpmsId("123");
        when(resourceDao.getResourceByApproveStatus(eq(ClientResourceStatus.APPROVING.getStatus()))).thenReturn(Lists.newArrayList(cr));


        // ERROR
        BpmsProcessStatus processStatus = new BpmsProcessStatus();
        processStatus.setSuccess(true);
        BpmsProcessStatus.Content content = processStatus.new Content();
        content.setInstStatus("ERROR");
        processStatus.setContent(content);
        when(approveClient.getProcessInstanceStatus(anyString())).thenReturn(processStatus);
        ArgumentCaptor<Integer> ac = ArgumentCaptor.forClass(Integer.class);
        doNothing().when(resourceDao).updateApproveStatus(anyList(), ac.capture(), any());
        clientResourceService.updateApproveStatus();
        Integer st = ac.getValue();
        Assert.assertEquals((int) st, ClientResourceStatus.APPROVE_ERROR.getStatus());

        // COMPLETED
        Mockito.reset(approveClient);
        content = processStatus.new Content();
        content.setInstStatus("COMPLETED");
        content.setOutResult("同意");
        processStatus.setContent(content);
        when(approveClient.getProcessInstanceStatus(anyString())).thenReturn(processStatus);
        ac = ArgumentCaptor.forClass(Integer.class);
        doNothing().when(resourceDao).updateApproveStatus(anyList(), ac.capture(), any());
        clientResourceService.updateApproveStatus();
        st = ac.getValue();
        Assert.assertEquals((int) st, ClientResourceStatus.APPROVE_ACCEPT.getStatus());

        Mockito.reset(approveClient);
        content = processStatus.new Content();
        content.setInstStatus("COMPLETED");
        content.setOutResult("拒绝");
        processStatus.setContent(content);
        when(approveClient.getProcessInstanceStatus(anyString())).thenReturn(processStatus);
        ac = ArgumentCaptor.forClass(Integer.class);
        doNothing().when(resourceDao).updateApproveStatus(anyList(), ac.capture(), any());
        clientResourceService.updateApproveStatus();
        st = ac.getValue();
        Assert.assertEquals((int) st, ClientResourceStatus.APPROVE_REJECT.getStatus());

        // TERMINATED
        Mockito.reset(approveClient);
        content = processStatus.new Content();
        content.setInstStatus("TERMINATED");
        processStatus.setContent(content);
        when(approveClient.getProcessInstanceStatus(anyString())).thenReturn(processStatus);
        ac = ArgumentCaptor.forClass(Integer.class);
        doNothing().when(resourceDao).updateApproveStatus(anyList(), ac.capture(), any());
        clientResourceService.updateApproveStatus();
        st = ac.getValue();
        Assert.assertEquals((int) st, ClientResourceStatus.APPROVE_CANCEL.getStatus());

        // RUNNING
        Mockito.reset(approveClient);
        Mockito.reset(resourceDao);
        when(resourceDao.getResourceByApproveStatus(eq(ClientResourceStatus.APPROVING.getStatus()))).thenReturn(Lists.newArrayList(cr));
        content = processStatus.new Content();
        content.setInstStatus("RUNNING");
        processStatus.setContent(content);
        when(approveClient.getProcessInstanceStatus(anyString())).thenReturn(processStatus);
        clientResourceService.updateApproveStatus();
        verify(resourceDao, times(0)).updateApproveStatus(anyList(), anyInt(), any());
    }
}
