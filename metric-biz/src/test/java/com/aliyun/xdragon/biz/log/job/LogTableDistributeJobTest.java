package com.aliyun.xdragon.biz.log.job;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.xdragon.biz.log.repository.ClusterTaskDao;
import com.aliyun.xdragon.biz.log.repository.GlobalVersionSourceDao;
import com.aliyun.xdragon.biz.log.repository.PatternDistributionDao;
import com.aliyun.xdragon.biz.log.repository.PatternIdMappingDao;
import com.aliyun.xdragon.common.generate.model.LogClusterTask;
import com.aliyun.xdragon.service.common.cache.RedisUtil;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.internal.WhiteboxImpl;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2023/01/04
 */
@RunWith(PowerMockRunner.class)
public class LogTableDistributeJobTest {
    @InjectMocks
    LogTableDistributeJob logTableDistributeJob;

    @Mock
    private ClusterTaskDao clusterTaskDao;

    @Mock
    private PatternDistributionDao patternDistributionDao;

    @Mock
    private GlobalVersionSourceDao globalVersionSourceDao;

    @Mock
    private PatternIdMappingDao patternIdMappingDao;

    @Mock
    private RedisUtil redisUtil;

    @Test
    public void genTask() {
        LogClusterTask t1 = new LogClusterTask();
        t1.setTaskId(1L);
        LogClusterTask t2 = new LogClusterTask();
        t2.setTaskId(2L);

        when(clusterTaskDao.listTask(any(), any(), anyBoolean())).thenReturn(Lists.newArrayList(t1, t2));
        List<Long> tasks = logTableDistributeJob.genTask(null, 123);
        Assert.assertEquals(2, tasks.size());
    }

    @Test
    public void processSubTask() throws Exception {
        when(patternDistributionDao.checkTable(anyLong())).thenReturn(false);
        when(redisUtil.hasKey(anyString())).thenReturn(true);
        when(patternIdMappingDao.checkTable(anyLong())).thenReturn(true);
        ProcessResult result = logTableDistributeJob.processSubTask(null, 123, 123, 1L);
        verify(patternDistributionDao, times(1)).createTable(anyLong());
        //verify(patternDistributionDao, times(2)).deleteDetail(anyLong(), any(Date.class), any(Date.class), anyBoolean());
        //verify(patternDistributionDao, times(2)).sumDetail(anyLong(), any(Date.class), any(Date.class));
        verify(patternDistributionDao, times(1)).clearDetail(anyLong(), any(Date.class), anyInt(), anyBoolean());
        verify(patternIdMappingDao, times(1)).cleanMapping(anyLong(), any(Date.class), anyInt(), anyBoolean());
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());
    }

    @Test
    public void reduce() throws Exception {
        when(patternDistributionDao.clearDetail(any(), any(Date.class), anyInt(), anyBoolean())).thenReturn(50000).thenReturn(100);
        when(globalVersionSourceDao.cleanByTime(any(Date.class), anyInt())).thenReturn(50000).thenReturn(100);
        ProcessResult result = logTableDistributeJob.reduce(null, 123, 123);
        ThreadPoolExecutor pool = WhiteboxImpl.getInternalState(logTableDistributeJob, "executorService");
        while (pool.getActiveCount() > 0) {
            Thread.sleep(100L);
        }
        verify(patternDistributionDao, times(2)).clearDetail(any(), any(Date.class), anyInt(), anyBoolean());
        verify(globalVersionSourceDao, times(2)).cleanByTime(any(Date.class), anyInt());
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());
    }
}
