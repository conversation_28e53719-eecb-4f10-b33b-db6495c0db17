package com.aliyun.xdragon.biz.evaluate.repository;

import com.aliyun.xdragon.common.generate.model.EvaluateMonitorPercentile;
import com.aliyun.xdragon.biz.AbstractDbTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

import java.util.ArrayList;
import java.util.List;

@Import(EvalMonitorPercentileDao.class)
public class EvalMonitorPercentileDaoTest extends AbstractDbTest {

    @Autowired
    EvalMonitorPercentileDao evalMonitorPercentileDao;

    @Test
    public void batchInsertTest() {
        List<EvaluateMonitorPercentile> percentiles = new ArrayList<>();
        evalMonitorPercentileDao.batchInsert(percentiles);
        EvaluateMonitorPercentile evaluateMonitorPercentile = new EvaluateMonitorPercentile();
        evaluateMonitorPercentile.setBatchId(1L);
        evaluateMonitorPercentile.setTaskId(1L);
        evaluateMonitorPercentile.setMetricName("test");
        evaluateMonitorPercentile.setActionName("test");
        evaluateMonitorPercentile.setExceptionName("test");
        evaluateMonitorPercentile.setDuration(2.0);
        evaluateMonitorPercentile.setPercentile(0.5);
        evaluateMonitorPercentile.setTotalDuration(4.0);
        percentiles.add(evaluateMonitorPercentile);
        evalMonitorPercentileDao.batchInsert(percentiles);
    }

    @Test
    public void listMonitorPercentilesTest(){
        Long taskId = 1L;
        Long batchId = 1L;
        String metricName = "test";
        String actionName = "test";
        List<EvaluateMonitorPercentile> result = evalMonitorPercentileDao.listMonitorPercentiles(taskId, batchId,metricName, actionName);
        assert result.size() == 1;
    }

    @Test
    public void batchDeleteTest() {
        Long taskId = 1L;
        Long batchId = 1L;
        String metricName = "test";
        String actionName = "test";
        int result = evalMonitorPercentileDao.batchDelete(taskId, batchId, metricName, actionName);
        assert result == 1;
    }
}
