package com.aliyun.xdragon.biz.evaluate.repository;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.aliyun.xdragon.common.enumeration.EvalTaskStatus;
import com.aliyun.xdragon.common.enumeration.EvalTaskType;
import com.aliyun.xdragon.common.generate.model.EvaluateTaskMeta;
import com.aliyun.xdragon.biz.AbstractDbTest;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @date 2022/04/13
 */
@Import({EvalTaskMetaDao.class})
public class EvalTaskMetaDaoTest extends AbstractDbTest {
    @Autowired
    EvalTaskMetaDao evalTaskMetaDao;

    @Test
    public void test() {
        String param = "{\n"
            + "    \"run_time\": \"0 0 8 * * ? \",\n"
            + "    \"data_set\":\n"
            + "    [\n"
            + "        {\n"
            + "            \"table\": \"ecs_dw.xdragon_hot_upgrade_iohub_downtime\",\n"
            + "            \"filter\": \"ip in ('***********','***********')\",\n"
            + "            \"metrics\": \"downtime,ds\",\n"
            + "            \"order\": \"desc\",\n"
            + "            \"order_by\": \"start_time\",\n"
            + "            \"name_type\": \"dynamic\",\n"
            + "            \"name\": \"ip\",\n"
            + "            \"partition\": \"ds=yyyyMMdd\",\n"
            + "            \"cross_partition\": true,\n"
            + "            \"partition_number\": 2\n"
            + "        },\n"
            + "        {\n"
            + "            \"table\": \"ecs_dw.xdragon_hot_upgrade_iohub_downtime\",\n"
            + "            \"filter\": \"ip in ('***********','***********')\",\n"
            + "            \"metrics\": \"downtime,ds\",\n"
            + "            \"order\": \"desc\",\n"
            + "            \"order_by\": \"start_time\",\n"
            + "            \"name_type\": \"dynamic\",\n"
            + "            \"name\": \"ip\",\n"
            + "            \"partition\": \"ds=yyyyMMdd\",\n"
            + "            \"cross_partition\": true,\n"
            + "            \"partition_number\": 2\n"
            + "        }\n"
            + "    ]\n"
            + "}";
        EvaluateTaskMeta task = new EvaluateTaskMeta();
        task.setParam(param);
        task.setTaskType(EvalTaskType.ODPS.getType().byteValue());
        task.setName("test_eval");
        task.setTaskStatus(EvalTaskStatus.NORMAL.getStatus().byteValue());
        task.setDescription("odps eval ut");
        task.setCreator("rongdi");
        task.setGmtCreate(new Date());
        task.setGmtModified(new Date());
        task.setCron("0 0 8 * * ?");
        task.setRunLock(new Byte("0"));
        task.setRunLockTime(0L);
        evalTaskMetaDao.add(task);
        Assert.assertTrue(task.getTaskId() > 0);

        EvaluateTaskMeta task1 = evalTaskMetaDao.getTask(task.getTaskId());
        Assert.assertNotNull(task1);

        EvaluateTaskMeta task2 = new EvaluateTaskMeta();
        task2.setParam(param);
        task2.setTaskType(EvalTaskType.ODPS.getType().byteValue());
        task2.setName("test_eval");
        task2.setTaskStatus(EvalTaskStatus.DELETE.getStatus().byteValue());
        task2.setDescription("odps eval ut");
        task2.setCreator("rongdi");
        task2.setGmtCreate(new Date());
        task2.setGmtModified(new Date());
        task2.setCron("0 0 8 * * ?");
        task2.setRunLock(new Byte("0"));
        task2.setRunLockTime(0L);
        evalTaskMetaDao.add(task2);

        List<EvaluateTaskMeta> taskMetas = evalTaskMetaDao.listTasksNotDeleted(0, 1000, "test", 1);
        Set<Long> ids = new HashSet<>();
        taskMetas.forEach(t -> ids.add(t.getTaskId()));
        Assert.assertFalse(ids.contains(task2.getTaskId()));
        Assert.assertTrue(ids.contains(task.getTaskId()));

        Long num = evalTaskMetaDao.countNotDeletedTask("test", 1);
        Assert.assertTrue(num >= 1);

        List<EvaluateTaskMeta> taskMetas2 = evalTaskMetaDao.listTasks(0, 1000,
            Lists.newArrayList(EvalTaskStatus.DELETE), Lists.newArrayList(EvalTaskType.ODPS));
        ids.clear();
        taskMetas2.forEach(t -> ids.add(t.getTaskId()));
        Assert.assertTrue(ids.contains(task2.getTaskId()));

        task2.setTaskStatus(EvalTaskStatus.STOP.getStatus().byteValue());
        evalTaskMetaDao.updateTaskById(task2.getTaskId(), task2);
        EvaluateTaskMeta task3 = evalTaskMetaDao.getTask(task2.getTaskId());
        Assert.assertEquals((byte)task3.getTaskStatus(), EvalTaskStatus.STOP.getStatus().byteValue());
    }
}
