package com.aliyun.xdragon.biz.metric.repository;

import java.util.List;
import java.util.stream.Collectors;

import com.aliyun.xdragon.biz.AbstractDbTest;
import com.aliyun.xdragon.common.enumeration.MatchingRule;
import com.aliyun.xdragon.common.generate.model.MetricAnomalyConfig;
import org.assertj.core.api.Assertions;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @date 2022/09/15
 */
@Import(MetricAnomalyConfigDao.class)
public class MetricAnomalyConfigDaoTest extends AbstractDbTest {
    @Autowired
    private MetricAnomalyConfigDao metricAnomalyConfigDao;

    @Test
    public void test() {
        MetricAnomalyConfig config = new MetricAnomalyConfig();
        config.setConfigName("metric");
        config.setTsInterval(60);
        config.setExpire(600);
        config.setMissFillType("m");
        config.setDetectType("single");
        config.setAnomalyConfig("{}");
        config.setMatchingRule(MatchingRule.PREFIX.getName());
        config.setSmoothWinSize(100);
        config.setPeriodLength(10);

        MetricAnomalyConfig config2 = new MetricAnomalyConfig();
        config2.setConfigName("metric2");
        config2.setTsInterval(60);
        config2.setExpire(600);
        config2.setMissFillType("m");
        config2.setDetectType("single");
        config2.setAnomalyConfig("{}");
        config2.setMatchingRule(MatchingRule.PREFIX.getName());
        config2.setSmoothWinSize(100);
        config2.setPeriodLength(10);
        metricAnomalyConfigDao.addConfig(config);
        metricAnomalyConfigDao.upsertConfig(config2);

        List<MetricAnomalyConfig> configs2 = metricAnomalyConfigDao.queryCheckConfigSimple();
        Assertions.assertThat(configs2).hasSize(2).element(0).hasFieldOrPropertyWithValue("configName", "metric");

        MetricAnomalyConfig anomalyConfig = metricAnomalyConfigDao.getConfigById(config.getConfigId());
        Assertions.assertThat(anomalyConfig).hasFieldOrPropertyWithValue("matchingRule", MatchingRule.PREFIX.getName());
        anomalyConfig.setConfigName("metric3");
        Assertions.assertThat(metricAnomalyConfigDao.getConfigById(anomalyConfig.getConfigId())).hasFieldOrPropertyWithValue("configName", "metric3");

        List<MetricAnomalyConfig> configs3 = metricAnomalyConfigDao.listMetricAnomalyConfigs(0, 10, null);
        Assertions.assertThat(configs3).hasSize(2);
        List<MetricAnomalyConfig> configs4 = metricAnomalyConfigDao.listMetricAnomalyConfigsByIds(configs3.stream().map(MetricAnomalyConfig::getConfigId).collect(
            Collectors.toList()));
        Assertions.assertThat(configs4).hasSize(2);
        int res = metricAnomalyConfigDao.deleteConfig(configs3.get(0).getConfigId());
        Assert.assertTrue(res > 0);
        Assertions.assertThat(metricAnomalyConfigDao.listMetricAnomalyConfigs(0, 10, "etric")).hasSize(1);

        Assert.assertEquals(1, metricAnomalyConfigDao.getConfigCount("metric"));
    }
}
