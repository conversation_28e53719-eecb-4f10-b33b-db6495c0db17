package com.aliyun.xdragon.biz.log.job;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.xdragon.api.service.chat.DingMessageService;
import com.aliyun.xdragon.biz.log.repository.LogPatternLibraryDao;
import com.aliyun.xdragon.common.generate.log.model.LogPatternLibrary;
import com.aliyun.xdragon.common.generate.model.LogClusterTask;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import com.aliyun.xdragon.biz.log.repository.ClusterTaskDao;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
public class PatternSimilarityReportJobTest {
    private final JobContext context = JobContext.newBuilder().setTaskName("PATTERN_SIMILARITY_REPORT_JOB_UT")
            .setScheduleTime(new DateTime()).setDataTime(new DateTime()).build();

    @InjectMocks
    private PatternSimilarityReportJob job;

    @Mock
    private ClusterTaskDao taskDao;

    @Mock
    private LogPatternLibraryDao libraryDao;

    @Mock
    private DingMessageService dingMessageService;

    @Test
    public void genTaskTest() {
        LogClusterTask task1 = makeTask(1L, "task1", "test task1");
        LogClusterTask task2 = makeTask(2L, "task2", "test task2");
        LogClusterTask task3 = makeTask(3L, "task3", "test task3");
        when(taskDao.listTask(any(), any(), anyBoolean())).thenReturn(Arrays.asList(task1, task2, task3));
        List<Long> subTasks = job.genTask(context, 0);
        Assert.assertNotNull(subTasks);
        Assert.assertEquals(3, subTasks.size());
    }

    @Test
    public void processSubTaskTest() throws Exception {
        LogPatternLibrary library1 = makeLibrary(1L, "md51", "A *** B C D", 1);
        LogPatternLibrary library2 = makeLibrary(1L, "md52", "A *** B C *** D", 0);
        LogPatternLibrary library3 = makeLibrary(1L, "md53", "E *** B F *** D", 5);
        when(libraryDao.getPatternWithStatus(anyLong(), any(List.class))).thenReturn(Arrays.asList(library1, library2, library3));
        LogClusterTask task1 = makeTask(1L, "task1", "test task1");
        when(taskDao.listTask(any(List.class))).thenReturn(Collections.singletonList(task1));
        when(dingMessageService.createMessageCard(any(), anyString(), any(), anyInt())).thenReturn("track-id");
        ProcessResult result = job.processSubTask(context, 0, 0, 1L);
        Assert.assertNotNull(result);
        Assert.assertEquals(InstanceStatus.SUCCESS, result.getStatus());
    }

    private LogClusterTask makeTask(Long taskId, String name, String description) {
        LogClusterTask task = new LogClusterTask();
        task.setTaskId(taskId);
        task.setTaskName(name);
        task.setTaskDescription(description);
        return task;
    }

    private LogPatternLibrary makeLibrary(Long taskId, String md5, String pattern, Integer status) {
        LogPatternLibrary library = new LogPatternLibrary();
        library.setTaskId(taskId);
        library.setMd5(md5);
        library.setPattern(pattern);
        library.setStatus(status);
        return library;
    }
}