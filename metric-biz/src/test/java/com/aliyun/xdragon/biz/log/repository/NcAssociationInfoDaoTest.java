package com.aliyun.xdragon.biz.log.repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.aliyun.xdragon.biz.AbstractADBTest;
import com.aliyun.xdragon.common.generate.log.model.NcAssociationInfo;
import com.aliyun.xdragon.common.generate.log.model.map.NcAssociationInfoMapper;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

@Import(NcAssociationInfoDao.class)
public class NcAssociationInfoDaoTest extends AbstractADBTest {
    @Autowired
    private NcAssociationInfoDao associationDao;

    @Autowired
    private NcAssociationInfoMapper mapper;

    private void addAssociation(long ms, String ip) {
        NcAssociationInfo info1 = new NcAssociationInfo();
        if (ip != null) {
            info1.setNcip(ip);
        } else {
            info1.setNcip("*******");
        }
        info1.setVirtType("dragon");
        info1.setVirtType2("MOC1.0");
        info1.setAzone("azone1");
        info1.setRegion("region1");
        info1.setClusterId("cluster1");
        info1.setNcMode("bare");
        info1.setInstanceFamily("family1");
        info1.setTime(new Date(ms));
        mapper.insertSelective(info1);
    }
    @Test
    public void testBatchInsert() {
        List<NcAssociationInfo> associationInfos = new ArrayList<>(3);
        NcAssociationInfo info1 = new NcAssociationInfo();
        info1.setNcip("*******");
        info1.setVirtType("dragon");
        info1.setVirtType2("MOC1.0");
        info1.setAzone("azone1");
        info1.setRegion("region1");
        info1.setClusterId("cluster1");
        info1.setNcMode("bare");
        info1.setInstanceFamily("family1");
        info1.setTime(new Date(1659283200000L));

        NcAssociationInfo info2 = new NcAssociationInfo();
        info2.setNcip("*******");
        info2.setVirtType("dragon");
        info2.setVirtType2("MOC1.5");
        info2.setAzone("azone2");
        info2.setRegion("region2");
        info2.setClusterId("cluster2");
        info2.setNcMode("shared");
        info2.setInstanceFamily("family2");
        info2.setTime(new Date(1659369600000L));

        NcAssociationInfo info3 = new NcAssociationInfo();
        info3.setNcip("*******");
        info3.setVirtType("dragon");
        info3.setVirtType2("MOC2.0");
        info3.setAzone("azone3");
        info3.setRegion("region3");
        info3.setClusterId("cluster3");
        info3.setNcMode("unknown");
        info3.setInstanceFamily("family3");
        info3.setTime(new Date(1659456000000L));

        associationInfos.add(info1);
        associationInfos.add(info2);
        associationInfos.add(info3);

        int cnt = associationDao.batchInsertAssociationInfo(associationInfos);
        Assert.assertEquals(3, cnt);
    }

    @Test
    public void testGetAssociationInfoByNcIp() {
        List<String> ipList = Arrays.asList("*******", "*******");
        long now = System.currentTimeMillis() % 10000000 * 1000;
        ipList.forEach(ip -> addAssociation(now, ip));

        List<NcAssociationInfo> associationInfo = associationDao.getAssociationInfoByNcIp(ipList, new Date(now - 10000), new Date(now + 10000));
        Assert.assertNotNull(associationInfo);
        Assert.assertTrue(associationInfo.size() >= ipList.size());
        for (NcAssociationInfo info : associationInfo) {
            Assert.assertNotNull(info);
            Assert.assertTrue(ipList.contains(info.getNcip()));
        }
    }

    @Test
    public void testGetDistinctAssociationDetail() {
        List<String> ipList = Arrays.asList("*******", "*******");
        long now = System.currentTimeMillis() % 10000000 * 1000;
        ipList.forEach(ip -> addAssociation(now, ip));
        List<String> associationDetailList = associationDao.getDistinctAssociationDetail("virt_type2",
            new Date(now - 10000), new Date(now + 10000));
        Assert.assertNotNull(associationDetailList);
        Assert.assertFalse(associationDetailList.isEmpty());
    }

    @Test
    public void testCountAndDelete() {
        List<String> ipList = Arrays.asList("*******", "*******");
        long now = System.currentTimeMillis() % 10000000 * 1000;
        ipList.forEach(ip -> addAssociation(now, ip));
        int cnt = associationDao.countAssociation(new Date(now));
        Assert.assertEquals(2, cnt);
        associationDao.deleteByTime(new Date(now));
        cnt = associationDao.countAssociation(new Date(now));
        Assert.assertEquals(0, cnt);
    }
}
