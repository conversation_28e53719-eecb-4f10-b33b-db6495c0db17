package com.aliyun.xdragon.biz.correlation.config;

import junit.framework.TestCase;
import org.junit.Test;

import java.util.List;
import java.util.Map;

public class CorrelationExclusionConfigTest extends TestCase {

    CorrelationExclusionConfig config = new CorrelationExclusionConfig();


    @Test
    public void testGetExclusionMap() {
        Map<String, List<String>> exclusionMap = config.exclusionMap;
        assertNotNull(exclusionMap);
        assertTrue(exclusionMap.size() > 0);
    }

}