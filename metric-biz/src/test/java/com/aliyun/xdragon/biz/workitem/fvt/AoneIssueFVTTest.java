package com.aliyun.xdragon.biz.workitem.fvt;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import com.alibaba.common.lang.StringUtil;

import cn.hutool.http.HtmlUtil;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.xdragon.biz.workitem.aone.AoneIssueTask;
import com.aliyun.xdragon.common.model.AoneIssue;
import com.aliyun.xdragon.service.common.service.aone.AoneIssueProxy;
import com.aliyun.xdragon.service.common.util.Checks;
import com.aliyun.xdragon.service.common.config.aone.AoneConstants;
import com.aliyun.xdragon.service.common.util.aone.AoneIssueUtils;
import com.aliyun.xdragon.common.model.workItem.CreateIssue;
import com.aliyun.xdragon.biz.workitem.service.WorkItemService;
import com.aliyun.xdragon.biz.BaseTestCase;
import com.aliyun.xdragon.service.common.config.sls.NbSlsClientFactoryService;
import com.aliyun.xdragon.biz.workitem.utils.SlsOperation;
import com.taobao.api.response.KeludeIssueGetResponse;
import com.taobao.api.response.KeludeTagGetbytargetResponse;
import com.taobao.api.response.KeludeTagGetbytargetResponse.Tag;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class AoneIssueFVTTest extends BaseTestCase {

    private static final long TEST_ISSUE_ID = 45051199L;

    //默认的测试aone的projectID
    private static final Long TEST_PROJECT_ID = 1190449L;

    private static final String TEST_ISSUE_SUBJECT = "this is title";
    private static final String TEST_ISSUE_CREATE_TIME = "2022-09-20 11:00";
    @Resource
    private AoneIssueProxy aoneIssueProxy;

    @Resource
    private AoneIssueTask aoneIssueTask;

    @Resource
    private WorkItemService workItemService;
    @Autowired
    private NbSlsClientFactoryService slsClientFactory;

    public static final String DEFAULT_AONE_TRACING_LOGSTORE_TEST = "issue_tracing_test";

    private void createSlsLogTest(Long akProjectId, String logStore, int size) throws Exception {
        SlsOperation op = new SlsOperation(slsClientFactory.getClient("cn-hangzhou-corp"));

        DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        String createAt = fmt.print(new DateTime());

        for (int i = 0; i < size; i++) {
            Map<String, String> maps = new HashMap<>();
            maps.put("author", "024194");
            maps.put("assignedTo", "024194");
            maps.put("subject", "test create issue");
            maps.put("description", "create issue description");
            maps.put("moduleName", "单元测试");
            maps.put("createAt", createAt);
            if (akProjectId != null) {
                maps.put("akProjectId", akProjectId.toString());
            }
            op.pushLogs(AoneConstants.DEFAULT_XUNJIAN_PROJECT, logStore, maps);
        }
    }

    /**
     * 只是往目标logstore写日志，作为例子，无实际意义，先ignore
     *
     * @throws Exception
     */
    @Test
    @Ignore
    public void createSlsLogTestV2() throws Exception {

        String ak = slsClientFactory.getAk();
        String sk = slsClientFactory.getSk();

        String logstoreName = DEFAULT_AONE_TRACING_LOGSTORE_TEST;
        String projectName = "ecs-xunjian";

        Client client = new Client("cn-hangzhou-corp.sls.xxxxx.com", ak, sk);

        DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        String createAt = fmt.print(new DateTime());

        for (int i = 0; i < 2; i++) {
            Map<String, String> maps = new HashMap<>();
            maps.put("author", "024194");
            maps.put("assignedTo", "024194");
            maps.put("subject", "test create issue");
            maps.put("description", "create issue description");
            maps.put("moduleName", "单元测试");
            maps.put("createAt", createAt);
            maps.put("akProjectId", TEST_PROJECT_ID.toString());

            System.out.println(String.format("ready to push logs for %s", logstoreName));
            List<LogItem> logGroup = new ArrayList();
            LogItem logItem = new LogItem();
            Iterator it = maps.entrySet().iterator();

            while (it.hasNext()) {
                Map.Entry<String, String> kv = (Map.Entry)it.next();
                String key = kv.getKey();
                String val = kv.getValue();
                logItem.PushBack(key, val);
            }

            logGroup.add(logItem);
            client.PutLogs(projectName, logstoreName, "", logGroup, "");
        }
    }

    /**
     * 生产环境往目标logstore写入日志，并等待指定的时间程序间隔后，获得预期的aone数据
     * 默认ignore该方法，因为涉及生产链路logstore的读写
     *
     * @throws Exception
     */
    @Test
    @Ignore
    public void aoneConsumerTestWithPrd() throws Exception {

        DateTime current = new DateTime();
        DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

        DateTime before = current.minusSeconds(30);
        DateTime end = current.plusSeconds(30);

        Date strStart = before.toDate();
        Date strEnd = end.toDate();

        //创建两条SLS测试日志
        int i = 2;

        this.createSlsLogTest(TEST_PROJECT_ID, "issue_tracing", i);

        Set<Long> projectIds = new HashSet<>();
        projectIds.add(TEST_PROJECT_ID);

        //确保日志已经写入到sls
        Thread.sleep(10 * 1000);

        //查询过往已经写入的aone, 预期的数量和插入的数量相同
        List<KeludeIssueGetResponse.Issue> items = workItemService.searchWorkItemByCreatTime(projectIds, strStart,
            strEnd);

        Assert.assertEquals(2, items.size());
    }

    @Test
    @Ignore("耗时严重先屏蔽")
    public void aoneConsumerTest() throws Exception {
        long cur = System.currentTimeMillis();
        String group = "aone_fvt_" + cur % 10000;

        try {
            //开始sls消费, 使用新group，从当前时间前开始消费
            aoneIssueTask.processSlsLog(group, (int)(cur / 1000) - 60);
            DateTime current = new DateTime();
            DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

            //等待后端线程启动
            Thread.sleep(10000);

            //创建两条SLS测试日志
            int i = 2;
            this.createSlsLogTest(TEST_PROJECT_ID, DEFAULT_AONE_TRACING_LOGSTORE_TEST, i);

            DateTime before = current.minusMinutes(2);
            DateTime end = current.plusMinutes(2);

            Set<Long> projectIds = new HashSet<>();
            projectIds.add(TEST_PROJECT_ID);

            //查询过往已经写入的aone, 预期的数量和插入的数量相同
            List<KeludeIssueGetResponse.Issue> items = null;
            for (int j = 0; j < 60; j++) {
                items = workItemService.searchWorkItemByCreatTime(projectIds, before.toDate(), end.toDate());
                if (items.size() >= 2) {
                    break;
                } else {
                    Thread.sleep(1000);
                }
            }

            //校验返回的结果是否符合期望, 这里先避免了可能有其他场景插入的数据，所以这里按>=2处理
            Assert.assertTrue(items.size() >= 2);
        } finally {
            aoneIssueTask.stopSlsWorker(group);
        }
    }

    /**
     * 创建issue测试，因为涉及写操作，且不能删除，可忽略该case，避免重复构建
     */
    @Test
    //@Ignore
    public void createIssueWithLogMapTest() {

        DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        String createAt = fmt.print(new DateTime());

        Map<String, String> maps = new HashMap<>();
        maps.put("author", "024194");
        maps.put("assignedTo", "024194");
        maps.put("subject", "test create issue");
        maps.put("description", "create issue description");
        maps.put("moduleName", "单元测试");
        maps.put("createAt", createAt);
        //maps.put("expiredAt","2022-04-14 08:04:00");

        CreateIssue issueObj = AoneIssueUtils.createAoneIssue(maps);

        Checks.notNull("check arguments error ", issueObj.getAuthor(), issueObj.getAssignedTo(),
            issueObj.getSubject(), issueObj.getDescription(), issueObj.getCreateAt(), issueObj.getModuleName());

        //此两项属性，用户输入无效，以默认值为准
        issueObj.setAkProjectId(TEST_PROJECT_ID);
        issueObj.setStamp(AoneConstants.DEFAULT_STAMP);

        //IssueTypeName如果为空，以默认值为准
        if (StringUtil.isEmpty(issueObj.getIssueTypeName())) {
            issueObj.setIssueTypeName(AoneConstants.DEFAULT_ISSUE_TYPE_NAME);
        }
        //PriorityName如果为空，以默认值为准
        if (StringUtil.isEmpty(issueObj.getPriorityName())) {
            issueObj.setPriorityName(AoneConstants.DEFAULT_PRIORITY_NAME);
        }

        //创建aone
        Long createResult = aoneIssueProxy.createIssue(issueObj);
        Assert.assertNotNull(createResult);
        //查询刚创建的aone
        KeludeIssueGetResponse.Issue queryResult = aoneIssueProxy.searchWorkItemById(createResult);
        Assert.assertNotNull(queryResult);

        //查询指定的标签
        List<KeludeTagGetbytargetResponse.Tag> tag = aoneIssueProxy.getTagByNameAndTarget(null, TEST_PROJECT_ID,
            "AKProject", null, null);
        Assert.assertNotNull(tag);

        //String tagName = issueObj.getTagName();
        //
        ////为创建的vm添加标签
        //Result<Integer> tagIdResult = aoneIssueProxy.addTag(createResult.getResult(), tagName, issueObj.getAuthor() );

        //Assert.assertTrue(tagIdResult.isSuccess());

    }

    /**
     * 创建issue测试，因为涉及aone写操作
     */
    @Test
    @Ignore
    public void createIssueTest() {

        DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter fmt2 = DateTimeFormat.forPattern("yyyy-MM-dd");
        String createAt = fmt.print(new DateTime());
        String expiredAt = fmt2.print(new DateTime());

        CreateIssue issue = new CreateIssue();
        issue.setAuthor("024194");
        issue.setAssignedTo("024194");
        issue.setSubject("test create issue");

        issue.setIssueTypeName(AoneConstants.DEFAULT_ISSUE_TYPE_NAME);
        issue.setExpiredAt(expiredAt);
        issue.setDescription("create issue description");
        issue.setAkProjectId(TEST_PROJECT_ID);
        issue.setCreateAt(createAt);
        issue.setStamp(AoneConstants.DEFAULT_STAMP);

        issue.setPriorityName(AoneConstants.DEFAULT_PRIORITY_NAME);

        issue.setModuleName("单元测试");

        //创建aone
        Long createResult = aoneIssueProxy.createIssue(issue);

        Assert.assertNotNull(createResult);
        KeludeIssueGetResponse.Issue queryResult = aoneIssueProxy.searchWorkItemById(createResult);
        Assert.assertNotNull(queryResult);

        //查询指定的标签
        List<Tag> tag = aoneIssueProxy.getTagByNameAndTarget(null, TEST_PROJECT_ID, "AKProject", null, null);
        Assert.assertNotNull(tag);

        Tag tag1 = workItemService.getTagById(tag.get(0).getId());
        Assert.assertNotNull(tag1);

        String tagName = "单元测试";

        //为创建的vm添加标签
        Boolean tagIdResult = aoneIssueProxy.addTag(TEST_PROJECT_ID, createResult, tagName,
            issue.getAuthor());
        //int tagId = tag.getResult().get(0).getId();
        //Result<Integer> tagIdResult = issueTopService.addTagging(createResult.getResult(), Arrays.asList(tagId),
        // "024194");
        Assert.assertTrue(tagIdResult);
    }

    @Test
    public void searchIssueByIdTest() {
        KeludeIssueGetResponse.Issue result = aoneIssueProxy.searchWorkItemById(TEST_ISSUE_ID);

        String subject = result.getSubject();
        Date createTime = result.getCreatedAt();
        String description = HtmlUtil.cleanHtmlTag(result.getDescription());
        description = HtmlUtil.unescape(description);
        Assert.assertNotNull(description);
        Assert.assertEquals(TEST_ISSUE_SUBJECT, subject);

        DateTime ct = new DateTime(createTime);
        String strCreateTime = ct.toString(DateTimeFormat.forPattern("yyyy-MM-dd HH:mm"));

        Assert.assertEquals(TEST_ISSUE_CREATE_TIME, strCreateTime);
    }

    @Test
    public void getAoneIssue() {
        String url1 = "http://aone.alibaba-inc.com/";
        try {
            aoneIssueProxy.getAoneIssue(url1);
            Assert.fail();
        } catch (IllegalArgumentException e) {
            Assert.assertTrue(e.getMessage().contains("unknown aone url"));
        }

        String url2 = "https://aone.alibaba-inc.com/v2/project/1182328/bug/49144650";
        AoneIssue issue = aoneIssueProxy.getAoneIssue(url2);
        Assert.assertEquals("Closed", issue.getStatus());
        Assert.assertEquals("043709", issue.getUserStaffId());
    }
}
