package com.aliyun.xdragon.biz.workitem.service.impl;

import com.alibaba.ecs.devops.consistent.queryField.VmQueryField.VmField;
import com.alibaba.ecs.devops.consistent.response.Vm;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.xdragon.api.service.chat.model.UserOperationRecordInfo;
import com.aliyun.xdragon.service.common.service.DevOpsApiService;
import com.aliyun.xdragon.service.common.util.DateUtil;
import com.aliyuncs.ecsops.model.v20160401.OpsDescribeResourceActionTrailInfoRequest;
import com.aliyuncs.ecsops.model.v20160401.OpsDescribeResourceActionTrailInfoResponse;
import com.aliyuncs.exceptions.ClientException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service
public class UserOperationRecordServiceImpl extends DevOpsApiService {
    private static final Logger logger = LoggerFactory.getLogger(UserOperationRecordServiceImpl.class);

    @Autowired
    DetailVmConsistentImpl detailVmConsistent;

    public List<UserOperationRecordInfo> queryActionTrail(String vm, Date startTime, Date endTime) throws ClientException {
        List<String> vmList = Collections.singletonList(vm);
        Vm vmInfo = detailVmConsistent.queryVmInfo(vmList, VmField.INSTANCE_ID, VmField.ALI_UID, VmField.REGION_NO_ALIAS)
            .stream().findFirst().orElseThrow(() -> new RuntimeException("vm not found"));

        return queryActionTrail(vmList, Long.valueOf(vmInfo.getAliUid()), vmInfo.getRegionNoAlias(), startTime, endTime)
            .values().stream().findFirst().orElse(new ArrayList<>());
    }

    //查询用户实例操作记录
    public HashMap<String,List<UserOperationRecordInfo>> queryActionTrail(List<String> vmList, Long aliUid, String regionId, Date startTime, Date endTime) throws ClientException {
        //查询用户的VM操作
        OpsDescribeResourceActionTrailInfoRequest actionTrailInfoRequest
                = new OpsDescribeResourceActionTrailInfoRequest();
        actionTrailInfoRequest.setResourceIds(vmList);
        actionTrailInfoRequest.setResourceType("instance");
        actionTrailInfoRequest.setAliUid(aliUid);
        actionTrailInfoRequest.setRegionId(regionId);
        actionTrailInfoRequest.setStartTime(DateUtil.toUTCDateString(startTime));
        actionTrailInfoRequest.setEndTime(DateUtil.toUTCDateString(endTime));

        HashMap<String,List<UserOperationRecordInfo>> userOperationRecordMap = new HashMap<>();
        OpsDescribeResourceActionTrailInfoResponse response = queryOpsApi(actionTrailInfoRequest,3);
        List<OpsDescribeResourceActionTrailInfoResponse.Resource> resources = response.getResources();
        if(CollectionUtils.isEmpty(resources)){
            return userOperationRecordMap;
        }
        resources.forEach(resource -> {
            String resourceId = resource.getResourceId();
            List<OpsDescribeResourceActionTrailInfoResponse.Resource.ResourceAction> resourceActionList = resource.getResourceActions();
            if(CollectionUtils.isEmpty(resourceActionList)){
                return;
            }
            List<UserOperationRecordInfo> userOperationRecordInfos = new ArrayList<>();
            resourceActionList.forEach(resourceAction -> {
                UserOperationRecordInfo userOperationRecordInfo = new UserOperationRecordInfo();
                userOperationRecordInfo.setActionEventName(resourceAction.getActionEventName());
                userOperationRecordInfo.setActionEventTime(DateUtil.UTCDateString2Date(resourceAction.getActionEventTime()));
                userOperationRecordInfo.setActionEventType(resourceAction.getActionEventType());
                userOperationRecordInfo.setActionSuccess(resourceAction.getActionSuccess());
                userOperationRecordInfo.setErrorCode(resourceAction.getActionErrorCode());
                userOperationRecordInfos.add(userOperationRecordInfo);
            });
            userOperationRecordMap.put(resourceId,userOperationRecordInfos);
        });
        logger.info("UserOperationRecordServiceImpl queryActionTrail:{}",JSONObject.toJSONString(userOperationRecordMap));
        return userOperationRecordMap;
    }
}
