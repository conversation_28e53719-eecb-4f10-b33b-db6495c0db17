package com.aliyun.xdragon.biz.log.manager.utils;

import com.aliyun.xdragon.biz.log.manager.param.ConfigParam;
import com.aliyun.xdragon.common.enumeration.log.TaskManagerType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/07/26
 */
public class TaskTemplate {
    private static final Logger logger = LoggerFactory.getLogger(TaskTemplate.class);
    public static Map<String, Object> propDefaults;
    public static Map<String, Object> propStrDefaults;
    public static Map<String, Object> sqlDefaults;

    public static Map<String, Object> unionPropDefaults;
    public static Map<String, Object> unionSqlDefaults;

    public static Map<String, Object> matchPropDefaults;
    public static Map<String, Object> matchSqlDefaults;

    public static Map<String, Object> onlineSqlDefaults;

    static {
        propDefaults = new HashMap<>(40);
        propDefaults.put("task_id", null);
        propDefaults.put("region", null);
        propDefaults.put("input_access_id", null);
        propDefaults.put("input_access_key", null);
        propDefaults.put("input_endPoint", null);
        propDefaults.put("input_project", null);
        propDefaults.put("input_logStore", null);
        propDefaults.put("input_consumer_group", null);
        propDefaults.put("adb_url", null);
        propDefaults.put("adb_userName", null);
        propDefaults.put("adb_password", null);
        propDefaults.put("adb_sinkTableName", null);
        propDefaults.put("adb_detailTableName", null);
        propDefaults.put("support", null);
        propDefaults.put("rsupport", null);
        propDefaults.put("cutLines", null);
        propDefaults.put("cutLength", null);
        propDefaults.put("weightThreshold", 0.04);
        propDefaults.put("indicesStr", null);
        propDefaults.put("trim_mode", "top");
        propDefaults.put("threshold", 1000);
        propDefaults.put("sls_access_id", "null");
        propDefaults.put("sls_access_key", "null");
        propDefaults.put("sls_endPoint", "cn-wulanchabu-share.log.aliyuncs.com");
        propDefaults.put("sls_project", "ecs-predict-result");
        propDefaults.put("sls_logStore", "xdragon-metric-log-pattern");
        propDefaults.put("gamma_url", "**********************************************************************");
        propDefaults.put("gamma_table_name", "ecs_release_plan_gamma_host_v2");
        propDefaults.put("gamma_user_name", "ecs_plan");
        propDefaults.put("filter_url", "********************************************************************************");
        propDefaults.put("filter_table_name", "nc_filter_newest");
        propDefaults.put("filter_user_name", "metric_prod");
        propDefaults.put("mapping_tableName", "log_pattern_mapping");

        propStrDefaults = new HashMap<>(40);
        propStrDefaults.put("support", null);
        propStrDefaults.put("rsupport", null);
        propStrDefaults.put("cutLines", null);
        propStrDefaults.put("cutLength", null);
        propStrDefaults.put("weightThreshold", 0.4);
        propStrDefaults.put("indicesStr", null);
        propStrDefaults.put("trim_mode", "top");
        propStrDefaults.put("threshold", 1000);
        propStrDefaults.put("task_id", null);
        propStrDefaults.put("region", "center");
        propStrDefaults.put("input_logStore", null);
        propStrDefaults.put("library_endpoint", null);
        propStrDefaults.put("library_id", null);
        propStrDefaults.put("library_key", null);
        propStrDefaults.put("library_project", null);

        unionPropDefaults = new HashMap<>(40);
        unionPropDefaults.put("region", null);
        unionPropDefaults.put("input_access_id", null);
        unionPropDefaults.put("input_access_key", null);
        unionPropDefaults.put("input_endPoint", null);
        unionPropDefaults.put("input_project", null);
        unionPropDefaults.put("input_logStore", null);
        unionPropDefaults.put("gamma_url", "**********************************************************************");
        unionPropDefaults.put("gamma_table_name", "ecs_release_plan_gamma_host_v2");
        unionPropDefaults.put("gamma_user_name", "ecs_plan");
        unionPropDefaults.put("filter_url", "********************************************************************************");
        unionPropDefaults.put("filter_table_name", "nc_filter_newest");
        unionPropDefaults.put("filter_user_name", "metric_prod");

        matchPropDefaults = new HashMap<>(40);
        matchPropDefaults.put("task_id", null);
        matchPropDefaults.put("input_logStore", null);
        matchPropDefaults.put("adb_url", null);
        matchPropDefaults.put("adb_userName", null);
        matchPropDefaults.put("adb_password", null);
        matchPropDefaults.put("adb_sinkTableName", null);
        matchPropDefaults.put("adb_detailTableName", null);
        matchPropDefaults.put("sls_access_id", null);
        matchPropDefaults.put("sls_access_key", null);
        matchPropDefaults.put("sls_endPoint", "cn-wulanchabu-share.log.aliyuncs.com");
        matchPropDefaults.put("sls_project", "ecs-predict-result");
        matchPropDefaults.put("sls_logStore", "xdragon-metric-log-pattern");

        sqlDefaults = new HashMap<>(32);
        sqlDefaults.put("time", null);
        sqlDefaults.put("description", null);
        sqlDefaults.put("raw", null);
        sqlDefaults.put("raw_usage", null);
        sqlDefaults.put("raw_filter", null);
        sqlDefaults.put("filter_fields", null);
        sqlDefaults.put("ext_fields", null);
        sqlDefaults.put("offset", null);
        sqlDefaults.put("filter_trans", null);
        sqlDefaults.put("ext_trans", null);
        sqlDefaults.put("filter_usage", null);
        sqlDefaults.put("ext_usage", null);
        sqlDefaults.put("window", null);
        sqlDefaults.put("sls_sink", "");
        sqlDefaults.put("sls_insert", "");
        sqlDefaults.put("filter_table", "");
        sqlDefaults.put("gamma_table", "");
        sqlDefaults.put("filter_status", "");
        sqlDefaults.put("filter_gamma", "");
        sqlDefaults.put("join_prefix", "");
        sqlDefaults.put("join_alias", "");
        sqlDefaults.put("filter_status_where", "");
        sqlDefaults.put("filter_gamma_where", "");
        sqlDefaults.put("remain.accessId", "");
        sqlDefaults.put("remain.accessKey", "");
        sqlDefaults.put("remain.endPoint", "");
        sqlDefaults.put("remain.project", "");
        sqlDefaults.put("remain.logStore", "");

        unionSqlDefaults = new HashMap<>(32);
        unionSqlDefaults.put("description", null);
        unionSqlDefaults.put("raw", null);
        unionSqlDefaults.put("raw_usage", null);
        unionSqlDefaults.put("raw_filter", null);
        unionSqlDefaults.put("filter_fields", null);
        unionSqlDefaults.put("ext_fields", null);
        unionSqlDefaults.put("filter_trans", null);
        unionSqlDefaults.put("ext_trans", null);
        unionSqlDefaults.put("filter_usage", null);
        unionSqlDefaults.put("ext_usage", null);
        unionSqlDefaults.put("filter_table", "");
        unionSqlDefaults.put("gamma_table", "");
        unionSqlDefaults.put("filter_status", "");
        unionSqlDefaults.put("filter_gamma", "");

        matchSqlDefaults = new HashMap<>(4);
        matchSqlDefaults.put("window", null);
        matchSqlDefaults.put("pattern_source_id", null);
        matchSqlDefaults.put("count_source_id", null);

        onlineSqlDefaults = new HashMap<>(8);
        onlineSqlDefaults.put("task_id", null);
        onlineSqlDefaults.put("logstore", null);
        onlineSqlDefaults.put("window", null);
        onlineSqlDefaults.put("pattern_source_id", null);
        onlineSqlDefaults.put("count_source_id", null);
    }

    public static boolean checkPropMap(Map<String, Object> dataMap) {
        return checkPropMapAux(dataMap, propDefaults);
    }

    public static boolean checkPropStrMap(Map<String, Object> dataMap) {
        return checkPropMapAux(dataMap, propStrDefaults);
    }

    public static boolean checkUnionPropMap(Map<String, Object> dataMap) {
        return checkPropMapAux(dataMap, unionPropDefaults);
    }

    public static boolean checkMatchProp(ConfigParam configParam) {
        if (configParam == null || configParam.getLogClusterSlsConfig() == null || configParam.getMetricSlsConfig() == null) {
            return false;
        }
        if (configParam.getProp() == null || configParam.getProp().getInputLogStore() == null) {
            return false;
        }
        configParam.getProp().setSlsEndPoint(String.valueOf(matchPropDefaults.getOrDefault("sls_endPoint", configParam.getProp().getSlsEndPoint())));
        configParam.getProp().setSlsProject(String.valueOf(matchPropDefaults.getOrDefault("sls_project", configParam.getProp().getSlsProject())));
        configParam.getProp().setSlsLogStore(String.valueOf(matchPropDefaults.getOrDefault("sls_logStore", configParam.getProp().getSlsLogStore())));
        return true;
    }

    public static boolean checkClusterProp(ConfigParam configParam) {
        if (configParam == null || configParam.getProp() == null || configParam.getLogClusterSlsConfig() == null || configParam.getMetricSlsConfig() == null) {
            return false;
        }
        configParam.getProp().setSlsEndPoint(String.valueOf(matchPropDefaults.getOrDefault("sls_endPoint", configParam.getProp().getSlsEndPoint())));
        configParam.getProp().setSlsProject(String.valueOf(matchPropDefaults.getOrDefault("sls_project", configParam.getProp().getSlsProject())));
        configParam.getProp().setSlsLogStore(String.valueOf(matchPropDefaults.getOrDefault("sls_logStore", configParam.getProp().getSlsLogStore())));
        return true;
    }

    private static boolean checkPropMapAux(Map<String, Object> dataMap, Map<String, Object> propDefault) {
        for (Map.Entry<String, Object> entry : propDefault.entrySet()) {
            if (dataMap.containsKey(entry.getKey()) && dataMap.get(entry.getKey()) != null) {
                continue;
            }
            if (entry.getValue() == null) {
                logger.error("Required field {} for prop not found!", entry.getKey());
                return false;
            }
            logger.info("Required field {} for prop miss, use default value {}.", entry.getKey(), entry.getValue());
            dataMap.put(entry.getKey(), entry.getValue());
        }
        return true;
    }

    public static boolean checkSqlMap(Map<String, Object> dataMap) {
        return checkSqlMapAux(dataMap, sqlDefaults);
    }

    public static boolean checkUnionSqlMap(Map<String, Object> dataMap) {
        return checkSqlMapAux(dataMap, unionSqlDefaults);
    }

    public static boolean checkMatchSqlMap(Map<String, Object> dataMap) {
        return checkSqlMapAux(dataMap, matchSqlDefaults);
    }

    public static boolean checkClusterSqlMap(Map<String, Object> dataMap) {
        return checkSqlMapAux(dataMap, onlineSqlDefaults);
    }

    private static boolean checkSqlMapAux(Map<String, Object> dataMap, Map<String, Object> sqlDefault) {
        for (Map.Entry<String, Object> entry : sqlDefault.entrySet()) {
            if (dataMap.containsKey(entry.getKey()) && dataMap.get(entry.getKey()) != null) {
                continue;
            }
            if (entry.getValue() == null) {
                logger.error("Required field {} for sql not found!", entry.getKey());
                return false;
            }
            logger.info("Required field {} for sql miss, use default value {}.", entry.getKey(), entry.getValue());
            dataMap.put(entry.getKey(), entry.getValue());
        }
        return true;
    }

    public static String getPropTemplateFile(TaskManagerType type, Long taskId, Long cid) {
        String defaultFile = String.format("templates/logcluster/%s/prop_default.tpl", type.getName());
        String taskFile = String.format("templates/logcluster/%s/prop_%d.tpl", type.getName(), taskId);
        String regionTaskFile = String.format("templates/logcluster/%s/prop_%d_%d.tpl", type.getName(), taskId, cid);
        if (TaskTemplate.class.getClassLoader().getResource(regionTaskFile) != null) {
            return regionTaskFile;
        } else if (TaskTemplate.class.getClassLoader().getResource(taskFile) != null) {
            return taskFile;
        } else {
            return defaultFile;
        }
    }

    public static String getSqlTemplateFile(TaskManagerType type, Long taskId, Long cid) {
        String defaultFile = String.format("templates/logcluster/%s/sql_default.tpl", type.getName());
        String taskFile = String.format("templates/logcluster/%s/sql_%d.tpl", type.getName(), taskId);
        String regionTaskFile = String.format("templates/logcluster/%s/sql_%d_%d.tpl", type.getName(), taskId, cid);
        if (TaskTemplate.class.getClassLoader().getResource(regionTaskFile) != null) {
            return regionTaskFile;
        } else if (TaskTemplate.class.getClassLoader().getResource(taskFile) != null) {
            return taskFile;
        } else {
            return defaultFile;
        }
    }

    public static String getUnionSqlTemplateFile(TaskManagerType type, Long taskId, Long unionId) {
        String defaultFile = String.format("templates/logcluster/%s/union_sql_default.tpl", type.getName());
        String taskFile = String.format("templates/logcluster/%s/union_sql_%d.tpl", type.getName(), taskId);
        String regionTaskFile = String.format("templates/logcluster/%s/union_sql_%d_%d.tpl", type.getName(), taskId, unionId);
        if (TaskTemplate.class.getClassLoader().getResource(regionTaskFile) != null) {
            return regionTaskFile;
        } else if (TaskTemplate.class.getClassLoader().getResource(taskFile) != null) {
            return taskFile;
        } else {
            return defaultFile;
        }
    }

    public static String getMatchSqlTemplateFile(Long taskId) {
        String defaultFile = "templates/logcluster/flink/match_sql_default.tpl";
        String taskFile = String.format("templates/logcluster/flink/match_sql_%d.tpl", taskId);
        if (TaskTemplate.class.getClassLoader().getResource(taskFile) != null) {
            return taskFile;
        }
        return defaultFile;
    }

    public static String getClusterSqlTemplateFile(Long taskId) {
        String defaultFile = "templates/logcluster/flink/online_sql_default.tpl";
        String taskFile = String.format("templates/logcluster/flink/online_sql_%d.tpl", taskId);
        if (TaskTemplate.class.getClassLoader().getResource(taskFile) != null) {
            return taskFile;
        }
        return defaultFile;
    }
}
