package com.aliyun.xdragon.biz.integration.utils;

import com.aliyun.xdragon.common.generate.model.XdragonMonitorAlertInfo;
import com.aliyun.xdragon.common.generate.model.XdragonMonitorInfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
public class XamAlertUtil {

    public static final String XAM_ROUTER_NAME_SUFFIX = "-fast_integration";

    public static final String SOURCE_LABEL = "fast_integration";

    public static List<String> getMonitorLabel(XdragonMonitorInfo xdragonMonitorInfo, List<XdragonMonitorAlertInfo> alertInfos) {
        List<String> monitorLabels = new ArrayList<>();
        alertInfos.forEach(alertInfo -> {
            // 添加 monitorLabel，配置成一个统一的话，多个渠道的通知会因为 resourceId 和 monitorLabel 都相同而报错
            monitorLabels.add(getRouterAlertUniqueLabel(xdragonMonitorInfo, alertInfo));
        });
        return monitorLabels;
    }

    public static String getRouterAlertUniqueLabel(XdragonMonitorInfo xdragonMonitorInfo, XdragonMonitorAlertInfo alertInfo) {
        // 带上 source，用于创建 config 时传递 label
        return SOURCE_LABEL + ":" + xdragonMonitorInfo.getMonitorName() + "-" + alertInfo.getAlertName();
    }

    public static String getMonitorLabel(XdragonMonitorInfo xdragonMonitorInfo, XdragonMonitorAlertInfo alertInfo) {
        // 不携带 source，用于发送 alertItem 时使用，通知内会将 source 组装到 label 中
        return xdragonMonitorInfo.getMonitorName() + "-" + alertInfo.getAlertName();
    }

    public static String getRouterName(XdragonMonitorInfo xdragonMonitorInfo) {
        return xdragonMonitorInfo.getMonitorName() + XamAlertUtil.XAM_ROUTER_NAME_SUFFIX;
    }
}
