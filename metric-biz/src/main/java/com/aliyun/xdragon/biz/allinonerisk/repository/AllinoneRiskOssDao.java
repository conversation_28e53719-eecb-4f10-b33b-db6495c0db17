package com.aliyun.xdragon.biz.allinonerisk.repository;

import java.io.ByteArrayInputStream;

import com.aliyun.xdragon.biz.allinonerisk.config.AllinoneRiskOssConfig;
import com.aliyun.xdragon.biz.log.repository.AbstractOssDao;
import com.aliyun.xdragon.common.model.OssConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class AllinoneRiskOssDao extends AbstractOssDao {
    @Autowired
    private AllinoneRiskOssConfig config;

    private static final String IMAGE_DIR = "batch_risk";

    @Override
    protected OssConfig ossConfig() {
        return config;
    }

    public void uploadImage(String filename, byte[] content) {
        uploadFile(getFilePath(filename), new ByteArrayInputStream(content));
    }

    public String getImageUrl(String filename) {
        return getOssUrl(getFilePath(filename), null);
    }

    private String getFilePath(String filename) {
        return String.format("%s/%s", IMAGE_DIR, filename);
    }

}
