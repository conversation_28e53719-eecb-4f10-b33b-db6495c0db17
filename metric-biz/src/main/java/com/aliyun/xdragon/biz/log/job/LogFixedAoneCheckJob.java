package com.aliyun.xdragon.biz.log.job;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.xdragon.api.service.workitem.AoneTopService;
import com.aliyun.xdragon.biz.log.repository.LogAnomalyAoneDao;
import com.aliyun.xdragon.biz.log.repository.PatternDao;
import com.aliyun.xdragon.biz.log.repository.PatternTagDao;
import com.aliyun.xdragon.biz.workitem.service.WorkItemService;
import com.aliyun.xdragon.common.enumeration.PatternTagSet;
import com.aliyun.xdragon.common.generate.log.model.LogAnomalyAone;
import com.aliyun.xdragon.common.generate.log.model.LogClusterPattern;
import com.aliyun.xdragon.common.generate.log.model.LogPatternTag;
import com.aliyun.xdragon.service.common.job.AbstractMapTask;
import com.aliyun.xdragon.service.common.util.DateUtil;
import com.google.common.collect.Lists;
import com.taobao.api.response.KeludeIssueGetResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/14
 */
@Component
@Lazy
public class LogFixedAoneCheckJob extends AbstractMapTask<Long> {
    private static final Logger logger = LoggerFactory.getLogger(LogFixedAoneCheckJob.class);
    private static final List<String> openedStatus = Lists.newArrayList("Open", "Reopen", "New", "Later", "Worksforme");

    // yuejia
    private static final String AONE_MODIFIER = "370713";

    @Autowired
    private LogAnomalyAoneDao logAnomalyAoneDao;

    @Autowired
    private WorkItemService workItemService;

    @Autowired
    private AoneTopService aoneTopService;

    @Autowired
    private PatternDao patternDao;

    @Autowired
    private PatternTagDao patternTagDao;

    @Value("${domain.cloudbot}")
    private String cloudbotDomain;

    @Override
    public List<Long> genTask(JobContext context, int dataTs) {
        return logAnomalyAoneDao.getAllTaskIds();
    }

    @Override
    public ProcessResult processSubTask(JobContext context, int dataTs, int schedTs, Long subTask) throws Exception {
        // Get all aones
        List<Long> aoneIds = logAnomalyAoneDao.getAoneIdsByTask(subTask);
        if (aoneIds.isEmpty()) {
            logger.warn("[LogFixedAoneCheckJob] no aone found for task id {}", subTask);
            return new ProcessResult(true);
        }
        logger.info("[LogFixedAoneCheckJob] found {} aone for task id {}", aoneIds.size(), subTask);

        List<KeludeIssueGetResponse.Issue> aones = new ArrayList<>(aoneIds.size());
        for (int i = 0; i < aoneIds.size(); i += 100) {
            List<String> aoneStrList = aoneIds.subList(i, Math.min(i + 100, aoneIds.size())).stream().map(
                String::valueOf).collect(Collectors.toList());
            aones.addAll(workItemService.searchWorkItemsByIdList(aoneStrList));
        }
        if (aones.size() < aoneIds.size()) {
            logger.warn(
                "[LogFixedAoneCheckJob] found incorrect aone count for task id {}, expect cnt {}, actual cnt {}",
                subTask, aoneIds.size(), aones.size());
            return new ProcessResult(false);
        }

        // Update aone status and handler in record
        for (KeludeIssueGetResponse.Issue aone : aones) {
            Long aoneId = aone.getId();
            LogAnomalyAone logAnomalyAone = logAnomalyAoneDao.getLogAnomalyAoneByAoneId(aoneId);
            if (logAnomalyAone == null) {
                logger.warn("[LogFixedAoneCheckJob] no log anomaly aone found for aone id {}", aoneId);
                continue;
            }
            boolean updated = false;
            String oldStatus = logAnomalyAone.getStatus();
            String oldHandler = logAnomalyAone.getHandler();
            String status = aone.getStatus();
            String handler = aone.getAssignedTo();
            Date closeTime = null;
            if (!status.equals(oldStatus)) {
                logger.info(
                    "[LogFixedAoneCheckJob] update log anomaly aone for aone id {}, update status from {} to {}",
                    aoneId, oldStatus, status);
                updated = true;
                if (openedStatus.contains(oldStatus) && !openedStatus.contains(status)) {
                    closeTime = aone.getUpdatedAt();
                }
            }
            if (!handler.equals(oldHandler)) {
                logger.info(
                    "[LogFixedAoneCheckJob] update log anomaly aone for aone id {}, update handler from {} to {}",
                    aoneId, oldHandler, handler);
                updated = true;
            }
            if (updated) {
                int cnt = logAnomalyAoneDao.updateStatusAndHandler(aoneId, status, handler, closeTime);
                logger.info("[LogFixedAoneCheckJob] update {} logAnomalyAone status and/or handler", cnt);
            }
        }

        // Get aone list which need to reopen
        List<Long> reopenAoneIds = logAnomalyAoneDao.getReopenAoneList(subTask);
        List<Long> updatedIds = new LinkedList<>();
        for (Long aoneId : reopenAoneIds) {
            LogAnomalyAone aone = logAnomalyAoneDao.getLogAnomalyAoneByAoneId(aoneId);
            Long taskId = aone.getTaskId();
            String md5 = aone.getMd5();
            LogClusterPattern pattern = patternDao.getPatternByMd5(taskId, md5);
            // Ignore pattern md5 no need to reopen
            LogPatternTag tag = patternTagDao.getLogTagByMd5(taskId, md5);
            if (tag != null && tag.getIgnore() != null && tag.getIgnore() == PatternTagSet.SET.getTag()) {
                logger.info("[LogFixedAoneCheckJob] No need to reopen aone {} for task {} and pattern md5 {}, " +
                        "because the pattern is ignored", aoneId, taskId, md5);
                continue;
            }
            if (aoneTopService.updateIssueStatus(AONE_MODIFIER, aoneId, "Reopen")) {
                updatedIds.add(aoneId);
            } else {
                logger.warn("update aone status fail, aone id {}", aoneId);
                continue;
            }

            Date patternTime = pattern.getTime();
            long patternTs = patternTime.getTime();
            String startTime = DateUtil.dateString(new Date(patternTs - 3600 * 12 * 1000L));
            String endTime = DateUtil.dateString(new Date(patternTs + 3600 * 6 * 1000L));
            String url = String.format(
                "https://%s/cloudbot/ng2/#/moon-micro-main/log-system/log-pattern-distribution"
                    + "?taskId=%s&startTime=%s&endTime=%s&regions=%s&selectedIndex=0&md5=%s&anomalySwitch=false",
                cloudbotDomain, aone.getTaskId(),  URLEncoder.encode(startTime, "UTF-8"),
                URLEncoder.encode(endTime, "UTF-8"), aone.getRegion(), aone.getMd5());
            String comment = String.format("pattern appear again, appear time: %s, check url: %s",
                DateUtil.dateString(patternTime), url);
            if (!aoneTopService.addAoneComment(aoneId, comment, AONE_MODIFIER)) {
                logger.warn("add aone comment fail, aone id {}, comment: {}", aoneId, comment);
            }
        }
        if (!updatedIds.isEmpty()) {
            int cnt = logAnomalyAoneDao.updateReopenedLogAnomalyAone(updatedIds);
            logger.info("[LogFixedAoneCheckJob] reopen {} log anomaly aone, success cnt is {}", updatedIds.size(), cnt);
        }

        return new ProcessResult(true);
    }
}
