package com.aliyun.xdragon.biz.chat.handle;

import com.alibaba.ecs.devops.consistent.queryField.NcQueryField;
import com.alibaba.ecs.devops.consistent.queryField.VmQueryField;
import com.alibaba.ecs.devops.consistent.response.Nc;
import com.alibaba.ecs.devops.consistent.response.Vm;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.xdragon.api.service.chat.enums.SceneEnum;
import com.aliyun.xdragon.api.service.chat.model.AgentAnswerResponse;
import com.aliyun.xdragon.biz.diagnostic.service.DetailNcConsistentImpl;
import com.aliyun.xdragon.biz.workitem.service.impl.DetailVmConsistentImpl;
import com.aliyun.xdragon.common.util.StringUtil;
import com.aliyun.xdragon.service.common.config.diamond.DiamondConfigItem;
import com.aliyun.xdragon.service.common.config.diamond.DiamondConfigManager;
import com.aliyun.xdragon.service.common.util.DateUtil;
import com.aliyuncs.exceptions.ClientException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service("metaqHandleService")
public class MetaqHandleServiceImpl extends ChatSceneBasicHandleServiceImpl{
    private static final Logger logger = LoggerFactory.getLogger(MetaqHandleServiceImpl.class);

    private static final String ipv4_regex = "^(([0-1]?\\d{1,2}|2[0-4]\\d|25[0-5])\\.){3}([0-1]?\\d{1,2}|2[0-4]\\d|25[0-5])$";

    @Autowired
    private DiagnoseHandleServiceImpl diagnoseHandleService;

    @Autowired
    private DetailVmConsistentImpl detailVmConsistent;

    @Autowired
    private DetailNcConsistentImpl detailNcConsistent;

    @Autowired
    private CustomerEventHandleServiceImpl customerEventHandleService;

    @Override
    protected String getViewContent(AgentAnswerResponse response, Integer source) throws Exception {
        JSONObject data = new JSONObject();
        if(StringUtils.isNotEmpty(response.getAnswer())){
            data.put("answer", response.getAnswer());
        } else {
            data.put("answer","未总结出有效信息");
        }
        data.put("aoneUrl",response.getAoneUrl());
        data.put("finish",response.isFinish());
        data.put("tool",response.getTool());
        if(response.isFinish()){
            logger.info("MetaqHandleServiceImpl start deal param:{}",JSONObject.toJSONString(response));
            //补充机器信息
            getMachinedBaseInfo(response,data);
            //补充根因诊断信息
            diagnoseHandleService.getDiagnoseParam(response,data,source);
            //补充客户侧事件查询
            getCustomerEvent(response,data);
            //关联排查工具
            suggestTools(response,data);
        }

        return fillViewTemplate(data,"chat/metaq_aone_detail.tpl",response.getTool(),source);
    }

    @Override
    protected String getContent(AgentAnswerResponse response) {
        return response.getAnswer();
    }

    @Override
    protected String getUrl(AgentAnswerResponse response, Integer source) {
        return null;
    }

    @Override
    protected String getLinks(AgentAnswerResponse response) {
        return null;
    }

    @Override
    protected String getAones(AgentAnswerResponse response) {
        return null;
    }

    @Override
    protected boolean aiAgentResCheck(AgentAnswerResponse response) throws Exception {
        JSONObject params = response.getParams();
        JSONObject aoneSummery = response.getAoneSummery();
        if(StringUtils.isEmpty(response.getAnswer()) && params == null && aoneSummery == null){
            return false;
        }

        if((SceneEnum.FULL_LINK_DELIMITATION.getName().equalsIgnoreCase(response.getTool())||SceneEnum.PERFORMANCE_DIAGNOSE.getName().equalsIgnoreCase(response.getTool())) && StringUtils.isEmpty(params.getString("machine_id"))){
            if(response.isFinish()){
                response.setTips("未提取出机器信息");
            }
            return false;
        }

        if(params != null && response.isFinish() && CollectionUtils.isNotEmpty(params.getJSONArray("machine_id"))){
            List<String> machineIds = JSONObject.parseArray(params.getString("machine_id"), String.class);
            String machineId = StringUtil.toString(machineIds);
            params.put("machine_id", machineId);

            //修正诊断时间参数
            Date startTime = params.getDate("start_time");
            Date endTime = params.getDate("end_time");
            if (endTime == null) {
                endTime = new Date();
            }

            if (startTime == null) {
                startTime = DateUtil.modifyDateByMinute(endTime,-1440);
            } else {
                if (startTime.after(endTime) || startTime.getTime() == endTime.getTime()) {
                    startTime = DateUtil.modifyDateByMinute(endTime,-1440);
                } else if (response.getAoneCreateTime() != null && response.getAoneCreateTime().equals(startTime)) {
                    startTime = DateUtil.modifyDateByMinute(startTime,-1440);
                } else {
                    startTime = DateUtil.modifyDateByMinute(startTime,-360);
                }
            }

            //如果开始时间和结束时间范围超过2天，则结束时间为开始时间+2天
            if (DateUtil.modifyDateByDay(endTime,-2).after(startTime)) {
                endTime = DateUtil.modifyDateByDay(startTime, 2);
            }
            params.put("start_time",startTime);
            params.put("end_time",endTime);
        }
        return true;
    }

    protected JSONObject getMachinedBaseInfo(AgentAnswerResponse response, JSONObject data) throws ClientException {
        JSONObject params = response.getParams();
        List<String> machineIdList = new ArrayList<>();
        if (SceneEnum.BATCH_DIAGNOSE.getName().equals(response.getTool())) {
            JSONArray machineIds = params.getJSONArray("machine_id");
            machineIdList = machineIds.toJavaList(String.class);
            machineIdList = machineIdList.stream().distinct().collect(Collectors.toList());
        } else {
            String machineId = params.getString("machine_id");
            machineIdList = Arrays.asList(machineId);
        }
        //判断为vm还是ncIp 获取机器信息
        if (!machineIdList.isEmpty()) {
            boolean isInstance = !machineIdList.get(0).matches(ipv4_regex);
            List<Vm> vms = new ArrayList<>();
            List<Nc> ncs = new ArrayList<>();

            if (isInstance) {
                vms = detailVmConsistent.batchQueryVmInfo(machineIdList,
                        VmQueryField.VmField.INSTANCE_ID,
                        VmQueryField.VmField.NC_ID,
                        VmQueryField.VmField.NC_IP,
                        VmQueryField.VmField.ALI_UID,
                        VmQueryField.VmField.REGION_NO,
                        VmQueryField.VmField.AZONE,
                        VmQueryField.VmField.INSTANCE_TYPE,
                        VmQueryField.VmField.STATUS);
                data.put("vmInfos", vms);
            } else {
                ncs = detailNcConsistent.batchQueryNcInfo(machineIdList,
                        NcQueryField.NcField.NC_IP,
                        NcQueryField.NcField.NC_ID,
                        NcQueryField.NcField.AZONE,
                        NcQueryField.NcField.CLUSTER_NO,
                        NcQueryField.NcField.ROOM,
                        NcQueryField.NcField.IDC);
                data.put("ncInfos", ncs);
            }
            if (vms.isEmpty() && ncs.isEmpty()) {
                data.put("message", getNoInfoResponse("详情", machineIdList.toString(), null, null, null));
            }else if(!vms.isEmpty()){
                Set<String> ncIps = new HashSet<>();
                vms.forEach(vm->{
                    ncIps.add(vm.getNcIp());
                });
                data.put("ncIp",StringUtil.toString(new ArrayList<>(ncIps)));
            }

        }
        return data;
    }

    protected void getCustomerEvent(AgentAnswerResponse response, JSONObject data) throws Exception {
        if(!SceneEnum.VM_EVENT.getName().equalsIgnoreCase(response.getTool())){
            return;
        }
        JSONObject eventObj = JSONObject.parseObject(customerEventHandleService.getContent(response));
        logger.info("MetaqHandleServiceImpl getCustomerEvent param:{},eventObj:{}",JSONObject.toJSONString(response),JSONObject.toJSONString(eventObj));
        data.put("machineId", eventObj.get("machineId"));
        data.put("startTime", eventObj.get("startTime"));
        data.put("endTime",eventObj.get("endTime"));
        data.put("events",eventObj.get("events"));
    }

    protected void suggestTools(AgentAnswerResponse response, JSONObject data){
        try{
            Object obj = DiamondConfigManager.getConfig(DiamondConfigItem.ROBOT_LINK_TOOL);
            if(obj==null){
                return;
            }

            JSONObject linkToolConfig = JSONObject.parseObject((String)obj);
            String toolConfig = linkToolConfig.getString(response.getTool());
            logger.info("MetaqHandleServiceImpl suggestTools queryToolConfig,tool:{},toolConfig:{}",response.getTool(),toolConfig);
            if(StringUtils.isEmpty(toolConfig)){
                return ;
            }
            String []tools = toolConfig.split(",");
            Object toolUrlConfig = DiamondConfigManager.getConfig(DiamondConfigItem.ROBOT_TOOL_URL);
            if(toolUrlConfig==null){
                return;
            }
            JSONObject urlConfigObj = JSONObject.parseObject((String)toolUrlConfig);
            List<JSONObject> toolList = new ArrayList<>();
            for(int i=0;i<tools.length;i++){
                JSONObject tool = new JSONObject();
                String toolName = tools[i];
                JSONObject toolUrlObj = urlConfigObj.getJSONObject(toolName);
                if(toolUrlObj == null){
                    logger.warn("MetaqHandleServiceImpl getToolUrl is empty,toolName:{},urlConfigObj:{}",toolName,JSONObject.toJSONString(urlConfigObj));
                    continue;
                }
                String url = toolUrlObj.getString("url");
                tool.put("url",getToolUrl(data,url));
                tool.put("name",toolUrlObj.getString("name"));
                toolList.add(tool);
            }
            data.put("tools",toolList);
        }catch (Exception e){
            logger.error("MetaqHandleServiceImpl suggestTools exception,response:{},data:{}",JSONObject.toJSONString(response),JSONObject.toJSONString(data),e);
        }
    }

    private String getToolUrl(JSONObject data, String url){
        Pattern pattern = Pattern.compile("\\$\\{([^}]*)\\}");
        Matcher matcher = pattern.matcher(url);

        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String key = matcher.group(1); // 获取参数名
            String replacement = (String)data.getOrDefault(key, ""); // 获取实际值或保留原样
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        String realUrl = sb.toString().replaceAll(" ","%20");
        logger.info("MetaqHandleServiceImpl getToolUrl data:{},url:{},realUrl:{}",JSONObject.toJSONString(data),url,realUrl);
        return realUrl;
    }
}
