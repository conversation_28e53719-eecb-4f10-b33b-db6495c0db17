package com.aliyun.xdragon.biz.log.job;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.xdragon.algorithm.log.model.entities.SimpleTriple;
import com.aliyun.xdragon.biz.log.repository.ClusterTaskDao;
import com.aliyun.xdragon.biz.log.repository.LogPatternAccuracyCalcDao;
import com.aliyun.xdragon.biz.log.repository.LogPatternLibraryDao;
import com.aliyun.xdragon.biz.log.repository.PatternDao;
import com.aliyun.xdragon.common.generate.log.model.LogPatternLibrary;
import com.aliyun.xdragon.common.generate.model.LogPatternAccuracyCalc;
import com.aliyun.xdragon.common.model.log.DayTimeRange;
import com.aliyun.xdragon.service.common.job.AbstractMapTask;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/05/23
 */
@Component
public class LogPatternAccuracyCalcJob extends AbstractMapTask<Long> {
    private static final Logger logger = LoggerFactory.getLogger(LogPatternAccuracyCalcJob.class);
    private static final Gson GSON = new Gson();

    @Autowired
    private ClusterTaskDao clusterTaskDao;

    @Autowired
    private PatternDao patternDao;

    @Autowired
    private LogPatternAccuracyCalcDao patternAccuracyCalcDao;

    @Autowired
    private LogPatternLibraryDao patternLibraryDao;

    @Override
    public List<Long> genTask(JobContext context, int dataTs) {
        List<Long> tasks = clusterTaskDao.getAllTaskIds();
        logger.info("[LogPatternAccuracyCalcJob] gen {} subTasks, detail is {}", tasks.size(), GSON.toJson(tasks));
        return tasks;
    }

    @Override
    public ProcessResult processSubTask(JobContext context, int dataTs, int schedTs, Long subTask) throws ParseException {
        Long taskId = subTask;

        /*
        // 回溯历史数据
        Map<String, SimpleTriple<Integer, String, Date>> patternWithFirstAppear = new HashMap<>();
        Date start = DateUtil.genDateFromStr("2025-02-20 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Date end = DateUtil.genDateFromStr("2025-05-27 23:59:59", "yyyy-MM-dd HH:mm:ss");
        Date appear = DateUtil.genDateFromStr("2025-03-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Date queryStart = end;
        Date queryEnd = end;
        while (!queryStart.before(start)) {
            queryEnd = queryStart;
            queryStart = DateUtil.modifyDateBySecond(DateUtil.nDaysLater(queryStart, -10), 1);
            List<Map<String, Object>> patternAnalysisData = patternDao.getPatternWithFirstAppear(taskId, queryStart, queryEnd, appear);
            for (Map<String, Object> patternAnalysis : patternAnalysisData) {
                String md5 = (String) patternAnalysis.get("md5");
                String pattern = (String) patternAnalysis.get("pattern");
                Integer hashcode = ((Long)patternAnalysis.get("hashcode")).intValue();
                Date firstAppear = (Date) patternAnalysis.get("first_appear");
                if (!patternWithFirstAppear.containsKey(md5)) {
                    patternWithFirstAppear.put(md5, SimpleTriple.of(hashcode, pattern, firstAppear));
                } else if (patternWithFirstAppear.get(md5).getThird().after(firstAppear)) {
                    patternWithFirstAppear.get(md5).setFirst(hashcode);
                    patternWithFirstAppear.get(md5).setSecond(pattern);
                    patternWithFirstAppear.get(md5).setThird(firstAppear);
                }
            }
            logger.info("[LogPatternAccuracyCalcJob] task {}, queryStart {}, queryEnd {}, pattern size {}", taskId, queryStart, queryEnd, patternWithFirstAppear.size());
        }
        if (patternWithFirstAppear.isEmpty()) {
            logger.info("[LogPatternAccuracyCalcJob] no history pattern analysis data for taskId:{}", taskId);
            return new ProcessResult(true);
        }
        List<LogPatternLibrary> libraries = patternLibraryDao.getPatternsByMd5s(taskId, new ArrayList<>(patternWithFirstAppear.keySet()));
        Map<String, LogPatternLibrary> libMap = new HashMap<>(libraries.size());
        libraries.forEach(record -> libMap.put(record.getMd5(), record));
        List<LogPatternAccuracyCalc> accuracyCalcs = new ArrayList<>();
        for (Map.Entry<String, SimpleTriple<Integer, String, Date>> record : patternWithFirstAppear.entrySet()) {
            LogPatternLibrary lib = libMap.get(record.getKey());
            LogPatternAccuracyCalc accuracyCalc = getLogPatternAccuracyFromPatternAnalysis(record, lib);
            accuracyCalc.setTaskId(taskId);
            if (accuracyCalc.getStatus() == null) {
                accuracyCalc.setStatus(2);
                accuracyCalc.setLabelTime(new Date(dataTs * 1000L));
            }
            accuracyCalcs.add(accuracyCalc);
        }
        int insertCount = patternAccuracyCalcDao.batchInsert(accuracyCalcs);
        logger.info("[LogPatternAccuracyCalcJob] insert {} patternAccuracyCalc records for taskId:{}, success count is {}",
                accuracyCalcs.size(), taskId, insertCount);
        return new ProcessResult(true);
        */

        // 处理前一天的新增数据
        DayTimeRange yesterday = DayTimeRange.of(new Date((dataTs - 86400) * 1000L));
        List<Map<String, Object>> patternAnalysisData = patternDao.getPatternWithFirstAppear(taskId,
                yesterday.getStartTime(), yesterday.getEndTime(), yesterday.getStartTime());
        Map<String, SimpleTriple<Integer, String, Date>> patternWithFirstAppear = new HashMap<>();
        for (Map<String, Object> patternAnalysis : patternAnalysisData) {
            String md5 = (String) patternAnalysis.get("md5");
            String pattern = (String) patternAnalysis.get("pattern");
            Integer hashcode = ((Long)patternAnalysis.get("hashcode")).intValue();
            Date firstAppear = (Date) patternAnalysis.get("first_appear");
            if (!patternWithFirstAppear.containsKey(md5)) {
                patternWithFirstAppear.put(md5, SimpleTriple.of(hashcode, pattern, firstAppear));
            } else if (patternWithFirstAppear.get(md5).getThird().after(firstAppear)) {
                patternWithFirstAppear.get(md5).setFirst(hashcode);
                patternWithFirstAppear.get(md5).setSecond(pattern);
                patternWithFirstAppear.get(md5).setThird(firstAppear);
            }
        }
        logger.info("[LogPatternAccuracyCalcJob] task {}, pattern size {}", taskId, patternWithFirstAppear.size());
        if (patternWithFirstAppear.isEmpty()) {
            logger.info("[LogPatternAccuracyCalcJob] no pattern analysis data for taskId:{}", taskId);
            return new ProcessResult(true);
        }
        List<String> md5s = new ArrayList<>(patternWithFirstAppear.keySet());
        List<LogPatternLibrary> libraries = patternLibraryDao.getPatternsByMd5s(taskId, md5s);
        Map<String, LogPatternLibrary> libMap = new HashMap<>(libraries.size());
        libraries.forEach(record -> libMap.put(record.getMd5(), record));
        List<LogPatternAccuracyCalc> historyPatterns = patternAccuracyCalcDao.getLogpatternAccuracyCalcByMd5(taskId, md5s);
        Map<String, LogPatternAccuracyCalc> historyMap = new HashMap<>();
        historyPatterns.forEach(record -> historyMap.put(record.getMd5(), record));
        List<LogPatternAccuracyCalc> accuracyCalcs = new ArrayList<>();
        for (Map.Entry<String, SimpleTriple<Integer, String, Date>> record : patternWithFirstAppear.entrySet()) {
            LogPatternAccuracyCalc history = historyMap.get(record.getKey());
            if (history != null) {
                continue;
            }
            LogPatternLibrary lib = libMap.get(record.getKey());
            LogPatternAccuracyCalc accuracyCalc = getLogPatternAccuracyFromPatternAnalysis(record, lib);
            accuracyCalc.setTaskId(taskId);
            if (accuracyCalc.getStatus() == null) {
                accuracyCalc.setStatus(2);
                accuracyCalc.setLabelTime(new Date(dataTs * 1000L));
            }
            accuracyCalcs.add(accuracyCalc);
        }
        if (!accuracyCalcs.isEmpty()) {
            int insertCount = patternAccuracyCalcDao.batchInsert(accuracyCalcs);
            logger.info("[LogPatternAccuracyCalcJob] insert {} patternAccuracyCalc records for taskId:{}, success count is {}",
                    accuracyCalcs.size(), taskId, insertCount);
        }

        // 尝试更新历史未打标数据
        List<LogPatternAccuracyCalc> unlabeledPatterns = patternAccuracyCalcDao.getUnlabeledLogPatternAccuracyCalcRecords(taskId);
        if (unlabeledPatterns.isEmpty()) {
            logger.info("[LogPatternAccuracyCalcJob] no unlabeled pattern analysis data for taskId:{}", taskId);
            return new ProcessResult(true);
        }
        md5s = unlabeledPatterns.stream().map(LogPatternAccuracyCalc::getMd5).collect(Collectors.toList());
        List<LogPatternLibrary> libLabels = patternLibraryDao.getPatternLabelAndModifyTime(taskId, md5s);
        for (LogPatternAccuracyCalc calc : unlabeledPatterns) {
            for (LogPatternLibrary label : libLabels) {
                if (calc.getMd5().equals(label.getMd5())) {
                    calc.setStatus(label.getStatus());
                    calc.setLabelTime(label.getModifyTime());
                    break;
                }
            }
        }
        int updateCount = patternAccuracyCalcDao.batchUpdate(unlabeledPatterns);
        logger.info("[LogPatternAccuracyCalcJob] update {} patternAccuracyCalc records for taskId:{}, success count {}", unlabeledPatterns.size(), taskId, updateCount);
        return new ProcessResult(true);
    }

    private LogPatternAccuracyCalc getLogPatternAccuracyFromPatternAnalysis(
            Map.Entry<String, SimpleTriple<Integer, String, Date>> record, LogPatternLibrary lib) {
        LogPatternAccuracyCalc accuracyCalc = new LogPatternAccuracyCalc();
        accuracyCalc.setMd5(record.getKey());
        accuracyCalc.setPatternSource(record.getValue().getFirst());
        accuracyCalc.setPattern(record.getValue().getSecond());
        accuracyCalc.setAppearTime(record.getValue().getThird());
        if (lib != null) {
            accuracyCalc.setStatus(lib.getStatus());
            accuracyCalc.setLabelTime(lib.getModifyTime());
        }
        return accuracyCalc;
    }
}
