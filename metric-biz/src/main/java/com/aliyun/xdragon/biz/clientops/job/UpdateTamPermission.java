package com.aliyun.xdragon.biz.clientops.job;

import java.util.List;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.xdragon.biz.clientops.repository.ClientopsAutoPermissionLogDao;
import com.aliyun.xdragon.common.enumeration.ClientPermissionSource;
import com.aliyun.xdragon.common.generate.model.ClientopsAutoPermissionLog;
import com.aliyun.xdragon.common.model.acl.AclGrantPermission;
import com.aliyun.xdragon.common.model.buc.BucSimpleUser;
import com.aliyun.xdragon.service.common.agent.AclClient;
import com.aliyun.xdragon.service.common.agent.BucClient;
import com.aliyun.xdragon.service.common.cache.RedisUtil;
import com.aliyun.xdragon.service.common.job.AbstractProcessTask;
import com.aliyun.xdragon.service.common.util.DateUtil;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/07/01
 */
@Component
public class UpdateTamPermission extends AbstractProcessTask {
    private static final Logger logger = LoggerFactory.getLogger(UpdateTamPermission.class);
    // one month
    private final int PERMISSION_DURATION = 30 * 24 * 3600;
    // six month
    private final int GRANT_TIME = 6 * 30 * 24 * 3600;

    private final int PROCESS_COUNT = 1000;

    private static Gson gson = new Gson();

    @Autowired(required = false)
    private AclClient aclClient;

    @Autowired
    private BucClient bucClient;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ClientopsAutoPermissionLogDao clientopsAutoPermissionLogDao;

    @Override
    public ProcessResult process(JobContext context, int dataTs, int schedTs) throws Exception {
        int start = 0, size = 500;
        int grantCount = 0;
        int grantFailCount = 0;
        List<ClientopsAutoPermissionLog> logs = null;
        while (grantCount < PROCESS_COUNT) {
            try {
                Integer expireTime = dataTs + PERMISSION_DURATION;
                // 一个月内要过期的数据
                logs = clientopsAutoPermissionLogDao.listPermissionLog(null, expireTime,
                    ClientPermissionSource.CLOUDDOC.getSource(), start, size);
                logger.info("get {} logs to check", logs.size());
                for (ClientopsAutoPermissionLog log : logs) {
                    BucSimpleUser userinfo;
                    String userInfoStr = (String)redisUtil.get("userInfo#" + log.getEmpid());
                    if (userInfoStr == null) {
                        userinfo = bucClient.getSimpleUserByEmpId(log.getEmpid());
                        if (userinfo != null) {
                            redisUtil.set("userInfo#" + log.getEmpid(), gson.toJson(userinfo), 86400);
                        }
                    } else {
                        userinfo = gson.fromJson(userInfoStr, BucSimpleUser.class);
                    }

                    if (userinfo != null) {
                        if (userinfo.getContent().getStatus() != null && (userinfo.getContent().getStatus() == 0
                            || userinfo.getContent().getStatus() == 2)) {
                            int expTs = log.getExpireTime();
                            int expiredTimestamp = expTs + GRANT_TIME;
                            if (expiredTimestamp < System.currentTimeMillis() / 1000) {
                                expiredTimestamp = (int)(System.currentTimeMillis() / 1000 )+ 3600;
                            }
                            String expiredTime = DateUtil.timestamp2str(expiredTimestamp);
                            String empId = log.getEmpid();
                            AclGrantPermission aclGrantPermission = aclClient.grantPermissionsToUser(
                                userinfo.getContent().getUserId(), log.getPermission(), expiredTime,
                                "clouddoc add notify");
                            grantCount ++;
                            if (!aclGrantPermission.isSuccess()) {
                                logger.warn("grant {} permission to user {} error", log.getPermission(), empId);
                                grantFailCount ++;
                            } else {
                                clientopsAutoPermissionLogDao.updateExpireTimeById(log.getId(),
                                    expiredTimestamp);
                            }
                        } else {
                            logger.info("user {} status is {}, ignore", log.getEmpid(),
                                userinfo.getContent().getStatus());
                        }
                    } else {
                        logger.warn("not found user:{}", log.getEmpid());
                    }
                }
                if (logs.size() < 200) {
                    break;
                }
            } catch (Exception e) {
                logger.error("update tam permission error", e);
                return new ProcessResult(false);
            } finally {
                start += size;
            }
        }
        logger.info("task run over, grant {} records, failed {} records", grantCount, grantFailCount);
        if (grantFailCount > 0) {
            logger.error("update tam permission, grant fail count {}", grantCount);
            return new ProcessResult(false);
        } else {
            return new ProcessResult(true);
        }
    }
}
