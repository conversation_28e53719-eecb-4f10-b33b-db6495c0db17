package com.aliyun.xdragon.biz.diagnostic.job.util;

import java.util.BitSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aliyun.odps.data.Record;

/**
 * <AUTHOR>
 * @date 2023/09/25
 */
public class WorkDataset extends Dataset{

    private final Map<String, BitSet> instanceMap;

    public WorkDataset(AppConfig appConfig, LabelConfig labelConfig) {
        super(appConfig, labelConfig);
        instanceMap = new HashMap<>();
    }

    public void readFromRecords(List<Record> records) {
        for (Record record : records) {
            String instance = record.getString("instance_id");
            String appName = record.getString("app_name");
            if (appConfig.getIdByName(appName) == null) {
                continue;
            }
            if (instanceMap.containsKey(instance)) {
                BitSet bitSet = instanceMap.get(instance);
                bitSet.set(appConfig.getIdByName(appName));
            } else {
                BitSet bitSet = new BitSet();
                bitSet.set(appConfig.getIdByName(appName));
                instanceMap.put(instance, bitSet);
            }
        }
    }

    public Map<String, BitSet> getInstanceMap() {
        return instanceMap;
    }
}
