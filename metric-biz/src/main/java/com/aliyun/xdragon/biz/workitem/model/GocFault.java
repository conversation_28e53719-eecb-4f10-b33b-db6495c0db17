package com.aliyun.xdragon.biz.workitem.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.xdragon.service.common.util.DataCleaningUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Data
public class GocFault implements Serializable {
    private static final long serialVersionUID = 1458493680902034393L;
    private String recoverTime;

    private String bizType;

    private String description;

    private String source;

    private String vid;

    private String score;

    private String uid;

    private String foundTime;

    private String problemAppearance;

    private String fvid;

    private String channelCode;

    private String productLineId;

    private String productLinePath;

    private String evid;

    private String langYanId;

    private String faultCreate;

    private String faultTitle;

    private String resources;

    private String progressAbstract;

    private String grade;

    private String gcLevel;

    private String name;

    private String productLineName;

    private String sourceName;

    private String time;

    private String status;

    private String cid;

    public static GocFault fill(JSONObject jsonobj){
        GocFault entity = new GocFault();
        if (jsonobj.containsKey("recovertime")) {
            entity.setRecoverTime(jsonobj.getString("recovertime"));
        }
        if (jsonobj.containsKey("biztype")) {
            entity.setBizType(jsonobj.getString("biztype"));
        }
        if (jsonobj.containsKey("description")) {
            entity.setDescription(jsonobj.getString("description"));
        }
        if (jsonobj.containsKey("source")) {
            entity.setSource(jsonobj.getString("source"));
        }
        if (jsonobj.containsKey("vid")) {
            entity.setVid(jsonobj.getString("vid"));
        }
        if (jsonobj.containsKey("score")) {
            entity.setScore(jsonobj.getString("score"));
        }
        if (jsonobj.containsKey("uid")) {
            entity.setUid(jsonobj.getString("uid"));
        }
        if (jsonobj.containsKey("foundtime")) {
            entity.setFoundTime(jsonobj.getString("foundtime"));
        }
        if (jsonobj.containsKey("problemappearance")) {
            entity.setProblemAppearance(jsonobj.getString("problemappearance"));
        }
        if (jsonobj.containsKey("fvid")) {
            entity.setFvid(jsonobj.getString("fvid"));
        }
        if (jsonobj.containsKey("channelcode")) {
            entity.setChannelCode(jsonobj.getString("channelcode"));
        }
        if (jsonobj.containsKey("productlineid")) {
            entity.setProductLineId(jsonobj.getString("productlineid"));
        }
        if (jsonobj.containsKey("productlinepath")) {
            entity.setProductLinePath(jsonobj.getString("productlinepath"));
        }
        if (jsonobj.containsKey("evid")) {
            entity.setEvid(jsonobj.getString("evid"));
        }
        if (jsonobj.containsKey("langyanid")) {
            entity.setLangYanId(jsonobj.getString("langyanid"));
        }
        if (jsonobj.containsKey("faultcreate")) {
            entity.setFaultCreate(jsonobj.getString("faultcreate"));
        }
        if (jsonobj.containsKey("faulttitle")) {
            entity.setFaultTitle(jsonobj.getString("faulttitle"));
        }
        if (jsonobj.containsKey("resources")) {
            entity.setResources(jsonobj.getString("resources"));
        }
        if (jsonobj.containsKey("progressabstract")) {
            entity.setProgressAbstract(jsonobj.getString("progressabstract"));
        }
        if (jsonobj.containsKey("grade")) {
            entity.setGrade(jsonobj.getString("grade"));
        }
        if (jsonobj.containsKey("gclevel")) {
            entity.setGcLevel(jsonobj.getString("gclevel"));
        }
        if (jsonobj.containsKey("name")) {
            entity.setName(jsonobj.getString("name"));
        }
        if (jsonobj.containsKey("productlinename")) {
            entity.setProductLineName(jsonobj.getString("productlinename"));
        }
        if (jsonobj.containsKey("sourcename")) {
            entity.setSourceName(jsonobj.getString("sourcename"));
        }
        if (jsonobj.containsKey("time")) {
            entity.setTime(jsonobj.getString("time"));
        }
        if (jsonobj.containsKey("status")) {
            entity.setStatus(jsonobj.getString("status"));
        }
        if (jsonobj.containsKey("cid")) {
            entity.setCid(jsonobj.getString("cid"));
        }
        return entity;
    }
    public static List<GocFault> fillList(JSONArray jsonarray) {
        if (jsonarray == null || jsonarray.size() == 0) {
            return null;
        }
        List<GocFault> olist = new ArrayList<GocFault>();
        for (int i = 0; i < jsonarray.size(); i++) {
            olist.add(fill(jsonarray.getJSONObject(i)));
        }
        return olist;
    }

    public ComplaintRecord transform2ComplaintRecord() {
        ComplaintRecord complaintRecord = new ComplaintRecord();
        complaintRecord.setId(this.langYanId);
        complaintRecord.setSubject(this.faultTitle);
        complaintRecord.setTime(this.time);
        complaintRecord.setDescription(this.problemAppearance);
        complaintRecord.setComments(this.progressAbstract);
        complaintRecord.setProduct(this.productLinePath);
        if (resources != null && resources.length() > 0){
            List<String> instanceList = DataCleaningUtil.extractInstanceIds(resources);
            if (!instanceList.isEmpty()) {
                List<MachineInfo> machineInfoList = instanceList.stream().map(id->new MachineInfo(id)).collect(Collectors.toList());
                complaintRecord.setInstanceIdList(machineInfoList);
            }
        }
        String corpus = problemAppearance;
        if (faultTitle != null && faultTitle.length() > 0) {
            corpus = faultTitle.replaceAll("(\\[.*]|【.*】)", "") + "。" + corpus;
        }
        complaintRecord.setCorpus(corpus);
        complaintRecord.setSource(DataSourceEnum.GOC_FAULT);
        return complaintRecord;
    }

}
