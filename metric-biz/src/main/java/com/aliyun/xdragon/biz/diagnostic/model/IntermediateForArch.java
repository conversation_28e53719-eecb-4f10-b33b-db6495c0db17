package com.aliyun.xdragon.biz.diagnostic.model;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/03
 */
@Data
public class IntermediateForArch {
    private String cpuModel;
    private String physicalModel;
    private Integer multiCnNum;
    private List<Integer> sockets;
    private List<Integer> nodes;
    private List<Integer> ccd;
    private List<Integer> cn;
    private List<Integer> cpus;
    private Integer vcpuNum;
    private Integer totalCpu;
    private String numaNodeTopology;
    private String socketTopology;
    private String productName;
}
