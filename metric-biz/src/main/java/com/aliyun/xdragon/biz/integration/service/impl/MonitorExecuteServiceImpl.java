package com.aliyun.xdragon.biz.integration.service.impl;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.xdragon.api.service.monitor.MonitorNotifyService;
import com.aliyun.xdragon.api.service.monitor.model.ExceptionNotifyResponse;
import com.aliyun.xdragon.api.service.monitor.model.MonitorInfo;
import com.aliyun.xdragon.biz.integration.constant.Constant;
import com.aliyun.xdragon.biz.integration.model.MatchedAlertInfo;
import com.aliyun.xdragon.biz.integration.model.MonitorExecuteTaskInfo;
import com.aliyun.xdragon.biz.integration.repository.IntegrationSlsDao;
import com.aliyun.xdragon.biz.integration.service.MonitorExecuteService;
import com.aliyun.xdragon.biz.integration.utils.AlertRuleUtil;
import com.aliyun.xdragon.biz.integration.utils.DataTransformHelper;
import com.aliyun.xdragon.common.exception.XdragonMetricSystemException;
import com.aliyun.xdragon.common.exception.XdragonMetricViolationException;
import com.aliyun.xdragon.common.generate.model.XdragonMonitorAlertHistory;
import com.aliyun.xdragon.common.generate.model.XdragonMonitorAlertInfo;
import com.aliyun.xdragon.common.generate.model.XdragonMonitorInfo;
import com.aliyun.xdragon.common.generate.model.map.XdragonMonitorAlertHistoryMapper;
import com.aliyun.xdragon.common.generate.model.map.XdragonMonitorAlertInfoMapper;
import com.aliyun.xdragon.common.generate.model.map.XdragonMonitorInfoMapper;
import com.aliyun.xdragon.common.model.SlsProjectGroup;
import com.aliyun.xdragon.common.model.SlsRegionProject;
import com.aliyun.xdragon.common.util.SQLUtil;
import com.aliyun.xdragon.service.common.config.diamond.ConfigService;
import com.aliyun.xdragon.service.common.config.diamond.DiamondConfigItem;
import com.aliyun.xdragon.service.common.config.diamond.DiamondConfigManager;
import com.aliyuncs.exceptions.ClientException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2023/8/31
 */
@Service
public class MonitorExecuteServiceImpl implements MonitorExecuteService {

    private static final Logger logger = LoggerFactory.getLogger(MonitorExecuteServiceImpl.class);

    /**
     * 为防止sls数据过大，如果查询配置中没有限制limit的话，这里限制一下最大的limit
     */
    private static final Integer QUERY_SLS_MAX_SIZE = 1000;

    @Resource
    private XdragonMonitorInfoMapper monitorInfoMapper;
    @Resource
    private XdragonMonitorAlertInfoMapper alertInfoMapper;
    @Resource
    private XdragonMonitorAlertHistoryMapper alertHistoryMapper;
    @Resource
    private IntegrationSlsDao integrationSlsDao;
    @Resource
    private ConfigService configService;
    @Resource
    private MonitorNotifyService notifyService;

    private static List<String> VM_PREFIX_LIST = Arrays.asList(
            "i-","eci-","BVT","hbm-","AY","wh","cn","wy","qy",
            "hm","VM","al","uh","ay","tmpvm","ace","vm","cp-","ic-", "netvm-",
            "acs-"
    );

    /**
     * 收集查询sls的任务列表
     * @return
     */
    @Override
    public List<MonitorExecuteTaskInfo> collectQuerySlsTaskList() {
        XdragonMonitorInfo request = new XdragonMonitorInfo();
        List<XdragonMonitorInfo> monitorInfoList = monitorInfoMapper.queryList(request);
        if (CollectionUtils.isEmpty(monitorInfoList)) {
            logger.info("executeMonitorInfo with empty monitor info.");
            return Collections.emptyList();
        }
        // time filter
        List<XdragonMonitorInfo> needScheduleMonitorInfos = this.scheduleIntervalFilter(monitorInfoList);
        logger.info("collectQuerySlsTaskList after schedule interval filter: {}", JSON.toJSONString(needScheduleMonitorInfos));
        if (CollectionUtils.isEmpty(needScheduleMonitorInfos)) {
            return Collections.emptyList();
        }
        // enabled filter
        List<XdragonMonitorInfo> enabledMonitorInfoList = needScheduleMonitorInfos.stream()
            .filter(info -> Constant.MONITOR_INFO_ENABLE.equals(info.getEnable()))
            .collect(Collectors.toList());
        // black filter
        List<XdragonMonitorInfo> outBlacklistInfos = blacklistFilter(enabledMonitorInfoList);
        logger.info("collectQuerySlsTaskList after blacklist filter: {}", JSON.toJSONString(outBlacklistInfos));
        if (CollectionUtils.isEmpty(outBlacklistInfos)) {
            return Collections.emptyList();
        }
        List<MonitorExecuteTaskInfo> executeTaskInfos = collectExecuteTaskInfoList(outBlacklistInfos);
        logger.info("collectQuerySlsTaskList collected executeTaskInfo: {}", JSON.toJSONString(executeTaskInfos));
        return executeTaskInfos;
    }

    @Override
    public List<MonitorExecuteTaskInfo> collectSpecifiedQuerySlsTaskList(List<String> careMonitorNames) {
        logger.info("start to collectSpecifiedQuerySlsTaskList with param: {}", JSON.toJSONString(careMonitorNames));
        if (CollectionUtils.isEmpty(careMonitorNames)) {
            return Collections.emptyList();
        }
        XdragonMonitorInfo request = new XdragonMonitorInfo();
        List<XdragonMonitorInfo> monitorInfoList = monitorInfoMapper.queryList(request);
        if (CollectionUtils.isEmpty(monitorInfoList)) {
            logger.info("executeMonitorInfo with empty monitor info.");
            return Collections.emptyList();
        }
        // name filter
        List<XdragonMonitorInfo> enabledMonitorInfoList = monitorInfoList.stream()
            .filter(info -> careMonitorNames.contains(info.getMonitorName()))
            .collect(Collectors.toList());
        // time filter
        List<XdragonMonitorInfo> needScheduleMonitorInfos = this.scheduleIntervalFilter(enabledMonitorInfoList);
        // black filter
        List<XdragonMonitorInfo> outBlacklistInfos = blacklistFilter(needScheduleMonitorInfos);
        logger.info("collectQuerySlsTaskList after blacklist filter: {}", JSON.toJSONString(outBlacklistInfos));
        if (CollectionUtils.isEmpty(outBlacklistInfos)) {
            return Collections.emptyList();
        }
        List<MonitorExecuteTaskInfo> executeTaskInfos = collectExecuteTaskInfoList(outBlacklistInfos);
        logger.info("collectQuerySlsTaskList collected executeTaskInfo: {}", JSON.toJSONString(executeTaskInfos));
        return executeTaskInfos;
    }

    /**
     * 执行单元化的监控任务
     * @param taskInfo
     */
    @Override
    public void executeUnitMonitorTask(MonitorExecuteTaskInfo taskInfo) {
        // update execute time
        monitorInfoMapper.updateLastExecuteTime(Date.from(Instant.now()), taskInfo.getMonitorId());
        // check logStore.
        Client slsClient = configService.getSlsClient(taskInfo.getUser(), taskInfo.getRegion(), taskInfo.getEndpoint());
        boolean logStoreExist = integrationSlsDao.checkLogStoreExist(taskInfo.getRealProject(), taskInfo.getLogStore(), slsClient);
        if (!logStoreExist) {
            logger.info("processMatchingRuleTask logStore not found, logStore: {}, taskInfo: {}", taskInfo.getLogStore(), JSON.toJSONString(taskInfo));
            return;
        }
        List<XdragonMonitorAlertInfo> alertInfos = alertInfoMapper.queryListByMonitorId(Collections.singleton(taskInfo.getMonitorId()));
        logger.info("getAlertByMonitorIds result: {}", JSON.toJSONString(alertInfos));
        if (CollectionUtils.isEmpty(alertInfos)) {
            logger.info("no alert info need to execute, taskInfo: {}", JSON.toJSONString(taskInfo));
            return;
        }
        // execute query
        alertInfos.parallelStream().forEach(alert -> this.processMatchingRuleTask(taskInfo, alert, slsClient));
    }

    /**
     * query sls with monitor info.
     * @param monitorInfo: {@link XdragonMonitorInfo}
     * @return
     */
    @Override
    public List<Map<String, Object>> querySlsWithDefaultProjectForPreCheck(XdragonMonitorInfo monitorInfo) throws LogException {
        Optional<SlsRegionProject> defaultRegionProject = configService.getDefaultRegionProject(monitorInfo.getDataSource());
        return getClientToQuerySls(monitorInfo, defaultRegionProject);
    }

    @Override
    public List<Map<String, Object>> querySlsForPreCheck(XdragonMonitorInfo monitorInfo, String region)
        throws LogException {
        Optional<SlsRegionProject> regionProject = configService.getRegionProject(monitorInfo.getDataSource(), region);
        return getClientToQuerySls(monitorInfo, regionProject);
    }

    /**
     * query all of projectName in diamond.
     * @return
     */
    @Override
    public Set<String> queryAllProjectNames() {
        List<SlsProjectGroup> slsProjectGroups = configService.loadRegionProject();
        logger.info("queryAllProjectNames result: {}", JSON.toJSONString(slsProjectGroups));
        return slsProjectGroups.stream().map(SlsProjectGroup::getProject).collect(Collectors.toSet());
    }

    /**
     * query all of groups.
     * @return
     */
    @Override
    public List<SlsProjectGroup> queryProjectRegionGroupList() {
        return configService.getRegionProject();
    }

    /**
     * 收集需要执行查询的配置任务
     * @param executeMonitorInfoList
     * @return
     */
    private List<MonitorExecuteTaskInfo> collectExecuteTaskInfoList(List<XdragonMonitorInfo> executeMonitorInfoList) {
        Map<String, SlsProjectGroup> projectGroupMap = configService.getProjectGroupMap();
        Set<String> centralProjectNames = centralProjectNames();

        List<MonitorExecuteTaskInfo> executeTaskInfos = new ArrayList<>(executeMonitorInfoList.size());
        executeMonitorInfoList.forEach(info -> {
            String projectName = info.getDataSource();
            List<SlsRegionProject> regionProjectList = projectGroupMap.get(projectName).getRegions();
            if (!centralProjectNames.contains(projectName)) {
                // 非中心化的sls，将其下面的region都展开，逐个查询
                List<MonitorExecuteTaskInfo> infoList = DataTransformHelper.monitorInfo2TaskInfoList(info, regionProjectList);
                logger.info("collectExecuteTaskInfoList not central sls, monitorInfo: {}, taskInfoList: {}", JSON.toJSONString(info), JSON.toJSONString(infoList));
                executeTaskInfos.addAll(infoList);
                return;
            }
            // 如果明确是中心化的，不再处理下面单元化的region
            Optional<SlsRegionProject> defaultRegionProject = configService.getDefaultRegionProject(projectName);
            if (!defaultRegionProject.isPresent()) {
                logger.error("collectExecuteTaskInfoList with central monitorInfo: {}, default not found", JSON.toJSONString(info));
                return;
            }
            MonitorExecuteTaskInfo taskInfo = DataTransformHelper.monitorInfo2TaskInfo(info, defaultRegionProject.get());
            logger.info("collectExecuteTaskInfoList is central sls, monitorInfo: {}, taskInfo: {}", JSON.toJSONString(info), JSON.toJSONString(taskInfo));
            executeTaskInfos.add(taskInfo);
        });
        logger.info("collectExecuteTaskInfoList with monitorInfoList: {}, result: {}",
            JSON.toJSONString(executeMonitorInfoList), JSON.toJSONString(executeTaskInfos));
        return executeTaskInfos;
    }

    /**
     * 处理匹配任务
     * @param taskInfo
     * @param alertInfo
     * @param client
     */
    private void processMatchingRuleTask(MonitorExecuteTaskInfo taskInfo, XdragonMonitorAlertInfo alertInfo, Client client) {
        try {
            logger.info("start to process matching task: {}, alertInfo: {}", JSON.toJSONString(taskInfo), JSON.toJSONString(alertInfo));
            List<Map<String, Object>> querySlsResult = querySlsResult(client, taskInfo.getRealProject(), taskInfo.getLogStore(), taskInfo.getSql(),
                taskInfo.getTimeRangeSecond());
            if (querySlsResult.isEmpty()) {
                return;
            }
            // 处理下结构
            Map<String, Map<String, Object>> resourceIdSlsDataMap = new HashMap<>(querySlsResult.size());
            querySlsResult.forEach(map -> {
                String resource = AlertRuleUtil.extractSlsData(map, taskInfo.getResourceId());
                resourceIdSlsDataMap.putIfAbsent(resource, map);
            });

            Date silentFrom = Date.from(Instant.now().minusSeconds(alertInfo.getSilence()));
            Date now = Date.from(Instant.now());
            List<XdragonMonitorAlertHistory> histories = alertHistoryMapper.queryByAlertInfoIds(Collections.singleton(alertInfo.getId()), silentFrom, now);
            XdragonMonitorInfo xdragonMonitorInfo = monitorInfoMapper.selectOneById(alertInfo.getMonitorId());

            resourceIdSlsDataMap.keySet().parallelStream().forEach(resource -> {
                if (StringUtils.isBlank(resource)) {
                    logger.info("resource is empty");
                    return;
                }
                boolean valid = resourceValid(resource, xdragonMonitorInfo.getResourceType());
                boolean silent = resourceSilent(histories, resource);
                logger.info("processMatchedAlert silent by resource: {}, history: {}, alertInfo: {}, monitor: {}, isSilent: {}",
                    resource, JSON.toJSONString(histories), JSON.toJSONString(alertInfo), JSON.toJSONString(xdragonMonitorInfo), silent);
                if (silent || !valid) {
                    return;
                }
                MatchedAlertInfo matchedAlertInfo = new MatchedAlertInfo()
                    .setMonitorAlertInfo(alertInfo)
                    .setMonitorInfo(xdragonMonitorInfo)
                    .setMatchedData(Collections.singleton(resource))
                    .setRegion(taskInfo.getRegion());
                List<MonitorInfo> monitorInfos = DataTransformHelper.alertInfo2Message(resourceIdSlsDataMap.get(resource), matchedAlertInfo);
                // should contain only 1 monitorInfo
                if (!CollectionUtils.isEmpty(monitorInfos)) {
                    MonitorInfo monitorInfo = monitorInfos.get(0);
                    try {
                        logger.info("start to consumeSlsMonitor, taskInfo: {}, alertInfo: {}, monitor: {}",
                            JSON.toJSONString(taskInfo), JSON.toJSONString(alertInfo), JSON.toJSONString(monitorInfo));
                        ExceptionNotifyResponse notifyResponse = notifyService.consumeSlsMonitor(monitorInfo);
                        logger.info("finish to consumeSlsMonitor, taskInfo: {}, monitor: {}, response: {}",
                            JSON.toJSONString(taskInfo), JSON.toJSONString(monitorInfo), JSON.toJSONString(notifyResponse));
                        if (notifyResponse.isSuccess()) {
                            this.recordAlertHistory(matchedAlertInfo, resource);
                        }
                    } catch (ClientException e) {
                        logger.error("processMatchedAlert send alert error, taskInfo: {}, monitorInfo: {}",
                            JSON.toJSONString(taskInfo), JSON.toJSONString(monitorInfo), e);
                    }
                }
            });
        } catch (Exception e) {
            logger.error("processMatchingRuleTask got error, taskInfo: {}, error: {}", JSON.toJSONString(taskInfo), e.getMessage());
        }
    }

    private static boolean resourceValid(String resource, String resourceType) {
        if ("vm".equalsIgnoreCase(resourceType)) {
            if (StringUtils.isBlank(resource)) {
                return false;
            }
            char c = resource.charAt(0);
            if (c == '1' || c == '2') {
                return false;
            }

            boolean result = false;
            for(String prefix : VM_PREFIX_LIST){
                if(resource.startsWith(prefix)) {
                    result = true;
                    break;
                }
            }
            return result;
        }
        return true;
    }

    public static boolean resourceSilent(List<XdragonMonitorAlertHistory> historyWithinSilent, String resource) {
        if (CollectionUtils.isEmpty(historyWithinSilent)) {
            return false;
        }
        return historyWithinSilent.stream().map(XdragonMonitorAlertHistory::getResourceId).anyMatch(r -> r.contains(resource));
    }

    private void recordAlertHistory(MatchedAlertInfo alertInfo, String resourceId) {
        XdragonMonitorAlertHistory history = DataTransformHelper.alertInfo2History(alertInfo.getMonitorAlertInfo(), resourceId);
        alertHistoryMapper.insert(history);
    }

    private List<XdragonMonitorAlertInfo> collectOutSilenceAlertInfo(List<XdragonMonitorAlertInfo> infos) {
        if (CollectionUtils.isEmpty(infos)) {
            logger.info("collectOutSilenceAlertInfo with empty alertInfo list");
            return Collections.emptyList();
        }
        Integer maxSilenceSecond = infos.stream().map(XdragonMonitorAlertInfo::getSilence).max(Integer::compareTo).get();
        Date from = Date.from(Instant.now().minusSeconds(maxSilenceSecond + 1));
        Date to = Date.from(Instant.now());
        Set<Long> alertInfoIds = infos.stream().map(XdragonMonitorAlertInfo::getId).collect(Collectors.toSet());

        List<XdragonMonitorAlertHistory> alertHistories = alertHistoryMapper.queryByAlertInfoIds(alertInfoIds, from, to);
        logger.info("getAlertHistoryWithMaxSilenceRange queried history from: {}, to: {}, infoIds: {}, result: {}",
            from, to, JSON.toJSONString(alertInfoIds), JSON.toJSONString(alertHistories));
        return AlertRuleUtil.silenceFilter(infos, alertHistories);
    }

    private List<XdragonMonitorInfo> blacklistFilter(List<XdragonMonitorInfo> monitorInfos) {
        if (CollectionUtils.isEmpty(monitorInfos)) {
            logger.info("blacklistFilter with empty monitorInfo list");
            return Collections.emptyList();
        }
        Set<String> blacklist = blacklist();
        if (!CollectionUtils.isEmpty(blacklist)) {
            return monitorInfos.stream().filter(info -> diamondBlacklistFilter(blacklist, info)).collect(Collectors.toList());
        }
        return monitorInfos;
    }

    private List<XdragonMonitorInfo> scheduleIntervalFilter(List<XdragonMonitorInfo> monitorInfos) {
        if (CollectionUtils.isEmpty(monitorInfos)) {
            logger.info("scheduleIntervalFilter with empty monitorInfo list");
            return Collections.emptyList();
        }
        return monitorInfos.stream().filter(info -> {
            int intervalSeconds = info.getScheduleInterval() == null ? 60 : info.getScheduleInterval();
            if (Objects.nonNull(info.getLastExecutedTime())) {
                return !info.getLastExecutedTime().toInstant().plusSeconds(intervalSeconds).isAfter(Instant.now());
            }
            return true;
        }).collect(Collectors.toList());
    }

    private List<Map<String, Object>> querySlsResult(Client client, String project, String logStore, String sql, int timeRange) {
        String trimSql = SQLUtil.trimSql(sql);
        try {
            String limitSql = appendLimit(trimSql, QUERY_SLS_MAX_SIZE);
            List<Map<String, Object>> mapList = integrationSlsDao.querySls(client, project, logStore, limitSql, timeRange);
            logger.info("querySlsCount with sql: {}, result: {}", limitSql, JSON.toJSONString(mapList));
            return mapList;
        } catch (Exception e) {
            logger.error("querySlsCount with transformed sql got error: ", e);
            return Collections.emptyList();
        }
    }

    private List<Map<String, Object>> getClientToQuerySls(XdragonMonitorInfo monitorInfo, Optional<SlsRegionProject> regionProject)
        throws LogException {
        if (!regionProject.isPresent()) {
            logger.error("querySls with param: {} not supported", JSON.toJSONString(monitorInfo));
            throw new XdragonMetricViolationException("dataSource belong to " + monitorInfo.getDataSource() + " not found");
        }
        String realProjectName = regionProject.get().getRealProject();
        Map<String, Client> clientMap = configService.getProjectNameClientMap();
        if (CollectionUtils.isEmpty(clientMap) || !clientMap.containsKey(realProjectName)) {
            logger.error("querySls with empty client map or dataSource not supported, info: {}", JSON.toJSONString(monitorInfo));
            throw new XdragonMetricSystemException("sls client init failed");
        }
        Client client = clientMap.get(realProjectName);
        return querySlsData(realProjectName, monitorInfo.getLogStore(), monitorInfo.getQueryStatement(), monitorInfo.getTimeRange(), client);
    }

    private List<Map<String, Object>> querySlsData(String projectName, String logStore, String sql, int timeRange, Client client) throws LogException {
        String trimSql = SQLUtil.trimSql(sql);
        String limitSql = appendLimit(trimSql, QUERY_SLS_MAX_SIZE);
        return integrationSlsDao.querySls(client, projectName, logStore, limitSql, timeRange);
    }

    private static Set<String> blacklist() {
        String blacklistName = (String)DiamondConfigManager.getConfig(DiamondConfigItem.MONITOR_INFO_NAME_BLACKLIST);
        logger.info("executeMonitorInfo get blacklist: {}", blacklistName);
        if (StringUtils.isNotBlank(blacklistName)
            && !DiamondConfigItem.MONITOR_INFO_NAME_BLACKLIST.getDefaultValue().equals(blacklistName)) {
            return new HashSet<>(JSON.parseArray(blacklistName, String.class));
        }
        return Collections.EMPTY_SET;
    }

    private static Set<String> centralProjectNames() {
        String centralProjectNames = (String)DiamondConfigManager.getConfig(DiamondConfigItem.MONITOR_ALERT_CENTRAL_PROJECTS);
        logger.info("centralProjectNames details: {}", centralProjectNames);
        if (StringUtils.isNotBlank(centralProjectNames)
            && !DiamondConfigItem.MONITOR_ALERT_CENTRAL_PROJECTS.getDefaultValue().equals(centralProjectNames)) {
            return new HashSet<>(JSON.parseArray(centralProjectNames, String.class));
        }
        return Collections.emptySet();
    }

    /**
     * 这里支持监控名称、sls的project和logStore同时加黑
     */
    private static boolean diamondBlacklistFilter(Set<String> blacklist, XdragonMonitorInfo info) {
        return !blacklist.contains(info.getMonitorName())
            && !blacklist.contains(info.getDataSource())
            && !blacklist.contains(info.getLogStore());
    }

    private static String appendLimit(String sql, int limit) {
        if (!sql.contains(" limit ") && !sql.contains(" LIMIT ")) {
            sql += " limit " + limit;
        }
        return sql;
    }
}
