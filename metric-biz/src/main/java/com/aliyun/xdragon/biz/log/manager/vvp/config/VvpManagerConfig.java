package com.aliyun.xdragon.biz.log.manager.vvp.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/11/16
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "manager.vvp")
public class VvpManagerConfig {
    private String workspace;
    private String namespace;
    private String endpoint;
    private String pilotEndpoint;
    private String accessId;
    private String accessKey;
    private String engineVersion;
    private String deploymentTargetName;
}
