package com.aliyun.xdragon.biz.log.job;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.xdragon.biz.log.model.StreamMonitorInfo;
import com.aliyun.xdragon.biz.log.repository.ClusterRegionConfigDao;
import com.aliyun.xdragon.biz.log.repository.LogUnionTaskDao;
import com.aliyun.xdragon.biz.log.service.MetricsPickupService;
import com.aliyun.xdragon.common.enumeration.log.StreamingTaskType;
import com.aliyun.xdragon.common.generate.model.LogClusterConfigRegion;
import com.aliyun.xdragon.common.generate.model.LogUnionTask;
import com.aliyun.xdragon.common.model.log.ClusterAlgParam;
import com.aliyun.xdragon.common.model.log.SlsSourceParam;
import com.aliyun.xdragon.service.common.cache.RedisUtil;
import com.aliyun.xdragon.service.common.job.AbstractProcessTask;
import com.aliyun.xdragon.service.common.job.JobHelper;
import com.aliyun.xdragon.service.common.repository.FlinkMetricDao;
import com.aliyun.xdragon.service.common.service.DevOpsApiService;
import com.aliyun.xdragon.service.common.service.XdragonAlertService;
import com.aliyun.xdragon.service.common.util.DateUtil;
import com.aliyun.xdragon.service.common.util.SlsUtil;
import com.aliyuncs.ecsops.model.v20160401.OpsXdragonSendAlertRequest.Item;
import com.aliyuncs.ecsops.model.v20160401.OpsXdragonSendAlertResponse;
import com.aliyuncs.ecsops.model.v20160401.OpsXdragonSendAlertResponse.SendResponse;
import com.aliyuncs.exceptions.ClientException;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import static com.aliyun.xdragon.common.enumeration.log.ClusterTaskStatus.RUNNING;

/**
 * <AUTHOR>
 * @date 2022/12/07
 * log cluster flink task monitor
 */
@Component
@Lazy
public class LogClusterTaskMonitorJob<T> extends AbstractProcessTask<T> {
    private static final Logger logger = LoggerFactory.getLogger(LogClusterTaskMonitorJob.class);
    private static final Gson GSON = new Gson();

    private final ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(
        "logcluster-task-monitor-pool-%d").build();
    private final ExecutorService executorService = new ThreadPoolExecutor(5, 10, 0, TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(2048), threadFactory, new ThreadPoolExecutor.AbortPolicy());

    private static final int MAX_ALERT_DETAIL_LEN = 15000;
    private static final int NOT_CONSUMER_LOG_COUNT = 3000;
    private static final int MAX_DELAY_TIME = 3600;
    private static final int MAX_UNION_DELAY_TIME = 600;
    private static final String ALERT_RESOURCE_NAME = "xdragon-metric.StreamTaskMonitorJob";
    private static final String ALERT_SOURCE = "xdragon-metric";
    private static final String ALERT_LABEL = "streaming-monitor";
    private static final String ALERT_TITLE = "流式任务指标异常";
    private static final Set<Long> EXCLUDE_TASK_ID_SET;

    static {
        EXCLUDE_TASK_ID_SET = new HashSet<>();
        EXCLUDE_TASK_ID_SET.add(15L);
        EXCLUDE_TASK_ID_SET.add(62L);
        EXCLUDE_TASK_ID_SET.add(101L);
    }

    @Autowired
    private ClusterRegionConfigDao configDao;

    @Autowired
    private LogUnionTaskDao logUnionTaskDao;

    @Autowired
    private FlinkMetricDao flinkMetricDao;

    @Autowired
    private MetricsPickupService metricsPickupService;

    @Autowired
    private XdragonAlertService alertService;

    @Autowired
    private RedisUtil redisUtil;

    @Value("${domain.vvp}")
    private String vvpDomain;

    @Override
    public ProcessResult process(JobContext context, int dataTs, int schedTs) throws Exception {
        // 只检查运行中的任务
        List<LogUnionTask> unionTasks = logUnionTaskDao.getUnionTasksByStatus(null, RUNNING.getStatus(), false);
        List<LogClusterConfigRegion> configRegionList = configDao.listAllConfigsWithStatus(RUNNING, StreamingTaskType.ONLINE_CLUSTER.getType());
        configRegionList.addAll(configDao.listAllConfigsWithStatus(RUNNING, StreamingTaskType.MATCH.getType()));
        configRegionList.removeIf(configRegion -> EXCLUDE_TASK_ID_SET.contains(configRegion.getTaskId()));
        unionTasks.removeIf(unionTask -> EXCLUDE_TASK_ID_SET.contains(unionTask.getTaskId()));
        List<StreamMonitorInfo> monitorList = new ArrayList<>(configRegionList.size());
        List<Future<StreamMonitorInfo>> fs = new LinkedList<>();
        Map<String, String> mdcContext = MDC.getCopyOfContextMap();
        for (LogClusterConfigRegion configRegion : configRegionList) {
            Future<StreamMonitorInfo> f = executorService.submit(() -> {
                if (mdcContext != null && !mdcContext.isEmpty()) {
                    MDC.setContextMap(mdcContext);
                }
                return processOneConfig(configRegion, dataTs);
            });
            fs.add(f);
        }

        for (LogUnionTask unionTask : unionTasks) {
            Future<StreamMonitorInfo> f = executorService.submit(() -> {
                if (mdcContext != null && !mdcContext.isEmpty()) {
                    MDC.setContextMap(mdcContext);
                }
                return processOneConfig(unionTask, dataTs);
            });
            fs.add(f);
        }

        for (Future<StreamMonitorInfo> f : fs) {
            try {
                StreamMonitorInfo p = f.get();
                if (p != null) {
                    monitorList.add(p);
                }
            } catch (Exception e) {
                logger.error("check single config fail", e);
            }
        }
        if (monitorList.isEmpty()) {
            logger.info("[StreamTaskMonitorJob] All {} task is ok", configRegionList.size());
            return new ProcessResult(true);
        }

        try {
            cacheDelayInfo(monitorList, dataTs);
        } catch (Exception e) {
            logger.warn("cache delay info fail", e);
        }

        // 判断是否有监控数据汇报
        for (StreamMonitorInfo streamMonitorInfo : monitorList) {
            if (streamMonitorInfo.getUptime() == null) {
                streamMonitorInfo.setNoData(true);
            }
        }

        boolean ret = sendNoDataAlert(monitorList, dataTs, configRegionList.size());

        if (!sendDelayAlert(monitorList, dataTs, configRegionList.size())) {
            ret = false;
        }
        if (!sendRestartAlert(monitorList, dataTs, configRegionList.size())) {
            ret = false;
        }
        if (!sendCheckpointAlert(monitorList, dataTs, configRegionList.size())) {
            ret = false;
        }

        return new ProcessResult(ret);
    }

    public StreamMonitorInfo processOneConfig(LogClusterConfigRegion configRegion, int runTs) {
        logger.info("check log cluster with regin cid: {}", configRegion.getRegionCid());

        StreamMonitorInfo streamMonitorInfo = new StreamMonitorInfo();
        streamMonitorInfo.setClusterConfig(configRegion);

        Integer uptime = flinkMetricDao.queryUpTime(configRegion.getPlatformId(), runTs - 1800, runTs);
        streamMonitorInfo.setUptime(uptime);
        Pair<Integer, Integer> dp = queryDelay(configRegion, runTs);
        if (dp != null) {
            streamMonitorInfo.setDelaySecond(dp.getLeft());
            streamMonitorInfo.setDelayDelta(dp.getRight());
        }
        Integer restart = queryRestart(configRegion.getPlatformId(), configRegion.getTaskId(), configRegion.getRegionCid(), runTs);
        if (restart != null && restart > 0) {
            streamMonitorInfo.setRestart(restart);
        }
        Integer checkpoint = queryCompletedCheckpoint(configRegion.getPlatformId(), configRegion.getTaskId(), configRegion.getRegionCid(),
            runTs);
        if (checkpoint != null) {
            streamMonitorInfo.setNewCheckpointCount(checkpoint);
        }
        return streamMonitorInfo;
    }

    public StreamMonitorInfo processOneConfig(LogUnionTask unionTask, int runTs) {
        logger.info("check log cluster with union id: {}", unionTask.getUnionId());

        StreamMonitorInfo streamMonitorInfo = new StreamMonitorInfo();
        streamMonitorInfo.setLogUnionTask(unionTask);

        Integer uptime = flinkMetricDao.queryUpTime(unionTask.getPlatformId(), runTs - 1800, runTs);
        streamMonitorInfo.setUptime(uptime);
        Pair<Integer, Integer> dp = queryDelay(unionTask, runTs);
        if (dp != null) {
            streamMonitorInfo.setDelaySecond(dp.getLeft());
            streamMonitorInfo.setDelayDelta(dp.getRight());
        }
        Integer restart = queryRestart(unionTask.getPlatformId(), unionTask.getTaskId(), unionTask.getUnionId(), runTs);
        if (restart != null && restart > 0) {
            streamMonitorInfo.setRestart(restart);
        }
        Integer checkpoint = queryCompletedCheckpoint(unionTask.getPlatformId(), unionTask.getTaskId(),
            unionTask.getUnionId(), runTs);
        if (checkpoint != null) {
            streamMonitorInfo.setNewCheckpointCount(checkpoint);
        }
        return streamMonitorInfo;
    }

    private Integer queryCompletedCheckpoint(String platformId, Long taskId, Long subTaskId, int runTs) {
        Integer checkpointCount = flinkMetricDao.queryCheckpoint(platformId, runTs - 3600, runTs);
        if (checkpointCount == null) {
            logger.info("query metric info, task id {}, cid {}, deploymentId {}, get null checkpoint",
                taskId, subTaskId, platformId);
        } else {
            logger.info("query metric info, task id {}, cid {}, deploymentId {}, new checkpoint {}", taskId,
                subTaskId, platformId, checkpointCount);
        }
        return checkpointCount;
    }

    private Integer queryRestart(String platformId, Long taskId, Long subTaskId, int runTs) {
        Integer restartCount = flinkMetricDao.queryRestart(platformId, runTs - 1800, runTs);
        if (restartCount == null) {
            logger.info("query metric info, task id {}, subtask id {}, deploymentId {}, get null restart info",
                taskId, subTaskId, platformId);
        } else {
            logger.info("query metric info, task id {}, subtask {}, deploymentId {}, restart count {}", taskId,
                subTaskId, platformId, restartCount);
        }
        return restartCount;
    }

    private Pair<Integer, Integer> queryDelay(LogUnionTask unionTask, int runTs) {
        SlsSourceParam slsSourceParam = GSON.fromJson(unionTask.getSourceParam(), SlsSourceParam.class);
        return queryDelay(unionTask.getPlatformId(), unionTask.getTaskId(), unionTask.getUnionId(),
            MAX_UNION_DELAY_TIME, slsSourceParam, runTs);
    }

    private Pair<Integer, Integer> queryDelay(LogClusterConfigRegion config, int runTs) {
        ClusterAlgParam algParam = GSON.fromJson(config.getAlgParam(), ClusterAlgParam.class);
        int windowTs = algParam.getWindowSize() * 60;
        int maxDelayTs = 5 * windowTs;
        if (maxDelayTs > MAX_DELAY_TIME) {
            maxDelayTs = MAX_DELAY_TIME;
        }
        SlsSourceParam slsSourceParam = GSON.fromJson(config.getInputSourceParam(), SlsSourceParam.class);
        return queryDelay(config.getPlatformId(), config.getTaskId(), config.getRegionCid(), maxDelayTs, slsSourceParam,
            runTs);
    }

    private Pair<Integer, Integer> queryDelay(String platformId, Long taskId, Long subTaskId, int maxDelayTs,
        SlsSourceParam sourceParam, int runTs) {
        List<Pair<String, Long>> hostsDelay = flinkMetricDao.queryDelay(platformId, runTs - 300, runTs);

        if (hostsDelay == null || hostsDelay.isEmpty()) {
            logger.info("query metric info, task id {}, sub task id {}, deploymentId {}, get null delay info",
                taskId, subTaskId, platformId);
            return null;
        }

        Long maxDelay = hostsDelay.get(0).getRight() / 1000;
        Long minDelay = hostsDelay.get(hostsDelay.size() - 1).getRight() / 1000;

        logger.info("query metric info, task id {}, sub task id {}, deploymentId {}, max delay {}, min delay {}",
            taskId, subTaskId, platformId, maxDelay, minDelay);

        if (maxDelay > maxDelayTs) {
            // 最新的日志的距离现在的时间，防止长时间没数据导致窗口没闭合的延迟
            long logCount = getLogCount(sourceParam, runTs, maxDelay.intValue());
            logger.info("query metric info, task id {}, sub task id {}, deploymentId {}, not consumer log count {}",
                taskId, subTaskId, platformId, logCount);
            // 防止稀疏数据造成的误报
            if (logCount > NOT_CONSUMER_LOG_COUNT) {
                return Pair.of(maxDelay.intValue(), minDelay.intValue());
            } else {
                return null;
            }
        } else {
            logger.info("current delay {} less than max delay {}, ignore this delay", maxDelay, maxDelayTs);
            return null;
        }
    }

    private long getLogCount(SlsSourceParam sourceParam, int runTs, int maxDelay) {
        String user = sourceParam.getAccountName();
        String endpoint = sourceParam.getEndpoint();
        String project = sourceParam.getProject();
        String logStore = sourceParam.getLogStore();
        long logCount = 0;
        Client client = metricsPickupService.getSlsClient(null, endpoint, user);
        if (client == null) {
            return logCount;
        }
        String sql = "* | select count(*) as t";
        List<QueriedLog> logs = metricsPickupService.getLogs(client, project, logStore, runTs - maxDelay, runTs,
            "", sql);
        if (!logs.isEmpty()) {
            List<Map<String, String>> m = SlsUtil.parseLogs(logs);
            logCount = Long.parseLong(m.get(0).get("t"));
        } else {
            logger.warn("no data find, project {}, logstore {}", project, logStore);
        }
        return logCount;
    }

    Pair<Integer, String> genCheckpointAlert(List<StreamMonitorInfo> monitorLists) {
        StringBuilder sb = new StringBuilder();
        int count = 0;
        for (StreamMonitorInfo streamMonitorInfo : monitorLists) {
            if (!streamMonitorInfo.getNoData() && (streamMonitorInfo.getUptime() == null
                || streamMonitorInfo.getUptime() > 1800)
                && streamMonitorInfo.getNewCheckpointCount() != null
                && streamMonitorInfo.getNewCheckpointCount() == 0) {
                count++;
                LogClusterConfigRegion config = streamMonitorInfo.getClusterConfig();
                LogUnionTask logUnionTask = streamMonitorInfo.getLogUnionTask();
                if (config != null) {
                    sb.append("  - Task id: ").append(config.getTaskId()).append(", cid: ").append(
                            config.getRegionCid()).append(", task type ").append(StreamingTaskType.of(config.getTaskType()));
                    if (StringUtils.isNotBlank(config.getInputSourceParam()) && !"{}".equals(config.getInputSourceParam())) {
                        SlsSourceParam slsSourceParam = GSON.fromJson(config.getInputSourceParam(), SlsSourceParam.class);
                        if (slsSourceParam.getLogStore() != null) {
                            sb.append(", logstore: ").append(slsSourceParam.getLogStore());
                        }
                    }
                    if (StreamingTaskType.DEFAULT.getType().equals(config.getTaskType())) {
                        sb.append(", region: ").append(config.getRegion());
                    }
                    sb.append(", no checkpoint in last hour! [详情](")
                        .append(genTaskLink(config.getPlatformId())).append(")\n");
                }
                if (logUnionTask != null) {
                    sb.append("  - Task id: ").append(logUnionTask.getTaskId()).append(", union id: ").append(
                        logUnionTask.getUnionId());

                    if (StringUtils.isNotBlank(logUnionTask.getSourceParam())) {
                        SlsSourceParam sourceParam = GSON.fromJson(logUnionTask.getSourceParam(), SlsSourceParam.class);
                        if (sourceParam.getLogStore() != null) {
                            sb.append(", logstore: ").append(sourceParam.getLogStore());
                        }
                    }
                    sb.append(", region: ").append(logUnionTask.getSlsRegion()).append(
                            ", no checkpoint in last hour! [详情](")
                        .append(genTaskLink(logUnionTask.getPlatformId())).append(")\n");
                }
            }
        }
        if (count == 0) {
            return null;
        } else {
            return Pair.of(count, sb.toString());
        }
    }

    Pair<Integer, String> genNoDataAlert(List<StreamMonitorInfo> monitorLists) {
        StringBuilder sb = new StringBuilder();
        int count = 0;
        for (StreamMonitorInfo streamMonitorInfo : monitorLists) {
            if (streamMonitorInfo.getNoData()) {
                count++;
                LogClusterConfigRegion config = streamMonitorInfo.getClusterConfig();
                LogUnionTask logUnionTask = streamMonitorInfo.getLogUnionTask();
                if (config != null) {
                    sb.append("  - Task id: ").append(config.getTaskId()).append(", cid: ").append(
                            config.getRegionCid()).append(", task type ").append(StreamingTaskType.of(config.getTaskType()));
                    if (StringUtils.isNotBlank(config.getInputSourceParam()) && !"{}".equals(config.getInputSourceParam())) {
                        SlsSourceParam slsSourceParam = GSON.fromJson(config.getInputSourceParam(), SlsSourceParam.class);
                        if (slsSourceParam.getLogStore() != null) {
                            sb.append(", logstore: ").append(slsSourceParam.getLogStore());
                        }
                    }
                    if (StreamingTaskType.DEFAULT.getType().equals(config.getTaskType())) {
                        sb.append(", region: ").append(config.getRegion());
                    }
                    sb.append("\n");
                    sb.append("    - miss monitor data! [详情](").append(genTaskLink(config.getPlatformId())).append(
                        ")\n");
                }
                if (logUnionTask != null) {
                    sb.append("  - Task id: ").append(logUnionTask.getTaskId()).append(", union id: ").append(
                        logUnionTask.getUnionId());
                    if (StringUtils.isNotBlank(logUnionTask.getSourceParam())) {
                        SlsSourceParam sourceParam = GSON.fromJson(logUnionTask.getSourceParam(), SlsSourceParam.class);
                        if (sourceParam.getLogStore() != null) {
                            sb.append(", logstore: ").append(sourceParam.getLogStore());
                        }
                    }

                    sb.append(", region: ").append(logUnionTask.getSlsRegion()).append("\n");
                    sb.append("    - miss monitor data! [详情](").append(genTaskLink(logUnionTask.getPlatformId()))
                        .append(")\n");
                }
            }
        }
        if (count == 0) {
            return null;
        } else {
            return Pair.of(count, sb.toString());
        }
    }

    Pair<Integer, String> genDelayAlert(List<StreamMonitorInfo> monitorLists) {
        StringBuilder sb = new StringBuilder();
        int count = 0;
        for (StreamMonitorInfo streamMonitorInfo : monitorLists) {
            if (!streamMonitorInfo.getNoData() && (streamMonitorInfo.getUptime() == null
                || streamMonitorInfo.getUptime() > 1800)
                && streamMonitorInfo.getDelaySecond() != null && streamMonitorInfo.getDelaySecond() > 0) {
                count++;
                LogClusterConfigRegion config = streamMonitorInfo.getClusterConfig();
                LogUnionTask logUnionTask = streamMonitorInfo.getLogUnionTask();
                if (config != null) {
                    sb.append("  - Task id: ").append(config.getTaskId()).append(", cid: ").append(
                            config.getRegionCid()).append(", task type ").append(StreamingTaskType.of(config.getTaskType()));
                    if (StringUtils.isNotBlank(config.getInputSourceParam()) && !"{}".equals(config.getInputSourceParam())) {
                        SlsSourceParam slsSourceParam = GSON.fromJson(config.getInputSourceParam(), SlsSourceParam.class);
                        if (slsSourceParam.getLogStore() != null) {
                            sb.append(", logstore: ").append(slsSourceParam.getLogStore());
                        }
                    }
                    if (StreamingTaskType.DEFAULT.getType().equals(config.getTaskType())) {
                        sb.append(", region: ").append(config.getRegion());
                    }
                    sb.append("\n");
                    sb.append("    - delay ").append(streamMonitorInfo.getDelaySecond()).append(" seconds");
                    sb.append(", max delta of sources ").append(streamMonitorInfo.getDelaySecond()).append(
                        " seconds! [详情](").append(genTaskLink(config.getPlatformId())).append(")\n");
                }
                if (logUnionTask != null) {
                    sb.append("  - Task id: ").append(logUnionTask.getTaskId()).append(", union id: ").append(
                        logUnionTask.getUnionId());
                    if (StringUtils.isNotBlank(logUnionTask.getSourceParam())) {
                        SlsSourceParam sourceParam = GSON.fromJson(logUnionTask.getSourceParam(), SlsSourceParam.class);
                        if (sourceParam.getLogStore() != null) {
                            sb.append(", logstore: ").append(sourceParam.getLogStore());
                        }
                    }

                    sb.append(", region: ").append(logUnionTask.getSlsRegion()).append("\n");
                    sb.append("    - delay ").append(streamMonitorInfo.getDelaySecond()).append(" seconds");
                    sb.append(", max delta of sources ").append(streamMonitorInfo.getDelaySecond()).append(
                        " seconds! [详情](").append(genTaskLink(logUnionTask.getPlatformId())).append(")\n");
                }
            }
        }
        if (count == 0) {
            return null;
        } else {
            return Pair.of(count, sb.toString());
        }
    }

    Pair<Integer, String> genRestartAlert(List<StreamMonitorInfo> monitorLists) {
        StringBuilder sb = new StringBuilder();
        int count = 0;
        for (StreamMonitorInfo streamMonitorInfo : monitorLists) {
            if (!streamMonitorInfo.getNoData() && streamMonitorInfo.getRestart() != null
                && streamMonitorInfo.getRestart() > 0) {
                count++;
                LogClusterConfigRegion config = streamMonitorInfo.getClusterConfig();
                LogUnionTask logUnionTask = streamMonitorInfo.getLogUnionTask();
                if (config != null) {
                    sb.append("  - Task id: ").append(config.getTaskId()).append(", cid: ").append(
                            config.getRegionCid()).append(", task type ").append(StreamingTaskType.of(config.getTaskType()));
                    if (StringUtils.isNotBlank(config.getInputSourceParam()) && !"{}".equals(config.getInputSourceParam())) {
                        SlsSourceParam slsSourceParam = GSON.fromJson(config.getInputSourceParam(), SlsSourceParam.class);
                        if (slsSourceParam.getLogStore() != null) {
                            sb.append(", logstore: ").append(slsSourceParam.getLogStore());
                        }
                    }
                    if (StreamingTaskType.DEFAULT.getType().equals(config.getTaskType())) {
                        sb.append(", region: ").append(config.getRegion()).append("\n");
                    }
                    sb.append("    - restart ").append(streamMonitorInfo.getRestart()).append(" times! [详情](").append(
                        genTaskLink(config.getPlatformId())).append(")\n");
                }
                if (logUnionTask != null) {
                    sb.append("  - Task id: ").append(logUnionTask.getTaskId()).append(", union id: ").append(
                        logUnionTask.getUnionId());
                    if (StringUtils.isNotBlank(logUnionTask.getSourceParam())) {
                        SlsSourceParam sourceParam = GSON.fromJson(logUnionTask.getSourceParam(), SlsSourceParam.class);
                        if (sourceParam.getLogStore() != null) {
                            sb.append(", logstore: ").append(sourceParam.getLogStore());
                        }
                    }
                    sb.append(", region: ").append(logUnionTask.getSlsRegion()).append("\n");
                    sb.append("    - restart ").append(streamMonitorInfo.getRestart()).append(" times! [详情](").append(
                        genTaskLink(logUnionTask.getPlatformId())).append(")\n");
                }
            }
        }
        if (count == 0) {
            return null;
        } else {
            return Pair.of(count, sb.toString());
        }
    }

    String genTaskLink(String deploymentId) {
        return String.format(
            "https://%s/web/my42lja/zh/#/workspaces/default/namespaces/xdragondw/operations/stream"
                + "/%s/overview", vvpDomain, deploymentId);
    }

    private boolean sendNoDataAlert(List<StreamMonitorInfo> monitorLists, int runTs, int total) {
        Pair<Integer, String> dp = genNoDataAlert(monitorLists);
        if (dp != null) {
            logger.info("send no data alert, message: {}", dp.getRight());
            return sendAlert(runTs, total, dp.getLeft(), dp.getRight(),
                String.format("%s.%s", ALERT_RESOURCE_NAME, "NO_MONITOR_DATA"));
        }
        return true;
    }

    private boolean sendDelayAlert(List<StreamMonitorInfo> monitorLists, int runTs, int total) {
        Pair<Integer, String> dp = genDelayAlert(monitorLists);
        if (dp != null) {
            logger.info("send delay alert, message: {}", dp.getRight());
            return sendAlert(runTs, total, dp.getLeft(), dp.getRight(),
                String.format("%s.%s", ALERT_RESOURCE_NAME, FlinkMetricDao.DELAY_METRIC_NAME));
        }
        return true;
    }

    private boolean sendRestartAlert(List<StreamMonitorInfo> monitorLists, int runTs, int total) {
        Pair<Integer, String> sp = genRestartAlert(monitorLists);
        if (sp != null) {
            logger.info("send restart alert, message: {}", sp.getRight());
            return sendAlert(runTs, total, sp.getLeft(), sp.getRight(),
                String.format("%s.%s", ALERT_RESOURCE_NAME, FlinkMetricDao.RESTART_METRIC_NAME));
        }
        return true;
    }

    private boolean sendCheckpointAlert(List<StreamMonitorInfo> monitorLists, int runTs, int total) {
        Pair<Integer, String> cp = genCheckpointAlert(monitorLists);
        if (cp != null) {
            logger.info("send checkpoint alert, message: {}", cp.getRight());
            return sendAlert(runTs, total, cp.getLeft(), cp.getRight(),
                String.format("%s.%s", ALERT_RESOURCE_NAME, FlinkMetricDao.CHECKPOINT_METRIC_NAME));
        }
        return true;
    }

    private boolean sendAlert(int runTs, int total, int cnt, String detail, String resourceId) {
        String requestId = JobHelper.getRequestId();
        Map<String, String> params = new HashMap<>();
        params.put("trace_id", requestId);
        params.put("title", ALERT_TITLE);
        params.put("total", String.valueOf(total));
        params.put("cnt", String.valueOf(cnt));
        params.put("time", DateUtil.getDateString(new Date(runTs * 1000L), DateUtil.DATE_FORMAT));

        StringBuilder sb = new StringBuilder();
        sb.append("Monitor time: ").append(DateUtil.getDateString(new Date(runTs * 1000L), DateUtil.DATE_FORMAT))
            .append("\n");
        sb.append(detail);
        String content = sb.length() > MAX_ALERT_DETAIL_LEN ? sb.substring(0, MAX_ALERT_DETAIL_LEN) : sb.toString();
        params.put("detail", content);
        if (logger.isDebugEnabled()) {
            logger.debug("[StreamTaskMonitorJob] Alert params are {}", GSON.toJson(params));
        }

        Item item = new Item();
        item.setResourceId(DigestUtils.md5DigestAsHex(resourceId.getBytes()));
        item.setResourceType(ALERT_LABEL);
        item.setParamsStr(DevOpsApiService.paramToJson(params));

        try {
            OpsXdragonSendAlertResponse resp = alertService.sendAlert(Lists.newArrayList(ALERT_LABEL), ALERT_SOURCE,
                Lists.newArrayList(item), null, null);
            boolean isSuccess = true;
            logger.info("alert over, request id {}, get {} response", resp.getRequestId(),
                resp.getSendResponses().size());
            for (SendResponse sr : resp.getSendResponses()) {
                if (!sr.getSuccessed()) {
                    isSuccess = false;
                    logger.error("[StreamTaskMonitorJob] Alert fail, alert request id {}, resourceId {}, uuid {}",
                        resp.getRequestId(), sr.getResourceId(), sr.getUuid());
                } else {
                    logger.info("[StreamTaskMonitorJob] Alert ok, alert request id {}", resp.getRequestId());
                }
            }
            return isSuccess;
        } catch (ClientException ce) {
            logger.error("[StreamTaskMonitorJob] Alert fail, alert request id {}, error {}", requestId,
                ce.getMessage());
            return false;
        }
    }

    private void cacheDelayInfo(List<StreamMonitorInfo> streamMonitorInfos, int dataTs) {
        Set<Long> taskIds = new HashSet<>();
        streamMonitorInfos.forEach(d -> {
            if (d.getDelaySecond() != null && d.getDelaySecond() > 0 && d.getClusterConfig() != null) {
                taskIds.add(d.getClusterConfig().getTaskId());
            }
        });
        taskIds.forEach(id -> {
            String cacheKey = "log_cluster_delay_" + id;
            redisUtil.set(cacheKey, new Integer(dataTs), 6 * 3600);
        });
        taskIds.clear();
        streamMonitorInfos.forEach(d -> {
            if (d.getDelaySecond() != null && d.getDelaySecond() > 0 && d.getLogUnionTask() != null) {
                taskIds.add(d.getLogUnionTask().getTaskId());
            }
        });
        taskIds.forEach(id -> {
            String cacheKey = "log_union_delay_" + id;
            redisUtil.set(cacheKey, new Integer(dataTs), 6 * 3600);
        });
    }
}
