package com.aliyun.xdragon.biz.log.config;

import freemarker.template.TemplateExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/09/28
 */
@Configuration
public class TplConfig {
    @Bean("logFreeMarkerConfig")
    public freemarker.template.Configuration logFreeMarkerConfig() {
        freemarker.template.Configuration cfg = new freemarker.template.Configuration(freemarker.template.Configuration.getVersion());
        cfg.setClassForTemplateLoading(this.getClass(), "/templates/");

        cfg.setDefaultEncoding("UTF-8");
        cfg.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        cfg.setLogTemplateExceptions(false);
        return cfg;
    }
}
