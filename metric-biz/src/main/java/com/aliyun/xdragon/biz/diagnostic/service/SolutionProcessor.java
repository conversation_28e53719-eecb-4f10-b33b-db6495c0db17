package com.aliyun.xdragon.biz.diagnostic.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.alibaba.ecs.devops.consistent.queryField.VmQueryField;
import com.alibaba.ecs.devops.consistent.queryField.VmQueryField.VmField;
import com.alibaba.ecs.devops.consistent.response.Vm;

import com.aliyun.xdragon.algorithm.common.LinearUtils;
import com.aliyun.xdragon.biz.diagnostic.repository.InstanceTypeInfoDao;
import com.aliyun.xdragon.biz.log.repository.AlertMetaSlsDao;
import com.aliyun.xdragon.biz.workitem.service.impl.DetailVmConsistentImpl;
import com.aliyun.xdragon.common.enumeration.DiagnosisStatus;
import com.aliyun.xdragon.common.model.metric.DiagnosisResult;
import com.aliyun.xdragon.common.model.metric.DiagnosisSolution;
import com.aliyun.xdragon.common.model.metric.MetricPoint;
import com.aliyun.xdragon.common.model.metric.MetricSeries;
import com.aliyun.xdragon.common.model.metric.request.PerformanceDiagnosisRequest;
import com.aliyun.xdragon.common.model.metric.strategy.SolutionStrategy;
import com.aliyun.xdragon.common.util.MDCUtil;
import com.aliyuncs.exceptions.ClientException;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.math3.stat.StatUtils;
import org.apache.commons.math3.util.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/03/05
 */
@Service
public class SolutionProcessor {
    private static final Logger logger = LoggerFactory.getLogger(SolutionProcessor.class);

    @Autowired
    private MetricDownloader metricDownloader;
    @Autowired
    private AlertMetaSlsDao alertMetaSlsDao;
    @Autowired
    private DetailVmConsistentImpl detailVmConsistent;
    @Autowired
    private InstanceTypeInfoDao instanceTypeInfoDao;

    private final ThreadFactory threadFactory = new ThreadFactoryBuilder()
        .setNameFormat("solution-processor-pool-%d").build();
    private final ExecutorService executorService = new ThreadPoolExecutor(32, 32, 0, TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(1024), threadFactory, new ThreadPoolExecutor.AbortPolicy());

    /**
     * 根据给定的策略生成诊断解决方案列表
     *
     * @param strategy          解决方案策略
     * @param request           性能诊断请求
     * @param scenarioResultMap 场景结果映射，键为场景名称，值为该场景的诊断结果列表
     * @return 诊断解决方案列表
     */
    public List<DiagnosisSolution> generateSolution(SolutionStrategy strategy, PerformanceDiagnosisRequest request,
        Map<String, List<DiagnosisResult>> scenarioResultMap) {
        logger.info("Generate solution {}", strategy.getSolutionName());
        // 根据策略中的解决方案名称进行不同的处理
        switch (strategy.getSolutionName()) {
            case "notice":
                return notice(strategy, request);
            case "liveMigration":
                return liveMigration(strategy, request);
            case "splitLock":
                return splitLock(strategy, request);
            case "perpetratorDiscovery":
                return perpetratorDiscovery(strategy, request, scenarioResultMap);
            case "shared":
                return shared(strategy, request);
            default:
                // 如果没有匹配的解决方案，则返回空列表
                return new ArrayList<>();
        }
    }

    private List<DiagnosisSolution> shared(SolutionStrategy strategy, PerformanceDiagnosisRequest request) {
        //判断实例是否是共享实例
        boolean shared = false;
        try {
            String instanceType = detailVmConsistent
                .queryVmSimpleInfo(Collections.singletonList(request.getInstanceId())).get(0).getInstanceType();
            String category = instanceTypeInfoDao.getCategory(instanceType);
            if ("share".equals(category) || "credit".equals(category)) {
                shared = true;
            }
        } catch (Exception e) {
            logger.error("[shared] Query instance type category failed.", e);
        }
        //如果实例是共享实例，则生成解决方案
        List<DiagnosisSolution> solutions = new ArrayList<>();
        if (shared) {
            DiagnosisSolution solution = new DiagnosisSolution();
            solution.setType("shared");
            String formatStr = strategy.getParam().getProperty("description", "%s");
            solution.setDescription(String.format(formatStr, request.getInstanceId()));
            solution.setLink(strategy.getParam().getProperty("link", ""));
            solutions.add(solution);
        }
        return solutions;
    }

    private List<DiagnosisSolution> notice(SolutionStrategy strategy, PerformanceDiagnosisRequest request) {
        DiagnosisSolution solution = new DiagnosisSolution();
        String suffix = strategy.getApplyScenarios().stream().map(x -> "-" + x).sorted().collect(Collectors.joining());
        solution.setType("notice" + suffix);
        solution.setDescription(strategy.getParam().getProperty("description", ""));
        solution.setLink(strategy.getParam().getProperty("link", ""));
        return Collections.singletonList(solution);
    }

    private List<DiagnosisSolution> liveMigration(SolutionStrategy strategy, PerformanceDiagnosisRequest request) {
        //解析表达式，判断是否应当热迁移
        EvaluationContext context = new StandardEvaluationContext();
        context.setVariable("request", request);
        ExpressionParser parser = new SpelExpressionParser();
        Expression expression = parser.parseExpression(strategy.getParam().getProperty("expression", "true"));
        //无需热迁移
        if (Boolean.FALSE.equals(expression.getValue(context, Boolean.class))) {
            if (strategy.getParam().getProperty("exclusiveKey") == null) {
                //不生成解决方案
                return Collections.emptyList();
            } else {
                //针对独占的情形，生成对应的解决方案
                DiagnosisSolution solution = new DiagnosisSolution();
                solution.setType(strategy.getParam().getProperty("exclusiveKey"));
                solution.setLink("");
                String exclusive = strategy.getParam().getProperty("exclusiveDescription", "%s");
                solution.setDescription(String.format(exclusive, request.getInstanceId()));
                return Collections.singletonList(solution);
            }
        }
        //需要热迁移，生成解决方案
        DiagnosisSolution solution = new DiagnosisSolution();
        solution.setType("liveMigration");
        solution.setDescription(String.format(strategy.getParam().getProperty("description", "%s"),
            request.getInstanceId()));
        solution.setLink(strategy.getParam().getProperty("link", ""));
        return Collections.singletonList(solution);
    }

    private List<DiagnosisSolution> splitLock(SolutionStrategy strategy, PerformanceDiagnosisRequest request) {
        //查询异常发生在哪些实例上
        String exceptions = Arrays.stream(strategy.getParam().getProperty("exceptions").split(","))
            .map(x -> "exceptionName: " + x).collect(Collectors.joining(" or "));
        String query = String.format("ncIp: %s and (%s) | select distinct instanceId", request.getNcIp(), exceptions);
        logger.info("[splitLock] {}", query);
        List<String> exceptionInstances = alertMetaSlsDao.getLogs(request.getStartTs(), request.getEndTs(), query)
            .stream().map(item -> item.GetLogItem().GetLogContents().get(0).GetValue())
            .filter(x -> request.getInstanceId().equals(x) || request.getNeighbors().contains(x))
            .collect(Collectors.toList());
        String scenario = strategy.getParam().getProperty("scenario");
        //如果不存在潜在施害实例，则不生成解决方案
        if (exceptionInstances.isEmpty()){
            return Collections.emptyList();
        }
        //解析结果并生成解决方案
        List<DiagnosisSolution> solutions = new ArrayList<>();
        if (exceptionInstances.contains(request.getInstanceId()) && exceptionInstances.size() == 1) {
            //只有当前实例存在Split Lock异常
            DiagnosisSolution solution = generateSplitLockSolution(strategy, scenario, "desc1", request.getInstanceId());
            solutions.add(solution);
        } else {
            try {
                List<String> otherExceptionInstances = exceptionInstances.stream()
                    .filter(x -> !x.equals(request.getInstanceId())).collect(Collectors.toList());
                String perpetratorStr = generatePerpetratorStr(request, otherExceptionInstances);
                DiagnosisSolution solution;
                if (!exceptionInstances.contains(request.getInstanceId())) {
                    //只有其他实例存在Split Lock异常
                    solution = generateSplitLockSolution(strategy, scenario,"desc2", perpetratorStr);
                } else {
                    //既有当前实例，又有其他实例存在Split Lock异常
                    solution = generateSplitLockSolution(strategy, scenario,"desc3", request.getInstanceId(), perpetratorStr);
                }
                solution.setComponent(scenario + "-" + strategy.getParam().getProperty("component"));
                solution.setAdditionInfo(exceptionInstances);
                solutions.add(solution);
            } catch (ClientException e) {
                logger.error("[splitLock] Query aliUid failed.", e);
            }
        }
        return solutions;
    }

    private DiagnosisSolution generateSplitLockSolution(SolutionStrategy strategy, String scenario, String key, String... values) {
        DiagnosisSolution solution;
        solution = new DiagnosisSolution();
        solution.setType(key.equals("desc2") ? "perpetratorDiscovery_" + scenario : "SplitLock");
        solution.setDescription(String.format(strategy.getParam().getProperty(key, "%s"), (Object[])values));
        solution.setLink(strategy.getParam().getProperty("link", ""));
        return solution;
    }

    private List<DiagnosisSolution> perpetratorDiscovery(SolutionStrategy strategy, PerformanceDiagnosisRequest request,
        Map<String, List<DiagnosisResult>> scenarioResultMap) {
        //获得当前场景的诊断结果
        String scenario = strategy.getParam().getProperty("scenario");
        List<DiagnosisResult> results = scenarioResultMap.get(scenario);
        String neighborType = strategy.getParam().getProperty("neighborType", "socket");
        List<String> neighbors = neighborType.equals("socket") ? request.getSocketNeighbors() :
            neighborType.equals("node") ? request.getNodeNeighbors() : request.getCcdNeighbors();
        if (CollectionUtils.isEmpty(results) || CollectionUtils.isEmpty(neighbors)) {
            return Collections.emptyList();
        }

        //拉取指标数据
        Pair<Pair<Integer, Integer>, Integer> p = getTsRange(results);
        Pair<Integer, Integer> tsRange = p.getFirst();
        int len = p.getSecond();
        double[][] data = downloadNeighborMetrics(strategy, request.getInstanceId(), neighbors, tsRange);
        logger.info("Download {} data series", data.length);

        //计算资源占用增加量
        double[] diff = new double[data.length];
        for (int i = 0; i < data.length; i++) {
            LinearUtils.fillNan(data[i], 0);
            double meanBefore = StatUtils.mean(data[i], 0, len);
            double meanAfter = StatUtils.mean(data[i], len, data[i].length - len);
            diff[i] = Math.max(0, meanAfter - meanBefore);
        }
        double totalDiff = StatUtils.sum(diff);
        logger.info("Total diff: {}, Diff List: {}", totalDiff, diff);

        //判定施害者
        List<DiagnosisSolution> solutions = new ArrayList<>();
        double relThreshold = Double.parseDouble(strategy.getParam().getProperty("relThreshold", "0"));
        double absThreshold = Double.parseDouble(strategy.getParam().getProperty("absThreshold", "0"));
        List<String> perpetrators = new ArrayList<>();
        for (int i = 0; i < data.length; i++) {
            if (diff[i] > absThreshold && diff[i] > relThreshold * totalDiff) {
                //绝对值和比例都超过阈值，判定为施害者，生成对应的解决方案
                perpetrators.add(neighbors.get(i));
            }
        }

        //如果有施害者，则生成解决方案
        if (!CollectionUtils.isEmpty(perpetrators)) {
            try {
                String perpetratorStr = generatePerpetratorStr(request, perpetrators);
                //生成解决方案
                DiagnosisSolution solution = new DiagnosisSolution();
                solution.setType("perpetratorDiscovery_" + scenario);
                solution.setDescription(String.format(strategy.getParam().getProperty("description", "%s"),
                    perpetratorStr, results.get(0).getProblem()));
                solution.setLink(strategy.getParam().getProperty("link", ""));
                solution.setAdditionInfo(perpetrators);
                String component = scenario + "-" + strategy.getParam().getProperty("component");
                solution.setComponent(component);
                solutions.add(solution);
                logger.info("Discover {} perpetrator", perpetrators.size());
            } catch (Exception e) {
                logger.error("[perpetratorDiscovery] Query aliUid failed.", e);
            }
        }
        return solutions;
    }

    private String generatePerpetratorStr(PerformanceDiagnosisRequest request, List<String> perpetrators)
        throws ClientException {
        //获取阿里云UID
        perpetrators.add(request.getInstanceId());
        Map<String, String> aliUidMap = detailVmConsistent.batchQueryVmInfo(perpetrators, VmField.ALI_UID)
            .stream().collect(Collectors.toMap(Vm::getInstanceId, Vm::getAliUid));
        perpetrators.remove(perpetrators.size() - 1);

        String requestAliUid = aliUidMap.get(request.getInstanceId());
        //生成施害者字符串
        return perpetrators.stream()
            .map(x -> {
                    if (requestAliUid.equals(aliUidMap.get(x))) {
                        return x + "(同客户)";
                    }
                    return x;
                }
            ).collect(Collectors.joining(","));
    }

    private double[][] downloadNeighborMetrics(SolutionStrategy strategy, String instanceId, List<String> neighbors,
        Pair<Integer, Integer> tsRange) {
        String[] metrics = strategy.getParam().getProperty("metric", "").split(",");
        //创建并行查询任务。注意，该实例也需要查询，以对齐数据
        int startTs = tsRange.getFirst();
        int endTs = tsRange.getSecond();
        int interval = PerformanceDiagnosisServiceImpl.TARGET_INTERVAL;
        List<String> downloadInstances = new ArrayList<>(neighbors);
        downloadInstances.add(instanceId);
        Map<String, String> logMdc = MDC.getCopyOfContextMap();
        List<Future<List<MetricSeries>>> tasks = new ArrayList<>();
        for (String detailMetric : metrics) {
            for (int i = 0; i < downloadInstances.size(); i += MetricDownloader.MAX_NEIGHBORS) {
                int finalI = i;
                Future<List<MetricSeries>> future = executorService.submit(
                    () -> {
                        MDCUtil.safeSetMdc(logMdc);
                        List<String> vmSubList = downloadInstances.subList(finalI,
                            Math.min(finalI + MetricDownloader.MAX_NEIGHBORS, downloadInstances.size()));
                        return metricDownloader.downloadMetricSeries(detailMetric, vmSubList, startTs, endTs,
                            interval);
                    });
                tasks.add(future);
            }
        }
        //获取并行任务结果
        Map<String, MetricSeries> results = new HashMap<>(16);
        for (Future<List<MetricSeries>> task : tasks) {
            try {
                List<MetricSeries> seriesList = task.get();
                for (MetricSeries series : seriesList) {
                    String metric = series.getMetric();
                    results.put(metric, series);
                }
            } catch (InterruptedException | ExecutionException e) {
                logger.error("[compareMetrics] Fetch metric series failed.", e);
                logger.error("[compareMetrics] Inner exception: ", e.getCause());
            }
        }
        //合并指标数据
        List<MetricSeries> rawMetricSeries = new ArrayList<>();
        for (String instance : downloadInstances) {
            boolean added = false;
            for (String metric : metrics) {
                String key = metric + "/" + instance;
                MetricSeries series = results.get(key);
                if (series != null && !CollectionUtils.isEmpty(series.getSeries())) {
                    rawMetricSeries.add(series);
                    added = true;
                    break;
                }
            }
            if (!added) {
                rawMetricSeries.add(new MetricSeries(instanceId));
            }
        }
        //对齐指标数据，并删掉最后一个
        double[][] data = metricDownloader.alignSeries(rawMetricSeries, interval);
        return Arrays.copyOf(data, data.length - 1);
    }

    /**
     * 获取受损点前后各一个时间片，返回总时间范围，及其第一个时间片的数据点个数
     *
     * @param results 诊断结果列表
     * @return 返回一个Pair，其中内部的Pair表示总时间范围（开始时间，结束时间），外部的值表示第一个时间片的数据点个数
     */
    private Pair<Pair<Integer, Integer>, Integer> getTsRange(List<DiagnosisResult> results) {
        Pair<Integer, Integer> tsRange;
        int len, interval = PerformanceDiagnosisServiceImpl.TARGET_INTERVAL;
        if (results.get(0).getStatus() == DiagnosisStatus.DAMAGED) {
            // 如果首个结果状态为DAMAGED，则将其均分为两个时间片
            tsRange = Pair.create(results.get(0).getStartTs(), results.get(0).getEndTs());
            len = (tsRange.getSecond() - tsRange.getFirst()) / interval / 2;
        } else {
            // 找到第一个状态为DAMAGED的结果
            int i;
            for (i = 1; i < results.size(); i++) {
                if (results.get(i).getStatus() == DiagnosisStatus.DAMAGED) {
                    break;
                }
            }
            // 将它与前一个结果作为受损前后的时间片
            tsRange = Pair.create(results.get(i - 1).getStartTs(), results.get(i).getEndTs());
            len = (results.get(i - 1).getEndTs() - results.get(i - 1).getStartTs()) / interval + 1;
        }
        return Pair.create(tsRange, len);
    }

}
