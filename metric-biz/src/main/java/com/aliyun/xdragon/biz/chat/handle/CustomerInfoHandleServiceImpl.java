package com.aliyun.xdragon.biz.chat.handle;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.xdragon.api.service.chat.model.AgentAnswerResponse;
import com.aliyun.xdragon.biz.workitem.service.impl.OpsInfoRetrievalServiceImpl;
import com.aliyun.xdragon.common.model.gts.PublicTamUser;
import com.aliyun.xdragon.common.model.gts.UidInfo;
import com.aliyun.xdragon.service.common.agent.GtsClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.aliyun.xdragon.service.common.util.Checks.nullOrEmpty;

/**
 * <AUTHOR>
 * @date 2024/07/05
 */
@Service("customerInfoHandleService")
public class CustomerInfoHandleServiceImpl extends ChatSceneBasicHandleServiceImpl {
    private static final Logger logger = LoggerFactory.getLogger(CustomerInfoHandleServiceImpl.class);

    @Autowired
    private GtsClient gtsClient;

    @Autowired
    private OpsInfoRetrievalServiceImpl opsInfoRetrievalService;

    @Override
    protected String getViewContent(AgentAnswerResponse response, Integer source) throws Exception {
        JSONObject data = JSONObject.parseObject(getContent(response));
        if (StringUtils.isNotEmpty(data.getString("message"))) {
            return data.getString("message");
        }
        return fillViewTemplate(data,"chat/customer_info.tpl", response.getTool(), source);
    }

    @Override
    protected String getContent(AgentAnswerResponse response) throws Exception {
        JSONObject data = new JSONObject();
        String uid = response.getParams().getString("aliuid");
        data.put("aliuid", uid);
        UidInfo uidInfo = gtsClient.queryUidInfo(uid);
        if (uidInfo == null) {
            data.put("message", String.format("未查询到用户%s的相关信息。", uid));
            return JSONObject.toJSONString(data);
        }
        data.put("name", uidInfo.getUidName());
        data.put("cid", uidInfo.getCid());
        List<PublicTamUser> tams = uidInfo.getPublicTamUser();
        if (tams != null && !tams.isEmpty()) {
            List<String> tamList = new ArrayList<>(tams.size());
            for (PublicTamUser tam : tams) {
                if (tam != null && tam.getIsDimission() != null && "N".equals(tam.getIsDimission())) {
                    tamList.add(String.format("&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;工号：%s，姓名：%s", tam.getWorkNo(), tam.getDisplayName()));
                }
            }
            data.put("tams", String.join("</br>", tamList));
        }

        List<Map<String, String>> pdsaInfos = opsInfoRetrievalService.getUserPdsaInfo(uid);
        if (pdsaInfos != null && !pdsaInfos.isEmpty()) {
            if (pdsaInfos.get(0).containsKey("gcLevel") && !nullOrEmpty(pdsaInfos.get(0).get("gcLevel"))) {
                data.put("gcLevel", pdsaInfos.get(0).get("gcLevel"));
            }
            List<String> cbmList = new ArrayList<>(pdsaInfos.size());
            for (Map<String, String> info : pdsaInfos) {
                if (info.containsKey("cbmName") && !nullOrEmpty(info.get("cbmName")) && info.containsKey("cbmId") && !nullOrEmpty(info.get("cbmId"))) {
                    cbmList.add(String.format("&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;工号：%s，姓名：%s", info.get("cbmId"), info.get("cbmName")));
                }
            }
            if (!cbmList.isEmpty()) {
                data.put("cbms", String.join("</br>", cbmList));
            }
        }

        Pair<Integer, List<String>> vmOwned = opsInfoRetrievalService.getUserVmSummary(uid);
        if (vmOwned != null && vmOwned.getLeft() > 0 && vmOwned.getRight() != null && !vmOwned.getRight().isEmpty()) {
            data.put("vmCnt", vmOwned.getLeft());
            data.put("vmListSize", vmOwned.getRight().size());
            data.put("vmList", String.join(",", vmOwned.getRight()));
        }
        return JSONObject.toJSONString(data);
    }

    @Override
    protected String getUrl(AgentAnswerResponse response, Integer source) {
        return null;
    }

    @Override
    protected String getLinks(AgentAnswerResponse response) {
        return null;
    }

    @Override
    protected String getAones(AgentAnswerResponse response) {
        return null;
    }

    @Override
    protected boolean aiAgentResCheck(AgentAnswerResponse response) throws Exception {
        if (!response.isFinish()) {
            return false;
        }

        if (response.getParams() == null) {
            response.setTips("未提取出用户信息");
            return false;
        }

        JSONObject params = response.getParams();
        String uid = params.getString("aliuid");
        if (StringUtils.isNotBlank(uid)) {
            return true;
        }

        String machineId = params.getString("machine_id");
        String paramName = params.getString("instance_extension");
        if (StringUtils.isNotBlank(machineId) && "aliuid".equals(paramName)) {
            List<String> machineIds = JSONObject.parseArray(machineId, String.class);
            if (CollectionUtils.isEmpty(machineIds)) {
                response.setTips("未提取出有效参数");
                return false;
            }
            uid = opsInfoRetrievalService.getVmUid(machineIds.get(0));
            if (StringUtils.isNotBlank(uid)) {
                params.put("aliuid", uid);
                logger.info("[CustomerInfoHandleServiceImpl] Param mismatch, get uid {} from instance {}", uid, machineId);
                return true;
            }
        }

        response.setTips("未提取出用户信息");
        return false;
    }
}
