package com.aliyun.xdragon.biz.chat.repository;

import java.util.List;
import java.util.Map;

import com.aliyun.xdragon.common.generate.model.map.ChatMessageInfoCustomMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/01/08
 */
@Repository
public class ChatMessageInfoDao {
    @Autowired
    private ChatMessageInfoCustomMapper mapper;

    public List<Map<String, String>> queryWorkItemMessageInfo(List<String> workItemIds) {
        return mapper.queryWorkItemMessageInfo(workItemIds);
    }
}
