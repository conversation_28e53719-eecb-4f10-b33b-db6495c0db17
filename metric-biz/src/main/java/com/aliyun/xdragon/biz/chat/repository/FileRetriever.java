package com.aliyun.xdragon.biz.chat.repository;

import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

import com.aliyun.xdragon.biz.chat.util.ToolInfo;
import com.aliyun.xdragon.service.common.cache.RedisUtil;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/02/07
 */
@Repository
public class FileRetriever {
    private static final Logger logger = LoggerFactory.getLogger(FileRetriever.class);
    private static final long REDIS_EXPIRE_TIME = 300;

    private static final String REDIS_PREFIX = "bailian_files:";
    private static final String OSS_PREFIX = "bailian_tool/";
    private static final String LOCAL_PREFIX = "/bailian_tool/";

    @Autowired
    private ChatOssDao chatOssDao;
    @Autowired
    private RedisUtil redisService;
    @Value("${bailian.prod:false}")
    private boolean prodEnvironment;

    /**
     * 加载文件
     *
     * @param fileName 文件名
     * @return 文件内容
     */
    public String loadFile(String fileName) {
        if (fileName == null) {
            // 快速返回
            return null;
        }
        if (prodEnvironment) {
            // 先从redis中获取
            try {
                String redisResult = (String)redisService.get(REDIS_PREFIX + fileName);
                if (redisResult != null) {
                    return redisResult;
                }
            } catch (Exception e) {
                logger.warn("loadFile from redis error. filename: {}", fileName, e);
            }
            // 再从oss中获取
            try {
                String ossFileName = OSS_PREFIX + fileName;
                InputStream in = chatOssDao.getFileInputStreamUnsafe(ossFileName);
                String ossResult = IOUtils.toString(in, Charset.defaultCharset());
                if (ossResult != null) {
                    // 保存到redis
                    redisService.set(REDIS_PREFIX + fileName, ossResult, REDIS_EXPIRE_TIME);
                    return ossResult;
                }
            } catch (Exception e) {
                logger.warn("loadFile from oss error. filename: {}", fileName, e);
            }
        }
        // 最后从本地获取
        try {
            String localFileName = LOCAL_PREFIX + fileName;
            InputStream in = getClass().getResourceAsStream(localFileName);
            String localResult = IOUtils.toString(in, Charset.defaultCharset());
            if (localResult != null) {
                return localResult;
            }
        } catch (Exception e) {
            logger.warn("loadFile from local error. filename: {}", fileName, e);
        }
        // 都没有找到，返回null
        logger.error("file not found. filename: {}", fileName);
        return null;
    }

    public String loadSummary() {
        return loadFile("tool_summary.md");
    }

    public String loadToolDoc(String toolName) {
        return loadFile(toolName + ".md");
    }

    public String loadToolInputDoc(String toolName) {
        String entireDoc = loadToolDoc(toolName);
        if (entireDoc == null) {
            return null;
        }
        int id = entireDoc.indexOf("## 输出结果");
        return id == -1 ? entireDoc : removeFirstLine(entireDoc.substring(0, id));
    }

    public String loadToolOutputDoc(String toolName) {
        String entireDoc = loadToolDoc(toolName);
        if (entireDoc == null) {
            return null;
        }
        int id = entireDoc.indexOf("## 输出结果");
        return id == -1 ? entireDoc : removeFirstLine(entireDoc.substring(id));
    }

    private String removeFirstLine(String doc) {
        return doc.substring(doc.indexOf("\n") + 1);
    }

    /**
     * 获取工具信息
     *
     * @param toolName 工具名
     * @return 工具信息，包括完整类路径和方法名
     */
    public ToolInfo getToolInfo(String toolName) {
        // 加载配置
        String jsonStr = loadFile("tool.json");
        if (jsonStr == null) {
            return null;
        }
        // 解析配置
        Map<String, ToolInfo> toolInfoMap = JSON.parseObject(jsonStr, new TypeReference<Map<String, ToolInfo>>() {
        });
        return toolInfoMap.get(toolName);
    }

    protected void setProdEnvironmentForTest(boolean prodEnvironment) {
        this.prodEnvironment = prodEnvironment;
    }
}
