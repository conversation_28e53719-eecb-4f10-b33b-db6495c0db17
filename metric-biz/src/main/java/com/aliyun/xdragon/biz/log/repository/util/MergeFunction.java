package com.aliyun.xdragon.biz.log.repository.util;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class MergeFunction {
    public static final Function<List<Long>, Long> SUM_LONG = l -> l.stream().filter(Objects::nonNull).reduce(0L,
        Long::sum);
    public static final Function<List<Integer>, Integer> SUM_INTEGER = l -> l.stream().filter(Objects::nonNull).reduce(0,
        Integer::sum);

    public static <T> List<T> mergeList(List<List<T>> lists) {
        return lists.stream().flatMap(List::stream).collect(Collectors.toList());
    }
}
