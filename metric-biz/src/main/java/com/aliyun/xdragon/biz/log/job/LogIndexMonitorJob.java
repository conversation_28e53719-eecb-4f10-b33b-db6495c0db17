package com.aliyun.xdragon.biz.log.job;

import java.util.LinkedList;
import java.util.List;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.xdragon.biz.log.model.LogIndexMonitorTask;
import com.aliyun.xdragon.service.common.job.AbstractProcessTask;
import com.aliyun.xdragon.biz.log.service.LogIndexMonitorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/07/28
 */
@Component
@Lazy
public class LogIndexMonitorJob<T> extends AbstractProcessTask<T> {
    private static final Logger logger = LoggerFactory.getLogger(LogIndexMonitorJob.class);

    @Autowired
    private LogIndexMonitorService logIndexMonitorService;

    @Override
    public ProcessResult process(JobContext context, int dataTs, int schedTs) throws Exception {
        List<LogIndexMonitorTask> tasks = logIndexMonitorService.getMonitorTask();
        logger.info("get total {} tasks", tasks.size());
        List<LogItem> changeIndex = new LinkedList<>();
        for (LogIndexMonitorTask task : tasks) {
            logger.info("check index for project {}, log store {}", task.getProject(), task.getLogStore());
            LogItem item = logIndexMonitorService.getIndexChangeItem(task, dataTs);
            if (item != null) {
                changeIndex.add(item);
            }
        }

        if (changeIndex.size() > 0) {
            logger.info("detect {} logstore index changed", changeIndex.size());
            logIndexMonitorService.writeChangeLog(changeIndex);
        } else {
            logger.info("no index change!");
        }
        return new ProcessResult(true);
    }
}
