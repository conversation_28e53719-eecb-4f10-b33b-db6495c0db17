package com.aliyun.xdragon.biz.allinonerisk.repository;

import java.util.Date;
import java.util.List;

import com.aliyun.xdragon.common.enumeration.aone.AoneReqStatusEnum;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRiskAction;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRiskActionExample;
import com.aliyun.xdragon.common.generate.log.model.map.AllinoneRiskActionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class AllinoneRiskActionDao {

    @Autowired
    private AllinoneRiskActionMapper mapper;

    public AllinoneRiskAction insertSelective(AllinoneRiskAction riskAction) {
        riskAction.setGmtCreated(new Date(System.currentTimeMillis() / 1000 * 1000));
        riskAction.setGmtModified(new Date(System.currentTimeMillis() / 1000 * 1000));
        int res = mapper.insertSelective(riskAction);
        if (res > 0) {
            return riskAction;
        }
        return null;
    }

    public AllinoneRiskAction getActionById(Long actionId) {
        AllinoneRiskActionExample e = new AllinoneRiskActionExample();
        AllinoneRiskActionExample.Criteria c = e.createCriteria();
        c.andActionIdEqualTo(actionId);
        return mapper.selectOneByExample(e);
    }

    public AllinoneRiskAction getActionByRiskIssue(Long riskId, Long issueId) {
        AllinoneRiskActionExample e = new AllinoneRiskActionExample();
        AllinoneRiskActionExample.Criteria c = e.createCriteria();
        c.andRiskIdEqualTo(riskId);
        c.andIssueIdEqualTo(issueId);
        return mapper.selectOneByExample(e);
    }

    public AllinoneRiskAction getActionByIssueId(Long issueId) {
        AllinoneRiskActionExample e = new AllinoneRiskActionExample();
        AllinoneRiskActionExample.Criteria c = e.createCriteria();
        c.andIssueIdEqualTo(issueId);
        return mapper.selectOneByExample(e);
    }

    public AllinoneRiskAction updateSelective(AllinoneRiskAction riskAction) {
        AllinoneRiskActionExample e = new AllinoneRiskActionExample();
        AllinoneRiskActionExample.Criteria c = e.createCriteria();
        riskAction.setGmtModified(new Date());
        c.andActionIdEqualTo(riskAction.getActionId());
        c.andGmtCreatedEqualTo(riskAction.getGmtCreated());
        int res = mapper.updateByExampleSelective(riskAction, e);
        if (res > 0) {
            return riskAction;
        }
        return null;
    }

    public List<AllinoneRiskAction> listValidActionsByRiskId(Long riskId) {
        AllinoneRiskActionExample e = new AllinoneRiskActionExample();
        AllinoneRiskActionExample.Criteria c = e.createCriteria();
        c.andRiskIdEqualTo(riskId);
        // 状态为取消的不展示，可认为是删除了的
        c.andIssueStatusNotEqualTo(AoneReqStatusEnum.STATUS_141230.getName());
        e.setOrderByClause("gmt_created");
        return mapper.selectByExample(e);
    }

    public int deleteByRiskId(Long riskId) {
        AllinoneRiskActionExample e = new AllinoneRiskActionExample();
        AllinoneRiskActionExample.Criteria c = e.createCriteria();
        c.andRiskIdEqualTo(riskId);
        return mapper.deleteByExample(e);
    }

}
