package com.aliyun.xdragon.biz.log.job;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.xdragon.biz.log.repository.AnomalyDataProcessDao;
import com.aliyun.xdragon.common.generate.model.AnomalyDataProcess;
import com.aliyun.xdragon.common.model.AnomalyData;
import com.aliyun.xdragon.service.common.job.AbstractMapTask;
import com.aliyun.xdragon.service.common.service.PostProcessor;
import com.aliyun.xdragon.service.common.service.ProcessorDispatcher;
import com.aliyun.xdragon.biz.log.repository.AnomalyStoreDao;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/09/06
 */
@Service
@Lazy
public class AnomalyDataProcessor extends AbstractMapTask<AnomalyDataProcess> {
    private static final Logger logger = LoggerFactory.getLogger(AnomalyDataProcessor.class);

    private final ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(
        "anomaly-data-processor-pool-%d").build();
    private final ExecutorService executorService = new ThreadPoolExecutor(32, 32, 0, TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(1024), threadFactory, new ThreadPoolExecutor.AbortPolicy());

    private final String REQ = "RequestID";
    private final String REQ_BAK = "RequestID_Bak";

    @Autowired
    private AnomalyStoreDao anomalyStoreDao;

    @Autowired
    private AnomalyDataProcessDao anomalyDataProcessDao;

    @Autowired
    private ProcessorDispatcher dispatcher;

    @Override
    public List<AnomalyDataProcess> genTask(JobContext context, int dataTs) {
        List<AnomalyDataProcess> processTasks = anomalyDataProcessDao.listTasks();
        processTasks.removeIf(t -> dataTs * 1000L - t.getLastRunTime() < t.getRunInterval() * 1000L || dataTs % t.getRunInterval() != 0);
        return processTasks;
    }

    @Override
    public ProcessResult processSubTask(JobContext context, int dataTs, int schedTs, AnomalyDataProcess subTask) throws Exception {
        int interval = subTask.getRunInterval();
        if (StringUtils.isNotBlank(MDC.get(REQ))) {
            String req = MDC.get(REQ);
            MDC.put(REQ_BAK, req);
            MDC.put(REQ, req + "-" + subTask.getProcessorName().toUpperCase());
        }
        List<AnomalyData> dataList = anomalyStoreDao.queryAnomalyDataByMetricPrefix(subTask.getMetricKey(),
            subTask.getMetricPrefix(), subTask.getLogstore(), dataTs - interval, dataTs, 0, 10000);
        logger.info("find {} anomaly records for metric {}", dataList.size(), subTask.getMetricPrefix());
        PostProcessor processor = dispatcher.getProcessorByName(subTask.getProcessorName());
        if (processor == null) {
            logger.warn("no processor for name {}, current processor list {}, over processor", subTask.getProcessorName(), dispatcher.getProcessorNames());
            return new ProcessResult(false, "no processor find");
        }
        if (executorService instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor tpe = (ThreadPoolExecutor)executorService;
            logger.info("thread pool stat, core size: {}, max size: {}, active size: {}, wait queue size: {}",
                tpe.getCorePoolSize(), tpe.getMaximumPoolSize(), tpe.getActiveCount(), tpe.getQueue().size());
        }
        Map<String, String> mdcContext = MDC.getCopyOfContextMap();
        executorService.execute(() -> {
            if (mdcContext != null && !mdcContext.isEmpty()) {
                MDC.setContextMap(mdcContext);
            }
            try {
                logger.info("execute processor in thread pool start, metric {}", subTask.getMetricPrefix());
                boolean result = processor.processor(subTask, dataTs, dataList);
                logger.info("execute processor in thread pool over, metric {}, run status {}",
                    subTask.getMetricPrefix(), result);
            } catch (Exception e) {
                logger.error("processor {} error", subTask.getMetricPrefix(), e);
            }
        });
        anomalyDataProcessDao.updateLastRunTime(subTask, dataTs * 1000L);
        logger.info("anomaly data processor job over,  metric {}", subTask.getMetricPrefix());
        if (StringUtils.isNotBlank(MDC.get(REQ)) && StringUtils.isNotBlank(MDC.get(REQ_BAK))) {
            String req = MDC.get(REQ_BAK);
            MDC.put(REQ, req);
            MDC.remove(REQ_BAK);
        }
        return new ProcessResult(true, "ok");
    }
}
