package com.aliyun.xdragon.biz.log.processor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import com.aliyun.xdragon.biz.log.model.JobErrorCode;
import com.aliyun.xdragon.biz.log.repository.GlobalVirtArchSourceDao;
import com.aliyun.xdragon.biz.log.repository.NcAssociationInfoDao;
import com.aliyun.xdragon.biz.log.repository.PatternDistributionDao;
import com.aliyun.xdragon.common.generate.log.model.GlobalVirtArchSource;
import com.aliyun.xdragon.common.generate.log.model.NcAssociationInfo;
import com.aliyun.xdragon.common.model.ExtInfo;
import freemarker.template.TemplateException;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/07/13
 */
@Deprecated
@Component
public class NcAssociationProcessor {
    private static Logger logger = LoggerFactory.getLogger(NcAssociationProcessor.class);
    private static Long TIME_INTERVAL_MS = 24L * 60L * 60L * 1000L;
    private static Integer MAX_IP_CNT_PER_BATCH = 1000;
    private static Integer MAX_LOOP_NUM = 1000;
    private static Integer MAX_UPDATE_CNT_PER_BATCH = 2000;
    private static long DATA_READY_MIN_CNT = 500000L;

    @Autowired
    private GlobalVirtArchSourceDao virtArchSourceDao;

    @Autowired
    private PatternDistributionDao distributionDao;

    @Autowired
    private NcAssociationInfoDao associationDao;

    public boolean isDataReady(Pair<Long, Long> timeRange) {
        long count = 0L;
        try {
            count = virtArchSourceDao.countSource(new Date(timeRange.getLeft()), new Date(timeRange.getRight() - 1000L));
        } catch (Exception e) {
            logger.error("Exception occurs when get virt source count! ", e);
            return false;
        }

        logger.info("Found {} records in source table.", count);
        return count >= DATA_READY_MIN_CNT;
    }

    public boolean isDataFullyProcessed(Pair<Long, Long> timeRange) {
        long count = 0L;
        try {
            count = associationDao.countAssociation(new Date(timeRange.getLeft()));
        } catch (Exception e) {
            logger.error("Exception occurs when get association info count! ", e);
            return false;
        }
        logger.info("Found {} records in association table.", count);
        return count >= DATA_READY_MIN_CNT;
    }

    public void cleanData(Pair<Long, Long> timeRange) {
        try {
            associationDao.deleteByTime(new Date(timeRange.getLeft()));
        } catch (Exception e) {
            logger.error("Exception occurs when delete old association info!", e);
        }
    }

    public JobErrorCode run(Pair<Long, Long> timeRange) throws TemplateException, IOException {
        Set<ExtInfo> ncSet = getNcSet(timeRange);
        if (ncSet.isEmpty()) {
            logger.error("Nc set is empty, there is no source data!");
            return JobErrorCode.Fail;
        }

        List<String> ipList = new ArrayList<>(MAX_IP_CNT_PER_BATCH);
        int successCnt = 0;
        for (ExtInfo nc : ncSet) {
            ipList.add(nc.getExt());
            if (ipList.size() >= MAX_IP_CNT_PER_BATCH) {
                // 获取架构信息
                List<GlobalVirtArchSource> associationList = joinAssociationInfo(timeRange, ipList);
                // 写入nc_association_info表，时间为timeRange.left
                if (uploadAssociationInfo(associationList)) {
                    ++successCnt;
                }
                ipList.clear();
            }
            if (successCnt >= MAX_LOOP_NUM) {
                break;
            }
        }
        if (!ipList.isEmpty()) {
            List<GlobalVirtArchSource> associationList = joinAssociationInfo(timeRange, ipList);
            if (uploadAssociationInfo(associationList)) {
                ++successCnt;
            }
        }

        logger.info("Success cnt is: {}", successCnt);
        if (successCnt == 0) {
            return JobErrorCode.Fail;
        }
        if (successCnt >= MAX_LOOP_NUM || successCnt == (ncSet.size() + MAX_IP_CNT_PER_BATCH - 1) / MAX_IP_CNT_PER_BATCH) {
            return JobErrorCode.Success;
        }
        return JobErrorCode.Partial_Success;
    }

    private Set<ExtInfo> getNcSet(Pair<Long, Long> timeRange) {
        Set<ExtInfo> ncSet = new TreeSet<>((o1, o2) -> {
            if (o1.getCnt().equals(o2.getCnt())) {
                return o1.hashCode() - o2.hashCode();
            }
            return o2.getCnt().compareTo(o1.getCnt());
        });

        Long startTime = timeRange.getLeft();
        Long endTime = startTime + TIME_INTERVAL_MS - 1000;
        while (endTime < timeRange.getRight()) {
            // 读取虚拟化的nc信息
            List<ExtInfo> ncList = distributionDao.getDistinctNcList(9L, new Date(startTime), new Date(endTime), true);
            startTime += TIME_INTERVAL_MS;
            endTime += TIME_INTERVAL_MS;
            ncSet.addAll(ncList);
        }
        if (startTime < timeRange.getRight()) {
            List<ExtInfo> ncList = distributionDao.getDistinctNcList(9L, new Date(startTime), new Date(timeRange.getRight() - 1000), true);
            ncSet.addAll(ncList);
        }
        logger.info("Got {} distinct ip from distribution table.", ncSet.size());
        return ncSet;
    }

    private List<GlobalVirtArchSource> joinAssociationInfo(Pair<Long, Long> timeRange, List<String> ipList) {
        return virtArchSourceDao.listSource(new Date(timeRange.getLeft()), new Date(timeRange.getRight() - 1000L), ipList);
    }

    private boolean uploadAssociationInfo(List<GlobalVirtArchSource> associationList) {
        int updateCnt = 0, totalCnt = associationList.size();
        int cnt = 0;
        List<NcAssociationInfo> associationBatch = new ArrayList<>(MAX_UPDATE_CNT_PER_BATCH);
        for (GlobalVirtArchSource associationInfo : associationList) {
            NcAssociationInfo info = new NcAssociationInfo();
            info.setTime(associationInfo.getTime());
            info.setNcip(associationInfo.getNcip());
            info.setVirtType(associationInfo.getVirtType());
            info.setVirtType2(associationInfo.getVirtType2());
            info.setAzone(associationInfo.getAzone());
            info.setRegion(associationInfo.getRegion());
            info.setClusterId(associationInfo.getClusterId());
            info.setNcMode(associationInfo.getNcMode());
            info.setInstanceFamily(associationInfo.getInstanceFamily());
            associationBatch.add(info);
            if (++cnt == MAX_UPDATE_CNT_PER_BATCH) {
                updateCnt += associationDao.batchInsertAssociationInfo(associationBatch);
                associationBatch.clear();
                cnt = 0;
            }
        }

        if (associationBatch.isEmpty()) {
            logger.info("Get empty associationBatch");
            return true;
        }

        updateCnt += associationDao.batchInsertAssociationInfo(associationBatch);
        logger.info("Update {} association infos, success cnt is {}.", totalCnt, updateCnt);
        return updateCnt == totalCnt;
    }
}
