package com.aliyun.xdragon.biz.clientops.service;

import com.aliyun.xdragon.api.service.clientops.ClientResourceService;
import com.aliyun.xdragon.biz.clientops.repository.ClientResourceDao;
import com.aliyun.xdragon.common.enumeration.ClientResourceStatus;
import com.aliyun.xdragon.common.enumeration.ClientResourceType;
import com.aliyun.xdragon.common.generate.model.ClientResource;
import com.aliyun.xdragon.common.model.bpms.BpmsProcessStatus;
import com.aliyun.xdragon.common.model.bpms.BpmsStartProcess;
import com.aliyun.xdragon.common.model.bpms.BpmsTerminateProcess;
import com.aliyun.xdragon.common.model.PageableInfo;
import com.aliyun.xdragon.common.model.clientops.request.AddClientResourceRequest;
import com.aliyun.xdragon.common.model.clientops.request.CheckClientResourceRequest;
import com.aliyun.xdragon.common.model.clientops.request.DelClientResourceRequest;
import com.aliyun.xdragon.common.model.clientops.request.ListEmpResourcesRequest;
import com.aliyun.xdragon.common.model.clientops.request.UpdateApproveStatusRequest;
import com.aliyun.xdragon.common.model.clientops.request.UpdateClientResourceRequest;
import com.aliyun.xdragon.common.model.XdragonMetricResponse;
import com.aliyun.xdragon.common.util.MDCUtil;
import com.aliyun.xdragon.service.common.agent.ApproveClient;
import com.aliyun.xdragon.service.common.agent.EcsInnerApiClient;
import com.aliyun.xdragon.service.common.agent.EdmClient;
import com.aliyun.xdragon.service.common.cache.RedisUtil;
import com.aliyuncs.ecsinc.model.v20160314.InnerEcsQueryByParamResponse;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/04/03
 */
@Service
@Validated
public class ClientResourceServiceImpl implements ClientResourceService {
    private static final Logger logger = LoggerFactory.getLogger(ClientResourceServiceImpl.class);
    @Autowired
    private ClientResourceDao resourceDao;

    @Autowired
    private EcsInnerApiClient ecsInnerApiClient;

    @Autowired
    private EdmClient edmClient;

    @Autowired
    private ApproveClient approveClient;

    @Autowired
    private RedisUtil redisUtil;

    private static final String BPMS_PROCESS_CODE = "xdragon_metric_client_resource_op";

    private final ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("client-source-pool-%d")
        .build();
    private final ExecutorService executorService = new ThreadPoolExecutor(4, 4, 0, TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(1024), threadFactory, new ThreadPoolExecutor.AbortPolicy());

    @Override
    public XdragonMetricResponse<PageableInfo<ClientResource>> listEmpResources(ListEmpResourcesRequest request) {
        long total = resourceDao.countResource(request.getEmpId(), request.getResourceType(), null,
            request.getApproveStatus());
        int startPos = (request.getPageNum() - 1) * request.getPageSize();
        List<ClientResource> crs = resourceDao.listEmpResources(request.getEmpId(), request.getResourceType(),
            request.getApproveStatus(), startPos, request.getPageSize());
        PageableInfo<ClientResource> pageableInfo = PageableInfo.of((int)total, crs);
        return XdragonMetricResponse.ofSuccess(pageableInfo);
    }

    @Override
    public XdragonMetricResponse<String> addResource(AddClientResourceRequest request) {
        long count = resourceDao.countByCondition(request.getEmpId(), request.getType().getType(), request.getResources(),
                Lists.newArrayList(
                        ClientResourceStatus.NOT_APPROVE.getStatus(),
                        ClientResourceStatus.APPROVING.getStatus(),
                        ClientResourceStatus.APPROVE_ACCEPT.getStatus(),
                        ClientResourceStatus.CLOUDDOC_NOTIFY_ACCEPT.getStatus()));
        if(count > 0) {
            return XdragonMetricResponse.ofSuccess("资源已存在");
        }
        Pair<Integer, Long> p = resourceDao.addResource(request.getEmpId(), request.getType().getType(),
            request.getResources(), request.getComment());
        // start approve
        if (p.getLeft() <= 0) {
            logger.error("add resource to db fail");
            return XdragonMetricResponse.ofFail("500", "add resource fail");
        }
        List<Integer> ids = resourceDao.getResourceIds(request.getEmpId(), request.getType().getType(),
            request.getResources(), ClientResourceStatus.NOT_APPROVE.getStatus(), p.getRight());
        Map<String, String> param = new HashMap<>();
        param.put("approve_type", "add resource");
        param.put("resource_type", request.getType().getType());
        String resourceList = String.join("\n", request.getResources());
        param.put("resource_list", resourceList);
        param.put("comment", request.getComment());
        BpmsStartProcess ret = approveClient.startProcess(BPMS_PROCESS_CODE, "用户可操作资源申请", request.getEmpId(),
            param);
        if (ret == null || !ret.getSuccess()) {
            logger.error("add resource, start approve failed, emp id {}, resource list {}", request.getEmpId(),
                request.getResources());
            resourceDao.updateApproveStatus(ids, ClientResourceStatus.APPROVE_ERROR.getStatus(), null);
            return XdragonMetricResponse.ofFail("500", "start approve fail");
        } else {
            resourceDao.updateApproveStatus(ids, ClientResourceStatus.APPROVING.getStatus(),
                ret.getContent().getProcessInstanceId());
            return XdragonMetricResponse.ofSuccess(ret.getContent().getProcessInstanceId());
        }
    }

    @Override
    public XdragonMetricResponse<String> updateResource(UpdateClientResourceRequest request) {
        ClientResource oldCr = resourceDao.getResourceWithId(request.getId());
        if (oldCr == null) {
            return XdragonMetricResponse.ofFail("400", "record not exist, id " + request.getId());
        }
        Map<String, String> logMdc = MDC.getCopyOfContextMap();
        executorService.execute(() -> {
            MDCUtil.safeSetMdc(logMdc);
            terminateProcess(Lists.newArrayList(Pair.of(oldCr.getBpmsId(), oldCr.getEmpId())));
        });
        ClientResource cr = new ClientResource();
        cr.setResources(request.getResource());
        cr.setComment(request.getComment());
        int cnt = resourceDao.updateResourceWithId(request.getId(), cr);
        if (cnt <= 0) {
            logger.error("modify resource in db fail");
            return XdragonMetricResponse.ofFail("500", "modify resource fail");
        }
        Map<String, String> param = new HashMap<>();
        param.put("approve_type", "modify resource");
        param.put("resource_type", oldCr.getResourceType());
        String rl = String.format("old resource: %s\nnew resource: %s", oldCr.getResources(), request.getResource());
        param.put("resource_list", rl);
        param.put("comment", request.getComment());
        BpmsStartProcess ret = approveClient.startProcess(BPMS_PROCESS_CODE, "用户可操作资源修改", oldCr.getEmpId(),
            param);
        if (ret == null || !ret.getSuccess()) {
            logger.error("modify resource, start approve failed, emp id {}, resource list {}", oldCr,
                oldCr.getResources());
            resourceDao.updateApproveStatus(Lists.newArrayList(oldCr.getId()),
                ClientResourceStatus.APPROVE_ERROR.getStatus(), null);
            return XdragonMetricResponse.ofFail("500", "start approve fail");
        } else {
            resourceDao.updateApproveStatus(Lists.newArrayList(oldCr.getId()),
                ClientResourceStatus.APPROVING.getStatus(), ret.getContent().getProcessInstanceId());
            return XdragonMetricResponse.ofSuccess(ret.getContent().getProcessInstanceId());
        }
    }

    @Override
    public XdragonMetricResponse<Boolean> deleteResource(DelClientResourceRequest request) {
        List<ClientResource> resources = resourceDao.getResourceWithIds(request.getIds());
        if (resources == null || resources.isEmpty()) {
            logger.info("no resource with id {}", request.getIds());
            return XdragonMetricResponse.ofSuccess(true);
        }
        List<Pair<String, String>> bpmsIds = new LinkedList<>();
        resources.forEach(r -> bpmsIds.add(Pair.of(r.getBpmsId(), r.getEmpId())));
        Map<String, String> logMdc = MDC.getCopyOfContextMap();
        executorService.execute(() -> {
            MDCUtil.safeSetMdc(logMdc);
            terminateProcess(bpmsIds);
        });
        int cnt = resourceDao.deleteResourceWithId(request.getIds());
        return XdragonMetricResponse.ofSuccess(cnt > 0);
    }

    @Override
    public XdragonMetricResponse<Pair<Boolean, String>> checkResource(CheckClientResourceRequest request) {
        return XdragonMetricResponse.ofSuccess(
            checkPermission(request.getEmpId(), request.getType().getType(), request.getResources()));
    }

    @Override
    public XdragonMetricResponse<Boolean> updateApproveStatus() {
        List<ClientResource> crs = resourceDao.getResourceByApproveStatus(ClientResourceStatus.APPROVING.getStatus());
        if (!crs.isEmpty()) {
            logger.info("{} record need update approve status", crs.size());
            crs.forEach(cs -> updateApproveStatus(cs.getId(), cs.getBpmsId()));
        } else {
            logger.info("no record need update approve status");
        }
        return XdragonMetricResponse.ofSuccess(true);
    }

    @Override
    public XdragonMetricResponse<Boolean> updateApproveStatus(UpdateApproveStatusRequest request) {
        updateApproveStatus(request.getId(), request.getBpmsId());
        return XdragonMetricResponse.ofSuccess(true);
    }

    @Scheduled(fixedRate = 60000L)
    public void updateApproveStatusCron() {
        try {
            String requestId = UUID.randomUUID().toString().toUpperCase();
            MDC.put("RequestID", requestId);
            logger.info("start update approve status in background");
            updateApproveStatus();
        } finally {
            MDC.remove("RequestID");
        }
    }

    private boolean terminateProcess(List<Pair<String, String>> bpmsIds) {
        boolean result = true;
        for (Pair<String, String> pid : bpmsIds) {
            BpmsProcessStatus status = approveClient.getProcessInstanceStatus(pid.getLeft());
            if (status == null || !status.getSuccess()) {
                logger.error("get process instance status failed, process instance id {}", pid.getLeft());
                continue;
            }
            // only running can be terminated
            if ("RUNNING".equals(status.getContent().getInstStatus())) {
                BpmsTerminateProcess ret = approveClient.terminateProcess(pid.getLeft(), pid.getRight());
                if (ret == null || !ret.getSuccess()) {
                    logger.error("terminate process failed, process instance id {}", pid.getLeft());
                    result = false;
                }
            }
        }
        return result;
    }

    private Pair<Boolean, String> checkPermission(String empId, String type, List<String> rcs) {
        for (String rc : rcs) {
            boolean checked = checkPermission(empId, type, rc);
            if (!checked) {
                return Pair.of(false, rc);
            }
        }
        return Pair.of(true, "");
    }

    private boolean checkPermission(String empId, String type, String instance) {
        String key = String.format("%s#%s#%s", empId, type, instance);
        // query cache
        Object cacheRet = redisUtil.get(key);
        // no cache or cache value is false, check again
        if (cacheRet == null || !((Boolean)cacheRet)) {
            boolean ret = false;
            if (type.equals(ClientResourceType.INSTANCE.getType())) {
                ret = checkInstancePermission(empId, instance);
                logger.info("check instance permission, emp id {}, type {}, instance {}, ret {}", empId, type, instance, ret);
            } else if (type.equals(ClientResourceType.UID.getType())) {
                ret = checkUidPermission(empId, instance);
                logger.info("check uid permission, emp id {}, type {}, uid {}, ret {}", empId, type, instance, ret);
            }
            // put cache
            redisUtil.set(key, ret, 600);
            return ret;
        } else {
            return true;
        }

    }

    private boolean checkInstancePermission(String empId, String instance) {
        // check instance
        long cnt = resourceDao.countResource(empId, Lists.newArrayList(ClientResourceType.INSTANCE.getType()), instance,
            Lists.newArrayList(ClientResourceStatus.APPROVE_ACCEPT.getStatus(), ClientResourceStatus.CLOUDDOC_NOTIFY_ACCEPT.getStatus()));
        if (cnt > 0) {
            logger.info("check instance permission, match instance, empid: {}, instance: {}",empId, instance);
            return true;
        }

        // check instance’s uid
        InnerEcsQueryByParamResponse.Item item = ecsInnerApiClient.queryVmDetail(instance);
        if (item != null) {
            Long aliUid = item.getAliUid();
            cnt = resourceDao.countResource(empId, Lists.newArrayList(ClientResourceType.UID.getType()),
                aliUid.toString(), Lists.newArrayList(ClientResourceStatus.APPROVE_ACCEPT.getStatus(), ClientResourceStatus.CLOUDDOC_NOTIFY_ACCEPT.getStatus()));
            if (cnt > 0) {
                logger.info("check instance permission, match uid, empid: {}, uid: {}",empId, aliUid);
                return true;
            }
        }

        // check instance's cid
        Long cid = edmClient.getVmCid(instance);
        if (cid != null) {
            cnt = resourceDao.countResource(empId, Lists.newArrayList(ClientResourceType.CID.getType()), cid.toString(),
                Lists.newArrayList(ClientResourceStatus.APPROVE_ACCEPT.getStatus(), ClientResourceStatus.CLOUDDOC_NOTIFY_ACCEPT.getStatus()));
            if (cnt > 0) {
                logger.info("check instance permission, match cid, empid: {}, cid: {}",empId, cid);
                return true;
            }
        }

        // check tags
        // TODO
        return false;
    }

    private boolean checkUidPermission(String empId, String uid) {
        long cnt = resourceDao.countResource(empId, Lists.newArrayList(ClientResourceType.UID.getType()), uid,
            Lists.newArrayList(ClientResourceStatus.APPROVE_ACCEPT.getStatus(), ClientResourceStatus.CLOUDDOC_NOTIFY_ACCEPT.getStatus()));
        if (cnt > 0) {
            logger.info("check uid permission, match uid, empid: {}, uid: {}",empId, uid);
            return true;
        }

        //check user's cid
        Long cid = edmClient.getUserCid(uid);
        if (cid != null) {
            cnt = resourceDao.countResource(empId, Lists.newArrayList(ClientResourceType.CID.getType()), cid.toString(),
                Lists.newArrayList(ClientResourceStatus.APPROVE_ACCEPT.getStatus(), ClientResourceStatus.CLOUDDOC_NOTIFY_ACCEPT.getStatus()));
            if (cnt > 0) {
                logger.info("check uid permission, match cid, empid: {}, cid: {}",empId, cid);
                return true;
            }
        }

        // check tags
        // TODO
        return false;
    }

    private void updateApproveStatus(int id, String bpmsId) {
        BpmsProcessStatus processStatus = approveClient.getProcessInstanceStatus(bpmsId);
        if (processStatus != null && processStatus.getSuccess()) {
            if ("ERROR".equals(processStatus.getContent().getInstStatus())) {
                logger.error("bpms process {} status is error", bpmsId);
                resourceDao.updateApproveStatus(Lists.newArrayList(id), ClientResourceStatus.APPROVE_ERROR.getStatus(),
                    null);
            } else if ("COMPLETED".equals(processStatus.getContent().getInstStatus())) {
                if ("同意".equals(processStatus.getContent().getOutResult())) {
                    logger.info("bpms process {} status is completed with agree", bpmsId);
                    resourceDao.updateApproveStatus(Lists.newArrayList(id),
                        ClientResourceStatus.APPROVE_ACCEPT.getStatus(), null);
                } else if ("拒绝".equals(processStatus.getContent().getOutResult())) {
                    logger.info("bpms process {} status is completed with reject", bpmsId);
                    resourceDao.updateApproveStatus(Lists.newArrayList(id),
                        ClientResourceStatus.APPROVE_REJECT.getStatus(), null);
                } else {
                    logger.error("bpms process {} status is completed with unknown outResult {}", bpmsId,
                        processStatus.getContent().getOutResult());
                }
            } else if ("RUNNING".equals(processStatus.getContent().getInstStatus())) {
                logger.info("bpms process {} status is running", bpmsId);
            } else if ("TERMINATED".equals(processStatus.getContent().getInstStatus())) {
                logger.info("bpms process {} status is terminated", bpmsId);
                resourceDao.updateApproveStatus(Lists.newArrayList(id), ClientResourceStatus.APPROVE_CANCEL.getStatus(),
                    null);
            } else {
                logger.error("bpms process {} status unknown {}", bpmsId, processStatus.getContent().getInstStatus());
            }
        } else {
            logger.error("query approve status fail, bpms id {}", bpmsId);
        }

    }

}
