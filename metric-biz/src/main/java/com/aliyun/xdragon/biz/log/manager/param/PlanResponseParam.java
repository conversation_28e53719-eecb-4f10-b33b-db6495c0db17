package com.aliyun.xdragon.biz.log.manager.param;

import com.aliyun.xdragon.biz.log.manager.enums.TaskManagerErrorCode;

/**
 * <AUTHOR>
 * @date 2022/07/19
 */
public class PlanResponseParam extends ResponseParam {
    private String planJson;

    public PlanResponseParam(String requestId) {
        super(requestId);
        planJson = "";
    }

    public PlanResponseParam(String requestId, String planJson) {
        super(requestId);
        this.planJson = planJson;
    }

    public PlanResponseParam(String requestId, TaskManagerErrorCode code, String planJson) {
        super(requestId, code);
        this.planJson = planJson;
    }

    public PlanResponseParam(String requestId, TaskManagerErrorCode code, String errorMessage, String planJson) {
        super(requestId, code, errorMessage);
        this.planJson = planJson;
    }

    public String getPlanJson() {
        return planJson;
    }

    public void setPlanJson(String planJson) {
        this.planJson = planJson;
    }
}
