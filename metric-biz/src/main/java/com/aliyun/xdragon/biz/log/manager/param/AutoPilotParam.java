package com.aliyun.xdragon.biz.log.manager.param;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/03/10
 */
@Data
public class AutoPilotParam implements Serializable {
    private static final long serialVersionUID = -466442729803831034L;

    /**
     * AUTOPILOT_MODE_MONITORING：关闭自动调优
     * AUTOPILOT_MODE_ACTIVE：开启调优的自适应模式
     * AUTOPILOT_MODE_TRAINING：开启调优的平稳模式
     */
    private String mode = "AUTOPILOT_MODE_ACTIVE";

    private String deploymentId;

    private Integer cooldownMin = 10;

    private LimitParam limitParam = new LimitParam();
    private ScaleUpParam scaleUpParam = new ScaleUpParam();
    private ScaleDownParam scaleDownParam = new ScaleDownParam();
    private AdvancedParam advancedParam = new AdvancedParam();

    public Map<String, String> genParamMap() {
        Map<String, String> m = new HashMap<>();
        // 启动后，自动调优的冷却时间
        m.put("cooldown.minutes", cooldownMin.toString());
        // ------------- limit -------------
        // 最大cpu
        m.put("resources.cpu.max", limitParam.getMaxCpu().toString());
        // 最大单内存
        m.put("resources.memory.max", limitParam.getMaxMemory() + "GiB");
        // 最大并发
        m.put("parallelism.scale.max", limitParam.getMaxParallelism().toString());
        // 最小并发
        m.put("parallelism.scale.min", limitParam.getMinParallelism().toString());

        // ------------------------- scale up -------------
        //开启延迟扩容
        if (scaleUpParam.getDelayScaleUpParam() != null) {
            m.put("delay-resolver.enabled", "true");
            // 最大容忍延迟,作业延时大于xx, 持续xx
            m.put("delay-detector.scale-up.threshold", scaleUpParam.getDelayScaleUpParam().getThreshold() + "min");
            m.put("delay-detector.scale-up.sample-interval",
                scaleUpParam.getDelayScaleUpParam().getIntervalMin() + "min");
        }

        // 开启slot使用率扩容
        if (scaleUpParam.getSlotUsageScaleUpParam() != null) {
            m.put("slot-usage.scale-up.enabled", "true");
            //作业单算子的平均 Busy xx% , 持续xx触发扩容
            m.put("slot-usage-detector.scale-up.threshold",
                scaleUpParam.getSlotUsageScaleUpParam().getThreshold().toString());
            m.put("slot-usage-detector.scale-up.sample-interval",
                scaleUpParam.getSlotUsageScaleUpParam().getIntervalMin() + "min");
        }

        // 开启内存扩容
        if (scaleUpParam.getMemoryUsageScaleUp() != null) {
            m.put("tm-memory-usage.scale-up.enabled", "true");
            //作业单 TM 的内存使用率大于, 触发扩容
            m.put("tm-memory-usage-detector.scale-up.threshold", scaleUpParam.getMemoryUsageScaleUp().toString());
        }

        //开启oom扩容
        if (scaleUpParam.getOomScaleUp() != null && scaleUpParam.getOomScaleUp()) {
            m.put("oom.scale-up.enabled", "true");
        }

        // 开启GC触发扩容
        if (scaleUpParam.getGcScaleUpParam() != null) {
            m.put("tm-gc-resolver.enabled", "true");
            //gc 时间大于xx, 持续时间触发缩容
            m.put("tm-gc-detector.gc-time-ms-per-second.threshold",
                String.valueOf(scaleUpParam.getGcScaleUpParam().getThreshold() * 1000));
            //作业 TM 或 JM 的每秒 GC 时长超过 xx,持续xx时间触发
            m.put("tm-gc-detector.sample-interval", scaleUpParam.getGcScaleUpParam().getIntervalMin() + "min");
        }

        // ------------------------- scale down -------------
        //开启使用率缩容
        if (scaleDownParam.getSlotUsageScaleDownParam() != null) {
            m.put("slot-usage.scale-down.enabled", "true");
            // 作业单算子的平均 Busy 率小于xx 持续 xx
            m.put("slot-usage-detector.scale-down.threshold",
                scaleDownParam.getSlotUsageScaleDownParam().getThreshold().toString());
            m.put("slot-usage-detector.scale-down.sample-interval",
                scaleDownParam.getSlotUsageScaleDownParam().getIntervalH() + "h");
        }

        // 开启内存使用率缩容
        if (scaleDownParam.getMemoryScaleDownParam() != null) {
            m.put("tm-memory-usage.scale-down.enabled", "true");
            // 作业单 TM 的内存使用率小于xx 持续xx
            m.put("tm-memory-usage-detector.scale-down.threshold",
                scaleDownParam.getMemoryScaleDownParam().getThreshold().toString());
            m.put("tm-memory-usage-detector.scale-down.sample-interval",
                scaleDownParam.getMemoryScaleDownParam().getIntervalH() + "h");
        }

        // --------------------------- advanced ------------
        // 高级配置
        if (advancedParam != null) {
            m.put("advanced.rules.enabled", "true");
            // 最小单tm 内存
            m.put("resources.memory-scale-down.min", advancedParam.getMinTMMemory() + "GiB");
            // 最大单tm 内存
            m.put("resources.memory-scale-up.max", advancedParam.getMaxTMMemory() + "GiB");
            // 并发扩容时间间隔
            m.put("parallelism.scale-up.interval", advancedParam.getParallelismUpIntervalMin() + "min");
            // 内存扩容时间间隔
            m.put("mem.scale-up.interval", advancedParam.getMemUpIntervalMin() + "min");
            // 并发缩容时间间隔
            m.put("parallelism.scale-down.interval", advancedParam.getParallelismDownIntervalH() + "h");
            // 内存缩容时间间隔
            m.put("mem.scale-down.interval", advancedParam.getMemDownIntervalH() + "h");
        }

        return m;
    }

    @Data
    public static class LimitParam implements Serializable {
        private static final long serialVersionUID = 1118034650444341681L;
        private Integer maxCpu = 100;
        private Integer maxParallelism = 64;
        private Integer minParallelism = 4;
        private Integer maxMemory = 400;
    }

    @Data
    public static class ScaleUpParam implements Serializable {
        private static final long serialVersionUID = -5191733922681280371L;
        Boolean oomScaleUp = true;
        Double memoryUsageScaleUp = 0.75;
        DelayScaleUpParam delayScaleUpParam = new DelayScaleUpParam();
        SlotUsageScaleUpParam slotUsageScaleUpParam = new SlotUsageScaleUpParam();
        GcScaleUpParam gcScaleUpParam = new GcScaleUpParam();
    }

    @Data
    public static class DelayScaleUpParam implements Serializable {
        private static final long serialVersionUID = -1251494181926859583L;
        private Integer threshold = 1;
        private Integer intervalMin = 3;
    }

    @Data
    public static class SlotUsageScaleUpParam implements Serializable {
        private static final long serialVersionUID = 4206511304160802661L;
        private Double threshold = 0.7;
        private Integer intervalMin = 5;
    }

    @Data
    public static class GcScaleUpParam implements Serializable {
        private static final long serialVersionUID = -76742199298617966L;
        private Double threshold = 0.2;
        private Integer intervalMin = 2;
    }

    @Data
    public static class ScaleDownParam implements Serializable {
        private static final long serialVersionUID = -2200373429500375689L;
        SlotUsageScaleDownParam slotUsageScaleDownParam = new SlotUsageScaleDownParam();
        MemoryScaleDownParam memoryScaleDownParam = new MemoryScaleDownParam();
    }

    @Data
    public static class SlotUsageScaleDownParam implements Serializable {
        private static final long serialVersionUID = -1251494181926859583L;
        private Double threshold = 0.15;
        private Integer intervalH = 6;
    }

    @Data
    public static class MemoryScaleDownParam implements Serializable {
        private static final long serialVersionUID = -1251494181926859582L;
        private Double threshold = 0.15;
        private Integer intervalH = 6;
    }

    @Data
    public static class AdvancedParam implements Serializable {
        private static final long serialVersionUID = 2448515621764383947L;
        private Integer minTMMemory = 4;
        private Integer maxTMMemory = 64;
        private Integer parallelismUpIntervalMin = 5;
        private Integer memUpIntervalMin = 5;
        private Integer parallelismDownIntervalH = 6;
        private Integer memDownIntervalH = 6;
    }
}
