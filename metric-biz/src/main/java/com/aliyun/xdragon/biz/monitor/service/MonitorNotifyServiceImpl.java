package com.aliyun.xdragon.biz.monitor.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import javax.annotation.PostConstruct;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.phoenix.api.common.response.CommonResultCode;
import com.aliyun.xdragon.api.service.monitor.EventCenterService;
import com.aliyun.xdragon.api.service.monitor.MonitorNotifyService;
import com.aliyun.xdragon.api.service.monitor.model.ExceptionNotifyResponse;
import com.aliyun.xdragon.api.service.monitor.model.HandlerInfo;
import com.aliyun.xdragon.api.service.monitor.model.MonitorInfo;
import com.aliyun.xdragon.api.service.monitor.model.NotifyInfo;
import com.aliyun.xdragon.api.service.monitor.model.OpsRuleInfo;
import com.aliyun.xdragon.service.common.service.DevOpsApiService;
import com.aliyun.xdragon.service.common.service.XdragonAlertService;
import com.aliyun.xdragon.service.common.util.DateUtil;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.ecsinc.model.v20160314.InnerEcsQueryByParamRequest;
import com.aliyuncs.ecsinc.model.v20160314.InnerEcsQueryByParamResponse;
import com.aliyuncs.ecsops.model.v20160401.OpsAddFeatureDataRequest;
import com.aliyuncs.ecsops.model.v20160401.OpsAddFeatureDataResponse;
import com.aliyuncs.ecsops.model.v20160401.OpsAddNcExceptionRequest;
import com.aliyuncs.ecsops.model.v20160401.OpsAddNcExceptionResponse;
import com.aliyuncs.ecsops.model.v20160401.OpsXdragonSendAlertRequest.Item;
import com.aliyuncs.ecsops.model.v20160401.OpsXdragonSendAlertResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
public class MonitorNotifyServiceImpl implements MonitorNotifyService {
    private static final Logger logger = LoggerFactory.getLogger(MonitorNotifyService.class);
    public static final String GLOBAL_REGION = "ecs-inner-global-center";

    private static final int PUSH_DINGJIE_RESOURCE_ID_BATCH_SIZE = 10;
    private static final int ALERT_RESOURCE_ID_BATCH_SIZE = 20;

    @Value("${cloud.ops.regionId}")
    private String regionId;

    @Value("${cloud.ops.region.hz}")
    private String oxsRegion;

    @Value("${cloud.ops.domain}")
    private String domain;

    @Value("${cloud.ops.accessKey}")
    private String accessKey;

    @Value("${cloud.ops.accessSecret}")
    private String accessSecret;

    @Value("${cloud.ecs.inner.domain}")
    private String ecsIncDomain;

    @Autowired
    private XdragonAlertService alertService;

    @Autowired
    private EventCenterService eventCenterService;

    public IAcsClient client;

    @Override
    public ExceptionNotifyResponse consumeSlsMonitor(MonitorInfo monitorInfo) throws ClientException {
        try{
            logger.info("MonitorNotifyService consumeSlsMonitor monitorInfo:{}", JSONObject.toJSONString(monitorInfo));
            sendNotify(monitorInfo);
            submitOps(monitorInfo);
            submitFeature(monitorInfo);
            return ExceptionNotifyResponse.ofSuccess();
        }catch (Exception e){
            logger.error("MonitorNotifyService consumeSlsMonitor error",e);
            return ExceptionNotifyResponse.ofFail(CommonResultCode.EXCEPTION.code, e.getMessage());
        }
    }

    /**
     * 通知信息发送
     * */
    private void sendNotify(MonitorInfo monitorInfo)throws ClientException{
        NotifyInfo notifyInfo = monitorInfo.getNotify();
//        List<String> monitorLabels = monitorInfo.getMonitorLabels();
        //需要向定界发送异常, 不再使用，依赖通知中心直接写 SLS
//        if(!CollectionUtils.isEmpty(monitorLabels) && monitorLabels.contains("dingjie")){
//            pushDingjie(monitorInfo);
//        }
        //同步消息到通知中心
        List<String> alarmChannels = notifyInfo.getAlarmChannel();
        if(!CollectionUtils.isEmpty(alarmChannels)){
            pushXdragonAlert(monitorInfo);
        }
    }

    private void submitOps(MonitorInfo monitorInfo) throws ClassCastException{
        if(monitorInfo.getHandler() ==null){
            logger.info("MonitorNotifyService not need submitOps, businessId:{}, resourceId:{}", monitorInfo.getBusinessId(), monitorInfo.getResourceId());
            return ;
        }
        String resourceType = monitorInfo.getResourceType();
        if(StringUtils.isEmpty(resourceType) || "vm".equals(resourceType.toLowerCase())){
            logger.info("MonitorNotifyService vm not submit ops, businessId:{}, resourceId:{}", monitorInfo.getBusinessId(),monitorInfo.getResourceId());
            return;
        }
        try{
            HandlerInfo handlerInfo = monitorInfo.getHandler();
            OpsRuleInfo opsRuleInfo = handlerInfo.getOpsRuleInfo();
            if(opsRuleInfo!=null && !StringUtils.isEmpty(opsRuleInfo.getOpsCode())){
                JSONObject msgObj = new JSONObject();
                msgObj.put("bizEventId", UUID.randomUUID().toString());
                msgObj.put("resourceType", "nc");
                msgObj.put("resourceId", monitorInfo.getResourceId());
                msgObj.put("opsCode", "EcsMaintenance");
                msgObj.put("impact", "Alert");
                JSONObject extensions = new JSONObject();
                HashMap<String, String> extra = opsRuleInfo.getExtraData();
                if(extra != null){
                    extensions.putAll(extra);
                }

                extensions.put("nc", monitorInfo.getResourceId());
                extensions.put("code", opsRuleInfo.getOpsCode());
                extensions.put("reason",opsRuleInfo.getReason());
                extensions.put("comment",opsRuleInfo.getComment());
                msgObj.put("extensions", extensions);
                String result = eventCenterService.publishEvent("cloudops.resources.maintenance", "xdragon/metric", msgObj);
                logger.info("MonitorNotifyService submitOps success, businessId:{}, resourceId:{}, result:{}",monitorInfo.getBusinessId(),monitorInfo.getResourceId(),result);
            }
        }catch (Exception e){
            logger.error("MonitorNotifyService submitOps exception, businessId:{},resourceId:{}",monitorInfo.getBusinessId(),monitorInfo.getResourceId(),e);
        }
    }

    private void submitFeature(MonitorInfo monitorInfo){
        List<String> monitorLabels = monitorInfo.getMonitorLabels();
        if(CollectionUtils.isEmpty(monitorLabels) || !monitorLabels.contains("feature")){
            logger.info("MonitorNotifyService not need submitFeature businessId:{}, monitorLabels:{}", monitorInfo.getBusinessId(), monitorLabels);
            return;
        }
        OpsAddFeatureDataRequest opsAddFeatureDataRequest = new OpsAddFeatureDataRequest();
        List<OpsAddFeatureDataRequest.FeatureDataParam> featureDataParamList = new ArrayList<>();
        OpsAddFeatureDataRequest.FeatureDataParam featureDataParam = new OpsAddFeatureDataRequest.FeatureDataParam();
        featureDataParam.setTargetId(monitorInfo.getResourceId());
        featureDataParam.setFeatureName(monitorInfo.getExceptionName());
        featureDataParam.setMatchedTimestamp(monitorInfo.getExceptionTime().getTime());
        featureDataParam.setTargetRegionId(monitorInfo.getRegionNo());
        featureDataParamList.add(featureDataParam);
        opsAddFeatureDataRequest.setFeatureDataParams(featureDataParamList);
        opsAddFeatureDataRequest.setRegionNo(monitorInfo.getRegionNo());
        opsAddFeatureDataRequest.setModifiedUser("xdragon-metric");
        try{
            OpsAddFeatureDataResponse response = client.getAcsResponse(opsAddFeatureDataRequest);
            logger.info("MonitorNotifyService OpsAddFeatureDataRequest success, businessId:{}, targetId:{},requestId:{}, request:{}",monitorInfo.getBusinessId(), monitorInfo.getResourceId(),response.getRequestId(),JSONObject.toJSONString(response));
        }catch(Exception e){
            logger.error("MonitorNotifyService OpsAddFeatureDataRequest exception, businessId:{}, targetId:{},exceptionName:{}, request:{}",monitorInfo.getBusinessId(), monitorInfo.getResourceId(),monitorInfo.getExceptionName(),JSONObject.toJSONString(opsAddFeatureDataRequest),e);
        }
    }

    private List<String> pushDingjie(MonitorInfo monitorInfo) {
        List<String> resourceIds = Arrays.asList(monitorInfo.getResourceId());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(monitorInfo.getExt())) {
            resourceIds = Arrays.asList(monitorInfo.getExt().split(","));
        }

        List<String> result = new ArrayList<>(PUSH_DINGJIE_RESOURCE_ID_BATCH_SIZE);
        String type = monitorInfo.getResourceType();
        String reason = monitorInfo.getExceptionDesc();
        String ncIps = monitorInfo.getNcIp();
        // 防止数据量太大，这里分批次推送
        if (PUSH_DINGJIE_RESOURCE_ID_BATCH_SIZE < resourceIds.size()) {
            List<List<String>> batchInstanceIds = Lists.partition(resourceIds, PUSH_DINGJIE_RESOURCE_ID_BATCH_SIZE);
            batchInstanceIds.parallelStream().forEach(instanceIds -> {
                String token = pushDingjie(instanceIds, type, reason, ncIps);
                if (Objects.nonNull(token)) {
                    result.add(token);
                }
            });
        } else {
            String token = pushDingjie(resourceIds, type, reason, ncIps);
            if (Objects.nonNull(token)) {
                result.add(token);
            }
        }
        return result;
    }

    private String pushDingjie(List<String> resourceIdList, String type, String reason, String ncIps) {
        OpsAddNcExceptionRequest opsAddNcExceptionRequest = new OpsAddNcExceptionRequest();
        opsAddNcExceptionRequest.setReason(reason);
        String ids = JSON.toJSONString(resourceIdList);
        if ("nc".equals(type)) {
            opsAddNcExceptionRequest.setExceptionName("nc_exception_push_dingjie");
            opsAddNcExceptionRequest.setWarningKey("ncCnt");
            opsAddNcExceptionRequest.setWarningValue("1");
            opsAddNcExceptionRequest.setNcIps(ids);
        } else {
            opsAddNcExceptionRequest.setExceptionName("vm_exception_push_dingjie");
            opsAddNcExceptionRequest.setWarningKey("vmCnt");
            opsAddNcExceptionRequest.setWarningValue("1");
            opsAddNcExceptionRequest.setMachineId(ids);
            if (StringUtils.isEmpty(ncIps)) {
                batchQueryVmInfo(resourceIdList);
            } else {
                opsAddNcExceptionRequest.setNcIps(ncIps);
            }
        }
        try {
            logger.info("MonitorNotifyService pushDingjie with request: {}", JSON.toJSONString(opsAddNcExceptionRequest));
            OpsAddNcExceptionResponse response = client.getAcsResponse(opsAddNcExceptionRequest);
            String token = response.getToken();
            if (!StringUtils.isEmpty(token)) {
                logger.info("MonitorNotifyService exception push dingjie success, resourceIds: {}, token: {}", ids, token);
                return token;
            }
            logger.error("MonitorNotifyService exception push dingjie error, resourceIds: {}, response: {}",
                ids, JSON.toJSONString(response));
            return null;
        } catch (ClientException e) {
            logger.error("MonitorNotifyService exception push dingjie got error, resourceIds: {}", ids, e);
            return null;
        }
    }

    public static Map<String, String> getAlertParamMap(MonitorInfo monitorInfo) {
        Map<String, String> kv = new HashMap<>();

        String alertTitle = StringUtils.isEmpty(monitorInfo.getAlertName())? monitorInfo.getExceptionDesc() : monitorInfo.getAlertName();
        kv.put("alertTitle", alertTitle+"("+monitorInfo.getExceptionName()+")");
        kv.put("exceptionName",monitorInfo.getExceptionName());
        kv.put("exceptionDesc", monitorInfo.getExceptionDesc());
        kv.put("exceptionTime", DateUtil.dateFormat(monitorInfo.getExceptionTime(),DateUtil.DATE_FORMAT));
        kv.put("resourceCnt", monitorInfo.getResourceCnt()==null? "1" : monitorInfo.getResourceCnt().toString());
        kv.put("resourceList", StringUtils.isEmpty(monitorInfo.getResourceList())? monitorInfo.getResourceId() : monitorInfo.getResourceList());
        kv.put("resourceType", monitorInfo.getResourceType().toUpperCase());
        kv.put("exceptionComponent", monitorInfo.getExceptionComponent());
        kv.put("clusterAlias", monitorInfo.getClusterAlias());
        kv.put("region", monitorInfo.getRegionNo());
        kv.put("ext", monitorInfo.getExt());
        kv.put("warningLevel", monitorInfo.getLevel().name().toLowerCase());
        kv.put("additionalInfo", monitorInfo.getAdditionalInfo());
        kv.put("reason", monitorInfo.getReason());

        return kv;
    }

    private void pushXdragonAlert(MonitorInfo monitorInfo) throws ClientException {
        NotifyInfo notifyInfo = monitorInfo.getNotify();
        String resourceId = monitorInfo.getResourceId();
        if(notifyInfo == null){
            logger.info("MonitorNotifyService not need pushXdragonAlert, businessId:{}, resourceId:{}", monitorInfo.getBusinessId(), resourceId);
            return;
        }
        List<String> alarmChannel = notifyInfo.getAlarmChannel();
        if(CollectionUtils.isEmpty(alarmChannel) || alarmChannel.size()==0){
            logger.info("MonitorNotifyService not need pushXdragonAlert, businessId:{}, resourceId:{}",monitorInfo.getBusinessId(), resourceId);
            return;
        }

        List<String> routerKeys;
        if (!StringUtils.isEmpty(monitorInfo.getXamMonitorLabel())) {
            routerKeys = Collections.singletonList(monitorInfo.getXamMonitorLabel());
        } else {
            routerKeys = new LinkedList<>();
            routerKeys.add("cloudops_multiple");
            // 需要有一个独立的 label，否则同 resourceId 不同的监控会在通知被静默
            routerKeys.add(String.format("%s-%s-%s", "cloudops_multiple", monitorInfo.getExceptionName(), monitorInfo.getAlertName()));
        }
        List<Item> itemList = new LinkedList<>();
        Item item = new Item();
        item.setResourceType(monitorInfo.getResourceType().toLowerCase());
        item.setResourceId(resourceId);

        // basic alert params
        Map<String, String> kv = getAlertParamMap(monitorInfo);

        if (routerKeys.size() == 1) {
            kv.put("monitorLabel", routerKeys.get(0));
        }

        if (!StringUtils.isEmpty(monitorInfo.getTemplateName())) {
            kv.put("templateName", monitorInfo.getTemplateName());
        } else {
            kv.put("templateName", "multiple_cloudops");
        }

        if (!StringUtils.isEmpty(monitorInfo.getConstructDataClass())) {
            kv.put("constructData", monitorInfo.getConstructDataClass());
        }

        kv.put("alarmChannel", JSONObject.toJSONString(alarmChannel));
        if(alarmChannel.contains("DingGroup")
                && !CollectionUtils.isEmpty(notifyInfo.getDingToken())
                && !CollectionUtils.isEmpty(notifyInfo.getDingReceiver())){
            kv.put("dingToken", notifyInfo.getDingToken().get(0));
            kv.put("dingUser", JSONObject.toJSONString(notifyInfo.getDingReceiver()));
        }
        if(alarmChannel.contains("DingTalk")
                && !CollectionUtils.isEmpty(notifyInfo.getDingReceiver())){
            kv.put("dingUser", JSONObject.toJSONString(notifyInfo.getDingReceiver()));
        }
        if(alarmChannel.contains("Phone") && !CollectionUtils.isEmpty(notifyInfo.getPhoneReceiver())){
            kv.put("phoneUser", JSONObject.toJSONString(notifyInfo.getPhoneReceiver()));
        }
        item.setParamsStr(DevOpsApiService.paramToJson(kv));
        itemList.add(item);
        logger.info("MonitorNotifyService sending alert, param: {}", JSON.toJSONString(itemList));
        OpsXdragonSendAlertResponse resp = alertService.sendAlert(routerKeys, StringUtils.isEmpty(monitorInfo.getFromSource()) ? "cloudops" : monitorInfo.getFromSource(), itemList, null, null);
        resp.getSendResponses().forEach(sr -> {
            if (!sr.getSuccessed()) {
                logger.error("MonitorNotifyService pushTongzhi error,businessId:{},params:{},result:{}", monitorInfo.getBusinessId(),JSONObject.toJSONString(itemList),JSONObject.toJSONString(sr));
            }
        });

        logger.info("MonitorNotifyService pushTongzhi success,businessId:{}, params:{},result:{}", monitorInfo.getBusinessId(), JSONObject.toJSONString(itemList),JSONObject.toJSONString(resp));
    }

    private OpsAddNcExceptionResponse getPushDingjieRes(String token) throws ClientException {
        OpsAddNcExceptionRequest opsAddNcExceptionRequest = new OpsAddNcExceptionRequest();
        opsAddNcExceptionRequest.setToken(token);
        OpsAddNcExceptionResponse response = client.getAcsResponse(opsAddNcExceptionRequest);
        logger.info("MonitorNotifyService getPushDingjieRes token:{},status:{},finished:{},error:{}",token,response.getStatus(),response.getFinished(),response.getErrorInfo());
        return response;
    }

    private List<InnerEcsQueryByParamResponse.Item> batchQueryVmInfo(List<String> instanceIdList) {
        InnerEcsQueryByParamRequest request = new InnerEcsQueryByParamRequest();
        request.setSysRegionId("ecs-inner-global-center");
        request.setPageNo(1);
        request.setPageSize(instanceIdList.size());

        String instanceIds = JSON.toJSONString(instanceIdList);
        request.setInstanceIds(instanceIds);
        try {
            InnerEcsQueryByParamResponse response = client.getAcsResponse(request);
            List<InnerEcsQueryByParamResponse.Item> items = response.getData();
            logger.info("MonitorNotifyService queryVmInfo, vmName:{}, response:{}", instanceIds, JSONObject.toJSONString(items));
            if (CollectionUtils.isEmpty(items)) {
                logger.warn("MonitorNotifyService queryVmInfo is empty, vmName:{}", instanceIds);
            }
            return items;
        } catch (Exception e) {
            logger.error("MonitorNotifyService queryVmInfo error, vmName:{}", instanceIds, e);
            return Collections.emptyList();
        }
    }

    @PostConstruct
    public void initAcsClient() throws ClientException {
        if (Objects.nonNull(client)) {
            return;
        }
        DefaultProfile.addEndpoint(regionId, "Ecsops", domain);
        DefaultProfile.addEndpoint(GLOBAL_REGION, "EcsInc", ecsIncDomain);

        DefaultProfile profile = DefaultProfile.getProfile(oxsRegion, accessKey, accessSecret);
        client = new DefaultAcsClient(profile);
    }
}
