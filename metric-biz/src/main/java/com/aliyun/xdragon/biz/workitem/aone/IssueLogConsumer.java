package com.aliyun.xdragon.biz.workitem.aone;

import javax.annotation.Resource;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.loghub.client.ClientWorker;
import com.aliyun.openservices.loghub.client.config.LogHubConfig;
import com.aliyun.xdragon.common.exception.SlsClientException;
import com.aliyun.xdragon.service.common.config.aone.AoneConstants;
import com.aliyun.xdragon.service.common.config.diamond.ConfigService;
import com.aliyun.xdragon.service.common.config.sls.NbSlsClientFactoryService;
import com.aliyun.xdragon.service.common.util.IpAddressHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 创建SLS日志消费对象，并启动消费进程
 *
 * <AUTHOR>
 */
@Component
public class IssueLogConsumer {

    private static final Logger logger = LoggerFactory.getLogger(IssueLogConsumer.class);

    /**
     * 消费组名称
     */
    @Value("${aone.sls.consumerGroup:consumerGroupX}")
    private String consumerGroup;

    @Value("${aone.sls.logstore}")
    private String aoneLogstore;

    /**
     * newbie的sls日志对象
     */
    @Resource
    private NbSlsClientFactoryService slsClientFactory;

    @Autowired
    private IssueProcessorFactory issueProcessorFactory;

    @Autowired
    private ConfigService configService;

    private ClientWorker worker = null;

    /**
     * 创建sls的消费线程
     *
     * @throws SlsClientException
     */
    public void create(String group, Integer startTimestamp) throws SlsClientException {

        try {
            LogHubConfig config = this.getLogHubConfig(group, startTimestamp);
            worker = new ClientWorker(issueProcessorFactory, config);
            Thread thread = new Thread(worker);
            thread.setName("sls log consumer");
            thread.setDaemon(true);
            //Thread运行之后，ClientWorker会自动运行，ClientWorker扩展了Runnable接口。
            thread.start();
        } catch (Exception e) {
            logger.error("start sls log consumer: ", e);
            throw new SlsClientException(e);
        }
    }

    public void shutdown(String group) {
        if (worker != null) {
            worker.shutdown();
            try {
                Thread.sleep(30 * 1000);
            } catch (InterruptedException e) {
                logger.warn("sleep error, ignore", e);
            } finally {
                worker = null;
            }
        }
        if (StringUtils.isNotBlank(group)) {
            try {
                deleteConsumerGroup(group);
            } catch (Exception e) {
                logger.warn("delete consumer group {} fail", group, e);
            }
        }
    }

    public void deleteConsumerGroup(String group)
        throws LogException {
        Client client = slsClientFactory.getClient("cn-hangzhou-corp");
        client.DeleteConsumerGroup(AoneConstants.DEFAULT_XUNJIAN_PROJECT, aoneLogstore, group);
    }

    private LogHubConfig getLogHubConfig(String group, Integer startTimestamp) {

        String ak = slsClientFactory.getAk();
        String sk = slsClientFactory.getSk();

        // consumer_1是消费者名称，同一个消费组下面的消费者名称必须不同，不同的消费者名称在多台机器上启动多个进程，来均衡消费一个Logstore，此时消费者名称可以使用机器IP地址来区分。
        // maxFetchLogGroupSize用于设置每次从服务端获取的LogGroup最大数目，使用默认值即可。您可以使用config.setMaxFetchLogGroupSize(100);调整，请注意取值范围为
        // (0,1000]。
        String consumer = "aone_" + IpAddressHelper.getInetAddress();
        // 允许外部参数指定group，测试时需要
        if (StringUtils.isBlank(group)) {
            group = consumerGroup;
        }
        if (startTimestamp == null) {
            return new LogHubConfig(group, consumer, configService.loadRegionEndpoint().get("cn-hangzhou-corp"),
                AoneConstants.DEFAULT_XUNJIAN_PROJECT, aoneLogstore, ak, sk, LogHubConfig.ConsumePosition.BEGIN_CURSOR,
                1000);
        } else {
            // 指定消费时间
            return new LogHubConfig(group, consumer, configService.loadRegionEndpoint().get("cn-hangzhou-corp"),
                AoneConstants.DEFAULT_XUNJIAN_PROJECT, aoneLogstore, ak, sk, startTimestamp,
                1000);
        }
    }
}
