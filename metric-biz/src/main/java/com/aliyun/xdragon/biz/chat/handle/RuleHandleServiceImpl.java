package com.aliyun.xdragon.biz.chat.handle;

import com.aliyun.xdragon.api.service.chat.model.AgentAnswerResponse;
import com.aliyun.xdragon.biz.chat.repository.ChatOssDao;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service("ruleHandleService")
public class RuleHandleServiceImpl extends ChatSceneBasicHandleServiceImpl{
    private static final Logger logger = LoggerFactory.getLogger(RuleHandleServiceImpl.class);

    private static final Pattern EXTRACT_PATTERN = Pattern.compile("^~·？·~(.+)~·？·~");

    private static final String RULE_PATH = "cipu";

    private static final String RULE_FILENAME = "moc_instruction.json";

    private static final String DEFAULT_VIEW_CONTENT = "未查询到相关库内容";

    private static final Gson GSON = new Gson();

    @Autowired
    private ChatOssDao ossDao;

    private AtomicReference<Map<String, String>> ruleContentMapHashMap = new AtomicReference<>(new HashMap());
    private AtomicReference<String> tag = new AtomicReference<>("");

    @Override
    protected String getViewContent(AgentAnswerResponse response, Integer source) throws Exception {
        return getResContent(response.getAsynToken());
    }

    @Override
    protected String getContent(AgentAnswerResponse response) throws Exception {
        return getResContent(response.getAsynToken());
    }

    @Override
    protected String getUrl(AgentAnswerResponse response, Integer source) {
        return null;
    }

    @Override
    protected String getLinks(AgentAnswerResponse response) {
        return null;
    }

    @Override
    protected String getAones(AgentAnswerResponse response) {
        return null;
    }

    @Override
    protected boolean aiAgentResCheck(AgentAnswerResponse response) throws Exception {
        return true;
    }

    private String getResContent(String query) {
        loadRuleContent();
        Matcher matcher = EXTRACT_PATTERN.matcher(query);
        if (matcher.find()) {
            return ruleContentMapHashMap.get().getOrDefault(matcher.group(1), DEFAULT_VIEW_CONTENT);
        }
        return DEFAULT_VIEW_CONTENT;
    }

    private void loadRuleContent() {
        String fileName = String.format("%s/%s", RULE_PATH, RULE_FILENAME);

        if (!ossDao.fileExist(fileName)) {
            logger.error("[RuleHandleServiceImpl] Rule content file {} not exist!", fileName);
            return;
        }

        String eTag = ossDao.getFileETagUnsafe(fileName);
        if (eTag == null) {
            logger.error("[RuleHandleServiceImpl] Rule content file {} eTag is null!", fileName);
            return;
        }
        if (eTag.equals(this.tag.get())) {
            logger.info("[RuleHandleServiceImpl] Rule content file {} is not changed!", fileName);
            return;
        }

        BufferedReader reader = new BufferedReader(new InputStreamReader(ossDao.getFileInputStreamUnsafe(fileName)));
        this.tag.set(eTag);
        Map<String, String> ruleMap = GSON.fromJson(reader, HashMap.class);
        this.ruleContentMapHashMap.set(ruleMap);
    }
}
