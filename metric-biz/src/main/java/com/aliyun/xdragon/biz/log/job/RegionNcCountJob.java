package com.aliyun.xdragon.biz.log.job;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.http.client.ClientConfiguration;
import com.aliyun.xdragon.biz.log.config.MetricsConfig;
import com.aliyun.xdragon.biz.log.enumeration.LogNcFilterType;
import com.aliyun.xdragon.biz.log.repository.NcFullInfoDao;
import com.aliyun.xdragon.biz.log.repository.RegionHostCountDao;
import com.aliyun.xdragon.biz.log.service.MetricsPickupService;
import com.aliyun.xdragon.biz.log.service.PickUpConfigService;
import com.aliyun.xdragon.common.generate.model.NcFullInfo;
import com.aliyun.xdragon.common.generate.model.RegionHostCount;
import com.aliyun.xdragon.common.model.SlsProjectGroup;
import com.aliyun.xdragon.common.model.SlsRegionInfo;
import com.aliyun.xdragon.common.model.SlsRegionProject;
import com.aliyun.xdragon.service.common.BizConsts;
import com.aliyun.xdragon.service.common.cache.RedisUtil;
import com.aliyun.xdragon.service.common.config.diamond.ConfigService;
import com.aliyun.xdragon.service.common.job.AbstractMapTask;
import com.aliyun.xdragon.service.common.util.DateUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/12/07
 */
@Component
@Lazy
public class RegionNcCountJob extends AbstractMapTask<SlsRegionInfo> {
    private static final Logger logger = LoggerFactory.getLogger(MetricsPickUpJob.class);

    private static final Gson GSON = new Gson();

    private static int HTTP_CONNECT_MAX_COUNT = 1000;
    private static int HTTP_CONNECT_TIME_OUT = 10000;
    private static int HTTP_SEND_TIME_OUT = 60000;
    @Autowired
    PickUpConfigService pickUpConfigService;

    @Autowired
    ConfigService configService;

    @Autowired
    MetricsPickupService metricsPickupService;

    @Autowired
    NcFullInfoDao ncFullInfoDao;

    @Autowired
    RegionHostCountDao regionHostCountDao;

    @Autowired
    RedisUtil redisUtil;


    @Override
    public List<SlsRegionInfo> genTask(JobContext context, int dataTs) {
        List<MetricsConfig> configs = new LinkedList<>();
        List<String> runModes = getRunModes(context);
        if (runModes.contains(BizConsts.LOG_COV_COMMON)) {
            configs.addAll(pickUpConfigService.loadMetricsConfigCommon());
        }
        if (runModes.contains(BizConsts.LOG_COV_COMMON_PERIOD)) {
            configs.addAll(pickUpConfigService.loadMetricsConfigCommonPeriod());
        }

        if (configs.isEmpty()) {
            return Collections.emptyList();
        }

        List<SlsProjectGroup> groups = pickUpConfigService.loadProjectGroupInfo();
        Map<String, SlsProjectGroup> name2group = new HashMap<>();
        groups.forEach(g -> name2group.put(g.getProject(), g));
        Map<String, List<SlsRegionInfo>> region2projects = new HashMap<>();
        for (MetricsConfig config : configs) {
            String project = config.getProject();
            String logstore = config.getLogStore();
            Integer interval = config.getInterval();
            String region = config.getRegion();
            SlsProjectGroup group = name2group.getOrDefault(project, null);
            if (group == null) {
                logger.error("miss sls project group config for {}", project);
                continue;
            }
            //具体的region project详情
            List<SlsRegionProject> regionProjects = group.getRegions();
            String defaultRegion = group.getDefaultRegion();
            boolean isAllDefault = checkDefault(regionProjects, defaultRegion);
            List<String> notInDefaultRegion = getNotDefaultRegion(regionProjects, defaultRegion);
            List<SlsRegionProject> filterRegionProjects = new LinkedList<>();
            if (StringUtils.isNotBlank(region)) {
                for (SlsRegionProject rp : regionProjects) {
                    if (region.equals(rp.getRegion())) {
                        filterRegionProjects.add(rp);
                    }
                }
            } else {
                filterRegionProjects = regionProjects;
            }
            filterRegionProjects.forEach(r -> {
                if (!region2projects.containsKey(r.getRegion())) {
                    region2projects.put(r.getRegion(), new LinkedList<>());
                }
                SlsRegionInfo info = SlsRegionInfo.of(r);
                info.setLogstore(logstore);
                info.setGroup(config.getProject());
                info.setInterval(interval);
                info.setAdditional(config.getAdditional());
                info.setNcKey(config.getNcKey());
                info.setNcFilterType(config.getNcFilterType());
                info.setQueryType(config.getQueryType());
                info.getAdditional().put("defaultRegion", defaultRegion);
                info.getAdditional().put("isAllDefault", isAllDefault);
                info.getAdditional().put("notInDefaultRegion", notInDefaultRegion);
                region2projects.get(r.getRegion()).add(info);
            });
        }

        List<SlsRegionInfo> ret = new LinkedList<>();
        region2projects.values().forEach(ret::addAll);
        Map<Integer, List<String>> ts2Ips = getOfflineTs();
        logCacheInfo(ts2Ips);
        String cacheKey = getCacheKey(context);
        Ts2Ips ips = new Ts2Ips();
        ips.setIps(ts2Ips);
        redisUtil.set(cacheKey, ips, BizConsts.SECONDS_OF_THREE_HOURS);
        return ret;
    }

    /**
     * SlsRegionInfo with same region
     */
    @Override
    public ProcessResult processSubTask(JobContext context, int dataTs, int schedTs, SlsRegionInfo subTask)
        throws Exception {
        Ts2Ips ips = (Ts2Ips)redisUtil.get(getCacheKey(context));
        if (ips != null && ips.getIps() != null) {
            logCacheInfo(ips.getIps());
        }
        Map<Integer, List<String>> ts2Ips;
        if (ips == null || ips.getIps() == null) {
            ts2Ips = getOfflineTs();
        } else {
            ts2Ips = ips.getIps();
        }
        RegionHostCount regionHostCount = processSingle(dataTs, subTask, ts2Ips);
        if (regionHostCount == null) {
            return new ProcessResult(false);
        } else {
            return new ProcessResult(true);
        }
    }

    private RegionHostCount processSingle(int runTs, SlsRegionInfo info, Map<Integer, List<String>> ts2ips) {
        logger.info("start count nc for region: {}, project: {}, logstore: {}, count type {}", info.getRegion(),
            info.getRealProject(), info.getLogstore(), info.getNcFilterType());
        Set<String> ips = null;
        if (LogNcFilterType.XDRAGON.getType().equals(info.getNcFilterType())) {
            ips = getNcByType(info);
        } else {
            ips = getNcByCnt(runTs, info);
        }
        if (ips != null) {
            int range = getQueryRangeByInterval(info.getInterval());
            // 获得range时间段内被offline的ip
            if (!ips.isEmpty()) {
                Set<String> offlineIps = getOfflineIps(ts2ips, runTs, range);
                if (!offlineIps.isEmpty()) {
                    ips.removeAll(offlineIps);
                }
            }
            logger.info("get {} nc for region {}, project {}, logstore {}", ips.size(), info.getRegion(),
                info.getRealProject(), info.getLogstore());
            RegionHostCount regionHostCount = new RegionHostCount();
            regionHostCount.setProjectGroup(info.getGroup());
            regionHostCount.setProject(info.getRealProject());
            regionHostCount.setLogstore(info.getLogstore());
            regionHostCount.setRegion(info.getRegion());
            regionHostCount.setCount(ips.size());
            regionHostCountDao.addOrUpdate(regionHostCount);
            return regionHostCount;
        } else {
            return null;
        }
    }

    // if all logs in one region
    private boolean checkDefault(List<SlsRegionProject> regionProjects, String defaultRegion) {
        if (StringUtils.isBlank(defaultRegion)) {
            return false;
        }
        boolean ret = true;
        for (SlsRegionProject r : regionProjects) {
            if (!defaultRegion.equals(r.getRegion())) {
                ret = false;
                break;
            }
        }
        return ret;
    }

    private List<String> getNotDefaultRegion(List<SlsRegionProject> regionProjects, String defaultRegion) {
        List<String> ret = new LinkedList<>();
        if (defaultRegion == null) {
            return ret;
        }
        for (SlsRegionProject r : regionProjects) {
            if (!defaultRegion.equals(r.getRegion())) {
                ret.add(r.getRegion());
            }
        }
        return ret;
    }

    private Set<String> getNcByType(SlsRegionInfo info) {
        String defaultRegion = (String)(info.getAdditional().get("defaultRegion"));
        boolean isAllDefault = (boolean)(info.getAdditional().getOrDefault("isAllDefault", false));
        List<String> notInDefaultRegion = (List<String>)(info.getAdditional().getOrDefault("notInDefaultRegion",
            new LinkedList<>()));
        if (!info.getRegion().equals(defaultRegion)) {
            // 不是默认地域
            return metricsPickupService.getFilterNc(info.getNcFilterType(), info.getQueryType(), info.getRegion());
        } else {
            // 是默认地域
            if (isAllDefault) {
                // 中心存储， 从所有的nc里过滤
                return metricsPickupService.getFilterNc(info.getNcFilterType(), info.getQueryType(), null);
            } else {
                // 非中心存储，这里存储剩下地域的日志
                return metricsPickupService.getFilterNcExcludeRegions(info.getNcFilterType(), info.getQueryType(),
                    notInDefaultRegion);
            }
        }
    }

    private Set<String> getNcByCnt(int runTs, SlsRegionInfo info) {
        ClientConfiguration clientConfig = new ClientConfiguration();
        clientConfig.setMaxConnections(HTTP_CONNECT_MAX_COUNT);
        clientConfig.setConnectionTimeout(HTTP_CONNECT_TIME_OUT);
        clientConfig.setSocketTimeout(HTTP_SEND_TIME_OUT);
        Client client = configService.getSlsClient(info.getUser(), info.getRegion(), null, clientConfig);
        if (client == null) {
            logger.error("no sls client config for user {}, region {}", info.getUser(), info.getRegion());
            return null;
        }
        int range = getQueryRangeByInterval(info.getInterval());
        int step = (int)info.getAdditional().getOrDefault("step", 3600 * 6);
        return getLogIps(client, info.getRealProject(), info.getLogstore(), runTs, range, step, info.getNcKey());
    }

    private String getCacheKey(JobContext context) {
        return String.format("nc_count_job_%d", context.getJobInstanceId());
    }

    private Map<Integer, List<String>> getOfflineTs() {
        List<NcFullInfo> ncs = ncFullInfoDao.getOfflineNcs(null);
        Map<Integer, List<String>> ts2Ips = new HashMap<>();
        for (NcFullInfo nc : ncs) {
            try {
                int ts = (int)DateUtil.datetime2Timestamp(nc.getDs(), "yyyyMMdd", false);
                if (!ts2Ips.containsKey(ts)) {
                    ts2Ips.put(ts, new LinkedList<>());
                }
                ts2Ips.get(ts).add(nc.getIp());
            } catch (Exception e) {
                logger.warn("parse ts fail, ds {}, ignore", nc.getDs());
            }
        }
        return ts2Ips;
    }

    private Set<String> getOfflineIps(Map<Integer, List<String>> ts2Ips, int runTs, int range) {
        long startMs = DateUtil.getStartMsOfDay((long)(runTs - range), false);
        Set<String> ips = new HashSet<>();
        for (Entry<Integer, List<String>> e : ts2Ips.entrySet()) {
            if (e.getKey() * 1000L >= startMs) {
                ips.addAll(e.getValue());
            }
        }
        return ips;
    }

    private Set<String> getLogIps(Client client, String project, String logstore, int runTs, int range, int step,
        String ncKey) {
        return metricsPickupService.getLogIps(client, project, logstore, runTs - range, runTs, step, ncKey);
    }

    private Integer getQueryRangeByInterval(Integer interval) {
        Integer ret;
        if (interval == null || interval == 0) {
            ret = BizConsts.SECONDS_OF_HALF_MONTH;
        } else {
            ret = interval * 15;
        }
        if (ret < BizConsts.SECONDS_OF_HOUR) {
            ret = BizConsts.SECONDS_OF_HOUR;
        }
        return ret;
    }

    private void logCacheInfo(Map<Integer, List<String>> ts2Ips) {
        int ips = 0;
        for (List<String> l : ts2Ips.values()) {
            ips += l.size();
        }
        logger.info("cache {} ts, {} ips", ts2Ips.size(), ips);
    }

    private List<String> getRunModes(JobContext context) {
        String param = context.getJobParameters();
        if (StringUtils.isBlank(param)) {
            return Collections.emptyList();
        } else {
            Map<String, Object> m = GSON.fromJson(param, new TypeToken<Map<String, Object>>() {}.getType());
            if (!m.containsKey(BizConsts.RUM_MODE_KEY)) {
                return Collections.emptyList();
            } else {
                return (List<String>)m.get(BizConsts.RUM_MODE_KEY);
            }
        }
    }

    @Data
    public static class Ts2Ips {
        private Map<Integer, List<String>> ips;
    }
}
