package com.aliyun.xdragon.biz.evaluate.repository;

import java.util.List;

import com.aliyun.xdragon.common.enumeration.EvalInstanceStatus;
import com.aliyun.xdragon.common.generate.model.EvaluateInstance;
import com.aliyun.xdragon.common.generate.model.EvaluateInstanceExample;
import com.aliyun.xdragon.common.generate.model.map.EvaluateInstanceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022/04/11
 */
@Repository
public class EvalInstanceDao {
    @Autowired
    private EvaluateInstanceMapper mapper;

    public EvaluateInstance queryById(Long id) {
        return mapper.selectByPrimaryKey(id);
    }

    private EvaluateInstanceExample buildQueryExample(Long taskId, Long startMs, Long endMs, List<Byte> runStatus) {
        EvaluateInstanceExample example = new EvaluateInstanceExample();
        EvaluateInstanceExample.Criteria c = example.createCriteria();
        c.andTaskIdEqualTo(taskId);
        if (startMs != null) {
            c.andTaskTimeGreaterThanOrEqualTo(startMs);
        }
        if (endMs != null) {
            c.andTaskTimeLessThanOrEqualTo(endMs);
        }
        if (runStatus != null && runStatus.size() > 0) {
            c.andRunStatusIn(runStatus);
        }
        return example;
    }

    public List<EvaluateInstance> queryInstance(Long taskId, Long batchId, String metricName) {
        EvaluateInstanceExample example = new EvaluateInstanceExample();
        EvaluateInstanceExample.Criteria c = example.createCriteria();
        c.andTaskIdEqualTo(taskId);
        c.andBatchIdEqualTo(batchId);
        c.andMetricNameEqualTo(metricName);
        return mapper.selectByExample(example);
    }

    public List<EvaluateInstance> queryByTask(Long taskId, Long startMs, Long endMs, Integer start, Integer size,
        List<Byte> runStatus) {
        EvaluateInstanceExample example = buildQueryExample(taskId, startMs, endMs, runStatus);
        example.setOrderByClause(String.format("id desc limit %d, %d", start, size));
        return mapper.selectByExample(example);
    }

    public Long countByTask(Long taskId, Long startMs, Long endMs, List<Byte> runStatus) {
        EvaluateInstanceExample example = buildQueryExample(taskId, startMs, endMs, runStatus);
        return mapper.countByExample(example);
    }

    public void updateStatusById(Long runId, EvalInstanceStatus status) {
        EvaluateInstance instance = new EvaluateInstance();
        instance.setId(runId);
        instance.setRunStatus(status.getStatus().byteValue());
        mapper.updateByPrimaryKeySelective(instance);
    }

    public void updateStatusById(Long taskId, Long taskMs, Long batchId, EvalInstanceStatus status) {
        EvaluateInstance instance = new EvaluateInstance();
        instance.setRunStatus(status.getStatus().byteValue());
        EvaluateInstanceExample example = new EvaluateInstanceExample();
        EvaluateInstanceExample.Criteria criteria = example.createCriteria();
        criteria.andTaskIdEqualTo(taskId);
        if (taskMs != null) {
            criteria.andTaskTimeEqualTo(taskMs);
        }
        if (batchId != null) {
            criteria.andBatchIdEqualTo(batchId);
        }

        mapper.updateByExampleSelective(instance, example);
    }

    public void addRunResult(EvaluateInstance instance) {
        mapper.insertSelective(instance);
    }

    public void updateRunResult(EvaluateInstance instance) {
        mapper.updateByPrimaryKeySelective(instance);
    }

    public void discard(Long taskId, Long taskTsMs, Long batchId) {
        EvaluateInstanceExample example = new EvaluateInstanceExample();
        EvaluateInstanceExample.Criteria criteria = example.createCriteria();
        criteria.andTaskIdEqualTo(taskId);
        criteria.andTaskTimeEqualTo(taskTsMs);
        criteria.andBatchIdLessThan(batchId);
        EvaluateInstance instance = new EvaluateInstance();
        instance.setRunStatus(EvalInstanceStatus.DISCARD.getStatus().byteValue());
        mapper.updateByExampleSelective(instance, example);
    }

}
