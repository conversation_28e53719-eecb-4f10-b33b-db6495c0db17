package com.aliyun.xdragon.biz.log.repository;

import com.aliyun.xdragon.common.enumeration.PatternTagSet;
import com.aliyun.xdragon.common.enumeration.log.PatternFilterMode;
import com.aliyun.xdragon.common.generate.log.model.LogPatternTag;
import com.aliyun.xdragon.common.generate.log.model.LogPatternTagExample;
import com.aliyun.xdragon.common.generate.log.model.map.LogPatternTagCustomMapper;
import com.aliyun.xdragon.common.generate.log.model.map.LogPatternTagMapper;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.aliyun.xdragon.common.enumeration.log.PatternFilterMode.EXCLUDE;
import static com.aliyun.xdragon.service.common.util.Checks.nullOrEmpty;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

/**
 * <AUTHOR>
 * @date 2022/08/09
 */
@Repository
public class PatternTagDao {
    @Autowired
    private LogPatternTagMapper mapper;

    @Autowired
    private LogPatternTagCustomMapper customMapper;

    public List<Long> getToAlarmTaskIds() {
        LogPatternTagExample e = new LogPatternTagExample();
        LogPatternTagExample.Criteria c = e.createCriteria();
        c.andAlarmDetailIsNotNull();
        c.andAlarmDetailNotEqualTo("");
        c.andFlagEqualTo(0);
        c.andIgnoreEqualTo(0);
        List<LogPatternTag> tags = mapper.selectByExample(e);
        return tags.isEmpty() ? Collections.emptyList() : tags.stream().map(LogPatternTag::getTaskId).collect(toList());
    }

    public LogPatternTag getLogTagByMd5(Long taskId, String md5) {
        List<LogPatternTag> tags = getLogTagsByMd5(taskId, Lists.newArrayList(md5));
        return tags.isEmpty() ? null : tags.get(0);
    }

    public List<LogPatternTag> getLogTagsByMd5(Long taskId, List<String> md5s) {
        LogPatternTagExample e = new LogPatternTagExample();
        LogPatternTagExample.Criteria c = e.createCriteria();
        c.andTaskIdEqualTo(taskId);
        if (!md5s.isEmpty()) {
            c.andMd5In(md5s);
        }
        return mapper.selectByExample(e);
    }

    public List<LogPatternTag> getToAlarmTagsByTaskId(Long taskId) {
        LogPatternTagExample e = new LogPatternTagExample();
        LogPatternTagExample.Criteria c = e.createCriteria();
        c.andTaskIdEqualTo(taskId);
        c.andAlarmDetailIsNotNull();
        c.andAlarmDetailNotEqualTo("");
        c.andFlagEqualTo(0);
        c.andIgnoreEqualTo(0);
        return mapper.selectByExample(e);
    }

    public LogPatternTag addLogTag(Long taskId, String md5, Long hashcode, Integer alarm, Integer follow, Integer ignore,
        String alarmReason, String followReason, String ignoreReason, String pattern) {
        LogPatternTag tag = new LogPatternTag();
        if (alarm != null) {
            tag.setAlarm(alarm);
        }
        if (follow != null) {
            tag.setFollow(follow);
        }
        if (ignore != null) {
            tag.setIgnore(ignore);
        }
        if (alarmReason != null) {
            tag.setAlarmReason(alarmReason);
        }
        if (followReason != null) {
            tag.setFollowReason(followReason);
        }
        if (ignoreReason != null) {
            tag.setIgnoreReason(ignoreReason);
        }
        if (pattern != null) {
            tag.setPattern(pattern);
        }
        if (StringUtils.isNotBlank(md5)) {
            tag.setMd5(md5);
        }
        tag.setTaskId(taskId);
        tag.setHashcode(hashcode);
        addLogTag(tag);
        return tag;
    }

    public void addLogTag(LogPatternTag tag) {
        mapper.insertSelective(tag);
    }

    public void updateTag(LogPatternTag tag, Long taskId, String md5) {
        LogPatternTagExample e = new LogPatternTagExample();
        LogPatternTagExample.Criteria c = e.createCriteria();
        c.andTaskIdEqualTo(taskId);
        c.andMd5EqualTo(md5);
        tag.setTagId(null);
        mapper.updateByExampleSelective(tag, e);
    }

    public int batchAddLogTags(List<LogPatternTag> tags) {
        return mapper.batchInsert(tags);
    }

    public int batchUpdateTags(LogPatternTag tag, Long taskId, List<String> md5s) {
        if (nullOrEmpty(md5s) || taskId == null) {
            return 0;
        }
        LogPatternTagExample e = new LogPatternTagExample();
        LogPatternTagExample.Criteria c = e.createCriteria();
        c.andTaskIdEqualTo(taskId);
        c.andMd5In(md5s);
        return mapper.updateByExampleSelective(tag, e);
    }

    public Map<String, String> queryPatternMd5WithIgnoreTag(Long taskId, PatternTagSet tag, String patternFilter,
        PatternFilterMode filterMode) {
        LogPatternTagExample e = new LogPatternTagExample();
        LogPatternTagExample.Criteria c = e.createCriteria();
        c.andTaskIdEqualTo(taskId);
        c.andIgnoreEqualTo(tag.getTag());
        if (StringUtils.isNotBlank(patternFilter)) {
            if (EXCLUDE.equals(filterMode)) {
                c.andPatternNotLike("%" + patternFilter + "%");
            } else {
                c.andPatternLike("%" + patternFilter + "%");
            }
        }
        List<LogPatternTag> tags = mapper.selectByExample(e);
        return patternTagListToMd5Map(tags);
    }

    public Map<Long, Map<String, String>> queryPatternMd5WithIgnoreTag(List<Long> taskIds, PatternTagSet tag,
        String patternFilter, PatternFilterMode filterMode) {
        LogPatternTagExample e = new LogPatternTagExample();
        LogPatternTagExample.Criteria c = e.createCriteria();
        c.andTaskIdIn(taskIds);
        c.andIgnoreEqualTo(tag.getTag());
        if (StringUtils.isNotBlank(patternFilter)) {
            if (EXCLUDE.equals(filterMode)) {
                c.andPatternNotLike("%" + patternFilter + "%");
            } else {
                c.andPatternLike("%" + patternFilter + "%");
            }
        }
        List<LogPatternTag> tags = mapper.selectByExample(e);
        return patternTagListToTaskIdMap(tags);
    }

    public Map<String, String> queryPatternMd5WithFollowTag(Long taskId, PatternTagSet tag, String patternFilter,
        PatternFilterMode filterMode) {
        LogPatternTagExample e = new LogPatternTagExample();
        LogPatternTagExample.Criteria c = e.createCriteria();
        c.andTaskIdEqualTo(taskId);
        c.andFollowEqualTo(tag.getTag());
        if (StringUtils.isNotBlank(patternFilter)) {
            if (EXCLUDE.equals(filterMode)) {
                c.andPatternNotLike("%" + patternFilter + "%");
            } else {
                c.andPatternLike("%" + patternFilter + "%");
            }
        }
        List<LogPatternTag> tags = mapper.selectByExample(e);
        return patternTagListToMd5Map(tags);
    }

    public Map<Long, Map<String, String>> queryPatternMd5WithFollowTag(List<Long> taskIds, PatternTagSet tag,
        String patternFilter, PatternFilterMode filterMode) {
        LogPatternTagExample e = new LogPatternTagExample();
        LogPatternTagExample.Criteria c = e.createCriteria();
        c.andTaskIdIn(taskIds);
        c.andFollowEqualTo(tag.getTag());
        if (StringUtils.isNotBlank(patternFilter)) {
            if (EXCLUDE.equals(filterMode)) {
                c.andPatternNotLike("%" + patternFilter + "%");
            } else {
                c.andPatternLike("%" + patternFilter + "%");
            }
        }
        List<LogPatternTag> tags = mapper.selectByExample(e);
        return patternTagListToTaskIdMap(tags);
    }

    public Map<String, String> queryPatternMd5WithAlarmTag(Long taskId, PatternTagSet tag, String patternFilter,
        PatternFilterMode filterMode) {
        LogPatternTagExample e = new LogPatternTagExample();
        LogPatternTagExample.Criteria c = e.createCriteria();
        c.andTaskIdEqualTo(taskId);
        c.andAlarmEqualTo(tag.getTag());
        if (StringUtils.isNotBlank(patternFilter)) {
            if (EXCLUDE.equals(filterMode)) {
                c.andPatternNotLike("%" + patternFilter + "%");
            } else {
                c.andPatternLike("%" + patternFilter + "%");
            }
        }
        List<LogPatternTag> tags = mapper.selectByExample(e);
        return patternTagListToMd5Map(tags);
    }

    public Map<Long, Map<String, String>> queryPatternMd5WithAlarmTag(List<Long> taskIds, PatternTagSet tag,
        String patternFilter, PatternFilterMode filterMode) {
        LogPatternTagExample e = new LogPatternTagExample();
        LogPatternTagExample.Criteria c = e.createCriteria();
        c.andTaskIdIn(taskIds);
        c.andAlarmEqualTo(tag.getTag());
        if (StringUtils.isNotBlank(patternFilter)) {
            if (EXCLUDE.equals(filterMode)) {
                c.andPatternNotLike("%" + patternFilter + "%");
            } else {
                c.andPatternLike("%" + patternFilter + "%");
            }
        }
        List<LogPatternTag> tags = mapper.selectByExample(e);
        return patternTagListToTaskIdMap(tags);
    }

    public List<LogPatternTag> listTag(Long taskId, List<String> md5s) {
        LogPatternTagExample e = new LogPatternTagExample();
        LogPatternTagExample.Criteria c = e.createCriteria();
        c.andTaskIdEqualTo(taskId);
        if (md5s.size() > 0) {
            c.andMd5In(md5s);
        }
        return mapper.selectByExample(e);
    }

    public List<LogPatternTag> listTag(Collection<String> idMd5s) {
        return customMapper.listTag(idMd5s);
    }

    public Map<Long, Set<String>> getIgnoreMd5s(Set<Long> taskSet) {
        LogPatternTagExample e = new LogPatternTagExample();
        LogPatternTagExample.Criteria criteria = e.createCriteria();
        criteria.andIgnoreEqualTo(1);
        if (!nullOrEmpty(taskSet)) {
            criteria.andTaskIdIn(new ArrayList<>(taskSet));
        }
        List<LogPatternTag> tags = mapper.selectByExample(e);
        return tags.stream().collect(
            groupingBy(LogPatternTag::getTaskId, mapping(LogPatternTag::getMd5, toSet())));
    }

    private Map<String, String> patternTagListToMd5Map(List<LogPatternTag> tags) {
        Map<String, String> ret = new HashMap<>();
        tags.forEach(t -> ret.put(t.getMd5(), t.getPattern()));
        return ret;
    }

    private Map<Long, Map<String, String>> patternTagListToTaskIdMap(List<LogPatternTag> tags) {
        Map<Long, Map<String, String>> ret = new HashMap<>();
        tags.forEach(t -> {
            Long taskId = t.getTaskId();
            if (!ret.containsKey(taskId)) {
                ret.put(taskId, new HashMap<>());
            }
            ret.get(taskId).put(t.getMd5(), t.getPattern());
        });
        return ret;
    }
}
