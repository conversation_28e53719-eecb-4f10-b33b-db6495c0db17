package com.aliyun.xdragon.biz.log.manager.param;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/07/06
 */
@Data
public class SqlFilterParam {
    private static final String CONTENT_MARKER_PREFIX = "[C]";

    private List<String> filterIncludeList;
    private List<String> filterExcludeList;
    private List<String> contentFilterList;

    public SqlFilterParam(String raw) {
        if (StringUtils.isBlank(raw)) {
            return;
        }

        filterIncludeList = new ArrayList<>();
        filterExcludeList = new ArrayList<>();
        contentFilterList = new ArrayList<>();
        String[] markers = raw.split(",");
        for (String marker : markers) {
            marker = marker.trim();
            if (marker.startsWith(CONTENT_MARKER_PREFIX)) {
                contentFilterList.add(marker.substring(CONTENT_MARKER_PREFIX.length()));
            } else if (marker.charAt(0) == '!') {
                filterExcludeList.add(marker.substring(1));
            } else {
                filterIncludeList.add(marker);
            }
        }
    }
}
