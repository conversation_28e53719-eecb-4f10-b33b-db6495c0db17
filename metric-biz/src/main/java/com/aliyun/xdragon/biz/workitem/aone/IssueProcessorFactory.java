package com.aliyun.xdragon.biz.workitem.aone;

import com.aliyun.openservices.loghub.client.interfaces.ILogHubProcessor;
import com.aliyun.openservices.loghub.client.interfaces.ILogHubProcessorFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class IssueProcessorFactory implements ILogHubProcessorFactory {

    @Autowired
    private AoneLogHubProcessor aoneLogHubProcessor;

    @Override
    public ILogHubProcessor generatorProcessor() {
        // 生成一个消费实例。
        return aoneLogHubProcessor;
    }
}