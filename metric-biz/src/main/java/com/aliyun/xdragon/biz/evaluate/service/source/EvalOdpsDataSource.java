package com.aliyun.xdragon.biz.evaluate.service.source;

import java.text.SimpleDateFormat;
import java.util.*;

import com.aliyun.odps.OdpsException;
import com.aliyun.odps.data.Record;
import com.aliyun.xdragon.common.enumeration.DataSourceCheckResult;
import com.aliyun.xdragon.common.exception.OdpsClientException;
import com.aliyun.xdragon.common.generate.model.EvaluateMetricRawMonitor;
import com.aliyun.xdragon.common.generate.model.EvaluateMonitorPercentile;
import com.aliyun.xdragon.common.model.param.*;
import com.aliyun.xdragon.service.common.agent.OdpsClient;
import com.aliyun.xdragon.common.enumeration.NameFetchType;
import com.aliyun.xdragon.common.enumeration.TimeShiftUnit;
import com.aliyun.xdragon.common.exception.EvalDataSourceException;
import com.aliyun.xdragon.common.exception.EvalServerException;
import com.aliyun.xdragon.common.generate.model.EvaluateInstance;
import com.aliyun.xdragon.common.generate.model.EvaluateTaskMeta;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import static java.util.Map.Entry.comparingByValue;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 * @date 2022/04/12
 * from
 * [
 * {
 * "table":"ecs_dw.xxxx",
 * "filter":"col=aaa and col2=bbb",
 * "metrics":"col4,col5,col6",
 * "order":"ts",
 * "order_by":"desc",
 * "name_type": "static",
 * "name": "0x1",
 * "partition":"ds=yyyymmdd",
 * "cross_partition": true,
 * "partition_number": 3,
 * },
 * {
 * "table":"ecs_dw.xxxx",
 * "filter":"col=aaa and col2=bbb",
 * "metrics":"col4,col5,col6",
 * "order":"ts",
 * "order_by":"desc",
 * "name_type": "dynamic",
 * "name": "action",
 * "partition":"ds=yyyymmdd",
 * "cross_partition": true,
 * "partition_number": 3,
 * }
 * ]
 *
 * to
 * [
 * {
 * "metric": "unavailable",
 * "data":[
 * {
 * "name": "action1",
 * "value": [1, 2, 3, 4]
 * },
 * {
 * "name": "action2",
 * "value": [1, 2, 3, 4]
 * }
 * ]
 * },
 * {
 * "metric": "performance",
 * "data":[
 * {
 * "name": "action1",
 * "value": [1, 2, 3, 4]
 * },
 * {
 * "name": "action2",
 * "value": [1, 2, 3, 4]
 * }
 * ]
 * }
 * ]
 */
@Component
public class EvalOdpsDataSource extends AbstractEvalDataSource {
    private static final Logger logger = LoggerFactory.getLogger(EvalOdpsDataSource.class);

    private final int MAX_QUERY = 5;
    private final int MAX_LIMIT = 10000;
    @Autowired
    private OdpsClient odpsClient;

    @Override
    public List<EvaluateInstance> buildRunInstance(EvaluateTaskMeta meta, Long taskTsMs, Long batchId) {
        String param = meta.getParam();
        OdpsTaskParam odpsTaskParam = gson.fromJson(param, OdpsTaskParam.class);

        List<EvaluateInstance> instances = new LinkedList<>();
        for (String metric: odpsTaskParam.getDataSet().get(0).getMetrics()) {
            String mm = metric.trim();
            if (metric.contains(" as ")) {
                mm = metric.substring(metric.indexOf(" as ") + 4).trim();
            }
            instances.add(createInstanceByMetric(mm, meta.getTaskId(), taskTsMs, batchId));
        }
        return instances;
    }

    @Override
    public List<EvaluateRequest> getData(EvaluateTaskMeta meta, Long endTsMs) {
        String param = meta.getParam();
        OdpsTaskParam odpsTaskParam = gson.fromJson(param, OdpsTaskParam.class);
        if (endTsMs == null) {
            endTsMs = System.currentTimeMillis();
        }
        Map<String, Map<String, List<Double>>> transit = new HashMap<>();
        for (OdpsDataSet dataSet : odpsTaskParam.getDataSet()) {
            //Map<metric, Map<action, valueList>>
            Map<String, Map<String, List<Double>>> subTransit = queryByDataSet(dataSet, endTsMs);
            transit = merge(transit, subTransit);
        }

        return parse(transit, odpsTaskParam.getLevel());
    }

    /**
     * 获取metric详情，返回的类中包含原始数据的详情以及各个异常所占的百分比
     * @param endTsMs 定时时间
     * @param odpsTaskParam odps配置类
     * @return EvaluateMetricDetail
     */
    public EvaluateMetricDetail getMetricDetail(Long endTsMs, OdpsTaskParam odpsTaskParam, Long taskId, Long batchId) {
        if (endTsMs == null) {
            endTsMs = System.currentTimeMillis();
        }
        Map<String, Map<String, List<Record>>> rawMonitors = new HashMap<>();
        Map<String, String> metricMatch = odpsTaskParam.getDetailConfig().getMetricMatch().getMetricMatchMap();
        for (OdpsDataSet dataSet : odpsTaskParam.getDataSet()) {
            Map<String, Map<String, List<Record>>> subRawMonitors = queryMetricDetail(dataSet, endTsMs, metricMatch);
            rawMonitors = merge(rawMonitors, subRawMonitors);
        }
        Map<String, Map<String, Map<String, List<Double>>>> monitorPercentiles = getTopPercentileException(rawMonitors);
        EvaluateMetricDetail evaluateMetricDetail = new EvaluateMetricDetail();
        List<EvaluateMetricRawMonitor> rawMonitorList = new ArrayList<>();
        List<EvaluateMonitorPercentile> monitorPercentileList = new ArrayList<>();
        for (String metricName: rawMonitors.keySet()) {
            Map<String, List<Record>> actionAndRecords = rawMonitors.get(metricName);
            for (String actionName: actionAndRecords.keySet()) {
                List<Record> records = actionAndRecords.get(actionName);
                for (Record record:records) {
                    EvaluateMetricRawMonitor monitor = new EvaluateMetricRawMonitor();
                    monitor.setBatchId(batchId);
                    monitor.setTaskId(taskId);
                    monitor.setMetricName(metricName);
                    monitor.setActionName(actionName);
                    monitor.setInstanceId(record.getString("instance_id"));
                    monitor.setExceptionName(record.getString("exception_name"));
                    monitor.setExceptionTimeList(record.getString("exception_time_list"));
                    monitor.setDuration(Double.parseDouble(record.getString("duration")));
                    monitor.setMonitorType(record.getString("monitor_type"));
                    monitor.setMonitorLevel(record.getString("monitor_level"));
                    monitor.setAliUid(record.getString("ali_uid"));
                    monitor.setNcIp(record.getString("nc_ip"));
                    monitor.setNcId(record.getString("nc_id"));
                    monitor.setSn(record.getString("sn"));
                    rawMonitorList.add(monitor);
                }
            }
        }
        for (String metricName:monitorPercentiles.keySet()) {
            Map<String, Map<String, List<Double>>> actionAndPercentiles = monitorPercentiles.get(metricName);
            for (String actionName: actionAndPercentiles.keySet()) {
                Map<String, List<Double>> exceptions = actionAndPercentiles.get(actionName);
                for (String exception: exceptions.keySet()) {
                    EvaluateMonitorPercentile percentile = new EvaluateMonitorPercentile();
                    List<Double> duration = exceptions.get(exception);
                    percentile.setBatchId(batchId);
                    percentile.setTaskId(taskId);
                    percentile.setMetricName(metricName);
                    percentile.setActionName(actionName);
                    percentile.setExceptionName(exception);
                    percentile.setPercentile(duration.get(0));
                    percentile.setDuration(duration.get(1));
                    percentile.setTotalDuration(duration.get(2));
                    monitorPercentileList.add(percentile);
                }
            }
        }
        evaluateMetricDetail.setRawMonitorList(rawMonitorList);
        evaluateMetricDetail.setMonitorPercentileList(monitorPercentileList);
        return evaluateMetricDetail;
    }

    @Override
    public int getMetricSize(EvaluateTaskMeta meta) {
        String param = meta.getParam();
        OdpsTaskParam odpsTaskParam = gson.fromJson(param, OdpsTaskParam.class);
        if (odpsTaskParam.getDataSet().size() == 0) {
            throw new EvalServerException("metric size is 0");
        }
        return odpsTaskParam.getDataSet().get(0).getMetrics().size();
    }

    @Override
    public DataSourceCheckResponse check(DataSourceCheckParam param) {
        // check table authorize
        OdpsCheckParam odpsCheckParam = (OdpsCheckParam)param;
        List<String> parts = getPartList(System.currentTimeMillis(),odpsCheckParam.getPartFormat(), false, 1);
        String sql = "";
        if (!NameFetchType.DYNAMIC.getFetchType().equals(odpsCheckParam.getNameType().getFetchType())) {
            sql = String.format("select %s from %s where %s limit 1;", odpsCheckParam.getColNames(),
                odpsCheckParam.getTableName(), parts.get(0));
        } else {
            sql = String.format("select %s, %s from %s where %s limit 1;", odpsCheckParam.getColNames(), odpsCheckParam.getName(),
                odpsCheckParam.getTableName(), parts.get(0));
        }
        try {
            odpsClient.runSql(sql);
            return DataSourceCheckResponse.of(DataSourceCheckResult.OK, "ok");
        } catch (OdpsClientException e) {
            Throwable throwable = e.getCause();
            if (throwable instanceof OdpsException) {
                OdpsException odpsException = (OdpsException)throwable;
                String msg = odpsException.getMessage();
                if (msg.contains("Authorization Failed")) {
                    return DataSourceCheckResponse.of(DataSourceCheckResult.AUTH_FAIL, msg);
                } else {
                    return DataSourceCheckResponse.of(DataSourceCheckResult.OTHER_FAIL, msg);
                }
            } else {
                return DataSourceCheckResponse.of(DataSourceCheckResult.ERROR, e.getMessage());
            }
        }
    }

    /**
     * 查询key metric详情数据
     * @param dataSet 配置信息
     * @param endTsMs
     * @param metricMatch 用户自定义名称和key metric的映射关系
     * @return Map<String, Map<String, List<Record>>>
     */
    public Map<String, Map<String, List<Record>>> queryMetricDetail(OdpsDataSet dataSet, Long endTsMs, Map<String, String> metricMatch) {
        Integer timeShift = dataSet.getTimeShift();
        if (timeShift > 0) {
            endTsMs = endTsMs - timeShift * TimeShiftUnit.of(dataSet.getShiftUnit()).getSecond() * 1000L;
        }
        List<String> parts = getPartList(endTsMs, dataSet.getPartition(), dataSet.getCrossPartition(),
                dataSet.getPartitionNumber());
        // key metric详情数据
        Map<String, Map<String, List<Record>>> keyMetricDetail = new HashMap<>();
        for (String part : parts) {
            String sql = String.format("select * from %s where %s", dataSet.getTable(), part);
            if (StringUtils.isNotBlank(dataSet.getFilter())) {
                sql = String.format("%s and %s", sql, dataSet.getFilter());
            }
            if (StringUtils.isNotBlank(dataSet.getOrder()) && StringUtils.isNotBlank(dataSet.getOrderBy())) {
                sql = String.format("%s order by %s %s", sql, dataSet.getOrderBy(), dataSet.getOrder());
            }
            logger.debug("query key metric sql: {}", sql);
            List<Record> detailRecords = queryKeyMetricRawMonitor(dataSet, sql, part);

            if (NameFetchType.DYNAMIC.getFetchType().equalsIgnoreCase(dataSet.getNameType())) {
                for (Record r: detailRecords) {
                    String action = r.getString(dataSet.getName());
                    processRecord(keyMetricDetail, action, metricMatch, r);
                }
            } else {
                for (Record r: detailRecords) {
                    String action = dataSet.getName();
                    processRecord(keyMetricDetail, action, metricMatch, r);
                }
            }
        }
        return keyMetricDetail;
    }

    private Map<String, Map<String, List<Double>>> queryByDataSet(OdpsDataSet dataSet, Long endTsMs) {
        //select $col from $table where $filter and $partition order by $order_by $order;
        Integer timeShift = dataSet.getTimeShift();
        if (timeShift > 0) {
            endTsMs = endTsMs - timeShift * TimeShiftUnit.of(dataSet.getShiftUnit()).getSecond() * 1000L;
        }
        List<String> parts = getPartList(endTsMs, dataSet.getPartition(), dataSet.getCrossPartition(),
            dataSet.getPartitionNumber());
        //Map<metric, Map<action, valueList>>
        Map<String, Map<String, List<Double>>> transit = new HashMap<>();
        List<String> metrics = dataSet.getMetrics();
        List<String> asMetric = new LinkedList<>();
        metrics.forEach(m -> {
            String mm = m.trim();
            if (m.contains(" as ")) {
                mm = m.substring(m.indexOf(" as ") + 4).trim();
            }
            asMetric.add(mm);
            transit.put(mm, new HashMap<>());
        });
        for (String part : parts) {
            logger.info("query odps data for part {}", part);
            List<String> cols = new LinkedList<>(dataSet.getMetrics());
            if (NameFetchType.DYNAMIC.getFetchType().equalsIgnoreCase(dataSet.getNameType())) {
                cols.add(dataSet.getName());
            }
            String sql = String.format("select %s from %s where %s", String.join(",", cols), dataSet.getTable(), part);
            if (StringUtils.isNotBlank(dataSet.getFilter())) {
                sql = String.format("%s and %s", sql, dataSet.getFilter());
            }
            if (StringUtils.isNotBlank(dataSet.getOrder()) && StringUtils.isNotBlank(dataSet.getOrderBy())) {
                sql = String.format("%s order by %s %s", sql, dataSet.getOrderBy(), dataSet.getOrder());
            }
            List<Record> rows = runSql(sql);

            if (NameFetchType.DYNAMIC.getFetchType().equalsIgnoreCase(dataSet.getNameType())) {
                for (Record r : rows) {
                    String action = r.getString(dataSet.getName());
                    processRow(transit, action, r, asMetric);
                }
            } else {
                for (Record r : rows) {
                    String action = dataSet.getName();
                    processRow(transit, action, r, asMetric);
                }
            }
        }
        logger.info("query all data over");
        return transit;
    }

    /**
     * 传入的是包含过滤条件的查询daily key metric的sql，通过改变select的内容，查询key metric type(unavailable|performance|control)，
     * instance id, ds，然后和ecs_dw.ecs_key_metric_raw_monitor join得到原始数据
     * 先将明细数据下载到本地，然后再计算百分比
     * 限制条件颇多，需要原始标中保存instance_id信息，又需要指标名称必须含有unavailable、performance、control等信息
     * @param dataSet 数据集的配置信息
     * @param sql 查询key metric的原始sql
     * @param part 分区信息
     * @return List<Record>
     */
    private List<Record> queryKeyMetricRawMonitor(OdpsDataSet dataSet, String sql, String part) {
        String select = "select";
        String from = "from";
        int startIndex = sql.indexOf(select) + select.length();
        int endIndex = sql.indexOf(from);
        String selectContent = sql.substring(startIndex, endIndex);
        String replaceContent;
        if (NameFetchType.DYNAMIC.getFetchType().equalsIgnoreCase(dataSet.getNameType())) {
            replaceContent = String.format(" instance_id, %s ", dataSet.getName());
        } else {
            replaceContent = " instance_id ";
        }
        String instanceSql = sql.replace(selectContent, replaceContent).replace(";", "");
        String monitorSql = String.format(" select * from ecs_dw.ecs_key_metric_raw_monitor where %s ", part);
        String joinSql;
        if (NameFetchType.DYNAMIC.getFetchType().equalsIgnoreCase(dataSet.getNameType())) {
            joinSql = String.format(" select t2.*, t1.%s from ", dataSet.getName()) + String.format(" (%s) t1 join " +
                    "(%s) t2 on (t1.instance_id=t2.instance_id);", instanceSql, monitorSql);
        } else {
            joinSql = " select t2.* from " + String.format(" (%s) t1 join (%s) t2 on (t1.instance_id=t2.instance_id);",
                    instanceSql, monitorSql);
        }
        logger.debug("query raw monitor sql: {}", joinSql);
        return runSql(joinSql);
    }

    private void processRecord(Map<String, Map<String, List<Record>>> target, String action, Map<String, String> metricMatch, Record record) {
        String metricType = record.getString("monitor_type");
        for (String metric : metricMatch.keySet()) {
            String matchColumn = metricMatch.get(metric);
            if (metricType.equals(metric) && matchColumn.length() > 0){
                if (!target.containsKey(matchColumn)) {
                    target.put(matchColumn, new HashMap<>());
                }
                Map<String, List<Record>> m = target.get(matchColumn);
                if (!m.containsKey(action)) {
                    //Map<action, valueList>
                    m.put(action, new LinkedList<>());
                }
                m.get(action).add(record);
                break;
            }
        }
    }

    /**
     * 对不同metric下的不同action的原始的异常统计异常时长的占比，并按照占比大小逆序排序返回结果
     * @param records metric下的不同action的原始的异常数据
     * @return Map<String, Map<String, Map<String, Double>>>
     */
    public Map<String, Map<String, Map<String, List<Double>>>> getTopPercentileException(Map<String, Map<String, List<Record>>> records) {
        Map<String, Map<String, Map<String, List<Double>>>> result = new HashMap<>();
        for (Map.Entry<String, Map<String, List<Record>>> entry:records.entrySet()) {
            String metric = entry.getKey();
            Map<String, List<Record>> actions = entry.getValue();
            result.put(metric, new HashMap<>());
            for (Map.Entry<String, List<Record>> action:actions.entrySet()) {
                String actionName = action.getKey();

                List<Record> recordList = action.getValue();
                Map<String, Double> exceptions = new HashMap<>();
                double sum = 0.0;
                for (Record record:recordList) {
                    Double duration = Double.parseDouble(record.getString("duration"));
                    String exceptionName = record.getString("exception_name");
                    sum += duration;
                    Double currentValue = exceptions.getOrDefault(exceptionName, 0.0) + duration;
                    exceptions.put(exceptionName, currentValue);
                }
                // 目前看起来不太需要排序，只需要在读取数据的时候order by 百分比即可
//                Map<String, Double> sortedExceptions = exceptions
//                        .entrySet()
//                        .stream()
//                        .sorted(Collections.reverseOrder(comparingByValue()))
//                        .collect(toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e2, LinkedHashMap::new));
                Map<String, List<Double>> exceptionAndPercentiles = new HashMap<>();
                for (Map.Entry<String, Double> exception:exceptions.entrySet()) {
                    List<Double> percentiles = new ArrayList<>();
                    // 百分比
                    percentiles.add(exception.getValue()/sum);
                    // 该异常的异常时长
                    percentiles.add(exception.getValue());
                    // 该action下的总异常时长
                    percentiles.add(sum);
                    exceptionAndPercentiles.put(exception.getKey(), percentiles);
                }
                result.get(metric).put(actionName, exceptionAndPercentiles);
            }
        }
        return result;
    }

    private List<Record> runSql(String sql) {
        if (sql.endsWith(";")) {
            sql = sql.substring(0, sql.length() - 1);
        }
        List<Record> rows = odpsClient.runSql(sql + ";");
        if (rows.size() < MAX_LIMIT) {
            return rows;
        } else {
            List<Record> ret = new LinkedList<>(rows);
            int queries = 1;
            int start = MAX_LIMIT;
            while (queries < MAX_QUERY) {
                String sqlLimit = String.format("%s limit %d, %d;", sql, start, MAX_LIMIT);
                List<Record> sub = odpsClient.runSql(sqlLimit);
                ret.addAll(sub);
                if (sub.size() < MAX_LIMIT) {
                    break;
                }
                queries++;
                start += MAX_LIMIT;
            }
            return ret;
        }

    }

    private void processRow(Map<String, Map<String, List<Double>>> transit, String action, Record r, List<String> metrics) {
        for (String metric : metrics) {
            Object o = r.get(metric);
            Map<String, List<Double>> m = transit.get(metric);
            if (!m.containsKey(action)) {
                //Map<action, valueList>
                m.put(action, new LinkedList<>());
            }
            try {
                m.get(action).add(Double.parseDouble(o.toString()));
            } catch (NumberFormatException e) {
                logger.warn("parse double fail, metric: {}, val: {}", metric, o.toString(), e);
            }
        }
    }

    private List<String> getPartList(Long endTsMs, String partition, Boolean crossPart, Integer partNum) {
        endTsMs = endTsMs / 3600000 * 3600000;
        Integer number = partNum;
        if (!crossPart) {
            number = 1;
        }
        String[] partSection = partition.split("=");
        if (partSection.length != 2) {
            throw new EvalDataSourceException("error partition format: " + partition);
        }
        SimpleDateFormat sdf = new SimpleDateFormat(partSection[1], Locale.getDefault());
        List<String> parts = new LinkedList<>();
        while (true) {
            String p = sdf.format(endTsMs);
            if (!parts.contains(String.format("%s='%s'", partSection[0], p))) {
                parts.add(String.format("%s='%s'", partSection[0], p));
                if (parts.size() >= number) {
                    return parts;
                }
            }
            endTsMs -= 3600000;
        }
    }
}
