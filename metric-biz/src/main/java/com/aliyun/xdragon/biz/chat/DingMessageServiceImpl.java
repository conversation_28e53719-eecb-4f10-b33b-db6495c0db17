package com.aliyun.xdragon.biz.chat;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkcard_1_0.Client;
import com.aliyun.dingtalkcard_1_0.models.*;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponseBody;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.aliyun.xdragon.api.service.chat.DingMessageService;
import com.aliyun.xdragon.service.common.cache.RedisUtil;
import com.aliyuncs.utils.StringUtils;
import com.google.common.base.Joiner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class DingMessageServiceImpl implements DingMessageService {
    private static final Logger logger = LoggerFactory.getLogger(DingMessageServiceImpl.class);

    private static final String DING_TALK_TOKEN_KEY = "ding_talk_token_key";

    @Value("${ding.talk.appKey}")
    private String appKey;

    @Value("${ding.talk.appSecret}")
    private String appSecret;

    @Value("${ding.talk.cardTemplateId}")
    private String cardTemplateId;

    private Client client;

    @Resource
    private RedisUtil redisUtil;

    private Client getClient() throws Exception {
        if(client != null){
            return client;
        }

        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        this.client = new Client(config);
        return client;
    }

    private String getAccessToken() throws Exception{
        Object value = redisUtil.get(DING_TALK_TOKEN_KEY);
        if(value != null){
            return String.valueOf(value);
        }

        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        com.aliyun.dingtalkoauth2_1_0.Client client = new com.aliyun.dingtalkoauth2_1_0.Client(config);
        GetAccessTokenRequest getAccessTokenRequest = new GetAccessTokenRequest()
                .setAppKey(appKey)
                .setAppSecret(appSecret);
        try {
            GetAccessTokenResponseBody response = client.getAccessToken(getAccessTokenRequest).getBody();
            String token = response.getAccessToken();
            redisUtil.set(DING_TALK_TOKEN_KEY,token,3600);
            return token;
        } catch (Exception e) {
            logger.error("DingMessageService getToken exception",e);
        }
        return null;
    }

    @Override
    public String createMessageCard(String message,String staffId,String dingGroupId,Integer conversationType) throws Exception {
        CreateAndDeliverHeaders createAndDeliverHeaders = new CreateAndDeliverHeaders();
        createAndDeliverHeaders.xAcsDingtalkAccessToken = getAccessToken();
        CreateAndDeliverRequest.CreateAndDeliverRequestCardData cardData = createCardDataMap(message);

        //这个参数是控制卡片发送给哪些人员或者给哪些群
        String openSpaceId = getOpenSpaceId(staffId,dingGroupId,conversationType);

        // 组合最终请求参数
        String outTrackId = UUID.randomUUID().toString();
        CreateAndDeliverRequest createAndDeliverRequest = new CreateAndDeliverRequest()
                .setUserId(staffId) // 创建人
                .setCardTemplateId(cardTemplateId) // 卡片模版id
                .setOutTrackId(outTrackId) // 卡片实例id
                .setCardData(cardData) // 卡片数据
                .setCallbackType("STREAM")
                .setUserIdType(1) // 用户id类型
                .setOpenSpaceId(openSpaceId);

        if(conversationType != null && conversationType==1){
            CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenSpaceModel imRobotOpenSpaceModel = getImRobotOpenSpaceModel(message);
            CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenDeliverModel imRobotOpenDeliverModel = new CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenDeliverModel()
                    .setSpaceType("IM_ROBOT");
            createAndDeliverRequest.setImRobotOpenSpaceModel(imRobotOpenSpaceModel);
            createAndDeliverRequest.setImRobotOpenDeliverModel(imRobotOpenDeliverModel);
        }

        if(conversationType != null && conversationType==2) {
            CreateAndDeliverRequest.CreateAndDeliverRequestImGroupOpenSpaceModel imGroupOpenSpaceModel = getImGroupOpenSpaceModel(message);
            CreateAndDeliverRequest.CreateAndDeliverRequestImGroupOpenDeliverModel imGroupOpenDeliverModel = new CreateAndDeliverRequest.CreateAndDeliverRequestImGroupOpenDeliverModel()
                    .setRobotCode(appKey);
            createAndDeliverRequest.setImGroupOpenSpaceModel(imGroupOpenSpaceModel);
            createAndDeliverRequest.setImGroupOpenDeliverModel(imGroupOpenDeliverModel);
        }

        // 开始相关钉钉接口
        try {
            CreateAndDeliverResponse response = getClient().createAndDeliverWithOptions(createAndDeliverRequest, createAndDeliverHeaders, new RuntimeOptions());
            logger.info("DingMessageService createMessageCard success,head:{},request:{}, response:{}",JSONObject.toJSONString(createAndDeliverHeaders),JSONObject.toJSONString(createAndDeliverRequest), JSONObject.toJSONString(response));
        }catch (Exception e){
            logger.error("DingMessageService createMessageCard, staffId:{},header:{},request:{},message:{}",staffId,JSONObject.toJSONString(createAndDeliverHeaders),JSONObject.toJSONString(createAndDeliverRequest),message,e);
            throw e;
        }
        return outTrackId;
    }

    @Override
    public void updateMessageCard(String message, String outTrackId, boolean isFinish) throws Exception{
        StreamingUpdateHeaders streamingUpdateHeaders = new StreamingUpdateHeaders();
        streamingUpdateHeaders.xAcsDingtalkAccessToken = getAccessToken();
        StreamingUpdateRequest streamingUpdateRequest = new StreamingUpdateRequest()
                .setOutTrackId(outTrackId)
                .setGuid(UUID.randomUUID().toString())
                .setKey("content")
                .setContent(message)
                .setIsFull(true)
                .setIsFinalize(isFinish)
                .setIsError(false);
        try{
            StreamingUpdateResponse response = client.streamingUpdateWithOptions(streamingUpdateRequest, streamingUpdateHeaders, new RuntimeOptions());
            logger.info("DingMessageService updateMessageCard, outTrackId:{}, message:{},response:{}", outTrackId, message, JSONObject.toJSONString(response));
        }catch (Exception e){
            logger.error("DingMessageService updateMessageCard, outTrackId:{}, message:{}",outTrackId,message,e);
        }
    }

    @Override
    public void updateCallbackCard(String hitSceneList, String workItemInfoList, String outTrackId) throws Exception {
        UpdateCardHeaders updateCardHeaders = new UpdateCardHeaders();
        updateCardHeaders.xAcsDingtalkAccessToken = getAccessToken();
        UpdateCardRequest.UpdateCardRequestCardUpdateOptions cardUpdateOptions = new UpdateCardRequest.UpdateCardRequestCardUpdateOptions()
                .setUpdateCardDataByKey(true)
                .setUpdatePrivateDataByKey(false);
            Map<String, String> cardDataCardParamMap = TeaConverter.buildMap(
                    new TeaPair("hitSceneList", hitSceneList),
                    new TeaPair("workItemInfoList", workItemInfoList)
            );
            com.aliyun.dingtalkcard_1_0.models.UpdateCardRequest.UpdateCardRequestCardData cardData1 = new com.aliyun.dingtalkcard_1_0.models.UpdateCardRequest.UpdateCardRequestCardData()
                    .setCardParamMap(cardDataCardParamMap);
            com.aliyun.dingtalkcard_1_0.models.UpdateCardRequest updateCardRequest = new com.aliyun.dingtalkcard_1_0.models.UpdateCardRequest()
                    .setOutTrackId(outTrackId)
                    .setCardData(cardData1)
                    .setCardUpdateOptions(cardUpdateOptions)
                    .setUserIdType(1);
        try{
            UpdateCardResponse updateCardResponse = getClient().updateCardWithOptions(updateCardRequest, updateCardHeaders, new RuntimeOptions());
            logger.info("DingMessageService updateCallbackCard, outTrackId:{}, response:{}", outTrackId, JSONObject.toJSONString(updateCardResponse));
        }catch (Exception e){
            logger.error("DingMessageService updateCallbackCard, outTrackId:{}",outTrackId,e);
        }
    }

    // 生成卡片数据
    private static CreateAndDeliverRequest.CreateAndDeliverRequestCardData createCardDataMap(String message) {
        Map<String, String> cardParamMap = new HashMap<>();

        cardParamMap.put("content", message);

        JSONObject doing = new JSONObject();
        doing.put("show_progress", true);
        doing.put("progress", "20");
        doing.put("title", "正在处理中");
        doing.put("content", "钉钉互动卡片");
        cardParamMap.put("doing", JSONObject.toJSONString(doing));

        JSONObject pending = new JSONObject();
        pending.put("title", "正在处理中");
        cardParamMap.put("pending", JSONObject.toJSONString(pending));

        JSONObject done = new JSONObject();
        pending.put("action_text", "内容由AI生成");
        pending.put("action_url", "https://dingtalk.com");
        cardParamMap.put("done", JSONObject.toJSONString(done));

        JSONObject failed = new JSONObject();
        pending.put("image", "https://static.dingtalk.com/media/lALPDeC2-_4rgFjNAVDNAVA_336_336.png");
        pending.put("text", "内容生成失败，您可以");
        pending.put("action_text", "点击重试");
        pending.put("action_url", "dtmd://dingtalkclient/sendMessage?content=%E8%BD%AC%E4%BA%BA%E5%B7%A5");
        cardParamMap.put("failed", JSONObject.toJSONString(failed));

        CreateAndDeliverRequest.CreateAndDeliverRequestCardData cardData = new CreateAndDeliverRequest.CreateAndDeliverRequestCardData()
                .setCardParamMap(cardParamMap);

        return cardData;
    }

    public static CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenSpaceModel getImRobotOpenSpaceModel(String message) {
        // 这段配置作用在手机的锁屏通知，手机通知中心里
        CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenSpaceModelNotification imRobotOpenSpaceModelNotification = new CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenSpaceModelNotification()
                .setAlertContent("ECS诊断助手为您推送了一条钉钉消息")
                .setNotificationOff(false);

        // LastMessage 还未点击进入机器人聊天窗口时，显示在群消息列表里的预览信息 ZH_CN：简体中文； ZH_TW：繁体中文； EN_US：英文； JA_JP：日语； VI_VN：越南语；
        Map<String, String> imRobotOpenSpaceModelLastMessageI18n = new HashMap<>();
        imRobotOpenSpaceModelLastMessageI18n.put("ZH_CN", message);
        imRobotOpenSpaceModelLastMessageI18n.put("EN_US", "you have a message to be confirmed");

        // 这段配置作用在钉钉搜索聊天记录框里，因为carddata里的东西是无法被搜索引擎感知到的
        CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenSpaceModelSearchSupport imRobotOpenSpaceModelSearchSupport = new CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenSpaceModelSearchSupport()
                .setSearchIcon("@lALPDgQ9q8hFhlHNAXzNAqI")
                .setSearchTypeName("{\"zh_CN\":\"待办\",\"zh_TW\":\"待辦\",\"en_US\":\"ToDo\"}")
                .setSearchDesc(message);

        return new CreateAndDeliverRequest.CreateAndDeliverRequestImRobotOpenSpaceModel()
                .setSupportForward(true)
                .setLastMessageI18n(imRobotOpenSpaceModelLastMessageI18n)
                .setSearchSupport(imRobotOpenSpaceModelSearchSupport)
                .setNotification(imRobotOpenSpaceModelNotification);
    }

    // IM群聊场域信息配置，消息通知相关的外部展示信息
    public static CreateAndDeliverRequest.CreateAndDeliverRequestImGroupOpenSpaceModel getImGroupOpenSpaceModel(String message) {
        // 这段配置作用在手机的锁屏通知，手机通知中心里
        CreateAndDeliverRequest.CreateAndDeliverRequestImGroupOpenSpaceModelNotification imGroupOpenSpaceModelNotification = new CreateAndDeliverRequest.CreateAndDeliverRequestImGroupOpenSpaceModelNotification()
                .setAlertContent("ECS诊断助手为您推送了一条钉钉消息")
                .setNotificationOff(false);

        // LastMessage 还未点击进入群内聊天窗口时，显示在群消息列表里的预览信息 ZH_CN：简体中文； ZH_TW：繁体中文； EN_US：英文；JA_JP：日语； VI_VN：越南语；
        Map<String, String> imGroupOpenSpaceModelLastMessageI18n = new HashMap<>();
        imGroupOpenSpaceModelLastMessageI18n.put("ZH_CN", message);
        imGroupOpenSpaceModelLastMessageI18n.put("EN_US", "you have a message to be confirmed");

        // 这段配置作用在钉钉搜索聊天记录框里，因为carddata里的东西是无法被搜索引擎感知到的
        CreateAndDeliverRequest.CreateAndDeliverRequestImGroupOpenSpaceModelSearchSupport imGroupOpenSpaceModelSearchSupport = new CreateAndDeliverRequest.CreateAndDeliverRequestImGroupOpenSpaceModelSearchSupport()
                .setSearchIcon("@lALPDgQ9q8hFhlHNAXzNAqI")
                .setSearchTypeName("{\"zh_CN\":\"待办\",\"zh_TW\":\"待辦\",\"en_US\":\"ToDo\"}")
                .setSearchDesc(message);

        return new CreateAndDeliverRequest.CreateAndDeliverRequestImGroupOpenSpaceModel()
                .setSupportForward(true)
                .setLastMessageI18n(imGroupOpenSpaceModelLastMessageI18n)
                .setSearchSupport(imGroupOpenSpaceModelSearchSupport)
                .setNotification(imGroupOpenSpaceModelNotification);
    }




    // 生成投放场域数据，确定发送给谁？给哪个群？
    public static String getOpenSpaceId(String staffId, String dingGroupId, Integer conversationType) {
        List<String> list = new ArrayList<>();
        // 单聊
        // 什么是userid https://alidocs.dingtalk.com/i/nodes/Y1OQX0akWmzdBowLFm4N2DkXVGlDd3mE?utm_scene=team_space&iframeQuery=anchorId%3Duu_ljb5h9lwqb6pk7b2quf

        //群聊
        if(conversationType!=null && conversationType==2 && !StringUtils.isEmpty(dingGroupId)){
            list.add(String.format("im_group.%s",dingGroupId));
        }else if(!StringUtils.isEmpty(staffId)){
            list.add(String.format("im_robot.%s",staffId)); // 指定通过单聊推送给谁 可同时推送多人
        }
        // 同时单聊、群聊等其他场景 多个之间 用;分割
        // openSpaceId 参数是设置卡片发送给谁？给哪个群？格式举例（用;分割）："dtv1.card//im_robot.xxx;im_robot.xxx;im_group.cidcidxxxxxxxxxxxxxxxxx==;im_group.cidcidxxxxxxxxxxxxxxxxx=="
        return String.format("dtv1.card//%s", Joiner.on(";").join(list));
    }

}
