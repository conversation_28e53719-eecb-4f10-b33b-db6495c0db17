package com.aliyun.xdragon.biz.integration.repository;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.request.GetLogStoreRequest;
import com.aliyun.openservices.log.response.GetLogStoreResponse;
import com.aliyun.xdragon.biz.log.repository.AbstractSlsDao;
import com.aliyun.xdragon.common.model.LogConfig;
import com.aliyun.xdragon.service.common.config.diamond.ConfigService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2023/9/1
 */
@Repository
public class IntegrationSlsDao extends AbstractSlsDao {

    private static final Logger logger = LoggerFactory.getLogger(IntegrationSlsDao.class);

    @Override
    protected LogConfig logConfig() {
        return null;
    }

    public List<Map<String, Object>> querySls(Client client, String projectName, String logStore, String sql, int timeSecondRange) throws LogException {
        int start = (int) Instant.now().minusSeconds(timeSecondRange).getEpochSecond();
        int end = (int) Instant.now().getEpochSecond();
        logger.info("start to query sls, project: {}, logStore: {}, sql: {}", projectName, logStore, sql);
        ArrayList<QueriedLog> queriedLogs = super.querySls(client, projectName, logStore, start, end, sql);
        logger.info("finish to query sls, project: {}, logStore: {}, sql: {}, result: {}",
            projectName, logStore, sql, JSON.toJSONString(queriedLogs));
        if (CollectionUtils.isEmpty(queriedLogs)) {
            return Collections.emptyList();
        }
        return queriedLogs.stream().map(log -> {
            List<LogContent> logContentList = log.GetLogItem().GetLogContents();
            Map<String, Object> m = new HashMap<>(logContentList.size());
            logContentList.forEach(content -> m.put(content.GetKey(), content.GetValue()));
            return m;
        }).collect(Collectors.toList());
    }

    /**
     * 校验logStore是否存在
     * @param projectName
     * @param logStore
     * @param client
     * @return
     */
    public boolean checkLogStoreExist(String projectName, String logStore, Client client) {
        GetLogStoreRequest request = new GetLogStoreRequest(projectName, logStore);
        try {
            logger.info("checkLogStoreExist with project: {}, logStore: {}", projectName, logStore);
            GetLogStoreResponse response = client.GetLogStore(request);
            logger.info("checkLogStoreExist with project: {}, logStore: {}, response: {}", projectName, logStore, JSON.toJSONString(response));
            return Objects.nonNull(response)
                && Objects.nonNull(response.GetLogStore())
                && StringUtils.isNotBlank(response.GetLogStore().GetLogStoreName());
        } catch (LogException e) {
            logger.info("checkLogStoreExist error cause getting logStore failed, project: {}, logStore: {}", projectName, logStore, e);
            return false;
        }
    }

}
