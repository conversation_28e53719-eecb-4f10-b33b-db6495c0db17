package com.aliyun.xdragon.biz.log.config;

import com.aliyun.xdragon.common.model.LogConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/03/14
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "log.cluster.sls")
public class LogClusterSlsConfig extends LogConfig {
    private String mappingStore;
    private String distributionStore;
    private String remainStore;
}
