package com.aliyun.xdragon.biz.diagnostic.job.util;

import java.io.FileNotFoundException;
import java.util.Scanner;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2023/09/25
 */
public class LabelConfig {
    private static final Logger logger = LoggerFactory.getLogger(LabelConfig.class);

    private BiMap<Integer, String> map = HashBiMap.create();

    public LabelConfig() {
        this("diagnostic/label.csv");
    }

    public LabelConfig(String fileName) {
        try {
            Scanner sc = new Scanner(ResourceUtils.getResourceAsStream(fileName));
            while (sc.hasNextLine()) {
                String line = sc.nextLine();
                String[] items = line.split(",");
                Integer id = Integer.parseInt(items[0]);
                String label = items[1];
                map.put(id, label);
            }
            sc.close();
        } catch (FileNotFoundException e) {
            logger.error("Init LabelConfig error! ", e);
        }
    }

    public String getLabelById(int id) {
        return map.get(id);
    }

    public int getIdByLabel(String label) {
        return map.inverse().get(label);
    }

    public int getLabelNum() {
        return map.size();
    }

}
