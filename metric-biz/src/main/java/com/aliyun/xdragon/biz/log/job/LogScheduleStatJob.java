package com.aliyun.xdragon.biz.log.job;

import java.util.Date;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.xdragon.biz.log.model.JobErrorCode;
import com.aliyun.xdragon.biz.log.processor.LogScheduleStatProcessor;
import com.aliyun.xdragon.common.model.log.DayTimeRange;
import com.aliyun.xdragon.service.common.job.AbstractProcessTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/10/24
 */
@Component
@Lazy
public class LogScheduleStatJob<T> extends AbstractProcessTask<T> {
    private static final int DELTA_DAY = 7;

    @Autowired
    private LogScheduleStatProcessor processor;


    @Override
    public ProcessResult process(JobContext context, int dataTs, int schedTs) throws Exception {
        JobErrorCode code = processor.process(DayTimeRange.of(new Date(dataTs * 1000L - DELTA_DAY * 86400L * 1000L),
            new Date(dataTs * 1000L - 86400L * 1000L)));
        return new ProcessResult(JobErrorCode.Success.equals(code));
    }
}
