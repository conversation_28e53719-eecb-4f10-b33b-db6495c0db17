package com.aliyun.xdragon.biz.chat.handle;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.xdragon.api.service.chat.enums.SessionSourceEnum;
import com.aliyun.xdragon.api.service.chat.model.AgentAnswerResponse;
import com.aliyun.xdragon.common.util.StringUtil;
import com.aliyun.xdragon.service.common.util.DataCleaningUtil;
import com.aliyun.xdragon.service.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service("aoneHandleService")
public class AoneHandleServiceImpl extends ChatSceneBasicHandleServiceImpl {
    private static final Logger logger = LoggerFactory.getLogger(AoneHandleServiceImpl.class);

    @Autowired
    private DiagnoseHandleServiceImpl diagnoseHandleService;

    @Value("${domain.cloudbot}")
    private String cloudbotDomain;

    @Override
    protected String getViewContent(AgentAnswerResponse response, Integer source) throws Exception {
        JSONObject aoneSummary = response.getAoneSummery();
        JSONObject data = new JSONObject();
        if (aoneSummary != null) {
            data.put("title",aoneSummary.getString("title"));
            data.put("exceptionTime",aoneSummary.getString("time"));
            data.put("description",aoneSummary.getString("description"));
            if (StringUtils.isNotEmpty(aoneSummary.getString("progress"))) {
                String progress = aoneSummary.getString("progress").replace("#","").replace("排查进展","");
                data.put("progress",progress);
            }
            if(aoneSummary.getJSONArray("rule_ids")!=null && aoneSummary.getJSONArray("rule_ids").size()>0){
                List<String> machineIds = aoneSummary.getJSONArray("rule_ids").toJavaList(String.class);
                data.put("machineIds", StringUtil.toString(machineIds));
            }
        } else if(StringUtils.isNotEmpty(response.getAnswer())){
            data.put("answer",response.getAnswer());
        } else if(!response.isFinish()){
            data.put("abstract",response.getTips());
        }else{
            data.put("abstract","未总结出有效信息");
        }
        data.put("aoneUrl",response.getAoneUrl());

        if(response.isFinish()){
            //总结出的时间与诊断时间不一致时
            checkFailureTime(response);
            diagnoseHandleService.getDiagnoseParam(response,data,source);
        }

        return fillViewTemplate(data,"chat/aone_diagnose.tpl",response.getTool(),source);
    }

    @Override
    protected String getContent(AgentAnswerResponse response) {
        JSONObject aoneSummary = response.getAoneSummery();
        if(aoneSummary==null){
            return response.getAnswer();
        }
        return JSONObject.toJSONString(aoneSummary);
    }

    @Override
    protected String getUrl(AgentAnswerResponse response, Integer source) {
        JSONObject params = response.getParams();
        String url = "";
        if(params == null){
            return url;
        }

        String machineId = params.getString("machine_id");
        if(StringUtils.isEmpty(machineId)){
            logger.error("DiagnoseHandleServiceImpl getUrl machineId is null");
            return url;
        }
        String[] machineIds = machineId.split(",");
        if (machineIds.length==1) {
            if(SessionSourceEnum.DINGDING.getSource().equals(source)){
                url = "dingtalk://dingtalkclient/page/link?pc_slide=true&url=http%3A%2F%2F"+ cloudbotDomain + "%2Fcloudbot%2Fng2%2F%23%2FfullLinkDelimitationH5%3FhideTopbar%3Dtrue%26clickMachineSkip%3Dfalse%26machineId%3D" + params.getString("machine_id");
            }else{
                url = String.format("https://%s/cloudbot/ng2/#/operations/monitor-exception?tab=index&machineId=%s&startTime=%s&endTime=%s",cloudbotDomain, params.getString("machine_id"), DateUtil.dateString(params.getDate("start_time")), DateUtil.dateString(params.getDate("end_time")));
            }
            return url;
        }


        List<String> machineIdList = Arrays.stream(machineIds).collect(Collectors.toList());
        String config = "REASON_VM_APPLICABILITY";
        if (DataCleaningUtil.isIp(machineIdList.get(0))) {
            config = "REASON_NC_PERF";
        }
        url = String.format("https://%s/cloudbot/ng2/#/operations/monitor-exception?tab=batchdetect&config=%s&machineId=%s&startTime=%s&endTime=%s",cloudbotDomain, config,StringUtil.toString(machineIdList), DateUtil.dateString(params.getDate("start_time")), DateUtil.dateString(params.getDate("end_time")));
        return url;
    }

    @Override
    protected String getLinks(AgentAnswerResponse response) {
        return null;
    }

    @Override
    protected String getAones(AgentAnswerResponse response) {
        return null;
    }

    @Override
    protected boolean aiAgentResCheck(AgentAnswerResponse response) throws Exception {
        JSONObject params = response.getParams();
        JSONObject aoneSummery = response.getAoneSummery();
        if(StringUtils.isEmpty(response.getAnswer()) && aoneSummery == null){
            if(response.isFinish()){
                response.setTips("未总结出有效信息");
            }
            return false;
        }

        if(response.isFinish() && StringUtils.isNotEmpty(params.getString("machine_id"))){
            //修正参数machine_id
            List<String> machineIds = JSONObject.parseArray(params.getString("machine_id"),String.class);
            if(machineIds.size()<=0){
                return false;
            }

            String machineId = StringUtil.toString(machineIds);
            params.put("machine_id", machineId);

            //修正诊断时间参数
            Date startTime = params.getDate("start_time");
            Date endTime = params.getDate("end_time");
            if (endTime == null) {
                endTime = new Date();
            }

            if (startTime == null) {
                startTime = DateUtil.modifyDateByMinute(endTime,-1440);
            } else {
                if (startTime.after(endTime) || startTime.getTime() == endTime.getTime()) {
                    startTime = DateUtil.modifyDateByMinute(endTime,-1440);
                } else if (response.getAoneCreateTime() != null && response.getAoneCreateTime().equals(startTime)) {
                    startTime = DateUtil.modifyDateByMinute(startTime,-1440);
                } else {
                    startTime = DateUtil.modifyDateByMinute(startTime,-360);
                }
            }

            //如果开始时间和结束时间范围超过2天，则结束时间为开始时间+2天
            if (DateUtil.modifyDateByDay(endTime,-2).after(startTime)) {
                endTime = DateUtil.modifyDateByDay(startTime, 2);
            }
            params.put("start_time",startTime);
            params.put("end_time",endTime);
        }
        return true;
    }

    private void checkFailureTime(AgentAnswerResponse response) throws Exception{
        // 正则表达式用于匹配 "故障时间：" 后跟着日期时间格式 "yyyy-MM-dd HH:mm:ss"
        String regex = "\\*\\*故障时间：\\*\\*\\s*(\\d{4}-\\d{2}-\\d{2}\\s*\\d{2}:\\d{2}:\\d{2})";
        Pattern pattern = Pattern.compile(regex);
        String answer = response.getAnswer();
        Matcher matcher = pattern.matcher(answer);
        JSONObject params = response.getParams();
        Date startTime = params.getDate("start_time");
        Date endTime = params.getDate("end_time");
        logger.info("AoneHandleServiceImpl checkFailureTime params:{},answer:{}",JSONObject.toJSONString(params),answer);

        if (matcher.find()) {
            // 返回第一个捕获组的内容，即日期时间字符串
            String failureTimeStr = matcher.group(1);
            logger.info("AoneHandleServiceImpl checkFailureTime failureTimeStr:{}",failureTimeStr);

            if(StringUtils.isEmpty(failureTimeStr)){
                return;
            }
            Date failureTime = DateUtil.genDateFromStr(failureTimeStr);
            if(failureTime.compareTo(startTime)>=0 && failureTime.compareTo(endTime)<=0){
                return;
            }
            params.put("start_time", DateUtil.modifyDateBySecond(failureTime,-10800));
            params.put("end_time", DateUtil.modifyDateBySecond(failureTime,3600));
            logger.info("AoneHandleServiceImpl modifyFailureTime startTime:{}, endTime:{}",params.getDate("start_time"),params.getDate("end_time"));
        }
    }
}
