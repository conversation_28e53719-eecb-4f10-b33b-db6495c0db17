package com.aliyun.xdragon.biz.diagnostic.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;

import com.aliyun.xdragon.common.enumeration.DiagnosisStatus;
import com.aliyun.xdragon.common.model.metric.CombinedDiagnosisResult;
import com.aliyun.xdragon.common.model.metric.DiagnosisResult;
import com.aliyun.xdragon.common.model.metric.request.PerformanceDiagnosisRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/02/05
 */
@Service
public class DiagnosisContextLogger {

    private static final Logger logger = LoggerFactory.getLogger("diagnosisLogger");

    public List<Map<String, String>> log(PerformanceDiagnosisRequest request, List<DiagnosisResult> resultList) {
        Map<String, List<DiagnosisResult>> scenarioMap = resultList.stream()
            .collect(Collectors.groupingBy(DiagnosisResult::getScenario));
        List<Map<String, String>> items = new ArrayList<>();
        for (Entry<String, List<DiagnosisResult>> entry : scenarioMap.entrySet()) {
            Map<String, String> item = new HashMap<>();
            item.put("timestamp", Long.toString(System.currentTimeMillis()));
            item.put("instanceId", request.getInstanceId());
            item.put("startTs", request.getStartTs().toString());
            item.put("endTs", request.getEndTs().toString());
            item.put("scenario", entry.getKey());
            item.put("params", JSONObject.toJSONString(request));
            item.put("status", analyseResults(entry.getValue()).name());
            CombinedDiagnosisResult r = new CombinedDiagnosisResult();
            r.setResults(entry.getValue());
            item.put("result", JSONObject.toJSONString(r));
            items.add(item);
            logger.info(JSONObject.toJSONString(item));
        }
        return items;
    }

    private DiagnosisStatus analyseResults(List<DiagnosisResult> results) {
        Map<DiagnosisStatus, Integer> map = new HashMap<>();
        for (DiagnosisResult result : results) {
            map.put(result.getStatus(), map.getOrDefault(result.getStatus(), 0) + 1);
        }
        if (map.containsKey(DiagnosisStatus.UNKNOWN)) {
            return DiagnosisStatus.UNKNOWN;
        }
        if (map.containsKey(DiagnosisStatus.DAMAGED)) {
            return DiagnosisStatus.DAMAGED;
        }
        return DiagnosisStatus.HEALTHY;
    }

}
