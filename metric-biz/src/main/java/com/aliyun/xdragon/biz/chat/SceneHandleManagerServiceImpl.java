package com.aliyun.xdragon.biz.chat;

import com.aliyun.xdragon.api.service.chat.ChatSceneHandleService;
import com.aliyun.xdragon.api.service.chat.SceneHandleManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class SceneHandleManagerServiceImpl implements SceneHandleManagerService {
    Map<String,ChatSceneHandleService> handleServiceMap = new HashMap<>();

    @Autowired
    public SceneHandleManagerServiceImpl(Map<String, ChatSceneHandleService> handleServiceMap) {
        this.handleServiceMap = handleServiceMap;
    }

    @Override
    public ChatSceneHandleService getHandleService(String handleServiceName) {
        return handleServiceMap.get(handleServiceName);
    }
}
