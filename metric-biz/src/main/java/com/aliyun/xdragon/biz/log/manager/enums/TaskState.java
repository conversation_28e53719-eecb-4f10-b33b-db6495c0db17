package com.aliyun.xdragon.biz.log.manager.enums;

/**
 * <AUTHOR>
 */

public enum TaskState {
    /**
     * 默认值，无特殊定义
     */
    NONE(0, "none"),
    /**
     * 未知状态，一般表示任务刚创建从未运行过
     */
    UNKNOWN(1, "unknown"),
    /**
     * 运行状态，表示任务正在运行中
     */
    RUNNING(2, "running"),
    /**
     * 暂停状态，表示任务当前已暂停
     */
    PAUSED(3, "paused"),
    /**
     * 终止状态，表示任务当前已停止
     */
    TERMINATED(4, "terminated"),
    /**
     * 等待状态，表示任务即将启动
     */
    WAITING(5, "waiting"),
    /**
     * 暂停中状态，表示任务即将暂停
     */
    PAUSING(6, "pausing"),
    /**
     * 终止中状态，表示任务即将停止
     */
    TERMINATING(7, "terminating"),
    /**
     * 不在线状态，表示任务不存在或未上线
     */
    OFFLINE(8, "offline"),
    /**
     * 结束状态，表示任务已完成运行
     */
    FINISHED(9, "finished"),
    /**
     * 不在线状态，表示任务不存在或未上线
     */
    FAILED(10, "failed"),
    /**
     * 多种状态
     */
    MULTI(11, "multi"),
    /**
     * 不存在状态，未找到指定任务
     */
    NOT_EXIST(12, "not exist");

    private int code;
    private String description;

    TaskState(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public Boolean isTransitionState() {
        return code == WAITING.code || code == PAUSING.code || code == TERMINATING.code;
    }

    public Boolean isRunningState() {
        return code == RUNNING.code;
    }

    public Boolean isStartableState() {
        return code == UNKNOWN.code || code == PAUSED.code || code == TERMINATED.code || code == OFFLINE.code || code == FAILED.code;
    }

    public Boolean isStopableState() {
        return code == RUNNING.code || code == WAITING.code;
    }

    public Boolean isErrorState() {
        return code == NONE.code;
    }
}
