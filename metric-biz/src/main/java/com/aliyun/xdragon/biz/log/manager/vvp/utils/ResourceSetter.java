package com.aliyun.xdragon.biz.log.manager.vvp.utils;

import com.aliyun.ververica_inner20220718.models.BasicResourceSetting;
import com.aliyun.ververica_inner20220718.models.BasicResourceSettingSpec;
import com.aliyun.ververica_inner20220718.models.BriefResourceSetting;
import com.aliyun.ververica_inner20220718.models.StreamingResourceSetting;
import com.aliyun.xdragon.biz.log.manager.config.ResourceConfig;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/11/21
 */
public class ResourceSetter {
    private static Gson gson = new Gson();
    public static BriefResourceSetting fullySpecifiedBriefResourceSetting(ResourceConfig config, String lastResourceSetting) {
        // streaming resource setting
        StreamingResourceSetting streamingResourceSetting;

        if (StringUtils.isBlank(lastResourceSetting)) {
            streamingResourceSetting = new StreamingResourceSetting();
            // basic resource setting spec
            BasicResourceSettingSpec basicResourceSettingSpec = new BasicResourceSettingSpec();
            basicResourceSettingSpec.setCpu(config.getCpu());
            basicResourceSettingSpec.setMemory(config.getMemory());

            BasicResourceSettingSpec masterBasicResourceSettingSpec = new BasicResourceSettingSpec();
            masterBasicResourceSettingSpec.setCpu(config.getCpu());
            masterBasicResourceSettingSpec.setMemory(config.getMemory());

            // basic resource setting
            BasicResourceSetting basicResourceSetting = new BasicResourceSetting();
            basicResourceSetting.setParallelism(config.getParallelism());
            basicResourceSetting.setJobmanagerResourceSettingSpec(masterBasicResourceSettingSpec);
            basicResourceSetting.setTaskmanagerResourceSettingSpec(basicResourceSettingSpec);

            streamingResourceSetting.setResourceSettingMode(config.getMode().name());
            streamingResourceSetting.setBasicResourceSetting(basicResourceSetting);
        } else {
            streamingResourceSetting = gson.fromJson(lastResourceSetting, StreamingResourceSetting.class);
        }

        // brief resource setting
        BriefResourceSetting briefResourceSetting = new BriefResourceSetting();
        briefResourceSetting.setStreamingResourceSetting(streamingResourceSetting);
        briefResourceSetting.setFlinkConf(config.getConfMap());
        return briefResourceSetting;
    }
}
