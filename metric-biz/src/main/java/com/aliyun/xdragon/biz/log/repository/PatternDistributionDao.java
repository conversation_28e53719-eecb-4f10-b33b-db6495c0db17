package com.aliyun.xdragon.biz.log.repository;

import java.sql.SQLException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

import com.alibaba.security.SecurityUtil;

import com.aliyun.xdragon.common.generate.log.model.ClusterDistributionDetail;
import com.aliyun.xdragon.common.generate.log.model.LogClusterPattern;
import com.aliyun.xdragon.common.generate.log.model.map.ClusterDistributionDetailCustomMapper;
import com.aliyun.xdragon.common.model.ExtInfo;
import com.aliyun.xdragon.common.model.log.DayTimeRange;
import com.aliyun.xdragon.common.model.log.FieldDetail;
import com.aliyun.xdragon.common.model.log.LogClusterPatternExt;
import com.aliyun.xdragon.service.common.util.DateUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Repository;

import static com.aliyun.xdragon.service.common.util.Checks.nullOrEmpty;

/**
 * <AUTHOR>
 * @date 2022/08/11
 */
@Deprecated
@Repository
public class PatternDistributionDao {
    private static final Logger logger = LoggerFactory.getLogger(PatternDistributionDao.class);

    @Autowired
    private ClusterDistributionDetailCustomMapper customMapper;

    public List<FieldDetail> getTopFieldListWithCondition(Long taskId, List<String> regions, Date start, Date end,
        Date associationDate, String md5,
        Map<String, List<String>> conditionMap, Integer extId, List<String> extValues, int cnt, Boolean useTaskTable) {
        return customMapper.getTopFieldListWithCondition(taskId, regions, start, end,
            associationDate, md5, conditionMap, extId, extValues, cnt, useTaskTable);
    }

    public List<FieldDetail> getTopFieldList(Long taskId, List<String> regions, Date start, Date end, String md5,
        Integer extId, List<String> extValues, int cnt, Boolean useTaskTable) {
        return customMapper.getTopFieldList(taskId, regions, start, end, md5, extId, extValues, cnt,
            useTaskTable);
    }

    public List<FieldDetail> getSpecialTopFieldListWithCondition(Long taskId, List<String> regions, Date start, Date end, Date associationDate, String md5,
        String field, Map<String, List<String>> conditionMap, int cnt, Boolean useTaskTable) {
        return customMapper.getSpecialTopFieldListWithConditon(taskId, regions, start, end,
            associationDate, md5, field, conditionMap, cnt, useTaskTable);
    }

    public List<FieldDetail> getSpecialTopFieldList(Long taskId, List<String> regions, Date start, Date end, Date associationDate, String md5,
        String field, int cnt, Boolean useTaskTable) {
        return customMapper.getSpecialTopFieldList(taskId, regions, start, end, associationDate, md5, field, cnt,
            useTaskTable);
    }

    public List<ExtInfo> getDistinctNcList(Long taskId, Date startTime, Date endTime, boolean useTaskTable) {
        return customMapper.getDistinctNcList(taskId, startTime, endTime, useTaskTable);
    }

    public List<ExtInfo> getExtensionDistribution(Long taskId, Date start, Date end, List<String> regions, Long typeId,
        String patternMd5, boolean useTaskTable) {
        return customMapper.getExtensionDistribution(taskId, start, end, regions, typeId, patternMd5, useTaskTable);
    }

    public List<Pair<String, Long>> getNcDistributionWithLimit(Long taskId, Date start, Date end, Date associationStart,
        Date associationEnd, List<String> regions, String patternMd5, String associationName, Integer limit,
        boolean useTaskTable) {
        List<Map<String, Object>> records = customMapper.getNcDistributionWithTime(taskId, start, end, associationStart,
            associationEnd, regions, patternMd5, SecurityUtil.trimSql(associationName), limit, useTaskTable);
        List<Pair<String, Long>> ncDistributions = new ArrayList<>(records.size());
        for (Map<String, Object> record : records) {
            Object nameObj = record.get("name");
            if (nameObj == null) {
                continue;
            }
            String name = nameObj.toString();
            Long cnt = Long.valueOf(record.get("cnt").toString());
            ncDistributions.add(Pair.of(name, cnt));
        }
        if (ncDistributions.size() == limit) {
            ncDistributions.remove(limit - 1);
        }
        return ncDistributions;
    }

    public List<Map<String, Object>> calNcInfos(Long taskId, List<String> regions, Date start, Date end,
        Collection<String> md5s, boolean includeFlag, boolean useTaskTable) {
        return customMapper.calNcInfos(taskId, regions, start, end, md5s,
            includeFlag, useTaskTable);
    }

    public List<Map<String, Object>> calNcInfosWithCondition(Long taskId, List<String> regions, Date start, Date end,
        Date associationDate, Collection<String> md5s, Map<String, List<String>> conditionMap, List<String> ips,
        boolean includeFlag, boolean useTaskTable) {
        return customMapper.calNcInfosWithCondition(taskId, regions, start, end, associationDate, md5s, ips,
            conditionMap, includeFlag, useTaskTable);
    }

    public List<Map<String, Object>> calNcInfosWithIps(Long taskId, List<String> regions, Date start, Date end,
        Collection<String> md5s, List<String> ips, boolean includeFlag, boolean useTaskTable) {
        return customMapper.calNcInfosWithIps(taskId, regions, start, end, md5s, ips, includeFlag,
            useTaskTable);
    }

    public boolean checkTable(Long taskId) {
        try {
            customMapper.checkTable(taskId);
            return true;
        } catch (BadSqlGrammarException e) {
            SQLException sqle = e.getSQLException();
            if (sqle.getErrorCode() == 1146) {
                return false;
            }
            throw e;
        }
    }

    public void createTable(Long taskId) {
        customMapper.createTable(taskId);
    }

    @Deprecated
    public void sumDetail(Long taskId, Date start, Date end) {
        customMapper.sumDetail(taskId, start, end);
    }

    public Integer deleteDetail(Long taskId, Date start, Date end, boolean useTaskTable) {
        return customMapper.deleteDetail(taskId, start, end, useTaskTable);
    }

    public Integer clearDetail(Long taskId, Date end, Integer size, Boolean useTaskTable) {
        return customMapper.cleanDetail(taskId, end, size, useTaskTable);
    }

    public List<ClusterDistributionDetail> getMd5ByIps(Long taskId, List<String> ips, Date startDate, Date endDate,
                                                       Boolean useTaskTable) {
        return customMapper.getMd5ByIps(taskId, ips, startDate, endDate, useTaskTable);
    }

    public List<String> queryNcByMd5(Long taskId, String md5, Date clusterDate,  Boolean useTaskTable) {
         List<Map<String, Object>> records = customMapper.queryNcByMd5(taskId, md5, clusterDate, useTaskTable);
         return records.stream().map(record -> record.get("nc_ip").toString()).collect(Collectors.toList());
    }

    public Map<String, List<String>> getRegionIpMap(Long taskId, Date start, Date end, List<String> regions,
        List<String> ncList, String md5, boolean useTaskTable) {
        List<Map<String, Object>> records = customMapper.getRegionIpMap(taskId, start, end, regions, ncList, md5,
            useTaskTable);
        Map<String, List<String>> ncRegionIpMap = new HashMap<>(records.size());
        records.forEach(record -> {
            String region = record.get("region").toString();
            String ip = record.get("detail_key").toString();
            if (!ncRegionIpMap.containsKey(region)) {
                ncRegionIpMap.put(region, new ArrayList<>());
            }
            ncRegionIpMap.get(region).add(ip);
        });
        return ncRegionIpMap;
    }

    public Pair<DayTimeRange, List<String>> getMd5sWithConditionByDay(Long taskId, Date start, Date end,
        Date associationDate, List<String> regions, Set<String> patternMd5s, boolean includeFlag,
        Map<String, List<String>> conditionMap, Integer extId, List<String> extValues, boolean useTaskTable) {
        conditionMap = trimCondition(conditionMap);
        boolean withCondition = !nullOrEmpty(conditionMap);
        if (withCondition && !extId.equals(0)) {
            extValues = null;
        }
        List<String> md5s = withCondition ? customMapper.getMd5sWithCondition(taskId, start, end, associationDate,
            regions, patternMd5s, includeFlag, conditionMap, extValues, useTaskTable)
            : customMapper.getDistinctMd5sByValues(taskId, start, end, associationDate, regions, patternMd5s,
                includeFlag, extId, extValues, useTaskTable);
        return nullOrEmpty(md5s) ? null : Pair.of(DayTimeRange.of(start, end), md5s);
    }

    public List<LogClusterPattern> getMd5SumWithConditionByDay(Long taskId, Date start, Date end,
        Date associationStart, List<String> regions, Set<String> patternMd5s, boolean includeFlag,
        Map<String, List<String>> conditionMap, Integer extId, List<String> extValues, boolean useTaskTable) {
        conditionMap = trimCondition(conditionMap);

        boolean withCondition = !nullOrEmpty(conditionMap);
        if (withCondition && !extId.equals(0)) {
            extValues = null;
        }
        List<Map<String, Object>> records = withCondition ? customMapper.getMd5SumWithCondition(taskId, start, end,
            associationStart, regions, patternMd5s, includeFlag, conditionMap, extValues, useTaskTable)
            : customMapper.getMd5SumWithValues(taskId, start, end, associationStart, regions, patternMd5s, includeFlag,
                extId, extValues, useTaskTable);
        List<LogClusterPattern> patternList = new ArrayList<>(records.size());
        for (Map<String, Object> record : records) {
            LogClusterPattern pattern = new LogClusterPattern();
            pattern.setTaskId(taskId);
            pattern.setSupport(Long.valueOf(record.get("sum").toString()));
            pattern.setMd5(record.get("md5").toString());
            patternList.add(pattern);
        }
        return patternList;
    }

    public List<LogClusterPatternExt> listSumMd5WithConditionByDay(Long taskId, Date start, Date end,
        Date associationDate, List<String> regions, List<String> patternMd5, List<String> patternFilterMd5,
        boolean includeFlag, Map<String, List<String>> conditionMap, Integer extId, List<String> extValues,
        boolean useTaskTable) {
        if (patternFilterMd5 != null && patternFilterMd5.isEmpty()) {
            return Collections.emptyList();
        }
        conditionMap = trimCondition(conditionMap);
        boolean withCondition = !nullOrEmpty(conditionMap);
        List<Map<String, Object>> records = withCondition ? customMapper.listSumMd5sWithCondition(taskId, start, end,
            associationDate, regions, patternMd5, patternFilterMd5, includeFlag, conditionMap, extValues, useTaskTable)
            : customMapper.listSumMd5sByValues(taskId, start, end, associationDate, regions, patternMd5,
                patternFilterMd5, includeFlag, extId, extValues, useTaskTable);
        List<LogClusterPatternExt> patternExtList = new ArrayList<>(records.size());
        for (Map<String, Object> record : records) {
            String md5 = record.get("md5").toString();
            LogClusterPattern pattern = new LogClusterPattern();
            pattern.setTaskId(taskId);
            pattern.setSupport(Long.valueOf(record.get("sum").toString()));
            pattern.setMd5(md5);
            LogClusterPatternExt patternExt = new LogClusterPatternExt();
            patternExt.setPattern(pattern);
            patternExt.setMd5(md5);
            patternExt.setStartDate((Date)record.get("min_time"));
            patternExt.setStartMs(patternExt.getStartDate().getTime());
            patternExt.setEndDate((Date)record.get("max_time"));
            patternExt.setEndMs(patternExt.getEndDate().getTime());
            patternExtList.add(patternExt);
        }
        return patternExtList;
    }

    public List<LogClusterPatternExt> listSumMd5WithConditionByDay(Long taskId, Date start, Date end, Date associationDate,
        List<String> idMd5s, List<String> filterIdMd5s, boolean includeFlag, Map<String, List<String>> conditionMap,
                                                                   Integer extId, List<String> extValues, boolean useTaskTable) {
        if (filterIdMd5s != null && filterIdMd5s.isEmpty()) {
            return Collections.emptyList();
        }
        conditionMap = trimCondition(conditionMap);
        boolean withCondition = !nullOrEmpty(conditionMap);
        List<Map<String, Object>> records = withCondition ? customMapper.listSumMd5sWithConditionForMultiTask(taskId, start,
                end, associationDate, idMd5s, filterIdMd5s, includeFlag, conditionMap, extValues, useTaskTable)
            : customMapper.listSumMd5sByValuesForMultiTask(start, end, associationDate, idMd5s, filterIdMd5s,
                includeFlag, extId, extValues);
        List<LogClusterPatternExt> patternExtList = new ArrayList<>(records.size());
        for (Map<String, Object> record : records) {
            String md5 = record.get("md5").toString();
            LogClusterPattern pattern = new LogClusterPattern();
            pattern.setTaskId(taskId);
            pattern.setSupport(Long.valueOf(record.get("sum").toString()));
            pattern.setMd5(md5);
            LogClusterPatternExt patternExt = new LogClusterPatternExt();
            patternExt.setPattern(pattern);
            patternExt.setMd5(md5);
            patternExt.setStartDate((Date)record.get("min_time"));
            patternExt.setStartMs(patternExt.getStartDate().getTime());
            patternExt.setEndDate((Date)record.get("max_time"));
            patternExt.setEndMs(patternExt.getEndDate().getTime());
            patternExtList.add(patternExt);
        }
        return patternExtList;
    }

    public List<ClusterDistributionDetail> getPatternSeriesByType(long taskId, int typeId, List<String> md5s,
        Date start, Date end, List<String> regions, boolean useTaskTable) {
        return customMapper.getPatternSeriesByType(taskId, typeId, md5s, start, end, regions, useTaskTable);
    }

    public List<ClusterDistributionDetail> getPatternSeriesByDim(long taskId, String dim, List<String> md5s,
        Long  startMs, Long endMs, List<String> regions, boolean useTaskTable) {
        Date start = new Date(startMs);
        Date end = new Date(endMs);
        return customMapper.getPatternSeriesByDim(taskId, dim, md5s, start, end, regions, useTaskTable);
    }

    public List<ClusterDistributionDetail> drillByTypes(Long taskId, Date drillTime, String md5, String region,
        Boolean useTaskTable) {
        return customMapper.drillByTypes(taskId, drillTime, md5, region, useTaskTable);
    }

    public List<ClusterDistributionDetail> drillByDim(Long taskId, Date drillTime, String dim, String md5,
        String region, Boolean useTaskTable) {
        Date assStart = new Date(drillTime.getTime() - 86400 * 1000);
        Date assEnd = new Date(drillTime.getTime());
        return customMapper.drillByDim(taskId, drillTime, assStart, assEnd, dim, md5, region, useTaskTable);
    }

    public Date getMd5MinAppearTime(Long taskId, Date start, Date end, String md5, String ip, List<String> regions, Boolean useTaskTable) {
        return customMapper.getMd5MinAppearTime(taskId, start, end, md5, ip, regions, useTaskTable);
    }

    public Date getMd5MaxAppearTime(Long taskId, Date start, Date end, String md5, String ip, List<String> regions, Boolean useTaskTable) {
        return customMapper.getMd5MaxAppearTime(taskId, start, end, md5, ip, regions, useTaskTable);
    }

    public boolean isRecordExist(Long taskId, Date start, Date end, String md5, String ip, List<String> regions, Boolean useTaskTable) {
        List<String> record = customMapper.tryGetRecord(taskId, start, end, md5, ip, regions, useTaskTable);
        return record != null && !record.isEmpty();
    }

    public List<Pair<Integer, Double>> getNcSupportPoints(Long taskId, Date start, Date end, String md5, String ip,
                                                          List<String> regions, Boolean useTaskTable) {
        List<Map<String, Object>> records = customMapper.getNcSupportPoints(taskId, start, end, md5, ip, regions, useTaskTable);
        List<Pair<Integer, Double>> result = new ArrayList<>(records.size());
        for (Map<String, Object> record : records) {
            try {
                Integer ncId = (int)(DateUtil.genDateFromStr(record.get("time").toString()).getTime() / 1000L);
                Double support = Double.valueOf(record.get("value").toString());
                result.add(Pair.of(ncId, support));
            } catch (ParseException e) {
                logger.error("[PatternDistributionDao] Failed to parse date from record {} with cause {}!, e", record,
                    e.getCause(), e);
            }
        }
        return result;
    }

    public List<Pair<String, Long>> getValidNcCnt(Long taskId, Date start, Date end, String region, List<String> ncs,
        String md5, boolean useTaskTable) {
        List<Map<String, Object>> records = customMapper.getValidNcCnt(taskId, start, end, region, ncs, md5, useTaskTable);
        List<Pair<String, Long>> ncCnts = new ArrayList<>(records.size());
        records.forEach(record -> {
            String nc = record.get("detail_key").toString();
            Long cnt = Long.valueOf(record.get("cnt").toString());
            ncCnts.add(Pair.of(nc, cnt));
        });
        return ncCnts;
    }

    public List<Date> getAppearDateList(Long taskId, Date start, Date end, String region, String ip, String md5,
        boolean useTaskTable) {
        return customMapper.getAppearDateList(taskId, start, end, region, md5, ip, useTaskTable);
    }

    public static Map<String, List<String>> trimCondition(Map<String, List<String>> m) {
        if (m == null || m.isEmpty()) {
            return m;
        } else {
            Map<String, List<String>> ret = new HashMap<>();
            for (Entry<String, List<String>> s : m.entrySet()) {
                List<String> trimValues = new ArrayList<>(s.getValue().size());
                s.getValue().forEach(v -> trimValues.add(SecurityUtil.trimSql(v)));
                ret.put(SecurityUtil.trimSql(s.getKey()), trimValues);
            }
            return ret;
        }
    }
}
