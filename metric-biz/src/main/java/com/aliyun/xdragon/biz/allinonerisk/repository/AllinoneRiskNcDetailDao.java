package com.aliyun.xdragon.biz.allinonerisk.repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetail;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetail.Column;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetailExample;
import com.aliyun.xdragon.common.generate.log.model.map.AllinoneRiskNcDetailCustomMapper;
import com.aliyun.xdragon.common.generate.log.model.map.AllinoneRiskNcDetailMapper;
import com.aliyun.xdragon.service.common.util.Checks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class AllinoneRiskNcDetailDao {
    @Autowired
    private AllinoneRiskNcDetailMapper allinoneRiskNcDetailMapper;

    @Autowired
    private AllinoneRiskNcDetailCustomMapper allinoneRiskNcDetailCustomMapper;

    public static final String DEFAULT_VALUE = "UNKNOWN";

    public int batchInsert(List<AllinoneRiskNcDetail> ncDetails) {
        if (ncDetails.isEmpty()) {
            return 0;
        }
        for (AllinoneRiskNcDetail ncDetail: ncDetails) {
            ncDetail.setGmtCreated(new Date());
            ncDetail.setGmtModified(new Date());
        }
        return allinoneRiskNcDetailMapper.batchInsert(ncDetails);
    }

    public int batchUpsert(List<AllinoneRiskNcDetail> ncDetails) {
        if (ncDetails.isEmpty()) {
            return 0;
        }
        for (AllinoneRiskNcDetail ncDetail: ncDetails) {
            ncDetail.setGmtModified(new Date());
        }
        return allinoneRiskNcDetailCustomMapper.batchUpsert(ncDetails);
    }

    public List<AllinoneRiskNcDetail> listRiskNcDetail(Long riskId, Integer offset, Integer limit) {
        AllinoneRiskNcDetailExample e = new AllinoneRiskNcDetailExample();
        AllinoneRiskNcDetailExample.Criteria c = e.createCriteria();
        c.andRiskIdEqualTo(riskId);
        if (offset != null && limit != null) {
            e.setOrderByClause("nc_detail_id limit " + offset + "," + limit);
        }
        return allinoneRiskNcDetailMapper.selectByExample(e);
    }

    public int queryNcCntByRiskIds(List<Long> riskIds) {
        if (Checks.nullOrEmpty(riskIds)) {
            return 0;
        }
        return allinoneRiskNcDetailCustomMapper.queryNcCntByRiskIds(riskIds);
    }

    public List<AllinoneRiskNcDetail> queryNcDetailByRiskIds(List<Long> riskIds, Integer offSet, Integer pageSize) {
        if (Checks.nullOrEmpty(riskIds)) {
            return new ArrayList<>();
        }
        return allinoneRiskNcDetailCustomMapper.queryNcDetailByRiskIds(riskIds, offSet, pageSize);
    }

    public int deleteByRiskId(Long riskId) {
        AllinoneRiskNcDetailExample e = new AllinoneRiskNcDetailExample();
        AllinoneRiskNcDetailExample.Criteria c = e.createCriteria();
        c.andRiskIdEqualTo(riskId);
        return allinoneRiskNcDetailMapper.deleteByExample(e);
    }

    public int deleteByDetailIds(List<Long> detailIds) {
        if (Checks.nullOrEmpty(detailIds)) {
            return 0;
        }
        AllinoneRiskNcDetailExample e = new AllinoneRiskNcDetailExample();
        AllinoneRiskNcDetailExample.Criteria c = e.createCriteria();
        c.andNcDetailIdIn(detailIds);
        return allinoneRiskNcDetailMapper.deleteByExample(e);
    }

    public List<String> getNcIpsByRiskId(Long riskId) {
        AllinoneRiskNcDetailExample e = new AllinoneRiskNcDetailExample();
        AllinoneRiskNcDetailExample.Criteria c = e.createCriteria();
        c.andRiskIdEqualTo(riskId);
        return allinoneRiskNcDetailMapper.selectByExampleSelective(e, Column.ncIp).stream()
            .map(AllinoneRiskNcDetail::getNcIp).collect(Collectors.toList());
    }

    public AllinoneRiskNcDetail getNcDetailFromMap(Map<String, String> mapInfo) {
        AllinoneRiskNcDetail ncDetail = new AllinoneRiskNcDetail();
        if (mapInfo.containsKey("nc_detail_id")) {
            ncDetail.setNcDetailId(Long.parseLong(mapInfo.get("nc_detail_id")));
        }
        ncDetail.setNcIp(mapInfo.get("nc_ip"));
        ncDetail.setNcId(mapInfo.getOrDefault("nc_id", DEFAULT_VALUE));
        ncDetail.setNcSn(mapInfo.getOrDefault("nc_sn", DEFAULT_VALUE));
        ncDetail.setHostname(mapInfo.getOrDefault("hostname", DEFAULT_VALUE));
        ncDetail.setCluster(mapInfo.getOrDefault("cluster", DEFAULT_VALUE));
        ncDetail.setClusterUsage(mapInfo.getOrDefault("cluster_usage", DEFAULT_VALUE));
        ncDetail.setAzone(mapInfo.getOrDefault("azone", DEFAULT_VALUE));
        ncDetail.setRegion(mapInfo.getOrDefault("region", DEFAULT_VALUE));
        ncDetail.setIdc(mapInfo.getOrDefault("idc", DEFAULT_VALUE));
        ncDetail.setRoom(mapInfo.getOrDefault("room", DEFAULT_VALUE));
        ncDetail.setRack(mapInfo.getOrDefault("rack", DEFAULT_VALUE));
        ncDetail.setAswId(mapInfo.getOrDefault("asw_id", DEFAULT_VALUE));
        ncDetail.setVcpuMod(mapInfo.getOrDefault("vcpu_mod", DEFAULT_VALUE));
        ncDetail.setPhysicalModel(mapInfo.getOrDefault("physical_model", DEFAULT_VALUE));
        ncDetail.setCpuModel(mapInfo.getOrDefault("cpu_model", DEFAULT_VALUE));
        ncDetail.setNcVcpu(mapInfo.getOrDefault("nc_vcpu", DEFAULT_VALUE));
        ncDetail.setAvsVersion(mapInfo.getOrDefault("avs_version", DEFAULT_VALUE));
        ncDetail.setNcCategory(mapInfo.getOrDefault("nc_category", DEFAULT_VALUE));
        ncDetail.setDateOutwarranty(mapInfo.getOrDefault("date_outwarranty", DEFAULT_VALUE));
        ncDetail.setDatePurchase(mapInfo.getOrDefault("date_purchase", DEFAULT_VALUE));
        ncDetail.setManufacturer(mapInfo.getOrDefault("manufacturer", DEFAULT_VALUE));
        ncDetail.setProductName(mapInfo.getOrDefault("product_name", DEFAULT_VALUE));
        ncDetail.setVirtType(mapInfo.getOrDefault("virt_type", DEFAULT_VALUE));
        ncDetail.setRiverType(mapInfo.getOrDefault("river_type", DEFAULT_VALUE));
        ncDetail.setNetworkType(mapInfo.getOrDefault("network_type", DEFAULT_VALUE));
        ncDetail.setStorageNetworkType(mapInfo.getOrDefault("storage_network_type", DEFAULT_VALUE));
        ncDetail.setStorageType(mapInfo.getOrDefault("storage_type", DEFAULT_VALUE));
        ncDetail.setCpuGeneration(mapInfo.getOrDefault("cpu_generation", DEFAULT_VALUE));
        ncDetail.setOnEcs(mapInfo.getOrDefault("on_ecs", DEFAULT_VALUE));
        ncDetail.setExceptionName(mapInfo.getOrDefault("exception_name", DEFAULT_VALUE));
        ncDetail.setExceptionTime(mapInfo.getOrDefault("exception_time", DEFAULT_VALUE));
        if (mapInfo.containsKey("recover_time")) {
            ncDetail.setRecoverTime(mapInfo.get("recover_time"));
        }
        return ncDetail;
    }
}
