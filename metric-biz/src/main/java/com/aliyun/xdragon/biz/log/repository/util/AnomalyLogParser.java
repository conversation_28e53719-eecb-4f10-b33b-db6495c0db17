package com.aliyun.xdragon.biz.log.repository.util;

import java.util.ArrayList;

import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.xdragon.common.model.MetricAnomalyData;
import com.aliyun.xdragon.service.common.util.Checks;
import com.aliyun.xdragon.service.common.util.DateUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class AnomalyLogParser<T extends MetricAnomalyData> {
    Class<T> tClass;

    public AnomalyLogParser(Class<T> tClass) {
        this.tClass = tClass;
    }

    @SneakyThrows
    public T parse(QueriedLog log) {
        T data = tClass.newInstance();
        ArrayList<LogContent> contents = log.GetLogItem().GetLogContents();
        for (LogContent c : contents) {
            // 如果原始数据为null或空则跳过
            if (Checks.nullOrEmpty(c.GetValue()) || "null".equals(c.GetValue())) {
                continue;
            }
            if ("anomaly".equals(c.GetKey())) {
                data.setAnomaly(c.GetValue());
            } else if ("metric".equals(c.GetKey())) {
                data.setMetric(c.GetValue());
            } else if ("metricName".equalsIgnoreCase(c.GetKey()) && StringUtils.isBlank(data.getMetric())) {
                data.setMetric(c.GetValue());
            } else if ("timestamp".equals(c.GetKey())) {
                data.setTimestamp(Integer.parseInt(c.GetValue()));
            } else if ("value".equals(c.GetKey())) {
                data.setValue(Double.parseDouble(c.GetValue()));
            } else if ("extremumValue".equalsIgnoreCase(c.GetKey())) {
                data.setExtremumValue(Double.parseDouble(c.GetValue()));
            } else if ("anomalyValue".equalsIgnoreCase(c.GetKey())) {
                data.setAnomalyValue(Double.parseDouble(c.GetValue()));
            } else if ("startTimestamp".equalsIgnoreCase(c.GetKey())) {
                data.setStartTimestamp(Integer.parseInt(c.GetValue()));
            } else if ("breakPointNum".equalsIgnoreCase(c.GetKey())) {
                data.setBreakPointNum(Integer.parseInt(c.GetValue()));
            } else if ("additional".equals(c.GetKey())) {
                data.setAdditional(c.GetValue());
            } else if ("sourceId".equalsIgnoreCase(c.GetKey()) && !Checks.nullOrEmpty(c.GetValue())) {
                data.setSourceId(Long.parseLong(c.GetValue()));
            } else if ("breakPointScore".equalsIgnoreCase(c.GetKey()) && !Checks.nullOrEmpty(c.GetValue())) {
                data.setBreakPointScore(Double.parseDouble(c.GetValue()));
            } else if ("trendScore".equalsIgnoreCase(c.GetKey()) && !Checks.nullOrEmpty(c.GetValue())) {
                data.setTrendScore(Double.parseDouble(c.GetValue()));
            } else if ("volatilityScore".equalsIgnoreCase(c.GetKey()) && !Checks.nullOrEmpty(c.GetValue())) {
                data.setVolatilityScore(Double.parseDouble(c.GetValue()));
            } else if ("anomalyTime".equalsIgnoreCase(c.GetKey())) {
                data.setAnomalyTime(c.GetValue());
                if (data.getStartTimestamp() == null) {
                    data.setStartTimestamp(DateUtil.getTimestamp(c.GetValue()));
                }
            } else if ("alarmTime".equalsIgnoreCase(c.GetKey())) {
                data.setAlarmTime(c.GetValue());
                if (data.getTimestamp() == null) {
                    data.setTimestamp(DateUtil.getTimestamp(c.GetValue()));
                }
            } else if ("weight".equalsIgnoreCase(c.GetKey()) && !Checks.nullOrEmpty(c.GetValue())) {
                data.setWeight(Double.parseDouble(c.GetValue()));
            }
        }
        return data;
    }
}
