package com.aliyun.xdragon.biz.log.job.mail;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/02/01
 */
@Service
public class MailHandlerDispatcher {

    private Map<String, MailHandler> handlers = new HashMap<>();

    public void addHandler(String name, MailHandler handler) {
        handlers.put(name, handler);
    }

    public MailHandler getHandler(String name) {
        return handlers.get(name);
    }

    public List<MailHandler> getHandlerList() {
        return new LinkedList<>(handlers.values());
    }

    public List<String> getHandlerNameList() {
        return new LinkedList<>(handlers.keySet());
    }
}
