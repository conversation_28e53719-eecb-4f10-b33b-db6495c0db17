package com.aliyun.xdragon.biz.integration.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/9/1
 */
public class Constant {

    @Getter
    public enum ALERT_RULE_KEY_ENUM {

        /**
         * 资源数量
         */
        RESOURCE_COUNT,

        /**
         * 日志数量
         */
        LOG_COUNT,
        ;
    }

    public static class AlertSignConstant {
        public static final String EQUAL = "=";
        public static final String NOT_EQUAL = "!=";
        public static final String GREATER_THAN = ">";
        public static final String LESS_THAN = "<";
        public static final String GREATER_OR_EQUAL_THAN = ">=";
        public static final String LESS_OR_EQUAL_THAN = "<=";
    }

    public static final Byte MONITOR_INFO_ENABLE = 0;
    public static final Byte MONITOR_INFO_DISABLE = 1;
}
