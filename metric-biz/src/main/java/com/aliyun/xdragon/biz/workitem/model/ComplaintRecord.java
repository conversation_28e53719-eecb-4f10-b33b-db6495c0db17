package com.aliyun.xdragon.biz.workitem.model;

import com.aliyun.xdragon.service.common.util.Checks;
import com.aliyun.xdragon.service.common.util.DataCleaningUtil;
import com.google.gson.Gson;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.beans.PropertyDescriptor;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分类的统一接口数据，不管是工单、钉群消息还是goc人工上报的故障，建议统一转为该实体类，用于问题分类
 * <AUTHOR>
 */
@Data
public class ComplaintRecord implements Serializable {

    private static final long serialVersionUID = -8377625614903462389L;
    /**
     * 投诉的产品线
     */
    private String product;

    /**
     * 记录的标题
     */
    private String subject;

    /**
     * 记录的正文描述
     */
    private String description;

    /**
     * 记录的评论信息
     */
    private String comments;

    /**
     * 用来分类的语料
     */
    private String corpus;

    /**
     * 记录归属的项目
     */
    private String project;

    /**
     * 归属项目的id
     */
    private String projectId;

    /**
     * 记录涉及的实例信息
     */
    private List<MachineInfo> instanceIdList;

    /**
     * 记录分类的结果
     */
    private String label;

    /**
     * 记录的url（如果有的话）
     */
    private String url;

    /**
     * 记录id
     */
    private String id;

    /**
     * 记录创建的时间
     */
    private String time;

    /**
     * 通知人
     */
    private String assignTo;

    /**
     * 记录来源
     */
    private DataSourceEnum source;

    /**
     * 追踪该问题的aone的url
     */
    private String aone;

    /**
     * 关联上的运维规则
     */
    private String rule;

    /**
     * 运维的时间
     */
    private String opsTime;

    /**
     * 二级标签
     */
    private Set<String> tags;

    /**
     * 是否为模型分类
     */
    private Boolean isModelClassified;

    /**
     * 大模型分类结果
     */
    private Map<String, String> llmClassification;

    /**
     * 小助手回答
     */
    private List<Map<String, String>> agentChatMessage;

    public ComplaintRecord() {
        this.tags = new HashSet<>();
        this.isModelClassified = false;
        this.llmClassification = new HashMap<>();
    }

    public ComplaintRecord(String product, String subject, String description, String comments, String corpus, String project, List<MachineInfo> instanceIdList, String label, String url, String id, String time, DataSourceEnum source, String aone) {
        this.product = product;
        this.subject = subject;
        this.description = description;
        this.comments = comments;
        this.corpus = corpus;
        this.project = project;
        this.instanceIdList = instanceIdList;
        this.label = label;
        this.url = url;
        this.id = id;
        this.time = time;
        this.source = source;
        this.aone = aone;
        this.tags = new HashSet<>();
        this.isModelClassified = false;
        this.llmClassification = new HashMap<>();
    }


    @Override
    public String toString() {
        String instanceInfo = "";
        if (instanceIdList != null) {
            instanceInfo = instanceIdList.toString();
        }
        String dataSourceInfo = "";
        if (source != null) {
            dataSourceInfo = source.getName();
        }
        StringBuilder chatMessage = new StringBuilder();
        if (agentChatMessage != null) {
            for (Map<String, String> map : agentChatMessage) {
                chatMessage.append("<br>").append(map.get("sender")).append(": ").append(map.get("message"));
            }
        }
        // aone description的换行需要使用html的写法：<br></br>
        return  String.format("<br>product: %s</br>", product) +
                String.format("<br>subject: %s</br>", subject) +
                String.format("<br>description: %s</br>", description) +
                String.format("<br>comments: %s</br>", comments) +
                String.format("<br>project: %s</br>", project) +
                String.format("<br>projectId: %s</br>", projectId) +
                String.format("<br>instanceIdList: %s</br>", instanceInfo) +
                String.format("<br>label: %s</br>", label) +
                String.format("<br>llmClassification: %s</br>", llmClassification.toString()) +
                String.format("<br>isModelClassified: %s</br>", isModelClassified) +
                String.format("<br>原aone链接: <a href=%s>%s</a></br>", url, url) +
                String.format("<br>id: %s</br>", id) +
                String.format("<br>time: %s</br>", time) +
                String.format("<br>dataSource: %s</br>", dataSourceInfo) +
                String.format("<br>rule: %s</br>", rule) +
                String.format("<br>opsTime: %s</br>", opsTime) +
                String.format("<br>tag: %s</br>", tags) +
                String.format("<br>chatMessage: %s</br>", chatMessage) +
                "<br>知识库问题： </br>" +
                "<br>知识库答案： </br>";

    }

    /**
     * 根据传入的文本内容，提取设备id
     * @param text 文本内容
     */
    public void createMachineInfo(String text) {
        int sizeThreshold = 10;
        if (text != null && !text.isEmpty()){
            List<String> instanceList = DataCleaningUtil.extractInstanceIds(text);
            if (instanceList.size() > sizeThreshold) {
                instanceList = instanceList.subList(0, sizeThreshold);
            }
            if (!instanceList.isEmpty()) {
                List<MachineInfo> machineInfoList = instanceList.stream().map(id->new MachineInfo(id)).collect(Collectors.toList());
                this.setInstanceIdList(machineInfoList);
            }
        }
    }

    /**
     * 将ComplaintRecord的信息转为map
     * @return 转为map后的结果，如果有多个实例信息，则会打平，返回多个map
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    public List<Map<String, String>> convertInfo() throws InvocationTargetException, IllegalAccessException {
        List<Map<String, String>> infoList = new ArrayList<>();
        PropertyDescriptor[] propertyDescriptors = BeanUtils.getPropertyDescriptors(this.getClass());
        Map<String, String> properties = new HashMap<>();
        for(PropertyDescriptor descriptor : propertyDescriptors) {
            String propertyName = descriptor.getName();
            Method readMethod = descriptor.getReadMethod();
            Object value = readMethod.invoke(this);
            if (String.class.equals(descriptor.getPropertyType()) || Boolean.class.equals(descriptor.getPropertyType())) {
                if (value != null) {
                    properties.put(propertyName,value.toString());
                } else {
                    properties.put(propertyName, "");
                }
            }
        }
        List<MachineInfo> machineInfoList = new ArrayList<>();
        if (!Checks.nullOrEmpty(instanceIdList)) {
            machineInfoList = instanceIdList;
        } else {
            MachineInfo machineInfo = new MachineInfo("", new HashMap<String, String>(){{put("rc", "根因");}});
            machineInfoList.add(machineInfo);
        }
        String sourceName = "";
        if (source != null) {
            sourceName = source.getName();
        }
        Gson gson = new Gson();
        for (int j = 0; j < machineInfoList.size(); j++) {
            MachineInfo machineInfo = machineInfoList.get(j);
            Map<String, String> info = new HashMap<>();
            info.put("machineId", machineInfo.getMachineId());
            info.put("instanceId", machineInfo.getMachineId());
            info.put("reason",machineInfo.getReason().toString());
            info.put("source", sourceName);
            info.put("host_name", machineInfo.getHostName());
            info.put("ali_uid", machineInfo.getAliUid());
            info.put("is_mix", machineInfo.getIsMix().toString());
            info.put("is_over_sale", machineInfo.getIsOverSale().toString());
            info.put("instance_family", machineInfo.getInstanceTypeFamily());
            info.put("nc_ip", machineInfo.getNcIp());
            info.put("product_name", machineInfo.getProductName());
            info.put("vcpu_mod", machineInfo.getVcpuMod());
            if (machineInfo.getCores() != null) {
                info.put("cores", machineInfo.getCores().toString());
            }
            if (machineInfo.getIsLocalDisk() != null) {
                info.put("is_local_disk", machineInfo.getIsLocalDisk().toString());
            }
            info.put("cluster", machineInfo.getCluster());
            info.put("region", machineInfo.getRegion());
            info.put("deploy_operator", gson.toJson(Collections.singletonList(assignTo)));
            for (String property:properties.keySet()) {
                info.put(property, properties.get(property));
            }
            infoList.add(info);
        }
        return infoList;
    }

    /**
     * 填充ComplaintRecord的属性
     * @param values
     * @return ComplaintRecord
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    public ComplaintRecord fillValue(Map<String, Object> values) throws InvocationTargetException, IllegalAccessException {
        PropertyDescriptor[] propertyDescriptors = BeanUtils.getPropertyDescriptors(this.getClass());
        for(PropertyDescriptor descriptor : propertyDescriptors) {
            String propertyName = descriptor.getName();
            if (values.containsKey(propertyName)) {
                Method writeMethod = descriptor.getWriteMethod();
                writeMethod.invoke(this, values.get(propertyName));
            }
        }
        return this;
    }

    public void addAgentChatMessage(Map<String, String> message) {
        if (agentChatMessage == null) {
            agentChatMessage = new ArrayList<>();
        }
        agentChatMessage.add(message);
    }

    public void addTag(String tag) {
        tags.add(tag);
    }

    public void appendDescription(String description) {
        if (this.description == null) {
            this.description = description;
        } else {
            if (!this.description.endsWith("<br>")) {
                this.description += "<br>";
            }
            this.description += description;
        }
    }

    public boolean isValidCorpus() {
        corpus = Checks.nullOrEmpty(corpus) ? "" : corpus.trim();
        return corpus.length() > 2;
    }

    public boolean isGammaOrder() {
        if (Checks.nullOrEmpty(instanceIdList)) {
            return false;
        }
        for (MachineInfo machineInfo: instanceIdList) {
            if (machineInfo.getIsGamma() != null && machineInfo.getIsGamma()) {
                return true;
            }
        }
        return false;
    }
}
