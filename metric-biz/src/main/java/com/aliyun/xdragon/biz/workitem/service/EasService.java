package com.aliyun.xdragon.biz.workitem.service;

import com.aliyun.openservices.eas.predict.response.JsonResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface EasService {


    /**
     * 调用eas模型进行predict
     * @param corpus 预测语料
     * @return List<String>
     */
    <T> List<String> predict(List<T> corpus) throws Exception;

    <T> List<String> predict(List<T> corpus, Map<String, String> params) throws Exception;
}
