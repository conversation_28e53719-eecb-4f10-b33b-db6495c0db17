package com.aliyun.xdragon.biz.chat.job;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.List;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.xdragon.biz.chat.repository.ChatTestOssDao;
import com.aliyun.xdragon.service.common.job.AbstractProcessTask;
import com.csvreader.CsvWriter;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/01/10
 */
@Service
@Lazy
public class AiAgentTestSyncJob extends AbstractProcessTask {

    private static final Logger logger = LoggerFactory.getLogger(AiAgentTestSyncJob.class);

    @Autowired
    private ChatTestOssDao ossDao;

    @Override
    public ProcessResult process(JobContext context, int dataTs, int schedTs) throws Exception {
        // 创建csv并写表头
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        CsvWriter csvWriter = new CsvWriter(outputStream, ',', StandardCharsets.UTF_8);
        csvWriter.setForceQualifier(true);
        csvWriter.writeRecord(new String[] {"query", "expectAnswer", "testId"});
        // 遍历oss文件
        List<OSSObjectSummary> ossFiles = ossDao.listFiles("AiAgentTest", 1000);
        logger.info("{} ossFiles are found.", ossFiles.size());
        for (OSSObjectSummary ossFile : ossFiles) {
            String key = ossFile.getKey();
            if (!key.endsWith(".md")) {
                continue;
            }
            logger.info("{} is processing.", key);
            try (InputStream in = ossDao.getFileInputStreamUnsafe(key)) {
                String[] lines = IOUtils.toString(in, Charset.defaultCharset()).split("\n");
                int inputId = searchWithPrefix(lines, "## Input");
                int outputId = searchWithPrefix(lines, "## Output");
                int mockId = searchWithPrefix(lines, "## Mock");
                // 写入csv
                csvWriter.writeRecord(new String[] {
                    extract(lines, inputId + 1, outputId),
                    extract(lines, outputId + 1, mockId),
                    key.substring(key.indexOf('/') + 1, key.indexOf('.'))
                });
            } catch (Exception e) {
                // 处理异常情况
                logger.error("{} is not valid.", key, e);
            }
        }
        csvWriter.close();
        // 上传到oss
        ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        ossDao.uploadFile("diagnosisAid/source/baiLianAgentChat.csv", inputStream);
        return new ProcessResult(true);
    }

    private String extract(String[] lines, int from, int to) {
        StringBuilder sb = new StringBuilder();
        for (int i = from; i < to; i++) {
            if (!StringUtils.isBlank(lines[i])) {
                sb.append(lines[i]).append("\n");
            }
        }
        return sb.toString();
    }

    private int searchWithPrefix(String[] lines, String prefix) {
        for (int i = 0; i < lines.length; i++) {
            if (lines[i].startsWith(prefix)) {
                return i;
            }
        }
        throw new RuntimeException(prefix + " is not found.");
    }

}
