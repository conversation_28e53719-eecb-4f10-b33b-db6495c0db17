package com.aliyun.xdragon.biz.log.manager.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/07/18
 */
@Configuration
public class AdbSinkConfig {
    @Value("${spring.adb.datasource.jdbc-url-short}")
    private String url;
    @Value("${spring.adb.datasource.username}")
    private String username;
    @Value("${spring.adb.datasource.password}")
    private String password;
    @Value("${spring.adb.datasource.pattern-table}")
    private String patternTableName;
    @Value("${spring.adb.datasource.detail-table}")
    private String detailTableName;

    public String getUrl() {
        return url;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public String getPatternTableName() {
        return patternTableName;
    }

    public String getDetailTableName() {
        return detailTableName;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public void setPatternTableName(String patternTableName) {
        this.patternTableName = patternTableName;
    }

    public void setDetailTableName(String detailTableName) {
        this.detailTableName = detailTableName;
    }

    @Override
    public String toString() {
        return "AdbConfig{" +
            "url='" + url + '\'' +
            ", username='" + username + '\'' +
            ", password='" + password + '\'' +
            ", patternTableName='" + patternTableName + '\'' +
            ", detailTableName='" + detailTableName + '\'' +
            '}';
    }
}
