package com.aliyun.xdragon.biz.external.job;

import com.alibaba.cloudops.api.SkylineService;
import com.alibaba.cloudops.model.skyline.SkylineDO;
import com.alibaba.cloudops.result.CloudOpsResult;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.odps.data.Record;
import com.aliyun.xdragon.service.common.agent.OdpsClient;
import com.aliyun.xdragon.service.common.config.IdnsConfig;
import com.aliyun.xdragon.service.common.job.AbstractProcessTask;
import com.aliyun.xdragon.service.common.service.HttpService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.google.common.util.concurrent.RateLimiter;

import java.nio.ByteBuffer;
import java.util.regex.Pattern;
import java.net.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/09/08
 */
@Service
@Lazy
public class CloudboxRulePeriodSyncJob extends AbstractProcessTask {
    private static final Logger logger = LoggerFactory.getLogger(CloudboxRulePeriodSyncJob.class);

    private static final String SUCCESS_RESULT_CODE = "000000";

    // 主键：(server_addr, server_port)
    private static final String CACHE_TABLE = "dedicated_line_audit_rule_cache_table";
    // 主键：server_addr
    private static final String CACHE_TABLE2 = "dedicated_line_audit_rule_cache_table2";
    // 主键：server_port
    private static final String CACHE_TABLE3 = "dedicated_line_audit_rule_cache_table3";

    private static final String PARTITION = "car_cloud";

    public static final int ODPS_PAGE_SIZE = 1000;

    private static final String APP_GROUP_NAME = "appGroupName";

    @Autowired
    private OdpsClient odpsClient;

    @Autowired
    private IdnsConfig idnsConfig;

    @Autowired
    private HttpService httpClient;

    @Autowired(required = false)
    private SkylineService skylineService;

    @Override
    public String getTaskName() {
        return "cloudbox public original rule period sync";
    }

    @Override
    public ProcessResult process(JobContext context, int dataTs, int schedTs) throws Exception {
        int pageNum = 0;
        List<Record> records = getOdpsRecords(pageNum);
        if (records == null || records.size() == 0) {
            logger.warn("cloudbox period sync job get 0 record from odps");
            return new ProcessResult(true, "there are no metirc period length records in odps");
        }

        List<JSONObject> tmpRecords = new ArrayList<>();
        List<JSONObject> tmpRecords2 = new ArrayList<>();
        List<JSONObject> tmpRecords3 = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String nowTime = formatter.format(calendar.getTime());
        RateLimiter rateLimiter = RateLimiter.create(10.0);
        Set<String> oxsIpSet = new HashSet<>();
        Set<String> oxsPortSet = new HashSet<>();
        while (records.size() > 0) {
            for (Record record : records) {
                String oxs = record.getString("server_addr");
                String oxsPort = record.getString("server_port");
                String[] oxsPortArr = oxsPort.contains(";") ? oxsPort.split(";") : oxsPort.split(",");
                List<String> oxsPortList = Arrays.stream(oxsPortArr).map(String::trim).collect(Collectors.toList());
                if ("*".equals(oxs)) {
                    if ("*".equals(oxsPort)) {
                        logger.warn("cloudbox period sync job invalid (server_addr, server_port): (*, *)");
                        continue;
                    }
                    for (String p: oxsPortList) {
                        if (oxsPortSet.contains(p)) {
                            logger.warn("cloudbox period sync job duplicated server_port: {}", p);
                            continue;
                        }
                        oxsPortSet.add(p);
                        JSONObject tmpObject = new JSONObject();
                        tmpObject.put("comm_id", record.getString("comm_id"));
                        tmpObject.put("client_addr", record.getString("client_addr"));
                        tmpObject.put("client_port", record.getString("client_port"));
                        tmpObject.put("direction", record.getString("direction"));
                        tmpObject.put("protocol", record.getString("protocol"));
                        tmpObject.put("server_addr", oxs);
                        tmpObject.put("server_port", p);
                        tmpObject.put("vlan_id", record.getString("vlan_id"));
                        tmpObject.put("update_time", nowTime);
                        tmpRecords3.add(tmpObject);
                    }
                    continue;
                }

                String[] oxsArr = oxs.contains(";") ? oxs.split(";") : oxs.split(",");
                List<String> oxsList = Arrays.stream(oxsArr).map(String::trim).collect(Collectors.toList());
                List<String> newOxsList = new ArrayList<>();
                for (String o : oxsList) {
                    if (isValidSubnet(o)) {
                        List<String> addrList = listSubnetIPs(o);
                        newOxsList = Stream.concat(newOxsList.stream(), addrList.stream()).collect(Collectors.toList());
                    } else if (isValidIPAddress(o)) {
                        newOxsList.add(o);
                    } else if (o.contains(".")) {
                        // domain
                        rateLimiter.acquire();
                        List<String> addrList = getRecordFromIdns(o);
                        newOxsList = Stream.concat(newOxsList.stream(), addrList.stream()).collect(Collectors.toList());
                    } else {
                        // app group name
                        List<String> addrList = listServerWithAppGroup(o);
                        newOxsList = Stream.concat(newOxsList.stream(), addrList.stream()).collect(Collectors.toList());
                   }
                }
                if (newOxsList.isEmpty()) {
                    logger.warn("cloudbox period sync job invalid server_addr: {}", oxs);
                    continue;
                }

                for (String o : newOxsList) {
                    if (oxsIpSet.contains(o)) {
                        logger.warn("cloudbox period sync job duplicated server_addr: {}", o);
                        continue;
                    }
                    oxsIpSet.add(o);
                    for (String p : oxsPortList) {
                        JSONObject tmpObject = new JSONObject();
                        tmpObject.put("comm_id", record.getString("comm_id"));
                        tmpObject.put("client_addr", record.getString("client_addr"));
                        tmpObject.put("client_port", record.getString("client_port"));
                        tmpObject.put("direction", record.getString("direction"));
                        tmpObject.put("protocol", record.getString("protocol"));
                        tmpObject.put("server_addr", o);
                        tmpObject.put("server_port", p);
                        tmpObject.put("vlan_id", record.getString("vlan_id"));
                        tmpObject.put("update_time", nowTime);
                        if ("*".equals(p)) {
                            tmpRecords2.add(tmpObject);
                        } else {
                            tmpRecords.add(tmpObject);
                        }
                    }
                }
            }

            if (records.size() < ODPS_PAGE_SIZE) {
                break;
            }
            pageNum++;
            records = getOdpsRecords(pageNum);
        }

        if (!tmpRecords.isEmpty()) {
            odpsClient.batchInsert(CACHE_TABLE, "cloud=" + PARTITION, tmpRecords);
        }
        if (!tmpRecords2.isEmpty()) {
            odpsClient.batchInsert(CACHE_TABLE2, "cloud=" + PARTITION, tmpRecords2);
        }
        if (!tmpRecords3.isEmpty()) {
            odpsClient.batchInsert(CACHE_TABLE3, "cloud=" + PARTITION, tmpRecords3);
        }
        removeOldOdpsRecords(nowTime);

        return new ProcessResult(true);
    }

    List<Record> getOdpsRecords(int pageNum) {
        // 每次返回1000条
        String sql = "SELECT comm_id,client_addr,client_port,direction,protocol,server_addr,server_port,vlan_id\n" +
                "     FROM   ecs_dw.dedicated_line_audit_rule_table\n" +
                "     WHERE  cloud = '%s'\n" +
                "     LIMIT %s,%s;";
        return odpsClient.runSql(String.format(sql, PARTITION, pageNum * ODPS_PAGE_SIZE, ODPS_PAGE_SIZE));
    }

    void removeOldOdpsRecords(String updateTime) {
        String sql = String.format("delete from ecs_dw.%s where cloud='%s' and update_time!='%s';", CACHE_TABLE, PARTITION, updateTime);
        odpsClient.runSql(sql);
        sql = String.format("delete from ecs_dw.%s where cloud='%s' and update_time!='%s';", CACHE_TABLE2, PARTITION, updateTime);
        odpsClient.runSql(sql);
        sql = String.format("delete from ecs_dw.%s where cloud='%s' and update_time!='%s';", CACHE_TABLE3, PARTITION, updateTime);
        odpsClient.runSql(sql);
    }

    boolean isValidSubnet(String subnet) {
        try {
            String[] parts = subnet.split("/");
            if (parts.length < 2) {
                return false;
            }
            Integer.parseInt(parts[1]);
            return isValidIPAddress(parts[0]);
        } catch (Exception e) {
            return false;
        }
    }

    boolean isValidIPAddress(String ipAddress) {
        String ipPattern = "^((\\d{1,3}\\.){3}\\d{1,3})$";
        return Pattern.matches(ipPattern, ipAddress);
    }

    int byteArrayToInt(byte[] bytes) {
        int value = 0;
        for (byte b : bytes) {
            value = (value << 8) | (b & 0xFF);
        }
        return value;
    }

    List<String> listServerWithAppGroup(String appGroupName) {
        List<String> ipList = new ArrayList<>();
        try {
            CloudOpsResult<List<SkylineDO>> skylineResult = skylineService.queryAllServerWhitCondition(APP_GROUP_NAME, appGroupName);
            if (!skylineResult.isSuccess()) {
                return ipList;
            }
            for (SkylineDO sDO: skylineResult.getValue()) {
                if (appGroupName.equals(sDO.getAppGroupName())) {
                    ipList.add(sDO.getIp());
                }
            }
        } catch (Exception e) {
            logger.warn("cloudbox period sync job failed to list server with app_group_name: {}", appGroupName);
        }
        return ipList;
    }

    List<String> listSubnetIPs(String subnet) {
        List<String> ipList = new ArrayList<>();

        try {
            String[] parts = subnet.split("/");
            String networkAddress = parts[0];
            int prefixLength = Integer.parseInt(parts[1]);
            int subnetMaskInt = 0xFFFFFFFF << (32 - prefixLength);
            InetAddress subnetAddress = InetAddress.getByName(networkAddress);
            byte[] subnetBytes = subnetAddress.getAddress();
            int subnetInt = byteArrayToInt(subnetBytes) & subnetMaskInt;
            byte[] networkAddressBytes = ByteBuffer.allocate(4).putInt(subnetInt).array();

            int numIPs = (int) Math.pow(2, 32 - prefixLength);
            for (int i = 0; i < numIPs; i++) {
                int[] ipBytes = new int[4];
                for (int j = 0; j < 4; j++) {
                    ipBytes[j] = networkAddressBytes[j] + (i >> (24 - (8 * j))) & 0xFF;
                }
                String ip = ipBytes[0] + "." + ipBytes[1] + "." + ipBytes[2] + "." + ipBytes[3];
                ipList.add(ip);
            }
        } catch (Exception e) {
            logger.warn("cloudbox period sync job failed to parse subnet: {}", subnet);
        }

        return ipList;
    }

    List<String> getRecordFromIdns(String domain) {
        List<String> result = new ArrayList<>();
        try {
            String appId = idnsConfig.getAccessId();
            String appKey = idnsConfig.getAccessKey();

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HHmmMMdd");
            String javaTimeStr = formatter.format(LocalDateTime.now());
            String dnsKey = DigestUtils.md5Hex(appId + javaTimeStr + appKey);

            String url = String.format("http://idns.vip.tbsite.net/idnsapi/common/workflow/query_domain_w_info/?app_id=%s&app_key=%s&domain=%s", appId, dnsKey, domain);
            String responseStr = httpClient.fetchDataFromUrl(url);

            if (StringUtils.isBlank(responseStr)) {
                logger.warn("cloudbox period sync job failed to get record from idns, domain: {}", domain);
                return result;
            }

            JSONObject jsonObject = JSONObject.parseObject(responseStr);
            String resultCode = jsonObject.getString("resultCode");
            Boolean success = jsonObject.getBoolean("success");
            if (!resultCode.equals(SUCCESS_RESULT_CODE) || !success) {
                logger.warn("cloudbox period sync job failed to get record from idns, domain: {}", domain);
                return result;
            }

            JSONArray recordList = jsonObject.getJSONObject("data").getJSONArray("record_list");
            for (int i = 0; i < recordList.size(); i++) {
                JSONObject record = recordList.getJSONObject(i);
                String data = record.getString("data");
                if (isValidIPAddress(data) && !result.contains(data)) {
                    result.add(data);
                }
            }
        } catch (Exception e) {
            logger.warn("cloudbox period sync job failed to get record from idns, domain: {}", domain);
        }
        return result;
    }
}
