package com.aliyun.xdragon.biz.workitem.model;

import java.io.Serializable;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PublishingRecord implements Serializable {

    private static final long serialVersionUID = 4866974709852938347L;
    private String sourceId;

    private String targetId;

    private String sourceType;

    private String targetType;

    private String deployStage;

    private String deployService;

    private String deployTask;

    private String deployTime;

    private String deployType;

    private String description;

    private String deploySource;

    private String ncIp;

    public static PublishingRecord fillValue(JSONObject object) {
        return JSONObject.toJavaObject(object, PublishingRecord.class);
    }
}
