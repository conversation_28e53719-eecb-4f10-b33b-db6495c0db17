package com.aliyun.xdragon.biz.log.job.mail.api;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/02/06
 */
@Service
public class DevOpsApiErrorMailHandler extends AbstractExternalServerErrorMailHandler {
    @Override
    public String getName() {
        return "external_svr_for_devopsapi";
    }

    @Override
    public String getSourceRole() {
        return "ecs-devopsapi";
    }

    //@Override
    //public Map<String, String> getErrorCodeReg() {
    //    return new HashMap<>();
    //}

    //@Override
    //public int getQueryStep() {
    //    return 86400 ;
    //}
}
