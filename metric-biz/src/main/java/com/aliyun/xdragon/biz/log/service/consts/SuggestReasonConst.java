package com.aliyun.xdragon.biz.log.service.consts;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aliyun.xdragon.common.enumeration.log.PatternTagType;

public class SuggestReasonConst {
    private static Map<Integer, List<String>> suggestReasonMap;

    static {
        List<String> ignoreList = new ArrayList<>();
        ignoreList.add("日志聚类badcase");
        ignoreList.add("三方依赖问题");
        ignoreList.add("影响面较小且可控");

        suggestReasonMap = new HashMap<>();
        suggestReasonMap.put(PatternTagType.IGNORE.ordinal(), ignoreList);
    }

    public static List<String> getSuggestReasons(Long taskId, PatternTagType type) {
        if (!suggestReasonMap.containsKey(type.ordinal())) {
            return Collections.emptyList();
        }
        return suggestReasonMap.get(type.ordinal());
    }
}
