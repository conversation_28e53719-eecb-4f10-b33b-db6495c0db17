package com.aliyun.xdragon.biz.log.manager;

import com.aliyun.xdragon.biz.log.manager.vvp.VvpTaskManager;
import com.aliyun.xdragon.common.enumeration.log.TaskManagerType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Component
public class TaskManagerFactory {
    private static final Logger logger = LoggerFactory.getLogger(TaskManagerFactory.class);

    private Map<Integer, Class<? extends TaskManager>> cachedManagers;

    @Autowired
    private ApplicationContext ctx;

    @PostConstruct
    public void init() {
        cachedManagers = new HashMap<>(4);
        cachedManagers.put(TaskManagerType.Flink.getCode(), VvpTaskManager.class);
        cachedManagers.put(TaskManagerType.Fake.getCode(), FakeTaskManager.class);
    }

    public TaskManager getManager(TaskManagerType type) {
        logger.info("try get manager by {}", type.getName());
        return getManager(type.getCode());
    }

    public TaskManager getManager(Integer code) throws IllegalArgumentException {
        if (code == null || !cachedManagers.containsKey(code)) {
            throw new IllegalArgumentException("Type code not supported!");
        }
        return ctx.getBean(cachedManagers.get(code));
    }

    @Override
    public String toString() {
        return String.format("The size of cachedManagers is: %d", cachedManagers.size());
    }
}
