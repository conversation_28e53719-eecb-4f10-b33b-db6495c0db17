package com.aliyun.xdragon.biz.log.manager.param;

import com.aliyun.xdragon.biz.log.config.AnomalyStoreConfig;
import com.aliyun.xdragon.biz.log.config.LogClusterSlsConfig;
import com.aliyun.xdragon.biz.log.config.NcRegionOdpsConfig;
import com.aliyun.xdragon.biz.log.manager.config.DatabaseConfig;
import com.aliyun.xdragon.common.model.SlsUserInfo;
import com.aliyun.xdragon.common.model.log.ClusterAlgParam;
import com.aliyun.xdragon.common.model.log.LogClusterParam;
import com.aliyun.xdragon.common.model.log.PlatformParam;
import com.aliyun.xdragon.common.model.log.SinkParam;
import com.aliyun.xdragon.common.model.log.SlsSourceParam;
import com.aliyun.xdragon.common.model.log.SourceParam;
import com.aliyun.xdragon.service.common.config.FlinkMetricSlsConfig;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class ConfigParam {
    private static final Logger logger = LoggerFactory.getLogger(ConfigParam.class);

    private final SqlParam sql;
    private final PropParam prop;
    private Boolean filterGamma = true;
    private Boolean filterStatus = true;
    private Integer batchGetSize;
    private Integer joinType;
    private Boolean filterContent;
    private Long taskId;
    private Long cid;
    private FlinkMetricSlsConfig metricSlsConfig;
    private LogClusterSlsConfig logClusterSlsConfig;
    private NcRegionOdpsConfig ncRegionOdpsConfig;
    private PlatformParam platformParam;

    public ConfigParam() {
        sql = new SqlParam();
        prop = new PropParam();
        filterStatus = true;
        filterGamma = true;
        batchGetSize = 100;
        filterContent = true;
        joinType = 0;
    }

    public ConfigParam(Long taskId, Date createTime, LogClusterParam logClusterParam, DatabaseConfig dbConfig,
        Map<String, SlsUserInfo> userInfoMap, FlinkMetricSlsConfig metricSlsConfig,
        LogClusterSlsConfig logClusterSlsConfig, NcRegionOdpsConfig ncRegionOdpsConfig,
        AnomalyStoreConfig anomalyStoreConfig) {
        this.taskId = taskId;
        this.platformParam = logClusterParam.getPlatformParam();
        this.metricSlsConfig = metricSlsConfig;
        this.logClusterSlsConfig = logClusterSlsConfig;
        this.ncRegionOdpsConfig = ncRegionOdpsConfig;

        SourceParam sourceParam = logClusterParam.getSourceParam();
        if (!(sourceParam instanceof SlsSourceParam)) {
            throw new IllegalArgumentException("Source param type not supported!");
        }
        SlsSourceParam slsSourceParam = (SlsSourceParam)sourceParam;
        this.batchGetSize = slsSourceParam.getBatchGetSize();

        this.sql = new SqlParam(createTime, batchGetSize, logClusterParam.getAlgParam());

        this.joinType = slsSourceParam.getJoinType() == null ? 0 : slsSourceParam.getJoinType();
        this.filterContent = slsSourceParam.getFilterContent() == null || slsSourceParam.getFilterContent();
        this.prop = new PropParam(taskId, "center", dbConfig, slsSourceParam, logClusterParam.getAlgParam(),
            logClusterParam.getSinkParam(), userInfoMap, anomalyStoreConfig);
    }

    public ConfigParam(Long taskId, Long cid, String description, String region, Date createTime,
        LogClusterParam logClusterParam, DatabaseConfig dbConfig, Map<String, SlsUserInfo> userInfoMap,
        FlinkMetricSlsConfig metricSlsConfig, LogClusterSlsConfig logClusterSlsConfig,
        AnomalyStoreConfig anomalyStoreConfig) {
        boolean hasNullParam = (logClusterParam == null || logClusterParam.getSourceParam() == null ||
            logClusterParam.getAlgParam() == null || dbConfig == null);
        if (hasNullParam) {
            logger.error("[ConfigParam] Failed to construct ConfigParam because of null param!");
            throw new IllegalArgumentException("Invalid null param, failed to construct ConfigParam!");
        }
        SourceParam sourceParam = logClusterParam.getSourceParam();
        if (!(sourceParam instanceof SlsSourceParam)) {
            throw new IllegalArgumentException("Source param type not supported!");
        }
        this.taskId = taskId;
        this.cid = cid;
        SlsSourceParam slsSourceParam = (SlsSourceParam)sourceParam;
        if (slsSourceParam.getFilterGamma() != null) {
            filterGamma = slsSourceParam.getFilterGamma();
        }
        if (slsSourceParam.getFilterStatus() != null) {
            filterStatus = slsSourceParam.getFilterStatus();
        }
        batchGetSize = slsSourceParam.getBatchGetSize();
        this.filterContent = slsSourceParam.getFilterContent() == null || slsSourceParam.getFilterContent();
        joinType = slsSourceParam.getJoinType() == null ? 0 : slsSourceParam.getJoinType();
        sql = new SqlParam(description, createTime, slsSourceParam, logClusterParam.getAlgParam());
        prop = new PropParam(taskId, region, dbConfig, slsSourceParam, logClusterParam.getAlgParam(),
            logClusterParam.getSinkParam(), userInfoMap, anomalyStoreConfig);
        this.platformParam = logClusterParam.getPlatformParam();
        this.metricSlsConfig = metricSlsConfig;
        this.logClusterSlsConfig = logClusterSlsConfig;
    }

    @Data
    public class SqlParam {
        private Boolean hasLevelField;
        private Integer watermarkOffset;
        private Integer windowSize;
        private Integer batchGetSize;
        private String description;
        private String rawLogField;
        private String levelField;
        private String levelFilterMarkers;
        private Date createTime;
        private List<String> extFields;

        public SqlParam() {
            this.hasLevelField = true;
            this.watermarkOffset = 10000;
            this.windowSize = 15;
            this.description = "defaultDescription";
            this.rawLogField = "detail";
            this.levelField = "level";
            this.levelFilterMarkers = "error,ERROR";
            this.createTime = new Date();
            this.extFields = new ArrayList<>(8);
            this.extFields.add("__source__");
            this.batchGetSize = 100;
        }

        public SqlParam(Date createTime, Integer batchGetSize, ClusterAlgParam algParam) {
            this.createTime = createTime;
            this.batchGetSize = batchGetSize;
            this.windowSize = algParam.getWindowSize();
        }

        public SqlParam(String description, Date createTime, SlsSourceParam slsSourceParam, ClusterAlgParam algParam) {
            this.watermarkOffset = algParam.getWaterMarker();
            this.windowSize = algParam.getWindowSize();
            this.description = description;
            this.createTime = createTime;
            this.batchGetSize = slsSourceParam.getBatchGetSize();

            this.rawLogField = slsSourceParam.getLogField();
            if (slsSourceParam.getFilterField() != null && !slsSourceParam.getFilterField().isEmpty()) {
                this.hasLevelField = true;
                this.levelField = slsSourceParam.getFilterField();
            } else {
                this.hasLevelField = false;
                this.levelField = "";
            }

            this.levelFilterMarkers = slsSourceParam.getFilterString();
            this.extFields = new ArrayList<>(8);
            if (slsSourceParam.getExtFields() != null && !slsSourceParam.getExtFields().isEmpty()) {
                String[] extFieldStrs = slsSourceParam.getExtFields().split(",");
                for (String ext : extFieldStrs) {
                    this.extFields.add(ext);
                }
            }
        }
    }

    @Data
    public class PropParam {
        private static final String DEFAULT_SLS_SINK_ACCOUNT = "sls_op";
        private static final String DEFAULT_SLS_ACCOUNT = "newbie_ecs";
        private static final String READONLY_SLS_ACCOUNT = "newbie_ecs_readonly";

        private Integer cutLines;
        private Integer cutLength;
        private Integer detailThreshold;
        private Integer ratioSupport;
        private Integer support;
        private Double weightThreshold;
        private String indicesStr;

        private Long taskId;
        private String region;
        private String inputAccessId;
        private String inputAccessKey;
        private String inputEndPoint;
        private String inputProject;
        private String inputLogStore;
        private String inputConsumerGroup;

        private String outputUrl;
        private String outputUserName;
        private String outputPassword;
        private String outputSinkTableName;
        private String outputDetailTableName;

        private String slsAccessId;
        private String slsAccessKey;
        private String slsEndPoint;
        private String slsProject;
        private String slsLogStore;

        private String filterEndpoint;
        private String filterProject;
        private String filterTableName;
        private String filterAccessId;
        private String filterAccessKey;

        private String gammaUrl;
        private String gammaTableName;
        private String gammaUserName;
        private String gammaPassword;

        private String remainEndpoint;
        private String remainProject;
        private String remainLogstore;

        private String libraryEndpoint;
        private String libraryId;
        private String libraryKey;
        private String libraryProject;

        private String sourceLogStore;

        public PropParam() {
            cutLines = 3;
            cutLength = 35;
            detailThreshold = 1000;
            ratioSupport = 5;
            support = 5;
            weightThreshold = 0.04;
            indicesStr = "1";

            taskId = -1L;
            region = "center";
            inputAccessId = "defaultAccessId";
            inputAccessKey = "defaultAccessKey";
            inputEndPoint = "defaultEndPoint";
            inputLogStore = "defaultLogStore";
            inputProject = "defaultProject";
            inputConsumerGroup = "defaultConsumerGroup";
            outputUrl = "defaultOutputUrl";
            outputUserName = "defaultOutputUserName";
            outputPassword = "defaultOutputPassword";
            outputSinkTableName = "defaultOutputSinkTableName";
            outputDetailTableName = "defaultOutputDetailTableName";
            filterEndpoint = "defaultFilterEndpoint";
            filterTableName = "defaultFilterTableName";
            filterProject = "defaultFilterProject";
            filterAccessId = "defaultFilterAccessId";
            filterAccessKey = "defaultFilterAccessKey";
            gammaUrl = "defaultGammaUrl";
            gammaTableName = "defaultGammaTableName";
            gammaUserName = "defaultGammaUserName";
            gammaPassword = "defaultGammaPassword";
            remainEndpoint = "defaultRemainEndpoint";
            remainProject = "defaultRemainProject";
            remainLogstore = "defaultRemainLogstore";
            libraryEndpoint = "defaultLibraryEndpoint";
            libraryId = "defaultLibraryId";
            libraryKey = "defaultLibraryKey";
            libraryProject = "defaultLibraryProject";
        }

        public PropParam(Long taskId, String region, DatabaseConfig dbConfig, SlsSourceParam slsSource,
            ClusterAlgParam alg, SinkParam sink, Map<String, SlsUserInfo> userInfoMap,
            AnomalyStoreConfig anomalyStoreConfig) {
            this.taskId = taskId;
            this.region = region;

            cutLines = alg.getCutLines();
            cutLength = alg.getCutLength();
            detailThreshold = alg.getThreshold();
            support = alg.getSupport();
            ratioSupport = alg.getRsupport();
            weightThreshold = alg.getWeightThreshold();
            indicesStr = alg.genIndicesStr();
            if (weightThreshold == null || weightThreshold < Math.ulp(1.0) || weightThreshold > 1.0) {
                logger.error("Invalid weightThreshold given, use default value 0.04 instead.");
                weightThreshold = 0.04;
            }
            if (detailThreshold == null) {
                logger.error("Invalid threshold given, use default value 1000 instead.");
                detailThreshold = 1000;
            }

            String accountName = DEFAULT_SLS_ACCOUNT.equals(slsSource.getAccountName()) ? READONLY_SLS_ACCOUNT
                : slsSource.getAccountName();
            SlsUserInfo slsInfo = userInfoMap.getOrDefault(accountName, new SlsUserInfo());
            inputAccessId = slsInfo.getAk();
            inputAccessKey = slsInfo.getSk();
            inputEndPoint = slsSource.getEndpoint();
            inputLogStore = slsSource.getLogStore();
            inputProject = slsSource.getProject();
            inputConsumerGroup = slsSource.getConsumerGroup();

            outputUrl = dbConfig.getAdbUrl();
            outputUserName = dbConfig.getAdbUsername();
            outputPassword = dbConfig.getAdbPassword();
            outputSinkTableName = dbConfig.getAdbPatternTableName();
            outputDetailTableName = dbConfig.getAdbDetailTableName();

            libraryEndpoint = dbConfig.getLibraryEndpoint();
            libraryId = dbConfig.getLibraryId();
            libraryKey = dbConfig.getLibraryKey();
            libraryProject = dbConfig.getLibraryProject();

            remainEndpoint = dbConfig.getRemainEndpoint();
            remainProject = dbConfig.getRemainProject();
            remainLogstore = dbConfig.getRemainLogstore();

            slsAccessId = anomalyStoreConfig.getAccessId();
            slsAccessKey = anomalyStoreConfig.getAccessKey();
            slsEndPoint = anomalyStoreConfig.getEndpoint();
            slsProject = anomalyStoreConfig.getProject();
            slsLogStore = anomalyStoreConfig.getLogStore();

            sourceLogStore = sink.getSourceLogStore();

            if (filterStatus) {
                filterEndpoint = dbConfig.getFilterEndpoint();
                filterProject = dbConfig.getFilterProject();
                filterTableName = dbConfig.getFilterTableName();
                filterAccessId = dbConfig.getFilterAccessId();
                filterAccessKey = dbConfig.getFilterAccessKey();
            }

            if (filterGamma) {
                gammaUrl = dbConfig.getGammaUrl();
                gammaTableName = dbConfig.getGammaTableName();
                gammaUserName = dbConfig.getGammaUserName();
                gammaPassword = dbConfig.getGammaPassword();
            }
        }
    }
}
