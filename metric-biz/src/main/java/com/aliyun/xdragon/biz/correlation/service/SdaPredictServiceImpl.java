package com.aliyun.xdragon.biz.correlation.service;

import com.aliyun.xdragon.biz.workitem.service.EasService;
import com.aliyun.xdragon.biz.workitem.service.impl.EasPredictServiceImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Lazy
public class SdaPredictServiceImpl {

    @Value("${eas.token.sda}")
    private String token;

    @Value("${eas.url.sda}")
    private String endPoint;

    @Value("${eas.url.type}")
    private String urlType;

    private String modelName = "sda_prediction";

    private EasService easService;

    @PostConstruct
    public void init() {
        easService = new EasPredictServiceImpl(token, endPoint, modelName, urlType);
    }

    public List<String> predict(List<List<Double>> corpus) throws Exception {
        return easService.predict(corpus);
    }

    public List<String> predict(List<List<Double>> corpus, Map<String, String> params) throws Exception {
        return easService.predict(corpus, params);
    }


}
