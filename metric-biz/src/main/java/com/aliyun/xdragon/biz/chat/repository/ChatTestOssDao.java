package com.aliyun.xdragon.biz.chat.repository;

import com.aliyun.xdragon.biz.chat.config.ChatTestOssConfig;
import com.aliyun.xdragon.biz.log.repository.AbstractOssDao;
import com.aliyun.xdragon.common.model.OssConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2024/09/04
 */
@Repository
public class ChatTestOssDao extends AbstractOssDao {
    @Autowired
    private ChatTestOssConfig chatTestOssConfig;

    @Override
    protected OssConfig ossConfig() {
        return chatTestOssConfig;
    }
}
