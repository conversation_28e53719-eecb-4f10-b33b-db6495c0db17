package com.aliyun.xdragon.biz.chat.job;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.xdragon.api.service.chat.ChatSessionService;
import com.aliyun.xdragon.service.common.job.AbstractProcessTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AoneDiagnoseResPushJob<T> extends AbstractProcessTask<T> {
    private static final Logger logger = LoggerFactory.getLogger(AoneDiagnoseResPushJob.class);

    @Autowired
    private ChatSessionService chatSessionService;

    @Override
    public ProcessResult process(JobContext context, int dataTs, int schedTs) throws Exception {
        try{
            chatSessionService.pushAoneDiagnose();
        }catch (Exception e){
            logger.error("AoneDiagnoseResPushJob exception",e);
        }
        return new ProcessResult(true);
    }
}
