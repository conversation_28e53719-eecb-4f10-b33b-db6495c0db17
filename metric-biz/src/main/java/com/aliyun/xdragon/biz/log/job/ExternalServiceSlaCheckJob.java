package com.aliyun.xdragon.biz.log.job;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.xdragon.common.generate.model.ExternalSvrSla;
import com.aliyun.xdragon.common.model.log.APISlaCount;
import com.aliyun.xdragon.common.model.log.ExternalSvrSlaCheckParam;
import com.aliyun.xdragon.service.common.job.AbstractMapTask;
import com.aliyun.xdragon.biz.log.service.ExternalSvrSlaService;
import com.aliyun.xdragon.biz.log.service.TsdbOperatorService;
import com.aliyun.xdragon.service.common.service.DevOpsApiService;
import com.aliyun.xdragon.service.common.service.XdragonAlertService;
import com.aliyuncs.ecsops.model.v20160401.OpsXdragonSendAlertRequest.Item;
import com.aliyuncs.ecsops.model.v20160401.OpsXdragonSendAlertResponse;
import com.aliyuncs.exceptions.ClientException;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @date 2022/10/24
 */
@Component
@Lazy
public class ExternalServiceSlaCheckJob extends AbstractMapTask<List<ExternalSvrSlaCheckParam>> {
    private static final Logger logger = LoggerFactory.getLogger(ExternalServiceSlaCheckJob.class);

    @Autowired
    private ExternalSvrSlaService externalSvrSlaService;
    @Autowired
    private XdragonAlertService alertService;

    @Autowired
    private TsdbOperatorService tsdbOperatorService;

    private final int CHECK_INTERVAL = 300;
    private final int CHECK_WAIT = 300;

    private final int DEFAULT_MIN_COUNT = 3;

    @Override
    public List<List<ExternalSvrSlaCheckParam>> genTask(JobContext context, int dataTs) {
        List<ExternalSvrSlaCheckParam> checks = externalSvrSlaService.genTask();
        checks = filterChecks(checks, dataTs);
        if (checks.isEmpty()) {
            logger.info("no task check in this round after filter");
            return Collections.emptyList();
        }
        Map<String, List<ExternalSvrSlaCheckParam>> tasks = checks.stream().collect(groupingBy(ExternalSvrSlaCheckParam::getSourceRole));
        return new LinkedList<>(tasks.values());
    }

    @Override
    public ProcessResult processSubTask(JobContext context, int dataTs, int schedTs, List<ExternalSvrSlaCheckParam> checks)
        throws Exception {
        checks.forEach(c -> checkOne(c, dataTs));
        return new ProcessResult(true);
    }

    public List<ExternalSvrSlaCheckParam> filterChecks(List<ExternalSvrSlaCheckParam> checks, int runTs) {
        List<ExternalSvrSlaCheckParam> ret = new LinkedList<>();
        checks.forEach(param -> {
            Integer interval = param.getCheckInterval();
            //没有配置，默认每次都检查，周期为5分钟
            if (interval == null || interval == CHECK_INTERVAL || runTs % interval == CHECK_WAIT) {
                ret.add(param);
            }
        });
        return ret;
    }

    public void checkOne(ExternalSvrSlaCheckParam param, int runTs) {
        String sql = externalSvrSlaService.genSql(param);
        logger.info("check sla sql: {}", sql);
        int interval = param.getCheckInterval() == null ? CHECK_INTERVAL : param.getCheckInterval();
        List<APISlaCount> apiSlaCounts = externalSvrSlaService.getSlaBySql(sql, runTs - CHECK_WAIT - interval,
            runTs - CHECK_WAIT, param.getGroupBys());
        Set<String> excludes = new HashSet<>();
        if (StringUtils.isNotBlank(param.getExpandType())) {
            List<ExternalSvrSla> detailApis = externalSvrSlaService.getConfigBySource(param.getSourceRole());
            detailApis.forEach(d -> excludes.add(String.format("%s#%s", d.getServerName(), d.getRequestApi())));
        }
        Double checkSla = param.getSla();
        if (checkSla == null) {
            checkSla = 99.5;
        }
        for (APISlaCount apiSlaCount : apiSlaCounts) {
            String key = String.format("%s#%s", apiSlaCount.getServerName(), apiSlaCount.getRequestApi());
            if (excludes.contains(key)) {
                continue;
            }
            if (apiSlaCount.getSuccessSla() < checkSla) {
                logger.info("need send alarm, source: {}, server {}, api: {}, time: {}, sla:{}", param.getSourceRole(),
                    apiSlaCount.getServerName(), apiSlaCount.getRequestApi(), runTs, apiSlaCount.getSuccessSla());
                try {
                    if (apiSlaCount.getFailCount() > (param.getMinFailCount() == null ? DEFAULT_MIN_COUNT
                        : param.getMinFailCount())) {
                        sendAlarm(param, runTs, apiSlaCount);
                    } else {
                        logger.info("error and fail count less than min count, ignore alarm");
                    }
                } catch (Exception e) {
                    logger.error("send alarm error, api info: {}", apiSlaCount, e);
                }
                // send data to sls
                List<String> label = new LinkedList<>();
                label.add("external_sla");
                label.add(apiSlaCount.getSourceRole());
                label.add(StringUtils.isBlank(apiSlaCount.getServerName()) ? "all" : apiSlaCount.getServerName());
                label.add(StringUtils.isBlank(apiSlaCount.getRequestApi()) ? "all" : apiSlaCount.getRequestApi());
                Map<String, Object> additional = new HashMap<>();
                additional.put("success", apiSlaCount.getSuccess());
                additional.put("business_fail", apiSlaCount.getBusinessFail());
                additional.put("system_error", apiSlaCount.getSystemError());
                additional.put("interval", interval);
                if (StringUtils.isNotBlank(param.getFilter())) {
                    additional.put("filter", param.getFilter());
                } else {
                    additional.put("filter", "");
                }
                if (apiSlaCount.getExtCols() != null && !apiSlaCount.getExtCols().isEmpty()) {
                    additional.put("ext_cols", apiSlaCount.getExtCols());
                }
                tsdbOperatorService.writeToTSDB(label, String.valueOf(apiSlaCount.getSuccessSla()), runTs, additional,
                    null);
            }
        }
    }

    private void sendAlarm(ExternalSvrSlaCheckParam checkParam, int runTs, APISlaCount apiSlaCount)
        throws ClientException {
        Item item = new Item();
        item.setResourceId(DigestUtils.md5DigestAsHex(externalSvrSlaService.genSql(checkParam).getBytes()));
        item.setResourceType("svr_sla");
        Map<String, String> params = new HashMap<>();
        if (StringUtils.isNotBlank(apiSlaCount.getSourceRole())) {
            params.put("sourceRole", apiSlaCount.getSourceRole());
        }
        if (StringUtils.isNotBlank(apiSlaCount.getServerName())) {
            params.put("serverName", apiSlaCount.getServerName());
        }
        if (StringUtils.isNotBlank(apiSlaCount.getRequestApi())) {
            params.put("requestApi", apiSlaCount.getRequestApi());
        }
        if (StringUtils.isNotBlank(checkParam.getFilter())) {
            params.put("filter", checkParam.getFilter());
        }
        params.put("comment", checkParam.getComment());
        int interval = checkParam.getCheckInterval() == null ? CHECK_INTERVAL : checkParam.getCheckInterval();
        params.put("interval", String.format("%d", interval));
        params.put("timestamp", String.format("%d", runTs - CHECK_WAIT));
        params.put("sla", String.format("%.2f", apiSlaCount.getSuccessSla()));
        params.put("success", String.format("%d", apiSlaCount.getSuccess()));
        params.put("fail", String.format("%d", apiSlaCount.getBusinessFail()));
        params.put("error", String.format("%d", apiSlaCount.getSystemError()));
        if (apiSlaCount.getExtCols() != null && !apiSlaCount.getExtCols().isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (String k : apiSlaCount.getExtCols().keySet()) {
                sb.append(k).append("=").append(apiSlaCount.getExtCols().get(k)).append(";");
            }
            params.put("extCols", sb.toString());
        }
        item.setParamsStr(DevOpsApiService.paramToJson(params));
        OpsXdragonSendAlertResponse resp = alertService.sendAlert(Lists.newArrayList("external_svr"), "xdragon-metric", Lists.newArrayList(item), null, null);
        resp.getSendResponses().forEach(sr -> {
            if (sr.getSuccessed()) {
                logger.info("alert ok, alert request id {}", resp.getRequestId());
            } else {
                logger.warn("alert fail, alert request id {}, resourceId {}, uuid {}", resp.getRequestId(), sr.getResourceId(), sr.getUuid());
            }
        });
    }
}
