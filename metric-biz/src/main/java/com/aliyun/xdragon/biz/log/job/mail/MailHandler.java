package com.aliyun.xdragon.biz.log.job.mail;

import java.util.List;

import com.aliyun.xdragon.common.model.MailInfo;

/**
 * <AUTHOR>
 * @date 2023/02/01
 */
public interface MailHandler {
    /**
     * register handler to {@link MailHandlerDispatcher}
     *
     * @param name, handler name
     */
    void register(String name);

    /**
     * set send quartz cron expression, <a href="http://yungoukeji.com/g/tools/quartzcron/">Quartz Cron Generator</a>
     *
     * @param cron, quartz cron expression
     */
    void setSendCron(String cron);

    /**
     * get send quartz cron expression
     *
     * @return cron expression
     */
    String getSendCron();

    /**
     * generate {@link MailInfo}
     * @param  runTs, time to run, in second
     * @return mail info object list
     */
    List<MailInfo> genMailInfo(int runTs);

    /**
     * check if need send
     *
     * @param runTs, time to check, in second
     * @return true for send
     */
    boolean needSend(int runTs);
}
