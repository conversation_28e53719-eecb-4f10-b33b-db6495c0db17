package com.aliyun.xdragon.biz.rule.repository;

import java.math.BigDecimal;
import java.util.List;

import javax.annotation.Resource;

import com.aliyun.xdragon.common.generate.model.OpsRuleMiningDetail;
import com.aliyun.xdragon.common.generate.model.OpsRuleMiningDetailExample;
import com.aliyun.xdragon.common.generate.model.map.OpsRuleMiningDetailMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/01/15
 */
@Repository
public class OpsRuleMiningDetailDao {

    @Resource
    private OpsRuleMiningDetailMapper mapper;

    /**
     * 查询潜在可行的特征组合
     */
    public List<OpsRuleMiningDetail> queryCandidateFeatures() {
        OpsRuleMiningDetailExample e = new OpsRuleMiningDetailExample();
        OpsRuleMiningDetailExample.Criteria c = e.createCriteria();
        //没有误命中
        c.andErrorCntEqualTo(0L);
        //平均提前时间大于10s
        c.andAvgLeadSecondGreaterThan(BigDecimal.valueOf(10L));
        //命中总目标数量大于10
        c.andTargetCntGreaterThan(10L);
        //同规则提前命中数量大于1
        c.andSameRuleLeadCntGreaterThan(1L);
        //状态为已回溯
        c.andStatusEqualTo("Backtracked");
        //执行查询
        return mapper.selectByExample(e);
    }

    /**
     * 批量更新指定行的状态为已发布
     *
     * @param features 需要更新的行的特征字段
     * @return 更新行数
     */
    public int batchUpdateStatus(List<String> features) {
        //如果特征为空，则不更新
        if (CollectionUtils.isEmpty(features)) {
            return 0;
        }
        //选择指定特征的行
        OpsRuleMiningDetailExample e = new OpsRuleMiningDetailExample();
        OpsRuleMiningDetailExample.Criteria c = e.createCriteria();
        c.andFeaturesIn(features);
        //将状态更新为已发布
        OpsRuleMiningDetail record = new OpsRuleMiningDetail();
        record.setStatus("Published");
        //执行更新
        return mapper.updateByExampleSelective(record, e);
    }

}
