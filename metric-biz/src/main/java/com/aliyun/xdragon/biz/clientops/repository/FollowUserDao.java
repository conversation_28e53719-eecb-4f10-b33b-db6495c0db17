package com.aliyun.xdragon.biz.clientops.repository;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.aliyun.xdragon.common.generate.model.FollowUsers;
import com.aliyun.xdragon.common.generate.model.FollowUsersExample;
import com.aliyun.xdragon.common.generate.model.map.FollowUsersMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/06/18
 */
@Component
public class FollowUserDao {

    @Autowired
    private FollowUsersMapper followUsersMapper;

    public boolean addFollowUser(String empId, String aliUid, String userName, Long cid, String comment) {
        if (StringUtils.isBlank(empId) || (StringUtils.isBlank(aliUid) && Objects.isNull(cid))) {
            return false;
        }
        FollowUsers followUser = new FollowUsers();
        followUser.setEmpId(empId);
        followUser.setAliUid(aliUid);
        followUser.setCid(cid);
        followUser.setComment(comment);
        followUser.setUserName(userName);
        return followUsersMapper.insert(followUser) > 0;
    }

    public FollowUsers selectOneByCondition(String empId, String aliUid, Long cid) {
        if (StringUtils.isBlank(empId) || (StringUtils.isBlank(aliUid) && Objects.isNull(cid))) {
            return null;
        }
        FollowUsersExample example = new FollowUsersExample();
        FollowUsersExample.Criteria criteria = example.createCriteria();
        criteria.andEmpIdEqualTo(empId);
        if (StringUtils.isNotBlank(aliUid)) {
            criteria.andAliUidEqualTo(aliUid);
        }
        if (Objects.nonNull(cid)) {
            criteria.andCidEqualTo(cid);
        }
        return followUsersMapper.selectOneByExample(example);
    }

    public List<FollowUsers> queryByEmpId(String empId) {
        if (StringUtils.isBlank(empId)) {
            return Collections.emptyList();
        }
        FollowUsersExample example = new FollowUsersExample();
        FollowUsersExample.Criteria criteria = example.createCriteria();
        criteria.andEmpIdEqualTo(empId);
        return followUsersMapper.selectByExample(example);
    }

    public FollowUsers selectById(Long id) {
        return followUsersMapper.selectByPrimaryKey(id);
    }

    public boolean updateById(Long id, String empId, String aliUid, String userName, Long cid, String comment) {
        if (StringUtils.isAnyBlank(empId, aliUid, userName)) {
            return false;
        }
        FollowUsers followUser = new FollowUsers();
        followUser.setId(id);
        followUser.setEmpId(empId);
        followUser.setAliUid(aliUid);
        followUser.setCid(cid);
        followUser.setUserName(userName);
        followUser.setComment(comment);
        return followUsersMapper.updateByPrimaryKeySelective(followUser) > 0;
    }

    public void deleteById(Long id) {
        followUsersMapper.deleteByPrimaryKey(id);
    }

}
