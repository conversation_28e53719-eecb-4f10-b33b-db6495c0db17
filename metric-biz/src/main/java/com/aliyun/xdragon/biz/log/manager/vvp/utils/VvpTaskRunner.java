package com.aliyun.xdragon.biz.log.manager.vvp.utils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.aliyun.autopilot_inner20221229.models.LibraConfig;
import com.aliyun.autopilot_inner20221229.models.Policy;
import com.aliyun.autopilot_inner20221229.models.UpdateAutopilotPolicyHeaders;
import com.aliyun.autopilot_inner20221229.models.UpdateAutopilotPolicyModeHeaders;
import com.aliyun.autopilot_inner20221229.models.UpdateAutopilotPolicyModeRequest;
import com.aliyun.autopilot_inner20221229.models.UpdateAutopilotPolicyModeResponse;
import com.aliyun.autopilot_inner20221229.models.UpdateAutopilotPolicyRequest;
import com.aliyun.autopilot_inner20221229.models.UpdateAutopilotPolicyResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.aliyun.ververica_inner20220718.Client;
import com.aliyun.ververica_inner20220718.models.BriefResourceSetting;
import com.aliyun.ververica_inner20220718.models.CreateDeploymentHeaders;
import com.aliyun.ververica_inner20220718.models.CreateDeploymentRequest;
import com.aliyun.ververica_inner20220718.models.CreateDeploymentResponse;
import com.aliyun.ververica_inner20220718.models.DeleteDeploymentHeaders;
import com.aliyun.ververica_inner20220718.models.DeleteDeploymentResponse;
import com.aliyun.ververica_inner20220718.models.DeleteJobHeaders;
import com.aliyun.ververica_inner20220718.models.DeleteJobResponse;
import com.aliyun.ververica_inner20220718.models.Deployment;
import com.aliyun.ververica_inner20220718.models.DeploymentRestoreStrategy;
import com.aliyun.ververica_inner20220718.models.DeploymentTarget;
import com.aliyun.ververica_inner20220718.models.EngineVersionMetadataIndex;
import com.aliyun.ververica_inner20220718.models.GetDeploymentHeaders;
import com.aliyun.ververica_inner20220718.models.GetDeploymentResponse;
import com.aliyun.ververica_inner20220718.models.GetJobHeaders;
import com.aliyun.ververica_inner20220718.models.GetJobResponse;
import com.aliyun.ververica_inner20220718.models.Job;
import com.aliyun.ververica_inner20220718.models.ListDeploymentTargetsHeaders;
import com.aliyun.ververica_inner20220718.models.ListDeploymentTargetsRequest;
import com.aliyun.ververica_inner20220718.models.ListDeploymentTargetsResponse;
import com.aliyun.ververica_inner20220718.models.ListEngineVersionMetadataHeaders;
import com.aliyun.ververica_inner20220718.models.ListEngineVersionMetadataResponse;
import com.aliyun.ververica_inner20220718.models.ListJobsHeaders;
import com.aliyun.ververica_inner20220718.models.ListJobsRequest;
import com.aliyun.ververica_inner20220718.models.ListJobsResponse;
import com.aliyun.ververica_inner20220718.models.StartJobHeaders;
import com.aliyun.ververica_inner20220718.models.StartJobRequest;
import com.aliyun.ververica_inner20220718.models.StartJobRequestBody;
import com.aliyun.ververica_inner20220718.models.StartJobResponse;
import com.aliyun.ververica_inner20220718.models.StopJobHeaders;
import com.aliyun.ververica_inner20220718.models.StopJobRequest;
import com.aliyun.ververica_inner20220718.models.StopJobRequestBody;
import com.aliyun.ververica_inner20220718.models.StopJobResponse;
import com.aliyun.ververica_inner20220718.models.UpdateDeploymentHeaders;
import com.aliyun.ververica_inner20220718.models.UpdateDeploymentRequest;
import com.aliyun.ververica_inner20220718.models.UpdateDeploymentResponse;
import com.aliyun.xdragon.biz.log.manager.config.ResourceConfig;
import com.aliyun.xdragon.biz.log.manager.enums.TaskManagerErrorCode;
import com.aliyun.xdragon.biz.log.manager.vvp.config.VvpManagerConfig;
import com.aliyun.xdragon.biz.log.manager.vvp.enums.RestoreStrategy;
import com.aliyun.xdragon.biz.log.manager.vvp.enums.StopStrategy;
import com.aliyun.xdragon.common.annotation.ServerLog;
import com.aliyun.xdragon.common.enumeration.SvrResultStatus;
import com.aliyun.xdragon.common.exception.TaskManagerException;
import com.aliyun.xdragon.common.function.Proxy5;
import com.aliyun.xdragon.common.util.ServerLogContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

import static com.aliyun.xdragon.biz.log.manager.vvp.utils.ServiceLogHelper.getServiceErrorInfo;
import static com.aliyun.xdragon.service.common.util.Checks.nullOrEmpty;

/**
 * <AUTHOR>
 * @date 2022/11/16
 */
@Component
public class VvpTaskRunner {
    private static final Logger logger = LoggerFactory.getLogger(VvpTaskRunner.class);
    private static final int MAX_RETRY_COUNT = 300;
    private static final int SLEEP_MS = 5000;
    private static final int TIMEOUT = 60 * 1000;
    public static List<String> unexpectedFinalStatus = Arrays.asList("FAILED", "FINISHED");

    private Client client;

    private com.aliyun.autopilot_inner20221229.Client autoPilotClient;

    @Autowired
    private VvpManagerConfig config;

    @PostConstruct
    public void init() throws Exception {
        Config cfg = new com.aliyun.teaopenapi.models.Config();
        cfg.setAccessKeyId(config.getAccessId());
        cfg.setAccessKeySecret(config.getAccessKey());
        cfg.setEndpoint(config.getEndpoint());
        cfg.setConnectTimeout(TIMEOUT);
        cfg.setReadTimeout(TIMEOUT);

        Config cfg2 = new com.aliyun.teaopenapi.models.Config();
        cfg2.setAccessKeyId(config.getAccessId());
        cfg2.setAccessKeySecret(config.getAccessKey());
        cfg2.setEndpoint(config.getPilotEndpoint());
        cfg2.setConnectTimeout(TIMEOUT);
        cfg2.setReadTimeout(TIMEOUT);
        try {
            client = new Client(cfg);
            if (nullOrEmpty(config.getEngineVersion())) {
                EngineVersionMetadataIndex engineVersionMetadataIndex = listEngineVersionMetadata();
                this.config.setEngineVersion(engineVersionMetadataIndex.getDefaultEngineVersion());
            }
            if (nullOrEmpty(config.getDeploymentTargetName())) {
                List<DeploymentTarget> deploymentTargetList = listDeploymentTargets();
                if (nullOrEmpty(deploymentTargetList)) {
                    throw new Exception("Got null or empty deployment target list!");
                }
                this.config.setDeploymentTargetName(deploymentTargetList.get(0).getName());
            }

            autoPilotClient = new com.aliyun.autopilot_inner20221229.Client(cfg2);
        } catch (Exception e) {
            logger.error("[VvpTaskRunner] Exception happened when init vvp sdk client! ", e);
            throw e;
        }
    }

    @ServerLog(serverName = "vvp", api = "updateAutopilotPolicyWithOptions")
    public UpdateAutopilotPolicyResponse updateAutoPilotPolicy(Map<String, String> policyParam, String deploymentId, String mode) {
        // update policy
        UpdateAutopilotPolicyHeaders headers = new UpdateAutopilotPolicyHeaders();
        headers.setWorkspace(config.getWorkspace());
        UpdateAutopilotPolicyRequest request = new UpdateAutopilotPolicyRequest();
        Policy policy = new Policy();
        policy.setWorkspace(config.getWorkspace());
        policy.setMode(mode);
        LibraConfig libraConfig = new LibraConfig();
        libraConfig.setExtraConfig(policyParam);
        policy.setLibraConfig(libraConfig);
        request.setBody(policy);

        UpdateAutopilotPolicyResponse resp = proxyCall(autoPilotClient::updateAutopilotPolicyWithOptions, config.getNamespace(), deploymentId, request,
                headers, new RuntimeOptions());
        if (!resp.getBody().getSuccess()) {
            ServerLogContextUtil.setErrorInfo(SvrResultStatus.SYSTEM_ERROR.getStatus(), resp.getBody().getHttpCode().toString(), resp.getBody().getMessage(), resp.getBody().getRequestId());
        }
        return resp;
    }

    @ServerLog(serverName = "vvp", api = "updateAutopilotPolicyModeWithOptions")
    public UpdateAutopilotPolicyModeResponse updateAutoPilotMode(String deploymentId, String mode) {
        UpdateAutopilotPolicyModeHeaders updateModeHeader = new UpdateAutopilotPolicyModeHeaders();
        updateModeHeader.setWorkspace(config.getWorkspace());
        UpdateAutopilotPolicyModeRequest updateModeRequest = new UpdateAutopilotPolicyModeRequest();
        updateModeRequest.setMode(mode);

        UpdateAutopilotPolicyModeResponse resp = proxyCall(autoPilotClient::updateAutopilotPolicyModeWithOptions, config.getNamespace(), deploymentId, updateModeRequest,
                updateModeHeader, new RuntimeOptions());
        if (!resp.getBody().getSuccess()) {
            ServerLogContextUtil.setErrorInfo(SvrResultStatus.SYSTEM_ERROR.getStatus(), resp.getBody().getHttpCode().toString(), resp.getBody().getMessage(), resp.getBody().getRequestId());
        }
        return resp;
    }

    @ServerLog(serverName = "vvp", api = "createDeploymentWithOptions")
    public CreateDeploymentResponse createDeployment(Deployment deployment) throws TaskManagerException {
        CreateDeploymentRequest createDeploymentRequest = new CreateDeploymentRequest();
        createDeploymentRequest.setBody(deployment);
        CreateDeploymentHeaders createDeploymentHeaders = new CreateDeploymentHeaders();
        createDeploymentHeaders.setWorkspace(config.getWorkspace());
        CreateDeploymentResponse response;
        try {
            response = client.createDeploymentWithOptions(
                config.getNamespace(), createDeploymentRequest, createDeploymentHeaders, new RuntimeOptions());
        } catch (Exception e) {
            logger.error("[VvpTaskRunner] Exception happened when create deployment! ", e);
            throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), e.getMessage(), e);
        }
        ServerLogContextUtil.setErrorInfo(getServiceErrorInfo(response));
        return response;
    }

    @ServerLog(serverName = "vvp", api = "updateDeploymentWithOptions")
    public UpdateDeploymentResponse updateDeployment(Deployment deployment) throws TaskManagerException {
        UpdateDeploymentRequest updateDeploymentRequest = new UpdateDeploymentRequest();
        updateDeploymentRequest.setBody(deployment);
        UpdateDeploymentHeaders updateDeploymentHeaders = new UpdateDeploymentHeaders();
        updateDeploymentHeaders.setWorkspace(config.getWorkspace());
        UpdateDeploymentResponse response;
        try {
            response = client.updateDeploymentWithOptions(deployment.getNamespace(), deployment.getDeploymentId(),
                updateDeploymentRequest, updateDeploymentHeaders, new RuntimeOptions());
        } catch (Exception e) {
            logger.error("[VvpTaskRunner] Exception happened when update deployment! ", e);
            throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), e.getMessage(), e);
        }
        ServerLogContextUtil.setErrorInfo(getServiceErrorInfo(response));
        return response;
    }

    @ServerLog(serverName = "vvp", api = "getDeploymentWithOptions")
    public GetDeploymentResponse getDeployment(String deploymentId) throws TaskManagerException {
        GetDeploymentHeaders getDeploymentHeaders = new GetDeploymentHeaders();
        getDeploymentHeaders.setWorkspace(config.getWorkspace());

        GetDeploymentResponse response;
        try {
            RuntimeOptions runtimeOptions = new RuntimeOptions();
            runtimeOptions.autoretry = true;
            runtimeOptions.maxAttempts = 3;
            response = client.getDeploymentWithOptions(
                config.getNamespace(), deploymentId, getDeploymentHeaders, runtimeOptions);
        } catch (Exception e) {
            logger.error("[VvpTaskRunner] Exception happened when get deployment! ", e);
            throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), e.getMessage(), e);
        }
        ServerLogContextUtil.setErrorInfo(getServiceErrorInfo(response));
        return response;
    }

    @ServerLog(serverName = "vvp", api = "deleteDeploymentWithOptions")
    public DeleteDeploymentResponse deleteDeployment(String deploymentId) {
        DeleteDeploymentHeaders deleteDeploymentHeaders = new DeleteDeploymentHeaders();
        deleteDeploymentHeaders.setWorkspace(config.getWorkspace());

        DeleteDeploymentResponse response;
        try {
            response = client.deleteDeploymentWithOptions(
                config.getNamespace(), deploymentId, deleteDeploymentHeaders, new RuntimeOptions());
        } catch (Exception e) {
            logger.error("Exception happened when delete deployment! ", e);
            throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), e.getMessage(), e);
        }
        ServerLogContextUtil.setErrorInfo(getServiceErrorInfo(response));
        return response;
    }

    @ServerLog(serverName = "vvp", api = "startJobWithOptions")
    public StartJobResponse startJob(String deploymentId, ResourceConfig resourceCfg, String lastResourceSetting) {
        // restoreStrategy
        DeploymentRestoreStrategy restoreStrategy = new DeploymentRestoreStrategy();
        if (resourceCfg.getStartStrategy() != null && resourceCfg.getStartStrategy().getLatestState()) {
            restoreStrategy.setKind(RestoreStrategy.LATEST_STATE.name());
        } else {
            restoreStrategy.setKind(RestoreStrategy.NONE.name());
            restoreStrategy.setJobStartTimeInMs(resourceCfg.getStartStrategy().getStartMs());

        }

        // brief resource setting
        BriefResourceSetting briefResourceSetting = ResourceSetter.fullySpecifiedBriefResourceSetting(resourceCfg, lastResourceSetting);

        // start job requestBody
        StartJobRequestBody startJobRequestBody = new StartJobRequestBody();
        startJobRequestBody.setDeploymentId(deploymentId);
        startJobRequestBody.setRestoreStrategy(restoreStrategy);
        startJobRequestBody.setResourceSettingSpec(briefResourceSetting);

        StartJobRequest startJobRequest = new StartJobRequest();
        startJobRequest.setBody(startJobRequestBody);

        StartJobHeaders startJobHeaders = new StartJobHeaders();
        startJobHeaders.setWorkspace(config.getWorkspace());

        StartJobResponse response;
        try {
            response = client.startJobWithOptions(
                config.getNamespace(), startJobRequest, startJobHeaders, new RuntimeOptions());
        } catch (Exception e) {
            logger.error("[VvpTaskRunner] Exception happened when start Job! ", e);
            throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), e.getMessage(), e);
        }
        ServerLogContextUtil.setErrorInfo(getServiceErrorInfo(response));

        if (response == null || response.getBody() == null || !response.getBody().getSuccess()) {
            logger.error("[VvpTaskRunner] Start job failed!");
            throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), "Start job failed", "Start job failed");
        }

        boolean changeSuccess;
        try {
            changeSuccess = waitUntilDesiredStatus(response.getBody().getData().getJobId(), "RUNNING");
        } catch (TaskManagerException e) {
            logger.error("[VvpTaskRunner] Exception happened when waiting state changing to running! ", e);
            throw e;
        } catch (Exception e) {
            logger.error("[VvpTaskRunner] Exception happened when waiting state changing to running! ", e);
            throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), e.getMessage(), e);
        }
        if (!changeSuccess) {
            logger.error("[VvpTaskRunner] Failed to change state to running!");
            throw new TaskManagerException("", TaskManagerErrorCode.STATE_ERROR.getCode(), "Task state change failed", "Task state change failed");
        }
        return response;
    }

    @ServerLog(serverName = "vvp", api = "getJobWithOptions")
    public Job getJob(String jobId) {
        GetJobHeaders getJobHeaders = new GetJobHeaders();
        getJobHeaders.setWorkspace(config.getWorkspace());

        GetJobResponse response;
        try {
            RuntimeOptions runtimeOptions = new RuntimeOptions();
            runtimeOptions.autoretry = true;
            runtimeOptions.maxAttempts = 3;
            response = client.getJobWithOptions(config.getNamespace(), jobId, getJobHeaders, runtimeOptions);
        } catch (Exception e) {
            logger.error("[VvpTaskRunner] Exception happened when get job! ", e);
            throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), e.getMessage(), e);
        }
        ServerLogContextUtil.setErrorInfo(getServiceErrorInfo(response));

        if (response == null || response.getBody() == null || response.getBody().getSuccess() == null) {
            throw new TaskManagerException("", TaskManagerErrorCode.RESPONSE_NULL.getCode(),
                "Null response when get job", "Null response");
        }
        if (!response.getBody().getSuccess()) {
            throw new TaskManagerException(response.getBody().getRequestId(), TaskManagerErrorCode.CMD_FAILED.getCode(),
                String.format("Failed to get job, errorCode: %s, errorMessage: %s.",
                    response.getBody().errorCode, response.getBody().errorMessage), response.getBody().getErrorMessage());
        }
        return response.getBody().getData();
    }

    @ServerLog(serverName = "vvp", api = "listJobWithOptions")
    public List<Job> listJob(String deploymentId) {
        ListJobsRequest listJobsRequest = new ListJobsRequest();
        listJobsRequest.setDeploymentId(deploymentId);
        // pageIndex must be larger than 0
        listJobsRequest.setPageIndex(1);
        // pageSize must be between with 1 and 100
        listJobsRequest.setPageSize(100);

        ListJobsHeaders listJobsHeaders = new ListJobsHeaders();
        listJobsHeaders.setWorkspace(config.getWorkspace());

        ListJobsResponse response;
        try {
            RuntimeOptions runtimeOptions = new RuntimeOptions();
            runtimeOptions.autoretry = true;
            runtimeOptions.maxAttempts = 3;
            response = client.listJobsWithOptions(
                config.getNamespace(), listJobsRequest, listJobsHeaders, runtimeOptions);
        } catch (Exception e) {
            logger.error("[VvpTaskRunner] Exception happened when list job! ", e);
            throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), e.getMessage(), e);
        }
        ServerLogContextUtil.setErrorInfo(getServiceErrorInfo(response));

        if (response == null || response.getBody() == null) {
            throw new TaskManagerException("", TaskManagerErrorCode.RESPONSE_NULL.getCode(), "Null response", "Null response");
        }

        String requestId = response.getBody().getRequestId();
        if (!response.getBody().getSuccess()) {
            String errorMessage =
                String.format("Failed to list jobs, errorCode: %s, errorMessage: %s.",
                    response.getBody().errorCode, response.getBody().errorMessage);
            throw new TaskManagerException(requestId, TaskManagerErrorCode.CMD_FAILED.getCode(),
                errorMessage, errorMessage);
        }

        return response.getBody().getData();
    }

    @ServerLog(serverName = "vvp", api = "stopJobWithOptions")
    public StopJobResponse stopJob(String jobId) {
        StopJobRequestBody stopJobRequestBody = new StopJobRequestBody();
        stopJobRequestBody.setStopStrategy(StopStrategy.NONE.name());

        StopJobRequest stopJobRequest = new StopJobRequest();
        stopJobRequest.setBody(stopJobRequestBody);

        StopJobHeaders stopJobHeaders = new StopJobHeaders();
        stopJobHeaders.setWorkspace(config.getWorkspace());

        StopJobResponse response;
        try {
            response = client.stopJobWithOptions(config.getNamespace(), jobId, stopJobRequest, stopJobHeaders,
                new RuntimeOptions());
        } catch (Exception e) {
            logger.error("[VvpTaskRunner] Exception happened when stop job");
            throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), e.getMessage(), e);
        }
        ServerLogContextUtil.setErrorInfo(getServiceErrorInfo(response));

        if (response == null || response.getBody() == null || !response.getBody().getSuccess()) {
            logger.error("[VvpTaskRunner] Stop job failed!");
            throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), "Stop job failed", "Stop job failed");
        }

        boolean changeSuccess;
        try {
            changeSuccess = waitUntilDesiredStatus(jobId, "CANCELLED");
        } catch (TaskManagerException e) {
            logger.error("[VvpTaskRunner] Exception happened when waiting state changing to cancelled! ", e);
            throw e;
        } catch (Exception e) {
            logger.error("[VvpTaskRunner] Exception happened when waiting state changing to cancelled! ", e);
            throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), e.getMessage(), e);
        }

        if (!changeSuccess) {
            logger.error("[VvpTaskRunner] Failed to change job status to cancelled!");
            throw new TaskManagerException("", TaskManagerErrorCode.STATE_ERROR.getCode(), "Task state change failed", "Task state change failed");
        }
        return response;
    }

    @ServerLog(serverName = "vvp", api = "deleteJobWithOptions")
    public DeleteJobResponse deleteJob(String jobId) {
        DeleteJobHeaders deleteJobHeaders = new DeleteJobHeaders();
        deleteJobHeaders.setWorkspace(config.getWorkspace());

        DeleteJobResponse response;
        try {
            response = client.deleteJobWithOptions(config.getNamespace(), jobId, deleteJobHeaders,
                new RuntimeOptions());
        } catch (Exception e) {
            logger.error("Exception happened when delete job! ", e);
            throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), e.getMessage(), e);
        }
        ServerLogContextUtil.setErrorInfo(getServiceErrorInfo(response));
        return response;
    }

    @ServerLog(serverName = "vvp", api = "listDeploymentTargetsWithOptions")
    private List<DeploymentTarget> listDeploymentTargets() {
        ListDeploymentTargetsRequest listDeploymentTargetsRequest = new ListDeploymentTargetsRequest();
        listDeploymentTargetsRequest.setPageSize(100);
        listDeploymentTargetsRequest.setPageIndex(1);

        ListDeploymentTargetsHeaders listDeploymentTargetsHeaders = new ListDeploymentTargetsHeaders();
        listDeploymentTargetsHeaders.setWorkspace(config.getWorkspace());

        ListDeploymentTargetsResponse response;
        try {
            RuntimeOptions runtimeOptions = new RuntimeOptions();
            runtimeOptions.autoretry = true;
            runtimeOptions.maxAttempts = 3;
            response = client.listDeploymentTargetsWithOptions(config.getNamespace(), listDeploymentTargetsRequest,
                listDeploymentTargetsHeaders, runtimeOptions);
        } catch (Exception e) {
            logger.error("Exception happended when listDeploymentTargetsWithOptions! ", e);
            return Collections.emptyList();
        }
        ServerLogContextUtil.setErrorInfo(getServiceErrorInfo(response));

        if (response == null || response.getBody() == null) {
            logger.error("Null response when listDeploymentTargets!");
            return Collections.emptyList();
        }
        if (!response.getBody().getSuccess()) {
            logger.error("Failed to list deployment targets, errorCode: {}, errorMessage: {}.",
                response.getBody().errorCode, response.getBody().errorMessage);
            return Collections.emptyList();
        }
        return response.getBody().getData();
    }

    @ServerLog(serverName = "vvp", api = "listEngineVersionMetadataWithOptions")
    private EngineVersionMetadataIndex listEngineVersionMetadata()
        throws Exception {
        ListEngineVersionMetadataHeaders listEngineVersionMetadataHeaders = new ListEngineVersionMetadataHeaders();
        listEngineVersionMetadataHeaders.setWorkspace(config.getWorkspace());
        RuntimeOptions runtimeOptions = new RuntimeOptions();
        runtimeOptions.autoretry = true;
        runtimeOptions.maxAttempts = 3;
        ListEngineVersionMetadataResponse result = client.listEngineVersionMetadataWithOptions(
            listEngineVersionMetadataHeaders, runtimeOptions);
        ServerLogContextUtil.setErrorInfo(getServiceErrorInfo(result));

        if (result == null || result.getBody() == null) {
            throw new TaskManagerException("", TaskManagerErrorCode.RESPONSE_NULL.getCode(), "Null response", "Null response");
        }

        if (!result.getBody().getSuccess()) {
            throw new RuntimeException(String.format("Failed to list engine versions, errorCode: %s, errorMessage: %s.",
                    result.getBody().errorCode, result.getBody().errorMessage));
        }
        return result.getBody().getData();
    }

    private boolean waitUntilDesiredStatus(String jobId, String desiredStatus)
        throws Exception {
        int retryCount = 0;
        Job currentJob = getJob(jobId);
        while (retryCount++ < MAX_RETRY_COUNT) {
            String currentJobStatus = currentJob.getStatus().getCurrentJobStatus();
            if (currentJobStatus.equals(desiredStatus)) {
                return true;
            }

            boolean reachUnexpectedFinalStatus = !unexpectedFinalStatus.contains(desiredStatus)
                && unexpectedFinalStatus.contains(currentJobStatus);
            if (reachUnexpectedFinalStatus) {
                return false;
            }
            Thread.sleep(SLEEP_MS);
            currentJob = getJob(jobId);
        }

        Thread.sleep(SLEEP_MS);
        return false;
    }


    private <T1, T2, T3, T4, T5, R> R proxyCall(Proxy5<T1, T2, T3, T4, T5, R> f, T1 t1, T2 t2, T3 t3, T4 t4, T5 t5) {
        R response;
        try {
            response = f.apply(t1, t2, t3, t4, t5);
        } catch (Exception e) {
            logger.error("Exception happened when call vvp api {}! ", f.getClass(), e);
            if (e instanceof TeaException) {
                TeaException te = (TeaException) e;
                String svrRequestId = te.getData().get("requestId") == null ? "" : te.getData().get("requestId").toString();
                String statusCode = te.getData().get("statusCode") == null ? "500" : te.getData().get("statusCode").toString();
                ServerLogContextUtil.setErrorInfo(SvrResultStatus.SYSTEM_ERROR.getStatus(), statusCode, te.getMessage(), svrRequestId);
                throw new TaskManagerException(svrRequestId, TaskManagerErrorCode.CMD_FAILED.getCode(), te.getMessage(), te);
            } else {
                ServerLogContextUtil.setErrorInfo(SvrResultStatus.SYSTEM_ERROR.getStatus(), "500", e.getMessage(), "");
                throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), e.getMessage(), e);
            }
        }
        if (response == null) {
            ServerLogContextUtil.setErrorInfo(SvrResultStatus.SYSTEM_ERROR.getStatus(), "500", "got null response", "");
            throw new TaskManagerException("", TaskManagerErrorCode.CMD_FAILED.getCode(), "got null response", "got null response");
        }
        return response;
    }
}
