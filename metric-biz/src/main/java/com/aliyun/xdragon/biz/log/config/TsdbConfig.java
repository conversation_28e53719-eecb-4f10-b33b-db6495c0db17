package com.aliyun.xdragon.biz.log.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/06/27
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "predict.tsdb.sls")
public class TsdbConfig {
    private String user;
    private String region;
    private String project;
    private String logStore;
    private String logStorePickUp;
}
