package com.aliyun.xdragon.biz.integration.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.ecs.devopsapi.domain.xam.DevOpsDeleteRouterParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsRouterParam;
import com.aliyun.ecs.devopsapi.domain.xam.DevOpsXamPreviewTemplateParam;
import com.aliyun.ecs.devopsapi.model.xam.DevOpsRouterModel;
import com.aliyun.ecs.devopsapi.model.xam.result.DevOpsXamResult;
import com.aliyun.phoenix.api.common.response.PlainResult;
import com.aliyun.xdragon.api.service.integration.model.AddOrUpdateIntegrationRequest;
import com.aliyun.xdragon.api.service.integration.model.EnableOrDisableIntegrationRequest;
import com.aliyun.xdragon.api.service.integration.model.IntegrationResponse;
import com.aliyun.xdragon.api.service.integration.model.PreviewAlertParamRequest;
import com.aliyun.xdragon.api.service.integration.model.PreviewAlertTemplateRequest;
import com.aliyun.xdragon.api.service.integration.model.PreviewQueryStatementRequest;
import com.aliyun.xdragon.api.service.integration.model.QueryMonitorInfoRequest;
import com.aliyun.xdragon.api.service.integration.service.FastIntegrationService;
import com.aliyun.xdragon.biz.integration.constant.Constant;
import com.aliyun.xdragon.biz.integration.model.MatchedAlertInfo;
import com.aliyun.xdragon.biz.integration.service.MonitorExecuteService;
import com.aliyun.xdragon.biz.integration.utils.AlertRuleUtil;
import com.aliyun.xdragon.biz.integration.utils.DataTransformHelper;
import com.aliyun.xdragon.biz.integration.utils.IntegrationTypeEnum;
import com.aliyun.xdragon.biz.integration.utils.XamAlertUtil;
import com.aliyun.xdragon.biz.monitor.service.MonitorNotifyServiceImpl;
import com.aliyun.xdragon.common.exception.XdragonMetricViolationException;
import com.aliyun.xdragon.common.generate.model.XdragonMonitorAlertInfo;
import com.aliyun.xdragon.common.generate.model.XdragonMonitorInfo;
import com.aliyun.xdragon.common.generate.model.map.XdragonMonitorAlertInfoMapper;
import com.aliyun.xdragon.common.generate.model.map.XdragonMonitorInfoMapper;
import com.aliyun.xdragon.common.model.SlsProjectGroup;
import com.aliyun.xdragon.common.model.XdragonMetricResponse;
import com.aliyun.xdragon.service.common.config.diamond.DiamondConfigItem;
import com.aliyun.xdragon.service.common.config.diamond.DiamondConfigManager;
import com.aliyun.xdragon.service.common.service.XdragonAlertService;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2023/8/30
 */
@Service
public class FastIntegrationServiceImpl implements FastIntegrationService {

    private static final Logger logger = LoggerFactory.getLogger(FastIntegrationServiceImpl.class);

    @Resource
    private XdragonMonitorInfoMapper monitorInfoMapper;

    @Resource
    private XdragonMonitorAlertInfoMapper alertInfoMapper;

    @Resource
    private MonitorExecuteService monitorExecuteService;

    @Resource
    private XdragonAlertService xdragonAlertService;

    private final String XUNJIAN_AK = "LTAI5t8XJwrKfSWmDGzjwJ7o";

    private final String XUNJIAN_SK = "akmZ3wsX1+ZzwEnBdxCR4Gg3aSdzFCMjojKkyi4CM8g=";

    /**
     * query all of projectName in diamond.
     * @return
     */
    @Override
    public XdragonMetricResponse<Set<String>> queryAllProjectNames() {
        return XdragonMetricResponse.ofSuccess(monitorExecuteService.queryAllProjectNames());
    }

    /**
     * 查询全量sls配置组
     * @return
     */
    @Override
    public XdragonMetricResponse<List<SlsProjectGroup>> queryAllSlsProjectGroup() {
        return XdragonMetricResponse.ofSuccess(monitorExecuteService.queryProjectRegionGroupList());
    }

    /**
     * 查询接入配置信息
     * @param request {@link QueryMonitorInfoRequest}
     * @return {@link IntegrationResponse}
     */
    @Override
    public XdragonMetricResponse<List<IntegrationResponse>> queryIntegrationConfig(QueryMonitorInfoRequest request) {
        XdragonMonitorInfo queryRequest = new XdragonMonitorInfo();
        queryRequest.setMonitorName(request.getMonitorName());
        queryRequest.setCreator(request.getCreator());
        queryRequest.setDataSource(request.getDataSource());
        queryRequest.setLogStore(request.getLogStore());

        List<XdragonMonitorInfo> xdragonMonitorInfos = monitorInfoMapper.queryList(queryRequest);
        if (CollectionUtils.isEmpty(xdragonMonitorInfos)) {
            logger.info("queryIntegrationConfig with param no data: {}", JSON.toJSONString(request));
            return XdragonMetricResponse.ofSuccess(Collections.emptyList());
        }
        Set<Long> monitorIds = xdragonMonitorInfos.stream().map(XdragonMonitorInfo::getId).collect(Collectors.toSet());
        List<XdragonMonitorAlertInfo> xdragonMonitorAlertInfos = alertInfoMapper.queryListByMonitorId(monitorIds);

        List<IntegrationResponse> responseList =
            DataTransformHelper.buildIntegrationResponse(xdragonMonitorInfos, xdragonMonitorAlertInfos);
        logger.info("queryIntegrationConfig with param: {}, result: {}", JSON.toJSONString(request), JSON.toJSONString(responseList));
        return XdragonMetricResponse.ofSuccess(responseList);
    }

    /**
     * 新增或更新快速接入配置信息
     * @param request {@link AddOrUpdateIntegrationRequest}
     * @return boolean
     */
    @Override
    public XdragonMetricResponse<Boolean> insertOrUpdateIntegrationConfig(AddOrUpdateIntegrationRequest request) {
        try {
            request.validate();
        } catch (XdragonMetricViolationException e) {
            logger.info("insertOrUpdateIntegrationConfig with invalid param: {}", JSON.toJSONString(request), e);
            return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_BAD_REQUEST), e.getMessage());
        }
        logger.info("insertOrUpdateIntegrationConfig with request param: {}", JSON.toJSONString(request));
        XdragonMonitorInfo xdragonMonitorInfo = DataTransformHelper.monitorRequest2DO(request.getIntegrationMonitorInfo());
        XdragonMonitorInfo queryRequest = new XdragonMonitorInfo();
        queryRequest.setMonitorName(xdragonMonitorInfo.getMonitorName());
        List<XdragonMonitorInfo> existInfoList = monitorInfoMapper.queryList(queryRequest);

        List<XdragonMonitorAlertInfo> alertInfos = request.getAlertInfoRequestList().stream()
                .map(DataTransformHelper::alertInfoRequest2DO).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(existInfoList)) {
            // insert
            // 新建新的 xam 通知配置
            boolean insertXamSucceeded = insertRouterConfigToXam(xdragonMonitorInfo, alertInfos);
            if (!insertXamSucceeded) {
                logger.error("create or update xam alert conf failed");
                return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_INTERNAL_SERVER_ERROR), "insert or update alertInfo failed");
            }
            int monitorInfoInsertCount = monitorInfoMapper.insertOrUpdate(xdragonMonitorInfo);
            if (monitorInfoInsertCount == 0 || Objects.isNull(xdragonMonitorInfo.getId())) {
                logger.error("insertOrUpdate monitorInfo failed, param: {}", JSON.toJSONString(xdragonMonitorInfo));
                return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_INTERNAL_SERVER_ERROR), "insert monitor info failed");
            }
        } else {
            // update
            // 更新场景，如果有新增的通知配置，需要在 xam 更新关联标签
            xdragonMonitorInfo.setId(existInfoList.get(0).getId());
            List<XdragonMonitorAlertInfo> existingAlertInfos = alertInfoMapper.queryListByMonitorId(Collections.singleton(xdragonMonitorInfo.getId()));
            boolean needUpdate = false;
            Map<String, XdragonMonitorAlertInfo> existingAlertInfoMap = existingAlertInfos.stream().collect(Collectors.toMap(XdragonMonitorAlertInfo::getAlertName, Function.identity()));
            Map<String, XdragonMonitorAlertInfo> newAlertInfoMap = alertInfos.stream().collect(Collectors.toMap(XdragonMonitorAlertInfo::getAlertName, Function.identity()));
            if (!Objects.equals(existInfoList.get(0).getMatchExpression(), xdragonMonitorInfo.getMatchExpression())) {
                needUpdate = true;
            }
            if (!existingAlertInfoMap.keySet().equals(newAlertInfoMap.keySet())) {
                needUpdate = true;
            }
            for (XdragonMonitorAlertInfo existAlertInfo: existingAlertInfos) {
                if (!newAlertInfoMap.containsKey(existAlertInfo.getAlertName())) {
                    needUpdate = true;
                    break;
                }
                XdragonMonitorAlertInfo newAlertInfo = newAlertInfoMap.get(existAlertInfo.getAlertName());
                if (!Objects.equals(existAlertInfo.getMatchExpression(), newAlertInfo.getMatchExpression())) {
                    needUpdate = true;
                    break;
                }
                if (!Objects.equals(existAlertInfo.getPushDingjie(), newAlertInfo.getPushDingjie())) {
                    needUpdate = true;
                    break;
                }
                if (!Objects.equals(existAlertInfo.getResourceIdSplit(), newAlertInfo.getResourceIdSplit())) {
                    needUpdate = true;
                    break;
                }
            }
            if (needUpdate) {
                // 如果有通知的更新，需要更新 xam 通知配置关联的标签, 匹配的表达式等
                boolean updateXamSuccess = updateXamMonitor(xdragonMonitorInfo, alertInfos);
                if (!updateXamSuccess) {
                    logger.error("create or update xam alert conf failed");
                    return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_INTERNAL_SERVER_ERROR), "insert or update alertInfo failed");
                }
            }
            monitorInfoMapper.updateById(xdragonMonitorInfo);
        }
        Long monitorId = xdragonMonitorInfo.getId();
        alertInfos.forEach(info -> info.setMonitorId(monitorId));
        boolean insertAlertInfoSuccess = this.batchInsertOrUpdateAlertInfos(monitorId, alertInfos);
        return XdragonMetricResponse.ofSuccess(insertAlertInfoSuccess);
    }

    /**
     * 在通知侧新增配置
     * 每个 xdragonMonitorInfo.monitorName 需要对应到一个通知那边的配置。(只有一个统一的配置存在问题，如：无法使用根据 UserInfo，VmInfo 等过滤的能力，无法自定义通知模版)
     * @param xdragonMonitorInfo
     * @param alertInfos
     * @return 是否创建成功
     */
    private boolean insertRouterConfigToXam(XdragonMonitorInfo xdragonMonitorInfo, List<XdragonMonitorAlertInfo> alertInfos) {
        DevOpsRouterParam param = new DevOpsRouterParam();
        param.setRequestId(UUID.randomUUID().toString());
        // TODO 改为业务方的 ownerGroup
        param.setOwnerGroup("FAST_INTEGRATION");
        param.setRouterName(XamAlertUtil.getRouterName(xdragonMonitorInfo));
        param.setSourceLabel(XamAlertUtil.SOURCE_LABEL);
        param.setMonitorLabels(XamAlertUtil.getMonitorLabel(xdragonMonitorInfo, alertInfos));
        param.setMatchExpression(xdragonMonitorInfo.getMatchExpression());
        param.setRequestId(UUID.randomUUID().toString());
        List<DevOpsRouterParam.Receiver> receivers = new ArrayList<>();
        alertInfos.forEach(alertInfo -> {
            receivers.add(constructReceiver(xdragonMonitorInfo, alertInfo, alertInfos));
            if (alertInfo.getPushDingjie()) {
                receivers.add(constructPushSlsReceiver(xdragonMonitorInfo, alertInfo, alertInfos));
            }
        });
        param.setReceivers(receivers);
        boolean insertSuccess = false;
        try {
            logger.info("Fast integration addNewRouter, param: {}",
                    JSONObject.toJSONString(param));
            DevOpsXamResult<Integer> result = xdragonAlertService.addNewRouter(param);
            if (result.isSuccess()) {
                logger.info("Fast integration addNewRouter success, response: {}", JSONObject.toJSONString(result));
                insertSuccess = true;
            } else {
                logger.error("Fast integration addNewRouter failed, message: {}", result.getMessage());
            }
        } catch (Exception e) {
            logger.error("Fast integration addNewRouter got exception, {}", e.getMessage());
        }
        if (!insertSuccess) {
            try {
                DevOpsXamResult<DevOpsRouterModel> routerResult = xdragonAlertService.getRouter(param.getRouterName());
                if (routerResult.isSuccess() && Objects.nonNull(routerResult.getData())) {
                    logger.info("Fast integration addNewRouter actually succeeded");
                    // TODO 确认其他字段？
                    insertSuccess = true;
                }
            } catch (Exception e) {
                logger.error("Fast integration query router got exception, {}", e.getMessage());
            }
        }
        return insertSuccess;
    }

    private DevOpsRouterParam.Receiver constructReceiver(XdragonMonitorInfo xdragonMonitorInfo, XdragonMonitorAlertInfo alertInfo, List<XdragonMonitorAlertInfo> alertInfos) {
        DevOpsRouterParam.Receiver receiver = new DevOpsRouterParam.Receiver();
        // token 目前没有作用，实际是从发送的参数中获取的，但是 DingGroup 这个 receiverType 会校验这个参数
        receiver.setToken("xxx");
        // receiveType 没有实际作用，但接口参数要求必传
        receiver.setReceiverType("DingGroup");
        // 使用快速接入自己的 receiver 发送通知
        receiver.setReceiverClass("fast_in");
        // 使用快速接入自己的 constructDataClass, 注意这里传入的可能会在发通知时被重写
        if (alertInfo.getResourceIdSplit()) {
            receiver.setConstructDataClass("fast_in_single");
        } else {
            receiver.setConstructDataClass("fast_in_merge");
        }
        // 有作用，通知优先用配置在模版中的
        if (xdragonMonitorInfo.getMonitorName().startsWith("ehpc")) {
            // TODO 临时 hack 一下，需要新增一个字段
            receiver.setTemplateName("fast_integration_ehpc");
        } else {
            receiver.setTemplateName("multiple_cloudops");
        }
        // 没有实际作用
        receiver.setAtUser(Arrays.asList(alertInfo.getReceiver().split(",")));
        // 通知侧聚合时间
        receiver.setGroupByTime(60);

        // 设置 advancedOption 的过滤标签
        // Note:后续可以如果需要 by receiver 配置告警生效时间、静默、表达式过滤，都可以通过这个完成
        DevOpsRouterParam.ReceiverAdvancedOption advancedOption = new DevOpsRouterParam.ReceiverAdvancedOption();
        advancedOption.setMonitorLabel(XamAlertUtil.getMonitorLabel(xdragonMonitorInfo, alertInfos));
        advancedOption.setMatchExpression(getAdvancedOptionExpression(xdragonMonitorInfo, alertInfo));
        if (Objects.nonNull(alertInfo.getSilence())) {
            advancedOption.setSilenceTime(alertInfo.getSilence());
            advancedOption.setSilenceType("resourceId");
        }

        receiver.setReceiverAdvancedOptionList(Collections.singletonList(advancedOption));
        return receiver;
    }

    private static String getAdvancedOptionExpression(XdragonMonitorInfo xdragonMonitorInfo, XdragonMonitorAlertInfo alertInfo) {
        String defaultExpression = String.format("\"{{monitorLabel}}\"==\"%s\"", XamAlertUtil.getMonitorLabel(xdragonMonitorInfo, alertInfo));
        return StringUtils.isBlank(alertInfo.getMatchExpression()) ? defaultExpression : String.format("(%s) and (%s)", defaultExpression, alertInfo.getMatchExpression());
    }

    private DevOpsRouterParam.Receiver constructPushSlsReceiver(XdragonMonitorInfo xdragonMonitorInfo, XdragonMonitorAlertInfo alertInfo, List<XdragonMonitorAlertInfo> alertInfos) {
        DevOpsRouterParam.Receiver receiver = new DevOpsRouterParam.Receiver();
        // token 目前没有作用，实际是从发送的参数中获取的，但是 DingGroup 这个 receiverType 会校验这个参数
        receiver.setToken("xxx");
        // receiveType 没有实际作用，但接口参数要求必传
        receiver.setReceiverType("Sls");
        // 通用 sls receiver
        receiver.setReceiverClass("sls");
        // 使用快速接入自己的 constructDataClass
        receiver.setConstructDataClass("sls_fast_in");
        // 发送 sls 场景，这个参数没有实际作用
        receiver.setTemplateName("default");
        // 没有实际作用
        receiver.setAtUser(Arrays.asList(alertInfo.getReceiver().split(",")));
        // 通知侧聚合时间
        receiver.setGroupByTime(60);
        // sls 的配置
        receiver.setSlsAk(XUNJIAN_AK);
        receiver.setSlsSk(XUNJIAN_SK);
        receiver.setSlsEndpoint("cn-hangzhou-corp.sls.aliyuncs.com");
        receiver.setSlsProject("ecs-xunjian");
        receiver.setSlsLogStore((String)DiamondConfigManager.getConfig(DiamondConfigItem.FAST_IN_PUSH_SLS_TARGET_LOGSTORE));

        // 设置 advancedOption 的过滤标签
        // Note:后续可以如果需要 by receiver 配置告警生效时间、静默、表达式过滤，都可以通过这个完成
        DevOpsRouterParam.ReceiverAdvancedOption advancedOption = new DevOpsRouterParam.ReceiverAdvancedOption();
        advancedOption.setMonitorLabel(XamAlertUtil.getMonitorLabel(xdragonMonitorInfo, alertInfos));
        advancedOption.setMatchExpression(getAdvancedOptionExpression(xdragonMonitorInfo, alertInfo));
        if (Objects.nonNull(alertInfo.getSilence())) {
            advancedOption.setSilenceTime(alertInfo.getSilence());
            advancedOption.setSilenceType("resourceId");
        }

        receiver.setReceiverAdvancedOptionList(Collections.singletonList(advancedOption));
        return receiver;
    }

    /**
     * 更新 xam 通知项关联的标签
     * @param xdragonMonitorInfo
     * @param alertInfos
     * @return
     */
    private boolean updateXamMonitor(XdragonMonitorInfo xdragonMonitorInfo, List<XdragonMonitorAlertInfo> alertInfos) {
        DevOpsRouterModel existingRouter = null;
        String routerName = XamAlertUtil.getRouterName(xdragonMonitorInfo);
        try {
            DevOpsXamResult<DevOpsRouterModel> routerResult = xdragonAlertService.getRouter(routerName);
            existingRouter = routerResult.getData();
        } catch (Exception e) {
            logger.error("Fast integration query router got exception, {}", e.getMessage());
            return false;
        }
        if (existingRouter == null) {
            logger.info("to update router no exist, routerName: {}", routerName);
            return false;
        }
        DevOpsRouterParam param = new DevOpsRouterParam();
        // TODO 改为业务方的 ownerGroup
        param.setOwnerGroup("FAST_INTEGRATION");
        param.setRouterName(XamAlertUtil.getRouterName(xdragonMonitorInfo));
        param.setMatchExpression(xdragonMonitorInfo.getMatchExpression());
        param.setSourceLabel(XamAlertUtil.SOURCE_LABEL);
        // 修改 monitorLabels
        List<String> monitorLabels = XamAlertUtil.getMonitorLabel(xdragonMonitorInfo, alertInfos);
        param.setMonitorLabels(monitorLabels);
        // 必须有，通知接口会校验 revision
        param.setRevision(existingRouter.getRevision());
        param.setRequestId(UUID.randomUUID().toString());
        // receiver 同 insert，不管有没有修改 receivers，都重新构造传到通知侧
        List<DevOpsRouterParam.Receiver> receivers = new ArrayList<>();
        alertInfos.forEach(alertInfo -> {
            receivers.add(constructReceiver(xdragonMonitorInfo, alertInfo, alertInfos));
            if (alertInfo.getPushDingjie()) {
                receivers.add(constructPushSlsReceiver(xdragonMonitorInfo, alertInfo, alertInfos));
            }
        });
        param.setReceivers(receivers);

        DevOpsXamResult<Boolean> updateRouterResult;
        try {
            updateRouterResult = xdragonAlertService.updateRouter(param);
            if (updateRouterResult.isSuccess() && updateRouterResult.getData()) {
                return true;
            }
            logger.error("Fast integration update router labels failed, message: {}, param: {}", updateRouterResult.getMessage(), JSONObject.toJSONString(param));
        } catch (Exception e) {
            logger.error("Fast integration update router got exception, {}", e.getMessage());
        }
        // 兜底确认下请求有没有成功
        try {
            DevOpsXamResult<DevOpsRouterModel> routerResult = xdragonAlertService.getRouter(param.getRouterName());
            if (routerResult.isSuccess() && Objects.nonNull(routerResult.getData())) {
                DevOpsRouterModel data = routerResult.getData();
                Set<String> routerMonitorLabels = new HashSet<>(data.getMonitorLabels());
                if (routerMonitorLabels.size() == monitorLabels.size() && routerMonitorLabels.containsAll(monitorLabels)) {
                    logger.info("Fast integration update router actually succeeded");
                    return true;
                }
            }
        } catch (Exception e) {
            logger.error("Fast integration query router got exception, {}", e.getMessage());

        }
        return false;
    }

    /**
     * 预览查询，用于校验用户输入的SLS相关配置是否正确
     * @param request {@link PreviewQueryStatementRequest}
     * @return
     */
    @Override
    public XdragonMetricResponse<List<Map<String, Object>>> previewQueryStatement(PreviewQueryStatementRequest request) {
        try {
            request.validate();
        } catch (XdragonMetricViolationException e) {
            logger.info("previewQueryStatement with invalid param: {}", JSON.toJSONString(request), e);
            return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_BAD_REQUEST), e.getMessage());
        }
        XdragonMonitorInfo monitorInfo = new XdragonMonitorInfo();
        monitorInfo.setDataSource(request.getDataSource());
        monitorInfo.setLogStore(request.getLogStore());
        monitorInfo.setQueryStatement(request.getQueryStatement());
        monitorInfo.setTimeRange(request.getTimeRangeSecond());

        try {
            logger.info("previewQueryStatement with param: {}", JSON.toJSONString(request));
            List<Map<String, Object>> result;
            if (StringUtils.isNotBlank(request.getRegion())) {
                // specified region
                result = monitorExecuteService.querySlsForPreCheck(monitorInfo, request.getRegion());
            } else {
                result = monitorExecuteService.querySlsWithDefaultProjectForPreCheck(monitorInfo);
            }
            logger.info("previewQueryStatement result: {}", JSON.toJSONString(result));
            return XdragonMetricResponse.ofSuccess(result);
        } catch (Exception e) {
            logger.error("previewQueryStatement got error", e);
            return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_BAD_REQUEST), e.getMessage());
        }
    }

    /**
     * 启用/禁用配置
     * @param request {@link EnableOrDisableIntegrationRequest}
     * @return boolean
     */
    @Override
    public XdragonMetricResponse<Boolean> enableOrDisableIntegration(EnableOrDisableIntegrationRequest request) {
        try {
            request.validate();
        } catch (XdragonMetricViolationException e) {
            return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_BAD_REQUEST), e.getMessage());
        }
        if (IntegrationTypeEnum.MONITOR_INFO.name().equals(request.getIntegrationType())) {
            // 启用/禁用监控配置
            XdragonMonitorInfo xdragonMonitorInfo = monitorInfoMapper.selectOneById(request.getId());
            logger.info("enableOrDisableIntegration is monitor, request: {}, data: {}",
                JSON.toJSONString(request), JSON.toJSONString(xdragonMonitorInfo));
            if (Objects.isNull(xdragonMonitorInfo)) {
                return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_NOT_FOUND), "data not found");
            }
            Byte targetValue = request.getEnable() ? Constant.MONITOR_INFO_ENABLE : Constant.MONITOR_INFO_DISABLE;
            int count = monitorInfoMapper.enableOrDisableInfo(targetValue, request.getId());
            return count > 0 ? XdragonMetricResponse.ofSuccess(true)
                : XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_INTERNAL_SERVER_ERROR), "operation failed");
        }
        return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_BAD_REQUEST), "unknown operation");
    }

    /**
     * 删除接入配置
     * @param request {@link EnableOrDisableIntegrationRequest}
     * @return boolean
     */
    @Override
    public XdragonMetricResponse<Boolean> deleteIntegration(EnableOrDisableIntegrationRequest request) {
        try {
            request.validate();
        } catch (XdragonMetricViolationException e) {
            return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_BAD_REQUEST), e.getMessage());
        }
        // 删除监控配置，删除后，需要将关联的告警配置也一并删除
        if (IntegrationTypeEnum.MONITOR_INFO.name().equals(request.getIntegrationType())) {
            XdragonMonitorInfo xdragonMonitorInfo = monitorInfoMapper.selectOneById(request.getId());
            logger.info("deleteIntegration is monitor, data: {}", JSON.toJSONString(xdragonMonitorInfo));
            if (Objects.isNull(xdragonMonitorInfo)) {
                return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_NOT_FOUND), "data not found");
            }

            boolean deleteRouterSuccess = deleteXamRouter(xdragonMonitorInfo);
            if (!deleteRouterSuccess) {
                return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_INTERNAL_SERVER_ERROR), "delete monitor info failed");
            }

            int deleteMonitorCount = monitorInfoMapper.deleteById(request.getId());
            if (deleteMonitorCount == 0) {
                return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_INTERNAL_SERVER_ERROR), "delete monitor info failed");
            }

            // 成功删除监控配置后，将与其相关的告警配置也一并删除
            List<XdragonMonitorAlertInfo> alertInfoList =
                alertInfoMapper.queryListByMonitorId(Collections.singleton(xdragonMonitorInfo.getId()));
            logger.info("deleteIntegration deleting alert after deleted monitor, alertInfos: {}",
                JSON.toJSONString(alertInfoList));
            if (CollectionUtils.isEmpty(alertInfoList)) {
                return XdragonMetricResponse.ofSuccess(true);
            }
            int deleteAlertCount = alertInfoMapper.batchDeleteByMonitorId(xdragonMonitorInfo.getId());
            return XdragonMetricResponse.ofSuccess(deleteAlertCount == alertInfoList.size());
        }
        if (IntegrationTypeEnum.MONITOR_ALERT_INFO.name().equals(request.getIntegrationType())) {
            XdragonMonitorAlertInfo alertInfo = alertInfoMapper.selectByPrimaryKey(request.getId());
            logger.info("deleteIntegration is alertInfo, data: {}", JSON.toJSONString(alertInfo));
            if (Objects.isNull(alertInfo)) {
                return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_NOT_FOUND), "data not found");
            }
            int deleteCount = alertInfoMapper.deleteByPrimaryKey(request.getId());
            return deleteCount > 0 ? XdragonMetricResponse.ofSuccess(true)
                : XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_INTERNAL_SERVER_ERROR), "operation failed");
        }
        return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_INTERNAL_SERVER_ERROR), "unknown operation");
    }

    @Override
    public XdragonMetricResponse<Map<String, Object>> previewAlertParam(PreviewAlertParamRequest request) {
        XdragonMonitorInfo monitorInfo = new XdragonMonitorInfo();
        monitorInfo.setDataSource(request.getDataSource());
        monitorInfo.setLogStore(request.getLogStore());
        monitorInfo.setQueryStatement(request.getQueryStatement());
        monitorInfo.setTimeRange(request.getTimeRangeSecond());
        monitorInfo.setResourceType(request.getResourceType());
        monitorInfo.setMonitorName(request.getMonitorName());
        monitorInfo.setResourceId(request.getResourceId());

        List<Map<String, Object>> result;
        try {
            logger.info("previewAlertParam with param: {}", JSON.toJSONString(request));
            if (StringUtils.isNotBlank(request.getRegion())) {
                // specified region
                result = monitorExecuteService.querySlsForPreCheck(monitorInfo, request.getRegion());
            } else {
                result = monitorExecuteService.querySlsWithDefaultProjectForPreCheck(monitorInfo);
            }
            logger.info("previewAlertParam result: {}", JSON.toJSONString(result));
        } catch (Exception e) {
            logger.error("previewAlertParam got error", e);
            return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_BAD_REQUEST), e.getMessage());
        }
        if (result.isEmpty()) {
            return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_BAD_REQUEST), "no data can be fetched");
        }

        Map<String, Object> toPreviewSlsData = result.get(0);

        XdragonMonitorAlertInfo alertInfo = new XdragonMonitorAlertInfo();
        alertInfo.setAlertName(request.getAlertName());
        alertInfo.setLevel(request.getAlertLevel());
        alertInfo.setPushDingjie(false);
        alertInfo.setAlertChannel("[\"DingTalk\"]");
        alertInfo.setReceiver("[\"test\"]");
        alertInfo.setResourceIdSplit(request.getResourceIdSplit());

        String resource = AlertRuleUtil.extractSlsData(toPreviewSlsData, monitorInfo.getResourceId());
        MatchedAlertInfo matchedAlertInfo = new MatchedAlertInfo().setMonitorAlertInfo(alertInfo)
                .setMonitorInfo(monitorInfo).setMatchedData(Collections.singleton(resource)).setRegion(request.getRegion());
        Map<String, String> alertParamMap = MonitorNotifyServiceImpl.getAlertParamMap(DataTransformHelper.alertInfo2Message(toPreviewSlsData, matchedAlertInfo).get(0));

        Map<String, Object> paramResult = new HashMap<>(alertParamMap);
        // 快速接入解析 ext 信息
        if (alertParamMap.containsKey("ext") && !StringUtils.isBlank((String)alertParamMap.get("ext")) && ((String)alertParamMap.get("ext")).startsWith("{")) {
            try {
                Map<String, Object> extMap = JSONObject.parseObject((String)alertParamMap.get("ext"), Map.class);
                paramResult.put("extMap", extMap);
            } catch (Exception e) {
                logger.info("parse ext to json map failed, ext: {}", alertParamMap.get("ext"));
            }
        }
        return XdragonMetricResponse.ofSuccess(paramResult);
    }

    @Override
    public XdragonMetricResponse<String> previewAlertTemplate(PreviewAlertTemplateRequest request) {
        logger.info("preview template, request: {}", JSONObject.toJSONString(request));
        DevOpsXamPreviewTemplateParam param = new DevOpsXamPreviewTemplateParam();
        param.setTemplateName("multiple_cloudops");
        param.setReceiverClass("fast_in");
        param.setConstructClass("fast_in_single");
        DevOpsXamPreviewTemplateParam.DevOpsOriginSendItemDetail itemDetail = new DevOpsXamPreviewTemplateParam.DevOpsOriginSendItemDetail();
        itemDetail.setFromSource("fast_integration");

        try {
            JSONObject paramMap = JSONObject.parseObject(request.getParamStr());
            // multiple receiver in xam must pass following keys in param
            paramMap.putIfAbsent("templateName", "multiple_cloudops");
            paramMap.putIfAbsent("alarmChannel", JSONObject.toJSONString(Collections.singletonList("DingTalk")));
            paramMap.putIfAbsent("dingUser", JSONObject.toJSONString(Collections.singletonList("test")));
            itemDetail.setParamsStr(JSONObject.toJSONString(paramMap));
        } catch (Exception e) {
            logger.error("param not json string, param: {}", request.getParamStr());
            itemDetail.setParamsStr(request.getParamStr());
        }
        itemDetail.setMonitorLabel(request.getMonitorName() + "-" + request.getAlertName());

        param.setItemDetails(Collections.singletonList(itemDetail));
        try {
            PlainResult<String> result = xdragonAlertService.previewTemplate(param);
            return XdragonMetricResponse.ofSuccess(result.getData());
        } catch (Exception e) {
            logger.error("preview failed, error: {}", e.getMessage());
            return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_BAD_REQUEST), e.getMessage());
        }

    }

    private boolean deleteXamRouter(XdragonMonitorInfo xdragonMonitorInfo) {
        // 删除对应的 xam 告警配置
        DevOpsDeleteRouterParam param = new DevOpsDeleteRouterParam();
        param.setRouterName(XamAlertUtil.getRouterName(xdragonMonitorInfo));
        param.setOwnerGroup("FAST_INTEGRATION");
        DevOpsXamResult<Boolean> booleanDevOpsXamResult;
        try {
            booleanDevOpsXamResult = xdragonAlertService.deleteRouter(param);
            if (booleanDevOpsXamResult.isSuccess()) {
                return true;
            }
            logger.error("delete xam alert conf failed, router: {}", xdragonMonitorInfo.getMonitorName());
        } catch (Exception e) {
            logger.error("delete xam alert conf failed, router: {}, msg: {}", xdragonMonitorInfo.getMonitorName(), e.getMessage());
        }
        try {
            DevOpsXamResult<DevOpsRouterModel> routerModel = xdragonAlertService.getRouter(param.getRouterName());
            if (routerModel.isSuccess() && Objects.isNull(routerModel.getData())) {
                logger.info("router {} already not exist", xdragonMonitorInfo.getMonitorName());
                return true;
            }
        } catch (Exception e) {
            logger.error("get router failed, router: {}, msg: {}", xdragonMonitorInfo.getMonitorName(), e.getMessage());
        }
        return false;
    }

    private boolean batchInsertOrUpdateAlertInfos(Long monitorId, List<XdragonMonitorAlertInfo> alertInfoList) {
        if (CollectionUtils.isEmpty(alertInfoList)) {
            return true;
        }
        logger.info("batchInsertOrUpdateAlertInfos, param: {}, alertCount: {}", JSON.toJSONString(alertInfoList), alertInfoList.size());
        // 简单起见，直接将现有的删除，重新插入一份
        alertInfoMapper.batchDeleteByMonitorId(monitorId);
        int alertInsertCount = alertInfoMapper.batchInsertOrUpdate(alertInfoList);
        logger.info("batchInsertOrUpdateAlertInfos, param: {}, result: {}", JSON.toJSONString(alertInfoList), alertInsertCount);
        return alertInsertCount > 0;
    }

}
