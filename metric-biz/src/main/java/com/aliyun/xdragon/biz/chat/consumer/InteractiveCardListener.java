package com.aliyun.xdragon.biz.chat.consumer;

import com.dingtalk.open.app.api.OpenDingTalkClient;
import com.dingtalk.open.app.api.OpenDingTalkStreamClientBuilder;
import com.dingtalk.open.app.api.callback.DingTalkStreamTopics;
import com.dingtalk.open.app.api.security.AuthClientCredential;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class InteractiveCardListener {
    @Value("${ding.talk.appKey}")
    private String appKey;

    @Value("${ding.talk.appSecret}")
    private String appSecret;
    @Autowired
    private CardCallbackHandler cardCallbackHandler;

    @PostConstruct
    public void init() throws Exception {
        // init stream client
        OpenDingTalkClient client = OpenDingTalkStreamClientBuilder
                .custom()
                .credential(new AuthClientCredential(appKey, appSecret))
                .registerCallbackListener(DingTalkStreamTopics.CARD_CALLBACK_TOPIC, cardCallbackHandler)
                .build();
        client.start();
    }

}
