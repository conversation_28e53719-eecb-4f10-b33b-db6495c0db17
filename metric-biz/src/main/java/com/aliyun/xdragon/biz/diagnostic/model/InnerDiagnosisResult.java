package com.aliyun.xdragon.biz.diagnostic.model;

import com.aliyun.xdragon.common.enumeration.DiagnosisStatus;
import com.aliyun.xdragon.common.model.metric.DiagnosisResult;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/03/13
 * @description 性能诊断各子序列的诊断结果，不对外透出
 */
@Data
public class InnerDiagnosisResult {
    /**
     * 诊断给出的结果
     */
    private DiagnosisStatus status;
    /**
     * 给出诊断结果的理由
     */
    private Reason reason;
    /**
     * 诊断开始时间
     */
    private int startTs;
    /**
     * 诊断结束时间
     */
    private int endTs;

    /**
     * 转换为诊断结果
     *
     * @param scenario 场景
     * @param problem  问题
     * @return 诊断结果
     */
    public DiagnosisResult toDiagnosisResult(String scenario, String problem) {
        DiagnosisResult result = new DiagnosisResult();
        result.setScenario(scenario);
        result.setProblem(problem);
        result.setStatus(status);
        result.setReason(reason.toString());
        result.setStartTs(startTs);
        result.setEndTs(endTs);
        return result;
    }
}
