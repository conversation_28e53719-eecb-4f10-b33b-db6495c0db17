package com.aliyun.xdragon.biz.metric.job;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.odps.data.Record;
import com.aliyun.xdragon.biz.metric.repository.MetricDetailDao;
import com.aliyun.xdragon.biz.metric.repository.MetricSourceDao;
import com.aliyun.xdragon.common.generate.model.MetricDetail;
import com.aliyun.xdragon.common.generate.model.MetricDetail.Column;
import com.aliyun.xdragon.common.generate.model.MetricSource;
import com.aliyun.xdragon.service.common.agent.OdpsClient;
import com.aliyun.xdragon.service.common.job.AbstractProcessTask;
import com.aliyun.xdragon.service.common.util.DateUtil;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/22
 */
@Service
@Lazy
public class MetricPeriodSyncJob extends AbstractProcessTask {
    private static final Logger logger = LoggerFactory.getLogger(MetricPeriodSyncJob.class);

    public static final int SCORE_THRESHOLD = 20;

    public static final int MISSING_RATE_THRESHOLD = 10;

    public static final int ODPS_PAGE_SIZE = 1000;

    @Autowired
    public OdpsClient odpsClient;

    @Autowired
    private MetricDetailDao metricDetailDao;

    @Autowired
    private MetricSourceDao metricSourceDao;

    @Override
    public ProcessResult process(JobContext context, int dataTs, int schedTs) throws Exception {
        Map<String, List<MetricSource>> sourceMap = getMetricSourceMap();
        if (sourceMap.size() <= 0) {
            return new ProcessResult(true, "there are no metric source need period analyze");
        }
        int pageNum = 0;
        Date dsDate = new Date();
        // 获取昨天的日期作为同步的数据时间
        String ds = DateUtil.dateFormat(DateUtil.modifyDateByDay(dsDate, -3), "yyyyMMdd");
        List<Record> records = getOdpsRecords(ds, pageNum);
        if (records == null || records.size() == 0) {
            logger.warn("metric period sync job get 0 record from odps on ds={}", ds);
            return new ProcessResult(true, "there are no metirc period length records in odps");
        }

        while (records.size() > 0) {
            List<MetricDetail> tmpMetrics = new ArrayList<>();
            for (Record record : records) {
                String metricName = record.getString("metric");
                String logStore = record.getString("logstore");
                List<MetricSource> sources = sourceMap.getOrDefault(logStore, new ArrayList<>());
                for (MetricSource source: sources) {
                    if (metricName.startsWith(source.getSourceName())) {
                        int periodLength = NumberUtils.createInteger(record.getString("period_length"));
                        int score = NumberUtils.createInteger(record.getString("score"));
                        int missingRate = NumberUtils.createInteger(record.getString("missing_rate"));
                        MetricDetail metricDetail = new MetricDetail();
                        metricDetail.setMetricName(metricName);
                        metricDetail.setPeriodScore((byte) score);
                        metricDetail.setMissingRate((byte) missingRate);
                        metricDetail.setPeriodLength(periodLength);
                        metricDetail.setSourceId(source.getSourceId());
                        tmpMetrics.add(metricDetail);
                        break;
                    }
                }
            }
            if (!tmpMetrics.isEmpty()) {
                int res = metricDetailDao.batchInsertOrUpdateMetric(tmpMetrics, Column.sourceId, Column.metricName,
                    Column.periodLength, Column.periodScore, Column.missingRate);
                logger.info("metric period sync job upsert {} metric with period length", res);
            }

            if (records.size() < ODPS_PAGE_SIZE) {
                break;
            }
            pageNum++;
            records = getOdpsRecords(ds, pageNum);
        }
        return new ProcessResult(true);
    }

    private List<Record> getOdpsRecords(String ds, int pageNum) {
        // 每次返回1000条
        String sql = "SELECT    metric, period_length, logstore, score, missing_rate\n" +
                "     FROM   ecs_dw.xdragon_metric_period_length t\n" +
                "     WHERE  ds = %s and score >= %s and missing_rate <= %s and period_length > 0\n" +
                "     LIMIT %s,%s;";
        return odpsClient.runSql(String.format(sql, ds, SCORE_THRESHOLD, MISSING_RATE_THRESHOLD,
                pageNum * ODPS_PAGE_SIZE, ODPS_PAGE_SIZE));
    }

    private Map<String, List<MetricSource>> getMetricSourceMap() {
        List<MetricSource> sources = metricSourceDao.listPeriodMetricSource();
        if (sources == null) {
            return new HashMap<>();
        }
        Map<String, List<MetricSource>> ret = new HashMap<>();
        for (MetricSource source: sources) {
            if (!ret.containsKey(source.getSourceLogstore())) {
                ret.put(source.getSourceLogstore(), new ArrayList<>());
            }
            ret.get(source.getSourceLogstore()).add(source);
        }
        return ret;
    }
}
