package com.aliyun.xdragon.biz.clientops.service;

import java.util.List;
import java.util.Objects;

import com.alibaba.fastjson.JSONObject;

import com.aliyun.xdragon.api.service.clientops.ClientFollowUserService;
import com.aliyun.xdragon.biz.clientops.repository.FollowUserDao;
import com.aliyun.xdragon.common.generate.model.FollowUsers;
import com.aliyun.xdragon.common.model.clientops.request.AddOrUpdateClientFollowUserRequest;
import com.aliyun.xdragon.common.model.clientops.request.DeleteClientFollowUserRequest;
import com.aliyun.xdragon.common.model.clientops.request.QueryClientFollowUserRequest;
import com.aliyun.xdragon.common.model.clientops.response.ClientFollowUserResponse;
import com.aliyun.xdragon.common.model.XdragonMetricResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @date 2024/06/20
 */
@Service
@Validated
public class ClientFollowUserServiceImpl implements ClientFollowUserService {
    private static final Logger logger = LoggerFactory.getLogger(ClientFollowUserServiceImpl.class);

    @Autowired
    private FollowUserDao followUserDao;

    @Override
    public XdragonMetricResponse<Boolean> addFollowUser(AddOrUpdateClientFollowUserRequest request) {
        logger.info("add follow user with param: {}", JSONObject.toJSONString(request));
        if (StringUtils.isBlank(request.getAliUid()) && Objects.isNull(request.getCid())) {
            return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_BAD_REQUEST), "aliUid or cid is required");
        }
        FollowUsers followUsers =
            followUserDao.selectOneByCondition(request.getEmpId(), request.getAliUid(), request.getCid());
        if (Objects.nonNull(followUsers)) {
            logger.info("add follow user failed, data exist");
            return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_BAD_REQUEST), "data exist");
        }
        boolean addResult = followUserDao.addFollowUser(request.getEmpId(), request.getAliUid(), request.getUserName(),
            request.getCid(), request.getComment());
        logger.info("finished to add follow user, param: {}, result: {}", JSONObject.toJSONString(request), addResult);
        return XdragonMetricResponse.ofSuccess(addResult);
    }

    @Override
    public XdragonMetricResponse<ClientFollowUserResponse> queryMyFollowUserList(QueryClientFollowUserRequest request) {
        List<FollowUsers> followUsers = followUserDao.queryByEmpId(request.getEmpId());
        ClientFollowUserResponse response = new ClientFollowUserResponse();
        response.setMyFollowingList(followUsers);
        logger.info("query my follow user result: {}", JSONObject.toJSONString(response));
        return XdragonMetricResponse.ofSuccess(response);
    }

    @Override
    public XdragonMetricResponse<Boolean> updateFollowUser(AddOrUpdateClientFollowUserRequest request) {
        if (Objects.isNull(request.getId())) {
            logger.info("update with invalid param, id is required");
            return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_BAD_REQUEST), "id is required");
        }
        if (StringUtils.isBlank(request.getEmpId())
            || (StringUtils.isBlank(request.getAliUid()) && Objects.isNull(request.getCid()))) {
            return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_BAD_REQUEST),
                "empId or aliUid/cid are required");
        }
        FollowUsers followUsers = followUserDao.selectById(request.getId());
        if (Objects.isNull(followUsers)) {
            logger.info("update with invalid param, data not found");
            return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_NOT_FOUND), "data not found");
        }
        logger.info("update follow user with param: {}", JSONObject.toJSONString(request));
        boolean result = followUserDao.updateById(request.getId(), request.getEmpId(), request.getAliUid(),
            request.getUserName(), request.getCid(), request.getComment());
        return XdragonMetricResponse.ofSuccess(result);
    }

    @Override
    public XdragonMetricResponse<Boolean> deleteFollowUser(DeleteClientFollowUserRequest request) {
        FollowUsers followUsers = followUserDao.selectById(request.getId());
        if (Objects.isNull(followUsers)) {
            logger.info("update with invalid param, data not found");
            return XdragonMetricResponse.ofFail(String.valueOf(HttpStatus.SC_NOT_FOUND), "data not found");
        }
        followUserDao.deleteById(request.getId());
        return XdragonMetricResponse.ofSuccess(true);
    }

}
