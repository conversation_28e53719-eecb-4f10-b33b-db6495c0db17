package com.aliyun.xdragon.biz.metric.config;

import java.util.HashMap;

import com.aliyun.xdragon.biz.log.config.BaseMetricsConfig;
import com.aliyun.xdragon.common.enumeration.MetricTsdbLogStore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/12/09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FeatureExtractionConfig extends BaseMetricsConfig implements Cloneable{
    private String region;
    private String user;
    private String project;
    private MetricTsdbLogStore metricTsdbLogStore;
    private int range;
    private Long sourceId;
    private Integer metricNameIdx;
    private Integer metricSetNameIdx;
    private HashMap <String, Double> volatilityParams = new HashMap<>();

    @Override
    public FeatureExtractionConfig clone() {
        try {
            return (FeatureExtractionConfig)super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}