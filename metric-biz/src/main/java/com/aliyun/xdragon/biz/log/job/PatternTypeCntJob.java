package com.aliyun.xdragon.biz.log.job;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliyun.xdragon.biz.log.model.JobErrorCode;
import com.aliyun.xdragon.biz.log.processor.PatternTypeCntProcessor;
import com.aliyun.xdragon.service.common.job.AbstractProcessTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/10/12
 */
@Component
@Lazy
public class PatternTypeCntJob extends AbstractProcessTask {
    @Autowired
    private PatternTypeCntProcessor processor;

    @Override
    public ProcessResult process(JobContext context, int dataTs, int schedTs) throws Exception {
        JobErrorCode code = processor.run(dataTs * 1000L);
        return new ProcessResult(JobErrorCode.Success.equals(code));
    }
}
