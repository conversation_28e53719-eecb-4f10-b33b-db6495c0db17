package com.aliyun.xdragon.biz.workitem.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.xdragon.common.annotation.ServerLog;
import com.aliyun.xdragon.common.enumeration.SvrResultStatus;
import com.aliyun.xdragon.common.util.ServerLogContextUtil;
import com.aliyun.xdragon.biz.workitem.service.EasService;
import com.aliyun.openservices.eas.predict.http.HttpConfig;
import com.aliyun.openservices.eas.predict.http.PredictClient;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.*;

/**
 * <AUTHOR>
 */
@Data
public class EasPredictServiceImpl implements EasService {

    private static final Logger logger = LoggerFactory.getLogger(EasPredictServiceImpl.class);

    private static final int TIME_OUT = 180000;

    private String token;

    private String endPoint;

    private String urlType;

    private String modelName;

    private PredictClient client;

    public EasPredictServiceImpl(String token, String endPoint, String modelName, String urlType) {
        this.token = token;
        this.endPoint = endPoint;
        this.modelName = modelName;
        this.urlType = urlType;
        HttpConfig config = new HttpConfig();
        // 设置超时时间，单位是毫秒
        config.setConnectTimeout(TIME_OUT);
        config.setReadTimeout(TIME_OUT);
        config.setRequestTimeout(TIME_OUT);

        client = new PredictClient(config);
        client.setToken(this.token);
        if ("endpoint".equalsIgnoreCase(urlType)) {
            client.setEndpoint(this.endPoint);
        } else if ("vipserver".equalsIgnoreCase(urlType)) {
            client.setVIPServer(this.endPoint);
        } else if ("direct".equalsIgnoreCase(urlType)){
            client.setDirectEndpoint(this.endPoint);
        } else {
            client.setEndpoint(this.endPoint);
        }
        client.setModelName(this.modelName);
    }

    @Override
    @ServerLog(serverName = "eas", api = "predict")
    public <T> List<String> predict(List<T> corpus) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("corpus", corpus);
        String openBracket = "[";
        String closeBracket = "]";
        String response = client.predict(jsonObject.toJSONString());
        List<String> ret;
        if (response.startsWith(openBracket) && response.endsWith(closeBracket)){
            ret = JSONArray.parseArray(response, String.class);
        }else {
            ret = Collections.singletonList(response);
        }
        return ret;
    }

    @Override
    @ServerLog(serverName = "eas", api = "predict")
    public <T> List<String> predict(List<T> corpus, Map<String, String> params) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("corpus", corpus);
        for(String key: params.keySet()) {
            jsonObject.put(key, params.get(key));
        }
        String openBracket = "[";
        String closeBracket = "]";
        String response = client.predict(jsonObject.toJSONString());
        List<String> ret;
        if (response.startsWith(openBracket) && response.endsWith(closeBracket)){
            ret = JSONArray.parseArray(response, String.class);
        }else {
            ret = Collections.singletonList(response);
        }
        return ret;
    }

    public void shutdown() {
        if (Objects.isNull(client)) {
            return;
        }
        this.client.shutdown();
    }
}
