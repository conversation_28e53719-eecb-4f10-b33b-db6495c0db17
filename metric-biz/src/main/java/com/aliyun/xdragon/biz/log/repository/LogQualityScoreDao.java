package com.aliyun.xdragon.biz.log.repository;

import com.aliyun.xdragon.common.generate.model.LogQualityScore;
import com.aliyun.xdragon.common.generate.model.LogQualityScoreExample;
import com.aliyun.xdragon.common.generate.model.map.LogQualityScoreCustomMapper;
import com.aliyun.xdragon.common.generate.model.map.LogQualityScoreMapper;
import com.aliyun.xdragon.common.model.log.LogScoreSummary;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class LogQualityScoreDao {
    @Autowired
    private LogQualityScoreMapper mapper;

    @Autowired
    private LogQualityScoreCustomMapper customMapper;

    public static double calAppHealthScore(int newlyCount, double anomalyWeightCount) {
        return 100 - Math.min(0.1 * newlyCount, 30) - Math.min(0.1 * anomalyWeightCount, 70);
    }

    public void addScoreRecord(LogQualityScore logQualityScore) {
        mapper.insertSelective(logQualityScore);
    }

    public void updateScoreRecord(LogQualityScore logQualityScore) {
        mapper.updateByPrimaryKeySelective(logQualityScore);
    }

    public LogQualityScore getScoreRecordsByTaskIdAndTime(Long taskId, Date start, Date end) {
        LogQualityScoreExample e = new LogQualityScoreExample();
        LogQualityScoreExample.Criteria criteria = e.createCriteria();
        criteria.andTaskIdEqualTo(taskId);
        criteria.andStartTimeEqualTo(start);
        criteria.andEndTimeEqualTo(end);
        return mapper.selectOneByExample(e);
    }

    public Long insertScoreAndOverwrite(Long taskId, Date start, Date end, Integer patternCount, Double patternCountRatio, Integer newlyCount, Double anomalyWeightedCount, Double score) {
        LogQualityScore logQualityScore = new LogQualityScore();
        logQualityScore.setTaskId(taskId);
        logQualityScore.setStartTime(start);
        logQualityScore.setEndTime(end);
        logQualityScore.setPatternCount(patternCount);
        logQualityScore.setPatternRate(patternCountRatio);
        logQualityScore.setNewlyCount(newlyCount);
        logQualityScore.setAnomalyCount(anomalyWeightedCount);
        logQualityScore.setAnomalyScore(0.0);
        logQualityScore.setAppHealthScore(score);

        // Check Nan
        if (Double.isNaN(patternCountRatio)) {
            logQualityScore.setPatternRate(1.0);
        }

        LogQualityScore existingScore = this.getScoreRecordsByTaskIdAndTime(taskId, start, end);
        if (existingScore != null) {
            logQualityScore.setId(existingScore.getId());
            mapper.updateByPrimaryKeySelective(logQualityScore);
            return existingScore.getId();
        } else {
            mapper.insertSelective(logQualityScore);
            return logQualityScore.getId();
        }
    }

    public Double getAvgScore(Long taskId, Date startDate, Date endDate) {
        List<Double> ss = customMapper.getAvgScore(taskId, startDate, endDate);
        if (ss.isEmpty()) {
            return null;
        } else {
            return ss.get(0);
        }
    }

    public List<LogQualityScore> listAllRecords() {
        LogQualityScoreExample e = new LogQualityScoreExample();
        e.setOrderByClause("start_time asc");
        return mapper.selectByExample(e);
    }



    public List<LogQualityScore> getScoreRecordsByTaskIdAndTimeRange(Long taskId, Date start, Date end) {
        LogQualityScoreExample e = new LogQualityScoreExample();
        LogQualityScoreExample.Criteria criteria = e.createCriteria();
        e.setOrderByClause("start_time asc");
        criteria.andTaskIdEqualTo(taskId);
        criteria.andStartTimeGreaterThanOrEqualTo(start);
        criteria.andStartTimeLessThanOrEqualTo(end);
        return mapper.selectByExample(e);
    }

    public List<LogScoreSummary> getTaskSummaryScore(List<Long> taskIds, Date start, Date end) {
        return customMapper.getTaskSummaryScore(taskIds, start, end);
    }
}
