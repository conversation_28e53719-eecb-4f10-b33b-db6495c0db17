# OpsRecord

## 简介

该工具可以查询指定机器（实例或NC）在指定时间段内的运维规则命中记录。输入是机器（具体为实例ID或NC IP）和需要查询的时间段，输出所有命中的运维规则，以及对应的命中时间和运维动作。

## 输入参数

+ machineId：String类型，被查询的机器。如果被查询的是一个VM，则是它的实例ID；如果被查询的是一个NC，则是它的NC IP。
+ startTime：String类型，查询开始时间，格式为yyyy-MM-dd HH:mm:ss。
+ endTime：String类型，查询结束时间，格式为yyyy-MM-dd HH:mm:ss。

## 入参示例

```json
{
  "machineId": "***********",
  "startTime": "2023-02-28 00:00:00",
  "endTime": "2023-02-28 23:59:59"
}
```

## 输出结果

输出结果是一个列表，列表中的每个元素是命中的一个运维规则记录。元素的各字段的含义如下：

- `ruleName`: 规则名称。
- `targetId`: 规则命中的对象的ID。
- `description`: 对规则含义的简短描述。
- `reason`: 触发该规则的原因。
- `actions`: 规则对应的运维动作。
- `firstMatchedDate`: 规则首次命中的时间。
- `lastMatchedDate`: 规则最后命中的时间。
- `matchedTimes`: 规则命中次数。
- `status`: 规则命中状态，各状态的含义如下：
  - `Pass`: 已发起工作流执行运维动作
  - `Delay`: 事件延迟运维，等待重试
  - `Concurrent`: 并发提交运维被取消
  - `Silent`: 运维静默，取消运维
  - `Conflict`: 运维被互斥
  - `Black`: 运维黑名单
  - `Limit`: 被运维限流
  - `Ignore`: prepareHandler等步骤校验不通过
  - `DryRun`: 试运行
  - `ExceededMaxSchedules`: 超过最大工作流个数被取消
  - `ExceedMaxPlanEvents`: 超过最大event个数被取消
  - `Error`: 处理异常
