# PerformanceDiagnose

## 简介

该工具可以对指定实例在指定时间段内的性能争抢问题进行诊断。输入单个实例ID和需要查询的时间段，输出该实例在时间段内的存在的争抢问题和对应时间点，以及争抢的施害者。

## 输入参数

+ instanceId: String类型。实例ID
+ startTs: String类型。开始时间，格式为yyyy-MM-dd HH:mm:ss
+ endTs: String类型。结束时间，格式为yyyy-MM-dd HH:mm:ss

## 入参示例

```json
{
  "instanceId": "i-0xigrnh25fde5f67nzzq",
  "startTs": "2025-03-07 12:00:00",
  "endTs": "2025-03-07 13:00:00"
}
```

## 输出结果

各字段的含义如下：
+ problems: Map类型，表示存在的争抢问题以及对应的时间段。
+ solutions: 争抢问题的解决方案


