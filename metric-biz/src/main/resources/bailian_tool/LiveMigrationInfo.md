# LiveMigrationInfo

## 简介

该工具用于查询VM在给定时间段内的热迁移记录信息。输入VM和需要查询的时间段，输出迁移状态、迁移时间、源NC、目的NC和迁移原因等信息。

## 输入参数

+ `vm`: String类型，被查询的实例ID。
+ `startTime`: String类型，开始时间，格式为yyyy-MM-dd HH:mm:ss。
+ `endTime`: String类型，结束时间，格式为yyyy-MM-dd HH:mm:ss。

## 入参示例

```json
{
  "vm": "i-uf69jls3ksnsk9syrb9l",
  "startTime": "2024-01-02 10:00:00",
  "endTime": "2024-01-03 18:00:00"
}
```

## 输出结果

工具输出是一个列表，列表中的每一个元素都是一次热迁移的记录。每个记录包含以下字段：
+ `instanceId`: 实例ID。
+ `startTs`: 迁移开始的时间戳。
+ `endTs`: 迁移结束的时间戳。
+ `oldNcIp`: 源NC的IP地址。
+ `newNcIp`: 目标NC的IP地址。
+ `status`: 热迁移的状态，例如成功或失败等。
+ `retCode`: 热迁移的结果码。
+ `reason`: 热迁移的原因。