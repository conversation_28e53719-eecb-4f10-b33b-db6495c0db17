# HealthStatus

## 简介
该工具用于查询指定实例的健康状态及其变化历史。输入单个实例ID，输出该实例当前的健康状态或其变化历史。

## 输入参数

+ vm: String类型，被查询的实例ID
+ showHistroy：boolean类型。true表示查询健康状态变化历史，false表示只查询当前健康状态

## 入参示例

```json
{
  "vm": "i-uf6ai3lekl5xpj1fw44j",
  "showHistroy": true
}
```

## 输出结果

输出结果是一个列表，列表中的每个元素是实例的一次健康状态变化。当showHistroy为true时，列表只包含一个元素。列表元素的各字段的含义如下：
+ time：健康状态发生变化的时间
+ newStatus：健康状态变化后的新状态
+ oldStatus：健康状态变化前的旧状态

健康状态的取值如下：
+ 健康：实例当前运行正常
+ 受损：实例当前运行不正常
+ 不可用：实例当前已停止或已释放
+ 数据不足：当前缺少数据判断实例健康状态
+ 启动中：实例正在启动
+ 未知：查询过程中存在问题
