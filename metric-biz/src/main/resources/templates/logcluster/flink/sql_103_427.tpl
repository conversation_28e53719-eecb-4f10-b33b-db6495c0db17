--SQL
--********************************************************************--
--Author: 日志分析代码生成器
--CreateTime: ${time}
--Comment: ${description}
--********************************************************************--

-- SLS输入表
CREATE TEMPORARY TABLE sls_input_table (
${raw},
${filter_fields}  `__timestamp__` BIGINT METADATA VIRTUAL,
  `__source__` STRING METADATA VIRTUAL,
${ext_fields}  ts as TO_TIMESTAMP(`__timestamp__` * 1000),
  WATERMARK wk FOR ts as withOffset (ts, ${offset})
) with (
  'connector' = 'sls',
  'accessId' = '${r'${sls.input.accessId}'}',
  'accessKey' = '${r'${sls.input.accessKey}'}',
  'endPoint' = '${r'${sls.input.endPoint}'}',
  'project' = '${r'${sls.input.project}'}',
  'logStore' = '${r'${sls.input.logStore}'}',
  'consumerGroup' = '${r'${sls.input.consumerGroup}'}',
  'batchGetSize' = '500',
  'startTime' = '2022-03-21 00:00:00'${filter_usage}
);

<#if filterStatus>
-- IP过滤表
CREATE TEMPORARY TABLE filter_table (
  `nc_ip` STRING,
  PRIMARY KEY(nc_ip) NOT ENFORCED
) with (
  'connector' = 'odps',
  'endpoint' = '${r'${filter.endpoint}'}',
  'project' = '${r'${filter.project}'}',
  'tableName' = '${r'${filter.tableName}'}',
  'accessId' = '${r'${filter.accessId}'}',
  'accessKey' = '${r'${filter.accessKey}'}',
  'partition' = 'max_pt_with_done()',
  'cache' = 'ALL',
  'cacheTTLMs' = '1800000'
);
</#if>

<#if filterGamma>
-- Gamma过滤表
CREATE TEMPORARY TABLE gamma_table (
  `nc_ip` STRING,
  PRIMARY KEY(nc_ip) NOT ENFORCED
) with (
  'connector' = 'rds',
  'url' = '${r'${gamma.url}'}',
  'tableName' = '${r'${gamma.tableName}'}',
  'userName' = '${r'${gamma.userName}'}',
  'password' = '${r'${gamma.password}'}',
  'cacheTTLMs' = '3600000'
);
</#if>

-- 剩余日志输出表
CREATE TEMPORARY TABLE remain_table (
  `task_id` BIGINT,
  `region` VARCHAR,
  `logstore` VARCHAR,
  `log` VARCHAR,
  `md5` VARCHAR,
  `exts` VARCHAR,
  `time` TIMESTAMP
) WITH (
  'connector' = 'sls',
  'accessId' = '${r'${sls.output.accessId}'}',
  'accessKey' = '${r'${sls.output.accessKey}'}',
  'endPoint' = '${r'${remain.endPoint}'}',
  'project' = '${r'${remain.project}'}',
  'logStore' = '${r'${remain.logStore}'}'
);

-- 模式结果输出表
CREATE TEMPORARY TABLE pattern_table (
  `task_id` BIGINT,
  `region` VARCHAR,
  `logstore` VARCHAR,
  `time` TIMESTAMP,
  `pattern` VARCHAR,
  `hashcode` BIGINT,
  `support` BIGINT,
  `window_size` BIGINT,
  `md5` VARCHAR,
  `nc_cnt` INT
) with (
  'connector' = 'adb3.0',
  'url' = '**********************************************************************************',
  'tableName' = '${r'${adb.sinkTableName}'}',
  'userName' = '${r'${adb.userName}'}',
  'password' = '${r'${adb.password}'}'
);

-- 模式详情信息输出表
CREATE TEMPORARY TABLE distribution_table (
  `task_id` BIGINT,
  `region` VARCHAR,
  `logstore` VARCHAR,
  `time` TIMESTAMP,
  `hashcode` BIGINT,
  `support` BIGINT,
  `type_id` BIGINT,
  `detail_key` VARCHAR,
  `detail_support` BIGINT,
  `md5` VARCHAR
) with (
  'connector' = 'adb3.0',
  'url' = '**********************************************************************************',
  'tableName' = '${r'${adb.detailTableName}'}',
  'userName' = '${r'${adb.userName}'}',
  'password' = '${r'${adb.password}'}'
);

<#if sinkToSls>
-- 异常检测sls输出表
CREATE TEMPORARY TABLE sls_table (
  `metric` VARCHAR,
  `timestamp` BIGINT,
  `value` BIGINT,
  `task_id` BIGINT,
  `region` VARCHAR,
  `logstore` VARCHAR,
  `time` TIMESTAMP,
  `pattern` VARCHAR,
  `hashcode` BIGINT,
  `support` BIGINT,
  `md5` VARCHAR,
  `sourceId` BIGINT
) with (
  'connector' = 'sls',
  'accessId' = '${r'${sls.output.accessId}'}',
  'accessKey' = '${r'${sls.output.accessKey}'}',
  'endPoint' = '${r'${sls.output.endPoint}'}',
  'project' = '${r'${sls.output.project}'}',
  'logStore' = '${r'${sls.output.logStore}'}'
);
</#if>

-- 预处理，过滤不分析的日志和无意义的日志
CREATE TEMPORARY VIEW pretreat_view AS
SELECT
  ${raw_usage} AS raw_log,
${filter_trans}${ext_trans}  ${join_prefix}ts AS ts
FROM
  sls_input_table${join_alias}
  <#if filterStatus>
    LEFT JOIN
      filter_table FOR SYSTEM_TIME AS OF PROCTIME () AS f ON t.__source__ = f.nc_ip
  </#if>
  <#if filterGamma>
    LEFT JOIN
      gamma_table FOR SYSTEM_TIME AS OF PROCTIME () AS g ON t.__source__ = g.nc_ip
  </#if>
  WHERE
    t.`content` IS NOT NULL
    AND t.`content` <> ''
  <#if filterStatus>
    AND f.nc_ip IS NULL
  </#if>
  <#if filterGamma>
    AND g.nc_ip IS NULL
  </#if>
    AND t.`content` NOT LIKE '%DBG%'
    AND t.`content` NOT LIKE '%INFO%'
    AND t.`content` NOT LIKE '%[LOG]%';

-- 匹配历史模式
CREATE TEMPORARY VIEW match_view AS
SELECT
  `raw_log`,
  `raw_md5`,
  `pattern`,
  `md5`,
${ext_list}  `ts`
FROM
  pretreat_view,
  LATERAL table (matchPattern(raw_log)) AS T(`raw_md5`, `pattern`, `md5`);

-- 一次匹配上的日志
CREATE TEMPORARY VIEW retain_view AS
SELECT
  `raw_md5`,
  `pattern`,
  `md5`,
${ext_list}  `ts`
FROM
  match_view
WHERE
  `pattern` IS NOT NULL AND `md5` IS NOT NULL;

-- 一次匹配上的日志结果
CREATE TEMPORARY VIEW pattern_view AS
SELECT
  TUMBLE_START(ts, INTERVAL '${window?c}' MINUTE) AS window_start,
  TUMBLE_END(ts, INTERVAL '${window?c}' MINUTE) AS window_end,
  `md5`,
  `pattern`,
  COUNT(*) AS `support`,
  COUNT(DISTINCT `ext1`) AS `nc_cnt`
FROM
  retain_view
GROUP BY `md5`, `pattern`, TUMBLE(ts, INTERVAL '${window?c}' MINUTE);

-- 一次匹配上的日志详情信息
<#list distribution_view as dv>
CREATE TEMPORARY VIEW distribution${dv.id}_view AS
SELECT
  TUMBLE_START(ts, INTERVAL '${dv.window?c}' MINUTE) AS window_start,
  TUMBLE_END(ts, INTERVAL '${dv.window?c}' MINUTE) AS window_end,
  ${dv.index} AS `type_id`,
  `md5`,
  `pattern`,
  `ext${dv.id}`,
  COUNT(*) AS `support`
FROM
  retain_view
GROUP BY `md5`, `pattern`, ext${dv.id}, TUMBLE(ts, INTERVAL '${dv.window?c}' MINUTE);
</#list>

-- 未匹配上的日志
CREATE TEMPORARY VIEW miss_view AS
SELECT
  `raw_log`,
${ext_list}  `ts`
FROM
  match_view
WHERE
  `pattern` IS NULL OR `md5` IS NULL;

-- 对未匹配上的日志进行预合并
CREATE TEMPORARY VIEW trans_view AS
SELECT
  `tokens`,
  `exts`,
  `md5`,
  `raw_id`,
  ts
FROM
  miss_view,
  LATERAL table (logPretreat(`raw_log`${ext_usage})) AS T(`tokens`, `exts`, `md5`, `raw_id`);

-- 对未匹配上的日志进行聚类
CREATE TEMPORARY VIEW cluster_view AS
SELECT
  `window_start`,
  `window_end`,
  `window_size`,
  logCluster(`tokens`, `extMap`, `md5`, `cnt`) as clusterDict
FROM (
  SELECT
    TUMBLE_START(ts, INTERVAL '${window?c}' MINUTE) AS window_start,
    TUMBLE_END(ts, INTERVAL '${window?c}' MINUTE) AS window_end,
    `md5`,
    FIRST_VALUE(tokens) AS tokens,
    CAST(COUNT(exts) AS INT) AS cnt,
    pretreatCluster(exts) AS extMap,
    CAST(${window?c} AS BIGINT) AS window_size
  FROM
    trans_view
  GROUP BY `md5`, TUMBLE(ts, INTERVAL '${window?c}' MINUTE)
)
GROUP BY window_start, window_end, window_size;

-- 获取二次匹配日志模式详情
CREATE TEMPORARY VIEW cluster_flat_view AS
SELECT
  `nc_cnt`,
  `key`,
  `support`,
  `extStr`,
  `pattern`,
  `md5`,
  `window_start`,
  `window_size`
FROM
  cluster_view,
  LATERAL table (clusterFlat(clusterDict)) as T(`nc_cnt`, `key`, `support`, `extStr`, `pattern`, `md5`, `id_list`);

-- 获取二次匹配日志模式模板
CREATE TEMPORARY VIEW clustered_patterns_view AS
SELECT
  `window_start`,
  `window_end`,
  `patterns`
FROM
  cluster_view,
  LATERAL table (getPatterns(clusterDict)) as T(`patterns`);

-- 未匹配上的日志和聚类结果进行合并
CREATE TEMPORARY VIEW prepare_view AS
SELECT  /*+ JOIN_STATE_TTL('m' = '30min', 'c' = '30min') */
  m.`raw_log` as `raw_log`,
${ext_list_ext}  c.`patterns` as `patterns`,
  c.`window_start` as `window_start`,
  c.`window_end` as `window_end`,
  m.`ts` as `ts`
FROM
  miss_view m, clustered_patterns_view c
WHERE
  m.ts BETWEEN c.window_start AND c.window_end;

-- 根据聚类结果进行二次匹配
CREATE TEMPORARY VIEW rematch_view AS
SELECT
  `raw_log`,
  `raw_md5`,
  `pattern`,
  `md5`,
${ext_list}  `ts`
FROM
  prepare_view,
  LATERAL table (localMatch(`raw_log`, `patterns`)) AS T(`raw_md5`, `pattern`, `md5`);

-- 二次匹配仍未命中的日志作为剩余日志
CREATE TEMPORARY VIEW remain_view AS
SELECT
  `raw_log`,
  `raw_md5`,
  CONCAT_WS('|'${ext_usage}) AS `exts`,
  CAST(`ts` AS STRING) as `ts_str`
FROM
  rematch_view
WHERE
  `pattern` IS NULL OR `md5` IS NULL;


-- sink到输出表
BEGIN STATEMENT SET;

INSERT INTO remain_table
SELECT
  `task_id`,
  `region`,
  `logstore`,
  `raw_log`,
  `raw_md5`,
  `exts`,
  TO_TIMESTAMP(`ts_str`)
FROM
  remain_view,
  LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`);

INSERT INTO pattern_table
SELECT
  `task_id`,
  `region`,
  `logstore`,
  `window_start`,
  `pattern`,
  0,
  `support`,
  CAST(${window?c} AS BIGINT),
  `md5`,
  CAST(`nc_cnt` AS INT)
FROM
  pattern_view,
  LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`);

INSERT INTO pattern_table
SELECT
  `task_id`,
  `region`,
  `logstore`,
  `window_start`,
  `pattern`,
  1,
  `support`,
  `window_size`,
  `md5`,
  CAST(`nc_cnt` AS INT)
FROM
  cluster_flat_view,
  LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`);

<#list distribution_insert as dt>
INSERT INTO distribution_table
SELECT
  `task_id`,
  `region`,
  `logstore`,
  window_start,
  0,
  0,
  `type_id`,
  COALESCE(`ext${dt.id}`, 'null'),
  `support`,
  `md5`
FROM
  distribution${dt.id}_view,
  LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`);

</#list>
INSERT INTO distribution_table
SELECT
  `task_id`,
  `region`,
  `logstore`,
  `window_start`,
  1,
  `support`,
  `extId`,
  `ext`,
  `extSupport`,
  `md5`
FROM
  cluster_flat_view,
  LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`),
  LATERAL table (distributionFlat(`extStr`)) AS T(`extId`, `ext`, `extSupport`);

<#if sinkToSls>
INSERT INTO sls_table
SELECT
  CONCAT_WS('/', 'logcluster', `task_id`, `md5`, `region`, `logstore`),
  UNIX_TIMESTAMP(`window_start`),
  `support`,
  `task_id`,
  `region`,
  `logstore`,
  `window_start`,
  `pattern`,
  0,
  `support`,
  `md5`,
  ${pattern_source_id}
FROM
  pattern_view,
  LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`);

INSERT INTO sls_table
SELECT
  CONCAT_WS('/', 'logcluster', `task_id`, `md5`, `region`, `logstore`),
  UNIX_TIMESTAMP(`window_start`),
  `support`,
  `task_id`,
  `region`,
  `logstore`,
  `window_start`,
  `pattern`,
  1,
  `support`,
  `md5`,
  ${pattern_source_id}
FROM
  cluster_flat_view,
  LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`);

INSERT INTO sls_table
SELECT
  CONCAT_WS('/', 'logclusternccnt', `task_id`, `md5`, `region`, `logstore`),
  UNIX_TIMESTAMP(`window_start`),
  `nc_cnt`,
  `task_id`,
  `region`,
  `logstore`,
  `window_start`,
  `pattern`,
  0,
  `nc_cnt`,
  `md5`,
  ${count_source_id}
FROM
  pattern_view,
  LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`);

INSERT INTO sls_table
SELECT
  CONCAT_WS('/', 'logclusternccnt', `task_id`, `md5`, `region`, `logstore`),
  UNIX_TIMESTAMP(`window_start`),
  `nc_cnt`,
  `task_id`,
  `region`,
  `logstore`,
  `window_start`,
  `pattern`,
  1,
  `nc_cnt`,
  `md5`,
  ${count_source_id}
FROM
  cluster_flat_view,
  LATERAL table (infoCollect()) AS T0(`task_id`, `region`, `logstore`);
</#if>

END;
