-- log union task, generate by xdragon-metric app, do not modify!!!

-- SLS输入表
CREATE TEMPORARY TABLE sls_input_table (
${raw},
${filter_fields}
  `__source__` STRING METADATA VIRTUAL,
${ext_fields}
`__timestamp__` BIGINT METADATA VIRTUAL
) with (
  'connector' = 'sls',
  'accessId' = '${r'${sls.input.accessId}'}',
  'accessKey' = '${r'${sls.input.accessKey}'}',
  'endPoint' = '${r'${sls.input.endPoint}'}',
  'project' = '${r'${sls.input.project}'}',
  'logStore' = '${r'${sls.input.logStore}'}',
  'batchGetSize' = '${batch_get_size}',
  'startTime' = '2022-03-21 00:00:00',
  'query' = '* | WHERE level IN (''ERROR'') AND __topic__ IN (''prod'', ''aso_prod'') AND content NOT LIKE ''%YUEJIA-DEBEG%'''
);

<#if filterStatus>
-- IP过滤表
CREATE TEMPORARY TABLE filter_table (
  `nc_ip` STRING,
  PRIMARY KEY(nc_ip) NOT ENFORCED
) with (
  'connector' = 'odps',
  'endpoint' = '${r'${filter.endpoint}'}',
  'project' = '${r'${filter.project}'}',
  'tableName' = '${r'${filter.tableName}'}',
  'accessId' = '${r'${filter.accessId}'}',
  'accessKey' = '${r'${filter.accessKey}'}',
  'partition' = 'max_pt_with_done()',
  'cache' = 'ALL',
  'cacheTTLMs' = '1800000'
);
</#if>

<#if filterGamma>
-- Gamma过滤表
CREATE TEMPORARY TABLE gamma_table (
  `nc_ip` STRING,
  PRIMARY KEY(nc_ip) NOT ENFORCED
) with (
  'connector' = 'rds',
  'url' = '${r'${gamma.url}'}',
  'tableName' = '${r'${gamma.tableName}'}',
  'userName' = '${r'${gamma.userName}'}',
  'password' = '${r'${gamma.password}'}',
  'cacheTTLMs' = '3600000'
);
</#if>

--SLS sink表
CREATE TEMPORARY TABLE log_union
(
    `content`    STRING,
    `timestamp` BIGINT,
    `nc`        STRING,
    log_id      BIGINT,
    exts        STRING,
    sls_source  STRING,
    ts          INT
)
with (
    'connector' = 'sls',
    'accessId' = '${r'${union.sls.accessId}'}',
    'accessKey' = '${r'${union.sls.accessKey}'}',
    'endPoint' = '${r'${union.sls.endPoint}'}',
    'project' = '${r'${union.sls.project}'}',
    'logStore' = '${r'${union.sls.logStore}'}',
    'timeField' = 'ts'
)
;

-- biz sql
CREATE TEMPORARY VIEW pretreat_view AS
SELECT ${filter_hint}
  ${raw_usage} AS raw_log,
${filter_trans}${ext_trans}  ${join_prefix}__timestamp__ AS ts
FROM
  sls_input_table${join_alias}
  <#if filterStatus>
    LEFT JOIN
      filter_table FOR SYSTEM_TIME AS OF PROCTIME () AS f ON t.__source__ = f.nc_ip
  </#if>
  <#if filterGamma>
    LEFT JOIN
      gamma_table FOR SYSTEM_TIME AS OF PROCTIME () AS g ON t.__source__ = g.nc_ip
  </#if>
  <#if filter_content || filterStatus || filterGamma>
  WHERE
  </#if>
  <#if filter_content>
    ${raw_filter}
  </#if>
  <#if filterStatus>
    <#if filter_content>
    AND
    </#if>
    f.nc_ip IS NULL
  </#if>
  <#if filterGamma>
    <#if filter_content || filterStatus>
     AND
    </#if>
     g.nc_ip IS NULL
  </#if>
  ;

INSERT INTO log_union
SELECT
    raw_log,
    ts,
    ext1,
    MURMUR_HASH_64(raw_log),
    CONCAT_WS('|'${ext_usage}),
    '${source_region}',
    CAST(ts as INT)
FROM pretreat_view
;