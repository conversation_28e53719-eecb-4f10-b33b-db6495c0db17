<strong>请求实例信息如下</strong>
<#if machine_max_cnt??>
<strong>(单次请求最多支持${machine_max_cnt}个实例，数量超出限制请分批请求)</strong>
</#if>
</br>
<#list infos as info>
<#if info.instanceId??>
<font color=#999 size=2px>&nbsp;&nbsp;&nbsp;实例ID:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.instanceId}</font></br>
</#if>
<#if info.status?? && info.ecsBusinessStatus?? && info.expectStatus??>
<font color=#999 size=2px>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;状态:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.status}(${info.ecsBusinessStatus})(预期：${info.expectStatus})</font></br>
</#if>
<#if info.status?? && info.ecsBusinessStatus?? && !info.expectStatus??>
<font color=#999 size=2px>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;状态:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.status}(${info.ecsBusinessStatus})</font></br>
</#if>
<#if info.status?? && !info.ecsBusinessStatus??>
<font color=#999 size=2px>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;状态:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.status}</font></br>
</#if>
<#if info.cores??>
<font color=#999 size=2px>实例核数:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.cores}</font></br>
</#if>
<#if info.gmtCreated??>
<font color=#999 size=2px>创建时间:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.gmtCreated}</font></br>
</#if>
<#if info.gmtModified??>
<font color=#999 size=2px>修改时间:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.gmtModified}</font></br>
</#if>
<#if info.gmtStarted??>
<font color=#999 size=2px>启动时间:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.gmtStarted}</font></br>
</#if>
<#if info.gmtSync??>
<font color=#999 size=2px>同步时间:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.gmtSync}</font></br>
</#if>
<#if info.ncId??>
<font color=#999 size=2px>&nbsp;&nbsp;&nbsp;机器ID:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.ncId}</font></br>
</#if>
<#if info.ncIp??>
<font color=#999 size=2px>&nbsp;&nbsp;所在NC:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.ncIp}</font></br>
</#if>
<#if info.isLocalDisk??>
<font color=#999 size=2px>磁盘类型:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.isLocalDisk}</font></br>
</#if>
<#if info.bid??>
<font color=#999 size=2px>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bid:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.bid}</font></br>
</#if>
<#if info.aliUid??>
<font color=#999 size=2px>&nbsp;&nbsp;&nbsp;&nbsp;aliUid:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.aliUid}</font></br>
</#if>
<#if info.instanceType?? && info.instanceTypeFamily??>
<font color=#999 size=2px>实例类型:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.instanceType}(${info.instanceTypeFamily})</font></br>
</#if>
<#if info.instanceType?? && !info.instanceTypeFamily??>
<font color=#999 size=2px>实例类型:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.instanceType}</font></br>
</#if>
<#if info.osType?? && info.osVersion?? && info.isWin??>
<font color=#999 size=2px>操作系统:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.osType}(${info.osVersion})(${info.isWin})</font></br>
</#if>
<#if info.imageName??>
<font color=#999 size=2px>镜像名称:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.imageName}</font></br>
</#if>
<#if info.netWorkType??>
<font color=#999 size=2px>网络类型:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.netWorkType}</font></br>
</#if>
<#if info.eip??>
<font color=#999 size=2px>&nbsp;&nbsp;&nbsp;弹性IP:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.eip}</font></br>
</#if>
<#if info.eipBandwidth??>
<font color=#999 size=2px>弹性IP带宽:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.eipBandwidth}</font></br>
</#if>
<#if info.intranetIp??>
<font color=#999 size=2px>intranetIP:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.intranetIp}</font></br>
</#if>
<#if info.privateIpAddress??>
<font color=#999 size=2px>privateIP:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.privateIpAddress}</font></br>
</#if>
<#if info.publicIpAddress??>
<font color=#999 size=2px>publicIP:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.publicIpAddress}</font></br>
</#if>
<#if info.azone??>
<font color=#999 size=2px>&nbsp;&nbsp;&nbsp;&nbsp;azone:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.azone}</font></br>
</#if>
<#if info.regionNo?? && info.regionNoAlias??>
<font color=#999 size=2px>&nbsp;&nbsp;&nbsp;region:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${info.regionNoAlias}(${info.regionNo})</font></br>
</#if>
</br>
</#list>

<#if summary??>
---
<font color=#999 size=2px>总结:</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${summary}</font>
</#if>
