### 您有一条ECS工单需要处理:
#### 工单总结(<a href="${aoneUrl}" target = "_blank">Aone</a>)
<#if answer??>
> ${answer}
</#if>
<#if machineId??>
---
#### 实例信息
<#if vmInfos?? && (vmInfos?size > 0)>
<#list vmInfos as vmInfo>
<#if vmInfo.instanceId??>
>**实例ID:** ${vmInfo.instanceId}</br>
</#if>
<#if vmInfo.ncId??>
**NcId:** ${vmInfo.ncId}</br>
</#if>
<#if vmInfo.ncIp??>
**NcIp:** ${vmInfo.ncIp}</br>
</#if>
<#if vmInfo.status??>
**实例状态:** ${vmInfo.status}</br>
</#if>
<#if vmInfo.instanceType??>
**实例规格:** ${vmInfo.instanceType}</br>
</#if>
<#if vmInfo.aliUid??>
**aliUid:** ${vmInfo.aliUid}</br>
</#if>
<#if vmInfo.azone??>
**azone:** ${vmInfo.azone}</br>
</#if>
---
</#list>
</#if>

<#if ncInfos?? && (ncInfos?size > 0)>
<#list ncInfos as ncInfo>
<#if ncInfo.ncIp??>
>**机器IP:** ${ncInfo.ncIp}**</br>
</#if>
<#if ncInfo.ncId??>
**机器ID:** ${ncInfo.ncId}</br>
</#if>
<#if ncInfo.azone??>
**可用区:** ${ncInfo.azone}</br>
</#if>
<#if ncInfo.idc??>
**idc:** ${ncInfo.idc}</br>
</#if>
---
</#list>
</#if>
</#if>

#### 关联数据
<#if diagnoseResults??>
<#list diagnoseResults as diagnoseResult>
>**实例**：${diagnoseResult.machineId}
<#if diagnoseResult.rootCause??>
>**根因诊断**：${diagnoseResult.rootCause}</br>
</#if>
</br>
<#if diagnoseResult.performanceDiagnose??>
>**性能分析**：${diagnoseResult.performanceDiagnose}
</#if>
***
</#list>
</#if>

<#if events??>
>**客户侧事件**：</br>
<#list events as event>
<#if event.code??>
>**运维码:** &emsp;
<font size=2px>${event.code}</font></br>
</#if>
<#if event.status??>
**状态:** &emsp;
<font size=2px>${event.status}</font></br>
</#if>
<#if event.reason??>
**原因:**&emsp;
<font size=2px>${event.reason}</font></br>
</#if>
<#if event.plan??>
**计划时间:**&emsp;
<font size=2px>${event.plan}</font><br>
</#if>
<#if event.end??>
**完成时间:**&emsp;
<font size=2px>${event.end}</font>
</#if>
***
</#list>
</#if>
---
<#if tools?? && (tools?size > 0)>
#### 工具推荐
><a href="https://cloudbot2.aliyun-inc.com/cloudbot/ng2/#/operations/diagnostic-aid" target = "_blank">诊断助手</a></br>
<#list tools as tool>
><a href="${tool.url}" target = "_blank">${tool.name}</a></br>
</#list>
</#if>

<#if finish==true>
---
<font sizeToken=common_footnote_text_style__font_size colorTokenV2=common_orange1_color>有问题可以直接回复查询哦[小蜜蜂]</font>
<font sizeToken=common_footnote_text_style__font_size>&emsp;&emsp;&emsp;&emsp; --- **异常调度团队**</font>
</#if>



