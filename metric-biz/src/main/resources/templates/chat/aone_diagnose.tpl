- <strong>工单总结(<a href="${aoneUrl}" target = "_blank">Aone</a>)</strong><br>
<#if answer??>
${answer}
<#else>
<#if title??>
<font color=#999 size=2px>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;标题</font>&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${title}</font></br>
</#if>
<#if machineIds??>
<font color=#999 size=2px>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;实例</font>&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${machineIds}</font></br>
</#if>
<#if exceptionTime??>
<font color=#999 size=2px>异常时间</font>&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${exceptionTime}</font></br>
</#if>
<#if description??>
<font color=#999 size=2px>问题描述</font>&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${description}</font></br>
</#if>
<#if progress??>
<font color=#999 size=2px>排查进展</font>&nbsp;&nbsp;&nbsp;&nbsp;<font size=2px>${progress}</font></br>
</#if>
</#if >

<#if source==0>
<br>
<#if machineId??>
- <strong>根因分析</strong></br>
${machineId}在${startTime}至${endTime}诊断信息：
</#if>
</#if>

<#if source==2>
<#if diagnoseResults??>
<#list diagnoseResults as diagnoseResult>
---
- <strong>根因分析(<a href="${diagnoseResult.diagnoseUrl}" target = "_blank">定界</a>)</strong></br>
${diagnoseResult.machineId} 在 ${startTime} - ${endTime}的诊断信息如下：
**底座诊断**：${diagnoseResult.rootCause}</br>
<#if diagnoseResult.performanceDiagnose??>
**性能分析**：${diagnoseResult.performanceDiagnose}
</#if>
<#if diagnoseResult.guestDiagnoseRes??>
**Guest诊断**：${diagnoseResult.guestDiagnoseRes}
</#if>
***
</#list>
</#if>
</#if>