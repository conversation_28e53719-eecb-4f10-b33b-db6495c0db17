# coding=utf-8
import json
import math
import sys

from scipy import stats
import numpy as np
import pandas as pd
from statsmodels.stats.multicomp import MultiComparison
from collections import defaultdict
from statsmodels.stats.power import FTestAnovaPower

from python.AnovaModel import welchAnova
from python.PostHocTestsModel import gamesHowell, tukeyKramer


class AnovaAnalysisService(object):

    def __init__(self, significanceLevel=0.05):
        super(AnovaAnalysisService, self).__init__()
        self.delimiter = "--"
        # 每个样本的最少数据量
        self.minimumSampleSize = 5
        # 显著性水平
        self.significanceLevel = significanceLevel
        self.fTestAnovaPower = FTestAnovaPower()
        self.meanDiffKeyword = "meandiff"
        self.rejectKeyword = "reject"

    def isDataEnough(self, actionDict):
        """
        方差分析每组的样本容量至少为3
        @type actionDict: dict[str, list]
        @rtype: bool
        """
        for _, sample in actionDict.iteritems():
            if len(sample) < self.minimumSampleSize:
                return False
        return True

    def isBalanced(self, sampleList):
        """
        判断各组样本量是否一致
        @param sampleList: 样本数据
        @type sampleList: list[array_like]
        @rtype: bool
        """
        groupSize = len(sampleList[0])
        for data in sampleList:
            if len(data) != groupSize:
                return False
        return True

    def anova(self, actionDict):
        """
        方差分析，以0.05作为显著性水平，判断是否具有显著性差异
        当符合方差齐时，使用f_oneway_anova，否则使用welch's anova
        @type actionDict: dict[str, list]
        @rtype: (float, float, bool)
        """
        data = []
        for action, keyMetricList in actionDict.iteritems():
            data.append(keyMetricList)
        _, probabilityOfLevene = stats.levene(*data)
        # The small p-value suggests that the populations do not have equal variances.
        equalVariance = bool(probabilityOfLevene > self.significanceLevel)

        if equalVariance:
            # 满足方差齐性假设，使用one way anova
            fValue, pValue = stats.f_oneway(*data)
        else:
            # 不满足方差齐性假设，使用welch's anova（相比于非参数检验Kruskal-wallis来更准确）
            fValue, pValue = welchAnova(actionDict)
        return fValue, pValue, equalVariance

    def postHocTest(self, actionDict, isVarianceEqual):
        """
        事后多重比较，只有在anova的结果显著时才调用
        @param isVarianceEqual: 各组样本是否等方差
        @type isVarianceEqual: bool
        @type actionDict: dict[str, list]
        @rtype: dict
        """
        if not isVarianceEqual:
            return gamesHowell(actionDict, significantLevel=self.significanceLevel)
        isBalance = self.isBalanced(actionDict.values())
        if not isBalance:
            return tukeyKramer(actionDict, significantLevel=self.significanceLevel)
        # 首先将数据改成DataFrame形式
        values = []
        groups = []
        for action, keyMetricList in actionDict.iteritems():
            values.extend(keyMetricList)
            groups.extend(np.repeat(action, len(keyMetricList)))

        df = pd.DataFrame({'values': values, 'groups': groups})
        result = defaultdict(dict)
        try:
            mc = MultiComparison(df['values'], df['groups'])
            table = mc.tukeyhsd()._results_table
            count = len(actionDict)

            for i in range(1, count * (count - 1) / 2 + 1):
                # mean是table[i][1].data-table[i][0].data
                key = table[i][1].data + self.delimiter + table[i][0].data
                result[key] = {self.meanDiffKeyword: table[i][2].data, self.rejectKeyword: str(table[i][6].data)}
        except Exception as e:
            print("tukey-hsd analysis error: %s" % e.message)
        return result

    def pairCompareMean(self, actionMeanDict, isSignificant):
        """
        当显著性检验效果为不显著时，调用该方法来成对比较不同运维动作key metric的平均值
        @param actionMeanDict: 运维动作的平均key metric
        @type actionMeanDict: dict[str, float]
        @type isSignificant: bool
        @rtype dict
        """
        pairCompareDict = defaultdict(dict)
        for action1, mean1 in actionMeanDict.iteritems():
            for action2, mean2 in actionMeanDict.iteritems():
                if action1 == action2:
                    continue
                actionPair1 = '{}-{}'.format(action1, action2)
                actionPair2 = '{}-{}'.format(action2, action1)
                if actionPair1 in pairCompareDict or actionPair2 in pairCompareDict:
                    continue
                pairCompareDict[actionPair1][self.meanDiffKeyword] = mean1 - mean2
                pairCompareDict[actionPair1][self.rejectKeyword] = isSignificant
        return pairCompareDict

    def getMinimumMean(self, actionDict):
        """
        对action的key metric直接给出均值最低的action
        @type actionDict: dict[str, list]
        @rtype: (int, str, dict[str, int], dict[str, float])
        """
        maxMean = sys.float_info.max
        actionName = ""
        actionMeanDict = {}
        actionSizeDict = {}
        sampleSize = 0
        for action, array in actionDict.iteritems():
            arrayLength = len(array)
            sampleSize += arrayLength
            mean = sum(array) / arrayLength
            actionMeanDict[action] = mean
            actionSizeDict[action] = arrayLength
            if maxMean > mean:
                actionName = action
                maxMean = mean
        return sampleSize, actionName, actionSizeDict, actionMeanDict

    def powerAnalysis(self, actionDict, fValue, sampleSize):
        """
        功效分析，根据效应量，显著性水平和样本量确定功效值
        功效：当H0为假时，正确拒绝H0的概率
        @type actionDict: dict[str, list]
        @type fValue: float
        @type sampleSize: int
        @rtype: float
        """
        groupNum = len(actionDict)
        # 组间自由度
        dfa = groupNum - 1
        # 计算组内方差的自由度
        dfe = sampleSize - groupNum
        # 效应量: eta squared
        etaSquared = (fValue * dfa) / (fValue * dfa + dfe)
        # 效应量: Cohen’s f
        cohensF = math.sqrt(etaSquared / (1 - etaSquared))
        return float(self.fTestAnovaPower.power(effect_size=cohensF, nobs=sampleSize, alpha=self.significanceLevel,
                                          k_groups=groupNum))

    def analyseAction(self, actionDict):
        """
        根据action的key metric推荐较优的action，如果通过方差分析没有显著性差异的话，说明无显著性差异，同时给出样本量大小以及给出平均
        key metric值最小的action，对于存在显著性差异的，给出显著性差异里面的较优值
        @type actionDict: dict[str, list]
        @rtype: dict
        """
        if len(actionDict) < 2:
            return {"message": "only one action, cannot be compared"}
        if not self.isDataEnough(actionDict):
            return {"message": "the amount of sample data for each action is at least %s" % self.minimumSampleSize}
        # 方差分析
        fValue, pValue, isVarianceEqual = self.anova(actionDict)
        isSignificant = bool(pValue < self.significanceLevel)
        (sampleSize, optimalAction, actionSizeDict, actionMeanDict) = self.getMinimumMean(actionDict)
        # 如果存在显著性差异，则进行功效分析
        if isSignificant:
            power = self.powerAnalysis(actionDict=actionDict, fValue=fValue, sampleSize=sampleSize)
            # 切流建议
            confidence = int(power * 100)
            recommendedPercentage = (2 * confidence - 91) / 10 * 10
        else:
            power = 0.95
            recommendedPercentage = 0
        # tukeyhsd在只有两组数据进行多重事后比较时容易出bug，因此两组直接手动计算；或者当检验结果不显著时，直接进行成对均值比较
        if len(actionDict) == 2 or not isSignificant:
            pairCompareDict = self.pairCompareMean(actionMeanDict=actionMeanDict, isSignificant=isSignificant)
        else:
            pairCompareDict = self.postHocTest(actionDict, isVarianceEqual)
        resultDict = {
            "isSignificant": isSignificant,
            "optimalAction": optimalAction,
            "sampleMean": json.dumps(actionMeanDict),
            "pairCompareResult": json.dumps(pairCompareDict) if len(pairCompareDict) else None,
            "sampleSize": json.dumps(actionSizeDict),
            "power": power,
            "recommendedPercentage": recommendedPercentage if recommendedPercentage > 0 else 0,
            "message": "success"
        }
        return resultDict
