# 基础镜像的Dockerfile ： https://lark.alipay.com/aone/docker/rm2g1d
# 基于基础镜像
FROM reg.docker.alibaba-inc.com/amwp/kubeone-base:1.8_062e4298_2024-08-12

ENV APP_NAME xdragon-metric
ENV APP_ENV=idpt

# 构建时要做的事，一般是执行shell命令，例如用来安装必要软件，创建文件（夹），修改文件
RUN rpm -ivh --nodeps "http://yum.corp.taobao.com/taobao/7/x86_64/current/ali-jdk/ali-jdk-8.4.7-1519273.alios7.x86_64.rpm" && \
mkdir -p /home/<USER>/$APP_NAME/target && \
echo "/home/<USER>/$APP_NAME/bin/appctl.sh stop" > /home/<USER>/stop.sh && \
echo "sudo /home/<USER>/plugins/aliyun-logtail/ilogtaild restart" >> /home/<USER>/stop.sh && \
echo "sudo /home/<USER>/plugins/aliyun-logtail/ilogtaild start" >> /home/<USER>/start.sh && \
echo "/home/<USER>/$APP_NAME/bin/preload.sh" > /home/<USER>/health.sh && \
chmod +x /home/<USER>/*.sh


# 增加jdk路径
ENV PATH "$PATH:/opt/taobao/java/bin/"

# 将应用启动脚本和配置文件复制到镜像中
COPY environment/common/bin/ /home/<USER>/${APP_NAME}/bin

# 解决 ilogtail 采集重复的问题
COPY environment/common/ilogtail_config.json /usr/local/ilogtail/

# 设置文件操作权限
RUN chmod -R a+x /home/<USER>/${APP_NAME}/bin/

# 挂载数据卷,指定目录挂载到宿主机上面,为了能够保存（持久化）数据以及共享容器间的数据，为了实现数据共享，例如日志文件共享到宿主机或容器间共享数据.
VOLUME /home/<USER>/$APP_NAME/logs \
       /home/<USER>/logs \
       /home/<USER>/cai/logs \
       /home/<USER>/diamond \
       /home/<USER>/snapshots \
       /home/<USER>/configclient \
       /home/<USER>/notify \
       /home/<USER>/catserver \
       /home/<USER>/liaoyuan-out \
       /home/<USER>/vipsrv-dns \
       /home/<USER>/vipsrv-failover \
       /home/<USER>/vipsrv-cache \
       /home/<USER>/csp \
       /home/<USER>/.rocketmq_offsets \
       /home/<USER>/amsdata \
       /home/<USER>/amsdata_all


############# test #################
# 健康检查默认3秒钟做一次,默认做100次也就是300秒,这里设置最多等180秒
ENV ali_start_timeout=180

# 将构建出的主包复制到指定镜像目录中
COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/${APP_NAME}.tgz

# 启动容器时进入的工作目录
WORKDIR /home/<USER>/$APP_NAME/