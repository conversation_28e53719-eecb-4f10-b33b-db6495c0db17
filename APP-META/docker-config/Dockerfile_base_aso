# 基础镜像的Dockerfile ： https://aliyuque.antfin.com/alipaas/hagq7u/fso9vpygg5hxpurb
# 基于基础镜像
FROM reg.docker.alibaba-inc.com/amwp/kubeone-base:1.8_062e4298_2024-08-12


# 构建时要做的事，一般是执行shell命令，例如用来安装必要软件，创建文件（夹），修改文件
RUN rpm -ivh --nodeps "http://yum.corp.taobao.com/taobao/7/x86_64/current/ali-jdk/ali-jdk-8.4.7-1519273.alios7.x86_64.rpm" && \
mkdir -p /home/<USER>/$APP_NAME/target && \
echo "/home/<USER>/$APP_NAME/bin/appctl.sh stop" > /home/<USER>/stop.sh && \
echo "sudo /home/<USER>/plugins/aliyun-logtail/ilogtaild restart" >> /home/<USER>/stop.sh && \
echo "sudo /home/<USER>/plugins/aliyun-logtail/ilogtaild start" >> /home/<USER>/start.sh && \
chmod +x /home/<USER>/*.sh


# 增加jdk路径
ENV PATH "$PATH:/opt/taobao/java/bin/"

# 安装pip环境
RUN yum install -y -b current gcc gcc-c++ tops-python27-pip

# 安装python三方包
RUN sudo /home/<USER>/bin/pip2.7 install -i http://yum.tbsite.net/pypi/simple/ numpy==1.16.6 --trusted-host=yum.tbsite.net && \
    sudo /home/<USER>/bin/pip2.7 install -i http://yum.tbsite.net/pypi/simple/ scipy==1.2.3 --trusted-host=yum.tbsite.net && \
    sudo /home/<USER>/bin/pip2.7 install -i http://yum.tbsite.net/pypi/simple/ pandas==0.24.2 --trusted-host=yum.tbsite.net && \
    sudo /home/<USER>/bin/pip2.7 install -i http://yum.tbsite.net/pypi/simple/ statsmodels==0.10.1 --trusted-host=yum.tbsite.net

RUN yum install -y -b current mysql