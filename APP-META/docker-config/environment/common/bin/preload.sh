#!/bin/bash

APP_HOME=$(cd $(dirname ${BASH_SOURCE[0]})/..; pwd)
source "${APP_HOME}/bin/setenv.sh"

SPACE_STR="..................................................................................................."

#####################################
checkpage() {
    if [[ -f ${APP_HOME}/target/${APP_NAME}/bin/appctl.sh ]]; then
        echo ${SPACE_STR}
        sh ${APP_HOME}/target/${APP_NAME}/bin/appctl.sh "status"
        if [ "$?" != "0" ]; then
            echo "ERROR: ${APP_NAME} check status [FAILED]"
            status=0
            error=1
        else
            echo "INFO: ${APP_NAME} check status [  OK  ]"
            status=1
            error=0
        fi
        echo ${SPACE_STR}
        return $error
    else
        if [ ! -z "$SERVICE_PID" ]; then
            if [ -f "$SERVICE_PID" ]; then
                if [ -s "$SERVICE_PID" ]; then
                    if [ -r "$SERVICE_PID" ]; then
                        PID=`cat "$SERVICE_PID"`
                        ps -p $PID >/dev/null 2>&1
                        if [ $? -eq 0 ] ; then
                            ret=`curl "http://127.0.0.1:22222/invoke/com.aliyun.xdragon.api.service.CheckHealth.checkAppHealth()" | fgrep 'ok'`
                            if [ $? -eq 0 -a ! -z "$ret" ]; then
                                echo ${SPACE_STR}
                                echo "INFO: ${APP_NAME} check status [  OK  ]"
                                echo ${SPACE_STR}
                                status=1
                                return 0
                            fi
                        fi
                    fi
                fi
            fi
        fi
        echo ${SPACE_STR}
        echo "INFO: ${APP_NAME} check status [FAILED]"
        echo ${SPACE_STR}
        return 1
    fi
}
#####################################
checkpage
