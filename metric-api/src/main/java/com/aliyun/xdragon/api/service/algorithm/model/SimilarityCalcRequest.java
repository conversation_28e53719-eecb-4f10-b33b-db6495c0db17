package com.aliyun.xdragon.api.service.algorithm.model;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.aliyun.xdragon.common.enumeration.algorithm.NormalizationType;
import com.aliyun.xdragon.common.enumeration.algorithm.SeriesSimilarityType;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/02/09
 */
@Data
public class SimilarityCalcRequest implements Serializable {

    private static final long serialVersionUID = -1779453451064886635L;

    @NotNull
    @NotEmpty
    private List<Double> array1;

    @NotNull
    @NotEmpty
    private List<Double> array2;

    @NotNull
    private SeriesSimilarityType similarityType;

    @Max(1)
    @Min(0)
    private Double similarityThreshold;

    @NotNull
    private NormalizationType normalizationType;


}
