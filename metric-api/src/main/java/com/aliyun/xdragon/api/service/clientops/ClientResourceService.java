package com.aliyun.xdragon.api.service.clientops;

import javax.validation.Valid;

import com.aliyun.xdragon.common.generate.model.ClientResource;
import com.aliyun.xdragon.common.model.PageableInfo;
import com.aliyun.xdragon.common.model.clientops.request.*;
import com.aliyun.xdragon.common.model.XdragonMetricResponse;
import org.apache.commons.lang3.tuple.Pair;

/**
 * <AUTHOR>
 * @date 2024/04/03
 */
public interface ClientResourceService {

    /**
     * list emp resources
     * @param request {@link ListEmpResourcesRequest}
     * @return
     */
    XdragonMetricResponse<PageableInfo<ClientResource>> listEmpResources(@Valid ListEmpResourcesRequest request);

    /**
     * 添加资源
     * @param request, {@link AddClientResourceRequest}
     * @return resource id
     */
    XdragonMetricResponse<String> addResource(@Valid AddClientResourceRequest request);

    /**
     * 更新资源
     * @param request, {@link UpdateClientResourceRequest}
     * @return true XdragonMetricResponse object if update success
     */
    XdragonMetricResponse<String> updateResource(@Valid UpdateClientResourceRequest request);

    /**
     * 删除资源
     * @param request, {@link DelClientResourceRequest}
     * @return true XdragonMetricResponse object if delete success
     */
    XdragonMetricResponse<Boolean> deleteResource(@Valid DelClientResourceRequest request);

    /**
     * 检查empId是否有对应资源的权限
     * @param request, {@link CheckClientResourceRequest}
     * @return true XdragonMetricResponse object if check pass， else false with check failed resource
     */
    XdragonMetricResponse<Pair<Boolean, String>> checkResource(@Valid CheckClientResourceRequest request);

    /**
     * update all approve status
     * @return
     */
    XdragonMetricResponse<Boolean> updateApproveStatus();

    /**
     * update approve status with special id
     * @param request {@link UpdateApproveStatusRequest}
     * @return
     */
    XdragonMetricResponse<Boolean> updateApproveStatus(@Valid UpdateApproveStatusRequest request);

    void updateApproveStatusCron();
}
