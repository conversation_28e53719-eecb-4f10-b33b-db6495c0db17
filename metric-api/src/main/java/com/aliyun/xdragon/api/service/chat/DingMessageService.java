package com.aliyun.xdragon.api.service.chat;

public interface DingMessageService {
    /**
     * 创建并推送消息卡片
     * */
    String createMessageCard(String message, String staffId, String dingGroupId, Integer conversationType) throws Exception;

    /**
     * 更新卡片消息内容
     * */
    void updateMessageCard(String message, String outTrackId, boolean isFinish) throws Exception;

    /**
     * 更新自定义诊断卡片
     */
    void updateCallbackCard(String hitSceneList, String workItemInfoList, String outTrackId) throws Exception;
}
