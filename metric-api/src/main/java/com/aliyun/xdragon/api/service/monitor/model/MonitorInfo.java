package com.aliyun.xdragon.api.service.monitor.model;

import com.aliyun.xdragon.api.service.monitor.enums.ExceptionLevel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class MonitorInfo implements Serializable {
    private static final long serialVersionUID = 8092653253555061531L;
    //异常名称，只能有数字、字母、下划线组成
    private String exceptionName;

    //告警名称
    private String alertName;

    //异常描述
    private String exceptionDesc;

    //资源ID,实例名或nc ip
    private String resourceId;

    //物理机ip
    private String ncIp;

    //资源类型
    private String resourceType;

    //告警组件
    private String exceptionComponent;

    //集群名称
    private String clusterAlias;

    //资源所在region
    private String regionNo;

    //异常时间
    private Date exceptionTime;

    //告警级别
    private ExceptionLevel level;

    //扩展字段
    private String ext;

    //通知到哪里,全链路定界及xdc中的特征
    private List<String> monitorLabels;

    private NotifyInfo notify;

    private HandlerInfo handler;

    private String businessId;

    private String resourceList;

    private Integer resourceCnt;

    private String xamMonitorLabel;

    private String fromSource;

    /*
    templateName, 对应通知那边使用的模版
     */
    private String templateName;

    private String constructDataClass;

    private String additionalInfo;

    private String reason;

    public String getExceptionName() {
        return exceptionName;
    }

    public void setExceptionName(String exceptionName) {
        this.exceptionName = exceptionName;
    }

    public String getExceptionDesc() {
        return exceptionDesc;
    }

    public void setExceptionDesc(String exceptionDesc) {
        this.exceptionDesc = exceptionDesc;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getNcIp() {
        return ncIp;
    }

    public void setNcIp(String ncIp) {
        this.ncIp = ncIp;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getExceptionComponent() {
        return exceptionComponent;
    }

    public void setExceptionComponent(String exceptionComponent) {
        this.exceptionComponent = exceptionComponent;
    }

    public String getClusterAlias() {
        return clusterAlias;
    }

    public void setClusterAlias(String clusterAlias) {
        this.clusterAlias = clusterAlias;
    }

    public String getRegionNo() {
        return regionNo;
    }

    public void setRegionNo(String regionNo) {
        this.regionNo = regionNo;
    }

    public Date getExceptionTime() {
        return exceptionTime;
    }

    public void setExceptionTime(Date exceptionTime) {
        this.exceptionTime = exceptionTime;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public List<String> getMonitorLabels() {
        return monitorLabels;
    }

    public void setMonitorLabels(List<String> monitorLabels) {
        this.monitorLabels = monitorLabels;
    }

    public NotifyInfo getNotify() {
        return notify;
    }

    public void setNotify(NotifyInfo notify) {
        this.notify = notify;
    }

    public HandlerInfo getHandler() {
        return handler;
    }

    public void setHandler(HandlerInfo handler) {
        this.handler = handler;
    }

    public ExceptionLevel getLevel() {
        return level;
    }

    public void setLevel(ExceptionLevel level) {
        this.level = level;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getAlertName() {
        return alertName;
    }

    public void setAlertName(String alertName) {
        this.alertName = alertName;
    }

    public String getResourceList() {
        return resourceList;
    }

    public void setResourceList(String resourceList) {
        this.resourceList = resourceList;
    }

    public Integer getResourceCnt() {
        return resourceCnt;
    }

    public void setResourceCnt(Integer resourceCnt) {
        this.resourceCnt = resourceCnt;
    }

    public String getXamMonitorLabel() {
        return xamMonitorLabel;
    }

    public void setXamMonitorLabel(String xamMonitorLabel) {
        this.xamMonitorLabel = xamMonitorLabel;
    }

    public String getFromSource() {
        return fromSource;
    }

    public void setFromSource(String fromSource) {
        this.fromSource = fromSource;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getConstructDataClass() {
        return constructDataClass;
    }

    public void setConstructDataClass(String constructDataClass) {
        this.constructDataClass = constructDataClass;
    }

    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
