package com.aliyun.xdragon.api.service.monitor.enums;

public enum ExceptionLevel {
    FATAL(5),
    CRITICAL(4),
    WARNING(3),
    LOW_WARNING(2),
    NORMAL(1);

    private final Integer value;

    ExceptionLevel(Integer value) {
        this.value = value;
    }

    public static ExceptionLevel of(Integer value) {
        for (ExceptionLevel level : ExceptionLevel.values()) {
            if (level.value.equals(value)) {
                return level;
            }
        }
        throw new IllegalArgumentException("No match ExceptionLevel from value: " + value.toString());
    }
}
