package com.aliyun.xdragon.api.service.keymetric.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum ComponentEnum {
    COMPUTE(0, "compute", "计算"),
    STORAGE(1, "storage", "存储"),
    LOCAL_STORAGE(2, "local_storage", "本地存储"),
    NETWORK(3, "network", "网络"),
    GPU(4, "gpu", "GPU"),
    FPGA(5, "fpga", "FPGA"),
    OS(6, "os", "操作系统"),
    SYSVM(7, "container", "容器"),
    ;

    private final Integer component;
    private final String componentName;
    private final String componentExpression;

    ComponentEnum(Integer component, String componentName, String componentExpression) {
        this.component = component;
        this.componentName = componentName;
        this.componentExpression = componentExpression;
    }

    public static ComponentEnum of(int value) {
        for (ComponentEnum componentEnum : ComponentEnum.values()) {
            if (componentEnum.getComponent().equals(value)) {
                return componentEnum;
            }
        }
        return null;
    }

    public static List<String> getComponentNameList() {
        List<String> componentNameList = new ArrayList<>();
        for (ComponentEnum componentEnum : ComponentEnum.values()) {
            componentNameList.add(componentEnum.getComponentName());
        }
        return componentNameList;
    }
}
