package com.aliyun.xdragon.api.service.allionerisk.model;

import java.io.Serializable;
import java.util.Map;

import javax.validation.constraints.NotNull;

import com.aliyun.xdragon.common.enumeration.aone.AoneReqStatusEnum;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
public class RiskActionUpdateRequest implements Serializable {

    private static final long serialVersionUID = 1385427261938232051L;

    @NotNull
    private Long actionId;

    private String owner;

    private String actionName;

    private AoneReqStatusEnum status;

    private String deadline;

    private Map<String, String> extParams;

    // 修改人，及操作者的工号
    @NotNull
    private String modifier;

}
