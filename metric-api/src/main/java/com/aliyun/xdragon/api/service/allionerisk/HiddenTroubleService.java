package com.aliyun.xdragon.api.service.allionerisk;

import com.aliyun.xdragon.api.service.allionerisk.model.AllinoneRiskQueryGocUserRequest;
import com.aliyun.xdragon.api.service.allionerisk.model.ProblemRecordRequest;
import com.aliyun.xdragon.api.service.allionerisk.model.RiskImageUploadRequest;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRiskFailureFeedbackUserInfo;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRiskNcDetail;
import com.aliyun.xdragon.common.generate.log.model.AllinoneRiskVmDetail;
import com.aliyun.xdragon.common.generate.log.model.HiddenTroubleRequest;
import com.aliyun.xdragon.common.generate.log.model.HiddenTroubleResponse;
import com.aliyun.xdragon.common.model.CloudOpsResult;
import com.aliyun.xdragon.common.model.PageableInfo;

import java.util.List;


public interface HiddenTroubleService {

    /**
     * 获取隐患平台基本信息
     * @param request
     * @return
     */
    PageableInfo<HiddenTroubleResponse> queryRiskBaseInfo(HiddenTroubleRequest request) throws Exception;

    /**
     * 获取nc详情列表
     * @param riskId
     * @return
     */
    PageableInfo<AllinoneRiskNcDetail> queryNcDetail(Long riskId, Integer offSet, Integer pageSize);

    /**
     * 获取vm详情列表
     * @param riskId
     * @return
     */
    PageableInfo<AllinoneRiskVmDetail> queryVmDetail(Long riskId, Integer offSet, Integer pageSize);


    CloudOpsResult<List<AllinoneRiskFailureFeedbackUserInfo>> queryGocUserInfo(AllinoneRiskQueryGocUserRequest request);

    CloudOpsResult<ProblemRecordRequest> analyzeAone(ProblemRecordRequest request);

    CloudOpsResult<Integer> problemRecord(ProblemRecordRequest request) throws Exception;

    /**
     * 上传图片，并获得url
     * @param request
     * @return
     */
    CloudOpsResult<String> uploadImage(RiskImageUploadRequest request);
}

