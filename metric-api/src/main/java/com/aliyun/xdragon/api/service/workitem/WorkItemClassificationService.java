package com.aliyun.xdragon.api.service.workitem;

import com.aliyun.xdragon.common.generate.model.WorkItemClassificationAndLabelDetail;
import com.aliyun.xdragon.common.generate.model.WorkItemClassificationDetail;
import com.aliyun.xdragon.common.generate.model.WorkItemLabel;
import com.aliyun.xdragon.common.generate.model.WorkItemStatistics;
import com.aliyun.xdragon.common.model.AoneIssue;
import com.aliyun.xdragon.common.model.PageableInfo;
import com.aliyun.xdragon.common.model.XdragonMetricRequest;
import com.aliyun.xdragon.common.model.XdragonMetricResponse;
import com.aliyun.xdragon.common.model.workItem.WorkItemEval;
import com.aliyun.xdragon.common.model.workItem.request.ClientListWorkItemRequest;
import com.aliyun.xdragon.common.model.workItem.request.GetAoneRequest;
import com.aliyun.xdragon.common.model.workItem.request.ListWorkItemRequest;
import com.aliyun.xdragon.common.model.workItem.request.QueryLabelRequest;
import com.aliyun.xdragon.common.model.workItem.request.StatLabelInfoByTeamRequest;
import com.aliyun.xdragon.common.model.workItem.request.StatPieChartsDataRequest;
import com.aliyun.xdragon.common.model.workItem.request.UpdateWorkItemRequest;
import com.aliyun.xdragon.common.model.workItem.request.WorkItemAoneRequest;
import com.aliyun.xdragon.common.model.workItem.request.WorkItemLabelRequest;
import com.aliyun.xdragon.common.model.workItem.request.WorkItemStatisticsRequest;
import com.aliyun.xdragon.common.model.workItem.request.WorkItemTrendRequest;
import com.aliyun.xdragon.common.model.workItem.response.ClientListWorkItemResponse;
import org.apache.commons.lang3.tuple.Pair;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/10
 */
public interface WorkItemClassificationService {

    /**
     * get Search Keys: Label, team, source andproduct
     *
     * @return Keys name with search list
     */
    Map<String, List<String>> getSearchKeys();

    /**
     * list work item in given time range, Labels, team, source and product
     *
     * @param request {@link ListWorkItemRequest}
     * @return {@link WorkItemClassificationDetail} page object, {@link PageableInfo}
     */
    PageableInfo<WorkItemClassificationAndLabelDetail> listWorkItem(@Valid ListWorkItemRequest request);


    /**
     * list work item in given time range
     *
     * @param request {@link com.aliyun.xdragon.common.model.workItem.request.ClientListWorkItemRequest}
     * @return {@link com.aliyun.xdragon.common.model.workItem.ClientWorkItemParam}
     */
    XdragonMetricResponse<ClientListWorkItemResponse> listClientWorkItem(@Valid XdragonMetricRequest<ClientListWorkItemRequest> request);

    /**
     * get work item desc info
     *
     * @param id, id
     * @return WorkItemClassificationDetail object
     */
    WorkItemClassificationAndLabelDetail getWorkItemInfo(Long id);

    /**
     * update work item
     *
     * @param request {@link UpdateWorkItemRequest}
     */
    void updateWorkItemInfo(@Valid UpdateWorkItemRequest request);

    /**
     * delete work item
     *
     * @param id, id
     */
    void deleteWorkItem(Long id);

    /**
     * add aone issue for given work item
     *
     * @param request {@link WorkItemAoneRequest}
     * @return aone issue url
     */
    String workItemAone(@Valid WorkItemAoneRequest request);

    /**
     * query aone issue state and update
     *
     * @param request {@link GetAoneRequest}
     * @return aone issue url
     */
    AoneIssue getAoneStatus(@Valid GetAoneRequest request);

    /**
     * list work item label
     *
     * @return Keys name with search list
     */
    PageableInfo<WorkItemLabel> queryLabelInfo();

    /**
     * query label info by team
     * @param request
     * @return
     */
    XdragonMetricResponse<List<String>> queryLabelByTeam(@Valid XdragonMetricRequest<QueryLabelRequest> request);

    /**
     * add work item label
     *
     * @param request {@link WorkItemLabelRequest}
     */
    void addLabelInfo(@Valid WorkItemLabelRequest request);

    /**
     * update work item label
     *
     * @param request {@link WorkItemLabelRequest}
     */
    void updateLabelInfo(@Valid WorkItemLabelRequest request);

    /**
     * delete work item label
     *
     * @param id, id
     */
    void deleteLabelInfo(Long id);

    /**
     * query WorkItemClassificationDetail by source id
     *
     * @param sourceId record source id
     * @return List<WorkItemClassificationDetail>
     */
    List<WorkItemClassificationDetail> queryWorkItemClassificationDetail(String sourceId);

    /**
     * count data source, team, label one id, label two id in given time rang
     *
     * @param request {@link StatPieChartsDataRequest}
     * @return region: number pair list
     */
    List<Pair<String, Long>> statChartsData(@Valid StatPieChartsDataRequest request);

    /**
     * count data source, team, label one id, label two id in given time rang
     *
     * @param request {@link StatLabelInfoByTeamRequest}
     * @return region: number pair list
     */
    List<Pair<String, Long>> statLabelInfoByTeam(@Valid StatLabelInfoByTeamRequest request);

    /**
     * list statistics of work item
     * @param request XdragonMetricRequest<WorkItemStatisticsRequest>
     * @return list of WorkItemStatistics
     */
    XdragonMetricResponse<List<WorkItemStatistics>> listWorkItemStatistics(XdragonMetricRequest<WorkItemStatisticsRequest> request);


    /**
     * 工单评价接口
     * @param startDs 开始日期
     * @param endDs 结束日期
     * @param label 工单信息
     * @return List<WorkItemEval>
     */
    List<WorkItemEval> evalWorkItem(String startDs, String endDs, String label);

    /**
     * 工单趋势接口
     * @param request XdragonMetricRequest<WorkItemTrendRequest>
     * @return Map<String, String>
     */
     XdragonMetricResponse<List<Map<String, Integer>>> getWorkItemTrend(@Valid XdragonMetricRequest<WorkItemTrendRequest> request);
}
