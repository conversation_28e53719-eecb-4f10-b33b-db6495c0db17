package com.aliyun.xdragon.api.service.chat.model;

import lombok.Data;

@Data
public class MachineExceptionEventInfo {
    /**
     * 异常名称
     * */
    private String exceptionName;
    /**
     * 异常描述(中文名)
     * */
    private String doc;
    /**
     * 异常次数
     * */
    private String exceptionCount;
    /**
     * 异常类型，例如：asw_network_hardware
     * */
    private String exceptionType;
    /**
     * 监控分类:CONTROLLER_EVENT(控制面)、HARDWARE_EVENT(NC硬件)、NC_SYSTEM_EVENT(NC系统侧监控)、VIRT_KEY_EVENT(虚拟化关键事件)、VM_NC_FATAL_EVENT(NC致命故障事件)、VM_PERF_DROP_EVENT(VM性能监控)、GUESTOS_EVENT(guest os监控)
     * */
    private String monitorType;
    /**
     * 异常等级，例如：fatal、critical、warning、low_warning、normal
     * */
    private String warningLevel;
    /**
     * 最后一次异常时间
     * */
    private String lastExceptionTime;
    /**
     * 异常发生时间
     * */
    private String exceptionTime;
    /**
     * 机器IP
     * */
    private String ncIp;
    /**
     * 异常额外信息
     * */
    private String additionalInfo;
    /**
     * 异常原因
     * */
    private String reason;
    /**
     * 机器ID
     * */
    private String machineId;
}
