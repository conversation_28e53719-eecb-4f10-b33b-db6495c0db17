package com.aliyun.xdragon.api.service.chat;

import java.util.Map;
import java.util.function.Consumer;

import com.aliyun.xdragon.api.service.chat.model.BaiLianAppResult;

/**
 * <AUTHOR>
 */
public interface ChatBaiLianAgentService {

    /**
     * @param appId see {@link com.aliyun.xdragon.common.enumeration.BaiLianAppEnum}
     */
    BaiLianAppResult callApp(String appId, String sessionId, String prompt, Map<String, Object> parameters);

    /**
     * 流式调用百炼工作流应用
     *
     * @param appId          see {@link com.aliyun.xdragon.common.enumeration.BaiLianAppEnum}
     * @param sessionId      会话id
     * @param prompt         用户输入
     * @param parameters     应用入参变量
     * @param nodeId         应用中需要流式处理的节点id
     * @param streamConsumer 中间结果消费
     * @return 调用结果
     */
    BaiLianAppResult streamCall(String appId, String sessionId, String prompt,
        Map<String, Object> parameters, String nodeId, Consumer<String> streamConsumer);
}
